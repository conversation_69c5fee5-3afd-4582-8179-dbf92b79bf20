/* ***************************************************************************
 *  File: utility.c                                          Part of Outcast *
 *  Usage: misc. small functions/macros                                      *
 *  Copyright  1990, 1991 - see 'license.doc' for complete information.      *
 *  Copyright  1994, 1995 - Outcast Systems Ltd.                             *
 *************************************************************************** */

#include <ctype.h>
#include <stdarg.h>
#include <stdio.h>
#include <string.h>
#include <time.h>
#ifdef _HPUX_SOURCE
#include <varargs.h>
#endif
#if 0
#include <mysql/mysql.h>
#endif

#include "comm.h"
#include "db.h"
#include "events.h"
#include "interp.h"
#ifdef OLDJUSTICE
#include "justice.h"
#endif
#ifdef NEWJUSTICE
#include "newjustice.h"
#endif
#include "prototypes.h"
#include "skillrec.h"
#include "spells.h"
#include "race_class.h"
#include "utils.h"
#include "weather.h"

/* extern variables */
#ifdef AZUTHSQL
extern MYSQL *m;
#endif

extern int port;
extern P_desc descriptor_list;
extern P_room world;
extern P_event current_event;
extern char *grantable_bits[];
extern const char *debug_subchannels[];
extern const struct racial_data_type racial_data[PC_RACES];
extern const struct stat_data stat_factor[];
extern const struct trait_data racial_traits[];
extern int innate_abilities[];
extern int movement_loss[];
extern int top_of_world;
extern struct command_info cmd_info[];
extern struct time_info_data time_info;
extern struct zone_data *zone_table;
extern int debuglogall;
extern FILE *debuglog_f;

clock_t cstart, cfinish; // clock timers for tracking CPU heavy usage

void timer_start(void) {
  cstart = clock();
}

void timer_stop(void) {
  cfinish = clock();
}

double timer_lap(void) {
  return (double) (clock() - cstart) / CLOCKS_PER_SEC;
}

double timer_elapsed(void) {
  return (double) (cfinish - cstart) / CLOCKS_PER_SEC;
}

int IS_PET(P_char ch) {
  if (!ch || !IS_NPC(ch))
    return FALSE;

  if (!ch->following)
    return FALSE;

  if (IS_PC(ch->following))
    return TRUE;

  return FALSE;

}

inline int IS_MORPH(P_char ch) {
  if (!ch || !IS_NPC(ch) || IS_TRUSTED(ch)) /* add check for immortals -Azuth */
    return FALSE;

  if (ch->only.npc && ch->only.npc->orig_char)
    return TRUE;

  return FALSE;
}

int is_granted(P_char ch, int cmd) {
  int i;

  if (ch->desc && ch->desc->original)
    ch = ch->desc->original;

  if (!cmd_info[cmd].grantable) {
    if (GET_LEVEL(GET_PLYR(ch)) >= cmd_info[cmd].minimum_level)
      return TRUE;
    else
      return FALSE;
  }

  if (IS_NPC(ch))
    return FALSE;

  i = cmd_info[cmd].grantable;

  return IS_CSET(GRANT_FLAGS(ch), i + 1);
}

int MIN(int a, int b) {
  return ((a < b) ? a : b);
}

int MAX(int a, int b) {
  return ((a > b) ? a : b);
}

int BOUNDED(int a, int b, int c) {
  return (MIN(MAX(a, b), c));
}

/* creates a random number in interval [from;to] */

int number(int from, int to) {
  if (to == from)
    return to;
  else if (to < from) { /* better to just flip them if they're in the wrong order */
    logit(LOG_DEBUG, "Numbers in wrong order to number(%d, %d).", from, to);
    dump_stack("number()");

    return ((erandom() % (from - to + 1)) + to);
  }

  return ((erandom() % (to - from + 1)) + from);
}

/* simulates dice roll */

int dice(int num, int size) {
  int r, sum = 0;

  if (size < 1) {
    logit(LOG_DEBUG, "size < 1 in dice() %d, %d", num, size);
    size = 1;
  }
  if (num < 1) {
    logit(LOG_DEBUG, "num < 1 in dice() %d, %d", num, size);
    num = 1;
  }
  for (r = 1; r <= num; r++)
    sum += number(1, size);

  return (sum);
}

/* This is almost exactly gcc's version of strdup, only difference is it keeps track of allocated string size,
   or we'd just use strdup.  Returns point to newly allocated string, or NULL in the case of a malloc failure.
   JAB  */

char *str_dup(const char *source) {
  void *new;
  size_t str_sz = strlen(source) + 1;

  new = malloc(str_sz);

  if (new == NULL)
    dump_core();

#ifdef MEM_DEBUG
  mem_use[MEM_STRINGS] += str_sz;
#endif

  return (char *) memcpy(new, (void *) source, str_sz);
}

/* returns: 0 if equal, 1 if arg1 > arg2, -1 if arg1 < arg2  */

/* scan 'till found different or end of both                 */

int str_cmp(const char *arg1, const char *arg2) {
  register const unsigned char *p1, *p2;
  unsigned char c1, c2;

  /* NULL ptr checks added, SAM 7-94 */
  if (arg1 == arg2) {
    return (0);
  } else if (arg1 == NULL) {
    return (-1);
  } else if (arg2 == NULL) {
    return (1);
  }

  p1 = (const unsigned char *) arg1;
  p2 = (const unsigned char *) arg2;

  do {
    c1 = LOWER(*p1);
    c2 = LOWER(*p2);
    p1++;
    p2++; /* NOTE, LOWER is a macro, using increment '++' in a macro arg is very dangerous */
  } while (c1 && (c1 == c2));

  if (c1 == c2) {
    return (0);
  }

  return ((c1 < c2) ? (-1) : (1));
}

/* returns: 0 if equal, 1 if arg1 > arg2, -1 if arg1 < arg2  */
/* scan 'till found different, end of both, or n reached     */

/* case insensitive, I'm leaving it, but it's named wrong -SE */

int strn_cmp(const char *arg1, const char *arg2, uint n) {
  int chk, i;

  for (i = 0; (*(arg1 + i) || *(arg2 + i)) && (n > 0); i++, n--)
    if ((chk = LOWER(*(arg1 + i)) - LOWER(*(arg2 + i)))) {
      if (chk < 0)
        return (-1);
      else
        return (1);
    }

  return (0);
}

int str_n_cmp(const char *argv1, const char *argv2) {
  char name[MAX_INPUT_LENGTH];

  strncpy(name, argv1, strlen(argv2));
  return str_cmp(name, argv2);
}

/* writes a string to the log */

void logit(const char *filename, const char *format, ...) {
  FILE *log_f;
  char lbuf[MAX_STRING_LENGTH], tbuf[MAX_STRING_LENGTH];
  long ct;
  va_list args;

  if (!(log_f = fopen(filename, "a"))) {
    if (str_cmp(filename, LOG_FILE))
      logit(LOG_FILE, "failure opening logfile %s", filename);
    return;
  }

  va_start(args, format);
  ct = time(0);

  if (str_cmp(filename, LOG_DEBUG)) {
    strcpy(tbuf, asctime(localtime(&ct)));
    tbuf[strlen(tbuf) - 1] = 0;
    strcat(tbuf, "::");
  } else {
    tbuf[0] = 0;
  }

  lbuf[0] = 0;
  vsprintf(lbuf, format, args);

  strcat(tbuf, lbuf);
  strcat(tbuf, "\n");

  if (fputs(tbuf, log_f) == -1)
    fprintf(stderr, "Failed write to %s: <%s>", filename, tbuf);

  fclose(log_f);
  if (!str_cmp(filename, LOG_EXIT))
    perror(tbuf);
  va_end(args);
}

void wizlog(int level, const char *format, ...) {
  P_desc d;
  char lbuf[MAX_STRING_LENGTH];
  va_list args;

  strcpy(lbuf, "&+C*** WIZLOG:&n ");
  va_start(args, format);
  vsprintf(lbuf + strlen(lbuf), format, args);
  strcat(lbuf, "\n");
  lbuf[strlen(lbuf)] = 0;

  for (d = descriptor_list; d; d = d->next) {
    if ((d->connected == CON_PLYNG) && IS_TRUSTED(d->character) && (GET_LEVEL(d->character) >= level) &&
            IS_CSET(d->character->only.pc->pcact, PLR_WIZLOG)) {
      send_to_char(lbuf, d->character);
    }
  }
  va_end(args);
}

void wizcmd(int level, const char *format, ...) {
  P_desc d;
  char lbuf[MAX_STRING_LENGTH];
  va_list args;

  strcpy(lbuf, "&+C*** WIZCMD:&n ");
  va_start(args, format);
  vsprintf(lbuf + strlen(lbuf), format, args);
  strcat(lbuf, "\n");
  lbuf[strlen(lbuf)] = 0;

  for (d = descriptor_list; d; d = d->next) {
    if ((d->connected == CON_PLYNG) && IS_TRUSTED(d->character) && (GET_LEVEL(d->character) >= level) &&
            IS_CSET(d->character->only.pc->pcact, PLR_WIZCMD)) {
      send_to_char(lbuf, d->character);
    }
  }
  va_end(args);
}

void assoclog(int level, const char *format, ...) {
  P_desc d;
  char lbuf[MAX_STRING_LENGTH];
  va_list args;

  strcpy(lbuf, "&+C*** ASSOCLOG:&n ");
  va_start(args, format);
  vsprintf(lbuf + strlen(lbuf), format, args);
  strcat(lbuf, "\n");
  lbuf[strlen(lbuf)] = 0;

  for (d = descriptor_list; d; d = d->next) {
    if ((d->connected == CON_PLYNG) && IS_TRUSTED(d->character) && (GET_LEVEL(d->character) >= level) &&
            IS_CSET(d->character->only.pc->pcact, PLR_ASSOCLOG)) {
      send_to_char(lbuf, d->character);
    }
  }
  va_end(args);

  /* convenient, eh? */
  logit(LOG_ASSOC, lbuf);
}

void debuglog(int level, int subchannel, const char *format, ...) {
  P_desc d;
  char lbuf[MAX_STRING_LENGTH];
  va_list args;

  sprintf(lbuf, "&+C*** D [%s]:&n ", debug_subchannels[subchannel]);
  va_start(args, format);
  vsprintf(lbuf + strlen(lbuf), format, args);
  strcat(lbuf, "\n");
  lbuf[strlen(lbuf)] = 0;

  if (debuglogall)
    fprintf(debuglog_f, lbuf);

  for (d = descriptor_list; d; d = d->next) {
    if ((d->connected == CON_PLYNG) && IS_TRUSTED(d->character) && (GET_LEVEL(d->character) >= level) &&
            IS_CSET(d->character->only.pc->pcact, PLR_DEBUGLOG) && IS_CSET(d->character->only.pc->debug, subchannel)) {
      send_to_char(lbuf, d->character);
    }
  }
  va_end(args);
}

void statuslog(int level, const char *format, ...) {
  P_desc d;
  char lbuf[MAX_STRING_LENGTH];
  va_list args;

  strcpy(lbuf, "&+c*** STATUS:&n ");
  va_start(args, format);
  vsprintf(lbuf + strlen(lbuf), format, args);
  strcat(lbuf, "\n");
  lbuf[strlen(lbuf)] = 0;

  for (d = descriptor_list; d; d = d->next) {
    if ((d->connected == CON_PLYNG) && IS_TRUSTED(d->character) && (GET_LEVEL(d->character) >= level) &&
            IS_CSET(d->character->only.pc->pcact, PLR_STATUS)) {
      send_to_char(lbuf, d->character);
    }
  }
  va_end(args);
}

void givelog(int level, const char *format, ...) {
  P_desc d;
  char lbuf[MAX_STRING_LENGTH];
  va_list args;


  strcpy(lbuf, "&+c*** GIVE:&n ");
  va_start(args, format);
  vsprintf(lbuf + strlen(lbuf), format, args);
  logit(LOG_GIVE, lbuf);
  strcat(lbuf, "\n");
  lbuf[strlen(lbuf)] = 0;

  for (d = descriptor_list; d; d = d->next) {
    if ((d->connected == CON_PLYNG) && IS_TRUSTED(d->character) && (GET_LEVEL(d->character) >= level) &&
            IS_CSET(d->character->only.pc->pcact, PLR_SHOWGIVES)) {
      send_to_char(lbuf, d->character);
    }
  }
  va_end(args);
}

/*
 id              BIGINT PRIMARY KEY NOT NULL AUTO_INCREMENT,\
 viewlevel       TINYINT,\
 giver           VARCHAR(255),\
 receiver        VARCHAR(255),\
 room_vnum       INT,\
 obj_vnum        INT,\
 obj_name        VARCHAR(255),\
 flag            TINYINT,\
 time            DATETIME\
 */
void sql_givelog(int viewlevel, char *giver, int obj_vnum, char *obj_name, char *receiver, int where) {
#ifdef AZUTHSQL
  static char q[MAX_STRING_LENGTH];
  static char oname[MAX_STRING_LENGTH];

  oname[0] = '\0';

  sql_escape_text(obj_name, oname);

  sprintf(q, "INSERT INTO eqxfer VALUES('AUTO_INCREMENT', '%d', '%s', '%s', '%d', '%d', '%s', '1', NOW());",
          viewlevel, giver, receiver, where, obj_vnum, oname);

  if (mysql_query(m, q) < 0) {
    fprintf(stderr, "Failed to insert object.  Error: %s\n", mysql_error(m));
    fprintf(stderr, "%s", q);
  }
#endif
}

void emslog(int level, const char *format, ...) {
  P_desc d;
  char lbuf[MAX_STRING_LENGTH];
  va_list args;

  strcpy(lbuf, "&+c*** EMS:&n&+y ");
  va_start(args, format);
  vsprintf(lbuf + strlen(lbuf), format, args);
  strcat(lbuf, "\n");
  lbuf[strlen(lbuf)] = 0;

  for (d = descriptor_list; d; d = d->next) {
    if ((d->connected == CON_PLYNG) && IS_TRUSTED(d->character) && (GET_LEVEL(d->character) >= level) &&
            IS_CSET(d->character->only.pc->pcact, PLR_EMSLOG)) {
      send_to_char(lbuf, d->character);
    }
  }
  va_end(args);
}

/* the dread connection log, spam supreme, also logs things to lib/logs/log/comm.  JAB */

void connectlog(int level, const char *format, ...) {
  FILE *log_f;
  P_desc d;
  char buf[MAX_STRING_LENGTH], lbuf[MAX_STRING_LENGTH], tbuf[MAX_STRING_LENGTH];
  time_t ct;
  va_list args;

  va_start(args, format);
  ct = time(0);

  vsprintf(buf, format, args);

  sprintf(lbuf, "&+y*** CONN:&n %s\n", buf);
  {
    struct tm *tm_info = localtime(&ct);
    if (!tm_info) {
      strcpy(tbuf, "[time error]");
    } else {
      strcpy(tbuf, asctime(tm_info));
    }
  }
  tbuf[strlen(tbuf) - 1] = 0;
  strcat(tbuf, "::");
  strcat(tbuf, buf);
  strcat(tbuf, "\n");


  for (d = descriptor_list; d; d = d->next) {
    if ((d->connected == CON_PLYNG) && IS_TRUSTED(d->character) && (GET_LEVEL(d->character) >= level) &&
            IS_CSET(d->character->only.pc->pcact, PLR_CONNMSG)) {
      send_to_char(lbuf, d->character);
    }
  }

  log_f = fopen(LOG_COMM, "a");
  if (!log_f) {
    logit(LOG_FILE, "failure opening logfile %s", LOG_COMM);
  } else {
    if (fputs(tbuf, log_f) == -1)
      fprintf(stderr, "Failed write to %s: <%s>", LOG_COMM, tbuf);
    fclose(log_f);
  }

  va_end(args);
}

void banlog(int level, const char *format, ...) {
  FILE *log_f;
  P_desc d;
  char lbuf[MAX_STRING_LENGTH];
  va_list args;

  strcpy(lbuf, "&+y*** BANNED:&n ");
  va_start(args, format);
  vsprintf(lbuf + strlen(lbuf), format, args);
  strcat(lbuf, "\n");
  lbuf[strlen(lbuf)] = 0;

  for (d = descriptor_list; d; d = d->next) {
    if ((d->connected == CON_PLYNG) && IS_TRUSTED(d->character) && (GET_LEVEL(d->character) >= level) &&
            IS_CSET(d->character->only.pc->pcact, PLR_BANS)) {
      send_to_char(lbuf, d->character);
    }
  }
  log_f = fopen(LOG_COMM, "a");
  if (!log_f) {
    logit(LOG_FILE, "failure opening logfile %s", LOG_COMM);
  } else {
    if (fputs(lbuf, log_f) == -1)
      fprintf(stderr, "Failed write to %s: <%s>", LOG_COMM, lbuf);
    fclose(log_f);
  }
  va_end(args);
}

void sprint_cbit(ubyte *var, uint limit, const char *names[], char *result) {
  int nr;

  *result = '\0';

  for (nr = 0; (nr < limit) && (*names[nr] != '\n'); nr++) {
    if (IS_CSET(var, nr) && (*names[nr] != '!')) {
      strcat(result, names[nr]);
      strcat(result, " ");
    }
  }

  if (!*result)
    strcat(result, "NOBITS");
}

void sprintbit(uint vektor, const char *names[], char *result) {
  int nr;

  *result = '\0';

  for (nr = 0; vektor; vektor >>= 1) {
    if (IS_SET(1, vektor))
      if (*names[nr] != '!') {
        if (*names[nr] != '\n') {
          strcat(result, names[nr]);
          strcat(result, " ");
        } else {
          strcat(result, "UNDEFINED");
          strcat(result, " ");
        }
      }
    if (*names[nr] != '\n')
      nr++;
  }

  if (!*result)
    strcat(result, "NOBITS");
}

void sprint64bit(uint *vektor, const char *names[], char *result) {
  int nr, v;

  if (!vektor)
    return;

  sprintbit(*vektor, names, result);
  if (!str_cmp(result, "NOBITS"))
    *result = '\0';

  for (nr = 0; nr < 32; nr++)
    if (*names[nr] == '\n')
      return;

  v = vektor[1];
  for (nr = 32; v; v >>= 1) {
    if (IS_SET(1, v))
      if (*names[nr] != '!') {
        if (*names[nr] != '\n') {
          strcat(result, names[nr]);
          strcat(result, " ");
        } else {
          strcat(result, "UNDEFINED");
          strcat(result, " ");
        }
      }
    if (*names[nr] != '\n')
      nr++;

    if (nr > 32)
      v &= 0x7fffffff;
  }
  if (!*result)
    strcat(result, "NOBITS");
}

void sprinttype(int type, const char *names[], char *result) {
  int nr;

  for (nr = 0; (*names[nr] != '\n'); nr++);
  if (type < nr)
    strcpy(result, names[type]);
  else
    strcpy(result, "UNDEFINED");
}

/* Calculate the REAL time passed over the last t2-t1 centuries (secs) */

void real_time_passed(struct time_info_data *now, time_t t2, time_t t1) {
  int secs;

  secs = (int) (t2 - t1);

  now->second = secs % 60; /* 0 - 59 seconds */
  secs -= now->second;

  now->minute = (secs / 60) % 60; /* 0 - 59 minutes */
  secs -= 60 * now->minute;

  now->hour = (secs / SECS_PER_REAL_HOUR) % 24; /* 0 - 23 hours   */
  secs -= SECS_PER_REAL_HOUR * now->hour;

  now->day = (secs / SECS_PER_REAL_DAY); /* 0+ days        */
  secs -= SECS_PER_REAL_DAY * now->day;

  now->month = -1;
  now->year = -1;
}

/* Calculate the MUD time passed over the last t2-t1 centuries (secs) */

void mud_time_passed(struct time_info_data *now, time_t t2, time_t t1) {
  int secs;

  secs = (int) (t2 - t1);

  now->second = secs % SECS_PER_MUD_HOUR;
  secs -= now->second;

  now->hour = (secs / SECS_PER_MUD_HOUR) % 24; /* 0..23 hours  */
  secs -= SECS_PER_MUD_HOUR * now->hour;

  now->day = (secs / SECS_PER_MUD_DAY) % 35; /* 0..34 days   */
  secs -= SECS_PER_MUD_DAY * now->day;

  now->month = (secs / SECS_PER_MUD_MONTH) % 17; /* 0..16 months */
  secs -= SECS_PER_MUD_MONTH * now->month;

  now->year = (secs / SECS_PER_MUD_YEAR); /* 0..XX? years */
}

void age(struct time_info_data *player_age, P_char ch) {
  mud_time_passed(player_age, time(0), ch->player.time.birth); /* natural aging */

  if (GET_CLASS(ch) == CLASS_LICH)
    player_age->year = 18;

  player_age->year += ch->player.time.perm_aging; /* permanent 'unnatural' aging */

  player_age->year += ch->player.time.age_mod; /* temporary 'unnatural' aging */

  player_age->year = MAX(0, player_age->year); /* since I don't want to deal with 'infants' */

  player_age->year += racial_data[(int) GET_RACE(ch)].base_age;
}

int exist_in_equipment(P_char ch, int bitflag) {
  register int i;

  for (i = 0; i < MAX_WEAR; i++) {
    if (ch->equipment[i])
      if (IS_SET(ch->equipment[i]->extra_flags, bitflag))
        return TRUE;
  }
  return FALSE;
}

/* Functions added by AC */

#ifndef USE_MACRO

/*
 ** Determine visibility of a person
 */

/* this function is called about a billion times a minute (ok, so I exaggerated a little), don't even
   THINK about doing anything to it that would make it slower, even a LITTLE bit.  JAB */

/* Base function for CAN_SEE */
int ac_can_see(P_char sub, P_char obj) {
  /* minor detail, sleeping chars can't see squat! */

  if (!AWAKE(sub))
    return 0;

  /* rearranging this by frequency of calls, NPCs call it about 99%,then PCs, then gods, some checks
     apply only to NPCs, some to PCs, and the order is different */

  if (IS_NPC(sub)) {

    /* Object is invisible and subject does not have detect invis */
    if (IS_AFFECTED(obj, AFF_INVISIBLE) && !IS_AFFECTED(sub, AFF_DETECT_INVISIBLE))
      return 0;

    /* Subject is blinded */
    if (IS_AFFECTED(sub, AFF_BLIND))
      return 0;

    if (IS_AFFECTED(obj, AFF_HIDE) && (sub != obj))
      return 0;

    /* Room is magically dark */
    if (IS_CSET(world[sub->in_room].room_flags, MAGIC_DARK))
      return 0;

    /* Determine visibility by "vis" command */
    if (WIZ_INVIS(sub, obj))
      return 0;

    /* as wraithform is kind of kludge, it is shown nowhere. */
    if (IS_AFFECTED(obj, AFF_WRAITHFORM))
      return 0;

    if (IS_LIGHT(obj->in_room)) {
      if (IS_SUNLIT(obj->in_room) && IS_AFFECTED((sub), AFF_ULTRAVISION))
        return 0; /* can't see ANYTHING */
      else
        return 1;
    } else {
      if (IS_AFFECTED((sub), AFF_ULTRAVISION))
        return 1; /* can see ANYTHING */

      if (IS_AFFECTED((sub), AFF_INFRAVISION)) {
        if (racial_traits[GET_RACE(obj)].emits_heat)
          return 1; /* will see 'red shape' */
        else
          return 0; /* invis to infravision */
      }
      return 0;
    }
  } else {
    /* PCs */

    /* Determine visibility by "vis" command */
    if (WIZ_INVIS(sub, obj))
      return 0;

    /* Immortal can see anything */
    if (IS_TRUSTED(sub))
      return 1;

    /* Room is magically dark */
    if (IS_CSET(world[sub->in_room].room_flags, MAGIC_DARK))
      return 0;

    /* as wraithform is kind of kludge, semi-godsight. */
    if (IS_AFFECTED(sub, AFF_WRAITHFORM))
      return 1;

    /* Object is invisible and subject does not have detect invis */
    /* but person can see self -Azuth */
    if (IS_AFFECTED(obj, AFF_INVISIBLE) && !IS_AFFECTED(sub, AFF_DETECT_INVISIBLE) && (sub != obj))
      return 0;

    /* Subject is blinded */

    if (IS_AFFECTED(sub, AFF_BLIND))
      return 0;

    if (IS_AFFECTED(obj, AFF_HIDE) && (sub != obj))
      return 0;

    /* as wraithform is kind of kludge, it is shown nowhere. */
    if (IS_AFFECTED(obj, AFF_WRAITHFORM))
      return 0;

    if (IS_LIGHT(obj->in_room)) {
      if (IS_SUNLIT(obj->in_room) && IS_AFFECTED((sub), AFF_ULTRAVISION))
        return 0; /* can't see ANYTHING */
      else
        return 1;
    } else {
      if (IS_AFFECTED((sub), AFF_ULTRAVISION))
        return 1; /* can see ANYTHING */

      if (IS_AFFECTED((sub), AFF_INFRAVISION)) {
        if (racial_traits[GET_RACE(obj)].emits_heat)
          return 1; /* will see 'red shape' */
        else
          return 0; /* invis to infravision */
      }
      return 0;
    }
  }

  /* Otherwise, object can be seen by subject */
  return 1;
}

/*
 ** Determine visibility of an object
 */

int ac_can_see_obj(P_char sub, P_obj obj) {
  /* wraiths can't see any objects */
  if (IS_AFFECTED(sub, AFF_WRAITHFORM))
    return 0;

  /* minor detail, sleeping chars can't see squat! */

  if (!AWAKE(sub))
    return 0;

  /* Check to see if object is wizinvis */

  if ((OBJ_WORN(obj) && WIZ_INVIS(sub, obj->loc.wearing)) ||
          (OBJ_CARRIED(obj) && WIZ_INVIS(sub, obj->loc.carrying)))
    return 0;

  /* Immortal can see anything */

  if (IS_TRUSTED(sub))
    return 1;

  if (IS_NOSHOW(obj))
    return 0;

  /* Check to see if object is invis */

  if (obj)
    if ((IS_SET(obj->extra_flags, ITEM_INVISIBLE) && !IS_AFFECTED(sub, AFF_DETECT_INVISIBLE)))
      return 0;

  if (obj)
    if (IS_SET((obj)->extra_flags, ITEM_SECRET))
      return 0;

  /* Room is magically dark */

  if (IS_CSET(world[sub->in_room].room_flags, MAGIC_DARK))
    return 0;

  /* Check if subject is blind */

  if (IS_AFFECTED(sub, AFF_BLIND))
    return 0;

  /* The ultravision exclusion */

  if (IS_SUNLIT(sub->in_room) && IS_AFFECTED(sub, AFF_ULTRAVISION))
    return 0;

  /* The ultravision exclusion */

  if (IS_DARK(sub->in_room) && !IS_AFFECTED(sub, AFF_ULTRAVISION))
    return 0;

  /* Otherwise, can see object */

  return 1;
}

#endif /* #ifndef USE_MACRO */

int coin_type(char *s) {
  if (is_abbrev(s, "copper"))
    return (COIN_COPPER);
  else if (is_abbrev(s, "silver"))
    return (COIN_SILVER);
  else if (is_abbrev(s, "gold"))
    return (COIN_GOLD);
  else if (is_abbrev(s, "platinum"))
    return (COIN_PLATINUM);
  else
    return (-1);
}

char *coin_stringv(int amount) {
  int p, g, s;
  static char buf[MAX_INPUT_LENGTH];

  buf[0] = 0;
  if (!amount)
    return (buf);
  p = amount / 1000;
  amount -= p * 1000;
  g = amount / 100;
  amount -= g * 100;
  s = amount / 10;
  amount -= s * 10;

  if (p) {
    sprintf(buf, "&N%d &+Wplatinum&N", p);
  }
  if (g) {
    if (p && !s && !amount)
      sprintf(buf + strlen(buf), ", and ");
    else if (p)
      sprintf(buf + strlen(buf), ", ");
    sprintf(buf + strlen(buf), "&N%d &+Ygold&N", g);
  }
  if (s) {
    if ((p || g) && !amount)
      sprintf(buf + strlen(buf), ", and ");
    else if (p || g)
      sprintf(buf + strlen(buf), ", ");
    sprintf(buf + strlen(buf), "&N%d silver", s);
  }
  if (amount) {
    if (p || g || s)
      sprintf(buf + strlen(buf), ", and ");
    sprintf(buf + strlen(buf), "&N%d &+ycopper&N", amount);
  }
  sprintf(buf + strlen(buf), " coin%s&N", ((p + g + s + amount) > 1) ? "s" : "");

  return buf;
}

/* Might be used by functions that require less spammier coin output */

char *coin_stringv_short(int amount) {
  int p, g, s;
  static char buf[MAX_INPUT_LENGTH];

  buf[0] = 0;
  if (!amount)
    return (buf);
  p = amount / 1000;
  amount -= p * 1000;
  g = amount / 100;
  amount -= g * 100;
  s = amount / 10;
  amount -= s * 10;

  if (p) {
    sprintf(buf, "&N%d &+Wp&N", p);
  }
  if (g) {
    if (p && !s && !amount)
      sprintf(buf + strlen(buf), ", and ");
    else if (p)
      sprintf(buf + strlen(buf), ", ");
    sprintf(buf + strlen(buf), "&N%d &+Yg&N", g);
  }
  if (s) {
    if ((p || g) && !amount)
      sprintf(buf + strlen(buf), ", and ");
    else if (p || g)
      sprintf(buf + strlen(buf), ", ");
    sprintf(buf + strlen(buf), "&N%d s", s);
  }
  if (amount) {
    if (p || g || s)
      sprintf(buf + strlen(buf), ", and ");
    sprintf(buf + strlen(buf), "&N%d &+yc&N", amount);
  }
  sprintf(buf + strlen(buf), " coin%s&N", ((p + g + s + amount) > 1) ? "s" : "");

  return buf;
}

/* adds flat amount (in copper), to ch, using smallest number of coins */

void ADD_MONEY(P_char ch, int amount) {
  int t = 0;

  if (amount < 0)
    dump_core();

  if (amount == 0)
    return;

  if (amount > 999) {
    t = amount / 1000;
    GET_PLATINUM(ch) += t;
    amount -= t * 1000;
  }
  if (amount > 99) {
    t = amount / 100;
    GET_GOLD(ch) += t;
    amount -= t * 100;
  }
  if (amount > 9) {
    t = amount / 10;
    GET_SILVER(ch) += t;
    amount -= t * 10;
  }
  if (amount)
    GET_COPPER(ch) += amount;
}

/* mode 0 = make change, return amount of change made */
/* mode 1 = subtract extra, return extra amount subtracted */
/* mode 2 = subtract as close as possible without going over, return amount short */

/* modes 1 & 2 not supported yet */

int SUB_MONEY(P_char ch, int amount, int mode) {
  int t = 0;

  if (amount <= 0)
    return -1;
  if (amount > GET_MONEY(ch))
    return -1;
  if (mode != 0)
    return -1;

  if (amount > GET_COPPER(ch)) {
    amount -= GET_COPPER(ch);
    GET_COPPER(ch) = 0;
  } else {
    GET_COPPER(ch) -= amount;
    return 0;
  }

  if (amount > 0) {
    t = GET_SILVER(ch) * 10;
    if (amount >= t) {
      amount -= t;
      GET_SILVER(ch) = 0;
    } else {
      t = (int) (amount / 10) + 1;
      GET_SILVER(ch) -= t;
      amount -= t * 10;
    }
  }
  if (amount > 0) {
    t = GET_GOLD(ch) * 100;
    if (amount >= t) {
      amount -= t;
      GET_GOLD(ch) = 0;
    } else {
      t = (int) (amount / 100) + 1;
      GET_GOLD(ch) -= t;
      amount -= t * 100;
    }
  }
  if (amount > 0) {
    t = GET_PLATINUM(ch) * 1000;
    if (amount >= t) {
      amount -= t;
      GET_PLATINUM(ch) = 0;
    } else {
      t = (int) (amount / 1000) + 1;
      GET_PLATINUM(ch) -= t;
      amount -= t * 1000;
    }
  }
  if (amount < 0)
    ADD_MONEY(ch, -(amount));

  return 0;
}

void strToLower(char *s1) {
  int i, len;

  len = strlen(s1);
  for (i = 0; i < len; i++) {
    if (s1[i] >= 'A' && s1[i] <= 'Z')
      s1[i] += 'a' - 'A';
  }
} /* strToLower */

/* I'm such a genius!  Talk about your redundant code!  Does basic sanity
   checks on a char_data struct, logs it and returns FALSE if there is a
   problem, else returns TRUE.  I really hate to think about how many places
   this can be (or should be) used. -JAB */

bool SanityCheck(P_char ch, const char *calling) {
  if (!ch) {
    logit(LOG_EXIT, "Call to %s() with NULL ch", calling);
    dump_core();
    return FALSE;
  }
  if (ch->in_room == -1) {
    logit(LOG_EXIT, "%s in room -1 in call to %s().", GET_NAME(ch), calling);
    dump_core();
    return FALSE;
  }
  return TRUE;
}

/* another stroke of genius (damn I'm modest).  Goes SanityCheck one better,
   this check for validity of both ch and vic, and also checks to see that
   ch is fighting, and that ch and vic are in the same room.  Returns FALSE
   if any check fails, TRUE if everything is fine. */

bool FightingCheck(P_char ch, P_char vic, const char *calling) {
  if (!SanityCheck(ch, calling))
    return FALSE;

  if (!SanityCheck(vic, calling))
    return FALSE;

  if (!IS_FIGHTING(ch)) {
    logit(LOG_DEBUG, "%s not fighting %s in call to %s().",
            GET_NAME(ch), GET_NAME(vic), calling);
    return FALSE;
  }
  if (ch->in_room != vic->in_room) {
    stop_fighting(ch);
    stop_fighting(vic);
    logit(LOG_DEBUG,
            "%s [%d] and %s [%d] not in same room while fighting in %s().",
            GET_NAME(ch), world[ch->in_room].number,
            GET_NAME(vic), world[vic->in_room].number, calling);
    return FALSE;
  }
  return TRUE;
}

/* calcs number of moves needed to move from current room to room in direction
   'dir', returns -1 on error, else 0+ for moves needed.  -JAB */

int move_cost(P_char ch, int dir) {
  P_obj obj;
  bool has_boat;
  int moves, direction, i;

  if (!SanityCheck(ch, "move_cost"))
    return -1;

  if ((dir < 0) || (dir > 5))
    return -1;

  if (GET_RACE(ch) == RACE_GOLEM) /* Golems never tire!!! Ilsie */
    return 0;

  if (IS_AFFECTED(ch, AFF_FLY))
    moves = 1;
  else if (IS_AFFECTED(ch, AFF_LEVITATE) && ((dir == 4) || (dir == 5)))
    moves = 1;
  else /* Modified for room dimensions! --MIAX 081096 */ {
    /* First find the direction of movement. */
    if ((dir == 1) || (dir == 3))
      direction = 1;
    else if ((dir == 0) || (dir == 2))
      direction = 2;
    else
      direction = 3;

    /* Now base movement cost on distance it takes to travel from the middle
       of this room to the middle of the next room.                         */
    if (direction == 1)
      moves = ((world[ch->in_room].length + world[world[ch->in_room].dir_option[dir]->to_room].length) / 25);
    else if (direction == 2)
      moves = ((world[ch->in_room].width + world[world[ch->in_room].dir_option[dir]->to_room].length) / 25);
    else if (direction == 3)
      moves = ((world[ch->in_room].height + world[world[ch->in_room].dir_option[dir]->to_room].height) / 25);

    /*  Old format based on sector type removed.
        moves = movement_loss[(int) world[ch->in_room].sector_type] +
          movement_loss[(int) world[world[ch->in_room].dir_option[dir]->to_room].sector_type];
     */

    /* Now add a move or two for vertical movement, which is harder, kinda
       like walking stairs and such. Also add modifiers for sector types,
       which now can do as they were intended = describe the terrain we are
       crossing over. The modifiers are not dump modifiers, they take into
       account the PC race as well as the length of travel to be made.     */

    if (dir == 4)
      moves = (moves + (moves / 2));
    else {
      if (dir == 5)
        moves = (moves + (moves / 3));
    }

#ifdef TRUE                       /* converted the if chain check to a switch statement
               * easier to read and you get a better optimization when
               * compiling       -- Alth Jan 98 */

    switch (world[ch->in_room].sector_type) {

      case SECT_INSIDE:
      case SECT_CITY:
      {
        moves = (moves - (moves / 3));
        break;
      }

      case SECT_WATER_SWIM:
      case SECT_WATER_NOSWIM:
      {
        has_boat = FALSE;

        if (IS_NPC(ch) && racial_traits[GET_RACE(ch)].can_swim)
          has_boat = TRUE;

        /* See if char is carrying a boat */
        for (obj = ch->carrying; obj && !has_boat; obj = obj->next_content)
          if ((obj->type == ITEM_BOAT) && (obj->wear_flags == ITEM_TAKE))
            has_boat = TRUE;

        /* See if char is wearing a boat (water walking items actually) */
        for (i = 0; (i < MAX_WEAR) && !has_boat; i++)
          if (ch->equipment[i] && (ch->equipment[i]->type == ITEM_BOAT))
            has_boat = TRUE;

        if (!has_boat && !IS_TRUSTED(ch) && !IS_AFFECTED(ch, AFF_WRAITHFORM) &&
                !IS_AFFECTED(ch, AFF_FLY) && !IS_AFFECTED(ch, AFF_LEVITATE)) {
          moves = (moves + (moves / 2));
        }
        break;
      }

      case SECT_FOREST:
      {
        if (GET_RACE(ch) == RACE_GREY)
          moves = (moves - (moves / 2));
        break;
      }

      case SECT_MOUNTAIN:
      {
        if ((GET_RACE(ch) == RACE_MOUNTAIN) || (GET_RACE(ch) == RACE_OGRE) ||
                (GET_RACE(ch) == RACE_TROLL))
          moves = (moves - (moves / 2));
        break;
      }

      case SECT_FIELD:
      {
        if (GET_RACE(ch) == RACE_BARBARIAN)
          moves = (moves - (moves / 2));
        break;
      }

      case SECT_UNDRWLD_WILD:
      {
        if (GET_RACE(ch) == RACE_DUERGAR || GET_RACE(ch) == RACE_ILLITHID ||
                GET_RACE(ch) == RACE_DROW)
          moves = (moves - (moves / 2));
        break;
      }

      case SECT_HILLS:
      {
        if ((GET_RACE(ch) == RACE_BARBARIAN) || (GET_RACE(ch) == RACE_OGRE) ||
                (GET_RACE(ch) == RACE_TROLL))
          moves = (moves - (moves / 3));
        break;
      }

      case SECT_FIREPLANE:
      case SECT_SMOKE_PLANE:
      case SECT_MAGMA_PLANE:
      case SECT_ASTRAL:
      case SECT_ETHEREAL:
        moves = 1;
        break;

      default:
        break;
    }

#else

    if ((world[ch->in_room].sector_type == SECT_INSIDE) ||
            (world[ch->in_room].sector_type == SECT_CITY))
      moves = (moves - (moves / 3));

    else if ((world[ch->in_room].sector_type == SECT_WATER_SWIM) ||
            (world[ch->in_room].sector_type == SECT_WATER_NOSWIM)) {
      has_boat = FALSE;

      if (IS_NPC(ch) && racial_traits[GET_RACE(ch)].can_swim)
        has_boat = TRUE;

      /* See if char is carrying a boat */
      for (obj = ch->carrying; obj && !has_boat; obj = obj->next_content)
        if ((obj->type == ITEM_BOAT) && (obj->wear_flags == ITEM_TAKE))
          has_boat = TRUE;

      /* See if char is wearing a boat (water walking items actually) */
      for (i = 0; (i < MAX_WEAR) && !has_boat; i++)
        if (ch->equipment[i] && (ch->equipment[i]->type == ITEM_BOAT))
          has_boat = TRUE;

      if (!has_boat && !IS_TRUSTED(ch) && !IS_AFFECTED(ch, AFF_WRAITHFORM) &&
              !IS_AFFECTED(ch, AFF_FLY) && !IS_AFFECTED(ch, AFF_LEVITATE)) {
        moves = (moves + (moves / 2));
      }
    }
    else if (world[ch->in_room].sector_type == SECT_FOREST)
      if (GET_RACE(ch) == RACE_GREY)
        moves = (moves - (moves / 2));

      else if (world[ch->in_room].sector_type == SECT_MOUNTAIN)
        if ((GET_RACE(ch) == RACE_MOUNTAIN) || (GET_RACE(ch) == RACE_OGRE) ||
                (GET_RACE(ch) == RACE_TROLL))
          moves = (moves - (moves / 2));

        else if (world[ch->in_room].sector_type == SECT_FIELD)
          if (GET_RACE(ch) == RACE_BARBARIAN)
            moves = (moves - (moves / 2));

          else if (world[ch->in_room].sector_type == SECT_UNDRWLD_WILD)
            if (GET_RACE(ch) == RACE_DUERGAR || GET_RACE(ch) == RACE_ILLITHID ||
                    GET_RACE(ch) == RACE_DROW)
              moves = (moves - (moves / 2));

            else if (world[ch->in_room].sector_type == SECT_HILLS)
              if ((GET_RACE(ch) == RACE_BARBARIAN) || (GET_RACE(ch) == RACE_OGRE) ||
                      (GET_RACE(ch) == RACE_TROLL))
                moves = (moves - (moves / 3));

              else if ((world[ch->in_room].sector_type == SECT_FIREPLANE) ||
                      (world[ch->in_room].sector_type == SECT_SMOKE_PLANE) ||
                      (world[ch->in_room].sector_type == SECT_MAGMA_PLANE) ||
                      (world[ch->in_room].sector_type == SECT_ASTRAL) ||
                      (world[ch->in_room].sector_type == SECT_ETHEREAL))
                moves = 1;
#endif

    /* Old modifier, this should be re-written at some point, its lame. */
    /* I altered it for now to be more compatable with room dimensions. */
    moves = ((load_modifier(ch) - 150) / 25) + moves;

    if ((movement_loss[(int) world[ch->in_room].sector_type] != 0) && (moves <= 0))
      moves = 1;
  }

  /* Final sanity check, lets be real about this! */
  if (moves > 15)
    moves = 15;
  else if (moves > 10)
    moves = moves - 2;

  // Reduce all of this by 1/3rd  for a test...if it works well, will mod above
  if (moves >= 2)
    moves *= .67;

  return moves;
}

/* little routine to count chars in room that are attacking 'ch' -JAB */

int NumAttackers(P_char ch) {
  P_char tch;
  int total = 0;

  if (!SanityCheck(ch, "NumAttackers"))
    return -1;

  if (ALONE(ch))
    return 0;

  for (tch = world[ch->in_room].people; tch; tch = tch->next_in_room)
    if (tch->specials.fighting && (tch->specials.fighting == ch))
      total++;

  return total;
}

/* ugly code all over, so I'm making it a function.  Warning:  this function is called
   extremely often, even a slight increase in size will strongly affect cpu load. JAB */

bool is_aggr_to(P_char ch, P_char target) {
  int p_u_lev = 0;
  struct affected_type *af;

  /* common checks first */

  if (!ch || !target || (ch == target) || (ch->in_room != target->in_room) ||
          IS_FIGHTING(ch) || !AWAKE(ch) || CHAR_IN_SAFE_ZONE(ch) || !CAN_SEE(ch, target))
    return FALSE;

  /* the definitive AGGIMMUNE */
  if ((IS_TRUSTED(target) && IS_CSET(target->only.pc->pcact, PLR_AGGIMMUNE)))
    return FALSE;

  /* the not so definitive REPULSION */
  if (IS_AFFECTED(target, AFF_REPULSION) && (GET_LEVEL(target) / 2) >= GET_LEVEL(ch))
    return FALSE;

  // The somewhat but not completely definitive MASSMORPH
  if (GET_GROUP(target) && IS_SET((GET_GROUP(target))->flags, GAFF_MASSMORPH) &&
          (GET_GROUP_LEADER(GET_GROUP(target))->in_room == target->in_room))
    return FALSE;

  if (IS_AFFECTED(ch, AFF_MINOR_PARALYSIS) || IS_AFFECTED(ch, AFF_MAJOR_PARALYSIS))
    return FALSE;

  if (IS_CSET(world[ch->in_room].room_flags, SINGLE_FILE) && !AdjacentInRoom(ch, target))
    return FALSE;

  /* now needs a master to inhibit aggression */
  if (ch->following && IS_AFFECTED(ch, AFF_CHARM))
    return FALSE;

  /* grumble, ah well, has to be done.  JAB */
  if (IS_UNDEAD(ch) && !IS_UNDEAD(target) && affected_by_spell(target, SPELL_PROT_FROM_UNDEAD))
    for (af = target->affected; af; af = af->next)
      if (af->type == SPELL_PROT_FROM_UNDEAD) {
        p_u_lev = af->modifier;
        break;
      }

  if (IS_ANIMAL(ch) && affected_by_spell(target, SPELL_PROTECTION_FROM_ANIMALS))
    for (af = target->affected; af; af = af->next)
      if (af->type == SPELL_PROTECTION_FROM_ANIMALS) {
        p_u_lev = af->modifier;
        break;
      }

  /* now different checks for pcs/npcs */

  if (IS_NPC(ch)) {

    if (IS_NPC(target) && (!target->following || IS_NPC(target->following)) && !IS_MORPH(target))
      return FALSE;

    if (IS_NPC(target) && IS_CSET(target->only.npc->npcact, ACT_MOUNT))
      return FALSE;

    /* after the above check, we are dealing with a pet or a PC */

    if (IS_CSET(ch->only.npc->npcact, ACT_WIMPY) && (GET_HIT(ch) < (GET_LEVEL(ch) * 4)))
      return FALSE;

    /* Smart high level mobs have seen pets before, they'll not worry about 'em
    if (GET_INT(ch) > 50 && GET_LEVEL(ch) > 40 && IS_PET(target)) {
      return FALSE;
    }
     */

    if (p_u_lev < GET_LEVEL(ch)) {
      if (!IS_CSET(ch->only.npc->npcact, ACT_AGG_RACEGOOD) &&
              !IS_CSET(ch->only.npc->npcact, ACT_AGG_RACEEVIL) &&
              !IS_CSET(ch->only.npc->npcact, ACT_AGGRESSIVE_EVIL) &&
              !IS_CSET(ch->only.npc->npcact, ACT_AGGRESSIVE_GOOD) &&
              (IS_AGGRESSIVE(ch)))
        return TRUE;

      /* uses special race check to allow for disguises - CRM */
      if (IS_CSET(ch->only.npc->npcact, (ACT_AGG_RACEGOOD))) {
        if ((GET_RACE1(target) == RACE_GREY) ||
                (GET_RACE1(target) == RACE_HUMAN) ||
                (GET_RACE1(target) == RACE_MOUNTAIN) ||
                (GET_RACE1(target) == RACE_GNOME) ||
                (GET_RACE1(target) == RACE_BARBARIAN) ||
                (GET_RACE1(target) == RACE_HALFELF) ||
                (GET_RACE1(target) == RACE_HALFLING)) {
          return (TRUE);
        }
      } else if (IS_CSET(ch->only.npc->npcact, (ACT_AGG_RACEEVIL))) {
        if ((GET_RACE1(target) == RACE_DROW) ||
                (GET_RACE1(target) == RACE_OGRE) ||
                (GET_RACE1(target) == RACE_DUERGAR) ||
                (GET_RACE1(target) == RACE_TROLL) ||
                (GET_RACE1(target) == RACE_PORC) ||
                (GET_RACE1(target) == RACE_ILLITHID) ||
                (GET_RACE1(target) == RACE_LICH) ||
                (GET_CLASS(target) == CLASS_LICH) ||
                (GET_RACE1(target) == RACE_YUANTI)) {
          return (TRUE);
        }
      }

      /* uses special alignment checks that allow for disguises  - CRM*/
      if ((!IS_AFFECTED(target, AFF_NONDETECTION)) &&
              ((IS_EVIL1(target) && IS_CSET(ch->only.npc->npcact, ACT_AGGRESSIVE_EVIL)) ||
              (IS_GOOD1(target) && IS_CSET(ch->only.npc->npcact, ACT_AGGRESSIVE_GOOD)) ||
              (IS_NEUTRAL1(target) && IS_CSET(ch->only.npc->npcact, ACT_AGGRESSIVE_NEUTRAL))))
        return TRUE;

      /* try to kill invaders on sight */
      if (IS_CSET(ch->only.npc->npcact, ACT_AGG_OUTCAST) && IS_PC(target) &&
              IS_INVADER(target) && !IS_INVADER_DISGUISED(target))
        return TRUE;

      /* justice_hook */
      if (IS_CSET(ch->only.npc->npcact, ACT_AGG_OUTCAST) && IS_PC(target) &&
              (PC_JUSTICE_FLAGS(target) == JUSTICE_IS_OUTCAST) && !IS_INVADER_DISGUISED(target))
        return TRUE;
    }

    /* Non-justice agg_outc hook */
    /* Commented out since the that flag is now used for DELAY_HUNTER
       if (IS_CSET(ch->only.npc->npcact, ACT_AGG_OUTLAW) && IS_PC(target) &&
       IS_CSET(target->only.pc->pcact, PLR_OUTCAST) && !IS_DISGUISE(target))
       return TRUE;
     */

    /* attack whatever they set us to be aggressive to..  -- Diirinka Nov 31 1997 */
    if (IS_CSET(ch->only.npc->aggressive, GET_RACE1(target)))
      return TRUE;

    /* if target in memory, then it acts as if it's agg, even if it's not */
    if (IS_PC(target) && HAS_MEMORY(ch) && (GET_MEMORY(ch) != NULL) &&
            mem_inMemory(GET_MEMORY(ch), GET_NAME(target)) && !IS_DISGUISE_NPC(target))
      return TRUE;

    /* hook for trade system makes all raider mobs attack all merchant mobs */
    /*if ((IS_AFFECTED(ch, AFF_RAIDER)) && (IS_AFFECTED(target, AFF_MERCHANT)))
      return TRUE;
     */

    /* not trigger, so it's not aggr */
    return FALSE;

  } else {

    if (IS_PC(target))
      return FALSE;

    if (affected_by_spell(ch, SKILL_BERSERK))
      return TRUE; /* cackle! (even better, after pkill, this goes BEFORE the IS_PC check) */

    if (GET_WIMPY(ch) > GET_HIT(ch)) /* not agg and wimpy both */
      return FALSE;

    if (!IS_CSET(ch->only.pc->pcact, PLR_VICIOUS)) {
      if (!AWAKE(target) || IS_AFFECTED(target, AFF_MINOR_PARALYSIS) ||
              IS_AFFECTED(target, AFF_MAJOR_PARALYSIS))
        return FALSE;
    }
    if ((ch->only.pc->aggressive != -1) && (ch->only.pc->aggressive < GET_HIT(ch)) && is_aggr_to(target, ch))
      return TRUE;
  }

  return FALSE;
}

/* Basicaly a copy of is_aggr_to() but makes the checks to see if something
 * is aggro when the ch and target are _not_ in the same room.. Put in separate
 * function to avoid adding additional checks to the is_aggr_to().. Added for
 * do_sense_danger type of stuff..                   Diirinka (Jan 6, 97)
 * ughh.. this sucks.. :P                           -- Diirinka Nov 31 97  */

bool is_aggr_to_remote(P_char ch, P_char target) {
  /* common checks first */

  if (!ch || !target || (ch == target) || CHAR_IN_SAFE_ZONE(ch))
    return FALSE;

  /* the definitive AGGIMMUNE */
  if ((IS_TRUSTED(target) && IS_CSET(target->only.pc->pcact, PLR_AGGIMMUNE)))
    return FALSE;

  /* now needs a master to inhibit aggression */
  if (ch->following && IS_AFFECTED(ch, AFF_CHARM))
    return FALSE;

  /* now different checks for pcs/npcs */

  if (IS_NPC(ch)) {

    if (IS_NPC(target) && (!target->following || IS_NPC(target->following)) && !IS_MORPH(target))
      return FALSE;

    /* after the above check, we are dealing with a pet or a PC */

    if (IS_CSET(ch->only.npc->npcact, ACT_WIMPY) && (GET_HIT(ch) < (GET_LEVEL(ch) * 4)))
      return FALSE;

    if (!IS_CSET(ch->only.npc->npcact, ACT_AGG_RACEGOOD) &&
            !IS_CSET(ch->only.npc->npcact, ACT_AGG_RACEEVIL) &&
            !IS_CSET(ch->only.npc->npcact, ACT_AGGRESSIVE_EVIL) &&
            !IS_CSET(ch->only.npc->npcact, ACT_AGGRESSIVE_GOOD) &&
            (IS_AGGRESSIVE(ch)))
      return TRUE;


    if (IS_CSET(ch->only.npc->npcact, (ACT_AGG_RACEGOOD))) {
      if ((GET_RACE(target) == RACE_GREY) ||
              (GET_RACE(target) == RACE_HUMAN) ||
              (GET_RACE(target) == RACE_MOUNTAIN) ||
              (GET_RACE(target) == RACE_GNOME) ||
              (GET_RACE(target) == RACE_BARBARIAN) ||
              (GET_RACE(target) == RACE_HALFELF) ||
              (GET_RACE(target) == RACE_HALFLING)) {
        return (TRUE);
      }
    } else if (IS_CSET(ch->only.npc->npcact, (ACT_AGG_RACEEVIL))) {
      if ((GET_RACE(target) == RACE_DROW) ||
              (GET_RACE(target) == RACE_OGRE) ||
              (GET_RACE(target) == RACE_DUERGAR) ||
              (GET_RACE(target) == RACE_PORC) ||
              (GET_RACE(target) == RACE_TROLL) ||
              (GET_RACE(target) == RACE_ILLITHID) ||
              (GET_CLASS(target) == CLASS_LICH) ||
              (GET_RACE(target) == RACE_LICH) ||
              (GET_RACE(target) == RACE_YUANTI)) {
        return (TRUE);
      }
    }

    if ((IS_EVIL(target) && IS_CSET(ch->only.npc->npcact, ACT_AGGRESSIVE_EVIL)) ||
            (IS_GOOD(target) && IS_CSET(ch->only.npc->npcact, ACT_AGGRESSIVE_GOOD)) ||
            (IS_NEUTRAL(target) && IS_CSET(ch->only.npc->npcact, ACT_AGGRESSIVE_NEUTRAL)))
      return TRUE;

    /* attack whatever they set us to be aggressive to..  -- Diirinka Nov 31 */

    if (IS_CSET(ch->only.npc->aggressive, target->player.race))
      return TRUE;

    /* try to kill invaders on sight */

    if (IS_CSET(ch->only.npc->npcact, ACT_AGG_OUTCAST) && IS_PC(target) &&
            IS_INVADER(target))
      return TRUE;

    /* justice_hook */
    if (IS_CSET(ch->only.npc->npcact, ACT_AGG_OUTCAST) && IS_PC(target) &&
            (PC_JUSTICE_FLAGS(target) == JUSTICE_IS_OUTCAST))
      return TRUE;

    /* if target in memory, then it acts as if it's agg, even if it's not */
    if (IS_PC(target) && HAS_MEMORY(ch) && (GET_MEMORY(ch) != NULL) &&
            mem_inMemory(GET_MEMORY(ch), GET_NAME(target)))
      return TRUE;

#if 0
    /* Dumb-ass NPC-sentinels who leave their post to chase players
       for a single step are aggro to exactly anyone in a room. */

    if (ch->specials.arena_color && IS_CSET(ch->only.npc->npcact, ACT_SENTINEL) &&
            (ch->in_room != ch->specials.arena_color))
      return TRUE;
#endif

    /* not trigger, so it's not aggr */
    return FALSE;

  } else {

    if (IS_PC(target))
      return FALSE;

    if (affected_by_spell(ch, SKILL_BERSERK))
      return TRUE; /* cackle! (even better, after pkill, this goes BEFORE the IS_PC check) */

    if (GET_WIMPY(ch) > GET_HIT(ch)) /* not agg and wimpy both */
      return FALSE;

    if ((ch->only.pc->aggressive != -1) && (ch->only.pc->aggressive < GET_HIT(ch)) && is_aggr_to(target, ch))
      return TRUE;
  }

  return FALSE;
}

/* saving throws based on ch's stats, rather than magical saves.  Only DEX
   is imped so far, but all stats can be done with this function.  'mod' is
   only any additional modifiers, this routine checks for all 'normal' mods
   to a stat-based save.  For simplicity, it uses the APPLY_* macros for 'stat'
   JAB */

bool StatSave(P_char ch, int stat, int mod) {
  int save_num;

  SanityCheck(ch, "StatSave");

  switch (stat) {
    case APPLY_AGI:
      save_num = STAT_INDEX(GET_C_AGI(ch)) + mod;

      /* Moving this to Dex check.  Has would make you more dextrous not agile
          if (IS_AFFECTED(ch, AFF_HASTE))
            save_num *= 2;

          if (IS_AFFECTED(ch, AFF_SLOW))
            save_num /= 2;
       */

      /* those heavy loaded folks are less than nimble eh? */
      if (load_modifier(ch) > 299)
        save_num -= 5;
      else if (load_modifier(ch) > 199)
        save_num -= 3;
      else if (load_modifier(ch) > 99)
        save_num -= 1;

      /* and let us penalize for being off balance eh? */
      if (IS_AFFECTED(ch, AFF_STUNNED))
        save_num -= 5;

      save_num += GET_POS(ch) - 3;

      /* there are a few bonuses */
      if (IS_AFFECTED(ch, AFF_FLY) || IS_AFFECTED(ch, AFF_LEVITATE))
        save_num += 3;

      break;
    case APPLY_INT:
      save_num = STAT_INDEX(GET_C_INT(ch)) + mod;

      break;
    case APPLY_WIS:
      save_num = STAT_INDEX(GET_C_WIS(ch)) + mod;

      break;
    case APPLY_STR:
      save_num = STAT_INDEX(GET_C_STR(ch)) + mod;

      break;
    case APPLY_CON:
      save_num = STAT_INDEX(GET_C_CON(ch)) + mod;

      break;
    case APPLY_DEX:
      save_num = STAT_INDEX(GET_C_DEX(ch)) + mod;

      if (IS_AFFECTED(ch, AFF_HASTE))
        save_num *= 2;

      if (IS_AFFECTED(ch, AFF_SLOW))
        save_num /= 2;

      break;
    case APPLY_POW:
      save_num = STAT_INDEX(GET_C_POW(ch)) + mod;

      break;
    case APPLY_CHA:
      save_num = STAT_INDEX(GET_C_CHA(ch)) + mod;

      break;
    case APPLY_KARMA:
      save_num = STAT_INDEX(GET_C_KARMA(ch)) + mod;

      break;
    case APPLY_LUCK:
      save_num = STAT_INDEX(GET_C_LUCK(ch)) + mod;

      break;
    default:
      return FALSE;
      break;
  }

  return (number(1, 20) < BOUNDED(1, save_num, 20));
}

/* capitalize the first alpha character in string, had to replace the old
   macro because so many strings have ansi at the start of them now.  JAB */

void CAP(char *str) {
  int pos = 0;

  if (!str || !*str)
    return;

  if (*str == '&') {
    if (*(str + 1) == '=')
      pos = 4;
    else if ((*(str + 1) == 'n') || (*(str + 1) == 'N'))
      pos = 2;
    else if ((*(str + 1) == '-') || (*(str + 1) == '+'))
      pos = 3;
  }
  if (pos < strlen(str))
    *(str + pos) = UPPER(*(str + pos));
}

char *PERS(P_char ch, P_char vict) {
  if (!CAN_SEE(vict, ch)) {
    strcpy(GS_buf1, "someone");
    return GS_buf1;
  }
  if (IS_DARK(ch->in_room)) {
    if (IS_TRUSTED(vict) || IS_AFFECTED(vict, AFF_ULTRAVISION) || IS_AFFECTED(vict, AFF_WRAITHFORM)) {
      if (IS_NPC(ch))
        return (ch->player.short_descr);
      else
        return (GET_NAME1(ch));
    }
    if (IS_AFFECTED(vict, AFF_INFRAVISION)) {
      strcpy(GS_buf1, "a red shape");
      return GS_buf1;
    } else {
      strcpy(GS_buf1, "someone");
      return GS_buf1;
    }
  }
  if (IS_NPC(ch))
    return (ch->player.short_descr);
  else
    return (GET_NAME1(ch));
}

/* this is a formatting toy, pass it a string with ansi codes, and the desired width of the output field,
   it will return the ACTUAL field width required to compensate, use it with %* in a format string so things
   line up right.  JAB */

int ansi_comp(char *str, int width) {
  int np_len = width;

  while (*str) {
    if (*str == '&') {
      if (LOWER(*(str + 1)) == 'n') {
        str += 2;
        np_len += 2;
        continue;
      }
      if ((*(str + 1) == '-') || (*(str + 1) == '+')) {
        str += 3;
        np_len += 3;
        continue;
      }
      if ((*(str + 1) == '=')) {
        str += 4;
        np_len += 4;
        continue;
      }
    }
    str++;
  }

  return np_len;
}

/* all stats are 1-100, all adjusted stats should fall in 0 to 511 range.  By the nature of the thing, all
   stats are treated identically, however, I shudder at the thought of 10 tables with 511 entries each.
   So, run the GET_C_XXX value through this function to yield a number from 0 to 51.  Index values from 0-16
   follow the bell-curve, but above 100, the divisions are linear.  The lookup tables reflect this.  You can
   of course, use this return value directly, or the stat directly.  This WAS a macro, but the expansions
   were hideous, and actually exhausted virtual memory during compile.
   So it's a function, though a simple one.  JAB */

int STAT_INDEX(int v) {
  if (v < 1)
    return 0;
  else if (v < 10)
    return 1;
  else if (v < 16)
    return 2;
  else if (v < 22)
    return 3;
  else if (v < 28)
    return 4;
  else if (v < 34)
    return 5;
  else if (v < 40)
    return 6;
  else if (v < 46)
    return 7;
  else if (v < 51)
    return 8;
  else if (v < 56)
    return 9;
  else if (v < 62)
    return 10;
  else if (v < 68)
    return 11;
  else if (v < 74)
    return 12;
  else if (v < 80)
    return 13;
  else if (v < 86)
    return 14;
  else if (v < 92)
    return 15;
  else if (v < 101)
    return 16;

  return ((v - 101) / 12 + 17);
}

bool are_together(P_char ch1, P_char ch2) { /* SKB - 20 May 1995 */
  if (!ch1 || !ch2)
    return (FALSE);

  if ((ch1 == ch2->following) || (ch2 == ch1->following)) {
    return (TRUE);
  } else if (ch1->following && (ch1->following == ch2->following)) {
    return (TRUE);
  } else if (IS_GROUPED(ch1, ch2)) {
    return (TRUE);
  } else if (IS_PC(ch1) && (ARE_GROUPED(ch1, ch2))) {
    return (TRUE);
  } else if (IS_PET(ch1)) {
    if (ARE_GROUPED(ch1->following, ch2))
      return (TRUE);
  } else if (IS_PET(ch2)) {
    if (ARE_GROUPED(ch1, ch2->following))
      return (TRUE);
  }

  return (FALSE);
}

bool has_help(P_char ch) { /* Function to enhance NPC interpretations *//* SKB - 10 Jul 1995 */
  P_char tmp = NULL;

  if (!ch)
    return (FALSE);

  if (IS_PET(ch))
    return (TRUE);

  for (tmp = world[ch->in_room].people; tmp; tmp = tmp->next_in_room) {
    if (tmp == ch)
      continue;
    if (are_together(ch, tmp))
      return (TRUE);
  }
  return (FALSE);
}

#if 0

/*****************************************************************************
 * Function get_spell_type(): Takes the spell value as defined in spells.h *
 *          and classifies it.  Returns a SPELLTYPE_     *
 *          value as defined in spells.h.         *
 *          SKB - 3 Mar 1995                         *
 *****************************************************************************/
char
get_spell_type(int spell_id) {
  /*
   * The value spell_id represents the index value of the spell as assigned
   * by SPELL_CREATE().
   */
  switch (spell_id) {

      /* Set of healing spells */

    case SPELL_CURE_LIGHT:
    case SPELL_CAUSE_LIGHT:
    case SPELL_CURE_SERIOUS:
    case SPELL_CAUSE_SERIOUS:
    case SPELL_CURE_CRITIC:
    case SPELL_CAUSE_CRITICAL:
    case SPELL_HEAL:
    case SPELL_HARM:
    case SPELL_FULL_HEAL:
    case SPELL_FULL_HARM:
    case SPELL_HEAL_UNDEAD:
    case SPELL_CURE_BLIND:
    case SPELL_SLOW_POISON:
    case SPELL_REMOVE_POISON:
    case SPELL_REVIVE:

      return (SPELLTYPE_HEALING);
      break;

      /* Set of protection spells */

    case SPELL_ARMOR:
    case SPELL_BLESS:
    case SPELL_NATURES_BLESSING:
    case SPELL_BARKSKIN:
    case SPELL_PROT_UNDEAD:
    case SPELL_PROT_FROM_UNDEAD:
    case SPELL_PROTECT_FROM_FIRE:
    case SPELL_PROTECT_FROM_COLD:
    case SPELL_PROTECT_FROM_LIGHTNING:
    case SPELL_PROTECT_FROM_ACID:
    case SPELL_PROTECT_FROM_GAS:
    case SPELL_PROTECT_FROM_EVIL:
    case SPELL_PROTECT_FROM_GOOD:
    case SPELL_STONE_SKIN:
    case SPELL_MISSILE_SHIELD:
    case SPELL_MINOR_GLOBE:
    case SPELL_GLOBE:
    case SPELL_GREATER_REALM_OF_PROTECTION:

      return (SPELLTYPE_PROTECTION);
      break;

      /* Set of summoning spells */

    case SPELL_CREATE_FOOD:
    case SPELL_CREATE_WATER:
    case SPELL_MINOR_CREATION:
    case SPELL_CREATE_SPRING:
    case SPELL_SUMMON:
    case SPELL_CONJURE_ELEMENTAL:
    case SPELL_CREATE_DRACOLICH:

      return (SPELLTYPE_SUMMONING);
      break;

      /* Set of enchantment spells */

    case SPELL_INVISIBLE:
    case SPELL_MASS_INVIS:
    case SPELL_DISPEL_INVISIBLE:
    case SPELL_DISPEL_MAGIC:
    case SPELL_BLINDNESS:
    case SPELL_PWORD_BLIND:
    case SPELL_SUNRAY:
    case SPELL_POISON:
    case SPELL_VITALITY:
    case SPELL_VIGORIZE_LIGHT:
    case SPELL_VIGORIZE_SERIOUS:
    case SPELL_VIGORIZE_CRITIC:
    case SPELL_LEVITATE:
    case SPELL_FLY:
    case SPELL_ENCHANT_WEAPON:
    case SPELL_CONTINUAL_LIGHT:
    case SPELL_CURSE:
    case SPELL_REMOVE_CURSE:
    case SPELL_STRENGTH:
    case SPELL_DEXTERITY:
    case SPELL_AGILITY:
    case SPELL_SLEEP:
    case SPELL_SLOW:
    case SPELL_INFRAVISION:
    case SPELL_FARSEE:
    case SPELL_CHARM_PERSON:
    case SPELL_MASS_CHARM:
    case SPELL_HASTE:
    case SPELL_RAY_OF_ENFEEBLEMENT:
    case SPELL_FEEBLEMIND:
    case SPELL_REJUVENATE_MINOR:
    case SPELL_REJUVENATE_MAJOR:
    case SPELL_WITHER:
    case SPELL_AGE:
    case SPELL_WATERBREATH:
    case SPELL_MINOR_PARALYSIS:
    case SPELL_MAJOR_PARALYSIS:
    case SPELL_VAMPIRIC_TOUCH:
    case SPELL_WRAITHFORM:

      return (SPELLTYPE_ENCHANTMENT);
      break;

      /* Set of divination spells */

    case SPELL_DETECT_INVISIBLE:
    case SPELL_DETECT_MAGIC:
    case SPELL_DETECT_EVIL:
    case SPELL_DETECT_GOOD:
    case SPELL_SENSE_LIFE:
    case SPELL_LOCATE_OBJECT:
    case SPELL_WIZARD_EYE:
    case SPELL_CLAIRVOYANCE:
    case SPELL_IDENTIFY:

      return (SPELLTYPE_DIVINATION);
      break;

      /* Set of teleportation spells */

    case SPELL_TELEPORT:
    case SPELL_WORD_OF_RECALL:
    case SPELL_DIMENSION_DOOR:
    case SPELL_PLANE_SHIFT:
    case SPELL_GATE:
    case SPELL_RELOCATE:
    case SPELL_MOONWELL:
    case SPELL_DIMENTIONAL_RIFT:

      return (SPELLTYPE_TELEPORTATION);
      break;

      /* Set of fire spells */

    case SPELL_BURNING_HANDS:
    case SPELL_FLAMESTRIKE:
    case SPELL_FIRESHIELD:
    case SPELL_FIREBALL:
    case SPELL_FIRESTORM:
    case SPELL_METEOR_SWARM:
    case SPELL_INCENDIARY_CLOUD:
    case SPELL_FIRE_BREATH:

      return (SPELLTYPE_FIRE);
      break;

      /* Set of cold spells */

    case SPELL_CHILL_TOUCH:
    case SPELL_COLDSHIELD:
    case SPELL_CONE_OF_COLD:
    case SPELL_ICE_STORM:
    case SPELL_FROST_BREATH:
    case SPELL_FELL_FROST:

      return (SPELLTYPE_COLD);
      break;

      /* Set of electricity spells */

    case SPELL_SHOCKING_GRASP:
    case SPELL_LIGHTNING_BOLT:
    case SPELL_CALL_LIGHTNING:
    case SPELL_CHAIN_LIGHTNING:
    case SPELL_LIGHTNING_BREATH:

      return (SPELLTYPE_ELECTRIC);
      break;

      /* Set of acid spells */

    case SPELL_ACID_BLAST:
    case SPELL_ACID_BREATH:

      return (SPELLTYPE_ACID);
      break;

    case SPELL_TOTEM_DARTS:
    case SPELL_SPIRITKNIFE:
    case SPELL_JAR_THE_SOUL:
    case SPELL_UNLEASH_FETISH:
    case SPELL_PUPPET:
    case SPELL_HEX:
    case SPELL_SOUL_TEMPEST:
    case SPELL_SPIRIT_WRACK:
    case SPELL_SPIRIT_WALK:
    case SPELL_ANCESTRAL_SHIELD:
    case SPELL_ANCESTRAL_FURY:
      return (SPELLTYPE_SPIRIT);
      break;

    case SPELL_GOODBERRY:
    case SPELL_SHILLELAGH:
    case SPELL_PROTECTION_FROM_ANIMALS:
    case SPELL_STICKS_TO_SNAKES:
    case SPELL_SUMMON_INSECTS:
    case SPELL_DUST_DEVIL:
    case SPELL_TRANSPORT_VIA_PLANTS:
    case SPELL_SUFFOCATE:
    case SPELL_INSECT_PLAGUE:
    case SPELL_CHANGESTAFF:
    case SPELL_PASS_WITHOUT_TRACE:
    case SPELL_FLAME_BLADE:
    case SPELL_ROCK_TO_MUD:
    case SPELL_MUD_TO_ROCK:
    case SPELL_FIRE_SEEDS:
    case SPELL_HAILSTORM:
    case SPELL_ENTANGLE:
    case SPELL_DESSICATE:
      return (SPELLTYPE_NATURE);
      break;

      /* The rest fall into the generic category */

    default:
      return (SPELLTYPE_GENERIC);
      break;
  }
}
#endif

P_char char_in_room(int room) {
  P_char pl;
  int num = 0;

  if (room == NOWHERE)
    return NULL;

  /* first count the number of mortals in the room */
  for (pl = world[room].people; pl; pl = pl->next_in_room)
    if (!IS_TRUSTED(pl) && IS_PC(pl))
      num++;
  if (!num)
    return (0);

  num = number(1, num); /* else pick one char at random */
  for (pl = world[room].people; pl; pl = pl->next_in_room) {
    if (!IS_TRUSTED(pl) && IS_PC(pl))
      num--;
    if (!num)
      return (pl);
  }
  return (0);
}

/* neatly (hopefully), shutdown the mud, dumping core in the process for debugging purposes. */

void dump_core(void) {
  //raise(SIGABRT);  /* Abort, because nothing else causes this signal. */
}

/* basic idea stolen gleefully from SillyMud, modified to work here of course.  JAB */

const char *MovementWords(P_char ch, int cmd, int room, int leaving) {
  if (TERRAIN_UNDERWATER(room)) {
    if ((IS_NPC(ch) && racial_traits[GET_RACE(ch)].can_swim) || IS_PC(ch))
      return (leaving ? "swims" : "swims in");
  }

  if (IS_AFFECTED(ch, AFF_FLY))
    return (leaving ? "flies" : "flies in");

  if ((IS_AFFECTED(ch, AFF_LEVITATE) && ((cmd == 4) || (cmd == 5))))
    return (leaving ? "floats" : "floats in");

  if (TERRAIN_WATER(room)) {
    if ((IS_NPC(ch) && racial_traits[GET_RACE(ch)].can_swim) || IS_AFFECTED(ch, AFF_WATERBREATH))
      return (leaving ? "swims" : "swims in");

    if (TERRAIN_BOATING(room))
      return (leaving ? "floats" : "floats in");

    return (leaving ? "thrashes" : "thrashes in");
  }

  if ((load_modifier(ch) > 199) || (GET_COND(ch, DRUNK) > 6))
    return (leaving ? "staggers" : "staggers in");

  if (!IS_TRUSTED(ch) &&
          (IS_AFFECTED(ch, AFF_BLIND) || (world[room].light == -1) || (ch->light == -1) ||
          (IS_SUNLIT(room) && IS_AFFECTED(ch, AFF_ULTRAVISION)) ||
          (IS_DARK(room) && (ch->light == 0) &&
          !IS_AFFECTED(ch, AFF_INFRAVISION) && !IS_AFFECTED(ch, AFF_ULTRAVISION))))
    return (leaving ? "stumbles" : "stumbles in");

  if (IS_AFFECTED(ch, AFF_SNEAK) || OUTDOOR_SNEAK(ch) || UD_WLD_SNEAK(ch))
    return (leaving ? "sneaks" : "sneaks in");

  if (GET_POS(ch) == POS_PRONE)
    return (leaving ? "slithers" : "slithers in");

  if (GET_POS(ch) == POS_KNEELING)
    return (leaving ? "crawls" : "crawls in");

  // Hook for elementalist spells
  if (IS_PC(ch)) {
    if (IS_AFFECTED(ch, AFF_ELEMENTAL_FIRE))
      return (leaving ? "blazes" : "blazes in");
    else if (IS_AFFECTED(ch, AFF_ELEMENTAL_WATER))
      return (leaving ? "flows" : "flows in");
    else if (IS_AFFECTED(ch, AFF_ELEMENTAL_EARTH))
      return (leaving ? "lumbers" : "lumbers in");
    else if (IS_AFFECTED(ch, AFF_ELEMENTAL_AIR))
      return (leaving ? "flies" : "flies in");
  }

  switch (GET_RACE(ch)) {
    case RACE_E_ELEMENTAL:
    case RACE_GIANT:
    case RACE_GOLEM:
    case RACE_HUMANOID_OTHER:
    case RACE_OGRE:
      return (leaving ? "lumbers" : "lumbers in");

    case RACE_W_ELEMENTAL:
      return (leaving ? "flows" : "flows in");

    case RACE_A_ELEMENTAL:
    case RACE_POSSESSED:
      return (leaving ? "flies" : "flies in");

    case RACE_INSECT:
    case RACE_PARASITE:
      return (leaving ? "skitters" : "skitters in");

    case RACE_ARACHNID:
      return (leaving ? "scuttles" : "scuttles in");

    case RACE_SLIME:
      return (leaving ? "oozes" : "oozes in");

    case RACE_ILLITHID:
    case RACE_DUERGAR:
    case RACE_PORC:
      return (leaving ? "skulks" : "skulks in");

    case RACE_CANINE:
    case RACE_CARNIVORE:
    case RACE_TROLL:
      return (leaving ? "prowls" : "prowls in");

    case RACE_BIRD:
    case RACE_FAERIE:
    case RACE_RAPTOR:
      return (leaving ? "flutters" : "flutters in");

    case RACE_GHOST:
    case RACE_SPIRIT:
      return (leaving ? "glides" : "glides in");

    case RACE_FELINE:
      return (leaving ? "stalks" : "stalks in");

    case RACE_FISH:
      return (leaving ? "flops" : "flops in"); /* swims handled above */

    case RACE_CENTAUR:
    case RACE_HERBIVORE:
    case RACE_HORSE:
      return (leaving ? "walks" : "walks in");

    case RACE_ANGEL:
    case RACE_DEMON:
    case RACE_DEVIL:
    case RACE_HIGH_UNDEAD:
    case RACE_LYCANTH:
    case RACE_VAMPIRE:
      return (leaving ? "strides" : "strides in");

    case RACE_NAGA:
    case RACE_SNAKE:
    case RACE_YUANTI:
      return (leaving ? "slithers" : "slithers in");

    case RACE_DRAGON:
    case RACE_DRAGONKIN:
      return (leaving ? "stomps" : "stomps in");

    case RACE_F_ELEMENTAL:
      return (leaving ? "blazes" : "blazes in");

    case RACE_UNDEAD:
    case RACE_LICH:
      return (leaving ? "shambles" : "shambles in");

    case RACE_MANSCORPION:
      return (leaving ? "scuttles" : "scuttles in");
  }

  return (leaving ? "leaves" : "enters");
}

void free_string(char *t_str) {
  if (!t_str)
    return;

#ifdef MEM_DEBUG
  mem_use[MEM_STRINGS] -= strlen(t_str) + 1;
#endif

  free(t_str);
}

/* removes the trailing tilde from a string. Also removes the bonus \n AFTER the tilde. */

void strip_tilde(char *buffer) {
  char *b_p;
  int count = 0, skip = 1;

  b_p = buffer + strlen(buffer) - 1;

  while ((*b_p != '~') && (b_p > buffer)) {
    b_p--;
    count++;
  }

  if (*b_p == '~') {
    for (; count >= 0; count--) {
      if ((*b_p == '~') && (*(b_p + 1) == '\n') && (skip == 1)) {
        skip = 2;
        count--;
      }
      *b_p = *(b_p + skip);
      b_p++;
    }
  }
}

/* reads lines from 'fp' of max length 'buf_sz' into 'buffer',
 * removes comments (denoted by ';;')
 * removes leading and trailing spaces and tabs
 * ignores blank lines
 * changes the strings "\n", "\r" and "\t" into the characters '\n', '\r' and '\t'
 * trailing '\' indicates that line is continued.
 *
 * reads until EOF or a string of greater than zero length is found.
 * Greater than 0 length, AFTER comments and leading/trailing whitespace is removed.
 *
 * returns EOF (-1) or the number of characters in 'buffer'.
 *
 * the 'buffer' arg needs to be allocated by the calling routine, it's assumed to be large enough to hold 'buf_sz'
 * characters, if not, it's your ass, not mine.
 *
 * This routine is called during the initial bootup, to read the various world.* files.
 * JAB */

int fget_line(FILE *fp, char *buffer, int buf_sz) {
  char tbuf[MAX_STRING_LENGTH * 2 + 1], *tb_p, *tb_ep;
  int read_more = TRUE, buf_idx = -1;


  if (!fp || !buffer || (buf_sz < 0) || (buf_sz > (2 * MAX_STRING_LENGTH))) {
    logit(LOG_DEBUG, "Bogus args to fget_line()");
    return EOF;
  }

  do {
    if (fgets(tbuf, buf_sz, fp) == NULL)
      return EOF;

    /* from ';;' to end of line is a comment, discard it, only have to find the first occurrence, of course. */
    if ((tb_p = strstr(tbuf, ";;")) != NULL)
      *tb_p = '\0';

    /* strip leading spaces and tabs */
    for (tb_p = tbuf; *tb_p && ((*tb_p == ' ') || (*tb_p == '\t')) && ((tb_p - tbuf) < buf_sz); tb_p++);

    /* strip trailing spaces and tabs */
    tb_ep = &tbuf[strlen(tbuf)];
    if ((tb_p - tbuf) > 0)
      for (; (tb_ep >= tb_p) && ((*tb_p == ' ') || (*tb_p == '\t')); tb_ep--)
        *tb_ep = '\0';

    if (tb_p != tb_ep) /* then we have some text, copy it and be done */
      read_more = FALSE;

    for (; tb_p < tb_ep; tb_p++) {
      if (*tb_p == '\\') {
        switch (*(tb_p + 1)) {
          case '\0': /* continuation marker */
            read_more = TRUE;
            break;
          case 'n':
            buffer[++buf_idx] = '\n';
            tb_p++;
            break;
          case 'r':
            buffer[++buf_idx] = '\r';
            tb_p++;
            break;
          case 't':
            buffer[++buf_idx] = '\t';
            tb_p++;
            break;
          default:
            buffer[++buf_idx] = '\\';
        }
      } else
        buffer[++buf_idx] = *tb_p;
    }
    if (buf_idx > -1)
      buffer[buf_idx] = 0;
    tbuf[0] = 0;

  } while (read_more);

  return buf_idx;
}

void update_last_login(P_desc d) {
  P_char t_ch = NULL;

  if (!d || !d->character)
    return;

  if (IS_NPC(d->character)) {
    /* wanker switched before one of the lookups came back, barely possible if ident is hung up */
    if (!d->original)
      dump_core();
    t_ch = d->original;
  } else
    t_ch = d->character;

  if (!t_ch || IS_NPC(t_ch))
    dump_core(); /* shouldn't be possible */

  if (GET_LAST_LOGIN(t_ch))
    free_string(t_ch->only.pc->last_login);

  t_ch->only.pc->last_login = str_dup(full_address(d, 0, 0));
  do_save_silent(t_ch, 1);
}

char *full_username(P_desc d, int max_len, int ansi_flag) {
  static char output[MAX_STRING_LENGTH];

  output[0] = 0;

  if (!d)
    return output;

  if (d->lookup_status & 1)
    sprintf(output, "%s%s%s", ansi_flag ? "&+Y" : "", " ??? ", ansi_flag ? "&n" : "");
  else
    sprintf(output, "%s%s%s", ansi_flag ? "&+g" : "", d->username ? d->username : "", ansi_flag ? "&n" : "");

  if ((max_len > 0) && (max_len < MAX_INPUT_LENGTH))
    output[ansi_comp(output, max_len)] = 0;
  else
    output[MAX_INPUT_LENGTH] = 0;

  return output;
}

char *full_hostname(P_desc d, int max_len, int ansi_flag) {
  static char output[MAX_STRING_LENGTH];

  output[0] = 0;

  if (!d)
    return output;

  if (d->lookup_status & 2)
    sprintf(output, "%s%s%s", ansi_flag ? "&+Y" : "", d->hostIP, ansi_flag ? "&n" : "");
  else
    sprintf(output, "%s%s%s", ansi_flag ? "&+c" : "", d->hostname, ansi_flag ? "&n" : "");

  if ((max_len > 0) && (max_len < MAX_INPUT_LENGTH))
    output[ansi_comp(output, max_len)] = 0;
  else
    output[MAX_INPUT_LENGTH] = 0;

  return output;
}

/* given a descriptor, returns a string (suitable for printing or str_dup), in the format username@hostname, with
   a total length of 'max_len' or MAX_INPUT_LENGTH, and ansified if flag is set.  In all but one case, this is
   called inside a sprintf(), so it works fine.  It's mainly a code-space-saver, so we don't have duplicates
   of most of its text in about 2 dozen places.  JAB */

char *full_address(P_desc d, int max_len, int ansi_flag) {
  static char output[MAX_STRING_LENGTH];

  output[0] = 0;

  if (!d)
    return output;

  sprintf(output, "%s%s%s%s",
          (ansi_flag) ? "&n" : "",
          full_username(d, max_len, ansi_flag),
          ((!d->username || !*d->username) && !IS_SET(d->lookup_status, 1)) ? "" : (ansi_flag ? "&+c@&n" : "@"),
          full_hostname(d, max_len, ansi_flag));

  if ((max_len > 0) && (max_len < MAX_INPUT_LENGTH))
    output[ansi_comp(output, max_len)] = 0;
  else
    output[MAX_INPUT_LENGTH] = 0;

  return output;
}

/* walk the chain of followers upwards for both chars, and compare the results, returns 1 if they are following
   the same ultimate leader, 0 otherwise.  JAB */

int same_group(P_char ch1, P_char ch2) {
  int counter = 0;

  while (ch1->following) {
    ch1 = ch1->following;
    if ((counter++) > 100) /* detect and break out of circular follows.. */
      /* doesnt fix anything, just prevents a checkpointing dump */
      return 0;
  }

  counter = 0;
  while (ch2->following) {
    ch2 = ch2->following;
    if ((counter++) > 100)
      return 0;
  }

  return ((ch1 == ch2) ? 1 : 0);
}

bool has_mount(P_char ch) {
  int movecost = 1;
  struct follow_type *fol;

  if (!ch->followers)
    return (FALSE);

  fol = ch->followers;

  while (fol) {
    if (IS_NPC(fol->follower) && (IS_CSET(fol->follower->only.npc->npcact, ACT_MOUNT)) &&
            (fol->follower->in_room == ch->in_room)) {

      if (ch->specials.cart) {
        movecost += get_cart_weight(ch->specials.cart) / 50;
        if (GET_MOVE(fol->follower) > movecost)
          return (TRUE);
        else
          return (FALSE);
      } else
        return TRUE;
    }

    fol = fol->next;
  }

  return (FALSE);
}

int get_cart_weight(P_obj cart) {
  int weight = 0;
  P_obj obj;

  obj = cart->contains;

  while (obj) {
    weight += obj->weight;
    obj = obj->next_content;
  }

  return weight;
}

int getNumberOfPets(P_char ch) {
  int numPets = 0;
  struct follow_type *f;


  for (f = ch->followers; f; f = f->next) {
    if (is_char_pet(ch, f->follower))
      numPets++;
  }

  return numPets;
}

bool is_char_pet(P_char ch, P_char pet) {
  if (pet->following == ch && IS_AFFECTED(pet, AFF_CHARM) && IS_NPC(pet))
    return TRUE;
  else
    return FALSE;
}

bool hasAutoSaveEvent(P_char ch) {
  P_event e1;

  LOOP_EVENTS(e1, ch->events) {
    if (e1->type == EVENT_AUTO_SAVE)
      return TRUE;
  }

  return FALSE;
}

bool removeAutoSaveEvent(P_char ch) {
  P_event e1 = NULL, e2 = NULL;

  if (!ch)
    return FALSE;

  LOOP_EVENTS(e1, ch->events)
  if ((e1->type == EVENT_AUTO_SAVE) && ((P_char) e1->actor.a_ch == ch)) {
    e2 = current_event;
    current_event = e1;
    RemoveEvent();
    current_event = e2;
    break;
  }

  return TRUE;
}


/* For use in circumstances requiring the character to be parted from all    */

/* of their commodities, whether in inventory or cart.  Unlinks cart as well */

void drop_commodities(P_char ch) {

  P_obj c_obj, next_obj;
  bool dropCom = FALSE;

  if (!ch)
    return;

  /* Loop through inv and toss out commodities */
  for (c_obj = ch->carrying; c_obj; c_obj = next_obj) {
    next_obj = c_obj->next_content;
    if (GET_ITEM_TYPE(c_obj) == ITEM_COMMODITY) {
      obj_from_char(c_obj);
      obj_to_room(c_obj, ch->in_room);
      dropCom = TRUE;
    }
  }

  /* Send the msg if we found any commodities */
  if (dropCom) {
    act("Some commodities fall to the ground as $n fades away!", TRUE, ch, 0, 0, TO_ROOM);
    act("Some commodities fall to the ground as you fade away!", TRUE, ch, 0, 0, TO_CHAR);
  }

  /* Unlink carts.. */
  if (ch->specials.cart) {
    ch->specials.cart->owner = NULL;
    ch->specials.cart = NULL;
    writeCart(ch);
  }
}

/* recursively counts money held in containers, returns an unsigned long sum */

static struct moneyStruct recMoneyHolder;

unsigned long getMoneyContainedInObj(P_obj obj) {
  unsigned long total = 0;
  P_obj next_obj, o_obj;

  for (o_obj = obj; o_obj; o_obj = next_obj) {
    next_obj = o_obj->next_content;
    if (o_obj->type == ITEM_CONTAINER)
      total += getMoneyContainedInObj(o_obj->contains);
    if (o_obj->type == ITEM_MONEY) {
      total += o_obj->value[3] * 1000 + o_obj->value[2] * 100 + o_obj->value[1] * 10 + o_obj->value[0];
      recMoneyHolder.platinum += o_obj->value[3];
      recMoneyHolder.gold += o_obj->value[2];
      recMoneyHolder.silver += o_obj->value[1];
      recMoneyHolder.copper += o_obj->value[0];
    }
  }

  return total;
}

/* count money contained in worn and inv objects.. goes thru containers */

unsigned long getCarriedMoney(P_char ch) {
  int i;
  unsigned long total = 0;

  if (!ch)
    dump_core();

  bzero((void*) &recMoneyHolder, sizeof (struct moneyStruct));

  total += getMoneyContainedInObj(ch->carrying);

  for (i = 0; i < MAX_WEAR; i++)
    if (ch->equipment[i])
      total += getMoneyContainedInObj(ch->equipment[i]);

  return total;
}

/* wrapper for getCarriedMoney(), returns a pointer to a static struct
 * NOTE: _NOT_ reentrant, ie dont go and change what this fn returns */

struct moneyStruct *getCarriedMoneyStruct(P_char ch) {
  unsigned long total;

  total = getCarriedMoney(ch);

  recMoneyHolder.total = total;

  return &recMoneyHolder;
}

/*
 * free a string created with str_dup()
 */
void str_free(char *source) {
  if (!source)
    return;

#ifdef MEM_DEBUG
  mem_use[MEM_STRINGS] -= (strlen(source) + 1);
#endif

  free(source);
}

int same_race_side(P_char ch, P_char victim) {

  if (IS_PC(ch) && IS_PC(victim) &&
          (IS_CSET(ch->only.pc->pcact, PLR_GROUPALLOW) ||
          IS_CSET(victim->only.pc->pcact, PLR_GROUPALLOW)))
    return TRUE;

  if (!IS_TRUSTED(ch) && IS_PC(ch) && IS_PC(victim)) {
    if ((RACE_GOOD(ch) && RACE_EVIL(victim)) || (RACE_GOOD(victim) && RACE_EVIL(ch)))
      return FALSE;
  }

  return TRUE;

}

