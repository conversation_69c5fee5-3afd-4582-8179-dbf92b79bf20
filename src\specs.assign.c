/* ***************************************************************************
 *  File: spec.assign.c                                        Part of Outcast *
 *  Usage: assign function pointers for special procs.                       *
 *  Copyright  1990, 1991 - see 'license.doc' for complete information.      *
 *  Copyright  1994, 1995 - Outcast Systems Ltd.                             *
 *************************************************************************** */

#include <stdio.h>
#include <string.h>

#include "db.h"
#include "events.h"
#include "path.h"
#include "prototypes.h"
#include "structs.h"
#include "specs.include.h"
#include "specs.prototypes.h"
#include "utils.h"
#ifdef KINGDOM
#include "kingdom.h"
#endif
/* external variables */

extern int top_of_world;
extern P_index mob_index;
extern P_index obj_index;
extern P_event current_event;
extern P_obj object_list;
extern P_room world;
extern P_group group_list;

/* PC Procs add and remove functions  -- Alth  April 29 '99 */

void AddProcPC(P_char ch, int (*add_func) (P_char, P_char, int, char *), const char *name) {
  struct func_attachment *fn, *t_fn;
  struct func_registration_data *frd;

  if (!add_func) {
    logit(LOG_DEBUG, "Bogus func (%s) in AddProcPC()", name);
    return;
  }

  if (!ch) {
    logit(LOG_EXIT, "NULL P_char passed to AddProcPC - function [%s]", name);
    dump_core();
  }

  if ((frd = findProcFunction((char *) name)) == NULL)
    return;

  CREATE(fn, struct func_attachment, 1);
  fn->type = FUNC_PC;
  fn->name = strdup(name);
  fn->func.ch = add_func;
  if (!GET_SPEC_FN(ch)) {
    //GET_SPEC_FN(ch) = fn;
    if (IS_PC(ch)) {
      ch->only.pc->func = fn;
    } else {
      mob_index[ch->nr].func = fn;
    }
  } else {
    t_fn = GET_SPEC_FN(ch);
    while (t_fn->next)
      t_fn = t_fn->next;
    t_fn->next = fn;
  }

  /* Call the proc to get the proc trigger flags */
  fn->proc_flag = (*fn->func.ch) (ch, 0, PROC_INITIALIZE, 0);
  //GET_SPEC_FLAG(ch) |= fn->proc_flag;
  if (IS_PC(ch)) {
    ch->only.pc->spec_flag |= fn->proc_flag;
  } else {
    mob_index[ch->nr].spec_flag |= fn->proc_flag;
  }

  if (IS_SET(fn->proc_flag, IDX_PERIODIC))
    AddEvent(EVENT_CHAR_PERIODIC, frd->repeatTime, TRUE, ch, frd->ptr);
}

/* call it with either function pointer or function name (same as the one given via
   AddProcPC(). NOTE: if a char has two or more instances of the same IDX_PERIODIC
   proc attached to it, the code yanks the first EVENT_CHAR_PERIODIC event
   it finds in ch->events..                                -- Altherog  Apr 30 1999 */

void RemoveProcPC(P_char ch, int (*remove_func) (P_char, P_char, int, char *), const char *name) {
  P_event e1, e2;
  struct func_attachment *fn, *t_fn;

  if (!remove_func && !name) {
    logit(LOG_EXIT, "RemoveProcPC called with null remove_func ptr AND name");
    dump_core();
  }

  if (!ch) {
    logit(LOG_EXIT, "NULL P_char passed to RemoveProcPC - function [%s]", name);
    dump_core();
  }

  /* Find the approperiate proc struct */
  fn = NULL;
  t_fn = GET_SPEC_FN(ch);
  while (t_fn) {
    if ((remove_func && (t_fn->func.ch == remove_func)) || (name && !strcmp(t_fn->name, name))) {
      fn = t_fn;
      break;
    }
    t_fn = t_fn->next;
  }

  if (!fn)
    return;

  /* Yank it */
  if (GET_SPEC_FN(ch) == fn) {
    //GET_SPEC_FN(ch) = fn->next;
    if (IS_PC(ch)) {
      ch->only.pc->func = fn->next;
    } else {
      mob_index[ch->nr].func = fn->next;
    }    
  } else {
    for (t_fn = GET_SPEC_FN(ch); (t_fn) && (t_fn->next != fn); t_fn = t_fn->next);
    if (t_fn) {
      t_fn->next = fn->next;
    } else {
      logit(LOG_EXIT, "RemoveProcPC(), Major FUBAR, cant find a previously located struct");
      dump_core();
    }
  }

  /* Yank the event if proc is periodic */
  if (IS_SET(fn->proc_flag, IDX_PERIODIC)) {
    LOOP_EVENTS(e1, ch->events)
    if (e1->type == EVENT_CHAR_PERIODIC &&
            ((void *) e1->target.t_func_proc == fn->func.ch)) {
      if (e1 && (current_event != e1)) {
        e2 = current_event;
        current_event = e1;
        RemoveEvent();
        current_event = e2;
      }
    }
  }

  /* Now, need to remove the proc flags set by this function, have to do that without
   nuking overlapping flags set by other procs */
  t_fn = GET_SPEC_FN(ch);
  while (t_fn) {
    REMOVE_BIT(fn->proc_flag, t_fn->proc_flag);
    t_fn = t_fn->next;
  }
  
  /* remove whats left (ie bits present only in the proc we are trying to remove */
  if (IS_PC(ch)) {    
    REMOVE_BIT(ch->only.pc->spec_flag, fn->proc_flag);
  } else {
    REMOVE_BIT(mob_index[ch->nr].spec_flag, fn->proc_flag);    
  }

  ReleaseProc(fn);

  return;
}

void AddProcMob(int nr, int (*add_func) (P_char, P_char, int, char *), const char *name) {
  int R_num;
  struct func_attachment *fn, *t_fn;

  if (!add_func) {
    logit(LOG_DEBUG, "Bogus func (%s) in AddProcMob()", name);
    return;
  }

  if ((R_num = real_mobile(nr)) < 0) {
    logit(LOG_DEBUG, "Bogus mob number (%d) in AddProcMob() function: %s", nr, name);
    return;
  }

  CREATE(fn, struct func_attachment, 1);
  fn->type = FUNC_MOB;
  fn->name = name;
  fn->func.ch = add_func;
  if (!mob_index[R_num].func)
    mob_index[R_num].func = fn;
  else {
    t_fn = mob_index[R_num].func;
    while (t_fn->next)
      t_fn = t_fn->next;
    t_fn->next = fn;
  }
}

void AddProcObj(int nr, int (*add_func) (P_obj, P_char, int, char *), const char *name) {
  int R_num;
  struct func_attachment *fn, *t_fn;

  if (!add_func) {
    logit(LOG_DEBUG, "Bogus func (%s) in AddProcObj()", name);
    return;
  }

  if ((R_num = real_object(nr)) < 0) {
    logit(LOG_DEBUG, "Bogus obj number (%d) in AddProcObj() function: %s", nr, name);
    return;
  }

  CREATE(fn, struct func_attachment, 1);
  fn->type = FUNC_OBJ;
  fn->name = name;
  fn->func.obj = add_func;
  if (!obj_index[R_num].func)
    obj_index[R_num].func = fn;
  else {
    t_fn = obj_index[R_num].func;
    while (t_fn->next)
      t_fn = t_fn->next;
    t_fn->next = fn;
  }
}

void AddProcRoom(int nr, int (*add_func) (int, P_char, int, char *), const char *name) {
  int R_num;

  if (!add_func) {
    logit(LOG_DEBUG, "Bogus func (%s) in AddProcRoom()", name);
    return;
  }

  if ((R_num = real_room(nr)) < 0) {
    logit(LOG_DEBUG, "Bogus room number (%d) in AddProcRoom() function: %s", nr, name);
    return;
  }

  if (world[R_num].funct) {
    logit(LOG_EXIT, "Attempt to add multiple functions to room #%d (%s)", nr, name);
    dump_core();
  }

  world[R_num].funct = add_func;
}

#ifdef NEW_GROUP_PROC

void AddProcGrp(int nr, int (*add_func) (P_group, P_char, int, char *), const char *name) {
  int R_num;
  struct func_attachment *fn, *t_fn;

  if (!add_func) {
    logit(LOG_DEBUG, "Bogus func (%s) in AddProcGrp()", name);
    return;
  }

  if ((R_num = real_group(nr)) < 0) {
    logit(LOG_DEBUG, "Bogus grp number (%d) in AddProcGrp() function: %s", nr, name);
    return;
  }

  CREATE(fn, struct func_attachment, 1);
  fn->type = FUNC_GRP;
  fn->name = name;
  fn->func.grp = add_func;
  if (!grp_index[R_num].func)
    grp_index[R_num].func = fn;
  else {
    t_fn = grp_index[R_num].func;
    while (t_fn->next)
      t_fn = t_fn->next;
    t_fn->next = fn;
  }
}
#endif


/* ********************************************************************
 *  Assignments                                                        *
 ******************************************************************** */

/* assign special procedures to mobiles */

void assign_mobiles(void) {
  /* specs.undermountain.c */
  assignUndermountainMobiles();

  /* specs.icecrag.c */
  assignIcecrag2Mobiles();

  /* specs.planar.c */
  assignExtraPlanarMobiles();

  /* specs.greycloak.c   -- diirinka */
  assignGreycloakMobs();

  /* specs.griffons.c   -- Diirinka */
  assignGriffonsNestMobiles();

  /* specs.scornubel.c  -- diirinka */
  assignScornubelMobs();

  /* specs.spiderhaunt.c  -- diirinka */
  assignSpiderhauntMobiles();

  /* specs.hive.c -- CRM */
  assignHiveMobiles();

  /* specs.hyssk.c -- CRM */
  assignHysskMobiles();

  /* specs.calimshan.c  -- diirinka */
  assignCalimshanMobiles();

  /* specs.duskroad.c -- diirinka */
  assignDuskRoadMobiles();

  /* specs.balders.c  -- JAB */
  assignBaldersGateMobiles();

  /* specs.zhentilkeep.c */
  AssignZKMobiles();

  /* specs.dobluth.c  -- CRM */
  assignDobluthKyorMobiles();

  /* specs.tarsellian.c  -- CRM */
  assignTarsellianMobiles();

  /* spe4cs.trahern.c   -- DA */
  assignTrahernMobiles();

  /* specs.avernus.c */
  assignAvernusMobiles();

  AddProcMob(9, lichConverter, "lichConverter");

#ifdef CONFIG_JAIL

  /* Northern Waterdeep */
  AddProcMob(3034, witness, "witness");
  AddProcMob(3060, witness, "witness");
  AddProcMob(3065, waterdeep_witness, "waterdeep_witness");
  AddProcMob(3067, witness, "witness");
  AddProcMob(3080, jailkeeper, "jailkeeper");
  AddProcMob(3081, apprehender, "apprehender");
  AddProcMob(3082, waterdeep_guard_three, "waterdeep_guard_three");
  AddProcMob(3082, cityguard, "cityguard");
  AddProcMob(3083, apprehender, "apprehender");

  /* Central Waterdeep */
  AddProcMob(3209, witness, "witness");
  AddProcMob(3210, witness, "witness");
  AddProcMob(3213, cell_drunk, "cell_drunk");
  AddProcMob(3228, witness, "witness");
  AddProcMob(3230, witness, "witness");
  AddProcMob(3233, witness, "witness");
  AddProcMob(3235, witness, "witness");
  AddProcMob(3239, witness, "witness");
  AddProcMob(3240, witness, "witness");

  /* Leuthilspar */
  AddProcMob(8025, witness, "witness");
  AddProcMob(8026, witness, "witness");
  AddProcMob(8027, witness, "witness");
  AddProcMob(8028, jailkeeper, "jailkeeper");
  AddProcMob(8034, apprehender, "apprehender");
  AddProcMob(8047, apprehender, "apprehender");

  /* Luiren */
  AddProcMob(16010, witness, "witness");
  AddProcMob(16011, apprehender, "apprehender");
  AddProcMob(16066, jailkeeper, "jailkeeper");
  AddProcMob(16067, apprehender, "apprehender");

#else

#if 0
  /* Quests */
  AddProcMob(1103, Evil_Invader, "Evil_Invader");
  AddProcMob(1104, Evil_Invader, "Evil_Invader");
#endif

  /* Northern Waterdeep */
  AddProcMob(3080, cityguard, "cityguard");
  AddProcMob(3081, cityguard, "cityguard");
  AddProcMob(3082, waterdeep_guard_three, "waterdeep_guard_three");
  AddProcMob(3082, cityguard, "cityguard");
  AddProcMob(3083, cityguard, "cityguard");

#if 0
  AddProcMob(3080, Invader_Repulser, "Invader_Repulser");
  AddProcMob(3081, Invader_Repulser, "Invader_Repulser");
  AddProcMob(3082, Invader_Repulser, "Invader_Repulser"a);
  AddProcMob(3083, Invader_Repulser, "Invader_Repulser");
#endif

  /* Leuthilspar */
  AddProcMob(8028, cityguard, "cityguard");
  AddProcMob(8034, cityguard, "cityguard");
  AddProcMob(8047, cityguard, "cityguard");

  /* Luiren */
  AddProcMob(16011, cityguard, "cityguard");
  AddProcMob(16066, cityguard, "cityguard");
  AddProcMob(16067, cityguard, "cityguard");

#endif



  /* Lower God Rooms */
  AddProcMob(200, shadow_demon_of_torm, "summoned shadow");
  /*  AddProcMob(201, tentacle, "tentacle"); */
  /*  AddProcMob(3, hunt_cat, "hunt_cat"); */
  /*  AddProcMob(4, citizenship, "citizenship"); */

  /* Summoned conjurer mobs --CRM */
  AddProcMob(300, conj_familiar_die, "conj_familiar_die");
  AddProcMob(301, conj_familiar_die, "conj_familiar_die");
  AddProcMob(302, conj_familiar_die, "conj_familiar_die");
  AddProcMob(303, conj_familiar_die, "conj_familiar_die");
  AddProcMob(304, conj_familiar_die, "conj_familiar_die");
  AddProcMob(305, conj_familiar_die, "conj_familiar_die");
  AddProcMob(306, conj_familiar_die, "conj_familiar_die");
  AddProcMob(307, conj_familiar_die, "conj_familiar_die");
  AddProcMob(308, conj_familiar_die, "conj_familiar_die");
  AddProcMob(309, conj_familiar_die, "conj_familiar_die");
  AddProcMob(310, conj_familiar_die, "conj_familiar_die");
  AddProcMob(311, conj_familiar_die, "conj_familiar_die");
  AddProcMob(312, conj_familiar_die, "conj_familiar_die");
  AddProcMob(313, conj_familiar_die, "conj_familiar_die");
  AddProcMob(314, conj_familiar_die, "conj_familiar_die");
  AddProcMob(315, conj_familiar_die, "conj_familiar_die");
  AddProcMob(316, conj_familiar_die, "conj_familiar_die");
  AddProcMob(317, conj_familiar_die, "conj_familiar_die");
  AddProcMob(318, conj_familiar_die, "conj_familiar_die");
  AddProcMob(319, conj_familiar_die, "conj_familiar_die");
  AddProcMob(320, conj_familiar_die, "conj_familiar_die");
  AddProcMob(321, conj_familiar_die, "conj_familiar_die");
  AddProcMob(322, conj_familiar_die, "conj_familiar_die");
  AddProcMob(323, conj_familiar_die, "conj_familiar_die");
  AddProcMob(324, conj_familiar_die, "conj_familiar_die");

  AddProcMob(325, conj_lycan_tiger, "conj_lycan_tiger");
  AddProcMob(326, conj_lycan_fox, "conj_lycan_fox");
  AddProcMob(327, conj_lycan_tiger, "conj_lycan_bear");
  AddProcMob(328, conj_lycan_tiger, "conj_lycan_wolf");

  AddProcMob(330, conj_mount_die, "conj_mount_die");
  AddProcMob(331, conj_mount_die, "conj_mount_die");
  AddProcMob(332, conj_mount_die, "conj_mount_die");
  AddProcMob(333, conj_mount_die, "conj_mount_die");
  AddProcMob(334, conj_mount_die, "conj_mount_die");
  AddProcMob(335, conj_mount_die, "conj_mount_die");
  AddProcMob(336, conj_mount_die, "conj_mount_die");
  AddProcMob(337, conj_mount_die, "conj_mount_die");
  AddProcMob(338, conj_mount_die, "conj_mount_die");
  AddProcMob(339, conj_mount_die, "conj_mount_die");

  AddProcMob(350, conj_monster_die, "conj_monster_die");
  AddProcMob(351, conj_monster_die, "conj_monster_die");
  AddProcMob(352, conj_monster_die, "conj_monster_die");
  AddProcMob(353, conj_monster_die, "conj_monster_die");
  AddProcMob(354, conj_monster_die, "conj_monster_die");
  AddProcMob(355, conj_monster_die, "conj_monster_die");
  AddProcMob(356, conj_monster_die, "conj_monster_die");
  AddProcMob(357, conj_monster_die, "conj_monster_die");
  AddProcMob(358, conj_monster_die, "conj_monster_die");
  AddProcMob(359, conj_monster_die, "conj_monster_die");

  AddProcMob(905, dark_shade_die, "dark_shade_die");

  /* Summoned shaman totem spirit procs */
  AddProcMob(716, spirit_wolf_die, "spirit_wolf_die");
  AddProcMob(717, spirit_bear_die, "spirit_bear_die");
  AddProcMob(718, spirit_boar_die, "spirit_boar_die");
  AddProcMob(719, spirit_elk_die, "spirit_elk_die");
  AddProcMob(720, spirit_eagle_die, "spirit_eagle_die");
  AddProcMob(721, spirit_crow_die, "spirit_crow_die");
  AddProcMob(722, spirit_lion_die, "spirit_lion_die");
  AddProcMob(723, spirit_tiger_die, "spirit_tiger_die");
  AddProcMob(724, spirit_stallion_die, "spirit_stallion_die");
  AddProcMob(725, spirit_snake_die, "spirit_snake_die");
  AddProcMob(732, spirit_worg_die, "spirit_worg_die");
  AddProcMob(733, spirit_vulture_die, "spirit_vulture_die");
  AddProcMob(734, spirit_crocodile_die, "spirit_crocodile_die");
  AddProcMob(735, spirit_serpent_die, "spirit_serpent_die");
  AddProcMob(736, spirit_scorpion_die, "spirit_scorpion_die");
  AddProcMob(737, spirit_hyena_die, "spirit_hyena_die");
  AddProcMob(738, spirit_jackal_die, "spirit_jackal_die");
  AddProcMob(739, spirit_spider_die, "spirit_spider_die");
  AddProcMob(740, spirit_viper_die, "spirit_viper_die");
  AddProcMob(741, spirit_bat_die, "spirit_bat_die");
  AddProcMob(742, spirit_raven_die, "spirit_raven_die");

  /* Main God Rooms */
  AddProcMob(1200, petRent, "petRent");
#ifdef NEWJUSTICE
  AddProcMob(1202, justice_clerk, "justice_clerk");
#endif
  AddProcMob(1205, sales_spec, "salesman");
  AddProcMob(1227, receptionist, "receptionist");
  AddProcMob(1232, warhorse, "warhorse");
  /* AddProcMob(1010, ammo_repair_shopkeeper, "Ammo Repair Shopkeeper"); by Garg*/

  //  AddProcMob(1210, repair_shopkeeper, "Repair Shopkeeper");
  AddProcMob(1263, auctioneer, "auctioneer");

  /* Lycanthropy Mobs */
  AddProcMob(525, werewolf_lycan, "werewolf_lycan");
  AddProcMob(97054, lycan_wolf_infect, "lycan_wolf_infect");

  /* Kobold Settlement */
  AddProcMob(1407, chicken, "chicken");
  AddProcMob(1433, stone_crumble, "stone_crumble");
  AddProcMob(1436, tako_demon, "tako_demon");
  AddProcMob(1437, kobold_priest, "kobold_priest");
  AddProcMob(1438, stone_golem, "stone_golem");

  /* Troll Hills */
  AddProcMob(1919, bridge_troll, "bridge_troll");

  /* ********************************************* */
  /* Begin Waterdeep special procedure assignments */
  /* ********************************************* */

  /* jungle */
  AddProcMob(2608, breath_attack_fire, "fire attack");
  AddProcMob(2611, breath_attack_fire, "fire attack");

  /* Southern Waterdeep */
  AddProcMob(2811, rogue_one, "rogue_one");
  AddProcMob(2812, mercenary_two, "mercenary_two");
  AddProcMob(2813, youth_one, "youth_one");
  /* AddProcMob(2814, thief_one, "thief_one"); */
  AddProcMob(2815, homeless_two, "homeless_two");
  AddProcMob(2816, homeless_one, "homeless_one");
  AddProcMob(2818, prostitute_one, "prostitute_one");
  /*AddProcMob(2820, dog_two, "dog_two");*/
  AddProcMob(2823, guildmaster_twelve, "guildmaster_twelve");
  AddProcMob(2824, guild_guard_thirteen, "guild_guard_thirteen");
  AddProcMob(2825, assassin_one, "assassin_one");
  AddProcMob(2827, mercenary_three, "mercenary_three");
  AddProcMob(2829, youth_two, "youth_two");
  AddProcMob(2830, brigand_one, "brigand_one");
  /* AddProcMob(2831, thief_one, "thief_one"); */
  AddProcMob(2832, commoner_four, "commoner_four");
  AddProcMob(2833, commoner_five, "commoner_five");
  AddProcMob(2834, commoner_six, "commoner_six");
  AddProcMob(2835, mercenary_three, "mercenary_three");
  AddProcMob(2836, drunk_two, "drunk_two");

  /* Summoned elemental specs. */
  AddProcMob(3050, fire_mental_die, "fire_mental_die");
  AddProcMob(3051, earth_mental_die, "earth_mental_die");
  AddProcMob(3052, air_mental_die, "air_mental_die");
  AddProcMob(3053, water_mental_die, "water_mental_die");
  AddProcMob(1250, fire_mental_die, "fire_mental_die");
  AddProcMob(1251, earth_mental_die, "earth_mental_die");
  AddProcMob(1252, air_mental_die, "air_mental_die");
  AddProcMob(1253, water_mental_die, "water_mental_die");
  AddProcMob(907, fire_mephit_die, "fire_mephit_die");
  AddProcMob(906, water_mephit_die, "water_mephit_die");
  AddProcMob(909, air_mephit_die, "air_mephit_die");
  AddProcMob(908, earth_mephit_die, "earth_mephit_die");

  /* New Conj pet specs - CRM */
  AddProcMob(499, unseen_servant_die, "unseen_servant_die");
  AddProcMob(202, tentacle_die, "tentacle_die");

  /* Summoned trean specs. */
  AddProcMob(902, treant_die, "treant_die");

  /* Phantom steed specs */
  AddProcMob(903, phantom_steed_die, "phantom_steed_die");

  AddProcMob(1210, ammo_repair_shopkeeper, "ammo_repair_shopkeeper");

  /* Northern Waterdeep */
  AddProcMob(3085, ammo_repair_shopkeeper, "ammo_repair_shopkeeper");
  AddProcMob(3005, receptionist, "receptionist");
  AddProcMob(3006, drunk_two, "drunk_two");
  AddProcMob(3007, homeless_one, "homeless_one");
  AddProcMob(3008, crier_one, "crier_one");
  AddProcMob(3009, merchant_one, "merchant_one");
  AddProcMob(3010, farmer_one, "farmer_one");
  AddProcMob(3011, baker_one, "baker_one");
  AddProcMob(3012, baker_two, "baker_two");
  AddProcMob(3014, mage_one, "mage_one");
  AddProcMob(3018, warrior_one, "warrior_one");
  AddProcMob(3020, guildmaster_eight, "guildmaster_eight");
  AddProcMob(3021, guildmaster_nine, "guildmaster_nine");
  AddProcMob(3022, guildmaster_eleven, "guildmaster_eleven");
  AddProcMob(3023, guildmaster_ten, "guildmaster_ten");
  AddProcMob(3024, guild_guard_nine, "guild_guard_nine");
  AddProcMob(3025, guild_guard_ten, "guild_guard_ten");
  AddProcMob(3026, guild_guard_twelve, "guild_guard_twelve");
  AddProcMob(3027, guild_guard_eleven, "guild_guard_eleven");
  AddProcMob(3030, cleric_one, "cleric_one");
  AddProcMob(3035, waterdeep_guard_two, "waterdeep_guard_two");
  //AddProcMob(3035, Invader_Repulser, "Invader_Repulser");
  AddProcMob(3035, cityguard, "cityguard");
  //AddProcMob(3035, Invader_Repulser, "Invader_Repulser");
  AddProcMob(3038, commoner_one, "commoner_one");
  AddProcMob(3039, commoner_two, "commoner_two");
  AddProcMob(3042, money_changer, "money_changer");
  AddProcMob(3042, tithe_collector, "tithe_collector");
  AddProcMob(3059, waterdeep_guard_one, "waterdeep_guard_one");
  AddProcMob(3059, cityguard, "cityguard");
  //AddProcMob(3059, Invader_Repulser, "Invader_Repulser");
  AddProcMob(3061, janitor, "janitor");
  AddProcMob(3062, dog_one, "dog_one");
  AddProcMob(3064, drunk_one, "drunk_one");
  AddProcMob(3065, homeless_one, "homeless_one");
  AddProcMob(3066, cat_one, "cat_one");
  AddProcMob(3068, blob, "blob");
  AddProcMob(3069, jester, "jester");
  AddProcMob(3070, waterdeep_guard_one, "waterdeep_guard_one");
  AddProcMob(3070, cityguard, "cityguard");
  //AddProcMob(3070, Invader_Repulser, "Invader_Repulser");
  AddProcMob(3090, cat_one, "cat_one");

  /* Central Waterdeep */
  AddProcMob(3201, mercenary_one, "mercenary_one");
  AddProcMob(3203, drunk_three, "drunk_three");
  AddProcMob(3204, casino_one, "casino_one");
  AddProcMob(3205, casino_two, "casino_two");
  AddProcMob(3206, casino_four, "casino_four");
  AddProcMob(3207, casino_three, "casino_three");
  AddProcMob(3210, mercenary_one, "mercenary_one");
  AddProcMob(3211, piergeiron, "piergeiron");
  //AddProcMob(3211, Invader_Repulser, "Invader_Repulser");
  AddProcMob(3212, guard_two, "guard_two");
  //AddProcMob(3212, Invader_Repulser, "Invader_Repulser");
  /*AddProcMob(3215, park_one, "park_one");
  AddProcMob(3216, park_two, "park_two");
  AddProcMob(3217, park_three, "park_three");
  AddProcMob(3219, park_four, "park_four");
  AddProcMob(3220, park_five, "park_five");
  AddProcMob(3221, park_six, "park_six");*/
  AddProcMob(3229, guard_one, "guard_one");
  //AddProcMob(3229, Invader_Repulser, "Invader_Repulser");
  /*AddProcMob(3231, dog_two, "dog_two");*/
  AddProcMob(3232, youth_one, "youth_one");
  AddProcMob(3234, tailor_one, "tailor_one");
  AddProcMob(3235, shopper_one, "shopper_one");
  AddProcMob(3236, drunk_three, "drunk_three");
  AddProcMob(3240, shopper_two, "shopper_two");
  AddProcMob(3242, mercenary_two, "mercenary_two");
  AddProcMob(3243, mercenary_three, "mercenary_three");
  AddProcMob(3244, piergeiron_guard, "piergeiron_guard");
  /* End of Waterdeep special procedure assignments */

  AddProcMob(3818, breath_weapon_lightning, "lightning breath");

  /* Mt. Skelenak (New Moria) */
  AddProcMob(4070, piercer, "piercer");
  AddProcMob(4080, poison, "poison");
  AddProcMob(4120, guild_guard, "guild_guard");

  /* The Underworld */
  AddProcMob(4480, purple_worm, "purple_worm");
  AddProcMob(4530, piercer, "piercer");
  AddProcObj(4797, hellish_fury_bow, "hellish_fury_bow");

  /* Alterian Region */
  AddProcMob(4812, poison, "poison");
  AddProcMob(4830, wanderer, "wanderer");
  AddProcMob(4833, poison, "poison");

  /* Labyrinth */
  AddProcMob(5023, spider_venom_medium, "spider_venom_medium");

  /* Waterdeep Harbor */
  AddProcMob(5300, fisherman_one, "fisherman_one");
  AddProcMob(5302, fisherman_two, "fisherman_two");
  AddProcMob(5303, sailor_one, "sailor_one");
  AddProcMob(5305, seaman_one, "seaman_one");
  AddProcMob(5307, naval_one, "naval_one");
  AddProcMob(5308, naval_two, "naval_two");
  AddProcMob(5310, merchant_two, "merchant_two");
  AddProcMob(5311, naval_three, "naval_three");
  AddProcMob(5313, lighthouse_one, "lighthouse_one");
  AddProcMob(5315, lighthouse_two, "lighthouse_two");
  AddProcMob(5316, commoner_three, "commoner_three");
  AddProcMob(5317, seabird_one, "seabird_one");
  AddProcMob(5318, seabird_two, "seabird_two");
  AddProcMob(5320, naval_four, "naval_four");
  AddProcMob(5321, artillery_one, "artillery_one");

  /* The Guilds of Waterdeep */
  AddProcMob(5500, guild_guard_one, "guild_guard_one");
  AddProcMob(5503, guildmaster_one, "guildmaster_one");
  AddProcMob(5504, young_paladin_one, "young_paladin_one");
  AddProcMob(5505, guild_guard_two, "guild_guard_two");
  AddProcMob(5507, wrestler_one, "wrestler_one");
  AddProcMob(5508, young_mercenary_one, "young_mercenary_one");
  AddProcMob(5510, guildmaster_two, "guildmaster_two");
  AddProcMob(5511, guild_guard_three, "guild_guard_three");
  AddProcMob(5513, guildmaster_three, "guildmaster_three");
  AddProcMob(5514, young_monk_one, "young_monk_one");
  AddProcMob(5516, selune_one, "selune_one");
  AddProcMob(5517, selune_two, "selune_two");
  AddProcMob(5518, selune_three, "selune_three");
  AddProcMob(5519, selune_four, "selune_four");
  AddProcMob(5520, selune_five, "selune_five");
  AddProcMob(5521, selune_six, "selune_six");
  AddProcMob(5523, bouncer_four, "bouncer_four");
  AddProcMob(5524, guild_guard_four, "guild_guard_four");
  AddProcMob(5525, guildmaster_four, "guildmaster_four");
  AddProcMob(5527, prostitute_one, "prostitute_one");
  AddProcMob(5528, guild_guard_five, "guild_guard_five");
  AddProcMob(5530, guildmaster_five, "guildmaster_five");
  AddProcMob(5531, guild_guard_six, "guild_guard_six");
  AddProcMob(5533, young_druid_one, "young_druid_one");
  AddProcMob(5534, guildmaster_six, "guildmaster_six");
  AddProcMob(5535, guild_guard_seven, "guild_guard_seven");
  AddProcMob(5537, guild_guard_eight, "guild_guard_eight");
  AddProcMob(5538, young_necro_one, "young_necro_one");
  AddProcMob(5540, guildmaster_seven, "guildmaster_seven");
  AddProcMob(5541, bouncer_two, "bouncer_two");
  AddProcMob(5542, bouncer_three, "bouncer_three");
  AddProcMob(5543, bouncer_one, "bouncer_one");

  /* Western Realms */
  AddProcMob(5701, dryad, "dryad");
  AddProcMob(5702, dryad, "dryad");
  AddProcMob(5709, poison, "poison");
  AddProcMob(5739, navagator, "navagator");
  AddProcMob(5718, wr_ancientBrownie, "wr_ancientBrownie");

  /* Caer Corwell */
#if 0  /* Yanked.. the vnums are not matching the zone */
  AddProcMob(6005, cc_fisherffolk, "cc_fisherffolk");
  AddProcMob(6006, cc_female_ffolk, "cc_female_ffolk");
  AddProcMob(6008, cc_warehouse_man, "cc_warehouse_man");
  AddProcMob(6009, cc_warehouse_foreman, "cc_warehouse_foreman");
  AddProcMob(6113, poison, "poison");
  AddProcMob(6114, poison, "poison");
#endif

  /* Mithril Hall */
  AddProcMob(6501, receptionist, "receptionist");
  AddProcMob(6547, devour, "devour");
  AddProcMob(6574, money_changer, "money_changer");

#if 0  /* dragonnia out, spamming debug log.  JAB */
  /* Dragonnia */
  AddProcMob(6801, demodragon, "demodragon");
  AddProcMob(6802, dragons_of_dragonnia, "dragons_of_dragonnia");
  AddProcMob(6803, dragons_of_dragonnia, "dragons_of_dragonnia");
  AddProcMob(6804, dragons_of_dragonnia, "dragons_of_dragonnia");
  AddProcMob(6805, dragons_of_dragonnia, "dragons_of_dragonnia");
  AddProcMob(6806, dragons_of_dragonnia, "dragons_of_dragonnia");
  AddProcMob(6807, dragons_of_dragonnia, "dragons_of_dragonnia");
  AddProcMob(6808, dragons_of_dragonnia, "dragons_of_dragonnia");
  AddProcMob(6809, dragons_of_dragonnia, "dragons_of_dragonnia");
  AddProcMob(6810, dragons_of_dragonnia, "dragons_of_dragonnia");
  AddProcMob(6811, dragons_of_dragonnia, "dragons_of_dragonnia");
  AddProcMob(6812, dragons_of_dragonnia, "dragons_of_dragonnia");
  AddProcMob(6813, dragons_of_dragonnia, "dragons_of_dragonnia");
  AddProcMob(6814, dragon_guard, "dragon_guard");
  AddProcMob(6815, dragons_of_dragonnia, "dragons_of_dragonnia");
  AddProcMob(6816, dragons_of_dragonnia, "dragons_of_dragonnia");
  AddProcMob(6822, dragons_of_dragonnia, "dragons_of_dragonnia");
  AddProcMob(6823, dragons_of_dragonnia, "dragons_of_dragonnia");
  AddProcMob(6824, dragons_of_dragonnia, "dragons_of_dragonnia");
  AddProcMob(6825, dragons_of_dragonnia, "dragons_of_dragonnia");
  AddProcMob(6827, dragon_guard, "dragon_guard");
  AddProcMob(6828, baby_dragon, "baby_dragon");
  AddProcMob(6829, statue, "statue");
  AddProcMob(6831, dragons_of_dragonnia, "dragons_of_dragonnia");
  AddProcMob(6832, dragons_of_dragonnia, "dragons_of_dragonnia");
#endif

  /* Bloodstone */
  AddProcMob(7100, bs_citizen, "bs_citizen");
  AddProcMob(7101, bs_comwoman, "bs_comwoman");
  AddProcMob(7102, bs_brat, "bs_brat");
  AddProcMob(7103, bs_holyman, "bs_holyman");
  AddProcMob(7104, bs_merchant, "bs_merchant");
  AddProcMob(7105, bs_wino, "bs_wino");
  AddProcMob(7106, bs_watcher, "bs_watcher");
  AddProcMob(7107, bs_guard, "bs_guard");
  AddProcMob(7108, bs_squire, "bs_squire");
  AddProcMob(7109, bs_peddler, "bs_peddler");
  AddProcMob(7110, bs_critter, "bs_critter");
  AddProcMob(7110, devour, "devour");
  AddProcMob(7111, bs_critter, "bs_critter");
  AddProcMob(7111, devour, "devour");
  AddProcMob(7112, bs_critter, "bs_critter");
  AddProcMob(7112, devour, "devour");
  AddProcMob(7113, bs_timid, "bs_timid");
  AddProcMob(7114, bs_shady, "bs_shady");
  AddProcMob(7115, bs_sinister, "bs_sinister");
  AddProcMob(7116, bs_menacing, "bs_menacing");
  AddProcMob(7117, bs_executioner, "bs_executioner");
  AddProcMob(7118, bs_baron, "bs_baron");
  AddProcMob(7119, bs_soultaker, "bs_soultaker");
  AddProcMob(7119, bs_undead_die, "bs_undead_die");
  AddProcMob(7120, bs_sparrow, "bs_sparrow");
  AddProcMob(7121, bs_squirrel, "bs_squirrel");
  AddProcMob(7122, bs_squirrel, "bs_squirrel");
  AddProcMob(7123, bs_crow, "bs_crow");
  AddProcMob(7124, bs_mountainman, "bs_mountainman");
  AddProcMob(7125, bs_salesman, "bs_salesman");
  AddProcMob(7126, bs_nomad, "bs_nomad");
  AddProcMob(7127, bs_insane, "bs_insane");
  AddProcMob(7128, bs_homeless, "bs_homeless");
  AddProcMob(7129, bs_servant, "bs_servant");
  AddProcMob(7140, bs_wolf, "bs_wolf");
  AddProcMob(7141, bs_critter, "bs_critter");
  AddProcMob(7141, devour, "devour");
  AddProcMob(7142, bs_bear, "bs_bear");
  AddProcMob(7143, bs_gnoll, "bs_gnoll");
  AddProcMob(7144, bs_ettin, "bs_ettin");
  AddProcMob(7146, devour, "devour");
  AddProcMob(7147, bs_griffon, "bs_griffon");
  AddProcMob(7152, bs_boar, "bs_boar");
  AddProcMob(7153, bs_cub, "bs_cub");
  AddProcMob(7154, bs_fierce, "bs_fierce");
  AddProcMob(7154, devour, "devour");
  AddProcMob(7155, devour, "devour");
  AddProcMob(7156, bs_flayer, "bs_flayer");
  AddProcMob(7160, bs_stirge, "bs_stirge");
  AddProcMob(7161, bs_undead_without_die, "bs_undead_without_die");
  AddProcMob(7162, bs_undead_with_die, "bs_undead_with_die");
  AddProcMob(7162, bs_undead_die, "bs_undead_die");
  AddProcMob(7164, bs_robber, "bs_robber");
  AddProcMob(7165, bs_undead_without_die, "bs_undead_without_die");
  AddProcMob(7166, bs_undead_without_die, "bs_undead_without_die");
  AddProcMob(7167, bs_undead_with_die, "bs_undead_with_die");
  AddProcMob(7167, bs_undead_die, "bs_undead_die");
  AddProcMob(7170, bs_sickguard, "bs_sickguard");
  AddProcMob(7172, bs_armor, "bs_armor");
  AddProcMob(7173, bs_eatery, "bs_eatery");
  AddProcMob(7174, bs_magic, "bs_magic");
  AddProcMob(7175, bs_weapon, "bs_weapon");
  AddProcMob(7176, bs_stable, "bs_stable");
  AddProcMob(7177, bs_inn, "bs_inn");
  AddProcMob(7177, receptionist, "receptionist");
  AddProcMob(7178, bs_youngm, "bs_youngm");
  AddProcMob(7179, bs_youngw, "bs_youngw");
  AddProcMob(7180, bs_prostitute, "bs_prostitute");
  AddProcMob(7180, prostitute_one, "prostitute_one");
  AddProcMob(7189, bs_pet, "bs_pet");
  AddProcMob(7190, bs_banker, "bs_banker");
  AddProcMob(7190, money_changer, "money_changer");
  AddProcMob(7191, bs_trenton, "bs_trenton");
  AddProcMob(7192, bs_enochel, "bs_enochel");
  AddProcMob(7193, bs_veriallo, "bs_veriallo");
  AddProcMob(7194, bs_cerrio, "bs_cerrio");
  AddProcMob(7195, bs_ghelian, "bs_ghelian");
  AddProcMob(7197, bs_undead_die, "bs_undead_die");
  AddProcMob(7199, bs_mslave, "bs_mslave");
  AddProcMob(7200, bs_fslave, "bs_fslave");
  AddProcMob(7201, bs_antimaster, "bs_antimaster");
  AddProcMob(7202, bs_antishop, "bs_antishop");
  AddProcMob(7203, bs_antibrothel, "bs_antibrothel");
  AddProcMob(7204, bs_guildguard_antiwar, "bs_guildguard_antiwar");
  AddProcMob(7205, bs_zzzguard, "bs_zzzguard");
  AddProcMob(7206, bs_zzzcitizen, "bs_zzzcitizen");
  AddProcMob(7209, bs_boss, "bs_boss");
  AddProcMob(7210, bs_tax, "bs_tax");
  AddProcMob(7218, bs_guildguard_clersham, "bs_guildguard_clersham");
  AddProcMob(7220, bs_acolyte, "bs_acolyte");
  AddProcMob(7221, bs_clericpriest, "bs_clericpriest");
  AddProcMob(7308, bs_wino, "bs_wino");
  AddProcMob(7311, bs_necromaster, "bs_necromaster");
  AddProcMob(7314, bs_thiefmaster, "bs_thiefmaster");
  AddProcMob(7317, bs_assmaster, "bs_assmaster");
  AddProcMob(7321, bs_magemaster, "bs_magemaster");
  AddProcMob(7322, bs_thiefshop, "bs_thiefshop");
  AddProcMob(7323, bs_assshop, "bs_assshop");
  AddProcMob(7324, bs_necroshop, "bs_necroshop");
  AddProcMob(7325, bs_mageshop, "bs_mageshop");
  AddProcMob(7326, bs_payton, "bs_payton");
  AddProcMob(7327, bs_guildguard_thief, "bs_guildguard_thief");
  AddProcMob(7328, bs_guildguard_assassin, "bs_guildguard_assassin");
  AddProcMob(7329, bs_guildguard_necro, "bs_guildguard_necro");
  AddProcMob(7330, bs_guildguard_sorcconj, "bs_guildguard_sorcconj");
  AddProcMob(7335, bs_bouncer, "bs_bouncer");
  AddProcMob(7335, bs_bouncer, "bs_bouncer");

  AddProcMob(22522, orcus_master, "orcus_master");
  AddProcMob(22549, shadow_stun, "shadow_stun");
  AddProcMob(22550, shadow_stun, "shadow_stun");
  AddProcMob(22523, demon_cambion, "demon_cambion");
  AddProcMob(22524, demon_cambion, "demon_cambion");
  AddProcMob(22525, demon_manesDeath, "demon_manesDeath");
  AddProcMob(22526, demon_manesDeath, "demon_manesDeath");
  AddProcMob(22527, demon_dretch, "demon_dretch");
  AddProcMob(22531, demon_vrockSpores, "demon_vrockSpores");
  AddProcMob(22531, demon_vrockDanceOfRuin, "demon_vrockSpores");
  AddProcMob(22531, demon_vrockScreech, "demon_vrockScreech");
  AddProcMob(22534, demon_glabrezuGrab, "demon_glabrezuGrab");
  AddProcMob(22536, demon_marilithTail, "demon_marilithTail");

  /* Leuthilspar */
  AddProcMob(8004, money_changer, "money_changer");
  AddProcMob(8019, guild_guard, "guild_guard");
  AddProcMob(8027, devour, "devour");
  AddProcMob(8029, guild_guard, "guild_guard");
  AddProcMob(8036, receptionist, "receptionist");
  AddProcMob(8037, guild_guard, "guild_guard");
  AddProcMob(8039, guild_guard, "guild_guard");
  AddProcMob(8040, guild_guard, "guild_guard");
  AddProcMob(8041, guild_guard, "guild_guard");
  AddProcMob(8042, guild_guard, "guild_guard");
  AddProcMob(8044, janitor, "janitor");
  AddProcMob(8050, guild_guard, "guild_guard");
  AddProcMob(8311, guild_guard, "guild_guard");
  AddProcMob(8312, guild_guard, "guild_guard");
  AddProcMob(8313, guild_guard, "guild_guard");

  /* Castle Hades */
  AddProcMob(9040, lich_energy_drain, "lich_touch");
  AddProcMob(1104, lich_energy_drain, "lich_touch");
  AddProcMob(1098, lich_energy_drain, "lich_touch");
  AddProcMob(9038, breath_weapon_fire, "fire attack");

  /* Split Shield */
  AddProcMob(10301, gate_guard, "gate_guard");
  AddProcMob(10302, shady_man, "shady_man");

  /* Elf Ship 1 */
  AddProcMob(11001, guild_guard, "guild_guard");
  AddProcMob(11004, navagator, "navagator");
  AddProcMob(11020, ticket_taker, "ticket_taker");
  AddProcMob(11021, ticket_taker, "ticket_taker");

  /* Realms Master */
  AddProcMob(11101, navagator, "navagator");
  AddProcMob(11106, ticket_taker, "ticket_taker");
  /*
    AddProcMob(11100, realms_master_shout, "realms_master_shout");
    AddProcMob(11102, realms_master_shout, "realms_master_shout");
    AddProcMob(11103, realms_master_shout, "realms_master_shout");
    AddProcMob(11104, realms_master_shout, "realms_master_shout");
    AddProcMob(11105, realms_master_shout, "realms_master_shout");
    AddProcMob(11107, realms_master_shout, "realms_master_shout");
    AddProcMob(11108, realms_master_shout, "realms_master_shout");
    AddProcMob(11109, realms_master_shout, "realms_master_shout");
    AddProcMob(11110, realms_master_shout, "realms_master_shout");
    AddProcMob(11111, realms_master_shout, "realms_master_shout");
    AddProcMob(11112, realms_master_shout, "realms_master_shout");
    AddProcMob(11114, realms_master_shout, "realms_master_shout");
   */
  /* Elf Ship 2 */
  AddProcMob(11201, guild_guard, "guild_guard");
  AddProcMob(11204, navagator, "navagator");

  /* Silver Lady */
  AddProcMob(11301, navagator, "navagator");
  AddProcMob(11306, ticket_taker, "ticket_taker");
  /*
    AddProcMob(11300, silver_lady_shout, "silver_lady_shout");
    AddProcMob(11302, silver_lady_shout, "silver_lady_shout");
    AddProcMob(11303, silver_lady_shout, "silver_lady_shout");
    AddProcMob(11304, silver_lady_shout, "silver_lady_shout");
    AddProcMob(11305, silver_lady_shout, "silver_lady_shout");
    AddProcMob(11307, silver_lady_shout, "silver_lady_shout");
    AddProcMob(11308, silver_lady_shout, "silver_lady_shout");
    AddProcMob(11309, silver_lady_shout, "silver_lady_shout");
    AddProcMob(11310, silver_lady_shout, "silver_lady_shout");
    AddProcMob(11311, silver_lady_shout, "silver_lady_shout");
    AddProcMob(11312, silver_lady_shout, "silver_lady_shout");
    AddProcMob(11314, silver_lady_shout, "silver_lady_shout");
   */
  /* Ghore */
  AddProcMob(11528, guild_guard, "guild_guard");
  AddProcMob(11542, ghore_paradise, "ghore_paradise");
  AddProcMob(11550, guild_guard, "guild_guard");
  AddProcMob(11559, money_changer, "money_changer");
  AddProcMob(11587, guild_guard, "guild_guard");
  AddProcMob(11588, guild_guard, "guild_guard");
  AddProcMob(11589, guild_guard, "guild_guard");
  AddProcMob(11590, guild_guard, "guild_guard");
  AddProcMob(11610, receptionist, "receptionist");

  /* Lava Tubes One */
  AddProcMob(12000, snowbeast, "snowbeast");
  AddProcMob(12001, snowvulture, "snowvulture");
  AddProcMob(12002, spiny, "spiny");
  AddProcMob(12003, spiny, "spiny");
  AddProcMob(12005, phalanx, "phalanx");
  AddProcMob(12006, skeleton, "skeleton");
  AddProcMob(12022, spore_ball, "spore_ball");
  AddProcMob(12023, spore_ball, "spore_ball");
  AddProcMob(12024, skeleton, "skeleton");
  AddProcMob(12025, xexos, "xexos");
  AddProcMob(12026, agthrodos, "agthrodos");
  AddProcMob(12027, automaton_unblock, "automaton");

  /* Citadel */
  AddProcMob(13019, breath_weapon_gas, "gas breath");
  AddProcMob(13020, breath_weapon_fire, "fire breath");
  AddProcMob(13021, breath_weapon_cold, "frost breath");
  AddProcMob(13022, breath_weapon_lightning, "lightning breath");
  AddProcMob(13024, breath_weapon_gas, "gas breath");

  /* Faerie Realm */
  AddProcMob(14015, finn, "finn");
  AddProcMob(14026, tree_spirit, "tree_spirit");
  AddProcMob(14029, faerie, "faerie");
  AddProcMob(14048, cricket, "cricket");

  /* Wilderness Near Waterdeep */
  AddProcMob(14202, bridge_troll, "bridge_troll");

  /* WD bypass road */
  AddProcMob(14601, plant_attacks_poison, "plant poison");
  AddProcMob(14602, plant_attacks_paralysis, "plant paralysis");
  AddProcMob(14603, plant_attacks_blindness, "plant blindness");
  AddProcMob(14605, barbarian_spiritist, "barbarian_spiritist");

  AddProcMob(15014, breath_weapon_gas, "gas breath");
  AddProcMob(15107, breath_weapon_gas, "gas breath");

  /* Faang */
  AddProcMob(15204, boulder_pusher, "boulder_pusher");
  AddProcMob(15217, poison, "poison");
  AddProcMob(15252, poison, "poison");
  AddProcMob(15279, poison, "poison");
  AddProcMob(15312, guild_guard, "guild_guard");
  AddProcMob(15314, guild_guard, "guild_guard");
  AddProcMob(15316, guild_guard, "guild_guard");
  AddProcMob(15318, guild_guard, "guild_guard");
  AddProcMob(15347, receptionist, "receptionist");
  AddProcMob(15348, receptionist, "receptionist");
  AddProcMob(15349, money_changer, "money_changer");

  /* New Cavecity */
  /* AddProcMob(15113, dranum_jurtrem, "dranum_jurtrem"); */
  AddProcMob(15125, dranum_jurtrem, "dranum_jurtrem");
  AddProcMob(15113, dranum_lifesuck, "dranum_lifesuck");

  /* Tower of High Sorcery Two */
  AddProcMob(15901, bulette, "bulette");

  /* Luiren */
  AddProcMob(16015, breath_weapon_gas, "gas breath");
  AddProcMob(16022, guild_guard, "guild_guard");
  AddProcMob(16023, guild_guard, "guild_guard");
  AddProcMob(16024, guild_guard, "guild_guard");
  AddProcMob(16025, guild_guard, "guild_guard");
  AddProcMob(16026, guild_guard, "guild_guard");
  AddProcMob(16027, guild_guard, "guild_guard");
  AddProcMob(16028, guild_guard, "guild_guard");
  AddProcMob(16029, guild_guard, "guild_guard");
  AddProcMob(16043, money_changer, "money_changer");
  AddProcMob(16062, devour, "devour");
  AddProcMob(16063, janitor, "janitor");
  AddProcMob(16064, receptionist, "receptionist");

  /* Players Guild II */
  AddProcMob(16500, receptionist, "receptionist");
  AddProcMob(16501, guild_guard, "guild_guard");
  AddProcMob(16501, breath_weapon_gas, "gas breath");

  /* Fun */
  AddProcMob(1228, beavis, "Uhhhh, I'm Beavis you Dork");
  AddProcMob(1229, butthead, "Heh Heh heh Hehehehe");
  AddProcMob(1230, billthecat, "Soup!");

  /* Astral Plane - Tiamat */
  AddProcMob(19700, Tiamat, "Tiamat");
  AddProcMob(19750, Tiamat_Crimson_Fury, "Tiamat_Crimson_Fury");
  AddProcMob(19701, lich_energy_drain, "lich_energy_drain");
  AddProcMob(19701, breath_weapon_acid, "breath_weapon_acid");
  AddProcMob(19701, Tiamat_Crimson_Fury, "Tiamat_Crimson_Fury");
  AddProcMob(19710, guild_guard, "guild_guard");
  AddProcMob(19720, guild_guard, "guild_guard");
  AddProcMob(19730, guild_guard, "guild_guard");
  AddProcMob(19740, guild_guard, "guild_guard");
  AddProcMob(19750, guild_guard, "guild_guard");
  AddProcMob(19810, breath_weapon_fire, "fire breath");
  AddProcMob(19820, breath_weapon_fire, "fire breath");
  AddProcMob(19920, demogorgon_shout, "demogorgon_shout");
  AddProcMob(19921, demogorgon_shout, "demogorgon_shout");

  AddProcMob(20305, breath_attack_lightning, "lightning attack");

  AddProcMob(20964, receptionist, "receptionist");
  AddProcMob(20971, lostTotemRestorer, "lostTotemRestorer");


  /* Bloodstone III */
  AddProcMob(22416, poison, "poison");
  /*
  AddProcMob(22432, follow_that_mob, "follow_that_mob");
  AddProcMob(22434, follow_that_mob, "follow_that_mob");
  AddProcMob(22436, follow_that_mob, "follow_that_mob");
  AddProcMob(22456, follow_that_mob, "follow_that_mob");
  AddProcMob(22458, follow_that_mob, "follow_that_mob");
  AddProcMob(22461, follow_that_mob, "follow_that_mob");
  AddProcMob(22466, follow_that_mob, "follow_that_mob");
  AddProcMob(22467, follow_that_mob, "follow_that_mob");
  AddProcMob(22494, bs_guildguard_monk, "bs_guildguard_monk");
  AddProcMob(22508, follow_that_mob, "follow_that_mob");
  AddProcMob(22509, follow_that_mob, "follow_that_mob");
  AddProcMob(22510, follow_that_mob, "follow_that_mob");
  AddProcMob(22512, follow_that_mob, "follow_that_mob");
  AddProcMob(22513, follow_that_mob, "follow_that_mob");
   */

  /* Plane of Air One */
  AddProcMob(24400, breath_weapon_lightning, "lightning breath");
  AddProcMob(24440, breath_weapon_lightning, "lightning breath");
  AddProcMob(24450, breath_weapon_cold, "cold attack");
  AddProcMob(24440, yancbin_shout, "yancbin_shout");

  /* Plane of Fire One */
  AddProcMob(25000, guild_guard, "guild_guard");
  AddProcMob(25400, breath_weapon_gas, "gas breath");
  AddProcMob(25400, guild_guard, "guild_guard");
  AddProcMob(25404, breath_weapon_fire, "fire breath");
  AddProcMob(25406, breath_weapon_fire, "fire breath");
  AddProcMob(25406, imix_shout, "imix_shout");
  AddProcMob(25409, imix_pet_demon_shout, "imix_pet_demon_shout");

  /* Swamp I */
  AddProcMob(26008, poison, "poison");

  /* Moonshae */
  AddProcMob(26218, sister_knight, "sister_knight");
  AddProcMob(26219, sister_knight, "sister_knight");
  AddProcMob(26220, sister_knight, "sister_knight");
  AddProcMob(26221, sister_knight, "sister_knight");
  AddProcMob(26222, sister_knight, "sister_knight");
  AddProcMob(26238, jessica_summon_wisp, "jessica_summon_wisp");
  AddProcMob(26241, robyn_summon_wisp, "robyn_summon_wisp");
  AddProcMob(26242, robyn_summon_wisp, "robyn_summon_wisp");
  AddProcMob(26243, robyn_summon_wisp, "robyn_summon_wisp");
  AddProcMob(26225, robyn_summon_servant, "robyn_summon_servant");
  AddProcMob(26216, ilshazone_roll_with_it, "rollwithit");
  AddProcMob(26244, ilshazone_roll_with_it, "rollwithit");
  AddProcMob(26245, ilshazone_roll_with_it, "rollwithit");
  AddProcMob(26208, ilshazone_roll_with_it, "rollwithit");
  AddProcMob(20247, ilshazone_roll_with_it, "rollwithit");
  AddProcMob(26236, ilshazone_roll_with_it, "rollwithit");

  /* Myrloch Vale */
  AddProcMob(26409, receptionist, "receptionist");

  /* Basin Wastes */
  AddProcMob(34006, breath_weapon_fire, "fire breath");
  AddProcMob(34009, poison, "poison");

  /* Gloomhaven */
  AddProcMob(34206, poison, "poison");
  AddProcMob(34261, guild_guard, "guild_guard");
  AddProcMob(34262, guild_guard, "guild_guard");
  AddProcMob(34425, money_changer, "money_changer");
  AddProcMob(34225, money_changer, "money_changer");
  AddProcMob(34227, receptionist, "receptionist");
  AddProcMob(34233, navagator, "navagator");
  AddProcMob(34274, gloomhaven_gate_guard, "gloomhaven_gate_guard");

  /* Derro pit */
  AddProcMob(34501, poison, "poison");

  /* Ruins of Yath Oloth */
  AddProcMob(34833, ryo_bansheeWail, "ryo_bansheeWail");

  /* Lava Caverns */
  AddProcMob(35704, breath_attack_fire, "breath_attack_fire");
  AddProcMob(35722, breath_attack_fire, "breath_attack_fire");
  AddProcMob(35735, breath_attack_fire, "breath_attack_fire");
  AddProcMob(35751, breath_attack_fire, "breath_attack_fire");
  AddProcMob(35752, breath_attack_fire, "breath_attack_fire");

  /* Nizari */
  AddProcMob(40053, thief, "thief");
  AddProcMob(40054, thief, "thief");
  AddProcMob(40055, thief, "thief");

  // Blip!
  AddProcMob(41900, swallow_whole_spit, "swallow_whole_spit");

  /* Artimus Nevarlith */
  AddProcMob(43310, major_beholder, "major_beholder");

  /* Calimport */
  AddProcMob(46100, money_changer, "money_changer");

  /* Thunderhead Peak */
  AddProcMob(46990, lichConverter, "lichConverter");

  /* Meilech */
  AddProcMob(52365, major_beholder, "major_beholder");

  /* Hulburg */
  AddProcMob(59286, money_changer, "money_changer");
  AddProcMob(50772, plant_attacks_poison, "plant poison");
  AddProcMob(59333, plant_attacks_paralysis, "plant paralysis");
  AddProcMob(59248, plant_attacks_blindness, "plant blindness");

  /* Elemental Tower */
  AddProcMob(62401, elemental_tower_shout, "elemental_tower_shout");
  AddProcMob(62402, elemental_tower_shout, "elemental_tower_shout");
  AddProcMob(62405, elemental_tower_shout, "elemental_tower_shout");
  AddProcMob(62406, elemental_tower_shout, "elemental_tower_shout");
  AddProcMob(62402, et_earthBoss, "et_earthBoss");
  AddProcMob(62405, et_airBoss, "et_airBoss");
  AddProcMob(62401, et_fireBoss, "et_fireBoss");
  AddProcMob(62406, et_waterBoss, "et_waterBoss");

  /* Ashrumite Village */
  AddProcMob(66037, drunk_one, "drunk_one");
  AddProcMob(66001, cityguard, "cityguard");
  AddProcMob(66002, cityguard, "cityguard");
  AddProcMob(66003, cityguard, "cityguard");
  AddProcMob(66031, guild_guard, "guild_guard");
  AddProcMob(66024, guild_guard, "guild_guard");
  AddProcMob(66023, guild_guard, "guild_guard");
  AddProcMob(66025, guild_guard, "guild_guard");
  AddProcMob(66022, guild_guard, "guild_guard");
  AddProcMob(66034, receptionist, "receptionist");
  AddProcMob(66036, janitor, "janitor");
  AddProcMob(66038, money_changer, "money_changer");

  /* Basion of Moncreef */
  AddProcMob(70101, bastion_ticket_taker, "bastion_ticket_taker");

  /* associations */
  AddProcMob(75000, guild_guard, "guild guard");
  AddProcMob(75001, receptionist, "receptionist");

  /* ORG - Swamp Hunters Mobs */
  AddProcMob(75100, guild_guard, "guild_guard");

  /* ORG - Honest Lyre Mobs */
  AddProcMob(75200, guild_guard, "guild_guard");

  /* ORG - Order of the Wyrm Mobs */
  AddProcMob(75301, guild_guard, "guild_guard");
  AddProcMob(75300, receptionist, "receptionist");
  AddProcMob(75303, money_changer, "money_changer");

  /* ORG - Oath of Gallistel Mobs */
  AddProcMob(75400, guild_guard, "guild_guard");

  /* ORG - Errants of Deorc Mobs */
  AddProcMob(75500, guild_guard, "guild_guard");
  AddProcMob(75501, receptionist, "receptionist");

  /* ORG - Sisters of the Flame */
  AddProcMob(75802, guild_guard, "guild_guard");
  AddProcMob(75801, receptionist, "receptionist");

  /* ORG - The Honest Lyre */
  AddProcMob(75200, guild_guard, "guild_guard");

  /* ORG - Vermilion Shadow */
  AddProcMob(75701, guild_guard, "guild_guard");
  AddProcMob(75751, guild_guard, "guild_guard");

  /* Zhentil Keep */
  AddProcMob(81029, major_beholder, "major_beholder");

  /* UnderDark River Ruins */
  AddProcMob(81508, ancient_man, "ancient_man");

  /* Menden-on-the-Deep */
  AddProcMob(88805, menden_fisherman, "menden_fisherman");
  AddProcMob(88806, menden_magus, "menden_magus");
  AddProcMob(88812, menden_inv_serv_die, "menden_inv_serv_die");
  AddProcMob(88813, menden_figurine_die, "menden_figurine_die");
  AddProcMob(88814, crystal_golem_die, "crystal_golem_die");
  AddProcMob(88815, hippogriff_die, "hippogriff_die");

  AddProcMob(90020, breath_weapon_gas, "gas breath");

  AddProcMob(92032, breath_attack_fire, "fire attack");

  /* Myth Unnohyr */

  AddProcMob(92613, um2_blackPuddingSplit, "um2_blackPuddingSplit");
  AddProcMob(92614, devour, "devour");
  AddProcMob(92608, piercer, "piercer");

  /* Mirar (Chionthar) Ferry */
  AddProcMob(90390, navagator, "navagator");

  /* Bloodtusk */
  AddProcMob(94702, receptionist, "receptionist");
  AddProcMob(94780, justice_clerk, "justice_clerk");
  AddProcMob(94793, money_changer, "money_changer");

  /* Mistywood */
  AddProcMob(95028, poison, "poison");

  /* Jotunhiem */
  AddProcMob(96007, swallow_whole, "swallow prey");
  AddProcMob(96013, jotun_mimer, "jotun_mimer");
  AddProcMob(96027, jotun_thrym, "jotun_thrym");
  AddProcMob(96030, jotun_balor, "jotun_balor");
  AddProcMob(96040, jotun_utgard_loki, "jotun_utgard_loki");
  AddProcMob(96076, swallow_whole, "swallow prey");
  AddProcMob(96070, breath_weapon_cold, "frost breath");
  AddProcMob(96077, breath_weapon_lightning, "lightning breath");

  /* Ixarkon (Mindflayer Hometown */
  AddProcMob(96429, receptionist, "receptionist");
  AddProcMob(96436, money_changer, "money_changer");

  /* Brain Stem Tunnel */
  AddProcMob(96806, breath_attack_acid, "breath_attack_acid");

  /* Icecrag keep */
  AddProcMob(97000, ice_snooty_wife, "ice_snooty_wife");
  AddProcMob(97001, ice_cleaning_crew, "ice_cleaning_crew");
  AddProcMob(97002, ice_artist, "ice_artist");
  AddProcMob(97003, ice_malice, "ice_malice");
  AddProcMob(97005, ice_privates, "ice_privates");
  AddProcMob(97006, ice_masha, "ice_masha");
  AddProcMob(97007, ice_tubby_merchant, "ice_tubby_merchant");
  AddProcMob(97008, ice_priest, "ice_priest");
  AddProcMob(97009, follow_that_mob, "follow_that_mob");
  AddProcMob(97011, ice_garden_attendant, "ice_garden_attendant");
  AddProcMob(97014, ice_raucous_guest, "ice_raucous_guest");
  AddProcMob(97016, ice_tar, "ice_tar");
  AddProcMob(97018, follow_that_mob, "follow_that_mob");
  AddProcMob(97019, follow_that_mob, "follow_that_mob");
  AddProcMob(97021, ice_commander, "ice_commander");
  AddProcMob(97023, ice_viscount, "ice_viscount");
  AddProcMob(97028, ice_masonary_crew, "ice_masonary_crew");
  AddProcMob(97030, ice_wolf, "ice_wolf");
  AddProcMob(97033, ice_impatient_guest, "ice_impatient_guest");
  AddProcMob(97036, follow_that_mob, "follow_that_mob");
  AddProcMob(97037, follow_that_mob, "follow_that_mob");
  AddProcMob(97040, ice_bodyguards, "ice_bodyguards");
  AddProcMob(97041, ice_bodyguards, "ice_bodyguards");
  AddProcMob(97042, ice_bodyguards, "ice_bodyguards");
  AddProcMob(97056, breath_weapon_cold, "frost breath");
  AddProcMob(97061, swallow_whole, "swallow prey");

  AddProcMob(97312, follow_that_mob, "follow_that_mob");
  AddProcMob(97321, follow_that_mob, "follow_that_mob");
  AddProcMob(97322, follow_that_mob, "follow_that_mob");
  AddProcMob(97325, follow_that_mob, "follow_that_mob");
  AddProcMob(97326, follow_that_mob, "follow_that_mob");
  AddProcMob(97327, follow_that_mob, "follow_that_mob");

  /* Havenport --CRM */
  AddProcMob(98357, ticket_taker, "ticket_taker");
  AddProcMob(98358, ticket_taker, "ticket_taker");
  AddProcMob(98300, havenport_lorde_blindproc, "lorde_blindproc");

  /* Spirit Raven */
  AddProcMob(98452, navagator, "navagator");

  /* Skerttd-Gul */
  AddProcMob(99038, breath_weapon_lightning, "lightning breath");

  /* Captains Fancy */
  AddProcMob(46610, navagator, "navagator");

  /* Dwarven Mining Settlement */
  AddProcMob(80013, major_beholder, "major_beholder");
  AddProcMob(80014, major_beholder, "major_beholder");
  AddProcMob(80018, major_beholder, "major_beholder");
  AddProcMob(80019, major_beholder, "major_beholder");
  AddProcMob(80020, major_beholder, "major_beholder");

  /* Newhaven */
  AddProcMob(51246, winged_deva, "winged_deva");
  AddProcMob(51333, ilshazone_kamerynn, "kamerynn");
  AddProcMob(51334, ilshazone_canthus, "canthus_breathe_summon");
  AddProcMob(20378, ashentoris, "lava_ball");
  AddProcMob(20388, breath_attack_fire, "fire breath");

  /* Demi Plane */
  AddProcMob(43358, movanic_deva, "movanic_deva");

  /* Neshkal */
  AddProcMob(50621, guild_guard, "guild_guard");

  /* Myth Drannor */
  AddProcMob(81706, devil_pitFiendBite, "devil_pitFiendBite");
  AddProcMob(81706, devil_pitFiendTail, "devil_pitFiendTail");
  AddProcMob(81747, devil_pitFiendBite, "devil_pitFiendBite");
  AddProcMob(81747, devil_pitFiendTail, "devil_pitFiendTail");
  AddProcMob(81746, devil_pitFiendBite, "devil_pitFiendBite");
  AddProcMob(81746, devil_pitFiendTail, "devil_pitFiendTail");
  AddProcMob(83224, devil_pitFiendBite, "devil_pitFiendBite");
  AddProcMob(83224, devil_pitFiendTail, "devil_pitFiendTail");
  AddProcMob(83253, lich_energy_drain, "lich_touch");

  /* New undead procs */
  AddProcMob(1256, undead_ghoul, "ghoul");
  AddProcMob(1257, undead_shadow, "shadow");
  AddProcMob(1258, undead_wight, "wight");
  AddProcMob(1259, undead_ghast, "ghast");
  AddProcMob(1260, undead_wraith, "wraith");
  AddProcMob(1261, undead_spectre, "spectre");
  AddProcMob(1262, undead_ghost, "ghost");

  /* Cart shop and trade */
  if (IS_ENABLED(CODE_TRADE)) {
    AddProcRoom(99500, cart_shops, "cart_shops");
    AddProcRoom(99510, trade_center, "trade center");
    AddProcRoom(99502, cart_shops, "cart_shops");
    AddProcRoom(99512, trade_center, "trade center");
    AddProcRoom(99504, cart_shops, "cart_shops");
    AddProcRoom(99514, trade_center, "trade center");
    /*    AddProcObj (99500, cart, "cart check"); */
    AddProcMob(99501, bandit, "bandit");
    AddProcMob(99502, bandit, "bandit");
    AddProcMob(99503, bandit, "bandit");
    AddProcMob(99504, bandit, "bandit");
    AddProcMob(99505, bandit, "bandit");
    AddProcMob(99506, bandit, "bandit");
    AddProcMob(99507, bandit, "bandit");
  }

  /* Muspelheim */
  AddProcMob(58611, breath_attack_fire, "fire attack");
  AddProcMob(58624, breath_attack_fire, "fire attack");
  AddProcMob(58828, breath_attack_fire, "fire attack");
  AddProcMob(59025, breath_attack_fire, "fire attack");
  AddProcMob(59026, breath_attack_fire, "fire attack");
  AddProcMob(58803, muspel_lookout_shout_m58806, "muspel_lookout_shout_m58806");
  AddProcMob(58804, muspel_giant_shout_m58806, "muspel_giant_shout_m58806");
  AddProcMob(58805, muspel_giant_shout_m58806, "muspel_giant_shout_m58806");
  AddProcMob(58706, muspel_lookout_shout_m58708_m58709, "muspel_lookout_shout_m58708_m58709");
  AddProcMob(58707, muspel_giant_shout_m58708_m58709, "muspel_giant_shout_m58708_m58709");
  AddProcMob(58815, muspel_giant_shout_m58833, "muspel_giant_shout_m58833");
  AddProcMob(58816, muspel_giant_shout_m58833, "muspel_giant_shout_m58833");
  AddProcMob(58817, muspel_giant_shout_m58833, "muspel_giant_shout_m58833");
  AddProcMob(58818, muspel_giant_shout_m58833, "muspel_giant_shout_m58833");
  AddProcMob(58819, muspel_giant_shout_m58833, "muspel_giant_shout_m58833");
  AddProcMob(58820, muspel_giant_shout_m58833, "muspel_giant_shout_m58833");
  AddProcMob(58821, muspel_giant_shout_m58833, "muspel_giant_shout_m58833");
  AddProcMob(58824, muspel_giant_shout_m58833, "muspel_giant_shout_m58833");
  AddProcMob(58825, muspel_giant_shout_m58833, "muspel_giant_shout_m58833");
  AddProcMob(58831, muspel_lookout_shout_m58833, "muspel_lookout_shout_m58833");

  // AV -- Shev
  AddProcMob(59810, av_drisinil_shout, "av_drisinil_shout");
  AddProcMob(59830, av_tukra_shout, "av_tukra_shout");
  AddProcMob(59815, av_vanish, "av_vanish");
  AddProcMob(59835, av_vanish, "av_vanish");


  /* Halruaa */
  AddProcMob(53268, halruaa_transmuter1, "halruaa_transmuter2");
  AddProcMob(53269, halruaa_transmuter2, "halruaa_transmuter3");
  AddProcMob(53270, halruaa_transmuter_itemdrop, "halruaa_transmuter_itemdrop");
  AddProcMob(53362, halruaa_fleshdoll, "halruaa_fleshdoll");
  AddProcMob(53264, halruaa_small_prismatic_elem, "halruaa_small_prismatic_elem");
  AddProcMob(53265, halruaa_crit_prismatic_elem, "halruaa_crit_prismatic_elem");
  AddProcMob(53266, halruaa_uber_prismatic_elem, "halruaa_uber_prismatic_elem");

  // Temple of Twisted Flesh
  AddProcMob(45146, ttf_tentacles, "ttf_tentacles");
  AddProcMob(45116, ttf_fourarms, "ttf_fourarms");
  AddProcMob(45182, ttf_rot_bringer, "ttf_rot_bringer");

  // Magma
  AddProcMob(33803, breath_weapon_fire, "fire_breath");
  AddProcMob(33806, breath_weapon_fire, "fire_breath");
  AddProcMob(33804, magma_dragon_shout, "magma_dragon_shout");

  /* Abandoned Monastery */
  AddProcMob(90812, pure_blood_90812, "pure_blood_transformation");
  AddProcMob(90819, pure_blood_90819, "pure_blood_transformation");
  AddProcMob(90837, pure_blood_90837, "pure_blood_transformation");
  AddProcMob(90866, pure_blood_90866, "pure_blood_transformation");
  AddProcMob(90840, guild_guard, "guild_guard");
  AddProcMob(90842, guild_guard, "guild_guard");
  AddProcMob(90855, shadow_giant, "shadow_giant");
  AddProcMob(90856, shadow_giant, "shadow_giant");
  AddProcMob(90858, shadow_giant, "shadow_giant");
  AddProcMob(90862, shadow_giant, "shadow_giant");
  AddProcMob(90863, shadow_giant, "shadow_giant");
  AddProcMob(90864, shadow_giant, "shadow_giant");
  AddProcMob(90865, shadow_giant, "shadow_giant");
  AddProcMob(90869, shadow_giant, "shadow_giant");

  /* Hulburg */
  AddProcMob(59106, hulburg_beholder_major, "hulburg_beholder_major");
  AddProcMob(59203, hulburg_beholder_major, "hulburg_beholder_major");
  AddProcMob(59205, hulburg_beholder_major, "hulburg_beholder_major");
  AddProcMob(59299, hulburg_beholder_major, "hulburg_beholder_major");
  AddProcMob(59105, hulburg_beholder_major, "hulburg_beholder_major");
  AddProcMob(59250, hulburg_beholder_minor, "hulburg_beholder_minor");
  AddProcMob(59206, hulburg_beholder_minor, "hulburg_beholder_minor");
  AddProcMob(59117, hulburg_beholder_minor, "hulburg_beholder_minor");
  AddProcMob(59104, hulburg_beholder_minor, "hulburg_beholder_minor");
  AddProcMob(59332, hulburg_beholder_minor, "hulburg_beholder_minor");
  AddProcMob(59314, hulburg_beholder_minor, "hulburg_beholder_minor");
  AddProcMob(59407, hulburg_beholder_minor, "hulburg_beholder_minor");
  AddProcMob(59110, hulburg_beholder_minor, "hulburg_beholder_minor");
  AddProcMob(59109, hulburg_beholder_minor, "hulburg_beholder_minor");
  AddProcMob(59107, hulburg_beholder_minor, "hulburg_beholder_minor");
  AddProcMob(59362, hulburg_beholder_minor, "hulburg_beholder_minor");
  AddProcMob(59363, hulburg_beholder_minor, "hulburg_beholder_minor");
  AddProcMob(59119, hulburg_beholder_minor, "hulburg_beholder_minor");
  AddProcMob(59313, hulburg_beholder_minor, "hulburg_beholder_minor");
  AddProcMob(59336, hulburg_beholder_minor, "hulburg_beholder_minor");

  /* specs.Tarrasque.c */
  assignTarrasqueMobiles();

  /* Drider */
  AddProcMob(70603, lich_energy_drain, "lich_touch");

#if 0
  /* neverwinter */
  AddProcMob(99001, nw_woodelf, "nw_woodelf");
  AddProcMob(99002, nw_elfhealer, "nw_elfhealer");
  AddProcMob(99003, nw_ammaster, "nw_ammaster");
  AddProcMob(99004, nw_sapmaster, "nw_sapmaster");
  AddProcMob(99005, nw_diamaster, "nw_diamaster");
  AddProcMob(99006, nw_rubmaster, "nw_rubmaster");
  AddProcMob(99007, nw_emmaster, "nw_emmaster");
  AddProcMob(99009, nw_human, "nw_human");
  AddProcMob(99010, nw_hafbreed, "nw_hafbreed");
  AddProcMob(99011, nw_owl, "nw_owl");
  AddProcMob(99015, nw_golem, "nw_golem");
  AddProcMob(99016, nw_mirroid, "nw_mirroid");
  AddProcMob(99017, nw_agatha, "nw_agatha");
  AddProcMob(99018, nw_farmer, "nw_farmer");
  AddProcMob(99019, nw_chicken, "nw_chicken");
  AddProcMob(99021, nw_pig, "nw_pig");
  AddProcMob(99022, nw_cow, "nw_cow");
  AddProcMob(99023, nw_chief, "nw_chief");
  AddProcMob(99028, nw_malchor, "nw_malchor");
  AddProcMob(99029, nw_builder, "nw_builder");
  AddProcMob(99030, nw_carpen, "nw_carpen");
  AddProcMob(99031, nw_logger, "nw_logger");
  AddProcMob(99032, nw_cutter, "nw_cutter");
  AddProcMob(99033, nw_foreman, "nw_foreman");
  AddProcMob(99034, nw_ansal, "nw_ansal");
  AddProcMob(99035, nw_brock, "nw_brock");
  AddProcMob(99036, nw_merthol, "nw_merthol");
  AddProcMob(99037, nw_vitnor, "nw_vitnor");
#endif

  /* Pirate Isle */
  AddProcMob(20038, doppelganger_switch, "doppelganger_switch");

  /* specs.bhaal.c */
  assignBhaalMobiles();

  /* specs.darkhold.c */
  assignDarkholdMobiles();

  /* specs.seeliecourt.c */
  assignSeelieCourtMobiles();

  /* Yggdrasil */
  AddProcMob(62800, yggdrasil_branch, "yggdrasil_branch");
  AddProcMob(62801, yggdrasil_branch, "yggdrasil_branch");
  AddProcMob(62802, yggdrasil_branch, "yggdrasil_branch");
  AddProcMob(62803, yggdrasil_branch, "yggdrasil_branch");
  AddProcMob(62804, yggdrasil_branch, "yggdrasil_branch");

  /* specs.tiamat.c */
  assignTiamatMobiles();

  /* Orcish Hall of Plunder (specs.ohp.c) */
  assignOhpMobiles();
  assignOhpObjects();

  logit(LOG_STATUS, "   Booting the shops.");
  fprintf(stderr, "--    Booting the shops.\n");
  /* Using optimized shop loading function for improved performance:
   * - O(1) keyword lookup via hash table instead of linear search
   * - Pre-allocated shop array with dynamic expansion
   * - More efficient string parsing and memory management
   */
  logit(LOG_STATUS, "Loading shop data - this process can take approximately 7 minutes");
  fprintf(stderr, "Loading shop data - this process can take approximately 7 minutes\n");
  boot_the_shops_optimized();
  /* logit(LOG_DEBUG, "DEBUG: boot_the_shops_optimized() completed, now assigning shopkeepers"); */
  /* fprintf(stderr, "DEBUG: boot_the_shops_optimized() completed, now assigning shopkeepers\n"); */
  logit(LOG_STATUS, "   Assigning the shopkeepers.");
  fprintf(stderr, "--    Booting the shopkeepers.\n");
  assign_the_shopkeepers();
  /* logit(LOG_DEBUG, "DEBUG: assign_the_shopkeepers() completed"); */
  /* fprintf(stderr, "DEBUG: assign_the_shopkeepers() completed\n"); */

  logit(LOG_STATUS, "   Booting quests.");
  fprintf(stderr, "--    Booting the quests.\n");
  boot_the_quests();
  logit(LOG_STATUS, "   Assigning questers.");
  fprintf(stderr, "--    Assigning the questors.\n");
  assign_the_questers();
}

/* assign special procedures to objects */

void assign_objects(void) {
  /* specs.undermountain.c */
  assignUndermountainObjects();
  /* specs.jotunheim.c */
  assignJotunheimObjects();
  /* specs.icecrag.c */
  assignIcecrag2Objects();
  /* specs.greycloak.c  -- diirinka */
  assignGreycloakObjects();
  /* specs.scornubel.c  -- diirinka */
  assignScornubelObjs();
  /* specs.spiderhaunt.c  -- diirinka */
  assignSpiderhauntObjects();
  /* specs.hyssk.c -- Shevarash */
  assignHysskObjects();
  /* specs.calimshan.c  -- diirinka */
  assignCalimshanObjects();
  /* specs.balders.c  -- JAB */
  assignBaldersGateObjects();
  /* specs.zhentilkeep.c */
  AssignZKObjects();
  /* specs.dobluth.c  -- CRM */
  assignDobluthKyorObjects();
  /* specs.tarsellian.c -- CRM */
  assignTarsellianObjects();
  /* specs.trahern.c -- DA */
  assignTrahernObjects();
  /* specs.acheron.c */
  assignAcheronObjects();
  /* specs.avernus.c */
  assignAvernusObjects();

  /* Lower God Rooms */
  AddProcObj(5, mystra, "mystra");
  AddProcObj(8, miaxthing, "miaxthing");
  //  AddProcObj( 9, blackPlagueListing, "blackPlagueListing");
  //  AddProcObj( 9, blackPlagueCure, "blackPlagueCure");

  // ATD Blockers
  AddProcObj(891, item_block, "atd_block_north");
  AddProcObj(892, item_block, "atd_block_east");
  AddProcObj(893, item_block, "atd_block_south");
  AddProcObj(894, item_block, "atd_block_west");
  AddProcObj(895, item_block, "atd_block_up");
  AddProcObj(896, item_block, "atd_block_down");
  AddProcObj(897, item_loot_block, "atd_loot_block");


  /* Generic Objects */
  AddProcObj(716, shaman_totem, "shaman_totem"); /* wolf */
  AddProcObj(717, shaman_totem, "shaman_totem"); /* bear */
  AddProcObj(718, shaman_totem, "shaman_totem"); /* boar */
  AddProcObj(719, shaman_totem, "shaman_totem"); /* stag */
  AddProcObj(720, shaman_totem, "shaman_totem"); /* eagle */
  AddProcObj(721, shaman_totem, "shaman_totem"); /* crow */
  AddProcObj(722, shaman_totem, "shaman_totem"); /* lion */
  AddProcObj(723, shaman_totem, "shaman_totem"); /* tiger */
  AddProcObj(724, shaman_totem, "shaman_totem"); /* stallion */
  AddProcObj(725, shaman_totem, "shaman_totem"); /* snake */
  AddProcObj(732, shaman_totem, "shaman_totem"); /* worg */
  AddProcObj(733, shaman_totem, "shaman_totem"); /* vulture */
  AddProcObj(734, shaman_totem, "shaman_totem"); /* crocodile */
  AddProcObj(735, shaman_totem, "shaman_totem"); /* serpent */
  AddProcObj(736, shaman_totem, "shaman_totem"); /* scorpion */
  AddProcObj(737, shaman_totem, "shaman_totem"); /* hyena */
  AddProcObj(738, shaman_totem, "shaman_totem"); /* jackal */
  AddProcObj(739, shaman_totem, "shaman_totem"); /* spider */
  AddProcObj(740, shaman_totem, "shaman_totem"); /* viper */
  AddProcObj(741, shaman_totem, "shaman_totem"); /* bat */
  AddProcObj(742, shaman_totem, "shaman_totem"); /* raven */

  AddProcObj(874, wolfsbane_potion, "wolfsbane_potion"); /* lycan cure */
  AddProcObj(876, goodberry_cure, "goodberry_cure"); /* goodberry casts cure light */

#ifdef KINGDOM
  /* Procs on house doors */
  AddProcObj(HOUSE_OUTER_DOOR, house_door, "house_door");
  AddProcObj(HOUSE_INNER_DOOR, house_door, "house_door");
#endif

  /* Middle God Rooms */
  AddProcObj(751, portal_door, "portal_door");
  AddProcObj(753, portal_door, "portal_door");
  AddProcObj(883, portal_door, "portal_door");
  AddProcObj(882, dim_fold, "dim_fold");


  /* Main God Rooms */
  AddProcObj(9, altherogs_blackSunSword, "altherogs_blackSunSword");
  AddProcObj(13, velshorn, "velshorns_toy");
  AddProcObj(17, mask, "masks_toy");
  AddProcObj(21, cinandriel, "cin_toy");
  AddProcObj(6, shar, "shar_toy");
  AddProcObj(22, caytra, "caytra_toy");
  AddProcObj(23, kelly_mirror, "kelly_mirror");
  AddProcObj(25, erevan, "erevan_toy");
  AddProcObj(28, diinkarazan, "d2_toy");
  //AddProcObj(29, rambo, "rambo");
  AddProcObj(30, varon, "varon");
  AddProcObj(752, portal_door, "portal_door");
  AddProcObj(44, lloth, "lloth");
  AddProcObj(1025, kor_avatar, "No");
  AddProcObj(1019, nuclear_bomb, "nuclear_bomb");
  /*  AddProcObj(1227, hammer, "hammer"); */
  AddProcObj(45, lloth_avatar, "lloth_avatar");
  AddProcObj(24, burunga, "burunga");
  AddProcObj(47, magius_staff, "magius_staff");
  AddProcObj(50, azuth, "the_old_staff");

#if 0                           /* commented out till I can fix it -- DTS */
  AddProcObj(1233, cursed_mirror, "cursed_mirror");
#endif

  /* Artifact Assigns */
  AddProcObj(1050, Doombringer, "Doombringer");
  AddProcObj(1048, Kelrom, "Kelrom");
  AddProcObj(1009, Kelrarin, "Kelrarin");
  AddProcObj(1007, Kelrarin, "Kelrarin");
  AddProcObj(1043, OakenDefender, "Oaken Defender");
  AddProcObj(1044, Amaukekel, "Amaukekel");
  AddProcObj(5343, Gesen, "Gesen");
  AddProcObj(1042, Fade2, "Fade2");
  AddProcObj(19932, lathander_disc, "lathander_disc");
  AddProcObj(1045, NeverLooseItem, "");
  AddProcObj(1046, HornOfHenekar, "Horn of Henekar");
  /*  AddProcObj(1047, MinorHealback, "Minor Healback"); */
  AddProcObj(1234, banana, "Da Banana Mon!");
  AddProcObj(1235, banana, "Da Banana Mon!");
  AddProcObj(1057, kor_only_sword, "kor_only_sword");

  /* Item Cache */
  /*  AddProcObj(1200, ItemCache, "ItemCache"); */

  /* Waterdeep Harbor */
  AddProcObj(24405, sphere_lightning_weapon, "sphere_lightning_weapon");
  AddProcObj(14023, sphere_lightning_weapon, "sphere_lightning_weapon");
  AddProcObj(1005, frulghiem, "frulghiem");

#ifdef CONFIG_JAIL

  /* Northern Waterdeep */
  AddProcObj(3068, jailtally, "jailtally");

  /* Leuthilspar */
  AddProcObj(8100, jailtally, "jailtally");

  /* Luiren */
  AddProcObj(16038, jailtally, "jailtally");

#endif

#if 0 /* obsolete due to new item_switch auto-attach */
  /* Kobold Settlement */
  AddProcObj(1421, item_switch, "item_switch");
  AddProcObj(1425, item_switch, "item_switch");
  AddProcObj(1427, item_switch, "item_switch");
#endif

  /* The High Road */
  AddProcObj(1704, magic_pool, "magic_pool");

  /* Ships */
  AddProcObj(2300, ship, "ship");
  AddProcObj(2301, control_panel, "control_panel");
  AddProcObj(2302, ship, "ship");

  /* Southern Waterdeep */
  AddProcObj(2998, clock_tower, "clock_tower");

  /* The Underworld */
  AddProcObj(4505, hammer, "hammer");

  /* Alterian Wilderness */
  AddProcObj(4801, magic_pool, "magic_pool");
  AddProcObj(4802, magic_pool, "magic_pool");

  /* The Guilds of Waterdeep */
  AddProcObj(5515, waterdeep_portal, "waterdeep_portal");
  AddProcObj(5516, waterdeep_portal, "waterdeep_portal");

  /* Test addition of waterdeep necromancer proc Vaprak WAD */
  /*  AddProcObj(3020, corpse_kisser, "corpse_kisser");  */
  AddProcObj(3088, waterdeep_fountain_teleport, "waterdeep_fountain_teleport");
  AddProcObj(3088, blackPlagueReservoir, "blackPlagueReservoir");

  /* Western Realms */
  AddProcObj(5731, ship, "ship");
  AddProcObj(5732, control_panel, "control_panel");

  /* Bloodstone */
  AddProcObj(7147, bs_portal, "bs_portal");
  AddProcObj(7148, bs_portal, "bs_portal");
  AddProcObj(7149, bs_portal, "bs_portal");
  AddProcObj(7151, bs_child_sacrifice, "bs_child_sacrifice");
  AddProcObj(22491, bs_portal, "bs_portal");
  AddProcObj(22598, orcus_pwk_wand, "orcus_pwk_wand");
  AddProcObj(22599, orcus_mace, "orcus_mace");

  /* Leuthilspar */
  AddProcObj(8000, holy_weapon, "holy_weapon");
  /* AddProcObj(8000, holy_avenger, "holy_avenger"); by garg */
  AddProcObj(8112, elfgate, "elfgate");
  AddProcObj(8113, elfgate, "elfgate");

  /* Griffons Nest */
  AddProcObj(10672, gn_dragoncultrobes, "gn_DragonCultRobes");
  AddProcObj(13308, pahlurukroot, "PahlurukRoot");
#if 0
  AddProcRoom(10896, home_reset, "home_reset");
#endif
  /* Castle Hades */
  /*
    AddProcObj(9007, magic_pool, "magic_pool");
   */
  AddProcObj(9054, orb, "orb");

#if 0
  /* Elf Ship 1 */
  AddProcObj(11000, ship, "ship");
  AddProcObj(11001, control_panel, "control_panel");
  AddProcObj(11032, slot_machine, "slot_machine");
#endif

  /* Realms Master */
  AddProcObj(11100, ship, "ship");
  AddProcObj(11104, control_panel, "control_panel");

#if 0
  /* Elf Ship 2 */
  AddProcObj(11200, ship, "ship");
  AddProcObj(11201, control_panel, "control_panel");
#endif

  /* Silver Lady */
  AddProcObj(11300, ship, "ship");
  AddProcObj(11304, control_panel, "control_panel");

  AddProcObj(11400, ship, "ship");
  AddProcObj(11404, control_panel, "control_panel");

  /* Lava Tubes */
  AddProcObj(12000, crystal_spike, "crystal_spike");
  AddProcObj(12025, skeleton_key, "skeleton_key");
  AddProcObj(12027, automaton_lever, "automaton_lever");

  /* New Cavecity */
  AddProcObj(15116, torment, "torment");
  AddProcObj(15141, holy_weapon, "holy_weapon");

  /* Dark Aliance Guild Ship */
  AddProcObj(16901, ship, "ship");
  AddProcObj(16902, control_panel, "control_panel");
  AddProcMob(16900, receptionist, "receptionist");

  /* Astral Plane - Tiamat */
  AddProcObj(19710, magic_pool, "magic_pool");
  AddProcObj(19711, magic_pool, "magic_pool");
  AddProcObj(19715, magic_pool, "magic_pool");
  AddProcObj(19933, mielikki_scimitar, "mielikki_scimitar");
  AddProcObj(19730, New_Avernus, "New_Avernus");
  AddProcObj(1008, tiamat_stinger, "tiamat_stinger");
  AddProcObj(1010, tahlshara, "tahlshara");
  AddProcObj(19985, craine_serpent, "craine_serpent");
  AddProcObj(19988, tiamat_crescent_moon, "tiamat_crescent_moon");
  AddProcObj(19912, valhalla_scepter, "valhalla_scepter");
  //  AddProcObj(19998, sneak_boots, "sneak_boots");

  /* Brando Procs */
  AddProcObj(38025, windsong, "windsong");
  AddProcObj(38095, windsong, "windsong");
  AddProcObj(95776, longsword_rippling_flames, "longsword_rippling_flames");
  AddProcObj(89462, longsword_acid, "longsword_acid");
  AddProcObj(95876, longsword_black_flames, "longsword_black_flames");
  AddProcObj(95851, jeweled_fang, "jeweled_fang");


  /* Astral Plane - Main Grid */
  AddProcObj(19860, magic_pool, "magic_pool");
  AddProcObj(19861, magic_pool, "magic_pool");
  AddProcObj(19862, magic_pool, "magic_pool");
  AddProcObj(19863, magic_pool, "magic_pool");
  AddProcObj(19864, magic_pool, "magic_pool");
  AddProcObj(19900, githyanki, "githyanki");
  AddProcObj(19886, githyanki2, "githyanki2");

  /* Shaman's totem Quest */
  AddProcObj(21500, shaman_quest_teleport, "shaman_quest_tele");
  AddProcObj(21501, shaman_quest_teleport, "shaman_quest_tele");

  /* The Etheral Plane */
  AddProcObj(22708, magic_pool, "magic_pool");
  AddProcObj(22706, floating_pool, "floating_pool");
  AddProcObj(22707, floating_pool, "floating_pool");
  AddProcObj(22710, floating_pool, "floating_pool");
  AddProcObj(22711, floating_pool, "floating_pool");
  AddProcMob(22705, succubus, "succubus");
  AddProcMob(22715, succubus, "succubus");
  AddProcMob(22716, succubus, "succubus");

  /* Gargamel Procs  */
  AddProcObj(13307, proc_icydagger, "icydagger"); /* Icy Dagger in Lyrkwood */
  AddProcObj(95878, moonblade_starsong, "moonblade_starsong"); /* a slender elven moonblade, elf quest */
  AddProcObj(97117, proc_dirk_reversehit, "reversehit"); /* straightback dirk, IC rare */
  AddProcObj(14837, sf_glimmering_burst, "sf_glimmering_burst"); /* Glimmering Sword from SF */
  AddProcObj(35701, azuth_test, "azuth_test"); /* Azuth Test Proc by Garg -sword- */
  AddProcObj(35739, azuth_test, "azuth_test"); /* Azuth Test Proc by Garg -shield- */
  AddProcObj(35715, azuth_test, "azuth_test"); /* Azuth Test Proc by Garg -cloak- */
  /* AddProcObj(35703, garg_test, "garg_test");  Test Proc by Garg -for ambran- */

  /* Erlan Procs */
  /* AddProcObj(35726, erlan_spike_shield, "erlan_spike_shield");  Test Proc by Garg -for erlan- */
  /* AddProcObj(35714, erlan_trackless_hunting, "erlan_trackless_hunting");  Test Proc by Garg -for erlan- */
  /* AddProcObj(60421, erlan_winged_boots, "erlan_winged_boots");  Test Proc by Garg -for erlan- */
  /* AddProcObj(60462, erlan_food_bag, "erlan_food_bag");  Test Proc by Garg -for erlan- */

  /* Myth Drannor */
  AddProcObj(83238, md_darken_aura, "darken_burst"); /* Sword in MD */
  AddProcObj(83235, md_gleaming_burst, "gleaming_burst"); /* Sword in MD */
  AddProcObj(83055, md_enchanted_khanjari, "md_enchanted_khanjari");

  /* Plane of Fire One */
  AddProcObj(25018, doombringer, "doombringer");
  AddProcObj(25030, flamberge, "flamberge");

  /* Plane of Fire Three */
  AddProcObj(25404, magic_pool, "magic_pool");

  /* Swamp Part Two */
  AddProcObj(26014, nightbringer, "nightbringer");

  /* Moonshae Island I */
  AddProcObj(26233, cymric_hugh, "cymric_hugh");
  AddProcObj(26248, cymric_hugh, "cymric_hugh"); // Azuth added per Cyric
  AddProcObj(26260, moonshae_earthmother_staff, "moonshae_earthmother_staff");


#if 0 /* obsolete */
  /* Myrloch Vale */
  AddProcObj(26413, item_switch, "item_switch");
  AddProcObj(26414, item_switch, "item_switch");
  AddProcObj(26415, item_switch, "item_switch");
  AddProcObj(26416, item_switch, "item_switch");
#endif

  /* Gloomhaven */
  AddProcObj(34249, ship, "ship");
  AddProcObj(34250, control_panel, "control_panel");

  /* Ruins of Yath Oloth */
  AddProcObj(34840, kirinHorn, "kirinHorn");

  /* Skullport */
  AddProcObj(37300, ship, "ship");
  AddProcObj(37301, control_panel, "control_panel");
  AddProcObj(2500, ship, "ship");
  AddProcObj(2501, control_panel, "control_panel");

  /* Rogath */
  AddProcObj(40923, obj_drain, "obj_drain");

  // Blip!
  AddProcObj(41941, blip_portal, "blip_portal");

#if 0                           /* Not in game -- DTS 7/8/95 */
  AddProcObj(41001, magic_pool, "magic_pool");
  AddProcObj(41002, magic_pool, "magic_pool");
#endif

#if 0                           /* Not in game -- DTS 7/8/95 */
  /* Shadamehr Keep */
  AddProcObj(41203, unholy_weapon, "unholy_weapon");
#endif

  /* Thunderhead Peak */
  AddProcObj(46991, thp_necroChild, "thp_necroChild");

  // Scardale
  AddProcObj(63000, scardale_distro, "scardale_distro");

  /* Myranthea */
#if 0
  AddProcObj(70013, myranth_key, "myranth_key");
  AddProcObj(70014, myranth_key, "myranth_key");
  AddProcObj(70015, myranth_key, "myranth_key");
  AddProcObj(70039, myranth_key, "myranth_key");
  AddProcObj(70204, lobos_jacket, "lobos_jacket");
  AddProcObj(70293, soulrender, "soulrender");
  AddProcObj(70978, blackness_sword, "blackness_sword");
#endif

  /* Bastion of Mon Creef */
  AddProcObj(70101, acheronRoamingPortal, "acheronRoamingPortal");
  AddProcObj(70102, acheronRoamingPortal, "acheronRoamingPortal");
  AddProcObj(70103, acheronRoamingPortal, "acheronRoamingPortal");
  AddProcObj(70104, acheronRoamingPortal, "acheronRoamingPortal");
  AddProcObj(70105, acheronRoamingPortal, "acheronRoamingPortal");

  /* Dwarven Mining Settlement */
  AddProcObj(80034, rockcrusher, "rockcrusher");
  AddProcObj(80038, rockcrusher, "rockcrusher");

  /* Newhaven */
  AddProcObj(51110, nh_blueplume, "nh_blueplume");
  AddProcObj(51207, nh_writhingash, "nh_writhingash");

  /* Hive */
  AddProcObj(43741, hive_gythka, "hive_gythka");

  /* Smoke */
  AddProcObj(57003, smoke_stun_shield, "smoke_stun_shield");

  /* Fire Giant Village */
  AddProcObj(80547, swordOfFireGiants, "swordOfFireGiants");

  /* Menden-on-the-Deep */
  AddProcObj(88825, menden_figurine, "menden_figurine");
  AddProcObj(88830, llyms_altar, "llyms_altar");

  /* The Fog-Enshrouded Woods */
  AddProcObj(90004, fw_ruby_monocle, "fw_ruby_monocle");
#if 0
  AddProcObj(90010, item_switch, "item_switch");
#endif

  /* Mirar (Chionthar) Ferry */
  AddProcObj(90390, control_panel, "control_panel");
  AddProcObj(90391, ship, "ship");

  /* Captains Fancy */
  AddProcObj(46610, ship, "ship");
  AddProcObj(46611, control_panel, "control_panel");

  AddProcObj(91248, blackPlagueCure, "blackPlagueCure");

  AddProcObj(91305, sword_wickedly_barbed, "sword_wickedly_barbed");

  /* Nizari */
  AddProcObj(40135, shadow_dagger, "shadow_dagger");

  /* Tanthorian */
  AddProcObj(40909, longsword_tanthorian, "longsword_tanthorian");
  AddProcObj(40928, flaming_tanthorian, "flaming_tanthorian");

  /* Cloud Realm of Arlurrium */
  AddProcObj(57236, haste_sleeves, "haste_sleeves");

  /* Manscorps */
  AddProcObj(43723, basilisk_leggings, "basilisk_leggings");
  AddProcObj(44019, basilisk_snakes, "basilisk_snakes");
  AddProcObj(44015, basilisk_drop, "basilisk_drop");

  /* Muspelheim */
  AddProcObj(59068, muspel_chieftain_open, "muspel_chieftain_open");
  AddProcObj(58923, muspel_bec_de_corbin, "muspel_bec_de_corbin");
  AddProcObj(58954, muspel_dragon_lance, "muspel_dragon_lance");
  AddProcObj(58915, muspel_spider_dagger, "muspel_spider_dagger");
  AddProcObj(58920, muspel_crystal_scimitar, "muspel_crystal_scimitar");
  AddProcObj(58961, muspel_duergar_battlehammer, "muspel_duergar_battlehammer");
  AddProcObj(58634, muspel_recurve_bow, "muspel_recurve_bow");
  AddProcObj(58636, muspel_dagger_whispers, "muspel_dagger_whispers");
  AddProcObj(58802, murlynds_spoon, "murlynds_spoon");
  AddProcObj(58803, thorn_shield, "thorn_shield");

  /* Halruaa */
  AddProcObj(53266, halruaa_enchanterstaff, "halruaa_enchanterstaff");
  AddProcObj(53263, halruaa_illusionstaff, "halruaa_illusionstaff");
  AddProcObj(53271, halruaa_necrostaff, "halruaa_necrostaff");
  AddProcObj(53259, halruaa_invokerstaff, "halruaa_invokerstaff");
  AddProcObj(53250, halruaa_elemstaff, "halruaa_elemstaff");
  AddProcObj(53289, halruaa_magebane, "halruaa_magebane");
  AddProcObj(53290, halruaa_magebane, "halruaa_magebane");
  AddProcObj(53291, halruaa_magebane, "halruaa_magebane");
  AddProcObj(53292, halruaa_magebane, "halruaa_magebane");
  AddProcObj(53243, halruaa_dwarven_hammer, "halruaa_dwarven_hammer");

  /* Havenport */
  AddProcObj(98451, ship, "ship");
  AddProcObj(98450, control_panel, "control_panel");
  /* added by Moradin 08/17/01 */
  AddProcObj(98330, glowing_crimson_dagger, "glowing_crimson_dagger");
  /* For Uthgar's zone in future... remmed out for now */
#if 0
  AddProcObj(89756, magetower_sunblade, "magetower_sunblade");
#endif

#if 0
  /* Instruments */
  AddProcObj(19959, dragonhide_drum, "dragonhide_drum");
#endif

  /* specs.Tarrasque.c */
  assignTarrasqueObjects();

  /* specs.cemetary.c */
  AddProcObj(55300, cemetary_disruption, "cemetary_disruption");
  AddProcObj(55301, cemetary_skeletal_hand, "cemetary_skeletal_hand");
  AddProcObj(55349, cemetary_black_blade, "cemetary_black_blade");
  AddProcObj(55350, cemetary_gleaming_blade, "cemetary_gleaming_blade");
  AddProcObj(55318, cemetary_instrument_rub, "cemetary_instrument_rub");
  AddProcObj(55328, cemetary_instrument_rub, "cemetary_instrument_rub");
  AddProcObj(55329, cemetary_instrument_rub, "cemetary_instrument_rub");
  AddProcObj(55330, cemetary_instrument_rub, "cemetary_instrument_rub");
  AddProcObj(55331, cemetary_instrument_rub, "cemetary_instrument_rub");
  AddProcObj(55332, cemetary_instrument_rub, "cemetary_instrument_rub");
  AddProcObj(55333, cemetary_instrument_rub, "cemetary_instrument_rub");
  AddProcObj(55325, cemetary_cloakMeteors, "cemetary_cloakMeteors");
  AddProcObj(55361, cemetary_lightsaber, "cemetary_lightsaber");

  /* specs.darkhold.c */
  assignDarkholdObjects();

  // 92604 some machine I found for temp use
  AddProcObj(899, slot_machine_new, "slot_machine_new");

  /* specs.bhaal.c */
  assignBhaalObjects();

  /* specs.seeliecourt.c */
  assignSeelieCourtObjects();

  /* specs.tiamat.c */
  assignTiamatObjs();

}

/* assign special procedures to rooms */


void assign_rooms(void) {
  /* specs.undermountain.c */
  assignUndermountainRooms();
  /* specs.scornubel.c */
  assignScornubelRooms();
  /* specs.hyssk.c -- Shevarash */
  assignHysskRooms();
  /* specs.calimshan.c  -- diirinka */
  assignCalimshanRooms();
  /* specs.balders.c  -- JAB */
  assignBaldersGateRooms();
  /* specs.zhentilkeep.c */
  AssignZKRooms();
  /* specs.griffons.c   -- Diirinka */
  assignGriffonsNestRooms();
  /* specs.dobluth.c  -- CRM */
  assignDobluthKyorRooms();

  /* specs.avernus.c -- DA */
  assignAvernusRooms();

  /* The Cage */
  AddProcRoom(1, cage_command_block, "cage_command_block");

  /* Newbie Load Room */
  AddProcRoom(101, newbieLoadRoom, "newbieLoadRoom");

  /* Distro Proc for newbie zone */
  AddProcRoom(23296, autoDistributor, "autoDistributor");

  /* Lurkwood */
  AddProcRoom(13396, home_reset, "home_reset");

  /* Calimport */
  AddProcRoom(46130, home_reset, "home_reset");
  AddProcRoom(46294, home_reset, "home_reset");
  AddProcRoom(46296, home_reset, "home_reset");

  /* Distro rooms */
  AddProcRoom(7909, home_reset, "home_reset");
  AddProcRoom(7910, home_reset, "home_reset");
  AddProcRoom(7911, home_reset, "home_reset");
  AddProcRoom(7912, home_reset, "home_reset");
  AddProcRoom(7913, home_reset, "home_reset");
  AddProcRoom(7914, home_reset, "home_reset");
  AddProcRoom(7915, home_reset, "home_reset");
  AddProcRoom(7916, home_reset, "home_reset");
  AddProcRoom(7917, home_reset, "home_reset");
  AddProcRoom(7918, home_reset, "home_reset");
  AddProcRoom(7919, home_reset, "home_reset");
  AddProcRoom(7920, home_reset, "home_reset");
  AddProcRoom(7921, home_reset, "home_reset");
  AddProcRoom(7922, home_reset, "home_reset");
  AddProcRoom(7923, home_reset, "home_reset");
  AddProcRoom(7929, home_reset, "home_reset");
  AddProcRoom(7930, home_reset, "home_reset");
  AddProcRoom(7931, home_reset, "home_reset");
  AddProcRoom(7932, home_reset, "home_reset");
  AddProcRoom(7933, home_reset, "home_reset");
  AddProcRoom(7934, home_reset, "home_reset");
  AddProcRoom(7935, home_reset, "home_reset");
  AddProcRoom(7936, home_reset, "home_reset");
  AddProcRoom(7937, home_reset, "home_reset");
  AddProcRoom(7938, home_reset, "home_reset");
  AddProcRoom(7950, home_reset, "home_reset");
  AddProcRoom(7951, home_reset, "home_reset");
  AddProcRoom(7952, home_reset, "home_reset");
  AddProcRoom(7953, home_reset, "home_reset");
  AddProcRoom(7954, home_reset, "home_reset");
  AddProcRoom(7955, home_reset, "home_reset");
  AddProcRoom(7956, home_reset, "home_reset");
  AddProcRoom(201013, home_reset, "home_reset");
  AddProcRoom(201014, home_reset, "home_reset");
  AddProcRoom(201015, home_reset, "home_reset");
  AddProcRoom(201016, home_reset, "home_reset");
  AddProcRoom(201017, home_reset, "home_reset");
  AddProcRoom(201018, home_reset, "home_reset");

  /* Myth Drannor */
  AddProcRoom(83003, home_reset, "home_reset");

  /* Distro Procs for grid zones */
  AddProcRoom(826396, autoDistributor, "autoDistributor");
  AddProcRoom(826196, autoDistributor, "autoDistributor");
  AddProcRoom(825996, autoDistributor, "autoDistributor");
  AddProcRoom(825796, autoDistributor, "autoDistributor");
  AddProcRoom(825596, autoDistributor, "autoDistributor");
  AddProcRoom(825396, autoDistributor, "autoDistributor");
  AddProcRoom(825196, autoDistributor, "autoDistributor");
  AddProcRoom(824996, autoDistributor, "autoDistributor");
  AddProcRoom(824796, autoDistributor, "autoDistributor");
  AddProcRoom(824596, autoDistributor, "autoDistributor");
  AddProcRoom(824396, autoDistributor, "autoDistributor");
  AddProcRoom(824196, autoDistributor, "autoDistributor");
  AddProcRoom(823996, autoDistributor, "autoDistributor");
  AddProcRoom(823796, autoDistributor, "autoDistributor");
  AddProcRoom(823596, autoDistributor, "autoDistributor");
  AddProcRoom(823396, autoDistributor, "autoDistributor");
  AddProcRoom(823196, autoDistributor, "autoDistributor");
  AddProcRoom(822996, autoDistributor, "autoDistributor");
  AddProcRoom(822796, autoDistributor, "autoDistributor");
  AddProcRoom(822596, autoDistributor, "autoDistributor");
  AddProcRoom(822396, autoDistributor, "autoDistributor");
  AddProcRoom(822196, autoDistributor, "autoDistributor");
  AddProcRoom(821996, autoDistributor, "autoDistributor");
  AddProcRoom(821796, autoDistributor, "autoDistributor");
  AddProcRoom(821596, autoDistributor, "autoDistributor");
  AddProcRoom(821396, autoDistributor, "autoDistributor");
  AddProcRoom(821196, autoDistributor, "autoDistributor");
  AddProcRoom(820996, autoDistributor, "autoDistributor");
  AddProcRoom(820796, autoDistributor, "autoDistributor");
  AddProcRoom(820596, autoDistributor, "autoDistributor");
  AddProcRoom(820396, autoDistributor, "autoDistributor");
  AddProcRoom(820196, autoDistributor, "autoDistributor");


  /* Ship Rooms */
  AddProcRoom(2300, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(2301, ship_exit_room, "ship_exit_room");
  AddProcRoom(2302, ship_exit_room, "ship_exit_room");

  /* Ixarkon (Mindflayer Hometown */
  AddProcRoom(96549, pet_shops, "pet_shops");

  /* Havenport */
  AddProcRoom(98453, ship_exit_room, "ship_exit_room");

  /* Kraken */
  AddProcRoom(13648, pet_shops, "pet_shops");

  /* Southern Waterdeep */
  AddProcRoom(2906, dump, "dump");
  AddProcRoom(2956, waterdeep_guild_twelve, "waterdeep_guild_twelve");

  /* Northern Waterdeep */
  AddProcRoom(3044, waterdeep_guild_eight, "waterdeep_guild_eight");
  AddProcRoom(3061, waterdeep_guild_ten, "waterdeep_guild_ten");
  AddProcRoom(3073, waterdeep_guild_nine, "waterdeep_guild_nine");
  AddProcRoom(3170, pet_shops, "pet_shops");
  AddProcRoom(3180, pet_shops, "pet_shops"); /* WD stable */

  /* Central Waterdeep */
  AddProcRoom(3289, waterdeep_guild_eleven, "waterdeep_guild_eleven");

  /* The Guilds of Waterdeep */
  AddProcRoom(5505, waterdeep_guild_one, "waterdeep_guild_one");
  AddProcRoom(5512, waterdeep_guild_two, "waterdeep_guild_two");
  AddProcRoom(5524, waterdeep_guild_three, "waterdeep_guild_three");
  AddProcRoom(5537, waterdeep_guild_four, "waterdeep_guild_four");
  AddProcRoom(5544, waterdeep_guild_five, "waterdeep_guild_five");
  AddProcRoom(5568, waterdeep_guild_six, "waterdeep_guild_six");
  AddProcRoom(5581, waterdeep_guild_seven, "waterdeep_guild_seven");
  AddProcRoom(5583, guild_classtype_mage, "guild_classtype_mage");

  /* Western Realms */
  AddProcRoom(5952, pet_shops, "pet_shops"); /* Western Rlm stables */
  AddProcRoom(5998, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(5999, ship_exit_room, "ship_exit_room");

  /* Mithril Hall */
  AddProcRoom(6532, guild, "guild");
  AddProcRoom(6550, guild, "guild");
  AddProcRoom(6575, guild, "guild");
  AddProcRoom(6733, guild, "guild");
  AddProcRoom(6744, guild, "guild");
  AddProcRoom(6745, guild, "guild");
  AddProcRoom(6752, guild, "guild");

#if 0
  /* Dragonnia Part Two */
  AddProcRoom(6841, room_of_sanctum, "room_of_sanctum");
#endif

  /* Bloodstone */
  /*  AddProcRoom(7205, bs_dispersement_room, "bs_dispersement_room");
    AddProcRoom(7656, bs_dispersement_room, "bs_dispersement_room");
   */
  /*
  AddProcRoom(22417, bs_whirlpool, "bs_whirlpool");
  AddProcRoom(22494, bs_altar, "bs_altar");
  AddProcRoom(7296, pet_shops, "pet_shops");
  AddProcRoom(7298, pet_shops, "pet_shops");
  AddProcRoom(7678, guild, "guild");
  AddProcRoom(7822, guild, "guild");
  AddProcRoom(7841, guild, "guild");
  AddProcRoom(7850, guild_classtype_mage, "guild_classtype_mage");
  AddProcRoom(7869, guild, "guild");
  AddProcRoom(7887, guild_classtype_mage, "guild_classtype_mage");
  AddProcRoom(7899, guild_lich, "guild_lich");
  AddProcRoom(22451, pet_shops, "pet_shops");
  AddProcRoom(22507, guild, "guild");
  AddProcRoom(22515, pet_shops, "pet_shops");
   */

  /* Leuthilspar */
  AddProcRoom(8010, pet_shops, "pet_shops");
  AddProcRoom(8017, guild, "guild");
  AddProcRoom(8074, guild, "guild");
  AddProcRoom(8117, guild, "guild");
  AddProcRoom(8140, guild, "guild");
  AddProcRoom(8203, guild, "guild");
  AddProcRoom(8211, dump, "dump");
  AddProcRoom(8310, guild, "guild");
  AddProcRoom(8316, guild, "guild");
  AddProcRoom(8321, guild, "guild");
  AddProcRoom(8323, pet_shops, "pet_shops");

#if 0
  /* Elf Ship 1 */
  AddProcRoom(11000, ship_exit_room, "ship_exit_room");
  AddProcRoom(11001, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11002, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11003, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11004, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11005, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11006, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11007, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11008, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11009, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11010, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11011, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11012, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11013, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11014, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11015, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11019, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11020, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11025, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11031, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11032, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11033, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11034, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11035, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11036, ship_look_out_room, "ship_look_out_room");
#endif

  /* Scavy Whore */
  AddProcRoom(16901, ship_exit_room, "ship_exit_room");
  AddProcRoom(16902, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(16903, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(16904, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(16905, ship_look_out_room, "ship_look_out_room");

  /* Realms Master */
  AddProcRoom(11100, ship_exit_room, "ship_exit_room");
  AddProcRoom(11101, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11102, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11103, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11104, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11105, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11108, ship_look_out_room, "ship_look_out_room");
#if 0
  AddProcRoom(11115, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11116, ship_look_out_room, "ship_look_out_room");
#endif
  AddProcRoom(11117, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11118, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11119, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11120, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11121, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11123, ship_look_out_room, "ship_look_out_room");

#if 0
  /* Elf Ship 2 */
  AddProcRoom(11200, ship_exit_room, "ship_exit_room");
  AddProcRoom(11201, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11202, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11203, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11204, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11205, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11206, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11207, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11208, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11209, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11210, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11211, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11212, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11213, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11214, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11215, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11219, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11220, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11225, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11231, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11232, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11236, ship_look_out_room, "ship_look_out_room");
#endif

  /* Silver Lady */
  AddProcRoom(11300, ship_exit_room, "ship_exit_room");
  AddProcRoom(11301, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11302, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11303, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11304, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11305, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11308, ship_look_out_room, "ship_look_out_room");
#if 0
  AddProcRoom(11315, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11316, ship_look_out_room, "ship_look_out_room");
#endif
  AddProcRoom(11317, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11318, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11319, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11320, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11321, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(11323, ship_look_out_room, "ship_look_out_room");

#if 0                           /* not in game - DTS 7/8/95 */
  AddProcRoom(11401, ship_exit_room, "ship_exit_room");
  AddProcRoom(11421, ship_look_out_room, "ship_look_out_room");
#endif

  /* Ghore */
  AddProcRoom(11610, guild, "guild");
  AddProcRoom(11613, guild, "guild");
  AddProcRoom(11626, guild, "guild");
  AddProcRoom(11638, guild, "guild");
#if 0                           /* Not in game -- needs fixing by areas type -- DTS 7/8/95 */
  AddProcRoom(11656, pet_shops, "pet_shops");
  AddProcRoom(11902, pet_shops, "pet_shops");
  AddProcRoom(11903, pet_shops, "pet_shops");
#endif
  AddProcRoom(11923, dump, "dump");

  /* Lava Tubes One */
  AddProcRoom(12158, automaton_trapdoor, "automaton_trapdoor");

  /* Faang */
  AddProcRoom(15317, guild, "guild");
  AddProcRoom(15339, guild, "guild");
  AddProcRoom(15513, guild, "guild");
  AddProcRoom(15663, guild, "guild");

  /* Luiren */
  AddProcRoom(16000, guild, "guild");
  AddProcRoom(16044, guild, "guild");
  AddProcRoom(16061, guild, "guild");
  AddProcRoom(16162, dump, "dump");
  AddProcRoom(16252, pet_shops, "pet_shops");
  AddProcRoom(16355, guild, "guild");
  AddProcRoom(16421, guild, "guild");

  /* Viperstongue Outpost */
  AddProcRoom(20958, guild_classtype_warrior, "Warrior guild");
  AddProcRoom(20963, guild_classtype_mage, "Mage guild");
  AddProcRoom(20956, guild_classtype_thief, "Rogue guild");
  AddProcRoom(20965, guild_classtype_cleric, "Cleric guild");
  AddProcRoom(20957, guild_bard, "Bard guild");

  /* Bloodtusk */
  AddProcRoom(94953, guild_classtype_warrior, "Warrior Guild");
  AddProcRoom(94933, guild_classtype_cleric, "Cleric Guild");
  AddProcRoom(94948, guild_classtype_thief, "Rogue Guild");
  AddProcRoom(94961, guild_battlechanter, "Battlechanter Guild");

  /* Gloomhaven */

  AddProcRoom(34491, pet_shops, "pet_shops");
  AddProcRoom(34435, guild, "guild");
  AddProcRoom(34452, guild, "guild");
  AddProcRoom(34433, guild, "guild");
  AddProcRoom(34427, guild, "guild");
  AddProcRoom(34418, guild, "guild");
  AddProcRoom(34421, guild, "guild");
  AddProcRoom(34371, guild, "guild");
  AddProcRoom(34372, guild, "guild");
  AddProcRoom(34774, ship_exit_room, "ship_exit_room");
  AddProcRoom(34775, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(34494, guild_classtype_mage, "guild_classtype_mage");
  AddProcRoom(34495, guild_classtype_mage, "guild_classtype_mage");

  /* Skullport */
  AddProcRoom(2574, ship_exit_room, "ship_exit_room");
  AddProcRoom(2575, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(2501, ship_exit_room, "ship_exit_room");
  AddProcRoom(2500, ship_look_out_room, "ship_look_out_room");

  /* Thunderhead Peak (guilds for MH) */
  AddProcRoom(46951, guild, "guild");
  AddProcRoom(46953, guild, "guild");
  AddProcRoom(46990, necro_passing_glyph, "necro_passing_glyph");

  /* Ashrumite */
  AddProcRoom(66080, dump, "dump");
  AddProcRoom(66131, guild, "guild");
  AddProcRoom(66106, guild, "guild");
  AddProcRoom(66103, guild, "guild");
  AddProcRoom(66107, guild, "guild");
  AddProcRoom(66088, guild, "guild");
  AddProcRoom(66142, guild, "guild");
  AddProcRoom(66116, pet_shops, "pet_shops");

  /* Myranthea */
#if 0
  AddProcRoom(70870, myranth_maze, "myranth_maze");
  AddProcRoom(70875, myranth_maze, "myranth_maze");
  AddProcRoom(70880, myranth_maze, "myranth_maze");
  AddProcRoom(70885, myranth_maze, "myranth_maze");
  AddProcRoom(70890, myranth_maze, "myranth_maze");
  AddProcRoom(70895, myranth_maze, "myranth_maze");
  AddProcRoom(70900, myranth_maze, "myranth_maze");
  AddProcRoom(70905, myranth_maze, "myranth_maze");
  AddProcRoom(70910, myranth_maze, "myranth_maze");
  AddProcRoom(70915, myranth_maze, "myranth_maze");
  AddProcRoom(70920, myranth_maze, "myranth_maze");
  AddProcRoom(70925, myranth_maze, "myranth_maze");
  AddProcRoom(70930, myranth_maze, "myranth_maze");
  AddProcRoom(70945, myranth_maze, "myranth_maze");
  AddProcRoom(70950, myranth_maze, "myranth_maze");
  AddProcRoom(70955, myranth_maze, "myranth_maze");
  AddProcRoom(70960, myranth_maze, "myranth_maze");
  AddProcRoom(70965, myranth_maze, "myranth_maze");
  AddProcRoom(70970, myranth_maze, "myranth_maze");
  AddProcRoom(70975, myranth_maze, "myranth_maze");
  AddProcRoom(70980, myranth_maze, "myranth_maze");
  AddProcRoom(70985, myranth_maze, "myranth_maze");
  AddProcRoom(70990, myranth_maze, "myranth_maze");
  AddProcRoom(71400, myranth_maze, "myranth_maze");
  AddProcRoom(71405, myranth_maze, "myranth_maze");
  AddProcRoom(71410, myranth_maze, "myranth_maze");
  AddProcRoom(71415, myranth_maze, "myranth_maze");
  AddProcRoom(71420, myranth_maze, "myranth_maze");
  AddProcRoom(71425, myranth_maze, "myranth_maze");
  AddProcRoom(71655, myranth_maze, "myranth_maze");
  AddProcRoom(71660, myranth_maze, "myranth_maze");
  AddProcRoom(71665, myranth_maze, "myranth_maze");
  AddProcRoom(71670, myranth_maze, "myranth_maze");
  AddProcRoom(71675, myranth_maze, "myranth_maze");
  AddProcRoom(71680, myranth_maze, "myranth_maze");
#endif /* 0 */

  /* Fog Enshrouded Woods */
  AddProcRoom(90107, fw_warning_room, "fw_warning_room");
  AddProcRoom(90112, fw_warning_room, "fw_warning_room");
  AddProcRoom(90114, fw_warning_room, "fw_warning_room");

  /* Mirar (Chionthar) Ferry */
  AddProcRoom(90395, ship_exit_room, "ship_exit_room");
  AddProcRoom(90396, ship_look_out_room, "ship_look_out_room");

  /* Captains Fury */
  AddProcRoom(46610, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(46612, ship_look_out_room, "ship_look_out_room");
  AddProcRoom(46611, ship_exit_room, "ship_exit_room");

  /* Muspelheim */
  AddProcRoom(59059, muspel_chimney_pour, "muspel_chimney_pour");
  AddProcRoom(58991, muspel_chimney_pour, "muspel_chimney_pour");
  AddProcRoom(58635, muspel_ice_river, "muspel_lava_river");
  AddProcRoom(58636, muspel_ice_river, "muspel_lava_river");
  AddProcRoom(58641, muspel_ice_river, "muspel_lava_river");
  AddProcRoom(58642, muspel_ice_river, "muspel_lava_river");
  AddProcRoom(58643, muspel_ice_river, "muspel_lava_river");
  AddProcRoom(58644, muspel_ice_river, "muspel_lava_river");
  AddProcRoom(58648, muspel_ice_river, "muspel_lava_river");
  AddProcRoom(58649, muspel_ice_river, "muspel_lava_river");
  AddProcRoom(58650, muspel_ice_river, "muspel_lava_river");
  AddProcRoom(58651, muspel_ice_river, "muspel_lava_river");
  AddProcRoom(58652, muspel_ice_river, "muspel_lava_river");
  AddProcRoom(58655, muspel_ice_river, "muspel_lava_river");
  AddProcRoom(58656, muspel_ice_river, "muspel_lava_river");
  AddProcRoom(58657, muspel_ice_river, "muspel_lava_river");
  AddProcRoom(58658, muspel_ice_river, "muspel_lava_river");
  AddProcRoom(58659, muspel_ice_river, "muspel_lava_river");
  AddProcRoom(58665, muspel_ice_river, "muspel_lava_river");
  AddProcRoom(58666, muspel_ice_river, "muspel_lava_river");
  AddProcRoom(58667, muspel_ice_river, "muspel_lava_river");
  AddProcRoom(58647, muspel_ice_river, "muspel_lava_river");

  /* Ixarkon */
  AddProcRoom(96503, guild, "guild");

  /* Arnd'ir */
  AddProcRoom(50473, pet_shops, "pet_shops");

  // Azuth's test zone
  AddProcRoom(51400, weight_trigger, "weight_trigger");

  /* Pirate Isle */
  AddProcObj(20075, longsword_slenderelven, "longsword_slenderelven");

}


#ifdef NEW_GROUP_PROC

void assign_groups(void) {

}
#endif
