/*************************************************************************** */
/*                                                                           */
/*    File actinf.c - Special Module, part of Outcast III MUD code base.     */
/*    Usage: Informative commands, display commands, and the like.           */
/*                                                                           */
/*      COPYRIGHT NOTICE: This code for Outcast is Copyright(c) 2000, by     */
/*    <PERSON><PERSON>her <PERSON>. This code is NOT freeware or shareware,    */
/*   and may NOT be used in any form without expressly written permission    */
/*                             from the author.                              */
/*        K. <PERSON> may be reached via <NAME_EMAIL>      */
/*             and <EMAIL> <NAME_EMAIL>                  */
/*                                                                           */
/*************************************************************************** */

#include <ctype.h>
#include <stdio.h>
#include <string.h>
#include <time.h>

#include "comm.h"
#include "db.h"
#include "events.h"
#include "interp.h"
#include "language.h"
#ifdef NEWJUSTICE
#include "newjustice.h"
#endif
#ifdef OLDJUSTICE
#include "justice.h"
#endif
#include "prototypes.h"
#include "skillrec.h"
#include "spells.h"
#include "race_class.h"
#include "utils.h"
#include "weather.h"
#include "mccp.h"

#include "utilset.h" /* some new set stuff under test -Azuth */

/* external variables */

/* extern struct timeval time_deficit; */
extern P_assoc Assoc_list;
extern FILE *help_fl;
extern FILE *whelp_fl;
extern FILE *info_fl;
extern P_char NPC_list;
extern P_char PC_list;
extern P_group group_list;
extern P_desc descriptor_list;
extern P_index mob_index;
extern P_index obj_index;
// extern P_index grp_index; dead code -Azuth */
extern P_obj object_list;
extern P_room world;
extern byte create_locked;
extern byte locked;
extern const char *affected_bits[];
extern const char *class_types[];
extern const char *color_liquid[];
extern const char *command[];
extern const char *connected_types[];
extern const char *dirs[];
extern const char *event_names[];
extern const char *fullness[];
extern const char *language_names[];
extern const char *month_name[];
extern const char *player_bits[];
extern const char *player_law_flags[];
extern const char *player_prompt[];
extern const char *weekdays[];
extern const char *where[];
extern const int boot_time;
extern const int exp_table[TOTALCLASS][52];
extern const int rev_dir[];
extern const struct stat_data stat_factor[];
extern const struct trait_data racial_traits[];
extern int avail_descs;
extern int help_array[27][2];
extern int whelp_array[27][2];
extern int info_array[27][2];
extern int max_descs;
extern int max_users_playing;
extern int number_of_quests;
extern int number_of_shops;
extern int num_of_socials;
extern int pulse;
extern int top_of_helpt;
extern int top_of_whelpt;
extern int top_of_infot;
extern int top_of_mobt;
extern int top_of_objt;
extern int top_of_grpt;
extern int top_of_world;
extern int top_of_zone_table;
extern int used_descs;
extern int num_of_socials;
extern int num_of_social_periodics;
extern int num_of_social_triggers;
extern int num_of_social_lists;
extern int num_of_social_path;
extern int num_of_social_timed;
extern int num_of_social_errors;
extern struct agi_app_type agi_app[];
extern struct command_info cmd_info[];
extern struct dex_app_type dex_app[];
extern struct help_index_element *help_index;
extern struct help_index_element *whelp_index;
extern struct info_index_element *info_index;
extern struct int_app_type int_app[];
extern struct str_app_type str_app[];
extern struct time_info_data time_info;
extern struct zone_data *zone_table;
extern struct socials_head *socials_index[SOCIALS_INDEX_SIZE];
extern u_int event_counter[];
extern unsigned long rescheduleCounter;
extern int cc_thac0_dexMod;
extern char *skillTypeHeader[];

extern const char *season_patterns[8];
extern const char *wind_patterns[7];
extern const char *wind_dir[4];
extern const char *precip_patterns[9];
extern const char *temp_patterns[11];

typedef struct IPstruct {
  char hostname[MAX_HOSTNAME + 1];
  char username[11];
  char name[17];
  bool ld;
  int socket;
  int idle;
  int in_room;
  byte state;
  int ip_count;
} PlrCh;

typedef struct IPshare_struct {
  char name[17];
  char email[128];
  int groupno;
} IPShare;

void ipshare_list(P_char ch);
int ipshare_search(P_char ch, char *arg);
void ipshare_remove(P_char ch, char *arg);
void ipshare_addchar(P_char ch, char *arg1, char *arg2);
void ipshare_addperson(P_char ch, char *arg1, char *arg2, char *arg3);
UtilSet ipshare_load(UtilSet set);
void ipshare_write(UtilSet set);
static int sort_by_name_comparef(IPShare **i1, IPShare **i2);
static int sort_by_group_comparef(IPShare **i1, IPShare **i2);
static int sort_by_group_email_comparef(IPShare **i1, IPShare **i2);
static int sort_by_email_comparef(IPShare **i1, IPShare **i2);

// static globals
static UtilSet ipshare = NULL;
static int groupno = 1;

/*====================================================================*/
/*
 *    Constant arrays for displaying information to the character
 *      SAM 5-94
 */
/*====================================================================*/

const char *room_dimension_names[ROOM_DIMENSION_NAMES] ={
  "Miniscule",
  "Tiny",
  "Small",
  "Mid-sized",
  "Large",
  "Very large",
  "Huge",
  "Enormous",
  "Open Area"
};

const char *race_names[PC_RACES] ={
  "Unknown",
  "&+CHuman&n",
  "&+BBarbarian&n",
  "&+mDrow Elf&n",
  "&+cGrey Elf&n",
  "&+YDwarf&n",
  "&+rDuergar&n",
  "&+yHalfling&n",
  "&+RGnome&n",
  "&+bOgre&n",
  "&+gTroll&n",
  "&+GHalf-Elf&n",
  "&+MIllithid&n",
  "&+LYuan-Ti&N",
  "&+LLich&N",
  "&+gMyconid&N",
  "&+gOrc&N"
};

const char *class_names[TOTALCLASS] ={
  "Unknown",
  "&+bWarrior     ",
  "&+GRanger      ",
  "&+bBerserker   ",
  "&+WPaladin     ",
  "&+LAnti&N&+r-&+LPaladin",
  "&+cCleric      ",
  "&nMonk        ",
  "&+gDruid       ",
  "&+CShaman      ",
  "&+MSorcerer    ",
  "&+LNecromancer ",
  "&+YConjurer    ",
  "&+yThief       ",
  "&+rAssassin    ",
  "&+yMercenary   ",
  "&=wbBard&N        ",
  "&=rwPsionicist&N  ",
  "&=mlLich&N        ",
  "&+BEnchanter&N ",
  "&+RInvoker&N   ",
  "&+mIllusionist&N  ",
  "&+gBattlechanter&N  ",
  "&+rRogue       ",
  "&+yElementalist",
  "&=rlDire Raider&N"
};

const char *class_abbrev[TOTALCLASS] ={
  "---",
  "&+bWar&n",
  "&+GRan&n",
  "&+bBer&n",
  "&+WPal&n",
  "&+LA&N&+r-&N&+LP&n",
  "&+cCle&n",
  "&nMon",
  "&+gDru&n",
  "&+CSha&n",
  "&+MSor&n",
  "&+LNec&n",
  "&+YCon&n",
  "&+yThi&n",
  "&+rAss&n",
  "&+yMer&n",
  "&=wbBar&n",
  "&=rwPsi&n",
  "&=mlLic&n",
  "&+BEnc&N",
  "&+RInv&N",
  "&+mIll&N",
  "&+gCtr&N",
  "&+rRog",
  "&+yEle",
  "&=rlDir",
};

const char *stat_names1[18] ={
  "&+rbad &n", /* 0 */
  "&+rbad &n", /* 1-9 */
  "&+rbad &n", /* 10-15 */
  "&+rbad &n", /* 16-21 */
  "&+rbad &n", /* 22-27 */
  "&+rbad &n", /* 28-33 */
  "&+rbad &n", /* 34-39 */
  "&+rbad &n", /* 40-45 */
  "&+rbad &n", /* 46-50 */
  "&+yfair&n", /* 51-55 */
  "&+yfair&n", /* 56-61 */
  "&+yfair&n", /* 62-67 */
  "&+yfair&n", /* 68-73 */
  "&+ggood&n", /* 74-79 */
  "&+ggood&n", /* 80-85 */
  "&+ggood&n", /* 86-91 */
  "&+ggood&n", /* 92-100 */
  "&+ggood&n" /* 100+ */
};

const char *stat_names2[18] ={
  "lame    ", /* 0 */
  "lame    ", /* 1-9 */
  "poor    ", /* 10-15 */
  "poor    ", /* 16-21 */
  "below average", /* 22-27 */
  "below average", /* 28-33 */
  "average ", /* 34-39 */
  "average ", /* 40-45 */
  "average ", /* 46-50 */
  "average ", /* 51-55 */
  "average ", /* 56-61 */
  "average ", /* 62-67 */
  "above average", /* 68-73 */
  "above average", /* 74-79 */
  "good    ", /* 80-85 */
  "good    ", /* 86-91 */
  "excellent", /* 92-100 */
  "excellent", /* 100+ */
};

const char *stat_names3[8] ={
  "&+Lbad&N    ",
  "&+Rmundane&N",
  "&+Maverage&N",
  "&+Yfair&N   ",
  "&+Ggood&N   ",
  "&+Bmighty&N ",
  "&+Cheroic&N ",
  "&+Wperfect&N"
};

const char *stat_outofrange[2] ={
  "sucky",
  "mighty"
};

const char *align_names[9] ={
  "extremely evil",
  "evil",
  "evil, leaning towards neutrality",
  "neutral, leaning towards evil",
  "neutral",
  "neutral, leaning towards good",
  "good, leaning towards neutrality",
  "good",
  "extremely good"
};

const char *ac_names[19] ={
  "At least you aren't naked", /* 90 to 99 */
  "You do not feel safe from a dull butter knife", /* 80 to 89 */
  "Your armor is pathetic", /* 70 to 79 */
  "Your armor is awful", /* 60 to 69 */
  "Your armor is bad", /* 50 to 59 */
  "Your armor is slightly better than bad", /* 40 to 49 */
  "Your armor is far worse than average", /* 30 to 39 */
  "Your armor is below average", /* 20 to 29 */
  "Your armor is slightly below average", /* 10 to 19 */
  "Your armor is average", /* -9 to 9 */
  "Your armor is slightly above average", /* -19 to -10 */
  "Your armor is above average", /* -29 to -20 */
  "Your armor is well above average", /* -39 to -30 */
  "Your armor is good", /* -49 to -40 */
  "Your armor is really good", /* -59 to -50 */
  "You are ready for a serious battle", /* -69 to -60 */
  "Your armor is awesome", /* -79 to -70 */
  "Your armor really rocks man", /* -89 to -80 */
  "Your armor kicks ass!" /* -99 to -90 */
};

const char *ac_outofrange[2] ={
  "You have no armor class, you are buck naked! *brrrr*",
  "Your armor is equivalent to that of an M1A1 Abrahams - Main battle tank"
};

const char *hitdam_roll_names[3] ={
  "bad", /* -4 to 0 */
  "fair", /* +1 to +5 */
  "good" /* +6 to +10 */
};

const char *hitdam_roll_outofrange[2] ={
  "lame",
  "awesome"
};

const char *save_names[3] ={
  "bad", /* 3 to 7 */
  "fair", /* 2 to -2 */
  "good" /* -3 to -7 */
};

const char *save_outofrange[2] ={
  "lame",
  "awesome"
};

const char *exp_names[12] ={
  "You have a long LONG way to go!",
  "You have just begun the trek",
  "You still have a very long way to go",
  "You still have a good distance to go",
  "You are nearing the half-way point",
  "You are very close to the half-way point",
  "You are at the half-way point",
  "You have just passed the half-way point",
  "You are well past the half-way point",
  "You are three quarters the way there",
  "You are almost there",
  "You should level anytime now!"
};

const char *load_names[15] ={
  "What load?",
  "Unburdened",
  "No Sweat",
  "Not a problem",
  "Paltry",
  "Very Light",
  "Light",
  "Moderate",
  "Heavy",
  "Very Heavy",
  "Extremely Heavy",
  "Backbreaking",
  "Crushing",
  "Atlas would be Proud",
  "Immobilizing"
};

/* some defines for 'who' code */

#define ALL_CHAR 0
#define SPEC     1
#define NONSPEC  2

#define MAX_PLAYERS     512

struct who_flags {
  char ANONYMOUS;
  int CLASS;
  int RACE;
  char RACE_O;
  char CLASS_O;
  char OUTLAW;
  char ZONE;
  char SORT;
  char LEADERS;
  char GROUPED;
  char GODS_ONLY;
  int short_list;
  int low;
  int high;
  char PKILLERS;
  char HELPERS;
  char UNGROUPED;
  int GOODRACE;
  int EVILRACE;
  int INGROUP;
  char LFG;
};

#define NUM_FIELDS 45 // virtually useless since we allow 14 args for who and no more
int cmd_nr[NUM_FIELDS];
P_char who_list[MAX_WHO_PLAYERS], who_gods[MAX_WHO_PLAYERS];

int make_list(P_char, struct who_flags);
void set_who_flags(P_char, int, struct who_flags *);
char *god_title(P_char tch, bool long_form);

void do_spell_damage(P_char ch);

const char *race_to_string(P_char ch) {
  int val;

  if (ch == NULL)
    return (NULL);

  val = ch->player.race;
  if ((val <= 0) || (val >= PC_RACES))
    return (race_names[0]);
  else
    return (race_names[val]);
}

const char *class_to_string(P_char ch) {
  int val;

  if (ch == NULL)
    return (NULL);

  val = ch->player.class;
  if ((val <= 0) || (val >= TOTALCLASS))
    return (class_names[0]);
  else
    return (class_names[val]);

}

const char *stat_to_string1(int val) {
  int si = STAT_INDEX(val);

  if (si < 0)
    return (stat_names1[0]);
  else if (si > 17)
    return (stat_names1[17]);
  else
    return (stat_names1[si]);
}

const char *stat_to_string2(int val) {
  int si = STAT_INDEX(val);

  if (si < 0)
    return (stat_names2[0]);
  else if (si > 17)
    return (stat_names2[17]);
  else
    return (stat_names2[si]);
}

const char *stat_to_string3(int val) {
  int dsi = DISPLAY_STAT_INDEX(val);

  if (dsi < 0)
    return (stat_names3[0]);
  if (dsi > 7)
    return (stat_names3[7]);

  return (stat_names3[dsi]);
}

int DISPLAY_STAT_INDEX(int val) {
  int temp = 0;

  if (val < 51)
    temp = 0;
  else if (val < 66)
    temp = 1;
  else if (val < 76)
    temp = 2;
  else if (val < 82)
    temp = 3;
  else if (val < 88)
    temp = 4;
  else if (val < 94)
    temp = 5;
  else if (val < 100)
    temp = 6;
  else
    temp = 7;

  return temp;
}

const char *align_to_string(int val) {
  if (val < -783)
    return (align_names[0]);
  else if ((val > -784) && (val < -566))
    return (align_names[1]);
  else if ((val > -567) && (val < -349))
    return (align_names[2]);
  else if ((val > -350) && (val < -117))
    return (align_names[3]);
  else if ((val > -118) && (val < 117))
    return (align_names[4]);
  else if ((val > 116) && (val < 350))
    return (align_names[5]);
  else if ((val > 349) && (val < 567))
    return (align_names[6]);
  else if ((val > 566) && (val < 783))
    return (align_names[7]);
  else if (val > 782)
    return (align_names[8]);
  else {
    logit(LOG_STATUS, "align_to_string: ATT BUG illegal value %d", val);
    return ("Att BUG, please report this immediately!");
  }
}

const char *ac_to_string(int val) {
  int temp, temp2;

  if (val >= 100)
    return (ac_outofrange[0]);
  else if (val <= -100)
    return (ac_outofrange[1]);

  temp = -val / 10;
  temp2 = temp + 9;

  if ((temp2 < 0) || (temp2 > 18)) {
    logit(LOG_STATUS, "ATT bug found, ac_to_string, temp = %d", temp);
    return ("BUGGED, report this now!");
  }
  return (ac_names[temp2]);
}

const char *hitdam_roll_to_string(int val) {
  if (val < -4)
    return (hitdam_roll_outofrange[0]);
  else if (val > 10)
    return (hitdam_roll_outofrange[1]);

  if (val < 1)
    return (hitdam_roll_names[0]);
  else if (val > 5)
    return (hitdam_roll_names[2]);
  else
    return (hitdam_roll_names[1]);
}

const char *save_to_string(int val) {
  if (val > 7)
    return (save_outofrange[0]);
  else if (val < -7)
    return (save_outofrange[1]);

  if (val > 2)
    return (save_names[0]);
  else if (val < -2)
    return (save_names[2]);
  else
    return (save_names[1]);
}

const char *exp_to_string(P_char ch) {
  double temp1, temp2, temp3;
  int curr_exp, exp_curr_level, exp_next_level, result, chclass, level;

  chclass = GET_CLASS(ch);
  level = GET_LEVEL(ch);

  if ((chclass <= 0) || (chclass >= TOTALCLASS))
    return ("Unknown");

  if ((level <= 0) || (level >= MAXLVLMORTAL))
    return ("Unknown");

  curr_exp = GET_EXP(ch);
  exp_curr_level = exp_table[chclass][level];
  exp_next_level = exp_table[chclass][level + 1];

  if (curr_exp < exp_curr_level) {
    result = -1;
  } else {
    temp1 = (double) (curr_exp - exp_curr_level);
    temp2 = (double) (exp_next_level - exp_curr_level);
    temp3 = temp1 / temp2 * 100.0;
    result = (int) temp3;
  }

  if (result < 0)
    return (exp_names[0]);
  else if (result < 11)
    return (exp_names[1]);
  else if (result < 21)
    return (exp_names[2]);
  else if (result < 31)
    return (exp_names[3]);
  else if (result < 41)
    return (exp_names[4]);
  else if (result < 49)
    return (exp_names[5]);
  else if (result < 53)
    return (exp_names[6]);
  else if (result < 61)
    return (exp_names[7]);
  else if (result < 71)
    return (exp_names[8]);
  else if (result < 81)
    return (exp_names[9]);
  else if (result < 91)
    return (exp_names[10]);
  else
    return (exp_names[11]);
}

/* heavy bell curve, middle one has 33%, middle five have 79% of total */

const char *load_to_string(P_char ch) {
  int percent = CAN_CARRY_W(ch);

  if (percent <= 0)
    percent = 1;

  percent = (int) ((IS_CARRYING_W(ch) * 100) / percent);

  if (percent <= 0)
    return (load_names[0]);
  else if (percent <= 1)
    return (load_names[1]);
  else if (percent <= 3)
    return (load_names[2]);
  else if (percent <= 6)
    return (load_names[3]);
  else if (percent <= 10)
    return (load_names[4]);
  else if (percent <= 19)
    return (load_names[5]);
  else if (percent <= 33)
    return (load_names[6]);
  else if (percent <= 66)
    return (load_names[7]);
  else if (percent <= 80)
    return (load_names[8]);
  else if (percent <= 89)
    return (load_names[9]);
  else if (percent <= 93)
    return (load_names[10]);
  else if (percent <= 96)
    return (load_names[11]);
  else if (percent <= 98)
    return (load_names[12]);
  else if (percent < 100)
    return (load_names[13]);

  return (load_names[14]);

}

/* Procedures related to 'look' */

void argument_split_2(char *argument, char *first_arg, char *second_arg) {
  int look_at, begin;

  begin = 0;

  if (strlen(argument) >= MAX_INPUT_LENGTH) {
    logit(LOG_SYS, "too long arg in argument_split_2.");
    *(first_arg) = '\0';
    *(second_arg) = '\0';
    return;
  }
  /* Find first non blank */
  for (; *(argument + begin) == ' '; begin++);

  /* Find length of first word */
  for (look_at = 0; *(argument + begin + look_at) > ' '; look_at++)
    /* Make all letters lower case, AND copy them to first_arg */
    *(first_arg + look_at) = LOWER(*(argument + begin + look_at));
  *(first_arg + look_at) = '\0';
  begin += look_at;

  /* Find first non blank */
  for (; *(argument + begin) == ' '; begin++);

  /* Find length of second word */
  for (look_at = 0; *(argument + begin + look_at) > ' '; look_at++)
    /* Make all letters lower case, AND copy them to second_arg */
    *(second_arg + look_at) = LOWER(*(argument + begin + look_at));
  *(second_arg + look_at) = '\0';
  begin += look_at;
}

P_obj get_object_in_equip_vis(P_char ch, char *arg, int *j) {
  for ((*j) = 0; (*j) < MAX_WEAR; (*j)++)
    if (ch->equipment[(*j)])
      if (CAN_SEE_OBJ(ch, ch->equipment[(*j)]))
        if (isname(arg, ch->equipment[(*j)]->name))
          return (ch->equipment[(*j)]);

  return (0);
}

P_obj get_object_in_equip(P_char ch, char *arg, int *j) {
  for ((*j) = 0; (*j) < MAX_WEAR; (*j)++)
    if (ch->equipment[(*j)])
      if (isname(arg, ch->equipment[(*j)]->name))
        return (ch->equipment[(*j)]);

  return (0);
}

char *find_ex_description(char *word, struct extra_descr_data *list) {
  struct extra_descr_data *i;

  for (i = list; i; i = i->next)
    if (isname(word, i->keyword))
      return (i->description);

  return (0);
}

char *show_obj_to_char(P_obj object, P_char ch, int mode, int print) {
  //bool found;
  static char buf[MAX_STRING_LENGTH];
  P_obj poison = NULL;

  if (IS_TRUSTED(ch) && IS_CSET(ch->only.pc->pcact, PLR_VNUM))
    sprintf(buf, "[&+B%5d&N] ", (object->R_num >= 0 ? obj_index[object->R_num].virtual : -1));
  else
    buf[0] = 0;

  if ((mode == 0) && object->description)
    strcat(buf, object->description);
  else if (object->short_description && (mode == BOUNDED(1, mode, 4)))
    strcat(buf, object->short_description);
  else if (mode == 5) {
    if (object->type == ITEM_NOTE) {
      if (object->action_description) {
        strcat(buf, "There is something written upon it:\n\n");
        strcat(buf, object->action_description);
        page_string(ch->desc, buf, 1);
      } else
        send_to_char("It's blank.\n", ch);
      return NULL;
    } else if ((object->type != ITEM_DRINKCON)) {
      strcat(buf, "You see nothing special...");
    } else {
      strcat(buf, "It looks like a drink container.");
    }
  }
  if (mode != 3) {
    //found = FALSE;
    if (IS_OBJ_STAT(object, ITEM_INVISIBLE)) {
      strcat(buf, " (&+Linvis&n)");
      //found = TRUE;
    }
    /* check for poison */
    if ((object->type == ITEM_WEAPON) || (object->type == ITEM_FIREWEAPON))
      for (poison = object->contains; poison; poison = poison->next_content)
        if (poison->type == ITEM_POISON)
          break;
    if (poison) {
      strcat(buf, " (&+Gpoisoned&n)");
      //found = TRUE;
    }
    if (IS_OBJ_STAT(object, ITEM_SECRET)) {
      strcat(buf, " (&+Lsecret&n)");
      //found = TRUE;
    }
    if (IS_OBJ_STAT(object, ITEM_MAGIC) && (IS_TRUSTED(ch) || IS_AFFECTED(ch, AFF_DETECT_MAGIC))) {
      strcat(buf, " (&+bmagic&n)");
      //found = TRUE;
    }
    if (IS_OBJ_STAT(object, ITEM_GLOW)) {
      strcat(buf, " (&+Mglowing&n)");
      //found = TRUE;
    }

    if ((object->type == ITEM_CORPSE) && object->value[5]) {
      strcat(buf, " &+L(&N&+mglowing&N&+L)&N");
      //found = TRUE;
    }

    if (IS_OBJ_STAT(object, ITEM_LIT) || ((object->type == ITEM_LIGHT) && (object->value[2] == -1))) {
      strcat(buf, " (&+Willuminating&n)");
      //found = TRUE;
    }
  }

  strcat(buf, "\n");
  if (print)
    page_string(ch->desc, buf, 1);

  return buf;
}

void list_obj_to_char(P_obj list, P_char ch, int mode, int show) {
  P_obj i;
  bool found;
  char buf[MAX_STRING_LENGTH], *s, buf2[20];
  int count;

  buf[0] = 0;
  count = 0;
  found = FALSE;
  for (i = list; i; i = i->next_content) {
    if (CAN_SEE_OBJ(ch, i)) {
      s = show_obj_to_char(i, ch, mode, 0);
      if (!str_cmp(s, buf)) {
        count++;
      } else {
        if (count) {
          sprintf(buf2, "[%d] ", count + 1);
          send_to_char(buf2, ch);
          count = 0;
        }
        if (buf[0])
          send_to_char(buf, ch);
        if (s)
          strcpy(buf, s);
        else
          logit(LOG_DEBUG, "Fubar strcpy in list_obj_to_char.");
      }
      found = TRUE;
    }
  }
  if (found) {
    if (count) {
      sprintf(buf2, "[%d] ", count + 1);
      send_to_char(buf2, ch);
    }
    if (buf[0])
      send_to_char(buf, ch);
  }
  if ((!found) && (show))
    send_to_char("Nothing.\n", ch);
}

void show_char_to_char(P_char i, P_char ch, int mode) {
  char buffer[MAX_STRING_LENGTH], buf2[MAX_STRING_LENGTH];
  int j, found, percent;
  P_obj tmp_obj;
  int wear_order[] ={24, 6, 19, 21, 22, 20, 3, 4, 5, 12, 23, 13, 10, 11, 14, 15, 9, 1, 2, 16, 17, 18, 7, 8, 25, -1};

  *buffer = '\0';
  *buf2 = '\0';

  if (IS_NPC(i) && !IS_TRUSTED(ch) && !i->player.long_descr)
    return; /* Undetectable mobs - SKB 22 Nov 1995 */

  if (mode == 0) {
    if (!CAN_SEE(ch, i) && !IS_TRUSTED(i) && !IS_UNDEAD(i) &&
            (GET_RACE(i) != RACE_POSSESSED) && (GET_RACE(i) != RACE_GOLEM)) {
      if (IS_AFFECTED(ch, AFF_SENSE_LIFE))
        send_to_char("&+LYou sense a hidden life form in the room.\n", ch);
      return;
    }
    if (IS_AFFECTED(i, AFF_INVISIBLE) && !IS_AFFECTED(ch, AFF_WRAITHFORM))
      strcpy(buffer, "*");

    if (IS_PC(i) || (i->specials.position != i->only.npc->default_pos) || IS_FIGHTING(i)) {
      /* A player char or a mobile w/o long descr, or not in default pos. */
      if (IS_PC(i) && !IS_DISGUISE_NPC(i)) {
        if (i->in_room < 0) {
          logit(LOG_DEBUG, "%s->in_room < 0 in show_char_to_char.", GET_NAME(i));
          return;
        }
        sprintf(buffer + strlen(buffer), "%s ", GET_NAME(i) ? GET_NAME(i) : "&=LRBogus char!&n");

        if (GET_TITLE(i) &&
                ((IS_PC(ch) && !IS_CSET(ch->only.pc->pcact, PLR_BRIEF)) || IS_TRUSTED(i) ||
                (IS_PC(ch) && IS_CSET(ch->only.pc->pcact, PLR_SHOWTITS))))
          strcat(buffer, GET_TITLE(i));

        if (i->desc && i->desc->olc)
          strcat(buffer, " (&+bO&+mL&+rC&N)");

        if (IS_CSET(i->only.pc->pcact, PLR_AFK))
          strcat(buffer, " (&+RAFK&N)");

        if (IS_CSET(i->only.pc->pcact, PLR_ROLEPLAY))
          strcat(buffer, " (&+WRP&N)");

        // RP Toggle stuff
        if (IS_CSET(ch->only.pc->pcact, PLR_G_QUEST) || GET_LEVEL(ch) >= 57) {
          if (IS_CSET(i->only.pc->pcact, PLR_B_RP))
            strcat(buffer, " (&+RBRP&N)");
          else if (IS_CSET(i->only.pc->pcact, PLR_P_RP))
            strcat(buffer, " (&N&+wPRP&N)");
          else if (IS_CSET(i->only.pc->pcact, PLR_G_RP))
            strcat(buffer, " (&N&+GGRP&N)");
          else if (IS_CSET(i->only.pc->pcact, PLR_E_RP))
            strcat(buffer, " (&N&+CERP&N)");
        }

        if (IS_CSET(i->only.pc->pcact, PLR_LFG))
          strcat(buffer, " (&+GLFG&N)");

        if (GET_LEVEL(i) < 51)
          sprintf(buffer + strlen(buffer), " (%s)", race_names[(int) GET_RACE(i)]);

        if (IS_AFFECTED(i, AFF_HIDE))
          strcat(buffer, "(&+Lhiding&n)");
        if (!i->desc)
          strcat(buffer, " %%");
      }        /* Players disguised as an NPC - CRM */
      else if (IS_PC(i) && IS_DISGUISE_NPC(i)) {
        strcpy(buf2, GET_DISGUISE_NAME(i));
        CAP(buf2);
        strcat(buffer, buf2);
      } else {
        if (i->player.short_descr) {
          strcpy(buf2, i->player.short_descr);
          CAP(buf2);
          strcat(buffer, buf2);
        }
      }

      switch (GET_STAT(i)) {
        case STAT_DEAD:
          strcat(buffer, " is lying here, quite dead");
          break;
        case STAT_DYING:
          strcat(buffer, " is lying here, mortally wounded");
          break;
        case STAT_INCAP:
          strcat(buffer, " is lying here, incapacitated");
          break;
        case STAT_SLEEPING:
          switch (GET_POS(i)) {
            case POS_PRONE:
              strcat(buffer, " is stretched out, sound asleep");
              break;
            case POS_SITTING:
              strcat(buffer, " has nodded off, sitting");
              break;
            case POS_KNEELING:
              strcat(buffer, " is asleep, kneeling");
              break;
            case POS_STANDING:
              strcat(buffer, " stands, apparently asleep");
              break;
          }
          if (IS_AFFECTED(i, AFF_LEVITATE))
            strcat(buffer, ", inches above the surface");
          else if (IS_AFFECTED(i, AFF_FLY))
            strcat(buffer, " in mid-air");
          break;
        case STAT_RESTING:
          switch (GET_POS(i)) {
            case POS_PRONE:
              strcat(buffer, " is sprawled out, resting");
              break;
            case POS_SITTING:
              strcat(buffer, " sits resting");
              break;
            case POS_KNEELING:
              strcat(buffer, " squats comfortably");
              break;
            case POS_STANDING:
              strcat(buffer, " stands at ease");
              break;
          }
          if (IS_AFFECTED(i, AFF_LEVITATE))
            strcat(buffer, ", inches above the surface");
          else if (IS_AFFECTED(i, AFF_FLY))
            strcat(buffer, " in mid-air");
          break;
        case STAT_NORMAL:
          switch (GET_POS(i)) {
            case POS_PRONE:
              strcat(buffer, " is lying");
              break;
            case POS_SITTING:
              strcat(buffer, " sits");
              break;
            case POS_KNEELING:
              strcat(buffer, " crouches");
              break;
            case POS_STANDING:
              if (IS_AFFECTED(i, AFF_ELEMENTAL_FIRE))
                strcat(buffer, " blazes");
              else if (IS_AFFECTED(i, AFF_ELEMENTAL_WATER))
                strcat(buffer, " oozes");
              else if (IS_AFFECTED(i, AFF_ELEMENTAL_AIR))
                strcat(buffer, " hovers");
              else if (IS_AFFECTED(i, AFF_ELEMENTAL_EARTH))
                strcat(buffer, " hulks");
              else
                strcat(buffer, " stands");
              break;
          }
          if (IS_AFFECTED(i, AFF_LEVITATE))
            strcat(buffer, ", inches above the surface");
          else if (IS_AFFECTED(i, AFF_FLY))
            strcat(buffer, " in mid-air");
          strcat(buffer, " here");
          break;
      }
      if (IS_RIDING(i)) {
        strcat(buffer, ", riding ");
        if (GET_MOUNT(i) == ch)
          strcat(buffer, " YOU");
        else {
          if (i->in_room == GET_MOUNT(i)->in_room) {
            if (CAN_SEE(ch, GET_MOUNT(i))) {
              strcat(buffer, C_NAME(GET_MOUNT(i)));
            } else {
              /* invis/hiding mount */
              strcat(buffer, "on thin air");
            }
          } else
            strcat(buffer, "someone who has already left");
        }
      }
      if (i && IS_PC(i) && i->only.pc->writing)
        strcat(buffer, ", writing a message");

      if (!i->specials.fighting) {
        strcat(buffer, ".");
      } else {
        strcat(buffer, ", fighting ");
        if (i->specials.fighting == ch)
          strcat(buffer, " YOU!");
        else {
          if (i->in_room == i->specials.fighting->in_room) {
            if (IS_NPC(i->specials.fighting)) {
              strcat(buffer, i->specials.fighting->player.short_descr);
            } else {
              strcat(buffer, GET_NAME(i->specials.fighting));
            }
            strcat(buffer, ".");
          } else
            strcat(buffer, "someone who has already left.");
        }
      }

      if (IS_PC(i) && IS_DISGUISE(i)) {
        if (IS_AFFECTED(ch, AFF_DETECT_EVIL) && IS_EVILRACE(GET_DISGUISE_RACE(i)))
          strcat(buffer, "(&+rRed Aura&N)");
        if (IS_AFFECTED(ch, AFF_DETECT_GOOD) && IS_GOODRACE(GET_DISGUISE_RACE(i)))
          strcat(buffer, "(&+YGold Aura&n)");
        if (IS_TRUSTED(ch)) {
          strcat(buffer, "(&+mDisguise&N) (");
          strcat(buffer, GET_NAME(i));
          strcat(buffer, ")");
        }

      } else {
        if (IS_AFFECTED(ch, AFF_DETECT_EVIL) && IS_EVIL(i))
          strcat(buffer, "(&+rRed Aura&n)");
        if (IS_AFFECTED(ch, AFF_DETECT_GOOD) && IS_GOOD(i))
          strcat(buffer, "(&+YGold Aura&n)");
      }

      if (IS_TRUSTED(ch) && IS_CSET(ch->only.pc->pcact, PLR_VNUM) && IS_NPC(i))
        sprintf(buffer + strlen(buffer), " [&+Y%d&n]", mob_index[i->nr].virtual);

      strcat(buffer, "\n");

      send_to_char(buffer, ch);
    } else { /* npc with long */
      if (i->player.long_descr) {
        found = FALSE;
        strcpy(buf2, i->player.long_descr);
        CAP(buf2);
        strcat(buffer, buf2);
        /* remove spurious characters from end of npc description. */
        j = strlen(buffer) - 1;
        if (!str_cmp((buffer + j - 1), "&n")) {
          found = TRUE;
          buffer[j - 1] = 0;
          j -= 2;
        }
        while ((j >= 0) &&
                ((buffer[j] == '\n') || (buffer[j] == '\r') || (buffer[j] == '~') || (buffer[j] == ' '))) {
          buffer[j] = 0;
          j--;
        }
        if (found)
          strcat(buffer, "&n");
      }
      *buf2 = '\0';

      if (IS_EVIL(i) && IS_AFFECTED(ch, AFF_DETECT_EVIL))
        strcat(buf2, "(&+rRed Aura&n)");

      if (IS_GOOD(i) && IS_AFFECTED(ch, AFF_DETECT_GOOD))
        strcat(buf2, "(&+YGold Aura&n)");

      if (IS_AFFECTED(i, AFF_HIDE))
        strcat(buf2, "(&+Lhiding&n)");

      if (IS_TRUSTED(ch) && IS_CSET(ch->only.pc->pcact, PLR_VNUM))
        sprintf(buf2 + strlen(buf2), "[&+Y%d&n] ", mob_index[i->nr].virtual);

      if (strlen(buf2))
        strcat(buffer, buf2);

      strcat(buffer, "\n");

      send_to_char(buffer, ch);
    }

  } else if (mode == 1) {

    *buffer = '\0';

    if (i->player.description && !IS_DISGUISE_NPC(i))
      send_to_char(i->player.description, ch);
    else
      act("You see nothing special about $m.", FALSE, i, 0, ch, TO_VICT);

    /* Show a character to another */

    if (GET_MAX_HIT(i) > 0)
      percent = (100 * GET_HIT(i)) / GET_MAX_HIT(i);
    else
      percent = -1; /* How could MAX_HIT be < 1?? */

    if (IS_NPC(i)) {
      if (i->player.short_descr) {
        strcat(buffer, i->player.short_descr);
        CAP(buffer);
      } else {
        strcat(buffer, "Mob with no short desc");
      }
    } else if (IS_PC(i) && IS_DISGUISE_NPC(i)) {
      if (GET_DISGUISE_NAME(i))
        strcat(buffer, GET_DISGUISE_NAME(i));
      else
        strcat(buffer, GET_NAME(i));
    } else {
      if (!IS_TRUSTED(i)) {
        sprintf(buffer + strlen(buffer), "%s appears to be %s and",
                GET_NAME1(i) ? GET_NAME1(i) : "&=LRBogus char!&n",
                GET_RACE1(i) ? race_names[(int) GET_RACE1(i)] : "&=LRBogus race!&n");
      } else {
        strcat(buffer, GET_NAME(i));
      }
    }

    if (percent >= 100)
      strcat(buffer, " &+gis in excellent condition.\n");
    else if (percent >= 90)
      strcat(buffer, " &+yhas a few scratches.\n");
    else if (percent >= 75)
      strcat(buffer, " &+Yhas some small wounds and bruises.\n");
    else if (percent >= 50)
      strcat(buffer, " &+Mhas quite a few wounds.\n");
    else if (percent >= 30)
      strcat(buffer, " &+mhas some big nasty wounds and scratches.\n");
    else if (percent >= 15)
      strcat(buffer, " &+Rlooks pretty hurt.\n");
    else if (percent >= 0)
      strcat(buffer, " &+ris in awful condition.\n");
    else
      strcat(buffer, " &+ris bleeding awfully from big wounds, and is close to death!\n");

    if (affected_by_spell(i, SPELL_ELEMENTAL_FIRE)) {
      sprintf(buf2, "&+r%s is consumed with &+Relemental fire&N!\n", HSSH(i));
      CAP(buf2);
      strcat(buffer, buf2);
    }

    if (affected_by_spell(i, SPELL_ELEMENTAL_WATER)) {
      sprintf(buf2, "&+b%s is consumed with &+Belemental water&N!\n", HSSH(i));
      CAP(buf2);
      strcat(buffer, buf2);
    }

    if (affected_by_spell(i, SPELL_ELEMENTAL_EARTH)) {
      sprintf(buf2, "&+L%s is consumed with &N&+yelemental earth&N!\n", HSSH(i));
      CAP(buf2);
      strcat(buffer, buf2);
    }

    if (affected_by_spell(i, SPELL_ELEMENTAL_AIR)) {
      sprintf(buf2, "&+c%s is consumed with &+Welemental air&N!\n", HSSH(i));
      CAP(buf2);
      strcat(buffer, buf2);
    }


    if (affected_by_spell(i, SPELL_ELEMENTAL_WARD)) {
      sprintf(buf2, "&+L%s is protected by a &N&+mcrackling purple&+L elemental ward!&N\n", HSSH(i));
      CAP(buf2);
      strcat(buffer, buf2);
    }

    if (affected_by_spell(i, SPELL_MIRROR_IMAGE)) {
      sprintf(buf2, "&+m%s is surrounded by a cluster of &+Wmirror images&N&+m.\n", HSSH(i));
      CAP(buf2);
      strcat(buffer, buf2);
    }
    if (affected_by_spell(i, SPELL_BARKSKIN)) {
      sprintf(buf2, "%s skin has barklike texture..\n", HSHR(i));
      CAP(buf2);
      strcat(buffer, buf2);
    }
    if (affected_by_spell(i, SPELL_MAGE_FLAME)) {
      sprintf(buf2, "A flickering blue flame floats over %s's head.\n", HSHR(i));
      CAP(buf2);
      strcat(buffer, buf2);
    }
    if (affected_by_spell(i, SPELL_MISSILE_SHIELD)) {
      sprintf(buf2, "&+b%s is surrounded by an opaque shield of magic.\n", HSSH(i));
      CAP(buf2);
      strcat(buffer, buf2);
    }
    if (affected_by_spell(i, SPELL_STONE_SKIN)) {
      sprintf(buf2, "&+L%s body seems to be made of stone!\n", HSHR(i));
      CAP(buf2);
      strcat(buffer, buf2);
    }
    if (affected_by_spell(i, SPELL_DRAGONSCALES)) {
      sprintf(buf2, "&+Y%s body is covered in dragon scales!\n", HSHR(i));
      CAP(buf2);
      strcat(buffer, buf2);
    }
    if (affected_by_spell(i, SPELL_BLUR)) {
      sprintf(buf2, "&+B%s form is blurred and difficult to make out!\n", HSHR(i));
      CAP(buf2);
      strcat(buffer, buf2);
    }
    if (affected_by_spell(i, SPELL_FAERIE_FIRE)) {
      sprintf(buf2, "&+r%s is surrounded by a dancing outline of purplish flames!\n", HSSH(i));
      CAP(buf2);
      strcat(buffer, buf2);
    }
    if (affected_by_spell(i, SPELL_SCARLET_OUTLINE)) {
      sprintf(buf2, "&+r%s is outlined with raging scarlet flames!\n", HSSH(i));
      CAP(buf2);
      strcat(buffer, buf2);
    }
    if (affected_by_spell(i, SPELL_DISPLACEMENT)) {
      sprintf(buf2, "&+m%s form is displaced, and difficult to track!\n", HSHR(i));
      CAP(buf2);
      strcat(buffer, buf2);
    }
    if (IS_AFFECTED(i, AFF_FIRESHIELD)) {
      if (affected_by_spell(i, SPELL_COLDSHIELD))
        sprintf(buf2, "&+B%s is encased in killing ice!\n", HSSH(i));
      else if (affected_by_spell(i, SPELL_UNHOLY_AURA))
        sprintf(buf2, "&+L%s radiates an awesome, unholy power!\n", HSSH(i));
      else if (affected_by_spell(i, SPELL_VAMPIRIC_CURSE))
        sprintf(buf2, "&+W%s is surrounded by a &+Ybright&+W, glowing aura!\n", HSSH(i));
      else
        sprintf(buf2, "&+R%s is surrounded by burning flames!\n", HSSH(i));
      CAP(buf2);
      strcat(buffer, buf2);
    }
    // Changed so that major globe is bright red and minor globe it dark red -Azuth 7/17/2002
    //    if (IS_AFFECTED(i, AFF_MINOR_GLOBE) || IS_AFFECTED(i, AFF_GLOBE)) {
    //      sprintf(buf2, "&+r%s's encased in a shimmering globe!\n", HSSH(i));
    //      CAP(buf2);
    //      strcat(buffer, buf2);
    //    }
    if (IS_AFFECTED(i, AFF_GLOBE)) {
      sprintf(buf2, "&+R%s's encased in a shimmering globe!\n", HSSH(i));
      CAP(buf2);
      strcat(buffer, buf2);
    } else if (IS_AFFECTED(i, AFF_MINOR_GLOBE)) {
      sprintf(buf2, "&+r%s's encased in a shimmering globe!\n", HSSH(i));
      CAP(buf2);
      strcat(buffer, buf2);
    }
    if (IS_AFFECTED(i, AFF_METAGLOBE)) {
      sprintf(buf2, "&+B%s's encased in a translucent globe!\n", HSSH(i));
      CAP(buf2);
      strcat(buffer, buf2);
    }
    if (IS_AFFECTED(i, AFF_WATERBREATH)) {
      sprintf(buf2, "&+r%s has little gills in %s neck!\n", HSSH(i), HSHR(i));
      CAP(buf2);
      strcat(buffer, buf2);
    }
    strcat(buffer, "\n");
    send_to_char(buffer, ch);

    if (IS_AFFECTED(ch, AFF_WRAITHFORM))
      return;

    /* new look character wear order - Allenbri */

    found = FALSE;
    for (j = 0; wear_order[j] != -1; j++) {
      if (wear_order[j] == -2) {
        send_to_char("You are holding:\n", ch);
        found = FALSE;
        continue;
      }
      if (i->equipment[wear_order[j]]) {
        if (CAN_SEE_OBJ(ch, i->equipment[wear_order[j]]) || (ch == i)) {
          if (IS_SET(i->equipment[wear_order[j]]->extra_flags, ITEM_TWOHANDS) /* &&
                  (GET_OBJ_WEIGHT(i->equipment[wear_order[j]]) * 4 > CAN_WIELD_W(i)) */)
            send_to_char(((wear_order[j] >= WIELD) && (wear_order[j] <= HOLD) &&
                  (i->equipment[wear_order[j]]->type != ITEM_WEAPON)) ?
                  "<held in both hands> " : "<wielding twohanded> ", ch);
          else
            send_to_char(((wear_order[j] >= WIELD) && (wear_order[j] <= HOLD) &&
                  (i->equipment[wear_order[j]]->type != ITEM_WEAPON)) ?
                  where[HOLD] : where[wear_order[j]], ch);
          found = TRUE;
        }
        if (CAN_SEE_OBJ(ch, i->equipment[wear_order[j]])) {
          show_obj_to_char(i->equipment[wear_order[j]], ch, 1, 1);
        } else if (ch == i) {
          send_to_char("Something.\n", ch);
        }
      }
    }

    if ((!IS_TRUSTED(ch)) && (ch != i) && ((GET_CLASS(ch) == CLASS_THIEF) || (GET_CLASS(ch) ==
            CLASS_ROGUE))) {
      found = FALSE;
      send_to_char("\nYou attempt to peek at the inventory:\n", ch);
      for (tmp_obj = i->carrying; tmp_obj; tmp_obj = tmp_obj->next_content) {
        if (CAN_SEE_OBJ(ch, tmp_obj) && (number(1, GET_LEVEL(i) * 4) < GET_LEVEL(ch))) {
          show_obj_to_char(tmp_obj, ch, 1, 1);
          found = TRUE;
        }
      }
      if (!found)
        send_to_char("You can't see anything.\n", ch);
    }
  } else if ((mode == 2) && IS_TRUSTED(ch)) {

    /* Lists inventory */
    act("$n is carrying:", FALSE, i, 0, ch, TO_VICT);
    list_obj_to_char(i->carrying, ch, 1, TRUE);
  }
}

void list_char_to_char(P_char ch, int room_no, int vis_mode, int mode) {
  P_char i;
  int wgt_diff, tmp_wgt;

  if (!ch || (room_no == NOWHERE))
    return;

  if (GET_WEIGHT(ch) < 1)
    tmp_wgt = 1;
  else
    tmp_wgt = GET_WEIGHT(ch);

  if (IS_SUNLIT(room_no) && IS_AFFECTED(ch, AFF_ULTRAVISION) && !IS_TRUSTED(ch))
    return;

  for (i = world[room_no].people; i; i = i->next_in_room) {
    if ((i == ch) || WIZ_INVIS(ch, i))
      continue;
    if (!CAN_SEE(ch, i)) {
      if (IS_AFFECTED(ch, AFF_SENSE_LIFE) && GET_RACE(i) != RACE_GOLEM && !IS_UNDEAD(i))
        send_to_char("&+LYou sense a lifeform nearby.\n", ch);
      continue;
    }

    /* ok, they can see SOMETHING at this point */

    /* in the checks for light and dark... we should check it from the vantage point of i...
       not of ch... as ch might not be in the same room... */

    if (IS_TRUSTED(ch) || IS_LIGHT(room_no) || IS_AFFECTED(ch, AFF_ULTRAVISION)) {
      show_char_to_char(i, ch, 0);
      continue;
    }

    /* then infravision */
    if (IS_DARK(room_no) && IS_AFFECTED(ch, AFF_INFRAVISION) && racial_traits[GET_RACE(ch)].emits_heat) {
      wgt_diff = (GET_WEIGHT(i) * 100) / tmp_wgt;
      if (wgt_diff < 30) {
        send_to_char("&+rYou see the red shape of a tiny being here.\n", ch);
      } else if (wgt_diff < 85) {
        send_to_char("&+rYou see the red shape of a small being here.\n", ch);
      } else if (wgt_diff < 115) {
        send_to_char("&+rYou see the red shape of a being here.\n", ch);
      } else if (wgt_diff < 160) {
        send_to_char("&+rYou see the red shape of a large being here.\n", ch);
      } else if (wgt_diff < 400) {
        send_to_char("&+rYou see the red shape of a VERY LARGE being here.\n", ch);
      } else {
        send_to_char("&+rYou see the red shape of a *HUGE* being here.\n", ch);
      }
    }
  }
}

void do_do(P_char ch, char *argument, int cmd) {
  /* This command should replace all social commands ... as in
     do poke instead of just poke ...
   */
  return;
}

void ShowCharSpellBookSpells(P_char ch, P_obj obj) {
  char buf[MAX_STRING_LENGTH];
  int k, m = 0, spl;
  struct extra_descr_data *desc;

  desc = find_spell_description(obj);
  if (!desc) {
    sprintf(buf, "$o appears to be unused and has %d pages left.", obj->value[2]);
    act(buf, TRUE, ch, obj, 0, TO_CHAR);
    return;
  }
  //i = 0;
  //j = 0;
  if (obj->value[0] && GET_LANGUAGE(ch, obj->value[0]) <= 30) {
    act("$o is written in some language you don't understand!", TRUE, ch, obj, 0, TO_CHAR);
    return;
  }
  if (obj->value[1] && obj->value[1] != GET_CLASS(ch)) {
    sprintf(buf, "Original writer of $o appears to have been %s.", class_types[obj->value[1]]);
#if 0                           /* old version -- DTS 7/9/95 */
    act(buf, TRUE, ch, 0, 0, TO_CHAR);
#endif
    act(buf, TRUE, ch, obj, 0, TO_CHAR);
  }
  if (obj->value[1]) {
    if (praying_class(obj->value[1]))
      k = SKILL_SPELL_KNOWLEDGE_CLERICAL;
    else
      k = SKILL_SPELL_KNOWLEDGE_MAGICAL;
  } else if (GET_CHAR_SKILL(ch, SKILL_SPELL_KNOWLEDGE_MAGICAL) > GET_CHAR_SKILL(ch, SKILL_SPELL_KNOWLEDGE_CLERICAL))
    k = SKILL_SPELL_KNOWLEDGE_MAGICAL;
  else
    k = SKILL_SPELL_KNOWLEDGE_CLERICAL;
  buf[0] = 0;
  m = 0;
  for (spl = 0; spl < numSkills; spl++) { /* 'spl' is pindex, NOT SKILL_xxx */
    if (spl < 0)
      continue;
    if (skills[spl].harmful == -1)
      continue;
    if (!SpellInThisSpellBook(desc, spl))
      continue;
    if (!(((GET_CLASS(ch) == obj->value[1]) && (GetSpellCircle(ch, spl) <= GetMaxCircle_char(ch))) ||
            (GET_CHAR_SKILL(ch, k) >= 70)))
      continue;
    if (m)
      strcat(buf, ", ");
    m++;
    strcat(buf, skills[spl].name);
  }
#if 0
  if (m)
    strcat(buf, ".");
#endif
  if (!buf[0]) {
    send_to_char("It contains no spells you recognize outright", ch);
  } else if (m == 1) {
    send_to_char("It has just one spell you recognize: ", ch);
    send_to_char(buf, ch);
  } else {
    send_to_char("It contains the spells: ", ch);
    send_to_char(buf, ch);
  }

  if (obj->value[2] <= obj->value[3])
    send_to_char(" and has no free pages.\n", ch);
  else {
    sprintf(buf, " and has %d free pages.\n", obj->value[2] - obj->value[3]);
    send_to_char(buf, ch);
  }
}

void do_look(P_char ch, char *argument, int cmd) {
  //sprintf(Gbuf1, "&+WDEBUG -> do_look called. arg: (%s), cmd: (%d)\n", argument, cmd);
  //send_to_char(Gbuf1, ch);
  new_look(ch, argument, cmd, ch->in_room);
}

void new_look(P_char ch, char *argument, int cmd, int room_no) {
  P_char tmp_char;
  P_obj tmp_object, found_object;
  bool found;
  char *tmp_desc;
  char arg1[MAX_STRING_LENGTH], arg2[MAX_STRING_LENGTH], buffer[MAX_STRING_LENGTH];
  char arg3[MAX_STRING_LENGTH];
  char Gbuf1[MAX_STRING_LENGTH];
  int keyword_no, brief_mode, bits, temp, vis_mode = 0, keyword_no2, j;

  const char *keywords[] ={
    "north",
    "east",
    "south",
    "west",
    "up",
    "down",
    "in",
    "at",
    "", /* Look at '' case */
    "room",
    "group",
    "\n"
  };

  *arg3 = '\0';

  if (!ch->desc || (room_no == NOWHERE))
    return;

  // send_to_char("&+WDEBUG -> new_look called.\n", ch);

  if (IS_AFFECTED(ch, AFF_KNOCKED_OUT))
    return;

  if (GET_STAT(ch) < STAT_SLEEPING)
    return;
  else if (GET_STAT(ch) == STAT_SLEEPING) {
    send_to_char("Try opening your eyes first.\n", ch);
    return;
  }
  if (IS_AFFECTED(ch, AFF_BLIND)) {
    send_to_char("You can't see a damn thing, you're blinded!\n", ch);
    return;
  }

  /* ultravision is 'dark sight', basically chars with ultravision can see in total darkness, just like
     'normal' chars can see in daylight, chars with ultravision are completely blind in 'normal' light.
     JAB */

  if (IS_CSET(world[room_no].room_flags, MAGIC_DARK)) {
    if (IS_TRUSTED(ch))
      send_to_char("&+LRoom is magically dark.\n", ch);
    else {
      send_to_char("&+LIt is pitch black...\n", ch);
      return;
    }
  }

  if (IS_TRUSTED(ch))
    vis_mode = 1;
  else if (IS_AFFECTED(ch, AFF_WRAITHFORM))
    vis_mode = 4;
  else {
    if (IS_AFFECTED(ch, AFF_ULTRAVISION)) {
      if (IS_SUNLIT(room_no)) {
        send_to_char("&+WArgh!  TOO MUCH LIGHT!\n", ch);
        return;
      } else {
        vis_mode = 2;
      }
    } else if (IS_LIGHT(room_no))
      vis_mode = 2;
    else if (IS_DARK(room_no) && IS_AFFECTED(ch, AFF_INFRAVISION))
      vis_mode = 3;
    else {
      send_to_char("&+LIt is pitch black...\n", ch);
      return;
    }
  }

  /* vis_mode:
     1 - god sight, sees everything
     2 - normal in light, ultra in dark, sees most things
     3 - infra in the dark, quite limited
     4 - wraithsight, sees all beings, no objects
   */

  brief_mode = (IS_PC(GET_PLYR(ch)) && IS_CSET(GET_PLYR(ch)->only.pc->pcact, PLR_BRIEF));

  // OMG

  if (argument)
    argument_split_2(argument, arg1, arg2);
  else {
    *arg1 = '\0';
    *arg2 = '\0';
  }
  keyword_no = search_block(arg1, strlen(arg1), keywords, FALSE); /* Partial Match */

  if ((keyword_no == -1) && *arg1) {
    keyword_no = 7;
    strcpy(arg2, arg1); /* Let arg2 become the target object (arg1) */
  }
  found = FALSE;
  tmp_object = 0;
  tmp_char = 0;
  tmp_desc = 0;

  if ((keyword_no == 8) && (cmd != CMD_LOOK))
    keyword_no = 11;

  switch (keyword_no) {
      /* look <dir> */
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
      /* ok, this gets ugly, but adding vis_mode helps, all the MAGIC_DARK
         cases are already handled.  vis_mode < 3 works pretty much like this
         routine always has.  vis_mode 3 can't see closed exits. */

      if (!EXIT(ch, keyword_no)) {
        /* not a valid exit there */
        send_to_char("You see nothing special...\n", ch);
        return;
      }
      /* there IS an exit in that direction */

      if ((vis_mode != 1) &&
              (IS_SET(EXIT(ch, keyword_no)->exit_info, EX_SECRET) ||
              IS_SET(EXIT(ch, keyword_no)->exit_info, EX_BLOCKED))) {
        send_to_char("You see nothing special...\n", ch);
        return;
      }

      if ((vis_mode == 3) &&
              (!IS_AFFECTED(ch, AFF_FARSEE)) &&
              (IS_SET(EXIT(ch, keyword_no)->exit_info, EX_CLOSED) ||
              IS_SET(EXIT(ch, keyword_no)->exit_info, EX_SECRET) ||
              IS_SET(EXIT(ch, keyword_no)->exit_info, EX_BLOCKED))) {
        /* but infra-only can't detect it */
        send_to_char("You see nothing special...\n", ch);
        return;
      }
      /* vis_mode < 3 or vis_mode 3 and not a closed door  */
      /* OR vis_mode 3 with farsee and a closed door --MIAX */

      if (IS_SET(EXIT(ch, keyword_no)->exit_info, EX_ISDOOR)) {
        if (vis_mode == 1) {
          sprintf(buffer, "The %s is %s%s%s%s.\n", FirstWord(EXIT(ch, keyword_no)->keyword),
                  IS_SET(EXIT(ch, keyword_no)->exit_info, EX_CLOSED) ? "closed" : "open",
                  IS_SET(EXIT(ch, keyword_no)->exit_info, EX_LOCKED) ? " (locked)" : "",
                  IS_SET(EXIT(ch, keyword_no)->exit_info, EX_SECRET) ? " (hidden)" : "",
                  IS_SET(EXIT(ch, keyword_no)->exit_info, EX_BLOCKED) ? " (blocked)" : "");
          send_to_char(buffer, ch);
        } else {

          if (IS_SET(EXIT(ch, keyword_no)->exit_info, EX_SECRET) ||
                  IS_SET(EXIT(ch, keyword_no)->exit_info, EX_BLOCKED)) {
            send_to_char("You see nothing special...\n", ch);
            return;
          }
          if (vis_mode == 4) {
            if (IS_SET(EXIT(ch, keyword_no)->exit_info, EX_ISDOOR) &&
                    IS_SET(EXIT(ch, keyword_no)->exit_info, EX_CLOSED))
              send_to_char("You see a tenuous barrier.\n", ch);
            else
              send_to_char("You see an opening.\n", ch);
            return;
          }
          if (vis_mode == 2) {
            sprintf(buffer, "The %s is %s.\n",
                    FirstWord(EXIT(ch, keyword_no)->keyword),
                    IS_SET(EXIT(ch, keyword_no)->exit_info, EX_CLOSED) ?
                    "closed" : "open");
            send_to_char(buffer, ch);
          }
        }

        /* Changed for Missile scanning. Farsee now allows vision through
           doors, but normal extended vision does not. 8-28-96  --MIAX */

        if (!IS_AFFECTED(ch, AFF_FARSEE)) {
          if (IS_SET(EXIT(ch, keyword_no)->exit_info, EX_CLOSED)) {
            sprintf(buffer, "The closed %s blocks your vision.\n",
                    FirstWord(EXIT(ch, keyword_no)->keyword));
            send_to_char(buffer, ch);
            return;
          }
        } else {
          if (IS_SET(EXIT(ch, keyword_no)->exit_info, EX_CLOSED)) {
            sprintf(buffer, "You extend your vision through the %s.\n",
                    FirstWord(EXIT(ch, keyword_no)->keyword));
            send_to_char(buffer, ch);
          }
        }

      } else { /* non-door exit */
#if 0
        if (EXIT(ch, keyword_no)->general_description) {
          send_to_char(EXIT(ch, keyword_no)->general_description, ch);
        } else {
          send_to_char("Looks like an exit.\n", ch);
        }
#endif
      }

      /* get here, and it's a visible open exit, is a mortal with farsee, or a
         god (with or without farsee).  change vis_mode for farsee.
         vis_mode 1: god sight (without farsee)
         2: gods with farsee or mortals with vis_mode 2
         3: farsee (infravision)
       */
      /* Changed: Now mortal without farsee can peer into the next room,
         and mortal with Farsee can peer through doors. 8-28-96  --MIAX */

      /* real room we are peering into */
      temp = world[room_no].dir_option[keyword_no]->to_room;

      if ((IS_AFFECTED(ch, AFF_FARSEE)) &&
              (!IS_SET(EXIT(ch, keyword_no)->exit_info, EX_CLOSED))) {
        sprintf(buffer, "You extend your sights %swards.\n", dirs[keyword_no]);
        send_to_char(buffer, ch);
      }
      if (temp == NOWHERE) {
        if (IS_TRUSTED(ch))
          send_to_char("&+LYou look into nothingness (NOWHERE). Bug!\n", ch);
        else
          send_to_char("&+LSwirling mists block your sight.\n", ch);
        return;
      }
      if ((vis_mode == 1) && IS_AFFECTED(ch, AFF_FARSEE))
        vis_mode = 2;

      /* if return direction doesn't exist (one-way) or is not the reverse
         direction, normal farsee will be blocked.  vis_mode 1 sees all. */
      /* New change: Farsee _allows_ vision here, but normal sight is
         the one blocked. Farsee now acts more like vis_mode 1. --MIAX */

      if ((!world[temp].dir_option[rev_dir[keyword_no]] ||
              (world[temp].dir_option[rev_dir[keyword_no]]->to_room != room_no)) &&
              (/*!IS_AFFECTED(ch, AFF_FARSEE)*/GET_LEVEL(ch) == 0) && (vis_mode > 1)) {
        /* sight blocked */
        send_to_char("Something seems to be blocking your line of sight.\n", ch);
        return;
      }
      /* snatch em out, throw em in new room, look, put em back */
      /* (GLD 10/12/95) screw that... farsee is an extension of
         vision.. not a way to move into another room, setting light
         flags, triggering procs, fucking with 1-way rooms, etc...
         just have them look into the new room */

      /* If all above checks pass, check last of all for solid fog in the next    */
      /*   room, and the current room, and block the extended sight if it exists. */
      /* - 10/98 CRM                                                              */

      if ((IS_CSET(world[temp].room_flags, ROOM_SOLID_FOG) ||
              IS_CSET(world[room_no].room_flags, ROOM_SOLID_FOG)) && !IS_TRUSTED(ch)) {
        send_to_char("&+LA thick bank of solid &+Wfog&+L blocks your vision.\n", ch);
        return;
      } else
        new_look(ch, NULL, -4, temp);

      break;

    case 6:
      /* look 'in'  */
      if (vis_mode == 3) {
        send_to_char("You can't make out much detail in the dark with just infravision..\n", ch);
        break;
      }
      if (*arg2) {
        /* Item carried */

        bits = generic_find(arg2, FIND_OBJ_INV | FIND_OBJ_ROOM | FIND_OBJ_EQUIP, ch, &tmp_char, &tmp_object);

        if (bits) { /* Found something */
          if (GET_ITEM_TYPE(tmp_object) == ITEM_DRINKCON) {
            if (tmp_object->value[1] == 0) {
              send_to_char("It is empty.\n", ch);
            } else {
              if (tmp_object->value[1] < 0)
                temp = 3;
              else
                temp = BOUNDED(0, ((tmp_object->value[1] * 3) / tmp_object->value[0]), 2);
              sprintf(buffer, "It's %sfull of a %s liquid.\n",
                      fullness[temp], color_liquid[tmp_object->value[2]]);
              send_to_char(buffer, ch);
            }
          } else if (GET_ITEM_TYPE(tmp_object) == ITEM_SPELLBOOK) {
            ShowCharSpellBookSpells(ch, tmp_object);
          } else if ((GET_ITEM_TYPE(tmp_object) == ITEM_CONTAINER) ||
                  (GET_ITEM_TYPE(tmp_object) == ITEM_CART) || /* @ TRADE @ */
                  (GET_ITEM_TYPE(tmp_object) == ITEM_CORPSE)) {
            sprintf(Gbuf1, tmp_object->short_description);
            CAP(Gbuf1);
            send_to_char(Gbuf1, ch);
            if ((GET_ITEM_TYPE(tmp_object) == ITEM_CONTAINER) &&
                    IS_SET(tmp_object->value[1], CONT_CLOSED)) {
              send_to_char(" is closed.\n", ch);
            } else {
              switch (bits) {
                case FIND_OBJ_INV:
                  send_to_char(" (carried) : \n", ch);
                  break;
                case FIND_OBJ_ROOM:
                  send_to_char(" (here) : \n", ch);
                  break;
                case FIND_OBJ_EQUIP:
                  send_to_char(" (used) : \n", ch);
                  break;
              }
              list_obj_to_char(tmp_object->contains, ch, 2, TRUE);
            }
          } else if (GET_ITEM_TYPE(tmp_object) == ITEM_QUIVER) {
            send_to_char(FirstWord(tmp_object->name), ch);
            switch (bits) {
              case FIND_OBJ_INV:
                send_to_char(" (carried) : \n", ch);
                break;
              case FIND_OBJ_ROOM:
                send_to_char(" (here) : \n", ch);
                break;
              case FIND_OBJ_EQUIP:
                send_to_char(" (used) : \n", ch);
                break;
            }
            list_obj_to_char(tmp_object->contains, ch, 2, TRUE);
          } else if (GET_ITEM_TYPE(tmp_object) == ITEM_CRUCIBLE) {
            list_obj_to_char(tmp_object->contains, ch, 2, TRUE);
            //        } else if (GET_ITEM_TYPE(tmp_object) == ITEM_COMPONENT_BAG) {
            //          list_obj_to_char(tmp_object->contains, ch, 2, TRUE);
          } else {
            send_to_char("That is not a container.\n", ch);
          }
        } else { /* wrong argument */
          send_to_char("You do not see that item here.\n", ch);
        }
      } else { /* no argument */
        send_to_char("Look in what?!\n", ch);
      }
      break;

    case 7:
      /* look 'at'  */
      if (vis_mode == 3) {
        send_to_char("You can't make out much detail in the dark with just infravision..\n", ch);
        break;
      }
      if (*arg2) {
        /* determine if this applies to groups */
        keyword_no2 = search_block(arg2, strlen(arg2), keywords, FALSE);
        if (keyword_no2 == 10) {
          lookAtGroup(ch, vis_mode, arg2);
          break;
        }

        bits = generic_find(arg2, FIND_OBJ_INV | FIND_OBJ_ROOM | FIND_OBJ_EQUIP |
                FIND_CHAR_ROOM, ch, &tmp_char, &found_object);
        if (tmp_char) {
          show_char_to_char(tmp_char, ch, 1);
          /* Wizard can also see person's inventory */
          if (IS_TRUSTED(ch))
            show_char_to_char(tmp_char, ch, 2);
          if (ch != tmp_char) {
            act("$n looks at you.", TRUE, ch, 0, tmp_char, TO_VICT);
            act("$n looks at $N.", TRUE, ch, 0, tmp_char, TO_NOTVICT);
          }
          return;
        }

        /* Search for Extra Descriptions in room and items */
        /* Extra description in room?? */
        if (!found) {
          tmp_desc = find_ex_description(arg2, world[room_no].ex_description);
          if (tmp_desc) {
            page_string(ch->desc, tmp_desc, 0);
            return; /* RETURN SINCE IT WAS A ROOM DESCRIPTION */
            /* Old system was: found = TRUE; */
          }
        }

        /* Search for extra descriptions in items */
        /* Equipment Used */
        if (!found) {
          for (j = 0; j < MAX_WEAR && !found; j++) {
            if (ch->equipment[j]) {
              if (CAN_SEE_OBJ(ch, ch->equipment[j])) {
                tmp_desc =
                        find_ex_description(arg2, ch->equipment[j]->ex_description);
                if (tmp_desc) {
                  page_string(ch->desc, tmp_desc, 1);
                  found = TRUE;
                }
              }
            }
          }
        }

        if (vis_mode != 4) {
          /* In inventory */
          if (!found) {
            for (tmp_object = ch->carrying; tmp_object && !found; tmp_object = tmp_object->next_content) {
              if (CAN_SEE_OBJ(ch, tmp_object)) {
                tmp_desc = find_ex_description(arg2, tmp_object->ex_description);
                if (tmp_desc) {
                  page_string(ch->desc, tmp_desc, 1);
                  found = TRUE;
                }
              }
            }
          }
          /* Object In room */
          if (!found) {
            for (tmp_object = world[room_no].contents;
                    tmp_object && !found;
                    tmp_object = tmp_object->next_content) {
              if (CAN_SEE_OBJ(ch, tmp_object)) {
                tmp_desc = find_ex_description(arg2, tmp_object->ex_description);
                if (tmp_desc) {
                  page_string(ch->desc, tmp_desc, 1);
                  found = TRUE;
                }
              }
            }
          }
        }

        /* wrong argument */
        if (bits && (vis_mode != 4)) { /* If an object was found */
          if (!found)
            show_obj_to_char(found_object, ch, 5, 1); /* Show no-description */
          else
            show_obj_to_char(found_object, ch, 6, 1); /* Find hum, glow etc */
        } else if (!found) {
          send_to_char("You do not see that here.\n", ch);
        }
      } else {
        /* no argument */
        send_to_char("Look at what?\n", ch);
      }
      break;

    case 10: /* look command dealing with groups */
      lookAtGroup(ch, vis_mode, arg2);
      break;
    case 8: /* look COMMAND, with NULL args, brief is forced */
    case 9: /* look 'room', brief is overridden */
    case 11: /* look called with cmd -4, brief mode is honored */

      switch (keyword_no) {
        case 8: /* look COMMAND, with NULL args, brief is forced */
          brief_mode = 1;
          break;
        case 9: /* look 'room', brief is overridden */
          brief_mode = 0;
      }

      if (vis_mode == 4) {
        /* cackle! JAB */
        if (IS_CSET(world[room_no].room_flags, INDOORS))
          send_to_char("An Enclosed Space\n", ch);
        else
          send_to_char("An Open Space\n", ch);
      } else {
        if (IS_CSET(world[room_no].room_flags, NUKED))
          send_to_char(world[real_room(99998)].name, ch);
        else
          send_to_char(world[room_no].name, ch);

        /* For Solid Fog - CRM */
        if (IS_CSET(world[room_no].room_flags, ROOM_SOLID_FOG))
          send_to_char(" &+L(&N&+WFogged&+L)&N", ch);
        else if (IS_CSET(world[room_no].room_flags, ROOM_EARTH_FOG) || IS_CSET(world[room_no].room_flags, ROOM_EARTH_FOG_TRAVEL))
          send_to_char(" &+L(&N&+yFogged&+L)&N", ch);
        else if (IS_CSET(world[room_no].room_flags, ROOM_FIRE_FOG) || IS_CSET(world[room_no].room_flags, ROOM_FIRE_FOG_TRAVEL))
          send_to_char(" &+L(&N&+rFogged&+L)&N", ch);

        /* For Sun Shadow - Mie */
        if (IS_CSET(world[room_no].room_flags, ROOM_SUN_SHADOW))
          send_to_char(" &+L(&+WShadowed&+L)&N", ch);

        /* For Airy Water - CRM */
        if (IS_CSET(world[room_no].room_flags, ROOM_AIRY_WATER))
          send_to_char(" &+B(&N&+cAiry&+B)&N", ch);

        /* For water rooms - DA */
        if (TERRAIN_WATER(room_no) && !TERRAIN_NOFALL(room_no))
          send_to_char(" (&+bWater&N)", ch);

        if (world[room_no].sector_type == SECT_NO_GROUND || world[room_no].sector_type == SECT_UNDRWLD_NOGROUND)
          send_to_char(" (&+LNo Ground&N)", ch);


        if (IS_PC(ch) && IS_CSET(ch->only.pc->pcact, PLR_VNUM) && IS_TRUSTED(ch)) {
          sprintf(buffer, " [&+R%d&N:&+C%d&N]", world[room_no].zone, world[room_no].number);
          send_to_char(buffer, ch);
          if (IS_CSET(world[room_no].room_flags, BURNING))
            send_to_char("&n(&=LrOn Fire!&n)", ch);
        }
        send_to_char("\n", ch);

        if (!(brief_mode || (keyword_no == 8) || (vis_mode == 3))) {
          if (IS_CSET(world[room_no].room_flags, NUKED))
            send_to_char(world[real_room(99998)].description, ch);
          else {
            if (IS_CSET(world[room_no].room_flags, BURNING)) {
              send_to_char(
                      "&=LrThis area is on fire! Everything blazes in flames that sting your eyes,\n"
                      "creating intense heat that burns at your skin and makes you want to run!\n"
                      "Burning ash and choking black smoke fill the air, making breathing nearly impossible!\n"
                      "Through the smoke, the room looks something like:\n", ch);
            }
            //sprintf(Gbuf1, "&+WDEBUG -> BEFORE DESC: brief_mode: %d, keyword_no: %d, vis_mode: %d\n",
            //  brief_mode, keyword_no, vis_mode);
            //send_to_char(Gbuf1, ch);
            send_to_char(world[room_no].description, ch);
            //send_to_char("&+WDEBUG -> AFTER DESC.\n", ch);
          }
        }
      }

      show_exits_to_char(ch, room_no, 1);

      if (vis_mode < 3)
        list_obj_to_char(world[room_no].contents, ch, 0, FALSE);

      list_char_to_char(ch, room_no, vis_mode, 0);

      break;

      /* wrong arg  */
    default:
      send_to_char("Sorry, I didn't understand that!\n", ch);
      break;
  }
}

/* end of look */

/* Routine modified to show room dimensions. --MIAX 081096. */

void show_exits_to_char(P_char ch, int room_no, int mode) {
  char buffer[MAX_STRING_LENGTH];
  const char dir_letters[7] = "NESWUD";
  int i, vis_mode, count = 0, brief_mode;

  const char *exits[] ={
    " &+cNorth&n",
    " &+cEast&n ",
    " &+cSouth&n",
    " &+cWest&n ",
    " &+cUp&n   ",
    " &+cDown&n "
  };

  *buffer = '\0';

  if (IS_TRUSTED(ch))
    vis_mode = 1;
  else if (IS_AFFECTED(ch, AFF_WRAITHFORM))
    vis_mode = 4;
  else {
    if (IS_AFFECTED(ch, AFF_ULTRAVISION)) {
      if (IS_SUNLIT(room_no)) {
        return;
      } else {
        vis_mode = 2;
      }
    } else if (IS_LIGHT(room_no))
      vis_mode = 2;
    else if (IS_DARK(room_no) && IS_AFFECTED(ch, AFF_INFRAVISION))
      vis_mode = 3;
    else {
      return;
    }
  }

  /* vis_mode:
     1 - god sight, sees everything
     2 - normal in light, ultra in dark, sees most things
     3 - infra in the dark, quite limited
     4 - wraithsight, sees all beings, no objects
   */

  brief_mode = IS_CSET(GET_PLYR(ch)->only.pc->pcact, PLR_BRIEF);

  if (mode == 1) /* Short form, shown with a general 'look' */ {
    switch (vis_mode) {
      case 1:
        if (IS_CSET(GET_PLYR(ch)->only.pc->pcact, PLR_ROOMSIZE)) {
          strcpy(buffer, "&+gRoom size:&n");
          sprintf(buffer + strlen(buffer), " &+C%s&n &+g(L:&+c%d ft&n&+g  W:&+c%d ft&n&+g  H:&+c%d ft&n&+g)&n\n&+gExits:&n",
                  room_dimension_names[(int) world[room_no].size],
                  world[room_no].length, world[room_no].width,
                  world[room_no].height);
        } else {
          strcpy(buffer, "&+gExits:&n");
        }
        break;
      case 2:
      case 3:
      case 4:
        if (IS_CSET(GET_PLYR(ch)->only.pc->pcact, PLR_ROOMSIZE)) {
          strcpy(buffer, "&+gRoom size:&n");
          sprintf(buffer + strlen(buffer), " &+C%s&n &+g(L:&+c%d ft&n&+g  W:&+c%d ft&n&+g  H:&+c%d ft&n&+g)&n\n&+gExits:&n",
                  room_dimension_names[(int) world[room_no].size],
                  world[room_no].length, world[room_no].width, world[room_no].height);
        } else {
          strcpy(buffer, "&+gExits:&n");
        }
        break;
    }

    for (count = 0, i = 0; i < 6; i++) {
      if (!(world[room_no].dir_option[i]) ||
              (((world[room_no].dir_option[i])->to_room == NOWHERE) && (vis_mode != 1)))
        continue;
      switch (vis_mode) {
        case 1:
          count++;
          if (!brief_mode)
            sprintf(buffer + strlen(buffer), " &+c-%s&n", exits[i]);
          else
            sprintf(buffer + strlen(buffer), " &+c-%c&n", dir_letters[i]);
          if (IS_SET((world[room_no].dir_option[i])->exit_info, EX_CLOSED))
            strcat(buffer, "&+g#&n");
          if (IS_SET((world[room_no].dir_option[i])->exit_info, EX_LOCKED))
            strcat(buffer, "&+y@&n");
          if (IS_SET((world[room_no].dir_option[i])->exit_info, EX_SECRET))
            strcat(buffer, "&+L*&n");
          if (IS_SET((world[room_no].dir_option[i])->exit_info, EX_BLOCKED))
            strcat(buffer, "&+r%&n");
          if (IS_SET((world[room_no].dir_option[i])->exit_info, EX_TRAPPED) &&
                  world[room_no].dir_option[i]->trap_state)
            strcat(buffer, "&+C(T)&n");
          if (IS_SET((world[room_no].dir_option[i])->exit_info, EX_TRAPPED) &&
                  !world[room_no].dir_option[i]->trap_state)
            strcat(buffer, "&+L(T)&n");

          if (IS_TRUSTED(ch) && IS_CSET(ch->only.pc->pcact, PLR_VNUM)) {
            if ((world[room_no].dir_option[i])->to_room == NOWHERE)
              strcat(buffer, "[&+R-1&N:&+C-1&N] ");
            else
              sprintf(buffer + strlen(buffer), " [&+R%d&N:&+C%d&N]",
                    world[(world[room_no].dir_option[i])->to_room].zone,
                    world[(world[room_no].dir_option[i])->to_room].number);
          }
          break;
        case 2:
          if (IS_SET((world[room_no].dir_option[i])->exit_info, EX_SECRET) ||
                  IS_SET((world[room_no].dir_option[i])->exit_info, EX_BLOCKED))
            break;
          count++;
          if (!brief_mode)
            sprintf(buffer + strlen(buffer), " &+c-%s&n", exits[i]);
          else
            sprintf(buffer + strlen(buffer), " &+c-%c&n", dir_letters[i]);
          if (IS_SET((world[room_no].dir_option[i])->exit_info, EX_CLOSED))
            strcat(buffer, "&+g#&n");
          break;
        case 3:
          if (IS_SET((world[room_no].dir_option[i])->exit_info, EX_SECRET) ||
                  IS_SET((world[room_no].dir_option[i])->exit_info, EX_CLOSED) ||
                  IS_SET((world[room_no].dir_option[i])->exit_info, EX_BLOCKED))
            break;
          count++;
          if (!brief_mode)
            sprintf(buffer + strlen(buffer), " &+c-%s&n", exits[i]);
          else
            sprintf(buffer + strlen(buffer), " &+c-%c&n", dir_letters[i]);
          break;
        case 4:
          if (IS_SET((world[room_no].dir_option[i])->exit_info, EX_SECRET) ||

                  (IS_SET((world[room_no].dir_option[i])->exit_info, EX_CLOSED) &&
                  (IS_SET((world[room_no].dir_option[i])->exit_info, EX_PICKPROOF) ||
                  ((world[room_no].dir_option[i])->key == -2))) ||
                  IS_SET((world[room_no].dir_option[i])->exit_info, EX_BLOCKED))
            break;
          count++;
          if (!brief_mode)
            sprintf(buffer + strlen(buffer), " &+c-%s&n", exits[i]);
          else
            sprintf(buffer + strlen(buffer), " &+c-%c&n", dir_letters[i]);
          if (IS_SET((world[room_no].dir_option[i])->exit_info, EX_CLOSED))
            strcat(buffer, "&+g#&n");
          break;
      }
    }
  } else if (mode == 2) {
    strcpy(buffer, "&+gObvious Exits:&n");
    strcat(buffer, "\n");

    for (count = 0, i = 0; i < 6; i++) {
      if (!EXIT(ch, i) || ((EXIT(ch, i)->to_room == NOWHERE) && (vis_mode != 1)))
        continue;

      switch (vis_mode) {
        case 1:
          count++;
          sprintf(buffer + strlen(buffer), "%s- %s%s%s%s%s&n ", exits[i],
                  IS_SET(EXIT(ch, i)->exit_info, EX_ISDOOR) ? "&+yD" : " ",
                  IS_SET(EXIT(ch, i)->exit_info, EX_CLOSED) ? "&+gC" : " ",
                  IS_SET(EXIT(ch, i)->exit_info, EX_LOCKED) ? "&+RL&n" : " ",
                  IS_SET(EXIT(ch, i)->exit_info, EX_SECRET) ? "&+LS&n" : " ",
                  IS_SET(EXIT(ch, i)->exit_info, EX_BLOCKED) ? "&+bB" : " ");

          if (EXIT(ch, i)->to_room != NOWHERE) {
            if (IS_CSET(world[EXIT(ch, i)->to_room].room_flags, MAGIC_DARK))
              strcat(buffer, "(&+LMagic Dark&n) ");
            else if (IS_DARK(EXIT(ch, i)->to_room))
              strcat(buffer, "(&+LDark&n) ");
            else if (IS_CSET(world[EXIT(ch, i)->to_room].room_flags, MAGIC_LIGHT))
              strcat(buffer, "(&+WMagic Light&n) ");
          }
          if (IS_TRUSTED(ch) && IS_CSET(ch->only.pc->pcact, PLR_VNUM)) {
            if (EXIT(ch, i)->to_room == NOWHERE)
              strcat(buffer, "[&+R-1&N:&+C-1&N] ");
            else
              sprintf(buffer + strlen(buffer), "[&+R%d&N:&+C%d&N] (&+C%s&N) ",
                    world[EXIT(ch, i)->to_room].zone,
                    world[EXIT(ch, i)->to_room].number,
                    room_dimension_names[(int) world[EXIT(ch, i)->to_room].size]);
          }
          if (EXIT(ch, i)->to_room != NOWHERE)
            strcat(buffer, world[EXIT(ch, i)->to_room].name);
          strcat(buffer, "\n");
          break;
        case 2:
          if ((EXIT(ch, i)->to_room == NOWHERE) ||
                  IS_SET(EXIT(ch, i)->exit_info, EX_SECRET) ||
                  IS_SET(EXIT(ch, i)->exit_info, EX_BLOCKED))
            break;
          count++;
          sprintf(buffer + strlen(buffer), "%s- ", exits[i]);
          if (IS_SET(EXIT(ch, i)->exit_info, EX_ISDOOR))
            sprintf(buffer + strlen(buffer), "(%s %s) ",
                  IS_SET(EXIT(ch, i)->exit_info, EX_CLOSED) ? "closed" : "open",
                  FirstWord(EXIT(ch, i)->keyword));
          if (!IS_SET(EXIT(ch, i)->exit_info, EX_CLOSED)) {
            if (IS_SUNLIT(EXIT(ch, i)->to_room) && IS_AFFECTED(ch, AFF_ULTRAVISION))
              strcat(buffer, "&+WToo bright to tell&n.");
            else if (IS_CSET(world[EXIT(ch, i)->to_room].room_flags, MAGIC_DARK) ||
                    (IS_DARK(EXIT(ch, i)->to_room) && !IS_AFFECTED(ch, AFF_ULTRAVISION)))
              strcat(buffer, "&+LToo dark to tell&n.");
            else {
              sprintf(buffer + strlen(buffer), "&+L(&n&+c%s&+L)&N ",
                      room_dimension_names[(int) world[EXIT(ch, i)->to_room].size]);
              strcat(buffer, world[EXIT(ch, i)->to_room].name);
            }
          }
          strcat(buffer, "\n");
          break;
        case 3:
          if ((EXIT(ch, i)->to_room == NOWHERE) ||
                  IS_SET(EXIT(ch, i)->exit_info, EX_SECRET) ||
                  IS_SET(EXIT(ch, i)->exit_info, EX_CLOSED) ||
                  IS_SET(EXIT(ch, i)->exit_info, EX_BLOCKED) ||
                  IS_DARK(EXIT(ch, i)->to_room))
            break;
          count++;
          sprintf(buffer + strlen(buffer), "%s- %s\n", exits[i], world[EXIT(ch, i)->to_room].name);
          break;
        case 4:
          if ((EXIT(ch, i)->to_room == NOWHERE) ||
                  IS_SET(EXIT(ch, i)->exit_info, EX_SECRET) ||
                  IS_SET(EXIT(ch, i)->exit_info, EX_BLOCKED) ||
                  IS_SET(EXIT(ch, i)->exit_info, EX_PICKPROOF) ||
                  (EXIT(ch, i)->key == -2))
            break;
          count++;
          sprintf(buffer + strlen(buffer), "%s- ", exits[i]);
          if (IS_SET(EXIT(ch, i)->exit_info, EX_ISDOOR) &&
                  IS_SET(EXIT(ch, i)->exit_info, EX_CLOSED))
            strcat(buffer, "(veiled)\n");
          else
            strcat(buffer, "open\n");
          break;
      }
    }
  }

  if (!count)
    strcat(buffer, " &+RNone!\n");
  else if (mode == 1)
    strcat(buffer, "\n");

  send_to_char(buffer, ch);
}

void do_read(P_char ch, char *argument, int cmd) {
  char buf[MAX_INPUT_LENGTH];

  /* This is just for now - To be changed later.! */
  sprintf(buf, "at %s", argument);
  do_look(ch, buf, -4);
}

void do_examine(P_char ch, char *argument, int cmd) {
  P_char tmp_char;
  P_obj tmp_object = NULL;
  char name[MAX_INPUT_LENGTH], buf[MAX_INPUT_LENGTH + 4];
  int bits;

  one_argument(argument, name);

  if (!*name) {
    send_to_char("Examine what?\n", ch);
    return;
  }
  sprintf(buf, "at %s", argument);
  do_look(ch, buf, -4);

  bits = generic_find(name, FIND_OBJ_INV | FIND_OBJ_ROOM | FIND_OBJ_EQUIP, ch, &tmp_char, &tmp_object);
  
  /* this was just put here to get rid of compiler warning :P */
  if (!bits && !tmp_char) {
    send_to_char("\r\n", ch);
  }

  if (tmp_object) {
    if ((GET_ITEM_TYPE(tmp_object) == ITEM_DRINKCON) ||
            (GET_ITEM_TYPE(tmp_object) == ITEM_CONTAINER) ||
            (GET_ITEM_TYPE(tmp_object) == ITEM_CORPSE) ||
            (GET_ITEM_TYPE(tmp_object) == ITEM_CART) || /* @ TRADE @ */
            (GET_ITEM_TYPE(tmp_object) == ITEM_QUIVER)/* ||
         (GET_ITEM_TYPE(tmp_object) == ITEM_COMPONENT_BAG)*/) {
      if (GET_ITEM_TYPE(tmp_object) == ITEM_CONTAINER) {
        sprintf(buf, "Looks like it can hold about %d lbs.\n", ((int) ((tmp_object->value[0] + 1) / 10) * 10));
        send_to_char(buf, ch);
      }
      send_to_char("When you look inside, you see:\n", ch);
      sprintf(buf, "in %s", argument);
      do_look(ch, buf, -4);
    }
  }
}

/* almost completely rewritten to handle various 'vis_modes'. JAB */

void do_exits(P_char ch, char *argument, int cmd) {
  if (!ch->desc || (ch->in_room == NOWHERE))
    return;

  if (GET_STAT(ch) < STAT_SLEEPING)
    return;
  else if (GET_STAT(ch) == STAT_SLEEPING) {
    send_to_char("Try opening your eyes first.\n", ch);
    return;
  }
  if (IS_AFFECTED(ch, AFF_BLIND)) {
    send_to_char("You can't see a damn thing, you're blinded!\n", ch);
    return;
  }

  if (IS_CSET(world[ch->in_room].room_flags, MAGIC_DARK)) {
    if (IS_TRUSTED(ch))
      send_to_char("&+LRoom is magically dark.\n", ch);
    else {
      send_to_char("&+LIt is pitch black...\n", ch);
      return;
    }
  }

  show_exits_to_char(ch, ch->in_room, 2);
}

void do_multi_list(P_char ch, int cmd) {
  int k, i, memClass, skillType, skill, j;
  int skillArray[NUMBER_OF_SKILL_SPELL_TYPES][MAX_SKILLS];
  int skillArrayCellCounter[NUMBER_OF_SKILL_SPELL_TYPES + 1];
  bool spellList = FALSE;
  char Gbuf3[MAX_STRING_LENGTH], tmpBuf[MAX_STRING_LENGTH];


  if (!ch || IS_NPC(ch)) {
    logit(LOG_EXIT, "do_spell_list():  funky parms, dumping core.");
    dump_core();
  }

  // Mmm, multi-function command.
  if (cmd == CMD_SPELLS)
    spellList = TRUE;

  if (spellList && !IS_MAGE(ch) && !IS_CLERIC(ch) && !IS_SEMI_CASTER(GET_CLASS(ch))) {
    send_to_char("You are not trained in magic!\n", ch);
    return;
  }

  memClass = meming_class(GET_CLASS(ch));
  bzero(skillArrayCellCounter, sizeof (skillArrayCellCounter));
  bzero(skillArray, sizeof (skillArray));
  strcpy(Gbuf3, "");


  // Loop through spells/skills
  for (k = 0; k < MAX_SKILLS; k++) {
    i = pindex2Skill[k];

    if (i < 0)
      continue;

    if (spellList && !IS_SPELL(k))
      continue;

    if (!spellList && IS_SPELL(k))
      continue;

    if ((k >= SKILL_FIRST_SPEC) && (k <= SKILL_LAST_SPEC) && CharHasSpec(ch) && !GET_CHAR_SKILL_S(ch, i))
      continue;

    if (!skills[i].class[GET_CLASS(ch) - 1].rlevel)
      continue;

    if (!IS_SPELL(k) && GET_LEVEL(ch) < skills[i].class[GET_CLASS(ch) - 1].rlevel)
      continue;

    if (IS_SPELL(k)) {
      if (!memClass) {
        if ((GetSpellCircle(ch, i) > GetMaxCircle_char(ch)) ||
                ((skills[i].class[GET_CLASS(ch) - 1].base_skill == SKILL_BASE_SPECIAL) &&
                !ch->only.pc->skills[k].learned))
          continue;
      } else if (!SpellInSpellBook(ch, i, (SBOOK_MODE_IN_INV + SBOOK_MODE_AT_HAND))) {
        continue;
      }
    }

    // Does this thing actually exist? :P
    //if ((skills[i].harmful != -1) && !skills[i].spell_pointer)
    //continue;

    // Valid skill/spell - insert into the array for sorted extraction
    skillArray[skills[i].type][skillArrayCellCounter[skills[i].type]++] = k;
  }

  // Sort and display with headers
  for (skillType = 0; skillType < NUMBER_OF_SKILL_SPELL_TYPES; skillType++) {
    tmpBuf[0] = 0;
    for (i = 0; i < skillArrayCellCounter[skillType]; i++) {
      skill = skillArray[skillType][i];
      if (IS_SPELL(skill)) {
        skill = pindex2Skill[skill];
        j = GetSpellCircle(ch, skill);
        sprintf(tmpBuf + strlen(tmpBuf), "%-25s %2d%s circle  %s\n",
                skills[skill].name, j,
                (j == 1) ? "st" : (j == 2) ? "nd" : (j == 3) ? "rd" : "th",
                (memClass &&
                SpellInSpellBook(ch, skill, (SBOOK_MODE_IN_INV + SBOOK_MODE_AT_HAND + SBOOK_MODE_NO_SCROLL))) ?
                ((GetSpellCircle(ch, skill) > GetMaxCircle_char(ch)) ?
                "[in spellbook, but too high level]" : "[in spellbook]") : "");
      } else {
        skill = pindex2Skill[skill];
        sprintf(tmpBuf + strlen(tmpBuf), "%-25s%-16s (%d)\n",
                skills[skill].name,
                how_good(GET_CHAR_SKILL_S(ch, skill)), GET_CHAR_SKILL_S(ch, skill));
      }
    }

    if (tmpBuf[0])
      sprintf(Gbuf3 + strlen(Gbuf3), "%s%s", skillTypeHeader[skillType], tmpBuf);
  }

  page_string(ch->desc, Gbuf3, 1);
  return;
}

struct spell_damage_table {
  int dice_num;
  int dice_size;
  int pc_pc_dice_num;
  int pc_pc_dice_size;
};

extern struct spell_damage_table SpellDamageTable[];

void do_spell_damage(P_char ch) {
  int spl, damage = 0, spec = 0, off_level, count;
  char buf[MAX_STRING_LENGTH], buf2[MAX_STRING_LENGTH], class_name[MAX_STRING_LENGTH];
  char class_list[LAST_CLASS + 1][MAX_STRING_LENGTH];

  *buf = '\0';
  *buf2 = '\0';
  for (spl = 1; spl <= LAST_CLASS; spl++)
    *class_list[spl] = '\0';
  for (spl = 0; spl < numSkills; spl++) {
    if ((skills[spl].spell_pointer == NULL) || (skills[spl].harmful == -1) ||
            (skills[spl].harmful == 0) || (skills[spl].off_level == 0) ||
            skills[spl].class[CLASS_PSIONICIST - 1].rlevel)
      continue;
    off_level = skills[spl].off_level;
    damage = MIN(50, SpellDamageTable[off_level].dice_num);
    damage = damage * (SpellDamageTable[off_level].dice_size + 1) / 2;
    if (GetLowestSpellCircle(spl) > 8 && IS_SET(skills[spl].targets, TAR_IGNORE))
      damage = damage * 80 / 100;
    if (!strcmp(skills[spl].name, "magic missile"))
      damage = damage * 5 / 2;
    else if (!strcmp(skills[spl].name, "minute meteors"))
      damage = damage * 5;
    else if (!strcmp(skills[spl].name, "force missiles"))
      damage = damage * 3;
    else if (!strcmp(skills[spl].name, "earth darts"))
      damage = damage * 5 / 2;
    else if (!strcmp(skills[spl].name, "shadow bolt"))
      damage = damage * 5 / 2;
    else if (!strcmp(skills[spl].name, "creeping doom"))
      damage = damage * 3;
    else if (!strcmp(skills[spl].name, "rot"))
      damage = damage * 4;
    else if (!strcmp(skills[spl].name, "sticks to snakes"))
      damage = damage * 5;
    else if (!strcmp(skills[spl].name, "fire seeds"))
      damage = damage * 4;
    else if (!strcmp(skills[spl].name, "blackthorns"))
      damage = damage * 5;
    else if (!strcmp(skills[spl].name, "totem darts"))
      damage = damage * 5 / 2;
    spec = damage * 135 / 100;
    if (IS_SET(skills[spl].targets, TAR_IGNORE))
      sprintf(buf + strlen(buf), "%-28s              %3d %3d\n",
            skills[spl].name, damage, spec);
    else
      sprintf(buf2 + strlen(buf2), "%-28s              %3d %3d\n",
            skills[spl].name, damage, spec);
    for (count = 1; count <= LAST_CLASS; count++)
      if (skills[spl].class[count - 1].rlevel)
        sprintf(class_list[count] + strlen(class_list[count]), "%-28s              %3d %3d\n",
              skills[spl].name, damage, spec);
  }
  send_to_char("These numbers assume a 50th level caster.\n\n", ch);
#if 0
  send_to_char("Area Spell                                Dmg Spc\n\n", ch);
  strcat(buf, "\n");
  page_string(ch->desc, buf, 1);
  send_to_char("Single-Target Spell                       Dmg Spc\n\n", ch);
  strcat(buf2, "\n");
  page_string(ch->desc, buf2, 1);
#endif
  for (count = 1; count <= LAST_CLASS; count++) {
    if (!meming_class(count) && !praying_class(count))
      continue;
    if (count == CLASS_SORCERER || count == CLASS_CONJURER)
      continue;
    sprintf(class_name, "%-28s              Dmg Spc\n", class_types[count]);
    send_to_char(class_name, ch);
    strcat(class_list[count], "\n");
    page_string(ch->desc, class_list[count], 1);
  }
}

void do_spells(P_char ch, char *argument, int cmd) {
  char buf[MAX_STRING_LENGTH];
  int spl;

  if (IS_NPC(ch)) {
    send_to_char("You ain't nothin' but a hound-dog.\n", ch);
    return;
  }

  // Love the hack.  BE the hack.  -- CRM
  if (!IS_TRUSTED(ch) || !strcmp(argument, " list")) {
    do_multi_list(ch, cmd);
    return;
  }

  if (!strcmp(argument, " damage")) {
    do_spell_damage(ch);
    return;
  }

  send_to_char(" Nbr  Name                           Agg    Ap Cl Co Dr Ne Pa Ra Sh So Li In En Il El DR\n", ch);

  *buf = '\0';

  for (spl = 0; spl < numSkills; spl++) {
    if ((skills[spl].spell_pointer == NULL) || (skills[spl].harmful == -1))
      continue;

    sprintf(buf + strlen(buf), "[%2d] %-20s              %1d   %2d %2d %2d %2d %2d %2d %2d %2d %2d %2d %2d %2d %2d %2d %2d\n",
            spl, skills[spl].name, (int) skills[spl].harmful,
            skills[spl].class[CLASS_ANTIPALADIN - 1].rlevel,
            skills[spl].class[CLASS_CLERIC - 1].rlevel,
            skills[spl].class[CLASS_CONJURER - 1].rlevel,
            skills[spl].class[CLASS_DRUID - 1].rlevel,
            skills[spl].class[CLASS_NECROMANCER - 1].rlevel,
            skills[spl].class[CLASS_PALADIN - 1].rlevel,
            skills[spl].class[CLASS_RANGER - 1].rlevel,
            skills[spl].class[CLASS_SHAMAN - 1].rlevel,
            skills[spl].class[CLASS_SORCERER - 1].rlevel,
            skills[spl].class[CLASS_LICH - 1].rlevel,
            skills[spl].class[CLASS_INVOKER - 1].rlevel,
            skills[spl].class[CLASS_ENCHANTER - 1].rlevel,
            skills[spl].class[CLASS_ILLUSIONIST - 1].rlevel,
            skills[spl].class[CLASS_ELEMENTALIST - 1].rlevel,
            skills[spl].class[CLASS_DIRERAIDER - 1].rlevel);
  }

  strcat(buf, "\n");
  page_string(ch->desc, buf, 1);
}

/* stolen shamelessly from do_spells -- DTS 2/9/95 */
void do_skills(P_char ch, char *argument, int cmd) {
  int skl;
  char buf[MAX_STRING_LENGTH];

  if (IS_NPC(ch)) {
    send_to_char("You ain't nothin' but a hound-dog.\n", ch);
    return;
  }

  // Love the hack.  BE the hack.  -- CRM
  if (!IS_TRUSTED(ch) || !strcmp(argument, " list")) {
    do_multi_list(ch, cmd);
    return;
  }

  send_to_char("      Name                   Ap As Bc Be Cl Co Dr Me Mo Ne Pa Ra Sh So Th Wa Ps Li Ro DR\n", ch);

  *buf = '\0';

  for (skl = 1; skl < numSkills; skl++) {
    if ((skills[skl].spell_pointer != NULL) || (skills[skl].harmful != -1))
      continue;

    sprintf(buf + strlen(buf),
            "%-25s    %2d %2d %2d %2d %2d %2d %2d %2d %2d %2d %2d %2d %2d %2d %2d %2d %2d %2d %2d %2d\n",
            skills[skl].name,
            skills[skl].class[CLASS_ANTIPALADIN - 1].rlevel,
            skills[skl].class[CLASS_ASSASSIN - 1].rlevel,
            skills[skl].class[CLASS_BATTLECHANTER - 1].rlevel,
            skills[skl].class[CLASS_BARD - 1].rlevel,
            skills[skl].class[CLASS_CLERIC - 1].rlevel,
            skills[skl].class[CLASS_CONJURER - 1].rlevel,
            skills[skl].class[CLASS_DRUID - 1].rlevel,
            skills[skl].class[CLASS_MERCENARY - 1].rlevel,
            skills[skl].class[CLASS_MONK - 1].rlevel,
            skills[skl].class[CLASS_NECROMANCER - 1].rlevel,
            skills[skl].class[CLASS_PALADIN - 1].rlevel,
            skills[skl].class[CLASS_RANGER - 1].rlevel,
            skills[skl].class[CLASS_SHAMAN - 1].rlevel,
            skills[skl].class[CLASS_SORCERER - 1].rlevel,
            skills[skl].class[CLASS_THIEF - 1].rlevel,
            skills[skl].class[CLASS_WARRIOR - 1].rlevel,
            skills[skl].class[CLASS_PSIONICIST - 1].rlevel,
            skills[skl].class[CLASS_LICH - 1].rlevel,
            skills[skl].class[CLASS_ROGUE - 1].rlevel,
            skills[skl].class[CLASS_DIRERAIDER - 1].rlevel);
  }

  strcat(buf, "\n");
  page_string(ch->desc, buf, 1);
}

#define WORLD_STATS         0
#define WORLD_ZONES         1
#define WORLD_EVENTS        2
#define WORLD_ROOMS         3
#define WORLD_OBJECTS       4
#define WORLD_MOBILES       5
#define WORLD_DEBUG         6
#define WORLD_SOCIALS       7
#define WORLD_SOCIALS_ZONE  8
#define WORLD_GROUPS        9
#define WORLD_ASSOCS       10

const char *world_keywords[] ={
  "stats",
  "zones",
  "events",
  "rooms",
  "objects",
  "mobiles",
  "debug",
  "socials",
  "zsocials",
  "groups",
  "assocs",
  "\n"
};

const int world_values[] ={
  WORLD_STATS,
  WORLD_ZONES,
  WORLD_EVENTS,
  WORLD_ROOMS,
  WORLD_OBJECTS,
  WORLD_MOBILES,
  WORLD_DEBUG,
  WORLD_SOCIALS,
  WORLD_SOCIALS_ZONE,
  WORLD_GROUPS,
  WORLD_ASSOCS,
  -1
};

/* boy was this ugly pile of strcmps - fixed. -Torm */

void do_world(P_char ch, char *argument, int cmd) {
  P_char t_mob = NULL;
  P_obj t_obj = NULL, k = NULL;
  P_assoc assoc = NULL;
  P_group t_grp = NULL;
  P_gmember member = NULL;
  char *tmstr = NULL, buf[MAX_STRING_LENGTH] = "", buff[MAX_STRING_LENGTH] = "";
  int count, i, choice, zone_count, world_index, room_count, length = 0;
  time_t ct;
  int diff_time, totals = 0, invisobjs = 0;
  struct zone_data *z_num = &zone_table[world[ch->in_room].zone];
  unsigned int rooms = 0;

  char first_arg[MAX_STRING_LENGTH] = "", second_arg[MAX_INPUT_LENGTH] = "";

  if (IS_NPC(ch))
    return;

  two_arguments(argument, first_arg, second_arg);
  world_index = search_block(first_arg, strlen(first_arg), world_keywords, FALSE);
  //   debuglog(51, DS_AZUTH, "argument [%s]  first_arg [%s]  second_arg [%s] index = %d", argument, first_arg, second_arg, world_index);

  if (world_index == -1 || (!IS_TRUSTED(ch) && (choice = world_values[world_index]) > WORLD_ZONES)) {
    sprintf(buff, "Syntax: world < ");
    for (i = 0; world_values[i] != (IS_TRUSTED(ch) ? -1 : WORLD_ZONES + 1); i++) {
      if (i)
        sprintf(buff + strlen(buff), ", ");

      sprintf(buff + strlen(buff), "%s", world_keywords[i]);
      if (i == WORLD_GROUPS)
        sprintf(buff + strlen(buff), " [all]");
    }

    sprintf(buff + strlen(buff), " >\n");
    send_to_char(buff, ch);

    if (!*first_arg) {
      send_to_char("This server compiled at " __TIME__ " " __DATE__ ".\n", ch);
      if (game_locked & LOCK_CREATE)
        send_to_char("&+RWIZLOCK&N&+r: The MUD is locked to new character creation.&N\n", ch);
      if (game_locked & LOCK_CONNECTIONS)
        send_to_char("&+RWIZLOCK&N&+r: The MUD is locked to new connections.&N\n", ch);
    }

    return;
  }

  switch (world_values[world_index]) {
    case WORLD_STATS:
      ct = time(0);
      {
        struct tm *tm_info = localtime(&ct);
        if (!tm_info) {
          send_to_char("Error: System time unavailable.\n", ch);
          break;
        }
        tmstr = asctime(tm_info);
      }
      *(tmstr + strlen(tmstr) - 1) = '\0';
      sprintf(buf, "Current time is: %s (EDT)\n\n", tmstr);
      send_to_char(buf, ch);

      diff_time = ct - boot_time;
      sprintf(buf, "Time elapsed, since boot-up: %d days %2dH %2dM %2dS\n\n",
              diff_time / 86400, (diff_time % 86400) / 3600, (diff_time / 60) % 60, diff_time % 60);
      send_to_char(buf, ch);

      sprintf(buf, "Total number of zones in world:    %5d\n\n", top_of_zone_table + 1);
      send_to_char(buf, ch);
      for (totals = 0; totals <= top_of_world; totals++) {
        if (world[totals].number)
          rooms++;
      }

      sprintf(buf, "Total number of rooms in world:    %5d\n", rooms + 1);
      send_to_char(buf, ch);
      sprintf(buf, "Total number of different mobiles: %5d\n", top_of_mobt + 1);
      send_to_char(buf, ch);

      for (i = 0, count = 0; i <= top_of_mobt; i++, count += mob_index[i].number)
        ;

      sprintf(buf, "Total number of living mobiles:    %5d\n\n", count);
      send_to_char(buf, ch);

      sprintf(buf, "Total number of different objects: %5d\n", top_of_objt + 1);
      send_to_char(buf, ch);

      for (i = 0, count = 0; i <= top_of_objt; i++, count += obj_index[i].number)
        ;

      sprintf(buf, "Total number of existing objects:  %5d\n\n", count);
      send_to_char(buf, ch);

      if (IS_TRUSTED(ch)) {
        sprintf(buf, "Total number of shops:             %5d\n", number_of_shops);
        send_to_char(buf, ch);

        sprintf(buf, "Total number of quests:            %5d\n", number_of_quests);
        send_to_char(buf, ch);

        sprintf(buf, "Total number of mobs with socials: %5d\n\n", num_of_socials);
        send_to_char(buf, ch);

        sprintf(buf, "Number of active sockets:          %5d\n", MAX(10, used_descs));
        send_to_char(buf, ch);
        sprintf(buf, "Max sockets used this boot:        %5d\n", MAX(10, max_descs));
        send_to_char(buf, ch);
        sprintf(buf, "Maximum allowable sockets:         %5d\n\n", avail_descs);
        send_to_char(buf, ch);
      }
      break;

    case WORLD_ZONES:
      if (GET_LEVEL(ch) < MINLVLIMMORTAL) { /* Show ONLY zones to mortals */
        send_to_char("Zone    Name\n", ch);
        for (zone_count = 0; zone_count <= top_of_zone_table; zone_count++)
          sprintf(buff + strlen(buff), "%3d   %-30s\n", zone_count, zone_table[zone_count].name);
      } else {
        send_to_char("Zone    Name                 Reset(min) Age To-Reset StartN    EndN   Mode\n", ch);
        for (zone_count = 0; zone_count <= top_of_zone_table; zone_count++)
          sprintf(buff + strlen(buff), "%3d %-30s %3d  %3d    %4d  %6d  %6d   %d\n",
                zone_count, zone_table[zone_count].name, zone_table[zone_count].lifespan,
                zone_table[zone_count].age,
                zone_table[zone_count].lifespan - zone_table[zone_count].age,
                world[zone_table[zone_count].real_bottom].number, zone_table[zone_count].top,
                zone_table[zone_count].reset_mode);
      }

      strcat(buff, "\n");
      page_string(ch->desc, buff, 1);
      break;

    case WORLD_EVENTS:
      strcpy(buf, "Type          :Number   %\n");
      for (i = 0; i < LAST_EVENT; i++) {
        if (event_counter[i])
          sprintf(buf + strlen(buf), "%-14s:%6d %5.2f\n", event_names[i], event_counter[i],
                (float) (100 * event_counter[i]) / event_counter[LAST_EVENT]);
      }

      sprintf(buf + strlen(buf), "\n%-14s:%6d\n%-14s:%6d\n%-14s:%6d\n%-14s:%10lu\n\n",
              "Pending", event_counter[LAST_EVENT],
              "Reserved", event_counter[LAST_EVENT + 1],
              "Total", event_counter[LAST_EVENT] + event_counter[LAST_EVENT + 1],
              "Resched", rescheduleCounter);

      page_string(ch->desc, buf, 1);
      break;

    case WORLD_ROOMS:
      i = world[ch->in_room].zone;
      send_to_char("&+LR-Num&n   V-Num   Room-Name\n", ch);
      for (room_count = 0; room_count <= top_of_world; room_count++) {
        // Added sanity check to allow for null (empty) rooms. --MIAX 10/17/00
        if (!IS_CSET(world[room_count].room_flags, RESERVED_OLC)) {
          if (world[room_count].zone == i) {
            sprintf(buf, "&+L%5d&n  %6d  %-s\n",
                    room_count, world[room_count].number, world[room_count].name);
            if ((strlen(buf) + length + 40) < MAX_STRING_LENGTH) {
              strcat(buff, buf);
              length += strlen(buf);
            } else {
              strcat(buff, "Too many rooms to list...\n");
              break;
            }
          }
        }
      }

      strcat(buff, "\n");
      page_string(ch->desc, buff, 1);
      break;

    case WORLD_OBJECTS:
      send_to_char("V-Num   Count  Name\n", ch);
      if ((world[ch->in_room].zone < 2) && (GET_LEVEL(ch) < 57)) {
        send_to_char("Sorry Mr. Nosy (or Ms.), you can't do this in god rooms.\n", ch);
        break;
      }

      for (i = 0; i <= top_of_objt; i++) {
        if ((obj_index[i].virtual >= world[z_num->real_bottom].number) &&
                (obj_index[i].virtual <= world[z_num->real_top].number)) {
          /* easier to just load one, and free it, than load only ones that aren't already in game. */
          invisobjs = 0;
          if (obj_index[i].number) {
            for (k = object_list; k; k = k->next) {
              if (k->R_num == i) {
                if (!CAN_SEE_OBJ(ch, k))
                  invisobjs++;

                if (OBJ_INSIDE(k)) { // only checking one lvl deep on bags
                  if (!CAN_SEE_OBJ(ch, k->loc.inside))
                    invisobjs++;
                }
              }
            }

            sprintf(buf + strlen(buf), "%6d  %5d  %-s\n", obj_index[i].virtual,
                    obj_index[i].number - invisobjs,
                    obj_index[i].desc2 ? obj_index[i].desc2 : "None");

          } else {
            t_obj = read_object(obj_index[i].virtual, VIRTUAL);
            if (t_obj) {
              for (k = object_list; k; k = k->next) {
                if (k->R_num == i) {
                  if (!CAN_SEE_OBJ(ch, k))
                    invisobjs++;

                  if (OBJ_INSIDE(k)) { // only checking one lvl deep on bags
                    if (!CAN_SEE_OBJ(ch, k->loc.inside))
                      invisobjs++;
                  }
                }
              }

              sprintf(buf + strlen(buf), "%6d  %5d  %-s\n",
                      obj_index[i].virtual,
                      (obj_index[i].number - 1) - invisobjs,
                      t_obj->short_description ? t_obj->short_description : "None");

              extract_obj(t_obj);
            } else
              logit(LOG_DEBUG, "do_world(): obj %d not loadable", obj_index[i].virtual);
          }

          if (strlen(buf) > (MAX_STRING_LENGTH - 100)) {
            strcat(buf, "Too many objects to list...\n");
            break;
          }
        }
      }

      strcat(buf, "\n");
      page_string(ch->desc, buf, 1);
      break;

    case WORLD_ASSOCS:
      send_to_char("Num   Name\n", ch);
      for (assoc = Assoc_list; assoc; assoc = assoc->next) {
        sprintf(buf, "  %u   %s\n", assoc->number, assoc->name);
        strcat(buff, buf);
      }

      page_string(ch->desc, buff, 1);
      break;

    case WORLD_MOBILES:
      send_to_char("V-Num   Count  Name\n", ch);
      for (i = 0; i <= top_of_mobt; i++) {
        if ((mob_index[i].virtual >= world[z_num->real_bottom].number) &&
                (mob_index[i].virtual <= world[z_num->real_top].number)) {
          /* easier to just load one, and free it, than load only
             ones that aren't already in game. Only gods can do this so
             a little load doesn't matter. JAB */
          /* easier perhaps, but certainly not faster, and the load was getting bad -Azuth */
          if (mob_index[i].number) {
            sprintf(buf + strlen(buf), "%6d  %5d  %-s\n",
                    mob_index[i].virtual, mob_index[i].number,
                    mob_index[i].desc2 ? mob_index[i].desc2 : "None");
          } else {
            t_mob = read_mobile(mob_index[i].virtual, VIRTUAL);
            if (t_mob) {
              if (IS_CSET(t_mob->only.npc->npcact, ACT_SPEC))
                REMOVE_CBIT(t_mob->only.npc->npcact, ACT_SPEC);
              char_to_room(t_mob, ch->in_room, -2);
              sprintf(buf + strlen(buf), "%6d  %5d  %-s\n",
                      mob_index[i].virtual, mob_index[i].number - 1,
                      t_mob->player.short_descr ? t_mob->player.short_descr : "None");

              extract_char(t_mob);
            } else
              logit(LOG_DEBUG, "do_world(): mob %d not loadable", mob_index[i].virtual);
          }

          if (strlen(buf) > (MAX_STRING_LENGTH - 100)) {
            strcat(buf, "Too many mobiles to list...\n");
            break;
          }
        }
      }

      strcat(buf, "\n");
      page_string(ch->desc, buf, 1);
      break;

    case WORLD_DEBUG:
      sprintf(buff, "Debug summary:\n\n");
      sprintf(buff + strlen(buff), "Scribing data in memory:        %4d\n", ScribeJobs());
      sprintf(buff + strlen(buff), "Scribing jobs free:             %4d\n", ScribeJobsFree());
      sprintf(buff + strlen(buff), "Spellcast data in memory:       %4d\n", SpellCastStack());
      sprintf(buff + strlen(buff), "Spare spellcast data in memory: %4d\n\n", SpareCastStack());
      /*    sprintf(buff + strlen(buff), "Pulses: deficit:  %ld seconds.\n", (int)time_deficit.tv_sec); */
      send_to_char(buff, ch);
      break;

    case WORLD_GROUPS:
      send_to_char("&+LActive PC lead groups&N\n", ch);
      send_to_char("Leader                     M-Vnum    InRoom   Size  Cache  Name\n", ch);
      count = 0;
      for (t_grp = group_list; t_grp; t_grp = t_grp->next) {
        if (IS_NPC(t_grp->leader))
          continue;

        count++;
        length += sprintf(buff + strlen(buff), "%-25.25s [%6d]  [%6d]  %2d    %2d     %-s\n", GET_NAME(t_grp->leader),
                IS_NPC(t_grp->leader) ? mob_index[t_grp->leader->nr].virtual : -1,
                t_grp->leader->in_room > -1 ? world[t_grp->leader->in_room].number : -1,
                countGroupMembers(t_grp), countItemsInCache(t_grp),
                (t_grp->long_descr) ? t_grp->long_descr : "None");

        if (length + 1024 > MAX_STRING_LENGTH) {
          strcat(buff, "Too many groups to list...\n");
          break;
        }

        if (*second_arg != 'a')
          continue;

        // they want to see ALL group members
        for (member = t_grp->members; member; member = member->next) {
          if (t_grp->leader == member->this)
            continue;

          length += sprintf(buff + strlen(buff), " %-24.24s [%6d]  [%6d]\n", GET_NAME(member->this),
                  IS_NPC(member->this) ? mob_index[member->this->nr].virtual : -1,
                  member->this->in_room > -1 ? world[member->this->in_room].number : -1);

          if (length + 1024 > MAX_STRING_LENGTH) {
            strcat(buff, "Too many groups to list...\n");
            break;
          }
        }
      }

      if (!count)
        strcat(buff, "NONE\n");

      strcat(buff, "\n&+LActive NPC lead groups&N\n");
      strcat(buff, "Leader                     M-Vnum    InRoom   Size  Cache  Name\n");
      count = 0;
      for (t_grp = group_list; t_grp; t_grp = t_grp->next) {
        if (IS_PC(t_grp->leader))
          continue;

        count++;
        length += sprintf(buff + strlen(buff), "%-25.25s [%6d]  [%6d]  %2d    %2d     %-s\n", GET_NAME(t_grp->leader),
                IS_NPC(t_grp->leader) ? mob_index[t_grp->leader->nr].virtual : -1,
                t_grp->leader->in_room > -1 ? world[t_grp->leader->in_room].number : -1,
                countGroupMembers(t_grp), countItemsInCache(t_grp),
                (t_grp->long_descr) ? t_grp->long_descr : "None");

        if (length + 1024 > MAX_STRING_LENGTH) {
          strcat(buff, "Too many groups to list...\n");
          break;
        }

        if (*second_arg != 'a')
          continue;

        // they want to see ALL group members
        for (member = t_grp->members; member; member = member->next) {
          if (t_grp->leader == member->this)
            continue;

          length += sprintf(buff + strlen(buff), " %-24.24s [%6d]  [%6d]\n", GET_NAME(member->this),
                  IS_NPC(member->this) ? mob_index[member->this->nr].virtual : -1,
                  member->this->in_room > -1 ? world[member->this->in_room].number : -1);

          if (length + 1024 > MAX_STRING_LENGTH) {
            strcat(buff, "Too many groups to list...\n");
            break;
          }
        }
      }

      if (!count)
        strcat(buff, "NONE\n");

      strcat(buff, "\n");
      page_string(ch->desc, buff, 1);
      break;

    case WORLD_SOCIALS:
      sprintf(buff + strlen(buff), "Overall Stats for Mob Social Actions\n\n");
      sprintf(buff + strlen(buff), "Total Number of Mobs with Socials:      %5d\n", num_of_socials);
      sprintf(buff + strlen(buff), "Total Number of Triggers:               %5d\n", num_of_social_triggers);
      sprintf(buff + strlen(buff), "Total Number of Periodic Actions:       %5d\n", num_of_social_periodics);
      sprintf(buff + strlen(buff), "Total Number of Periodic Lists:         %5d\n", num_of_social_lists);
      sprintf(buff + strlen(buff), "Total Number of Path:                   %5d\n", num_of_social_path);
      sprintf(buff + strlen(buff), "Total Number of Timed Actions:          %5d\n", num_of_social_timed);
      sprintf(buff + strlen(buff), "Total Number of Errors:                 %5d\n", num_of_social_errors);
      strcat(buff, "\n");
      page_string(ch->desc, buff, 1);
      break;

    case WORLD_SOCIALS_ZONE:
      send_to_char("V-Num   Type  Name\n", ch);
      for (i = 0; i <= top_of_mobt; i++) {
        if ((mob_index[i].virtual >= world[z_num->real_bottom].number) &&
                (mob_index[i].virtual <= world[z_num->real_top].number)) {
          if (socials_index[i]) {
            sprintf(buf, "%6d  ", mob_index[i].virtual);

            if (socials_index[i]->periodic)
              strcat(buf, "P");
            else
              strcat(buf, " ");

            if (socials_index[i]->trigger)
              strcat(buf, "T");
            else
              strcat(buf, " ");

            if (socials_index[i]->list)
              strcat(buf, "L");
            else
              strcat(buf, " ");

            if (socials_index[i]->error)
              strcat(buf, "&+rE&N");
            else
              strcat(buf, " ");

            if ((t_mob = read_mobile(mob_index[i].virtual, VIRTUAL))) {
              if (IS_CSET(t_mob->only.npc->npcact, ACT_SPEC))
                REMOVE_CBIT(t_mob->only.npc->npcact, ACT_SPEC);

              char_to_room(t_mob, ch->in_room, -2);
              sprintf(buf + strlen(buf), "  %-s\n",
                      (t_mob->player.short_descr) ? t_mob->player.short_descr : "&+rNo Short Description -- FIX IT&N");

              if (t_mob) {
                extract_char(t_mob);
                t_mob = NULL;
              } else {
                logit(LOG_EXIT, "GLITCH 1");
                dump_core();
              }

              if ((strlen(buf) + length + 40) < MAX_STRING_LENGTH) {
                strcat(buff, buf);
                length += strlen(buf);
              } else {
                strcat(buff, "Too many mobiles to list...\n");
                break;
              }
            } else
              logit(LOG_DEBUG, "do_world(): mob %d not loadable", mob_index[i].virtual);
          }
        }
      }

      strcat(buff, "\n");
      page_string(ch->desc, buff, 1);
      break;
  }
}

/*
 *      SAM 5-94, re-written to make a bit nicer, show more info!
 */
void do_attributes(P_char ch, char *argument, int cmd) {
  char buf[MAX_STRING_LENGTH];
  int t_val;
  struct time_info_data ch_age;

  if (ch == NULL) {
    logit(LOG_STATUS, "do_attributes passed NULL ch pointer");
    return;
  }

  if (IS_MORPH(ch)) {
    send_to_char("You cannot accurately gauge the attributes of this form.\n", ch);
    return;
  }

  /* header */
  sprintf(buf, "\n\t\t&+gCharacter attributes for&n &+G%s\n\n&n", GET_NAME(ch));
  send_to_char(buf, ch);

  /* level, race, class */
  sprintf(buf, "Level: %d   Race: %s   Class: %s\n&n",
          GET_LEVEL(ch), race_to_string(ch), class_to_string(ch));
  send_to_char(buf, ch);

  age(&ch_age, ch);
  /* age, height, weight */
  sprintf(buf, "Age: %d yrs / %d mths  Height: %d inches  Weight: %d lbs\n",
          ch_age.year, ch_age.month, GET_HEIGHT(ch), GET_WEIGHT(ch));
  send_to_char(buf, ch);

  /* Stats */
  if (GET_LEVEL(ch) >= 20) {
    /* this is ugly, because of new racial stat mods.  JAB */

    if (GET_C_STR(ch) > stat_factor[(int) GET_RACE(ch)].Str)
      sprintf(buf, "STR: ***");
    else
      sprintf(buf, "STR: %3d",
            MAX(1, (int) ((GET_C_STR(ch) * 100. / stat_factor[(int) GET_RACE(ch)].Str) + .95)));

    if (GET_C_AGI(ch) > stat_factor[(int) GET_RACE(ch)].Agi)
      sprintf(buf + strlen(buf), "  AGI: ***");
    else
      sprintf(buf + strlen(buf), "  AGI: %3d",
            MAX(1, (int) ((GET_C_AGI(ch) * 100. / stat_factor[(int) GET_RACE(ch)].Agi) + .95)));

    if (GET_C_DEX(ch) > stat_factor[(int) GET_RACE(ch)].Dex)
      sprintf(buf + strlen(buf), "  DEX: ***");
    else
      sprintf(buf + strlen(buf), "  DEX: %3d",
            MAX(1, (int) ((GET_C_DEX(ch) * 100. / stat_factor[(int) GET_RACE(ch)].Dex) + .95)));

    if (GET_C_CON(ch) > stat_factor[(int) GET_RACE(ch)].Con)
      sprintf(buf + strlen(buf), "  CON: ***\n");
    else
      sprintf(buf + strlen(buf), "  CON: %3d\n",
            MAX(1, (int) ((GET_C_CON(ch) * 100. / stat_factor[(int) GET_RACE(ch)].Con) + .95)));

    if (GET_C_POW(ch) > stat_factor[(int) GET_RACE(ch)].Pow)
      sprintf(buf + strlen(buf), "POW: ***");
    else
      sprintf(buf + strlen(buf), "POW: %3d",
            MAX(1, (int) ((GET_C_POW(ch) * 100. / stat_factor[(int) GET_RACE(ch)].Pow) + .95)));

    if (GET_C_INT(ch) > stat_factor[(int) GET_RACE(ch)].Int)
      sprintf(buf + strlen(buf), "  INT: ***");
    else
      sprintf(buf + strlen(buf), "  INT: %3d",
            MAX(1, (int) ((GET_C_INT(ch) * 100. / stat_factor[(int) GET_RACE(ch)].Int) + .95)));

    if (GET_C_WIS(ch) > stat_factor[(int) GET_RACE(ch)].Wis)
      sprintf(buf + strlen(buf), "  WIS: ***");
    else
      sprintf(buf + strlen(buf), "  WIS: %3d",
            MAX(1, (int) ((GET_C_WIS(ch) * 100. / stat_factor[(int) GET_RACE(ch)].Wis) + .95)));

    if (GET_C_CHA(ch) > stat_factor[(int) GET_RACE(ch)].Cha)
      sprintf(buf + strlen(buf), "  CHA: ***\n");
    else
      sprintf(buf + strlen(buf), "  CHA: %3d\n",
            MAX(1, (int) ((GET_C_CHA(ch) * 100. / stat_factor[(int) GET_RACE(ch)].Cha) + .95)));


  } else {
    sprintf(buf, "STR: %s\tAGI: %s\tDEX: %s\tCON: %s\nPOW: %s\tINT: %s\tWIS: %s\tCHA: %s\n",
            stat_to_string3((int) ((GET_C_STR(ch) * 100. / stat_factor[(int) GET_RACE(ch)].Str) + .5)),
            stat_to_string3((int) ((GET_C_AGI(ch) * 100. / stat_factor[(int) GET_RACE(ch)].Agi) + .5)),
            stat_to_string3((int) ((GET_C_DEX(ch) * 100. / stat_factor[(int) GET_RACE(ch)].Dex) + .5)),
            stat_to_string3((int) ((GET_C_CON(ch) * 100. / stat_factor[(int) GET_RACE(ch)].Con) + .5)),
            stat_to_string3((int) ((GET_C_POW(ch) * 100. / stat_factor[(int) GET_RACE(ch)].Pow) + .5)),
            stat_to_string3((int) ((GET_C_INT(ch) * 100. / stat_factor[(int) GET_RACE(ch)].Int) + .5)),
            stat_to_string3((int) ((GET_C_WIS(ch) * 100. / stat_factor[(int) GET_RACE(ch)].Wis) + .5)),
            stat_to_string3((int) ((GET_C_CHA(ch) * 100. / stat_factor[(int) GET_RACE(ch)].Cha) + .5)));
  }
  /*
      else {
      sprintf(buf, "STR: %s\tAGI: %s\tDEX: %s\tCON: %s\nPOW: %s\tINT: %s\tWIS: %s\tCHA: %s\n",
              stat_to_string1((int) ((GET_C_STR(ch) * 100. / stat_factor[(int) GET_RACE(ch)].Str) + .5)),
              stat_to_string1((int) ((GET_C_AGI(ch) * 100. / stat_factor[(int) GET_RACE(ch)].Agi) + .5)),
              stat_to_string1((int) ((GET_C_DEX(ch) * 100. / stat_factor[(int) GET_RACE(ch)].Dex) + .5)),
              stat_to_string1((int) ((GET_C_CON(ch) * 100. / stat_factor[(int) GET_RACE(ch)].Con) + .5)),
              stat_to_string1((int) ((GET_C_POW(ch) * 100. / stat_factor[(int) GET_RACE(ch)].Pow) + .5)),
              stat_to_string1((int) ((GET_C_INT(ch) * 100. / stat_factor[(int) GET_RACE(ch)].Int) + .5)),
              stat_to_string1((int) ((GET_C_WIS(ch) * 100. / stat_factor[(int) GET_RACE(ch)].Wis) + .5)),
              stat_to_string1((int) ((GET_C_CHA(ch) * 100. / stat_factor[(int) GET_RACE(ch)].Cha) + .5)));
    }
   */
  send_to_char(buf, ch);

  /* Armor Class */

  t_val = BOUNDED(-100, GET_AC(ch), 100);

  if (load_modifier(ch) > 299)
    t_val += 40;
  else if (load_modifier(ch) > 199)
    t_val += 25;
  else if (load_modifier(ch) > 99)
    t_val += 10;

  if (GET_CLASS(ch) == CLASS_MONK)
    t_val += (MonkAcBonus(ch) * 3);

  if (AWAKE(ch) && !IS_AFFECTED(ch, AFF_MINOR_PARALYSIS) && !IS_AFFECTED(ch, AFF_MAJOR_PARALYSIS))
    t_val += agi_app[STAT_INDEX(GET_C_AGI(ch))].defensive;

  t_val = BOUNDED(-100, t_val, 100);

  if ((GET_CLASS(ch) == CLASS_MONK) && (t_val < 0))
    t_val = 0;

  //  if (GET_LEVEL(ch) >= 25) {
  sprintf(buf, "Armor Class: %d  (100 to -100)\n", t_val);
  /*
    } else {
      sprintf(buf, "Armor Class: %s\n", ac_to_string(t_val));
    }
   */

  send_to_char(buf, ch);

  if (IS_PC(ch) && (GET_CLASS(ch) == CLASS_MONK))
    MonkSetSpecialDie(ch);

  /* Hitroll, Damroll */
  //  if (GET_LEVEL(ch) >= 25) {
  sprintf(buf, "Hitroll: %d   Damroll: %d",
          GET_HITROLL(ch) +
#ifdef NEW_BARD
          (affected_by_song(ch, SONG_OF_OFFENSIVE_HARMONY) ? ch->bard_singing->song_singing->modifier : 0) -
          (affected_by_song(ch, SONG_OF_OFFENSIVE_DISRUPTION) ? ch->bard_singing->song_singing->modifier : 0) +
#endif
          cc_thac0_dexMod / 100 *
          DexHitBonus(STAT_INDEX(GET_C_DEX(ch))),
          GET_DAMROLL(ch) +
#ifdef NEW_BARD
          (affected_by_song(ch, SONG_OF_OFFENSIVE_HARMONY) ? ch->bard_singing->song_singing->modifier / 2 : 0) -
          (affected_by_song(ch, SONG_OF_OFFENSIVE_DISRUPTION) ? ch->bard_singing->song_singing->modifier / 2 : 0) +
#endif
          StrDamBonus(STAT_INDEX(GET_C_STR(ch))));
  if (IS_NPC(ch) || (GET_CLASS(ch) == CLASS_MONK))
    sprintf(buf + strlen(buf), "   Barehand Damage: %dd%d", ch->points.damnodice, ch->points.damsizedice);
  /*
    } else {
      sprintf(buf, "Hitroll: %s\tDamroll: %s",
              hitdam_roll_to_string(GET_HITROLL(ch)), hitdam_roll_to_string(GET_DAMROLL(ch)));
      if (IS_NPC(ch) || (GET_CLASS(ch) == CLASS_MONK)) {
        t_val = ch->points.damnodice * ch->points.damsizedice;
        sprintf(buf + strlen(buf), "   Approx Barehand Damage: %s",
                (t_val < 8) ? "mace" :
                (t_val < 10) ? "axe" :
                (t_val < 12) ? "broadsword" :
                (t_val < 18) ? "battleaxe" : "greatsword");
      }
    }
   */

  strcat(buf, "\n");
  send_to_char(buf, ch);

  /* Alignment */
  //  if (GET_LEVEL(ch) >= 25) {
  sprintf(buf, "Alignment: %d  (-1000 to 1000)\n", GET_ALIGNMENT(ch));
  /*
    } else {
      sprintf(buf, "Alignment: %s\n", align_to_string(GET_ALIGNMENT(ch)));
    }
   */
  send_to_char(buf, ch);

  /* Saving throws */
  //  if (GET_LEVEL(ch) >= 25) {
  sprintf(buf, "Saving Throws: PAR[%d]  ROD[%d]  PET[%d]  BRE[%d]  SPE[%d]\n",
          ch->specials.apply_saving_throw[0],
          ch->specials.apply_saving_throw[1],
          ch->specials.apply_saving_throw[2],
          ch->specials.apply_saving_throw[3],
          ch->specials.apply_saving_throw[4]);
  /*
    } else {
      sprintf(buf, "Saving Throws: PAR[%s]  ROD[%s]  PET[%s]  BRE[%s]  SPE[%s]\n",
              save_to_string(ch->specials.apply_saving_throw[0]),
              save_to_string(ch->specials.apply_saving_throw[1]),
              save_to_string(ch->specials.apply_saving_throw[2]),
              save_to_string(ch->specials.apply_saving_throw[3]),
              save_to_string(ch->specials.apply_saving_throw[4]));
    }
   */
  send_to_char(buf, ch);

  if (IS_PC(ch)) {
    if (GET_WIMPY(ch) > 0) {
      sprintf(buf, "   Wimpy: %d\n", GET_WIMPY(ch));
    } else {
      sprintf(buf, "   Wimpy: not set\n");
    }
    send_to_char(buf, ch);
    if (GET_MANABURN(ch) > 0) {
      sprintf(buf, "   Manaburn: %d\n", GET_MANABURN(ch));
    } else if (GET_CLASS(ch) == CLASS_BARD || GET_CLASS(ch) == CLASS_BATTLECHANTER) {
      sprintf(buf, "   ManaBurn: not set\n");
    }
  }

  /* Equipment Carried */
  sprintf(buf, "Load carried: %s\n", load_to_string(ch));
  send_to_char(buf, ch);
}

/* SAM 7-94, improved a bit to show more info, format more like att now */
void do_score(P_char ch, char *argument, int cmd) {
  bool found = FALSE;
  int i, base, next, diff, progress, unit, bar;
  static char buf[MAX_STRING_LENGTH], buf2[MAX_STRING_LENGTH];
  struct affected_type *aff;
  struct follow_type *fol;
  struct time_info_data playing_time;
  struct moneyStruct *money;
  char str1[57] = "&+W>";
  char str2[2];
  //char expstr[12], levelstr[12];
  char str3[] = "&+R>";
  char Gbuf1[MAX_STRING_LENGTH];

  if (ch == NULL) {
    logit(LOG_STATUS, "do_score passed NULL ch pointer");
    return;
  }
  /* header */
  /* header */
  sprintf(buf, "\n\t\t&+gScore information for&n &+G%s\n\n&n",
          IS_MORPH(ch) ? ch->player.short_descr : GET_NAME(ch));
  send_to_char(buf, ch);

  /* level, race, class */
  sprintf(buf, "Level: %d   Race: %s   Class: %s\n&n",
          GET_LEVEL(ch), race_to_string(ch), class_to_string(ch));
  send_to_char(buf, ch);

  /* hit pts, mana, moves */
  if (GET_CLASS(ch) == CLASS_PSIONICIST ||
          GET_CLASS(ch) == CLASS_BARD ||
          GET_CLASS(ch) == CLASS_BATTLECHANTER ||
          GET_RACE(ch) == RACE_YUANTI)
    sprintf(buf,
          "&+RHit points: %d&+r(%d)   &+MPSP's: %d&+m(%d)   &+YMoves: %d&+y(%d)&N\n",
          GET_HIT(ch), GET_MAX_HIT(ch), GET_MANA(ch), GET_MAX_MANA(ch), GET_MOVE(ch), GET_MAX_MOVE(ch));
  else
    sprintf(buf,
          "&+RHit points: %d&+r(%d)  &+YMoves: %d&+y(%d)&N\n",
          GET_HIT(ch), GET_MAX_HIT(ch), GET_MOVE(ch), GET_MAX_MOVE(ch));

  send_to_char(buf, ch);

  if (!IS_TRUSTED(ch)) {
    /* now comes the kludge for poor saps that can't read large numbers */
    /* exp numbers taken out - DA 3/21/01
    strcpy (expstr, comma_string ((int) GET_EXP(ch)));
    strcpy (levelstr, comma_string ((int) GET_NEXT_LEVEL(ch)));

    sprintf(buf, "&+GTotal Experience: %s  &+LExperience to next level: %s&N\n",
            expstr, levelstr);
    send_to_char(buf, ch);
     */
    if (IS_PC(ch)) {
      sprintf(buf, "&+yExperience Progress: ");
      send_to_char(buf, ch);

      base = exp_table[GET_CLASS(ch)][GET_LEVEL(ch)]; /* min XP to curr. lvl */
      next = exp_table[GET_CLASS(ch)][GET_LEVEL(ch) + 1]; /* min XP to next lvl */
      diff = next - base; /* difference */
      progress = GET_EXP(ch) - base; /* how far along this level are we */
      unit = diff / 50; /* what one 'X' on our bar is equal to */
      bar = progress / unit; /* how many X's to display */

      if (bar > 49) /* for PCs at level 50 with mondo XP */
        bar = 49;

      str2[0] = '>';
      str2[1] = '\0';
      for (i = 0; i < bar; i++)
        strcat(str1, str2);

      str2[0] = ' ';
      for (; i < 50; i++)
        strcat(str1, str2);

      strcat(str1, str3);
      strcat(str1, "\n");

      send_to_char(str1, ch);
    }
  }

  /* money */
  sprintf(buf,
          "Coins carried: &+W%4d platinum&N  &+Y%4d gold&N  &n%4d silver&N  &+y%4d copper&N\n",
          GET_PLATINUM(ch), GET_GOLD(ch), GET_SILVER(ch), GET_COPPER(ch));
  send_to_char(buf, ch);
  buf[0] = 0;
  if (IS_PC(ch)) {
    money = getCarriedMoneyStruct(ch);
    if (money->total)
      sprintf(buf,
            "Coins in bags: &+W%4d platinum&N  &+Y%4d gold&N  &n%4d silver&N  &+y%4d copper&N\n",
            money->platinum, money->gold, money->silver, money->copper);
    sprintf(buf + strlen(buf),
            "Coins in bank: &+W%4d platinum&N  &+Y%4d gold&N  &n%4d silver&N  &+y%4d copper&N\n",
            GET_BALANCE_PLATINUM(ch), GET_BALANCE_GOLD(ch), GET_BALANCE_SILVER(ch), GET_BALANCE_COPPER(ch));
    send_to_char(buf, ch);

    // Prestige Listing -- CRM
    sprintf(buf, "&+BPrestige: %d\n", GET_PRESTIGE(ch));
    send_to_char(buf, ch);

    /* justice info */
    found = FALSE;
    /* causing grief. let's just show them the towns they are wanted/outlaw */
    strcpy(buf, "&+GCitizen of:   &N");
    for (i = 1; i <= LAST_HOME; i++)
      if ((PC_TOWN_JUSTICE_FLAGS(ch, i) == JUSTICE_IS_CITIZEN) && !IS_TOWN_INVADER(ch, i)) {
        found = TRUE;
        strcat(buf, town_name_list[i]);
        strcat(buf, ", ");
      }
    if (found) {
      buf[strlen(buf) - 2] = '\0';
      strcat(buf, "\n");
      send_to_char(buf, ch);
    }

    found = FALSE;

    strcpy(buf, "&+YWanted in:    &N");
    for (i = 1; i <= LAST_HOME; i++) {
      if ((PC_TOWN_JUSTICE_FLAGS(ch, i) == JUSTICE_IS_WANTED) && !IS_TOWN_INVADER(ch, i)) {
        found = TRUE;
        strcat(buf, town_name_list[i]);
        strcat(buf, ", ");
      }
    }
    if (found) {
      buf[strlen(buf) - 2] = '\0';
      strcat(buf, "\n");
      send_to_char(buf, ch);
    }
    found = FALSE;
    strcpy(buf, "&+ROutcast from: &N");
    for (i = 1; i <= LAST_HOME; i++)
      if ((PC_TOWN_JUSTICE_FLAGS(ch, i) == JUSTICE_IS_OUTCAST) && !IS_TOWN_INVADER(ch, i)) {
        found = TRUE;
        strcat(buf, town_name_list[i]);
        strcat(buf, ", ");
      }
    if (found) {
      buf[strlen(buf) - 2] = '\0';
      strcat(buf, "\n");
      send_to_char(buf, ch);
    }

    /* playing time */
    real_time_passed(&playing_time, (int) ((time(0) - ch->player.time.logon) + ch->player.time.played), 0);
    sprintf(buf, "Playing time: %d days / %d hours/ %d minutes\n",
            playing_time.day, playing_time.hour, playing_time.minute);
    send_to_char(buf, ch);

    /* title */
    sprintf(buf, "Title: %s\n", GET_TITLE(ch) ? GET_TITLE(ch) : "");

    /* poofin/poofout for gods */
    if (IS_TRUSTED(ch)) {
      if (ch->only.pc->poofIn == NULL)
        sprintf(buf + strlen(buf), "PoofIn:  None\n");
      else
        sprintf(buf + strlen(buf), "PoofIn:  %s\n", ch->only.pc->poofIn);

      if (ch->only.pc->poofOut == NULL)
        sprintf(buf + strlen(buf), "PoofOut: None\n");
      else
        sprintf(buf + strlen(buf), "PoofOut: %s\n", ch->only.pc->poofOut);
    }
  }

  /* group name, leader */
  if (GET_GROUP(ch)) {
    sprintf(buf + strlen(buf), "Group Name: %s\n", GET_GROUP_NAME(GET_GROUP(ch)));
    if (IS_AFFECTED(ch, AFF_CHARM) && ch->following && (ch->following != ch))
      sprintf(buf + strlen(buf), "Your Master: %s\n", C_NAME(GET_GROUP_LEADER(GET_GROUP(ch))));
    else
      sprintf(buf + strlen(buf), "Group Leader: %s\n", C_NAME(GET_GROUP_LEADER(GET_GROUP(ch))));
  }

  send_to_char(buf, ch);

  /* followers */
  found = FALSE;
  strcpy(buf, "Followers:\n");
  for (fol = ch->followers; fol; fol = fol->next) {
    if (WIZ_INVIS(ch, fol->follower)) /* Should not show up on "score" if a god is following. */
      continue;
    found = TRUE;
    sprintf(buf + strlen(buf), "   %s\n", (IS_NPC(fol->follower) ||
            IS_MORPH(fol->follower)) ? fol->follower->player.short_descr :
            GET_NAME(fol->follower));
  }
  if (found)
    send_to_char(buf, ch);

  /* status */
  switch (GET_STAT(ch)) {
    case STAT_DEAD:
      strcpy(buf, "Status:  How odd, you appear to be quite dead.");
      logit(LOG_DEBUG, "Deader in do_score().  %s (%d/%d).", GET_NAME(ch), GET_HIT(ch), GET_MAX_HIT(ch));
      statuslog(GET_LEVEL(ch), "%s is dead, but still in game.", GET_NAME(ch));
      break;
    case STAT_DYING:
      if (hit_regen(ch) > 0) {
        strcpy(buf, "Status:  Very badly wounded, but healing");
      } else
        strcpy(buf, "Status:  Bleeding to death");

      if (IS_FIGHTING(ch)) {
        logit(LOG_DEBUG, "%s dying, but still fighting %s.", GET_NAME(ch), GET_NAME(ch->specials.fighting));
        statuslog(GET_LEVEL(ch), "%s is dying, but still fighting %s.",
                GET_NAME(ch), GET_NAME(ch->specials.fighting));
        strcat(buf, ".");
      } else if (NumAttackers(ch) > 0) {
        strcat(buf, ", assuming you are allowed the time.");
      } else
        strcat(buf, ".");
      break;
    case STAT_INCAP:
      if (hit_regen(ch) > 0) {
        strcpy(buf, "Status:  Badly wounded, but healing");
      } else
        strcpy(buf, "Status:  Slowly bleeding to death");

      if (IS_FIGHTING(ch)) {
        logit(LOG_DEBUG, "%s incap, but still fighting %s.", GET_NAME(ch), GET_NAME(ch->specials.fighting));
        statuslog(GET_LEVEL(ch), "%s is incap, but still fighting %s.",
                GET_NAME(ch), GET_NAME(ch->specials.fighting));
        strcat(buf, ".");
      } else if (NumAttackers(ch) > 0) {
        strcat(buf, ", assuming you are allowed the time.");
      } else
        strcat(buf, ".");
      break;
    case STAT_SLEEPING:
      switch (GET_POS(ch)) {
        case POS_PRONE:
          if (IS_RIDING(ch)) {
            strcpy(buf, "Status:  Stretched out, fast asleep (while riding?)");
            logit(LOG_DEBUG, "%s prone/asleep, but still riding?", GET_NAME(ch));
            statuslog(GET_LEVEL(ch), "%s prone/asleep, but still riding?", GET_NAME(ch));
            send_to_char("It's kind of tough to ride while sleeping.\n", ch);
            stop_riding(ch);
          } else
            strcpy(buf, "Status:  Stretched out, fast asleep");
          break;
        case POS_SITTING:
          if (IS_RIDING(ch))
            strcpy(buf, "Status:  Asleep, in the saddle");
          else
            strcpy(buf, "Status:  Asleep, sitting up");
          break;
        case POS_KNEELING:
          if (IS_RIDING(ch))
            strcpy(buf, "Status:  Kneeling, fast asleep (while riding?)");
          else
            strcpy(buf, "Status:  Asleep, kneeling (?)");
          break;
        case POS_STANDING:
          if (IS_RIDING(ch))
            strcpy(buf, "Status:  Asleep, (standing in the saddle?)");
          else
            strcpy(buf, "Status:  Asleep on your feet (literally)");
          break;
      }

      if (IS_FIGHTING(ch)) {
        logit(LOG_DEBUG, "%s asleep, but still fighting %s.", GET_NAME(ch), GET_NAME(ch->specials.fighting));
        statuslog(GET_LEVEL(ch), "%s is asleep, but still fighting %s.",
                GET_NAME(ch), GET_NAME(ch->specials.fighting));
        strcat(buf, ".");
      } else if (NumAttackers(ch) > 0) {
        strcat(buf, ", but not for long.");
      } else
        strcat(buf, ".");
      break;
    case STAT_RESTING:
      switch (GET_POS(ch)) {
        case POS_PRONE:
          if (IS_RIDING(ch))
            strcpy(buf, "Status:  Stretched out in the saddle, resting (?)");
          else
            strcpy(buf, "Status:  Laying down, resting");
          break;
        case POS_KNEELING:
          if (IS_RIDING(ch))
            strcpy(buf, "Status:  Kneeling in the saddle (?), resting (?)");
          else
            strcpy(buf, "Status:  Resting on your hands and knees");
          break;
        case POS_SITTING:
          if (IS_RIDING(ch))
            strcpy(buf, "Status:  Resting in the saddle");
          else
            strcpy(buf, "Status:  Sitting around, resting");
          break;
        case POS_STANDING:
          if (IS_RIDING(ch))
            strcpy(buf, "Status:  Standing in the saddle (?), resting (?)");
          else
            strcpy(buf, "Status:  Standing, catching your breath");
          break;
      }
      if (IS_FIGHTING(ch)) {
        logit(LOG_DEBUG, "%s resting, but still fighting %s.", GET_NAME(ch), GET_NAME(ch->specials.fighting));
        statuslog(GET_LEVEL(ch), "%s is resting, but still fighting %s.",
                GET_NAME(ch), GET_NAME(ch->specials.fighting));
        strcat(buf, ".");
      } else if (NumAttackers(ch) > 0) {
        strcat(buf, ", but not for long.");
      } else
        strcat(buf, ".");
      break;
    case STAT_NORMAL:
      switch (GET_POS(ch)) {
        case POS_PRONE:
          if (IS_RIDING(ch))
            strcpy(buf, "Status:  Lying down (while riding?)");
          else
            strcpy(buf, "Status:  Lying down");
          break;
        case POS_KNEELING:
          if (IS_RIDING(ch))
            strcpy(buf, "Status:  Kneeling (while riding?)");
          else
            strcpy(buf, "Status:  Kneeling");
          break;
        case POS_SITTING:
          if (IS_RIDING(ch))
            sprintf(buf, "Status:  Sitting in %s's saddle", GET_MOUNT(ch)->player.short_descr);
          else
            strcpy(buf, "Status:  Sitting");
          break;
        case POS_STANDING:
          if (IS_RIDING(ch))
            sprintf(buf, "Status:  Standing in %s's saddle", GET_MOUNT(ch)->player.short_descr);
          else
            strcpy(buf, "Status:  Standing");
          break;
      }
      if (IS_FIGHTING(ch)) {
        sprintf(buf + strlen(buf), ", fighting %s.", PERS(ch->specials.fighting, ch));
      } else if (NumAttackers(ch) > 0) {
        strcat(buf, ", and getting beaten on.");
      } else
        strcat(buf, ".");
      break;
    default:
      strcpy(buf, "Status:  Beats the hell out of me, how did you DO that?");
      break;
  }

  strcat(buf, "\n");
  send_to_char(buf, ch);

  /* status type things */

  buf[0] = 0;

  if (IS_AFFECTED(ch, AFF_FIRESHIELD)) {
    if (affected_by_spell(ch, SPELL_COLDSHIELD))
      strcat(buf, " &+BCold Shielded&n");
    else if (affected_by_spell(ch, SPELL_UNHOLY_AURA))
      strcat(buf, " &+LUnholy Aura&N");
    else if (affected_by_spell(ch, SPELL_VAMPIRIC_CURSE))
      strcat(buf, " &+WVampiric Curse&N");
    else
      strcat(buf, " &+RFire Shielded&n");
  }
  if (IS_AFFECTED(ch, AFF_CAMPING))
    strcat(buf, " Camping");
  if (ch->song_singing)
    strcat(buf, " Singing");
  if (ch->accompany) {
    buf2[0] = 0;
    sprintf(buf2, " Accompanying: %s", ch->accompany->player.name);
    strcat(buf, buf2);
  }
  if (IS_AFFECTED(ch, AFF_VIRTUOSO))
    strcat(buf, " Virtuoso");
  if (IS_AFFECTED(ch, AFF_MEMORIZING)) {
    if (meming_class(GET_CLASS(ch)))
      strcat(buf, " Memorizing");
    else
      strcat(buf, " Praying");
    if (IS_AFFECTED(ch, AFF_MEDITATE))
      strcat(buf, " Meditating");
    if (IS_AFFECTED(ch, AFF_CHARGING))
      strcat(buf, " Charging");
  }
  if (IS_AFFECTED(ch, AFF_KNOCKED_OUT))
    strcat(buf, " Unconscious");
  if (IS_AFFECTED(ch, AFF_BOUND))
    strcat(buf, " Restrained");
  if (IS_AFFECTED(ch, AFF_STUNNED))
    strcat(buf, " Stunned");
  if (IS_AFFECTED(ch, AFF_RES_PENALTY))
    strcat(buf, " Resurrection Fatigue");


  if (*buf) {
    sprintf(Gbuf1, "        %s\n", buf);
    send_to_char(Gbuf1, ch);
  }

  if (IS_DISGUISE(ch)) {
    sprintf(buf, "Disguise: %s\n", ch->disguise.name);
    send_to_char(buf, ch);
  }

  buf[0] = 0;

  if (IS_AFFECTED(ch, AFF_DETECT_INVISIBLE))
    strcat(buf, " Invisible");
  if (IS_AFFECTED(ch, AFF_DETECT_EVIL))
    strcat(buf, " Evil");
  if (IS_AFFECTED(ch, AFF_DETECT_GOOD))
    strcat(buf, " Good");
  if (IS_AFFECTED(ch, AFF_DETECT_MAGIC))
    strcat(buf, " Magic");
  if (IS_AFFECTED(ch, AFF_SENSE_LIFE))
    strcat(buf, " Life");
  if (IS_AFFECTED(ch, AFF_INFRAVISION))
    strcat(buf, " Heat");

  if (*buf) {
    sprintf(Gbuf1, "Detecting:      %s\n", buf);
    send_to_char(Gbuf1, ch);
  }
  buf[0] = 0;

  if (IS_AFFECTED(ch, AFF_DETECT_MAGIC) || (GET_LEVEL(ch) > MAXLVLMORTAL)) {
    if (IS_AFFECTED(ch, AFF_PROTECT_EVIL))
      strcat(buf, " Evil");
    if (IS_AFFECTED(ch, AFF_PROTECT_GOOD))
      strcat(buf, " Good");
    if (IS_AFFECTED(ch, AFF_PROT_FIRE))
      strcat(buf, " Fire");
    if (IS_AFFECTED(ch, AFF_PROT_COLD))
      strcat(buf, " Cold");
    if (IS_AFFECTED(ch, AFF_PROT_LIGHTNING))
      strcat(buf, " Lightning");
    if (IS_AFFECTED(ch, AFF_PROT_GAS))
      strcat(buf, " Gas");
    if (IS_AFFECTED(ch, AFF_PROT_ACID))
      strcat(buf, " Acid");
    if (IS_AFFECTED(ch, AFF_BODY_CONTROL))
      strcat(buf, " Environment");
  }
  if (IS_AFFECTED(ch, AFF_MINOR_GLOBE))
    strcat(buf, " Low Circle spells");
  if (IS_AFFECTED(ch, AFF_GLOBE))
    strcat(buf, " All but High Circle Spells");

  if (*buf) {
    sprintf(Gbuf1, "Protected from: %s\n", buf);
    send_to_char(Gbuf1, ch);
  }
  buf[0] = 0;

  if (IS_AFFECTED(ch, AFF_METAGLOBE)) {
    for (aff = ch->affected; aff; aff = aff->next) {
      if (aff->type == SKILL_METAGLOBE) {
        break;
      }
    }
    if (aff->type != SKILL_METAGLOBE)
      dump_core();
    for (i = 0; i < MAX_CIRCLE; i++) {
      if (IS_SET(aff->modifier, (1 << i)))
        sprintf(buf + strlen(buf), " %d", i + 1); /* prints circles against which MG doesnt have any effect */
    }
  }

  if (*buf) {
    sprintf(Gbuf1, "Metaglobe NOT active for circles: %s\n", buf);
    send_to_char(Gbuf1, ch);
  }
  buf[0] = 0;

  if (IS_AFFECTED(ch, AFF_BLUR))
    strcat(buf, " Blur");
  if (IS_AFFECTED(ch, AFF_DISPLACEMENT))
    strcat(buf, " Displacement");
  if (IS_AFFECTED(ch, AFF_ELEMENTAL_FIRE))
    strcat(buf, " Elemental Fire");
  if (IS_AFFECTED(ch, AFF_ELEMENTAL_AIR))
    strcat(buf, " Elemental Air");
  if (IS_AFFECTED(ch, AFF_ELEMENTAL_WATER))
    strcat(buf, " Elemental Water");
  if (IS_AFFECTED(ch, AFF_ELEMENTAL_EARTH))
    strcat(buf, " Elemental Earth");
  if (IS_AFFECTED(ch, AFF_ELEMENTAL_WARD))
    strcat(buf, " Elemental Ward");
  if (IS_AFFECTED(ch, AFF_FARSEE))
    strcat(buf, " Farsee");
  if (IS_AFFECTED(ch, AFF_FLY))
    strcat(buf, " Fly");
  if (IS_AFFECTED(ch, AFF_HASTE) ||
          (affected_by_song(ch, SONG_OF_OFFENSIVE_HARMONY) && ch->bard_singing->song_singing->modifier >= 6))
    strcat(buf, " Haste");
  if (IS_AFFECTED(ch, AFF_INVISIBLE) && IS_AFFECTED(ch, AFF_DETECT_MAGIC))
    strcat(buf, " Invisibility");
  if (IS_AFFECTED(ch, AFF_LEVITATE))
    strcat(buf, " Levitation");
  if (IS_AFFECTED(ch, AFF_MIND_BLANK))
    strcat(buf, " Mind Blank");
  if (IS_AFFECTED(ch, AFF_NONDETECTION))
    strcat(buf, " Nondetection");
  if (IS_AFFECTED(ch, AFF_REPULSION))
    strcat(buf, " Repulsion");
  if (IS_AFFECTED(ch, AFF_SEQUESTER))
    strcat(buf, " Sequester");
  if (IS_AFFECTED(ch, AFF_ULTRAVISION))
    strcat(buf, " Ultravision");
  if (IS_AFFECTED(ch, AFF_WATERBREATH))
    strcat(buf, " Waterbreath");

  if (*buf) {
    sprintf(Gbuf1, "Enchantments:   %s\n", buf);
    send_to_char(Gbuf1, ch);
  }
  buf[0] = 0;


  if (IS_AFFECTED(ch, AFF_SLOW))
    strcat(buf, " Slowness");
  if (IS_AFFECTED(ch, AFF_BLIND))
    strcat(buf, " Blindness");
  if (IS_AFFECTED(ch, AFF_BURNING))
    strcat(buf, " Burning");
  if (IS_AFFECTED(ch, AFF_FEAR))
    strcat(buf, " Fear");
  if (IS_AFFECTED(ch, AFF_MAJOR_PARALYSIS))
    strcat(buf, " Total Paralysis");
  if (IS_AFFECTED(ch, AFF_MINOR_PARALYSIS))
    strcat(buf, " Paralysis");
  if (IS_AFFECTED(ch, AFF_BLACKMANTLE))
    strcat(buf, " Blackmantle");

  if (*buf) {
    sprintf(Gbuf1, "Afflicted with: %s\n", buf);
    send_to_char(Gbuf1, ch);
  }
  buf[0] = 0;

  /* loop through affected list, show them the ones they can see, if affected
     by detect magic, show remaining durations too. JAB */

  if (ch->affected) {
    for (aff = ch->affected; aff; aff = aff->next)
      if (aff->type && (aff->type <= MAX_SKILLS)) {
        switch (aff->type) {
          case SKILL_AWARENESS:
          case SKILL_DISARM_DROPPED_WEAP:
          case SKILL_DISARM_FUMBLING_WEAP:
          case SKILL_SELF_PRESERVATION:
          case SKILL_HEADBUTT:
          case SKILL_SNEAK:
          case SPELL_RECHARGER:
          case SKILL_CAMP:
          case SPELL_SUMMON:
          case SKILL_SCRIBE:
          case SKILL_CON_BONUS:
          case SKILL_RES_PENALTY:
            /* these are never reported */
            continue;
            break;

          case SKILL_HEROISM:
          case SONG_CHARMING:
          case SPELL_CHARM_PERSON:
          case SPELL_PROTECT_FROM_ACID:
          case SPELL_PROTECT_FROM_COLD:
          case SPELL_PROTECT_FROM_EVIL:
          case SPELL_PROTECT_FROM_FIRE:
          case SPELL_PROTECT_FROM_GAS:
          case SPELL_PROTECT_FROM_GOOD:
          case SPELL_PROTECT_FROM_LIGHTNING:
          case SPELL_PROT_FROM_UNDEAD:
          case SPELL_PROT_UNDEAD:
          case SPELL_SLOW_POISON:
          case SPELL_VAMPIRIC_TOUCH:
            /* these get reported, only if detect magic active */
            if (!IS_AFFECTED(ch, AFF_DETECT_MAGIC))
              continue;
            break;

            /* following spells have two affect structures, so ignore one or
               they get listed twice. */
          case SPELL_WITHER:
            if (aff->location == APPLY_MOVE)
              continue;
            break;
          case SPELL_BLESS:
            if (aff->location == APPLY_HITROLL)
              continue;
            break;
          case SPELL_CURSE:
            if (!IS_AFFECTED(ch, AFF_DETECT_MAGIC) || (aff->location == APPLY_HITROLL))
              continue;
            break;
          case SPELL_FEEBLEMIND:
            if (aff->location == APPLY_INT)
              continue;
            break;

          case SPELL_ELEMENTAL_FIRE:
            if (aff->location != APPLY_HIT)
              continue;
            break;

          case SPELL_ELEMENTAL_AIR:
            if (aff->location != APPLY_HIT)
              continue;
            break;

          case SPELL_ELEMENTAL_WATER:
            if (aff->location != APPLY_HIT)
              continue;
            break;

          case SPELL_ELEMENTAL_EARTH:
            if (aff->location != APPLY_HIT)
              continue;
            break;

          case SPELL_BLUR:
            if (aff->location != APPLY_SAVING_SPELL)
              continue;
            break;

          case SKILL_VIPER_MIND:
            if (aff->location == APPLY_HITROLL)
              continue;
            break;

          case SKILL_EXPANSION:
          case SKILL_REDUCTION:
          case SPELL_ENLARGE:
          case SPELL_REDUCE:
            if (aff->location != APPLY_CHAR_HEIGHT)
              continue;
            break;

          case SKILL_BERSERK:
            if (aff->location != APPLY_STR_MAX)
              continue;
            break;
          case SKILL_COMBATMIND:
            if (aff->location != APPLY_DAMROLL)
              continue;
            break;
          case SKILL_ADRENALIZE:
            if (aff->location != APPLY_CON)
              continue;
            break;
          case SKILL_BATTLE_TRANCE:
            if (aff->location != APPLY_HITROLL)
              continue;
            break;
          case SPELL_CASTER_STONE:
            continue;
            break;
          case SPELL_CASTER_SCALE:
            continue;
            break;
          case BARD_PROTECTION:
            continue;
            break;

          default:
            /* rest are reported always */
            break;
        }

        strcat(buf, spells[aff->type - 1]);
        if (!IS_AFFECTED(ch, AFF_DETECT_MAGIC) || (aff->duration > 1))
          strcat(buf, "\n");
        else
          strcat(buf, " (fading)\n");
      }
    if (*buf && !affected_by_spell(ch, SPELL_FEEBLEMIND)) {
      send_to_char("\nActive Spells:\n--------------\n", ch);

      send_to_char(buf, ch);
    }
  }
  send_to_char("\n", ch);
}

void do_time(P_char ch, char *argument, int cmd) {
  char *tmstr, Gbuf1[MAX_STRING_LENGTH], Gbuf2[MAX_STRING_LENGTH];
  const char *suf;
  int weekday, day;
  time_t ct;
  struct time_info_data uptime;
  struct tm *lt;

  if (IS_TRUSTED(ch))
    sprintf(Gbuf1, ":%s%d", ((pulse / 4) > 9) ? "" : "0", pulse / 4);
  else
    Gbuf1[0] = 0;

  sprintf(Gbuf2, "It is %d%s%s, on ",
          (time_info.hour % 12) ? (time_info.hour % 12) : 12, Gbuf1,
          (time_info.hour > 11) ? "pm" : "am");

  /* 35 days in a month */
  weekday = ((35 * time_info.month) + time_info.day + 1) % 7;

  strcat(Gbuf2, weekdays[weekday]);
  strcat(Gbuf2, "\n");
  send_to_char(Gbuf2, ch);

  day = time_info.day + 1; /* day in [1..35] */

  if (day == 1)
    suf = "st";
  else if (day == 2)
    suf = "nd";
  else if (day == 3)
    suf = "rd";
  else if (day < 20)
    suf = "th";
  else if ((day % 10) == 1)
    suf = "st";
  else if ((day % 10) == 2)
    suf = "nd";
  else if ((day % 10) == 3)
    suf = "rd";
  else
    suf = "th";

  sprintf(Gbuf2, "The %d%s Day of the %s, Year %d.\n",
          day, suf, month_name[time_info.month], time_info.year);
  send_to_char(Gbuf2, ch);

  ct = time(0);
  real_time_passed(&uptime, ct, boot_time);
  sprintf(Gbuf2, "Time elapsed since boot-up: %d:%s%d:%s%d\n", uptime.day * 24 + uptime.hour,
          (uptime.minute > 9) ? "" : "0", uptime.minute, (uptime.second > 9) ? "" : "0", uptime.second);
  send_to_char(Gbuf2, ch);

  lt = localtime(&ct);
  if (!lt) {
    send_to_char("Error: System time unavailable.\n", ch);
    return;
  }
  tmstr = asctime(lt);
  *(tmstr + strlen(tmstr) - 1) = '\0';
  sprintf(Gbuf2, "Current time is: %s (%s)\n", tmstr, (lt->tm_isdst <= 0) ? "EST" : "EDT");
  send_to_char(Gbuf2, ch);
}

void do_weather(P_char ch, char *argument, int cmd) {
  char buf[MAX_STRING_LENGTH];
  int seas;
  struct climate *clime;
  struct weather_data *cond;


  clime = &zone_table[world[ch->in_room].zone].climate;
  cond = &zone_table[world[ch->in_room].zone].conditions;

  if (IS_TRUSTED(ch)) {
    seas = get_season(world[ch->in_room].zone);
    send_to_char("Weather Dump\n   Season Chars: ", ch);

    send_to_char(season_patterns[clime->season_pattern - 1], ch);
    sprintf(buf, "(#%d)\n                 ", seas);
    send_to_char(buf, ch);
    send_to_char(wind_patterns[clime->season_wind[seas] - 1], ch);
    if (clime->season_wind_variance[seas - 1])
      send_to_char("variable. ", ch);
    else {
      send_to_char("from ", ch);
      send_to_char(wind_dir[(int) clime->season_wind_dir[seas]], ch);
    }
    send_to_char(precip_patterns[clime->season_precip[seas] - 1], ch);
    send_to_char(temp_patterns[clime->season_temp[seas] - 1], ch);

    sprintf(buf, "\nTemp: %d  Humidity: %d  Pressure: %d\n", cond->temp, cond->humidity, cond->pressure);

    send_to_char(buf, ch);

    sprintf(buf, "Windspeed: %d  Direction: %d  Precip Rate: %d\n",
            cond->windspeed, cond->wind_dir, cond->precip_rate);

    send_to_char(buf, ch);

    sprintf(buf, "Light: %d  Energy: %d  Pressure change: %d  Precip change: %d\n",
            cond->ambient_light, cond->free_energy, cond->pressure_change, cond->precip_change);

    send_to_char(buf, ch);
  }

  if (!OUTSIDE(ch)) {
    send_to_char("How can you know what the weather's like when you are inside?\n", ch);
    return;
  }
  buf[0] = 0;

  if (cond->precip_rate) {
    if (cond->temp <= 0)
      strcat(buf, "It's snowing");
    else
      strcat(buf, "It's raining");
    if (cond->precip_rate > 65)
      strcat(buf, " extremely hard");
    else if (cond->precip_rate > 50)
      strcat(buf, " very hard");
    else if (cond->precip_rate > 30)
      strcat(buf, " hard");
    else if (cond->precip_rate < 15)
      strcat(buf, " lightly");
    strcat(buf, ", ");
  } else {
    if (cond->humidity > 80)
      strcat(buf, "It's very cloudy, ");
    else if (cond->humidity > 55)
      strcat(buf, "It's cloudy, ");
    else if (cond->humidity > 25)
      strcat(buf, "It's partly cloudy, ");
    else if (cond->humidity)
      strcat(buf, "It's mostly clear, ");
    else
      strcat(buf, "It's clear, ");
  }

  if (cond->temp > 100)
    strcat(buf, "boiling, ");
  else if (cond->temp > 80)
    strcat(buf, "blistering, ");
  else if (cond->temp > 50)
    strcat(buf, "incredibly hot, ");
  else if (cond->temp > 40)
    strcat(buf, "very, very hot, ");
  else if (cond->temp > 30)
    strcat(buf, "very hot, ");
  else if (cond->temp > 24)
    strcat(buf, "hot, ");
  else if (cond->temp > 18)
    strcat(buf, "warm, ");
  else if (cond->temp > 9)
    strcat(buf, "mild, ");
  else if (cond->temp > 3)
    strcat(buf, "cool, ");
  else if (cond->temp > -1)
    strcat(buf, "cold, ");
  else if (cond->temp > -10)
    strcat(buf, "freezing, ");
  else if (cond->temp > -25)
    strcat(buf, "well past freezing, ");
  else
    strcat(buf, "numbingly frozen, ");

  strcat(buf, "and ");

  if (cond->windspeed <= 0)
    strcat(buf, "there is absolutely no wind");
  else if (cond->windspeed < 10)
    strcat(buf, "calm");
  else if (cond->windspeed < 20)
    strcat(buf, "breezy");
  else if (cond->windspeed < 35)
    strcat(buf, "windy");
  else if (cond->windspeed < 50)
    strcat(buf, "very windy");
  else if (cond->windspeed < 70)
    strcat(buf, "very, very windy");
  else if (cond->windspeed < 100)
    strcat(buf, "there is a gale blowing");
  else
    strcat(buf, "the wind is unbelievable");
  strcat(buf, ".\n");
  send_to_char(buf, ch);
#if 0
  if (GET_CLASS(ch) == CLASS_CLERIC ||
          GET_CLASS(ch) == CLASS_RANGER ||
          GET_CLASS(ch) == CLASS_MONK ||
          GET_CLASS(ch) == CLASS_DRUID ||
          GET_CLASS(ch) == CLASS_SHAMAN ||
          GET_CLASS(ch) == CLASS_PALADIN ||
          GET_CLASS(ch) == CLASS_ANTIPALADIN ||
          GET_CLASS(ch) == CLASS_NECROMANCER ||
          GET_CLASS(ch) == CLASS_CONJURER ||
          GET_CLASS(ch) == CLASS_SORCERER) {

  }
#endif
  /*
  if (GET_CLASS(ch) == CLASS_PSIONICIST)  {
    if (cond->free_energy > 40000)
      send_to_char("Wow! This place is bursting with energy!\n", ch);
    else if (cond->free_energy > 30000)
      send_to_char("The environs tingle your magical senses.\n", ch);
    else if (cond->free_energy > 20000)
      send_to_char("The area is rich with energy.\n", ch);
    else if (cond->free_energy < 4000)
      send_to_char("There is almost no magical energy here.\n", ch);
    else if (cond->free_energy < 5000)
      send_to_char("Your magical senses are dulled by the scarceness of energy here.\n", ch);
  }
   */
  send_to_char("\n", ch);
}

void do_whelp(P_char ch, char *argument, int cmd) {
  char buf[MAX_STRING_LENGTH], buffer[MAX_STRING_LENGTH];
  int chk, i, j;
  uint minlen;

  if (!ch->desc)
    return;

  for (; isspace(*argument); argument++);

  if (!*argument) {
    send_to_char("Yes, get wizhelp we must, but wizhelp on what?\n", ch);
    return;
  }

  if (!whelp_index) {
    send_to_char("No wizhelp available.\n", ch);
    return;
  }

  debuglog(51, DS_SHEVARASH, "wizhelp arg: %s", argument);

  j = BOUNDED(0, (LOWER(*argument) - 'a' + 1), 27);
  if ((j > 26) || (whelp_array[j][0] == -1)) {
    send_to_char("There is no wizhelp found on that word.\n", ch);
    return;
  }
  minlen = strlen(argument);

  for (i = whelp_array[j][0]; i < whelp_array[j][1]; i++) {
    if (!(chk = strn_cmp(argument, whelp_index[i].keyword, minlen))) {
      fseek(whelp_fl, whelp_index[i].pos, 0);
      *buffer = '\0';
      for (;;) {
        fgets(buf, 80, whelp_fl);
        if (*buf == '#')
          break;
        strcat(buffer, buf);
      }
      /* Commented out some code here to allow paging during char gen WAD Vaprak
            if (cmd == CMD_HELP) */
      page_string(ch->desc, buffer, 1);
      /*      else
              send_to_char(buffer, ch);
       */
      return;
    }
  }

  send_to_char("There is no wizhelp on that word.\n", ch);
}

void do_help(P_char ch, char *argument, int cmd) {
  char buf[MAX_STRING_LENGTH], buffer[MAX_STRING_LENGTH];
  int chk, i, j;
  uint minlen;

  if (!ch->desc)
    return;

  for (; isspace(*argument); argument++);

  if (!*argument) {
    if (ch->desc->term_type == TERM_ANSI)
      send_to_char(helpa, ch);
    else
      send_to_char(help, ch);
    return;
  }
  if (!help_index) {
    send_to_char("No help available.\n", ch);
    return;
  }
  j = BOUNDED(0, (LOWER(*argument) - 'a' + 1), 27);
  if ((j > 26) || (help_array[j][0] == -1)) {
    send_to_char("There is no help on that word.\n", ch);
    return;
  }
  minlen = strlen(argument);

  for (i = help_array[j][0]; i < help_array[j][1]; i++) {
    if (!(chk = strn_cmp(argument, help_index[i].keyword, minlen))) {
      fseek(help_fl, help_index[i].pos, 0);
      *buffer = '\0';
      for (;;) {
        fgets(buf, 80, help_fl);
        if (*buf == '#')
          break;
        strcat(buffer, buf);
      }
      /* Commented out some code here to allow paging during char gen WAD Vaprak
            if (cmd == CMD_HELP) */
      page_string(ch->desc, buffer, 1);
      /*      else
              send_to_char(buffer, ch);
       */
      return;
    }
  }

  send_to_char("There is no help on that word.\n", ch);
}

void do_info(P_char ch, char *argument, int cmd) {
  char buf[MAX_STRING_LENGTH], buffer[MAX_STRING_LENGTH];
  int chk, i, j;
  uint minlen;

  if (!ch->desc)
    return;

  for (; isspace(*argument); argument++);

  if (!*argument) {
    if (ch->desc->term_type == TERM_ANSI)
      send_to_char(infoa, ch);
    else
      send_to_char(info, ch);
    return;
  }
  if (!info_index) {
    send_to_char("No info available.\n", ch);
    return;
  }
  j = BOUNDED(0, (LOWER(*argument) - 'a' + 1), 27);
  if ((j > 26) || (info_array[j][0] == -1)) {
    send_to_char("There is no info on that word.\n", ch);
    return;
  }
  minlen = strlen(argument);

  for (i = info_array[j][0]; i < info_array[j][1]; i++) {
    if (!(chk = strn_cmp(argument, info_index[i].keyword, minlen))) {
      fseek(info_fl, info_index[i].pos, 0);
      *buffer = '\0';
      for (;;) {
        fgets(buf, 80, info_fl);
        if (*buf == '#')
          break;
        strcat(buffer, buf);
      }
      if (cmd == CMD_INFO)
        page_string(ch->desc, buffer, 1);
      else
        send_to_char(buffer, ch);
      return;
    }
  }

  send_to_char("There is no info on that word.\n", ch);
}

void do_wizhelp(P_char ch, char *argument, int cmd) {
  char buf[MAX_STRING_LENGTH];
  int no, i, j, found;

  if (IS_NPC(ch))
    return;

  if (!IS_TRUSTED(ch)) {
    send_to_char("Duh???\n", ch);
    return;
  }

  // Send it of to do_whelp if we have an argu
  if (*argument) {
    if (!strcmp(argument, " ?")) {
      do_whelp(ch, "wizhelp ?", cmd);
    } else {
      do_whelp(ch, argument, cmd);
    }
    return;
  }

  // FIX Hrm, two lines?  Do these actually show up? -Urdlen
  send_to_char("The following privileged commands are available to you:\n\n", ch);

  send_to_char("\nThe following commands have been granted to you:\n\n", ch);
  *buf = '\0';

  for (no = 0, i = 1; *command[i] != '\n'; i++) {
    found = 0;
    if (cmd_info[i].minimum_level > MAXLVLMORTAL) {
      if (!cmd_info[i].grantable) {
        if (cmd_info[i].minimum_level <= GET_LEVEL(ch)) {
          sprintf(buf + strlen(buf), "[&+y%2d&n] &+c%-14s&n",
                  cmd_info[i].minimum_level, command[i - 1]);
          no++;
          found = 1;
        }
      } else {
        j = cmd_info[i].grantable;
        if (IS_CSET(GRANT_FLAGS(ch), j + 1)) {
          //                      ((j < 32) && IS_SET(GRANT_FLAGS(ch), 1 << j)) ||
          //          ((j > 31) && IS_SET(GRANT_FLAGS2(ch), 1 << (j - 32)))) {
          if (cmd_info[i].minimum_level <= GET_LEVEL(ch))
            sprintf(buf + strlen(buf), "[&+y%2d&n] &+c%-14s&n",
                  cmd_info[i].minimum_level, command[i - 1]);
          else
            sprintf(buf + strlen(buf), "[&+b G&n] &+c%-14s&n", command[i - 1]);
          no++;
          found = 1;
        }
      }
    }
    if (found && !(no % 4))
      strcat(buf, "\n");
  }
  if (*buf) {
    strcat(buf, "\n&+cFor an index of availble wizhelp, type \"&N&+wwizhelp ?&N&+c\"\n");
    page_string(ch->desc, buf, 1);
  } else
    send_to_char("None, go away.\n", ch);
}

/* The code here after concerning do_who was coded by Silvanus 1994-06-30
   for OutcastMUD. I hope it will be appreciated.

   This should make up for all the different demands the players might
   have on a good who code, if not I'll just add that part too :-)       */

void set_who_flags(P_char ch, int nr_args_now, struct who_flags *flags) {
  char buf[MAX_STRING_LENGTH];
  int i;

  strcpy(buf, "");
  for (i = 0; (i < nr_args_now) && (i < NUM_FIELDS); i++) {

    switch (cmd_nr[i]) {
      case 0: /*level argument */
        strcat(buf, "level argument...");
        break;
      case 1: /*gods */
        flags->GODS_ONLY = TRUE;
        flags->low = 51;
        flags->high = 60;
        break;
      case 2: /*warrior */
      case 3: /*ranger */
      case 4: /*berserker */
      case 5: /*paladin */
      case 6: /*antipaladin */
      case 7: /*cleric */
      case 8: /*monk */
      case 9: /*druid */
      case 10: /*shaman */
      case 11: /*sorcerer */
      case 12: /*necromancer */
      case 13: /*conjurer */
      case 14: /*thief */
      case 15: /*assassin */
      case 16: /*mercenary */
      case 17: /*bard */
      case 18: /*psionicist */
      case 19: /* enchanter */
      case 20: /* invoker */
      case 21:
      case 22: /* illusionist */
      case 23: /* battlechanter */
      case 24: /* rogue */
        flags->CLASS |= (1 << (cmd_nr[i] - 1));
        flags->CLASS_O = TRUE;
        break;

      case 25: /*human */
      case 26: /*barbarian */
      case 27: /*drow elf */
      case 28: /*grey elf */
      case 29: /*mountain dwarf */
      case 30: /*duergar dwarf */
      case 31: /*halfling */
      case 32: /*gnome */
      case 33: /*ogre */
      case 34: /*swamp troll or troll */
      case 35: /*half elf */
      case 36: /*illithid*/
      case 37: /*yuan-ti*/
      case 38: /*lich*/
      case 40: /*Orc*/
        flags->RACE |= (1 << (cmd_nr[i] - 24));
        flags->RACE_O = TRUE;
        break;
      case 45: /*sort */
        flags->SORT = TRUE;
        break;
      case 46: /*outlaws */
        flags->OUTLAW = TRUE;
        break;
      case 47: /*short */
        flags->short_list = TRUE;
        break;
      case 48: /*zone */
        if (GET_LEVEL(ch) > MAXLVLMORTAL)
          flags->ZONE = TRUE;
        else
          send_to_char("Sorry, 'who zone' is for &+rimmortals only.&n\n", ch);
        break;
      case 49: /*grouped */
        flags->GROUPED = TRUE;
        break;
      case 50: /*leaders */
        flags->LEADERS = TRUE;
        break;
      case 51: /*annonymous */
        flags->ANONYMOUS = TRUE;
        break;
      case 52: /*gods (staff option) */
        flags->GODS_ONLY = TRUE;
        flags->low = 51;
        flags->high = 60;
        break;
      case 53:
        flags->PKILLERS = TRUE;
        break;
      case 54:
        flags->HELPERS = TRUE;
        break;
      case 55: /*ungrouped */
        flags->UNGROUPED = TRUE;
        break;
      case 56: /* elementalist */
        flags->CLASS |= (1 << 24);
        flags->CLASS_O = TRUE;
        break;
      case 57:
        flags->GOODRACE = TRUE;
        break;
      case 58:
        flags->EVILRACE = TRUE;
        break;
      case 59:
        flags->INGROUP = TRUE; /* who's in your group? */
        break;
      case 60:
        flags->CLASS |= (1 << 25);
        flags->CLASS_O = TRUE; /* Dire Raider*/
        break;
      case 61:
        flags->LFG = TRUE; /* who's looking for a group? */
        break;
      default: /*invalid option */
        logit(LOG_STATUS, "wrong WHO option caused default option. (actinf.c)");
        break;
    }
  }
  if (IS_TRUSTED(ch))
    return;

  if (flags->GROUPED && flags->UNGROUPED) {
    send_to_char("Grouped and Ungrouped are mutually exclusive, cancelling grouped.\n", ch);
    return;
  }

  if (flags->ANONYMOUS) {
    if ((flags->high < (MAXLVL + 1)) || (flags->low > 0)) {
      send_to_char("Anon and level specifications are mutually exclusive, cancelling Anon.\n", ch);
      flags->ANONYMOUS = FALSE;
      return;
    }
    if (flags->SORT) {
      send_to_char("Sort and Anon are mutually exclusive, cancelling Anon.\n", ch);
      flags->ANONYMOUS = FALSE;
    }
    if (flags->CLASS_O) {
      send_to_char("Specifying classes and Anon are mutually exclusive, cancelling Anon.\n", ch);
      flags->ANONYMOUS = FALSE;
    }
    if (flags->HELPERS) {
      send_to_char("Specifying helpers and Anon are mutually exclusive, cancelling Anon.\n", ch);
      flags->ANONYMOUS = FALSE;
    }
  }
}

int make_list(P_char ch, struct who_flags flags) {
  P_desc d;
  char buf[MAX_STRING_LENGTH];
  int who_gods_size = 1, who_list_size = 0;
  /* int found;             see below commented code -Azuth
     P_group group = NULL;
     P_gmember member = NULL;*/

  strcpy(buf, "");

  if (flags.INGROUP) {
    if (!GET_GROUP(ch))
      return 1000;
  }

  for (d = descriptor_list; d; d = d->next) {
    if (d->connected || !d->character || IS_NPC(d->character))
      continue;

    if (IS_DISGUISE(d->character) && !IS_TRUSTED(ch))
      continue;

    if (flags.ANONYMOUS && !IS_CSET(d->character->only.pc->pcact, PLR_ANONYMOUS))
      continue;

    if (flags.GOODRACE && !RACE_GOOD(d->character))
      continue;

    if (flags.EVILRACE && !RACE_EVIL(d->character))
      continue;

    if (flags.INGROUP) {
      if (!ARE_GROUPED(ch, d->character))
        continue;
      /* Azuth really wants to know why the below code didn't work
      found = FALSE;
      group = GET_GROUP(ch);
      for(member = group->members; member; member = member->next)
         {
         debuglog(51, DS_AZUTH, "checking group member [%s]", C_NAME(member->this));
         if(member->this == d->character);
            {
            debuglog(51, DS_AZUTH, "group member [%s] matched loop char [%s]", C_NAME(member->this), C_NAME(d->character));
            found = TRUE;
            break;
            }
         }

      if(found == FALSE)
         {
         debuglog(51, DS_AZUTH, "excluding [%s]", C_NAME(d->character));
         continue;
         }*/
    }

    if (flags.RACE_O) {
      if (!IS_SET(flags.RACE, (1 << GET_RACE(d->character))))
        continue;
    }

    if (flags.CLASS_O)
      if (!IS_SET(flags.CLASS, (1 << GET_CLASS(d->character))) ||
              (IS_CSET(d->character->only.pc->pcact, PLR_ANONYMOUS) && !IS_TRUSTED(ch) && (ch != d->character)))
        continue;

    if ((flags.GROUPED) && (!GET_GROUP(d->character)))
      continue;

    if ((flags.UNGROUPED) && (GET_GROUP(d->character)))
      continue;

    if ((flags.LEADERS) && (!d->character->followers))
      continue;

    if (flags.ZONE)
      if (world[d->character->in_room].zone != world[ch->in_room].zone)
        continue;

    if (IS_CSET(d->character->only.pc->pcact, PLR_ANONYMOUS) && !IS_TRUSTED(ch) && (ch != d->character) &&
            !(flags.INGROUP && ARE_GROUPED(ch, d->character)) && (flags.SORT || flags.CLASS_O || (flags.high < FORGER) || (flags.low > 1)))
      continue;

    if ((GET_LEVEL(d->character) > flags.high) || (GET_LEVEL(d->character) < flags.low))
      continue;

    if (flags.PKILLERS && (IS_CSET(ch->only.pc->pcact, PLR_NO_KILL) || IS_CSET(d->character->only.pc->pcact, PLR_NO_KILL)))
      continue;

    if (flags.HELPERS && (!IS_CSET(d->character->only.pc->pcact, PLR_HELPER)))
      continue;

    if (flags.LFG && (!IS_CSET(d->character->only.pc->pcact, PLR_LFG)))
      continue;

    if ((CAN_SEE(ch, d->character)) && (IS_PC(d->character))) {
      if (GET_LEVEL(d->character) > MAXLVLMORTAL)
        who_gods[who_gods_size++] = d->character;
      else
        who_list[who_list_size++] = d->character;
    }
  }

  who_list_size = (who_list_size + (who_gods_size * 1000));
  return (who_list_size);
}

struct who_struct {
  const char *options;
  int number;
};

static struct who_struct fields[] = {
  { "Name", 0},
  { "low-high", 0},
  { "low-", 0},
  { "-high", 0},
  { "gods", 1},
  { "sort", 45},
  { "outlaws", 46},
  { "short", 47},
  { "zone", 48},
  { "grouped", 49},
  { "leaders", 50},
  { "anonymous", 51},
  { "staff", 52},
  { "warrior", 2},
  { "ranger", 3},
  { "berserker", 4},
  { "paladin", 5},
  { "antipaladin", 6},
  { "cleric", 7},
  { "monk", 8},
  { "druid", 9},
  { "shaman", 10},
  { "sorcerer", 11},
  { "necromancer", 12},
  { "conjurer", 13},
  { "thief", 14},
  { "assassin", 15},
  { "mercenary", 16},
  { "bard", 17},
  { "psionicist", 18},
  { "lich", 19},
  { "enchanter", 20},
  { "invoker", 21},
  { "illusionist", 22},
  { "battlechanter", 23},
  { "rogue", 24},
  { "human", 25},
  { "barbarian", 26},
  { "drow_elf", 27},
  { "grey_elf", 28},
  { "dwarf_mountain", 29},
  { "duergar_dwarf", 30},
  { "halfling", 31},
  { "gnome", 32},
  { "ogre", 33},
  { "swamp_troll", 34},
  { "troll", 34},
  { "halfelf", 35},
  { "illithid", 36},
  { "yuan_ti", 37},
  //  { "lich", 38 },
  { "orc", 40},
  { "pkill", 53},
  { "helper", 54},
  { "ungrouped", 55},
  { "elementalist", 56},
  { "goodrace", 57},
  { "evilrace", 58},
  { "ingroup", 59},
  { "dire_raider", 60},
  { "lfg", 61},
  { "\n", 66}
};

void do_who(P_char ch, char *argument, int cmd) {
  P_char tch;
  P_desc d;
  bool found;
  char buf4[MAX_STRING_LENGTH], buf5[MAX_STRING_LENGTH], buf[MAX_STRING_LENGTH], buf3[MAX_STRING_LENGTH];
  char val_arg[15][MAX_INPUT_LENGTH], who_output[MAX_STRING_LENGTH], rplevel[MAX_STRING_LENGTH];
  int i, j, k, nr_args_now = 0, who_list_size = 0, who_gods_size = 0, who_anon_size = 0;
  int timer = 0;
  struct time_info_data playing_time;
  struct who_flags flags;

  flags.ANONYMOUS = FALSE;
  flags.OUTLAW = FALSE;
  flags.ZONE = FALSE;
  flags.SORT = FALSE;
  flags.LEADERS = FALSE;
  flags.GROUPED = FALSE;
  flags.GODS_ONLY = FALSE;
  flags.RACE = 0;
  flags.RACE_O = FALSE;
  flags.CLASS_O = FALSE;
  flags.CLASS = 0;
  flags.low = 1;
  flags.high = FORGER;
  flags.short_list = FALSE;
  flags.PKILLERS = FALSE;
  flags.HELPERS = FALSE;
  flags.UNGROUPED = FALSE;
  flags.GOODRACE = FALSE;
  flags.EVILRACE = FALSE;
  flags.INGROUP = FALSE;
  flags.LFG = FALSE;

  strcpy(buf, "");

  while (argument && *argument) {
    while (*argument == ' ')
      argument++;
    argument = one_argument(argument, val_arg[nr_args_now]);
    strcpy(buf, "");
    found = FALSE;

    /**** start of help ****/
    if (*val_arg[nr_args_now] == '?') {
      send_to_char("Usage: WHO <argument list>\n", ch);
      strcpy(buf, "Who arguments:\n");
      for (j = 0, i = 0; fields[i].number < 66; i++)
        sprintf(buf, "%s%-17s%s", buf, fields[i].options, (!(++j % 4) ? "\n" : ""));
      strcat(buf, "\n\n? - This list\n\nWhere <high> and <low> is numbers representing levels.\n");
      send_to_char(buf, ch);
      return;
    }
    /**** end of help ****/

    if (isdigit(*val_arg[nr_args_now])) {
      switch (sscanf(val_arg[nr_args_now], "%2d-%2d", &i, &k)) {
        case 2: /* arg is ##-## */
          cmd_nr[nr_args_now] = 0;
          flags.high = k;
          /* drop through */
        case 1: /* arg is ##- (or drop through from ##-## */
          cmd_nr[nr_args_now] = 0;
          flags.low = i;
          found = TRUE;
          continue;

        default:
          break;
      }
    }

    if ((*val_arg[nr_args_now] == '-') && isdigit(*(val_arg[nr_args_now] + 1))) {
      if (sscanf(val_arg[nr_args_now], "-%2d", &k) > 0) {
        cmd_nr[nr_args_now] = 0;
        flags.high = k;
        found = TRUE;
        continue;
      }
    }

    for (j = 0; !found && (fields[j].number != 66); j++) {
      if (is_abbrev(val_arg[nr_args_now], fields[j].options)) {
        cmd_nr[nr_args_now] = fields[j].number;
        found = TRUE;
        break;
      }
    }

    tch = NULL;
    /* switching to descriptor list, rather than get_char_vis, since it was lagging hell out of things. JAB */
    for (d = descriptor_list; !found && d; d = d->next) {
      if (!d->character || d->connected || !d->character->player.name)
        continue;
      if (!isname(d->character->player.name, val_arg[nr_args_now]))
        continue;
      if (!CAN_SEE(ch, d->character))
        continue;
      tch = d->character;
      break;
    }

    if (!tch && !found) {
      sprintf(buf, "Sorry, but %s is not a valid option.\n", val_arg[nr_args_now]);
      send_to_char(buf, ch);
      return;
    }

    if (!found) {
      if (tch && (IS_NPC(tch) || (!tch->desc))) {
        sprintf(buf, "Sorry, but %s is not a valid option.\n", val_arg[nr_args_now]);
        send_to_char(buf, ch);
        return;
      } else if (tch) { /* print specific character BEGIN */
        if (GET_LEVEL(ch) > MAXLVLMORTAL) {
          // RP Toggle stuff
          if (IS_CSET(ch->only.pc->pcact, PLR_G_QUEST) || GET_LEVEL(ch) >= 57) {
            if (IS_CSET(tch->only.pc->pcact, PLR_B_RP))
              sprintf(rplevel, " (&+RBRP&N)");
            else if (IS_CSET(tch->only.pc->pcact, PLR_P_RP))
              sprintf(rplevel, " (&N&+wPRP&N)");
            else if (IS_CSET(tch->only.pc->pcact, PLR_G_RP))
              sprintf(rplevel, " (&N&+GGRP&N)");
            else if (IS_CSET(tch->only.pc->pcact, PLR_E_RP))
              sprintf(rplevel, " (&N&+CERP&N)");
            else
              ; //sprintf(rplevel, "");
          } else
            ; // sprintf(rplevel, "");

          sprintf(who_output,
                  " Listing of Specific Character\n"
                  "-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-\n"
                  "[&+w%2d %-12s&N] %s %s%s%s%s%s%s (%s&n)",
                  GET_LEVEL(tch), class_names[(int) GET_CLASS(tch)],
                  GET_NAME(tch), (GET_TITLE(tch) ? GET_TITLE(tch) : ""),
                  (tch->desc && tch->desc->olc) ? " (&n&+bo&+ml&+rc&N)" : "",
                  IS_CSET(tch->only.pc->pcact, PLR_AFK) ? " (&+RAFK&N)" : "",
                  IS_CSET(tch->only.pc->pcact, PLR_ROLEPLAY) ? " (&+WRP&N)" : "",
                  rplevel,
                  IS_CSET(tch->only.pc->pcact, PLR_LFG) ? " (&+GLFG&N)" : "",
                  race_names[(int) GET_RACE(tch)]);
          sprintf(who_output + strlen(who_output), "\n");
#if 0
          if (GET_PRESTIGE(tch) > 5999)
            sprintf(who_output + strlen(who_output), "                  Prestige Title Goes Here\n");
#endif
          sprintf(who_output + strlen(who_output),
                  "-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-\n");
        } else {
          sprintf(who_output,
                  " Listing of Specific Character\n"
                  "-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-\n");
          if (IS_CSET(tch->only.pc->pcact, PLR_G_WWW) || (GET_LEVEL(tch) > MAXLVLMORTAL)) {
            if (GET_LEVEL(tch) > MAXLVLMORTAL)
              strcat(who_output, god_title(tch, TRUE));

            strcat(who_output, GET_NAME(tch));
            if (GET_TITLE(tch))
              sprintf(who_output + strlen(who_output), " %s", GET_TITLE(tch));
            if (tch->desc && tch->desc->olc)
              sprintf(who_output + strlen(who_output), "&n(&+bo&+ml&+rc&N)");
            if (IS_CSET(tch->only.pc->pcact, PLR_AFK))
              sprintf(who_output + strlen(who_output), "&n(&+RAFK&N)");
            if (IS_CSET(tch->only.pc->pcact, PLR_ROLEPLAY))
              sprintf(who_output + strlen(who_output), "&n(&+WRP&N)");
            if (IS_CSET(tch->only.pc->pcact, PLR_LFG))
              sprintf(who_output + strlen(who_output), "&n(&+GLFG&N)");
            sprintf(who_output + strlen(who_output), "\n");
          } else {
            if (IS_CSET(tch->only.pc->pcact, PLR_ANONYMOUS) && ((GET_LEVEL(ch) < 51) || (ch != tch)))
              strcat(who_output, "[&+L Anon &N] ");
            else
              sprintf(who_output + strlen(who_output), "[&+w%2d %-12s&N] ",
                    GET_LEVEL(tch), class_names[(int) GET_CLASS(tch)]);

            sprintf(who_output + strlen(who_output), "%s %s &n(%s&n)%s%s%s",
                    GET_NAME(tch), (GET_TITLE(tch) ? GET_TITLE(tch) : ""),
                    race_names[(int) GET_RACE(tch)],
                    IS_CSET(tch->only.pc->pcact, PLR_AFK) ? " (&+RAFK&N)" : "",
                    IS_CSET(tch->only.pc->pcact, PLR_ROLEPLAY) ? " (&+WRP&N)" : "",
                    IS_CSET(tch->only.pc->pcact, PLR_LFG) ? " (&+GLFG&N)" : "");
            sprintf(who_output + strlen(who_output), "\n");
          }
#if 0
          if (GET_PRESTIGE(tch) > 5999)
            if (!(IS_CSET(tch->only.pc->pcact, PLR_ANONYMOUS)) || (GET_LEVEL(ch) > 50) || (tch == ch))
              sprintf(who_output + strlen(who_output), "                  Prestige Title Goes Here\n");
#endif
          strcat(who_output,
                  "-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-\n");
        }
        send_to_char(who_output, ch);
      }

      if (IS_TRUSTED(ch) && tch) {
        strcpy(who_output, "");
        /* *********************** */
        /* God stuff for who here! */
        /* *********************** */
        /* Get name */
        strcpy(buf4, "");
        if ((GET_LEVEL(ch) > 59 && tch->desc && tch->desc->snoop.snoop_by))
          sprintf(who_output + strlen(who_output), " (Snooped By: %s)",
                GET_NAME(tch->desc->snoop.snoop_by->character));
        strcat(who_output, "\n");
        timer = tch->desc->character->specials.timer;
        sprintf(buf4, "%3d [%2d] : ", tch->desc->descriptor, timer);
        strcat(who_output, buf4);
        sprintf(who_output + strlen(who_output), "[%2d]",
                (tch->desc->original) ? GET_LEVEL(tch->desc->original) : GET_LEVEL(tch->desc->character));
        if (tch->desc->original)
          strcat(who_output, " S ");
        else
          strcat(who_output, "   ");
        sprintf(who_output + strlen(who_output), "%-12s",
                (tch->desc->original) ? (tch->desc->original->player.name) :
                (tch->desc->character->player.name));
        if (tch->in_room > NOWHERE)
          sprintf(who_output + strlen(who_output), "%6d ", world[tch->in_room].number);
        else
          strcat(who_output, "   ??? ");
        if (GET_LEVEL(ch) >= 59) {
          if (tch->desc->original && (tch->desc->original->only.pc->wiz_invis != 0))
            sprintf(who_output + strlen(who_output), "%2d ", tch->desc->original->only.pc->wiz_invis);
          else if (tch->only.pc->wiz_invis != 0)
            sprintf(who_output + strlen(who_output), "%2d ", tch->only.pc->wiz_invis);
          else
            strcat(who_output, "   ");
        } else
          strcat(who_output, "   ");
        if (tch->desc) {
          sprinttype(tch->desc->connected, connected_types, buf5);
          sprintf(who_output + strlen(who_output), "%-11s", buf5);
          sprintf(who_output + strlen(who_output), " %s", full_address(tch->desc, 60, 0));
        } else
          sprintf(who_output + strlen(who_output), "LD - %s", GET_LAST_LOGIN(tch));
        send_to_char(who_output, ch);
        send_to_char("\n\n", ch);
        if (GET_CLASS(ch) == CLASS_PSIONICIST ||
                GET_CLASS(ch) == CLASS_BARD ||
                GET_CLASS(ch) == CLASS_BATTLECHANTER ||
                GET_RACE(ch) == RACE_YUANTI)
          sprintf(buf3,
                "\n       Hit Points = %d(%d),  PSPs = %d(%d),  Movement Points = %d(%d)\n",
                GET_HIT(tch), GET_MAX_HIT(tch),
                GET_MANA(tch), GET_MAX_MANA(tch),
                GET_MOVE(tch), GET_MAX_MOVE(tch));
        else
          sprintf(buf3,
                "\n      Hit Points = %d(%d), Movement Points = %d(%d), Alignment = %d\n",
                GET_HIT(tch), GET_MAX_HIT(tch), GET_MOVE(tch), GET_MAX_MOVE(tch), GET_ALIGNMENT(tch));

        strcat(buf, buf3);
        sprintf(buf3, "      %s&n, Total XP = %d, XP to next level = %d.\n",
                (GET_SEX(tch) == SEX_MALE) ? "&+BMale" :
                (GET_SEX(tch) == SEX_FEMALE) ? "&+RFemale" : "&+YNeuter",
                (GET_EXP(tch)), (EXP_TABLE(tch, 1) - GET_EXP(tch)));
        strcat(buf, buf3);
        sprintf(buf3,
                "            Stats: STR = %d, DEX = %d, AGI = %d, CON = %d\n"
                "                   POW = %d, INT = %d, WIS = %d, CHA = %d\n",
                GET_C_STR(tch), GET_C_DEX(tch), GET_C_AGI(tch), GET_C_CON(tch),
                GET_C_POW(tch), GET_C_INT(tch), GET_C_WIS(tch), GET_C_CHA(tch));
        strcat(buf, buf3);
        real_time_passed(&playing_time,
                (int) ((time(0) - tch->player.time.logon) + tch->player.time.played), 0);
        sprintf(buf3, "                   Playing time = %d days and %d hours.\n",
                playing_time.day, playing_time.hour);
        strcat(buf, buf3);
        page_string(ch->desc, buf, TRUE);
      }

      return;
    }
    /* search for specific character END */

    nr_args_now++;
    if (nr_args_now > 14) {
      send_to_char("Too many arguments.\n", ch);
      return;
    }
  }

  set_who_flags(ch, nr_args_now, &flags);
  who_list_size = make_list(ch, flags);

  who_gods_size = (who_list_size / 1000);
  who_list_size -= (who_gods_size * 1000);

  *buf = '\0';
  k = 1;
  if (flags.short_list) {
    strcpy(who_output, " Short Listing of Staff\n-=-=-=-=-=-=-=-=-=-=-=-\n");
    if (flags.SORT) {
      for (i = flags.high; i >= flags.low; i--) {
        for (j = 1; j < who_gods_size; j++) {
          if ((i == GET_LEVEL(who_gods[j]))) {
            strcat(who_output, god_title(who_gods[j], FALSE));
            sprintf(who_output, "%s %-15s%s", who_output, GET_NAME(who_gods[j]), (!(k++ % 3) ? "\n" : ""));
          }
        }
      }
    } else {
      for (j = 1; j < who_gods_size; j++) {
        if ((GET_LEVEL(who_gods[j]) <= flags.high) && (GET_LEVEL(who_gods[j]) >= flags.low)) {
          strcat(who_output, god_title(who_gods[j], FALSE));
          sprintf(who_output, "%s %-15s%s", who_output, GET_NAME(who_gods[j]), (!(k++ % 3) ? "\n" : ""));
        }
      }
    }

    if (who_gods_size == 1)
      strcat(who_output, "<None>\n");
    sprintf(who_output, "%s\nThere are %d visible &+Bstaff member(s)&N on.\n", who_output, who_gods_size - 1);
    strcat(who_output, "\n");

    strcpy(buf, "");
    k = 1;
    if (!flags.GODS_ONLY) {
      strcat(who_output, "\n"
              " Short Listing for Mortals\n"
              "-=-=-=-=-=-=-=-=-=-=-=-=-=-\n");
      if (flags.SORT) {
        for (i = flags.high; i >= flags.low; i--) {
          for (j = 0; j < who_list_size; j++) {
            if (IS_CSET(who_list[j]->only.pc->pcact, PLR_ANONYMOUS) && !IS_TRUSTED(ch) && (ch != who_list[j]))
              continue;
            if ((i == GET_LEVEL(who_list[j]))) {
              sprintf(who_output + strlen(who_output), "[&+w%2d&N%s%-3s&N]", GET_LEVEL(who_list[j]),
                      IS_CSET(who_list[j]->only.pc->pcact, PLR_ANONYMOUS) ? "&+g*&N" : " ",
                      class_abbrev[(int) GET_CLASS(who_list[j])]);
              sprintf(who_output + strlen(who_output), " %-15s%s",
                      GET_NAME(who_list[j]), (!(k++ % 3) ? "\n" : ""));
            }
          }
        }
      } else {
        for (j = 0; j < who_list_size; j++) {
          if (IS_CSET((who_list[j])->only.pc->pcact, PLR_ANONYMOUS) && !IS_TRUSTED(ch) && (ch != who_list[j]))
            continue;
          if ((GET_LEVEL(who_list[j]) <= flags.high) && (GET_LEVEL(who_list[j]) >= flags.low)) {
            sprintf(who_output + strlen(who_output), "[&+w%2d&N%s%-3s&N]", GET_LEVEL(who_list[j]),
                    (IS_CSET(who_list[j]->only.pc->pcact, PLR_ANONYMOUS) ? "&+g*&N" : " "),
                    class_abbrev[(int) GET_CLASS(who_list[j])]);
            sprintf(who_output + strlen(who_output), " %-15s%s",
                    GET_NAME(who_list[j]), (!(k++ % 3) ? "\n" : ""));
          }
        }
      }

      strcat(who_output, "\n");
      if (who_list_size == 0)
        strcat(who_output, "<None>\n");
    }

    page_string(ch->desc, who_output, TRUE);
  } else if (!flags.short_list) {
    strcpy(who_output,
            " Listing of the Staff\n"
            "-=-=-=-=-=-=-=-=-=-=-\n");
    for (i = flags.high; i >= flags.low; i--) {
      for (j = 1; j < who_gods_size; j++) {
        if ((i == GET_LEVEL(who_gods[j]) && flags.SORT) ||
                (!flags.SORT && ((GET_LEVEL(who_gods[j]) <= flags.high) &&
                (GET_LEVEL(who_gods[j]) >= flags.low)))) {
          strcat(who_output, god_title(who_gods[j], TRUE));
          strcat(who_output, GET_NAME(who_gods[j]));
          if (GET_TITLE(who_gods[j])) {
            strcat(who_output, " ");
            strcat(who_output, GET_TITLE(who_gods[j]));
          }
          if (who_gods[j]->desc && who_gods[j]->desc->olc)
            strcat(who_output, " (&+bo&+ml&+rc&N)");
          if (IS_CSET((who_gods[j])->only.pc->pcact, PLR_AFK))
            strcat(who_output, " (&+RAFK&N)");
          if (IS_CSET((who_gods[j])->only.pc->pcact, PLR_ROLEPLAY))
            strcat(who_output, " (&+WRP&N)");
          if (IS_CSET((who_gods[j])->only.pc->pcact, PLR_LFG))
            strcat(who_output, " (&+GLFG&N)");
          if (IS_TRUSTED(ch)) {
            sprintf(who_output + strlen(who_output), " (%d)", who_gods[j]->only.pc->wiz_invis);
            if (IS_AFFECTED(who_gods[j], AFF_INVISIBLE))
              strcat(who_output, " (inv)");
          }
          strcat(who_output, "\n");
#if 0
          if (GET_PRESTIGE(who_gods[j]) > 5999)
            sprintf(who_output + strlen(who_output), "                  Prestige Title Goes Here\n");
#endif
        }
      }

      if (!flags.SORT)
        break;
    }

    if (who_gods_size == 1)
      strcat(who_output, "<None>\n");
    sprintf(who_output + strlen(who_output), "\nThere are %d visible &+rstaff member(s)&n on.\n",
            who_gods_size - 1);
    strcpy(buf, "");
    if (!flags.GODS_ONLY) {
      strcat(who_output, "\n"
              " Listing of the Mortals!\n"
              "-=-=-=-=-=-=-=-=-=-=-=-=-\n");
      for (i = flags.high; i >= flags.low; i--) {
        for (j = 0; j < who_list_size; j++) {
          if ((i == GET_LEVEL(who_list[j]) && flags.SORT) ||
                  (!flags.SORT &&
                  ((GET_LEVEL(who_list[j]) <= flags.high) && (GET_LEVEL(who_list[j]) >= flags.low)))) {
            if (!IS_CSET((who_list[j])->only.pc->pcact, PLR_ANONYMOUS) || IS_TRUSTED(ch) || (ch == who_list[j]))
              sprintf(who_output + strlen(who_output), "&N&n[&+w%2d&N&+g%s%-3s&N&n] ",
                    GET_LEVEL(who_list[j]),
                    (IS_CSET(who_list[j]->only.pc->pcact, PLR_ANONYMOUS) ? "*" : " "),
                    class_abbrev[(int) GET_CLASS(who_list[j])]);
            else {
              /*            strcat(who_output, "[&+L Anon &N] "); */
              if (flags.INGROUP && ARE_GROUPED(ch, who_list[j])) {
                strcat(who_output, "[&+L Anon &N] ");
                who_anon_size++;
              } else {
                who_anon_size++;
                continue;
              }
            }

            if (IS_CSET(ch->only.pc->pcact, PLR_WHODISPROOM))
              sprintf(who_output + strlen(who_output), "[%6d] ", world[who_list[j]->in_room].number);

            strcat(who_output, GET_NAME(who_list[j]));
            strcat(who_output, " ");
            if (GET_TITLE(who_list[j]))
              strcat(who_output, GET_TITLE(who_list[j]));
            if (IS_AFFECTED(who_list[j], AFF_INVISIBLE))
              strcat(who_output, " (inv)");
            strcat(who_output, " &N(");
            strcat(who_output, race_names[(int) GET_RACE(who_list[j])]);
            strcat(who_output, "&N)");
            if (who_list[j]->desc && who_list[j]->desc->olc)
              strcat(who_output, " (&+bo&+ml&+rc&N)");
            if (IS_CSET((who_list[j])->only.pc->pcact, PLR_AFK))
              strcat(who_output, " (&+RAFK&N)");
            if (IS_CSET((who_list[j])->only.pc->pcact, PLR_ROLEPLAY))
              strcat(who_output, " (&+WRP&N)");

            // RP Toggle stuff
            if (IS_CSET(ch->only.pc->pcact, PLR_G_QUEST) || GET_LEVEL(ch) >= 57) {
              if (IS_CSET((who_list[j])->only.pc->pcact, PLR_B_RP))
                strcat(who_output, " (&+RBRP&N)");
              else if (IS_CSET((who_list[j])->only.pc->pcact, PLR_P_RP))
                strcat(who_output, " (&N&+wPRP&N)");
              else if (IS_CSET((who_list[j])->only.pc->pcact, PLR_G_RP))
                strcat(who_output, " (&N&+GGRP&N)");
              else if (IS_CSET((who_list[j])->only.pc->pcact, PLR_E_RP))
                strcat(who_output, " (&N&+CERP&N)");
            }

            if (IS_CSET((who_list[j])->only.pc->pcact, PLR_LFG))
              strcat(who_output, " (&+GLFG&N)");
            strcat(who_output, "\n");
#if 0
            if (GET_PRESTIGE(who_list[j]) > 5999)
              if (!(IS_CSET(who_list[j]->only.pc->pcact, PLR_ANONYMOUS)) || (GET_LEVEL(ch) > 50) || (who_list[j] == ch))
                sprintf(who_output + strlen(who_output), "         Prestige Title Goes Here\n");
#endif
          }

          /* (Neblun 17OCT95 - The huge size of the who list was exceeding MAX_STRING_LENGTH.  These couple
             of lines of code (stolen from users) should correct that.. for now */
          if (strlen(who_output) > (MAX_STRING_LENGTH - 175)) {
            strcat(who_output, "List too long, use 'who <arg>'\n");
            break;
          }
        }

        if (!flags.SORT)
          break;
      }
      who_list_size -= who_anon_size;
      strcat(buf, who_output);
      if (who_list_size == 0)
        strcat(who_output, "<None>\n");

      sprintf(who_output + strlen(who_output),
              "\nThere are %d &+gmortal(s)&n on.\n\n&+cTotal visible players: %d.&N\n&+rRecord number of players on this boot:  %d.&n\n",
              who_list_size, who_list_size + who_gods_size - 1, max_users_playing);
    }

    page_string(ch->desc, who_output, TRUE);
    strcpy(who_output, "");
  }
}

char *god_title(P_char tch, bool long_form) {
  int lvl, type;

  lvl = GET_LEVEL(tch);

  if (IS_CSET(tch->only.pc->pcact, PLR_G_ADMIN))
    type = 0;
  else if (IS_CSET(tch->only.pc->pcact, PLR_G_CODER))
    type = 1;
  else if (IS_CSET(tch->only.pc->pcact, PLR_G_AREAS))
    type = 2;
  else if (IS_CSET(tch->only.pc->pcact, PLR_G_WWW))
    type = 3;
  else if (IS_CSET(tch->only.pc->pcact, PLR_G_QUEST))
    type = 4;
  else {
    if (GET_LEVEL(tch) == 60) {
      if (long_form)
        return "[     &+bSysop&n     ] ";
      else
        return "[&+bSysOp&N]";
    } else {
      if (long_form)
        return "[ &+gNobodySpecial&n ] ";
      else
        return "[&+gNobdy&n]";
    }
  }

  switch (lvl) {
    case FORGER:
    case 59:
    case 58:
    case 57:
    case 56:
    case 55:
    case 54:
    case 53:
    case 52:
    case 51:
      switch (type) {
        case 0:
          return long_form ? "[ &+cAdministrator&n ] " : "[&+cAdmin&n]";
        case 1:
          return long_form ? "[     &+LCoder&n     ] " : "[&+LCoder&n]";
        case 2:
          return long_form ? "[     &+mAreas&n     ] " : "[&+mAreas&n]";
        case 3:
          return long_form ? "[ &+gWeb Creation&n  ] " : "[&+gWebCr&n]";
        case 4:
          return long_form ? "[    &+yRP-Quest&n   ] " : "[&+yQuest&n]";
        default:
          break;
      }
      break;

    default:
      break;
  }

  return long_form ? "[ &+gNobodySpecial&n ] " : "[&+gNobdy&n]";
}

/* changed to show linkdeads as well, checks PC_list, rather than descriptor_list, added some
   features too.  For one thing, if now takes args to limit it's spam:
   users [N][P][L][S]|[A] [string] show (non-playing|playing|linkdead|summary only|all) users only.
   JAB */

void do_multi_users(P_char ch, char *argument, int cmd);
void find_dups(UtilSet s);
static int sort_by_ip_comparef(PlrCh **p1, PlrCh **p2);
bool is_room_an_inn(int room);
UtilSet AddItem(UtilSet s, char *hst, char *name, char *usr, bool ld, int skt, int idl, int inr, byte st);


#define USERS_SYNTAX "\
Usage:  users [ALNMPS?] [<string>]\n\
  Options: (must be uppercase)\n\
    A - all users (the default)\n\
    L - linkdead characters\n\
    N - non-playing sockets\n\
    P - playing sockets (in the game)\n\
    S - summary information only (S by itself implies 'AS')\n\
    I - idle longer than 1 minute (this is an override, only idle people will show up)\n\
    M - show only multiple uses of any IP (not combinable with other options)\n\
    ? - show this syntax info.\n\
  <string> is a case sensitive substring to match against any/all of:\n\
  character name, username or site.  If you wish to do a substring match\n\
  of a string containing ONLY the characters ALNPS, you must type:\n\
  users A <string>, or the command will assume that your string is a list\n\
  of options.\n"

void do_users(P_char ch, char *argument, int cmd) {
  P_char t_ch;
  P_desc d;
  char biglist[MAX_STRING_LENGTH], buf[MAX_STRING_LENGTH];
  char buf2[MAX_INPUT_LENGTH], line[MAX_INPUT_LENGTH], name[MAX_INPUT_LENGTH], *name_p;
  int num_link_dead = 0, num_in_game = 0, num_non_play = 0, b_len = 160, mode = 0, timer = 0, idle_only = 0;
  int args_done = 0;

  biglist[0] = '\0';

  half_chop(argument, name, line);

  name_p = name;
  if (!str_cmp(name_p, "m")) {
    do_multi_users(ch, argument, cmd);
    return;
  }

  /* parse our command options. */

  while (!args_done) {
    switch (LOWER(*name_p)) {
      case '?':
        send_to_char(USERS_SYNTAX, ch);
        return;

      case 'i':
        idle_only = 1; /* just the idle ones */
        break;

      case 'n':
        mode |= 1; /* Non-playing sockets */
        break;

      case 'p':
        mode |= 2; /* Playing sockets */
        break;

      case 'l':
        mode |= 4; /* Linkdead characters */
        break;

      case 's':
        mode |= 8; /* only summary info */
        break;

      case 'a':

        /* special case, this is the default, but if you are trying to match a name of say 'PLAN', you
           now have to type 'users A PLAN' or it will think it's an option, not a string. */
        mode |= 7; /* options NPL */
        break;

      default:

        /* ok, to make this work, if we get here, we have a character other than one of the of the command
           option characters.  If that character is NOT a space, they either typoed, or we have been trying
           to parse the name/site/username.  So we check, and most likely break out of this loop. JAB */

        if (!*name_p || isspace(*name_p)) {
          /* ok, end of the command string, we are fine, copy line into name and carry on. */
          name[0] = 0;
          if (*line)
            strcpy(name, line);
        } else {
          /* we've been parsing a string they want to search for, not a list of args */

          mode = 7;
          idle_only = 0;
        }

        if (!mode)
          mode = 7;

        args_done = 1;
        break;
    }
    name_p++;
  }

  if (mode == 8)
    mode = 15;

  if (mode & 1) {
    if (!(mode & 8))
      strcat(biglist, "&+RNon-playing sockets:\n--------------------\n");

    for (d = descriptor_list; d; d = d->next) {
      if (!d->connected) /* want non-playing sockets only */
        continue;

      if (idle_only && (d->wait < 240))
        continue;

      if (d->character)
        t_ch = d->character;
      else
        t_ch = NULL;

      if (t_ch && (GET_LEVEL(t_ch) > GET_LEVEL(ch)))
        continue; /* hide status on higher levels */

      if (*name) {
        if ((!d || !d->hostIP || !strstr(d->hostIP, name)) &&
                (!d || !d->hostname || !strstr(d->hostname, name)) &&
                (!d || !d->username || !strstr(d->username, name)) &&
                (d || !strstr(GET_LAST_LOGIN(t_ch), name)) &&
                (!t_ch || !t_ch->player.name || !strstr(t_ch->player.name, name)))
          continue;
      }

      num_non_play++;

      sprintf(line, "%3d I:%3d", d->descriptor, d->wait / 240);

      if (t_ch && t_ch->player.name)
        sprintf(line + strlen(line), "  %-15s", GET_NAME(t_ch));
      else
        strcat(line, "  &+mNONE&n           ");

      sprinttype(d->connected, connected_types, buf2);
      sprintf(line + strlen(line), "  %11s", buf2);

      /* Get IP Address */
      if (d) {
        if (GET_LEVEL(ch) > 50)
          sprintf(line + strlen(line), " %10s %s", full_username(d, 10, 0), full_hostname(d, 0, 1));
      } else {
        sprintf(line + strlen(line), " &+CUNKNOWN&n");
      }

      strcat(line, "\n");

      if (!(mode & 8)) {
        b_len += strlen(line);
        if (b_len > MAX_STRING_LENGTH) {
          strcat(biglist, "List too long, use 'users <arg>'\n");
          page_string(ch->desc, biglist, 1);
          return;
        }
        strcat(biglist, line);
      }
    }
  }

  if ((mode & 2) || (mode & 4)) {
    if (!(mode & 8))
      strcat(biglist, "\n&+GCharacters:\n-----------\n");

    b_len = strlen(biglist) + 160;

    /* ok, this function is basically about links, so, in that vein, there are 4 possible states, only
       linkdead excluding the others.  They are:
       normal (connected, in the game, none of the below apply)
       linkdead (PCs body is in game, but there is no active control)
       snooping (PC is receiving input from two locations, his own, and wherever, the victim is)
       switched (PC has a linkdead body, and his link is attached to an NPC somewhere.
     */

    for (t_ch = PC_list; t_ch; t_ch = t_ch->next) {
      if (t_ch->desc)
        d = t_ch->desc;
      else if (t_ch->only.pc->switched && t_ch->only.pc->switched->desc)
        d = t_ch->only.pc->switched->desc;
      else
        d = NULL;

      if (d && d->connected)
        continue; /* not in game yet */

      if (!t_ch || (t_ch->in_room < 0) || (t_ch->in_room > top_of_world))
        continue; /* not in game */

      if (idle_only && (t_ch->specials.timer < 2))
        continue;

      if (t_ch->player.name && WIZ_INVIS(ch, t_ch))
        continue;

      if (*name) {
        if ((!d || !d->hostIP || !strstr(d->hostIP, name)) &&
                (!d || !d->hostname || !strstr(d->hostname, name)) &&
                (!d || !d->username || !strstr(d->username, name)) &&
                (d || !strstr(GET_LAST_LOGIN(t_ch), name)) &&
                (!t_ch || !t_ch->player.name || !strstr(t_ch->player.name, name)))
          continue;
      }

      timer = t_ch->specials.timer; /* idle time */

      if (t_ch->player.name) {
        sprintf(line, "%-15s", t_ch->player.name);
      } else
        strcpy(line, "&+RUNDEFINED&n     ");

      if (t_ch->in_room != NOWHERE)
        sprintf(line + strlen(line), " [%6d] ", world[t_ch->in_room].number);
      else
        strcat(line, "  ??????  ");

      if (IS_TRUSTED(t_ch))
        sprintf(line + strlen(line), "%2d ", t_ch->only.pc->wiz_invis);
      else
        strcat(line, "   ");

      if (mccp_is_compressed(t_ch->desc))
        strcat(line, "C");
      else
        strcat(line, " ");

      if (d) {
        num_in_game++;

        /* Get IP Address o_o */
        if (GET_LEVEL(ch) > 50) {
          sprintf(line + strlen(line), " %10s %s\n", full_username(d, 10, 0), full_hostname(d, 0, 0));
        }
      } else {
        if (mode & 4)
          num_link_dead++;
        sprintf(line + strlen(line), " &+BLD - %s&n\n", GET_LAST_LOGIN(t_ch));
      }

      if (d) {
        if (d->snoop.snooping && (GET_LEVEL(ch) >= GET_LEVEL(t_ch))) {
          if (d->snoop.snooping->character)
            sprintf(line + strlen(line), "   &+g(Snooping:&n %s&+g)&n\n",
                  GET_NAME(d->snoop.snooping->character));
          else
            sprintf(line + strlen(line), "   &+g(Snooping:&n A disembodied socket (%d)&+g)&n\n",
                  d->snoop.snooping->descriptor);
        }
        if (d->snoop.snoop_by) {
          if (d->snoop.snoop_by->character && (GET_LEVEL(ch) >= GET_LEVEL(d->snoop.snoop_by->character)))
            sprintf(line + strlen(line), "   &+M(Snoop by:&n %s&+M)&n\n",
                  GET_NAME(d->snoop.snoop_by->character));
          else if (!d->snoop.snoop_by->character)
            sprintf(line + strlen(line), "   &+M(Snoop by:&n A disembodied socket (%d)&+M)&n\n",
                  d->snoop.snoop_by->descriptor);
        }
        if (d->original) {
          sprintf(line + strlen(line), "   &+Y(Switched:&n %s &+Y[&n%d&+Y])&n\n",
                  GET_NAME(t_ch->only.pc->switched),
                  (t_ch->only.pc->switched->in_room == NOWHERE) ?
                  -1 : world[t_ch->only.pc->switched->in_room].number);
        }
      }

      if (d)
        sprintf(buf, "%3d I:%3d  ", d->descriptor, timer);
      else
        sprintf(buf, "&+B---&n I:%3d  ", timer);

      strcat(buf, line);

      if (!(mode & 8) && ((mode == 7) || (d && (mode & 2)) || (!d && (mode & 4)))) {
        b_len += strlen(buf);
        if (b_len > MAX_STRING_LENGTH) {
          strcat(biglist, "List too long, use 'users <arg>'\n");
          page_string(ch->desc, biglist, 1);
          return;
        }
        strcat(biglist, buf);
      }
    }
  }

  sprintf(line, "\nNon-playing: %d  In game: %d  Linkdeads: %d  Sockets: %d\n",
          num_non_play, num_in_game, num_link_dead, (num_non_play + num_in_game));
  strcat(biglist, line);

  page_string(ch->desc, biglist, 1);
}
#undef USERS_SYNTAX

static int sort_by_ip_comparef(PlrCh **p1, PlrCh **p2) {
  return strcmp((*p1)->hostname, (*p2)->hostname);
}

UtilSet AddItem(UtilSet s, char *hst, char *name, char *usr, bool ld, int skt, int idl, int inr, byte st) {
  PlrCh *plr;

  CREATE(plr, PlrCh, 1);

  strcpy(plr->hostname, hst);
  strcpy(plr->username, usr);
  if (name)
    strcpy(plr->name, name);
  else
    strcpy(plr->name, "NONE");
  plr->ld = ld;
  plr->socket = skt;
  plr->idle = idl;
  plr->in_room = inr;
  plr->state = st;
  plr->ip_count = 1;

  s = SetAdd(s, plr);

  return s;
}

void find_dups(UtilSet s) {
  int x, y, z, nitems, count;
  PlrCh *plr, *plr2;

  nitems = SetNitems(s);
  for (x = 0; x < nitems; x++) {
    SetRetreive(s, x, (void *) &plr);
    if (!plr)
      continue;

    y = x + 1;
    count = 1;
    while (y < nitems) {
      SetRetreive(s, y, (void *) &plr2);
      if (!plr2) {
        y++;
        continue;
      }

      if (strcmp(plr->hostname, plr2->hostname))
        break;

      y++;
      count++;
    }

    if (count > 1) {
      for (z = x; z < y; z++) {
        SetRetreive(s, z, (void *) &plr2);
        plr2->ip_count = count;
      }

      x = y - 1; // let x get naturally incremented via the for loop
    }
  }
}

/* this list should be sorted by order of popularity since it's linearly searched */
static int inns[22] = {3008, 21797, 8003, 6170, 91430, 6535, 7450, 10729,
  15870, 16247, 20977, 26566, 34329, 35415, 11901,
  46037, 66054, 81005, 15297, 91516, 94829, 96537};

bool is_room_an_inn(int room) {
  int x;

  for (x = 0; x < 22; x++) {
    if (room == inns[x])
      return TRUE;
  }

  return FALSE;
}

void do_multi_users(P_char ch, char *argument, int cmd) {
  UtilSet ipset;
  P_char t_ch;
  P_desc d;
  char biglist[MAX_STRING_LENGTH], tmpstr[MAX_STRING_LENGTH], share[5];
  char name[MAX_INPUT_LENGTH], line[MAX_INPUT_LENGTH], room[24], status[24];
  PlrCh *item;
  int x, timer;

  /* load up the IP Share Database for cross-reference if it's not loaded yet */
  boot_ipshare();

  send_to_char("&+GList of multiple connections to single host&N\n", ch);
  send_to_char("--------------------------------------------\n", ch);
  /*   debuglog(51, DS_AZUTH, "char [%s]  argument [%s]  cmd [%d]", C_NAME(ch), argument, cmd);*/

  half_chop(argument, name, line);
  /*   debuglog(51, DS_AZUTH, "argument [%s] -> name [%s] + line [%s]", argument, name, line);*/

  biglist[0] = '\0';

  ipset = SetCreate(200, 10); /* initial set should be a little more than max expected */

  /* gather non-playing ones first */
  for (d = descriptor_list; d; d = d->next) {
    if (!d->connected) /* want non-playing sockets only for now */
      continue;

    if (d->character)
      t_ch = d->character;
    else
      t_ch = NULL;

    if (t_ch && (GET_LEVEL(t_ch) > GET_LEVEL(ch)))
      continue; /* hide status on higher levels */

    ipset = AddItem(ipset, full_hostname(d, 0, 0), t_ch ? GET_NAME(t_ch) : "NONE",
            full_username(d, 10, 0), 0, d->descriptor, d->wait / 240, -1, d->connected);
  }

  for (t_ch = PC_list; t_ch; t_ch = t_ch->next) {
    if (t_ch->desc)
      d = t_ch->desc;
    else if (t_ch->only.pc->switched && t_ch->only.pc->switched->desc)
      d = t_ch->only.pc->switched->desc;
    else
      d = NULL;

    if (d && d->connected)
      continue; /* not in game yet */

    if (!t_ch || (t_ch->in_room < 0) || (t_ch->in_room > top_of_world))
      continue; /* not in game */

    if (t_ch->player.name && WIZ_INVIS(ch, t_ch))
      continue;

    if (GET_LEVEL(t_ch) > GET_LEVEL(ch))
      continue; /* skipping any gods higher lvl than you, sorry charlie */

    timer = t_ch->specials.timer; /* idle time */

    /* Get IP Address o_o */
    if (d) {
      ipset = AddItem(ipset, full_hostname(d, 0, 0), t_ch ? GET_NAME(t_ch) : "NONE",
              full_username(d, 10, 0), 0, d->descriptor, timer, t_ch->in_room == NOWHERE ? -1 :
              world[t_ch->in_room].number, d->connected);
    } else {
      ipset = AddItem(ipset, GET_LAST_LOGIN(t_ch), t_ch ? GET_NAME(t_ch) : "NONE",
              "", 1, 0, timer, t_ch->in_room == NOWHERE ? -1 :
              world[t_ch->in_room].number, 11);
    }
  }

  SetSort(ipset, (COMPARE_FCT) sort_by_ip_comparef);
  find_dups(ipset);

  for (x = 0; x < SetNitems(ipset); x++) {
    SetRetreive(ipset, x, (void *) &item);
    if (is_room_an_inn(item->in_room))
      sprintf(room, "&+G%6d&N", item->in_room);
    else
      sprintf(room, "%6d", item->in_room);

    sprinttype(item->state, connected_types, status);

    if (ipshare_search(NULL, item->name))
      strcpy(share, "&+G");
    else
      strcpy(share, "");

    if (item->ip_count == 2) {
      sprintf(tmpstr, "%3d I: %3d  %s%16.16s&n [%s] (%2d) &+c%11.11s&N %10.10s &+Y%s&N\n",
              item->socket, item->idle, share, item->name, room, item->ip_count, status, item->username, item->hostname);
      strcat(biglist, tmpstr);
    } else if (item->ip_count >= 2) {
      sprintf(tmpstr, "%3d I: %3d  %s%16.16s&n [%s] (%2d) &+c%11.11s&N %10.10s &+R%s&N\n",
              item->socket, item->idle, share, item->name, room, item->ip_count, status, item->username, item->hostname);
      strcat(biglist, tmpstr);
    } else
      ;
  }

  SetFree(ipset, NULL);

  page_string(ch->desc, biglist, 1);
}

void do_inventory(P_char ch, char *argument, int cmd) {
  if (IS_AFFECTED(ch, AFF_WRAITHFORM))
    send_to_char("You have no place to keep anything!\n", ch);
  else {
    send_to_char("You are carrying:\n", ch);
    list_obj_to_char(ch->carrying, ch, 1, TRUE);
  }
}

void do_equipment(P_char ch, char *argument, int cmd) {
  P_obj t_obj, poison = NULL;
  bool found;
  char buf[MAX_STRING_LENGTH];
  int j;
  int wear_order[] ={24, 6, 19, 21, 22, 20, 3, 4, 5, 12, 23, 26, 13, 10, 11, 14, 15, 9, 1, 2, 16, 17, 18, 7, 8, 25, -1};

  if (IS_AFFECTED(ch, AFF_WRAITHFORM)) {
    send_to_char("You have no body to wear anything upon!\n", ch);
    return;
  }

  found = FALSE;
  strcpy(buf, "You are using:\n");

  for (j = 0; wear_order[j] != -1; j++) {
    if (ch->equipment[wear_order[j]]) {
      t_obj = ch->equipment[wear_order[j]];
      found = TRUE;
      if (IS_SET(t_obj->extra_flags, ITEM_TWOHANDS)) {
        /* && (GET_OBJ_WEIGHT(t_obj) * 4 > CAN_WIELD_W(ch)) ) { */
        strcat(buf, ((wear_order[j] >= WIELD) && (wear_order[j] <= HOLD) && (t_obj->type != ITEM_WEAPON)) ?
                "<held in both hands> " : "<wielding twohanded> ");
      } else if (t_obj->type == ITEM_FIREWEAPON) {
        if (ch->equipment[PRIMARY_WEAPON] == t_obj)
          strcat(buf, where[PRIMARY_WEAPON]);
        else
          strcat(buf, where[SECONDARY_WEAPON]);
      } else {
        strcat(buf, ((wear_order[j] >= WIELD) && (wear_order[j] <= HOLD) && (t_obj->type != ITEM_WEAPON)) ?
                where[HOLD] : where[wear_order[j]]);
      }
      if (CAN_SEE_OBJ(ch, t_obj)) {
        /* stolen from show_obj_to_char(), so we can buffer it. JAB */
        if (IS_TRUSTED(ch) && IS_CSET(ch->only.pc->pcact, PLR_VNUM))
          sprintf(buf + strlen(buf), "[&+B%5d&N] ",
                (t_obj->R_num >= 0 ? obj_index[t_obj->R_num].virtual : -1));
        if (t_obj->short_description)
          strcat(buf, t_obj->short_description);
        if (IS_OBJ_STAT(t_obj, ITEM_INVISIBLE))
          strcat(buf, " (&+Linvis&n)");
        /* check for poison */
        poison = NULL;
        if ((t_obj->type == ITEM_WEAPON) || (t_obj->type == ITEM_FIREWEAPON))
          for (poison = t_obj->contains; poison; poison = poison->next_content)
            if (poison->type == ITEM_POISON)
              break;
        if (poison)
          strcat(buf, " (&+Gpoisoned&n)");
        if (IS_OBJ_STAT(t_obj, ITEM_SECRET))
          strcat(buf, " (&+Lsecret&n)");
        if (IS_OBJ_STAT(t_obj, ITEM_MAGIC) &&
                (IS_TRUSTED(ch) || IS_AFFECTED(ch, AFF_DETECT_MAGIC)))
          strcat(buf, " (&+bmagic&n)");
        if (IS_OBJ_STAT(t_obj, ITEM_GLOW))
          strcat(buf, " (&+Mglowing&n)");
        if (IS_OBJ_STAT(t_obj, ITEM_LIT) ||
                ((t_obj->type == ITEM_LIGHT) && t_obj->value[2]))
          strcat(buf, " (&+Willuminating&n)");

        strcat(buf, "\n");
      } else {
        strcat(buf, "Something.\n");
      }
    }
  }

  if (!found)
    send_to_char("You aren't wearing anything!\n", ch);
  else
    send_to_char(buf, ch);
}

void do_credits(P_char ch, char *argument, int cmd) {
  page_string(ch->desc, credits, 0);
}

void do_news(P_char ch, char *argument, int cmd) {
  if (cmd == CMD_WIZNEWS)
    page_string(ch->desc, wiznews, 0);
  else if (cmd == CMD_NEWS)
    page_string(ch->desc, news, 0);
}

void do_faq(P_char ch, char *argument, int cmd) {
  page_string(ch->desc, faq, 0);
}

void do_wizlist(P_char ch, char *argument, int cmd) {
  if (IS_ANSI_TERM(ch->desc))
    SEND_TO_Q(wizlista, ch->desc);
  else
    page_string(ch->desc, wizlist, 0);
}

void do_rules(P_char ch, char *argument, int cmd) {
  char Gbuf3[MAX_INPUT_LENGTH];

  sprintf(Gbuf3, "help rules");

  // Let's just pass to help rules, makes this easier. --CRM
  command_interpreter(ch, Gbuf3);

  //page_string(ch->desc, rules, 0);
}

void do_motd(P_char ch, char *argument, int cmd) {
  page_string(ch->desc, motd, 0);
}

void do_changelog(P_char ch, char *argument, int cmd) {
  if (!changelog) {
    send_to_char("Changelog is empty!\n", ch);
    return;
  }
  page_string(ch->desc, changelog, 0);
}

void do_genlog(P_char ch, char *argument, int cmd) {
  if (!genlog) {
    send_to_char("Genlog is empty!\n", ch);
    return;
  }
  page_string(ch->desc, genlog, 0);
}

void do_where(P_char ch, char *argument, int cmd) {
  P_char tch;
  P_obj k, tobj;
  bool flag = FALSE;
  char buf[MAX_STRING_LENGTH], buf2[MAX_STRING_LENGTH];
  int length = 0, count = 0, o_count = 0, v_num;

  /* Executable section */

  if (!ch || !ch->desc)
    return;

  buf[0] = 0;
  length = 0;

  while (*argument == ' ')
    argument++;

  if (!*argument) {
    strcpy(buf, "Players:\n--------\n");

    for (tch = PC_list; tch; tch = tch->next) {
      if (tch->desc && tch->desc->connected)
        continue; /* not in game */

      if ((tch->in_room < 0) || (tch->in_room > top_of_world))
        continue; /* not in game */

      if (!CAN_SEE(ch, tch))
        continue;

      if (!tch->desc && tch->only.pc->switched) { /* switched */
        sprintf(buf2, "%-16s  [%5d] %s\n (switched into) %s - [%d] %s",
                tch->player.name,
                world[tch->in_room].number, world[tch->in_room].name,
                tch->only.pc->switched->player.name,
                world[tch->only.pc->switched->in_room].number, world[tch->only.pc->switched->in_room].name);
      } else {
        sprintf(buf2, "%-16s [%5d] %s",
                tch->player.name, world[tch->in_room].number, world[tch->in_room].name);
        if (!tch->desc)
          strcat(buf2, " (&+BLINKDEAD&n)");
      }
      strcat(buf2, "\n");

      if ((strlen(buf2) + length + 50) > MAX_STRING_LENGTH) {
        strcat(buf, "   ...the list is too long...\n");
        page_string(ch->desc, buf, 1);
        return;
      }
      strcat(buf, buf2);
      length = strlen(buf);
    }

    page_string(ch->desc, buf, 1);
    return;
  }

  /* This chunk of code allows "where <v-number>" if the argument is a
   * number.  It will return all mobs/objects with that v-number.
   * Otherwise it defaults to treating the argument as a string.

   * Added by DTS 7/5/95
   */

  if (is_number(argument)) {
    v_num = atoi(argument);

    /* NPCs */
    for (tch = NPC_list; tch; tch = tch->next) {
      if ((tch->in_room != NOWHERE) && (v_num == mob_index[tch->nr].virtual) && CAN_SEE(ch, tch)) {
        count++;

        sprintf(buf2, "%3d.%-*s [%5d] %s\n",
                count, ansi_comp(tch->player.short_descr, 30), tch->player.short_descr,
                world[tch->in_room].number, world[tch->in_room].name);
        if ((length + strlen(buf2) + 40) > MAX_STRING_LENGTH) {
          strcat(buf, "   ...the list is too long...\n");
          page_string(ch->desc, buf, 1);
          return;
        }
        strcat(buf, buf2);
        length += strlen(buf2);
      }
    }

    if (count)
      strcat(buf, "\n\n"); /* extra lines between mobs/objs */

    /* objects */
    for (k = object_list; k; k = k->next) {
      if ((v_num == obj_index[k->R_num].virtual) && CAN_SEE_OBJ(ch, k)) {

        /* heh, this is ugly, but we don't want people finding wizinvis gods via their objects.  JAB */
        for (tobj = k; OBJ_INSIDE(tobj); tobj = tobj->loc.inside);
        if (!CAN_SEE_OBJ(ch, tobj))
          continue;

        o_count++;
        count++;

        if (k->short_description)
          sprintf(buf2, "%3d.%-*s %s\n",
                o_count, ansi_comp(k->short_description, 30), k->short_description, where_obj(k, FALSE));
        else
          sprintf(buf2, "%3d.%-*s %s\n",
                o_count, 4, "NULL", where_obj(k, FALSE));

        if ((strlen(buf2) + length + 40) > MAX_STRING_LENGTH) {
          strcat(buf, "   ...the list is too long...\n");
          page_string(ch->desc, buf, 1);
          return;
        }
        strcat(buf, buf2);
        length += strlen(buf2);
      }
    }

    if (!count)
      send_to_char("Nothing found.\n", ch);
    else
      page_string(ch->desc, buf, 1);
    return;
  }

  /* "where zone" -- added by DTS 7/6/95 */
  if (is_abbrev(argument, "zone")) {

    strcpy(buf, "Players in this zone:\n---------------------\n");

    for (count = 0, tch = PC_list; tch; tch = tch->next) {
      if (tch->desc && tch->desc->connected)
        continue; /* not in game */

      if ((tch->in_room < 0) || (tch->in_room > top_of_world))
        continue; /* not in game */

      if ((world[tch->in_room].zone != world[ch->in_room].zone) || !CAN_SEE(ch, tch))
        continue;
      count++;
      if (!tch->desc && tch->only.pc->switched) { /* switched */
        sprintf(buf2, "%-16s [%5d] %s\n (switched into) %s - [%d] %s",
                tch->player.name,
                world[tch->in_room].number, world[tch->in_room].name,
                tch->only.pc->switched->player.name,
                world[tch->only.pc->switched->in_room].number, world[tch->only.pc->switched->in_room].name);
      } else {
        sprintf(buf2, "%-16s [%5d] %s",
                tch->player.name, world[tch->in_room].number, world[tch->in_room].name);
        if (!tch->desc)
          strcat(buf2, " (&+BLINKDEAD&n)");
      }
      strcat(buf2, "\n");

      if ((strlen(buf2) + length + 50) > MAX_STRING_LENGTH) {
        strcat(buf, "   ...the list is too long...\n");
        page_string(ch->desc, buf, 1);
        return;
      }
      strcat(buf, buf2);
      length = strlen(buf);
    }
    if (!count)
      strcat(buf, "&+RNONE!&n\n");
    page_string(ch->desc, buf, 1);
    return;
  }

  for (flag = 0, tch = PC_list; tch; tch = tch->next) {

    if (tch->desc && tch->desc->connected)
      continue; /* not in game */

    if ((tch->in_room < 0) || (tch->in_room > top_of_world))
      continue; /* not in game */

    if (!isname(argument, GET_NAME(tch)) || !CAN_SEE(ch, tch))
      continue;

    if (!flag)
      strcat(buf, "PCs:\n");

    flag++;

    if (!tch->desc && tch->only.pc->switched) { /* switched */
      sprintf(buf2, "    %-16s [%5d] %s\n (switched into) %s - [%d] %s",
              tch->player.name,
              world[tch->in_room].number, world[tch->in_room].name,
              tch->only.pc->switched->player.name,
              world[tch->only.pc->switched->in_room].number, world[tch->only.pc->switched->in_room].name);
    } else {
      sprintf(buf2, "    %-16s [%5d] %s",
              tch->player.name, world[tch->in_room].number, world[tch->in_room].name);
      if (!tch->desc)
        strcat(buf2, " (&+BLINKDEAD&n)");
    }
    strcat(buf2, "\n");

    if ((strlen(buf2) + length + 50) > MAX_STRING_LENGTH) {
      strcat(buf, "   ...the list is too long...\n");
      page_string(ch->desc, buf, 1);
      return;
    }
    strcat(buf, buf2);
    length = strlen(buf);
  }

  /* NPCs */
  for (count = 0, tch = NPC_list; tch; tch = tch->next) {
    if ((!isname(argument, GET_NAME(tch)) && !isname(argument, tch->player.short_descr)) ||
            !CAN_SEE(ch, tch) || tch->in_room == NOWHERE)
      continue;

    if (!count) {
      if (flag)
        strcat(buf, "\n");
      strcat(buf, "NPCs:\n");
    }
    count++;

    sprintf(buf2, "%3d.%-*s [%5d] %s\n",
            count, ansi_comp(tch->player.short_descr, 30), tch->player.short_descr,
            world[tch->in_room].number, world[tch->in_room].name);

    if ((length + strlen(buf2) + 40) > MAX_STRING_LENGTH) {
      strcat(buf, "   ...the list is too long...\n");
      page_string(ch->desc, buf, 1);
      return;
    }
    strcat(buf, buf2);
    length += strlen(buf2);
  }

  /* objects */
  for (o_count = 0, k = object_list; k; k = k->next) {
    if ((!isname(argument, k->name) && !isname(argument, k->short_description)) || !CAN_SEE_OBJ(ch, k))
      continue;

    /* heh, this is ugly, but we don't want people finding wizinvis gods via their objects. JAB */
    for (tobj = k; OBJ_INSIDE(tobj); tobj = tobj->loc.inside);
    if (!CAN_SEE_OBJ(ch, tobj))
      continue;

    if (!o_count) {
      if ((flag && !count) || count)
        strcat(buf, "\n");
      strcat(buf, "Objects:\n");
    }
    o_count++;

    if (k->short_description)
      sprintf(buf2, "%3d.%-*s %s\n",
            o_count, ansi_comp(k->short_description, 30), k->short_description, where_obj(k, FALSE));
    else
      sprintf(buf2, "%3d.%-*s %s\n",
            o_count, 4, "NULL", where_obj(k, FALSE));

    if ((strlen(buf2) + length + 40) > MAX_STRING_LENGTH) {
      strcat(buf, "   ...the list is too long...\n");
      page_string(ch->desc, buf, 1);
      return;
    }
    strcat(buf, buf2);
    length += strlen(buf2);
  }

  if (!count && !o_count && !flag)
    send_to_char("Nothing found.\n", ch);
  else
    page_string(ch->desc, buf, 1);
}

void do_levels(P_char ch, char *argument, int cmd) {
  char buf[MAX_STRING_LENGTH], arg[MAX_STRING_LENGTH];
  int i, which = CLASS_NONE;

  if (IS_NPC(ch)) {
    send_to_char("You ain't nothin' but a hound-dog.\n", ch);
    return;
  }

  one_argument(argument, arg);
  if (*arg) {
    switch (LOWER(*arg)) {
      case 'm':
        if (LOWER(*(arg + 1)) == 'a')
          which = CLASS_SORCERER;
        else if (LOWER(*(arg + 1)) == 'e')
          which = CLASS_MERCENARY;
        else if (LOWER(*(arg + 1)) == 'o')
          which = CLASS_MONK;
        else
          which = GET_CLASS(ch);
        break;
      case 'c':
        if (LOWER(*(arg + 1)) == 'l')
          which = CLASS_CLERIC;
        else if (LOWER(*(arg + 1)) == 'o')
          which = CLASS_CONJURER;
        else
          which = GET_CLASS(ch);
        break;
      case 'b':
        if (LOWER(*(arg + 1)) == 'e')
          which = CLASS_BERSERKER;
        else if (LOWER(*(arg + 1)) == 'a')
          which = CLASS_BARD;
        else
          which = GET_CLASS(ch);
        break;
      case 't':
        which = CLASS_THIEF;
        break;
      case 'w':
        which = CLASS_WARRIOR;
        break;
      case 'a':
        if (LOWER(*(arg + 1)) == 's')
          which = CLASS_ASSASSIN;
        else if (LOWER(*(arg + 1)) == 'n')
          which = CLASS_ANTIPALADIN;
        else
          which = GET_CLASS(ch);
        break;
      case 'r':
        if (LOWER(*(arg + 1)) == 'o')
          which = CLASS_ROGUE;
        else if (LOWER(*(arg + 1)) == 'a')
          which = CLASS_RANGER;
        else
          which = GET_CLASS(ch);
        break;
      case 'p':
        if (LOWER(*(arg + 1)) == 'a')
          which = CLASS_PALADIN;
        else if (LOWER(*(arg + 1)) == 's')
          which = CLASS_PSIONICIST;
        else
          which = GET_CLASS(ch);
        break;
      case 'd':
        if (LOWER(*(arg + 1)) == 'i')
          which = CLASS_DIRERAIDER;
        else if (LOWER(*(arg + 1)) == 'r')
          which = CLASS_DRUID;
        break;
      case 'n':
        which = CLASS_NECROMANCER;
        break;
      case 's':
        which = CLASS_SHAMAN;
        break;
      case 'i':
        which = CLASS_INVOKER;
        break;
      case 'e':
        which = CLASS_ENCHANTER;
        break;
      default:
        which = GET_CLASS(ch);
        break;
    }
  } else
    which = GET_CLASS(ch);

  *buf = '\0';

  for (i = 1; i < MAXLVLMORTAL; i++)
    sprintf(buf + strlen(buf), "[%2d %2s] %9d-%-9d\n", i, class_names[which], exp_table[which][i], exp_table[which][i + 1] - 1);

  sprintf(buf + strlen(buf), "[%2d %2s] %9d+\n", i, class_names[which], exp_table[which][50]);

  page_string(ch->desc, buf, 1);
}

/*
 ** Command to ignore certain devious individual.  At most one
 ** person can be ignored at a time.
 */

void do_ignore(P_char ch, char *argument, int cmd) {
  P_char target;
  char arg[MAX_STRING_LENGTH], buf[MAX_STRING_LENGTH];

  if (IS_NPC(ch))
    return;

  one_argument(argument, arg);

  if (!*arg) {
    send_to_char("You feel sociable and stop ignoring anyone.\n", ch);
    ch->only.pc->ignored = NULL;
    return;
  }
  if ((target = get_char_in_game_vis(ch, arg, FALSE)) == NULL) {
    send_to_char("No one by that name here..\n", ch);
    return;
  }
  if (GET_LEVEL(target) > 52) {
    send_to_char("No one by that name here..\n", ch);
    return;
  }
  if (IS_NPC(target)) {
    send_to_char("No one by that name here..\n", ch);
    return;
  }
  if (ch->only.pc->ignored) {
    sprintf(buf, "You stop ignoring %s.\n", GET_NAME(ch->only.pc->ignored));
    ch->only.pc->ignored = NULL;
    send_to_char(buf, ch);
  }
  if (ch == target) {
    send_to_char("You can't ignore yourself!\n", ch);
    return;
  }
  sprintf(buf, "You now ignore %s.\n", GET_NAME(target));
  send_to_char(buf, ch);
  ch->only.pc->ignored = target;
}

#if 1

void do_consider(P_char ch, char *argument, int cmd) {
  P_char victim;
  char name[MAX_STRING_LENGTH];
  int diff, highest = 0;
  char buf[MAX_STRING_LENGTH];

  static const char *undead_type[UNDEAD_TYPES] ={
    "skeleton",
    "zombie",
    "ghoul",
    "shadow",
    "wight",
    "ghast",
    "wraith",
    "spectre",
    "ghost"
  };

  /* Min PC level required to raise the type, Min corpse level to raise as the type, spec modifier */
  static int undead_level[UNDEAD_TYPES] = {
    1, 1, 15, 20, 25, 30, 35, 40, 45
  };

  one_argument(argument, name);

  if (!(victim = get_char_room_vis(ch, name))) {
    send_to_char("Consider killing who?\n", ch);
    return;
  }
  if (victim == ch) {
    send_to_char("Easy! Very easy indeed!\n", ch);
    return;
  }
  if (IS_PC(victim)) {
    send_to_char("Would you like to borrow a cross and a shovel?\n", ch);
    return;
  }
  diff = (GET_LEVEL(victim) - GET_LEVEL(ch));

  if (diff <= -10)
    send_to_char("That creature appears to be no match for you!\n", ch);
  else if (diff <= -5)
    send_to_char("You could do it with a needle!\n", ch);
  else if (diff <= -2)
    send_to_char("Easy.\n", ch);
  else if (diff <= -1)
    send_to_char("Fairly easy.\n", ch);
  else if (diff == 0)
    send_to_char("The perfect match!\n", ch);
  else if (diff <= 1)
    send_to_char("You would need some luck!\n", ch);
  else if (diff <= 2)
    send_to_char("You would need a lot of luck!\n", ch);
  else if (diff <= 3)
    send_to_char("You would need a lot of luck and great equipment!\n", ch);
  else if (diff <= 5)
    send_to_char("Do you feel lucky, punk?\n", ch);
  else if (diff <= 10)
    send_to_char("Are you mad!?\n", ch);
  else if (diff <= 15)
    send_to_char("You ARE mad!\n", ch);
  else if (diff <= 20)
    send_to_char("Why don't you just lie down and pretend you're dead instead?\n", ch);
  else if (diff <= 25)
    send_to_char("What do you want your epitaph to say?!?\n", ch);
  else if (diff <= 100)
    send_to_char("LAUGH This thing will kill you so fast, its not EVEN funny!\n", ch);

  if (GET_CLASS(ch) == CLASS_NECROMANCER || GET_CLASS(ch) == CLASS_LICH) {
    if ((GET_RACE(victim) == RACE_DRAGON || GET_RACE(victim) == RACE_DRAGONKIN) ||
            IS_UNDEAD(victim) || (GET_LEVEL(victim) > GET_LEVEL(ch) + 4 + GET_LEVEL(ch) / 10)) {
      send_to_char("You cannot raise any undead from this being.\n", ch);
      return;
    }
    for (diff = 0; diff < UNDEAD_TYPES; diff++)
      if (GET_LEVEL(victim) >= (undead_level[diff]))
        highest = diff;
      else break;
    if (!highest)
      send_to_char("You cannot raise any undead from this being.\n", ch);
    else {
      sprintf(buf, "The most powerful undead minion you can raise from this being is a %s.", undead_type[highest]);
      act(buf, TRUE, ch, 0, 0, TO_CHAR);
    }
  }

}
#else

void
do_consider(P_char ch, char *argument, int cmd) {
  P_char victim;
  char name[MAX_INPUT_LENGTH];
  int o_estm, u_estm, l_diff, est_hit, est_ac, est_dam, est_spells;

  one_argument(argument, name);

  if (!(victim = get_char_room_vis(ch, name))) {
    send_to_char("Consider killing who?\n", ch);
    return;
  }
  if (victim == ch) {
    send_to_char("Easy! Very easy indeed!\n", ch);
    return;
  }

  /* ok, preliminaries handled, now for the meat.  This first effort uses NO random numbers, and also
     uses no skills.  It does use Int/Wis/Level to affect the accuracy.  The goal here, is to give a
     pretty fair estimate of one character's chances of defeating another in one-on-one combat.  JAB */

  /* ok, here's the most important part of this new function, using levels, and considerers stats, we
     come up with over- and underestimate numbers, then we combine these number to get the actual
     perceptual accuracy.  By making sure that almost all combinations yield SOME error, we now have
     a multiplier to apply to the consideree's abilities.  Other factors will come up, like non-casters
     not being able to accurately guess a caster's abilities, and non-warriors vice versa. */

  /* since overestimation is 'safer' from the considerers point of view, we weight things that way. */

  l_diff = GET_LEVEL(ch) - GET_LEVEL(victim);

  /*     Level 2       Level 25    Level 50  */
  if (l_diff > 7) {
    /* tend to underestimate lesser opponents */
    o_estm = (60 - GET_LEVEL(ch) + l_diff) / 2; /* O: -----------   21 <USER>  <GROUP>%   9 to  29% */
    u_estm = (60 - GET_LEVEL(ch) + l_diff * 2) / 2; /* U: -----------   25 <USER>  <GROUP>%  13 to  54% */
  } else if (l_diff < -7) {
    /* tend to overestimate >= opponents */
    o_estm = (60 - GET_LEVEL(ch) - l_diff * 2) / 2; /* O:  37 to  86%   25 to  51%  13 to  14% */
    u_estm = (60 - GET_LEVEL(ch) - l_diff) / 2; /* U:  30 to  38%   21 to  34%   9 to   9% */
  } else { /*   -7 <= l_diff <= 7   */
    /* targets close to your level yield smallest error ranges */
    o_estm = (60 - GET_LEVEL(ch) + l_diff) / 3; /* O:  17 to  21%    9 to  14%   1 to   5% */
    u_estm = (60 - GET_LEVEL(ch) + l_diff) / 4; /* U:  12 to  16%    7 to  10%   0 to   4% */
  }

  u_estm = u_estm * (550 - GET_C_WIS(ch)) / 550; /* U:   9 to  37%    5 to  40%   0 to  39% */

  o_estm = o_estm * (100 - int_app[GET_C_INT(ch)].learn) / 100;
  u_estm = u_estm * (100 - int_app[GET_C_INT(ch)].learn) / 100;
  /* O:   7 to  83%    3 to  49%   0 to  28% */
  /* U:   3 to  35%    2 to  38%   0 to  37% */

  /* ok, now for the tricky part, we have to consider all pertinent factors, and relate them to
     either the overestimate or underestimate %'s, some will use an average.  Still no random
     numbers, so this function will always return the same answer for any two opponents. */

  /* target hits versus our damage */
  /* our hits versus target damage */
  /* overall rating */
}
#endif

void do_report(P_char ch, char *argument, int cmd) {
  char buf[240], report_name[MAX_INPUT_LENGTH];

  report_name[0] = 0;

  if (GET_CLASS(ch) == CLASS_PSIONICIST)
    sprintf(buf, "I have %d (%d) hits, %d (%d) psp's, %d (%d) movement points.",
          GET_HIT(ch), GET_MAX_HIT(ch),
          GET_MANA(ch), GET_MAX_MANA(ch),
          GET_MOVE(ch), GET_MAX_MOVE(ch));
  else
    sprintf(buf, "I have %d (%d) hits, and %d (%d) movement points.",
          GET_HIT(ch), GET_MAX_HIT(ch), GET_MOVE(ch), GET_MAX_MOVE(ch));


  if (*argument) {
    one_argument(argument, report_name);
    strcat(report_name, " ");
    strcat(report_name, buf);
    do_tell(ch, report_name, -4);
  } else {
    if (GET_CLASS(ch) == CLASS_PSIONICIST)
      do_project(ch, buf, -4);
    else
      do_say(ch, buf, -4);
  }
}

void do_display(P_char ch, char *argument, int cmd) {
  char buf[MAX_STRING_LENGTH];

  if (IS_NPC(ch))
    return;

  one_argument(argument, buf);

  if (*buf) {

    if (!str_cmp("twoline", buf)) {
      if (IS_SET(ch->only.pc->prompt, PROMPT_TWOLINE)) {
        REMOVE_BIT(ch->only.pc->prompt, PROMPT_TWOLINE);
        send_to_char("Twoline display turned &+rOFF&N.\n", ch);
      } else {
        SET_BIT(ch->only.pc->prompt, PROMPT_TWOLINE);
        send_to_char("Twoline display turned &+gON&N.\n", ch);
      }
    } else if (!str_cmp("hits", buf)) {
      if (IS_SET(ch->only.pc->prompt, PROMPT_HIT)) {
        REMOVE_BIT(ch->only.pc->prompt, PROMPT_HIT);
        send_to_char("Hits display turned &+rOFF&N.\n", ch);
      } else {
        SET_BIT(ch->only.pc->prompt, PROMPT_HIT);
        send_to_char("Hits display turned &+gON&N.\n", ch);
      }
    } else if (!str_cmp("maxhits", buf)) {
      if (IS_SET(ch->only.pc->prompt, PROMPT_MAX_HIT)) {
        REMOVE_BIT(ch->only.pc->prompt, PROMPT_MAX_HIT);
        send_to_char("Maxhits display turned &+rOFF&N.\n", ch);
      } else {
        SET_BIT(ch->only.pc->prompt, PROMPT_MAX_HIT);
        send_to_char("Maxhits display turned &+gON&N.\n", ch);
      }
    } else if (!str_cmp("psp", buf)) {
      if (IS_SET(ch->only.pc->prompt, PROMPT_MANA)) {
        REMOVE_BIT(ch->only.pc->prompt, PROMPT_MANA);
        send_to_char("PSP display turned &+rOFF&N.\n", ch);
      } else {
        SET_BIT(ch->only.pc->prompt, PROMPT_MANA);
        send_to_char("PSP display turned &+gON&N.\n", ch);
      }
    } else if (!str_cmp("maxpsp", buf)) {
      if (IS_SET(ch->only.pc->prompt, PROMPT_MAX_MANA)) {
        REMOVE_BIT(ch->only.pc->prompt, PROMPT_MAX_MANA);
        send_to_char("Max PSP display turned &+rOFF&N.\n", ch);
      } else {
        SET_BIT(ch->only.pc->prompt, PROMPT_MAX_MANA);
        send_to_char("Max PSP display turned &+gON&N.\n", ch);
      }
    } else if (!str_cmp("moves", buf)) {
      if (IS_SET(ch->only.pc->prompt, PROMPT_MOVE)) {
        REMOVE_BIT(ch->only.pc->prompt, PROMPT_MOVE);
        send_to_char("Moves display turned &+rOFF&N.\n", ch);
      } else {
        SET_BIT(ch->only.pc->prompt, PROMPT_MOVE);
        send_to_char("Moves display turned &+gON&N.\n", ch);
      }
    } else if (!str_cmp("maxmoves", buf)) {
      if (IS_SET(ch->only.pc->prompt, PROMPT_MAX_MOVE)) {
        REMOVE_BIT(ch->only.pc->prompt, PROMPT_MAX_MOVE);
        send_to_char("Maxmoves display turned &+rOFF&N.\n", ch);
      } else {
        SET_BIT(ch->only.pc->prompt, PROMPT_MAX_MOVE);
        send_to_char("Maxmoves display turned &+gON&N.\n", ch);
      }
    } else if (!str_cmp("tankname", buf)) {
      if (IS_SET(ch->only.pc->prompt, PROMPT_TANK_NAME)) {
        REMOVE_BIT(ch->only.pc->prompt, PROMPT_TANK_NAME);
        send_to_char("Tank name display turned &+rOFF&N.\n", ch);
      } else {
        SET_BIT(ch->only.pc->prompt, PROMPT_TANK_NAME);
        send_to_char("Tank name display turned &+gON&N.\n", ch);
      }
    } else if (!str_cmp("tankcond", buf)) {
      if (IS_SET(ch->only.pc->prompt, PROMPT_TANK_COND)) {
        REMOVE_BIT(ch->only.pc->prompt, PROMPT_TANK_COND);
        send_to_char("Tank condition display turned &+rOFF&N.\n", ch);
      } else {
        SET_BIT(ch->only.pc->prompt, PROMPT_TANK_COND);
        send_to_char("Tank condition display turned &+gON&N.\n", ch);
      }
    } else if (!str_cmp("enemy", buf)) {
      if (IS_SET(ch->only.pc->prompt, PROMPT_ENEMY)) {
        REMOVE_BIT(ch->only.pc->prompt, PROMPT_ENEMY);
        send_to_char("Enemy display turned &+rOFF&N.\n", ch);
      } else {
        SET_BIT(ch->only.pc->prompt, PROMPT_ENEMY);
        send_to_char("Enemy display turned &+gON&N.\n", ch);
      }
    } else if (!str_cmp("enemycond", buf)) {
      if (IS_SET(ch->only.pc->prompt, PROMPT_ENEMY_COND)) {
        REMOVE_BIT(ch->only.pc->prompt, PROMPT_ENEMY_COND);
        send_to_char("Enemy condition display turned &+rOFF&N.\n", ch);
      } else {
        SET_BIT(ch->only.pc->prompt, PROMPT_ENEMY_COND);
        send_to_char("Enemy condition display turned &+gON&N.\n", ch);
      }
    }      /* SAM 7-94 visibility prompt */
    else if ((!str_cmp("vis", buf)) && (GET_LEVEL(ch) > MAXLVLMORTAL)) {
      if (IS_SET(ch->only.pc->prompt, PROMPT_VIS)) {
        REMOVE_BIT(ch->only.pc->prompt, PROMPT_VIS);
        send_to_char("Visibility display turned &+rOFF&N.\n", ch);
      } else {
        SET_BIT(ch->only.pc->prompt, PROMPT_VIS);
        send_to_char("Visibility display turned &+gON&N.\n", ch);
      }
    } else if (!str_cmp("all", buf)) {
      ch->only.pc->prompt =
              (PROMPT_HIT | PROMPT_MAX_HIT | PROMPT_MOVE | PROMPT_MAX_MOVE |
              PROMPT_TANK_NAME | PROMPT_TANK_COND | PROMPT_ENEMY |
              PROMPT_ENEMY_COND | PROMPT_TWOLINE | PROMPT_POSITION);
      if (!((GET_CLASS(ch) == CLASS_WARRIOR) ||
              (GET_CLASS(ch) == CLASS_BERSERKER) ||
              (GET_CLASS(ch) == CLASS_THIEF) ||
              (GET_CLASS(ch) == CLASS_BARD) ||
              (GET_CLASS(ch) == CLASS_MERCENARY) ||
              (GET_CLASS(ch) == CLASS_ROGUE) ||
              (GET_CLASS(ch) == CLASS_ASSASSIN)))
#ifdef PCS_USE_MANA
        ch->only.pc->prompt |= (PROMPT_MANA | PROMPT_MAX_MANA);
#endif
      if (GET_LEVEL(ch) > MAXLVLMORTAL)
        ch->only.pc->prompt |= PROMPT_VIS;
    } else if (!str_cmp("off", buf)) {
      if (ch->only.pc->prompt)
        send_to_char("Turning off display.\n", ch);
      else
        send_to_char("But you aren't displaying anything!\n", ch);
      ch->only.pc->prompt = 0;
    } else if (!str_cmp("time", buf)) {
      if (IS_SET(ch->only.pc->prompt, PROMPT_TIME)) {
        REMOVE_BIT(ch->only.pc->prompt, PROMPT_TIME);
        send_to_char("Time display turned &+rOFF&N.\n", ch);
      } else {
        SET_BIT(ch->only.pc->prompt, PROMPT_TIME);
        send_to_char("Time display turned &+gON&N.\n", ch);
      }
    } else if (!str_cmp("position", buf)) { /* Moradin - Add Position to prompt */
      if (IS_SET(ch->only.pc->prompt, PROMPT_POSITION)) {
        REMOVE_BIT(ch->only.pc->prompt, PROMPT_POSITION);
        send_to_char("Position display turned &+rOFF&N.\n", ch);
      } else {
        SET_BIT(ch->only.pc->prompt, PROMPT_POSITION);
        send_to_char("Position display turned &+gON&N.\n", ch);
      }
    }
    return;
  } else
    send_to_char("Syntax: display <option>\n"
          "Note: You must type the full name of the option listed below.\n"
          "Options: all|off|twoline|time\n"
          "         hits|maxhits|psp|maxpsp|moves|maxmoves|slots|maxslots\n"
          "         tankname|tankcond|enemy|enemycond|position\n", ch);
  return;
}

// Count title length only, NOT including ANSI

int god_title_len(char *title) {
  int len = 0;

  /* Sanity Check */
  if (!title)
    return (0);

  /* Ok, walk through the string and ONLY count characters that are NOT */
  /* part of an ANSI code sequence                                      */

  while (*title != '\0') {

    if (*title == '&') {
      switch (*(title + 1)) {
        case 'N':
        case 'n':
          title += 2;
          break;
        case '+':
          title += 3;
          break;
        case '=':
          title += 4;
          break;
        default:
          title++;
          len++;
          break;
      }
    } else {
      len++;
      title++;
    }
  }

  return (len);
}

/* Set a players title */

void do_title(P_char ch, char *arg, int cmd) {
  P_char t_ch = NULL;
  char Gbuf1[MAX_STRING_LENGTH], name[MAX_INPUT_LENGTH];

  arg = one_argument(arg, name);

  if (!*name || !(t_ch = get_char_in_game_vis(ch, name, FALSE))) {
    send_to_char("Title who?\n", ch);
    return;
  }
  if (IS_NPC(t_ch)) {
    send_to_char("Sorry, mobs don't deserve titles.\n", ch);
    return;
  }
  if ((GET_LEVEL(t_ch) > GET_LEVEL(ch)) && (t_ch != ch)) {
    send_to_char("Sorry, you can't change the title of your superiors!\n", ch);
    return;
  }
  /* eat leading spaces */
  while (*arg == ' ')
    arg++;

  // Don't count ANSI for gods.
  if (IS_TRUSTED(t_ch)) {
    if ((strlen(GET_NAME(t_ch)) + god_title_len(arg)) > 80) {
      send_to_char("Sorry, keep title + name under 80 characters (not including ANSI).\n", ch);
      return;
    }
  } else {
    if ((strlen(arg) + strlen(GET_NAME(t_ch))) > 80) {
      send_to_char("Sorry, keep title + name under 80 characters.\n", ch);
      return;
    }
  }

  if (!*arg) {
    sprintf(Gbuf1, "%s's title cleared.\n", GET_NAME(t_ch));
    send_to_char(Gbuf1, ch);
    set_title(t_ch);
    return;
  }
  sprintf(Gbuf1, "Title Bestowed:\n%s %s\n", GET_NAME(t_ch), arg);
  send_to_char(Gbuf1, ch);

  sprintf(Gbuf1, "char %s title %s", GET_NAME(t_ch), arg);
  do_string(ch, Gbuf1, -4);
}

/* do_glance function taken from shayol mud -DCL */

void do_glance(P_char ch, char *argument, int cmd) {
  P_char tar_char = NULL;
  char Gbuf1[MAX_STRING_LENGTH], name[MAX_INPUT_LENGTH];
  int percent;

  if (!ch->desc)
    return;

  /* Technically, this doesn't have to be here either, but if you
   * want the proper messages, I guess. . . .
   */
  if (IS_AFFECTED(ch, AFF_BLIND)) {
    send_to_char("You can't see a thing, you're blinded!\n", ch);
    return;
  }

  if (!IS_TRUSTED(ch) && !IS_AFFECTED(ch, AFF_WRAITHFORM)) {
    if (IS_AFFECTED(ch, AFF_ULTRAVISION) && IS_SUNLIT(ch->in_room)) {
      send_to_char("&+WArgh!  TOO MUCH LIGHT!\n", ch);
      return;
    } else if (IS_DARK(ch->in_room) && !IS_AFFECTED(ch, AFF_ULTRAVISION)) {
      if (IS_AFFECTED(ch, AFF_INFRAVISION)) {
        send_to_char("You can't make out much detail in the dark with just infravision..\n", ch);
        return;
      } else {
        send_to_char("&+LIt is pitch black...\n", ch);
        return;
      }
    }
  }

  if (*argument) {
    one_argument(argument, name);
    tar_char = get_char_room_vis(ch, name);
    if (!tar_char) {
      send_to_char("You don't see that here.\n", ch);
      return;
    }
  } else if (ch->specials.fighting)
    tar_char = ch->specials.fighting;

  if (!tar_char) {
    send_to_char("Glance at whom?\n", ch);
    return;
  }
  if (GET_MAX_HIT(tar_char) > 0)
    percent = (100 * GET_HIT(tar_char)) / GET_MAX_HIT(tar_char);
  else
    percent = -1; /* How could MAX_HIT be < 1? (-hit items, and some mobs) */

  if (IS_NPC(tar_char) && tar_char->player.short_descr)
    strcpy(Gbuf1, tar_char->player.short_descr);
  else if (GET_NAME(tar_char))
    strcpy(Gbuf1, GET_NAME(tar_char));

  CAP(Gbuf1);

  if (percent >= 100)
    strcat(Gbuf1, " is in an &+Gexcellent&n condition.\n");
  else if (percent >= 90)
    strcat(Gbuf1, " has a &+Yfew scratches&n.\n");
  else if (percent >= 75)
    strcat(Gbuf1, " has &+ysome small wounds and bruises&n.\n");
  else if (percent >= 50)
    strcat(Gbuf1, " has &+Mquite a few wounds&n.\n");
  else if (percent >= 30)
    strcat(Gbuf1, " has &+msome big nasty wounds and scratches&n.\n");
  else if (percent >= 15)
    strcat(Gbuf1, " looks &+Rpretty hurt&n.\n");
  else if (percent >= 0)
    strcat(Gbuf1, " is in &+rawful condition&n.\n");
  else
    strcat(Gbuf1, " is &=rlbleeding awfully from big wounds&n.\n");

  if (affected_by_spell(tar_char, SPELL_ELEMENTAL_FIRE))
    sprintf(Gbuf1 + strlen(Gbuf1), "&+r%s is consumed with &+Relemental fire&N!\n", HSSH(tar_char));

  if (affected_by_spell(tar_char, SPELL_ELEMENTAL_WATER))
    sprintf(Gbuf1 + strlen(Gbuf1), "&+b%s is consumed with &+Belemental water&N!\n", HSSH(tar_char));

  if (affected_by_spell(tar_char, SPELL_ELEMENTAL_EARTH))
    sprintf(Gbuf1 + strlen(Gbuf1), "&+L%s is consumed with &N&+yelemental earth&N!\n", HSSH(tar_char));

  if (affected_by_spell(tar_char, SPELL_ELEMENTAL_AIR))
    sprintf(Gbuf1 + strlen(Gbuf1), "&+c%s is consumed with &+Welemental air&N!\n", HSSH(tar_char));

  if (affected_by_spell(tar_char, SPELL_ELEMENTAL_WARD))
    sprintf(Gbuf1 + strlen(Gbuf1), "&+L%s is protected by a &N&+mcrackling purple&+L elemental ward!&N!\n", HSSH(tar_char));

  if (affected_by_spell(tar_char, SPELL_MIRROR_IMAGE))
    sprintf(Gbuf1 + strlen(Gbuf1), "&+m%s is surrounded by a cluster of &+Wmirror images&N&+m.\n", HSSH(tar_char));

  if (affected_by_spell(tar_char, SPELL_BARKSKIN))
    sprintf(Gbuf1 + strlen(Gbuf1), "%s skin has a barklike texture..\n", HSHR(tar_char));

  if (affected_by_spell(tar_char, SPELL_BLUR))
    sprintf(Gbuf1 + strlen(Gbuf1), "&+B%s form is blurred and difficult to make out!\n", HSHR(tar_char));

  if (affected_by_spell(tar_char, SPELL_DRAGONSCALES))
    sprintf(Gbuf1 + strlen(Gbuf1), "&+Y%s body is covered in dragon scales!\n", HSHR(tar_char));

  if (affected_by_spell(tar_char, SPELL_STONE_SKIN))
    sprintf(Gbuf1 + strlen(Gbuf1), "&+L%s body seems to be made of stone!\n", HSHR(tar_char));

  if (affected_by_spell(tar_char, SPELL_FAERIE_FIRE))
    sprintf(Gbuf1 + strlen(Gbuf1), "&+m%s is outlined with dancing purplish flames!\n", HSSH(tar_char));

  if (affected_by_spell(tar_char, SPELL_SCARLET_OUTLINE))
    sprintf(Gbuf1 + strlen(Gbuf1), "&+r%s is outlined with raging scarlet flames!\n", HSSH(tar_char));


  if (affected_by_spell(tar_char, SPELL_DISPLACEMENT))
    sprintf(Gbuf1 + strlen(Gbuf1), "&+m%s form is displaced, and difficult to track!\n", HSHR(tar_char));


  if (IS_AFFECTED(tar_char, AFF_FIRESHIELD)) {
    if (affected_by_spell(tar_char, SPELL_COLDSHIELD))
      sprintf(Gbuf1 + strlen(Gbuf1), "&+B%s is encased in killing ice!\n", HSSH(tar_char));
    else if (affected_by_spell(tar_char, SPELL_UNHOLY_AURA))
      sprintf(Gbuf1 + strlen(Gbuf1), "&+L%s radiates an awesome, unholy power!\n", HSSH(tar_char));
    else if (affected_by_spell(tar_char, SPELL_VAMPIRIC_CURSE))
      sprintf(Gbuf1 + strlen(Gbuf1), "&+W%s is surrounded by a &+Ybright&+W, glowing aura!\n", HSSH(tar_char));
    else
      sprintf(Gbuf1 + strlen(Gbuf1), "&+R%s is surrounded by burning flames!\n", HSSH(tar_char));
  }
  // Changed so that major globe is bright red and minor globe it dark red -Azuth 7/17/2002
  //  if (IS_AFFECTED(tar_char, AFF_MINOR_GLOBE) || IS_AFFECTED(tar_char, AFF_GLOBE))
  //    sprintf(Gbuf1 + strlen(Gbuf1), "&+r%s's encased in a shimmering globe!\n", HSSH(tar_char));
  if (IS_AFFECTED(tar_char, AFF_GLOBE))
    sprintf(Gbuf1 + strlen(Gbuf1), "&+R%s's encased in a shimmering globe!\n", HSSH(tar_char));
  else if (IS_AFFECTED(tar_char, AFF_MINOR_GLOBE))
    sprintf(Gbuf1 + strlen(Gbuf1), "&+r%s's encased in a shimmering globe!\n", HSSH(tar_char));
  if (IS_AFFECTED(tar_char, AFF_WATERBREATH))
    sprintf(Gbuf1 + strlen(Gbuf1), "&+r%s has little gills in %s neck!\n", HSSH(tar_char), HSHR(tar_char));
  if (IS_AFFECTED(tar_char, AFF_MISSILE_SHIELD))
    sprintf(Gbuf1 + strlen(Gbuf1), "&+b%s is surrounded by an opaque shield of magic.\n", HSSH(tar_char));

  send_to_char(Gbuf1, ch);
}

void do_scan(P_char ch, char *argument, int cmd) {
  int i, to_room, cur_room;
  char Gbuf1[MAX_STRING_LENGTH];
  bool BriefMode;

  const char *directions[] = {
    "north",
    "east",
    "south",
    "west",
    "up",
    "down"
  };

  cur_room = ch->in_room;

  /* Can we see *anything*? */
  if (IS_AFFECTED(ch, AFF_BLIND)) {
    send_to_char("You can't see a damn thing, you're blinded!\n", ch);
    return;
  }

  if (IS_DARK(ch->in_room) && !IS_TRUSTED(ch) && !(IS_AFFECTED(ch, AFF_INFRAVISION) ||
          IS_AFFECTED(ch, AFF_ULTRAVISION))) {
    send_to_char("&+LIt is pitch black...\n", ch);
    return;
  }

  if (IS_SUNLIT(ch->in_room) && !IS_TRUSTED(ch) && IS_AFFECTED(ch, AFF_ULTRAVISION)) {
    send_to_char("&+WArgh!  TOO MUCH LIGHT!&N\n", ch);
    return;
  }

  if (IS_CSET(world[cur_room].room_flags, MAGIC_DARK)) {
    if (IS_TRUSTED(ch))
      send_to_char("&+LRoom is magically dark.\n", ch);
    else {
      send_to_char("&+LIt is pitch black...\n", ch);
      return;
    }
  }

  if ((IS_CSET(world[cur_room].room_flags, ROOM_SOLID_FOG) && !IS_TRUSTED(ch))) {
    send_to_char("&+LA thick bank of solid &+Wfog&+L blocks your vision.\n", ch);
    return;
  }

  /* First we temporarily set BRIEF mode */
  BriefMode = IS_CSET(GET_PLYR(ch)->only.pc->pcact, PLR_BRIEF);

  if (!BriefMode)
    SET_CBIT(GET_PLYR(ch)->only.pc->pcact, PLR_BRIEF);

  /* Now loop through possible exits and look in each one if it's valid */

  for (i = 0; i < 6; i++) {

    if (!(world[cur_room].dir_option[i]))
      continue;

    if (IS_SET((world[cur_room].dir_option[i])->exit_info, EX_CLOSED) ||
            IS_SET((world[cur_room].dir_option[i])->exit_info, EX_SECRET) ||
            IS_SET((world[cur_room].dir_option[i])->exit_info, EX_BLOCKED) ||
            ((world[cur_room].dir_option[i])->to_room == NOWHERE))
      continue;

    /* Looks good, check it out */
    to_room = (world[cur_room].dir_option[i])->to_room;
    sprintf(Gbuf1, "\nYou scan &+B%s&N...\n", directions[i]);
    send_to_char(Gbuf1, ch);

    /* Is our sight blocked? */
    if ((!world[to_room].dir_option[rev_dir[i]] ||
            (world[to_room].dir_option[rev_dir[i]]->to_room != cur_room))
            && GET_LEVEL(ch) == 0 /* !IS_AFFECTED(ch, AFF_FARSEE)*/) {
      /* sight blocked */
      send_to_char("Something seems to be blocking your line of sight.\n", ch);
      continue;
    }

    /* Check for Solid Fog in the adjoining room.. */
    if ((IS_CSET(world[to_room].room_flags, ROOM_SOLID_FOG) && !IS_TRUSTED(ch))) {
      send_to_char("&+LA thick bank of solid &+Wfog&+L blocks your vision.\n", ch);
      continue;
    }

    new_look(ch, NULL, -4, to_room);
  }

  /* Unset BRIEF mode if we set it previously */
  if (!BriefMode)
    REMOVE_CBIT(GET_PLYR(ch)->only.pc->pcact, PLR_BRIEF);
}

/* A mechanical list of all the commands.  Added many options, cause spam was
   awful with ~500 commands.  JAB */

void do_commands(P_char ch, char *arg, int cmd) {
  int no, i, mode, t_pos;
  char Gbuf1[MAX_STRING_LENGTH];

  if (IS_NPC(ch))
    return;

  if (!*arg)
    mode = 0; /* commands legal to level and current position */
  else if (is_abbrev(arg, " all"))
    mode = 1; /* same as old list, everything */
  else if (is_abbrev(arg, " socials"))
    mode = 2; /* only commands that have 'do_action' as their function */
  else if (is_abbrev(arg, " nosocials"))
    mode = 3; /* old list, not including socials */
  else {
    send_to_char("Syntax:  commands [all | socials | nosocials]\n", ch);
    return;
  }

  send_to_char("The following commands are available:\n\n", ch);
  *Gbuf1 = '\0';

  if (IS_AFFECTED(ch, AFF_KNOCKED_OUT))
    t_pos = POS_PRONE + STAT_DEAD;
  else
    t_pos = ch->specials.position;

  for (no = 1, i = 0; *command[i] != '\n'; i++) {
    if ((int) GET_LEVEL(ch) >= cmd_info[i + 1].minimum_level) {
      switch (mode) {
        case 0:
          if ((cmd_info[i + 1].command_pointer == do_action) ||
                  ((cmd_info[i + 1].minimum_position & 3) > (t_pos & 3)) ||
                  ((cmd_info[i + 1].minimum_position & STAT_MASK) > (t_pos & STAT_MASK)))
            continue;
          break;
        case 1:
          break;
        case 2:
          if ((cmd_info[i + 1].command_pointer != do_action))
            continue;
          break;
        case 3:
          if ((cmd_info[i + 1].command_pointer == do_action))
            continue;
          break;
      }

      sprintf(Gbuf1 + strlen(Gbuf1), "%-16s", command[i]);
      if (!(no % 5))
        strcat(Gbuf1, "\n");
      no++;
    }
  }
  sprintf(Gbuf1 + strlen(Gbuf1),
          "\n\nCommands listed:  %d of %d total.   (Use 'commands all' to see a full list)\n", no - 1, i);
  page_string(ch->desc, Gbuf1, 1);
}

void do_trophy(P_char ch, char *arg, int cmd) {
  char *buf;
  struct Trophy_data *t_troph = NULL; //, *t_troph_last = NULL;
  int i2, len = 0, count = 0;
  P_char t_mob;
  char *trop_name;

  if (GET_EXP(ch) < 1) {
    send_to_char("You have 0 XP, thus, no trophies.\n", ch);
    return;
  }

  if (IS_MORPH(ch)) {
    send_to_char("You cannot do this while in this form.\n", ch);
    return;
  }

  for (t_troph = ch->only.pc->Trophies; t_troph; t_troph = t_troph->next)
    count++;

  buf = (char *) malloc(count * 60 + 1024);
  if (!buf)
    dump_core(); // might as well grab it here :P

  sprintf(buf, "&+gKill statistics for %s\n&+Y      Exp   %%   \tName\n", GET_NAME(ch));

  for (t_troph = ch->only.pc->Trophies; t_troph; t_troph = t_troph->next) {
    i2 = real_mobile(t_troph->Vnum);
    if (!mob_index[i2].desc2) {
      t_mob = read_mobile(i2, REAL);
      if (!t_mob)
        continue;

      char_to_room(t_mob, 0, -2);
      trop_name = t_mob->player.short_descr;
      extract_char(t_mob);
      t_mob = NULL;
    } else
      trop_name = mob_index[i2].desc2;

    if ((t_troph->XP > 0) && (i2 != -1) && (((float) t_troph->XP / (float) (GET_EXP(ch))) >= 0.02)) {
      len = strlen(buf);
      sprintf(buf + len, "%s %4.1f   \t%s\n", "\t", 100. * ((float) t_troph->XP / (float) (GET_EXP(ch))), trop_name);
    }

    if (len + 256 > count * 60 + 1024) {
      strcat(buf, "\nToo many records to display, truncating...\n");
      wizlog(51, "Post to code board for Azuth: trophy trunc len = %d, count = %d, victim = %s",
              len, count, GET_NAME(ch));
      break;
    }

    //t_troph_last = t_troph;
  }

  page_string(ch->desc, buf, 1);
  free(buf);
  return;
}

void do_command_stub(P_char ch, char *arg, int cmd) {
  send_to_char("Sorry this command is not available at this time.\n", ch);
  return;
}

/* Adding for new condensed settings - Iyachtu */

void set_condensed_melee(P_char ch, int bit1, int bit2, int bit3, int bit4) {
  if (bit1)
    SET_BIT(ch->only.pc->condensed_flags, BIT_1);
  else
    REMOVE_BIT(ch->only.pc->condensed_flags, BIT_1);

  if (bit2)
    SET_BIT(ch->only.pc->condensed_flags, BIT_2);
  else
    REMOVE_BIT(ch->only.pc->condensed_flags, BIT_2);

  if (bit3)
    SET_BIT(ch->only.pc->condensed_flags, BIT_3);
  else
    REMOVE_BIT(ch->only.pc->condensed_flags, BIT_3);

  if (bit4)
    SET_BIT(ch->only.pc->condensed_flags, BIT_4);
  else
    REMOVE_BIT(ch->only.pc->condensed_flags, BIT_4);
}

void set_condensed_spell(P_char ch, int bit1, int bit2, int bit3, int bit4) {
  if (bit1)
    SET_BIT(ch->only.pc->condensed_flags, BIT_5);
  else
    REMOVE_BIT(ch->only.pc->condensed_flags, BIT_5);

  if (bit2)
    SET_BIT(ch->only.pc->condensed_flags, BIT_6);
  else
    REMOVE_BIT(ch->only.pc->condensed_flags, BIT_6);

  if (bit3)
    SET_BIT(ch->only.pc->condensed_flags, BIT_7);
  else
    REMOVE_BIT(ch->only.pc->condensed_flags, BIT_7);

  if (bit4)
    SET_BIT(ch->only.pc->condensed_flags, BIT_8);
  else
    REMOVE_BIT(ch->only.pc->condensed_flags, BIT_8);
}

// Call is condensed with the appropriate type of condensed message:
// MELEE_HITS, MELEE_SPEC, MELEE_DEF, SPELL_DMG, SPELL_SPEC, SPELL_MESS
// target should be either OTHER (which is 0) or SELF (which is 1)
// Iyachtu

int is_condensed(P_char ch, uint condense, int target) {
  int qualifies = FALSE;

  if (!ch) {
    debuglog(51, DS_BADPARAMS, "Invalid ch to is_condensed");
    return FALSE;
  }

  if (!IS_PC(ch))
    return FALSE;

  /* 0 used for fire/cold/unholy aura shields - Iyachtu */
  if (!condense)
    return FALSE;

  if (!IS_CSET(ch->only.pc->pcact, PLR_CONDENSED))
    return FALSE;

  if (IS_SET(ch->only.pc->condensed_flags, condense))
    qualifies = TRUE;

  else if ((condense == SPELL_DMG) && (IS_SET(ch->only.pc->condensed_flags, SPELL_MESS)))
    qualifies = TRUE;

  if (target) {
    if (condense < SPELL_DMG)
      target = MELEE_SELF;
    else
      target = SPELL_SELF;
    if ((qualifies) && (!IS_SET(ch->only.pc->condensed_flags, target)))
      qualifies = FALSE;
  }

  return qualifies;
}

/*
IP Share board is a pain to look at, we all know this.
What I propose is a database of IP Shares
There are 3 main entities to this, the chars, the person, and the group

Let's start with what I see the file looking like that gets loaded.
1 <EMAIL> Tarnec,Grenan,Roc
1 <EMAIL> Flargle,Shaphor
1 <EMAIL> Reginold,Cleaver,Mokter
2 <EMAIL> char1,char2,char3,char4
2 <EMAIL> char1,char2,char3
3 <EMAIL> char1
3 <EMAIL> char1
etc, etc

The nuber at the left would be the group, and could be statically generated
and internally incremented when new groups are added.

The e-mail addy would represent a person

Basically this would get loaded into a binary tree with charname as the key
each node would contain the group # and the e-mail addy
users m could use the info to highlight known IPShares

ipshare ?
---------
displays the help list above

ipshare list
------------
shows a list of all ip shares similar to what the file layout would be
but more formatted

ipshare search <charname>
-------------------------
would search the list for that char
then display all persons and chars in that group

ipshare remove <charname>
-------------------------
would remove that char from the database
it would then remove the player if it was only char for that player
and if the group is empty at that point it would remove that as well

ipshare addperson <chanrname> <group_no|existing_charname|0> <e-mail>
-------------------------------------------------
would add a new person to an existing group number found via list or search
optionally let the code find the group number via an existing char
if group is 0, this is a new group as well

ipshare addchar <charname> <existing_charname>
----------------------------------------------
would add an additional char to an existing persons list of chars

if a player does not have an e-mail we could use first and last name
but whatever is used it must be unique within a group per person
the group numbers would simply continually grow, holes in them from deletions
would be ok too
-Azuth 6/2002
 */

#define IPSHARE_SYNTAX "\
&+LUsage:&n\n\
   ipshare ?\n\
   ipshare &+RL&nist\n\
   ipshare &+RS&nearch <charname>\n\
   ipshare &+RR&nemove <charname>\n\
   ipshare add&+RC&nhar <charname> <existing_charname>\n\
   ipshare add&+RP&nerson <chanrname> <group_no|existing_charname|0 (adds new group)> <e-mail>\n"

static int sort_by_name_comparef(IPShare **i1, IPShare **i2) {
  return strcmp((*i1)->name, (*i2)->name);
}

static int sort_by_email_comparef(IPShare **i1, IPShare **i2) {
  return strcmp((*i1)->email, (*i2)->email);
}

static int sort_by_group_comparef(IPShare **i1, IPShare **i2) {
  int diff;

  diff = (*i1)->groupno - (*i2)->groupno;
  if (diff < 0)
    return -1;
  else if (diff > 0)
    return 1;
  else
    return 0;
}

static int sort_by_group_email_comparef(IPShare **i1, IPShare **i2) {
  if ((*i1)->groupno - (*i2)->groupno == 0)
    return strcmp((*i1)->email, (*i2)->email);
  else
    return (*i1)->groupno - (*i2)->groupno;
}

void do_ipshare(P_char ch, char *args, int cmd) {
  char arg1[MAX_STRING_LENGTH] = "", arg2[MAX_STRING_LENGTH] = "";
  char arg3[MAX_STRING_LENGTH] = "", arg4[MAX_STRING_LENGTH] = "";

  debuglog(51, DS_AZUTH, "ipshare: args[%s]", args);
  args = one_argument(args, arg1);
  //   debuglog(51, DS_AZUTH, "ipshare: args[%s] arg1[%s]", args, arg1);

  if (!*arg1 || *arg1 == '?') {
    send_to_char(IPSHARE_SYNTAX, ch);
    return;
  }

  boot_ipshare(); // if it's not loaded already load it

  if (!strcmp(arg1, "list") || *arg1 == 'l') {
    send_to_char("&+LIP Share Database List&n\n", ch);
    send_to_char("-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=\n", ch);
    ipshare_list(ch);
    return;
  } else if (!strcmp(arg1, "search") || *arg1 == 's') {
    args = one_argument(args, arg2);
    if (*arg2 && strlen(arg2) < 17) {
      ipshare_search(ch, arg2);
      return;
    }
  } else if (!strcmp(arg1, "remove") || *arg1 == 'r') {
    args = one_argument(args, arg2);
    if (*arg2 && strlen(arg2) < 17) {
      ipshare_remove(ch, arg2);
      return;
    }
  } else if (!strcmp(arg1, "addchar") || *arg1 == 'c') {
    args = one_argument(args, arg2);
    if (*arg2 && strlen(arg2) < 17) {
      args = one_argument(args, arg3);
      if (*arg3) {
        ipshare_addchar(ch, arg2, arg3);
        return;
      }
    }
  } else if (!strcmp(arg1, "addperson") || *arg1 == 'p') {
    args = one_argument(args, arg2);
    if (*arg2 && strlen(arg2) < 17) {
      args = one_argument(args, arg3);
      if (*arg3) {
        args = one_argument(args, arg4);
        if (*arg4) {
          if (strlen(arg4) > 127) {
            send_to_char("e-mail too big (127 char max)", ch);
            return;
          }

          ipshare_addperson(ch, arg2, arg3, arg4);
          return;
        }
      }
    }
  } else {
    send_to_char(IPSHARE_SYNTAX, ch);
    return;
  }

  /* if we get here they need help */
  send_to_char(IPSHARE_SYNTAX, ch);
}

void ipshare_list(P_char ch) {
  int x = 0, len = 0;
  char buf[MAX_STRING_LENGTH] = "";
  char dashes[36] = "-----------------------------------";
  char prev_email[128] = "";
  IPShare *item = NULL;

  SetSort(ipshare, (COMPARE_FCT) sort_by_group_email_comparef); // this display probably best for list

  for (x = 0; x < SetNitems(ipshare); x++) {
    SetRetreive(ipshare, x, (void *) &item);
    if (!item)
      continue;

    if (!strcmp(prev_email, item->email))
      sprintf(buf + strlen(buf), ", &+W%s&n", item->name);
    else {
      len = strlen(item->email);
      sprintf(buf + strlen(buf), "%s&+Y%3d&n &+R%s&n %*.*s &+W%s&n", *prev_email ? "\n" : "",
              item->groupno, item->email, 35 - len, 35 - len, dashes, item->name);
      strcpy(prev_email, item->email);
    }
  }

  sprintf(buf + strlen(buf), "\n");
  page_string(ch->desc, buf, 1);
}

/* call with NULL ch to just find out if they in IP Share Database */
int ipshare_search(P_char ch, char *arg) {
  int x = 0, len = 0;
  IPShare *find = NULL, **found = NULL, *item = NULL;
  char buf[MAX_STRING_LENGTH];
  char dashes[31] = "------------------------------", color[4] = "";
  char prev_email[128] = "";

  CREATE(find, IPShare, 1);
  strcpy(find->name, arg);
  *(find->name) = toupper(*(find->name));

  SetSort(ipshare, (COMPARE_FCT) sort_by_name_comparef); // insure it's sorted right for the search

  found = (IPShare **) SetSearch(ipshare, &find, (COMPARE_FCT) sort_by_name_comparef);
  if (found) {
    if (!ch) {
      free(find);
      return TRUE;
    }

    find->groupno = (*found)->groupno;

    SetSort(ipshare, (COMPARE_FCT) sort_by_group_email_comparef);

    for (x = 0; x < SetNitems(ipshare); x++) {
      SetRetreive(ipshare, x, (void *) &item);
      if (!item)
        continue;

      if (item->groupno < find->groupno)
        continue;

      if (item->groupno > find->groupno)
        break;

      if (!strcmp(prev_email, item->email)) {
        if (!strcmp(find->name, item->name))
          strcpy(color, "&+B");
        else
          strcpy(color, "&+W");

        sprintf(buf, ", %s%s&n", color, item->name);
        send_to_char(buf, ch);
      } else {
        len = strlen(item->email);
        if (!strcmp(find->name, item->name))
          strcpy(color, "&+B");
        else
          strcpy(color, "&+W");

        sprintf(buf, "%s&+Y%3d&n &+R%s&n %*.*s %s%s&n", *prev_email ? "\n" : "", item->groupno, item->email,
                30 - len, 30 - len, dashes, color, item->name);
        send_to_char(buf, ch);
        strcpy(prev_email, item->email);
      }
    }
  } else {
    if (!ch) {
      free(find);
      return FALSE;
    }

    sprintf(buf, "&+B%s&n is not in the IP Share Database\n", find->name);
    send_to_char(buf, ch);
  }

  free(find);
  send_to_char("\n", ch);
  if (found)
    return TRUE;

  return FALSE;
}

void ipshare_remove(P_char ch, char *arg) {
  int x = 0;
  char buf[MAX_STRING_LENGTH] = "";
  IPShare *find = NULL, **found = NULL, *item = NULL;

  if (GET_LEVEL(ch) < 55) {
    send_to_char("Sorry charlie, you're not powerful enough to do this.\n", ch);
    return;
  }

  CREATE(find, IPShare, 1);
  strcpy(find->name, arg);
  *(find->name) = toupper(*(find->name));

  SetSort(ipshare, (COMPARE_FCT) sort_by_name_comparef); // insure it's sorted right for the search

  found = (IPShare **) SetSearch(ipshare, &find, (COMPARE_FCT) sort_by_name_comparef);
  if (!found) {
    send_to_char("They're not in the IP Share Database you fool!\n", ch);
    free(find);
    return;
  }

  for (x = 0; x < SetNitems(ipshare); x++) {
    SetRetreive(ipshare, x, (void *) &item);
    if (!item)
      continue;

    if (item == *found) {
      SetRemove(ipshare, x, free);
      break;
    }
  }

  free(find);
  *arg = toupper(*arg);
  sprintf(buf, "&+B%s&n removed from IP Share Database\n", arg);
  send_to_char(buf, ch);
  send_to_char("\n", ch);
  logit(LOG_IPSHARE, "%s removed of %s", GET_NAME(ch), arg);
  wizlog(GET_LEVEL(ch), "%s removed %s from IP share database.", GET_NAME(ch), arg);

  ipshare_write(ipshare);
  if (!SetNitems(ipshare))
    groupno = 1;
}

void ipshare_addchar(P_char ch, char *arg1, char *arg2) {
  char buf[MAX_STRING_LENGTH] = "";
  IPShare *find = NULL, **found = NULL, *item = NULL;

  CREATE(find, IPShare, 1);
  strcpy(find->name, arg1);
  *(find->name) = toupper(*(find->name));

  SetSort(ipshare, (COMPARE_FCT) sort_by_name_comparef); // insure it's sorted right for the search

  found = (IPShare **) SetSearch(ipshare, &find, (COMPARE_FCT) sort_by_name_comparef);
  if (found) {
    send_to_char("They're already in the IP Share Database you fool!\n", ch);
    free(find);
    return;
  }

  strcpy(find->name, arg2);
  *(find->name) = toupper(*(find->name));

  found = (IPShare **) SetSearch(ipshare, &find, (COMPARE_FCT) sort_by_name_comparef);
  if (!found) {
    *arg2 = toupper(*arg2);
    sprintf(buf, "&+B%s&n does not exist in the IP Share Database to add to.\n", arg2);
    send_to_char(buf, ch);
    free(find);
    return;
  }

  CREATE(item, IPShare, 1);

  item->groupno = (*found)->groupno;
  strcpy(item->email, (*found)->email);
  *arg1 = toupper(*arg1);
  strcpy(item->name, arg1);

  ipshare = SetAdd(ipshare, item);

  free(find);
  *arg2 = toupper(*arg2);
  sprintf(buf, "&+B%s&n added to list of chars for &+B%s&n\n", arg1, arg2);
  send_to_char(buf, ch);
  send_to_char("\n", ch);
  logit(LOG_IPSHARE, "%s added char %s to %s's group", GET_NAME(ch), arg1, arg2);
  wizlog(GET_LEVEL(ch), "%s added char %s to %s's IP share group.", GET_NAME(ch), arg1, arg2);
  ipshare_write(ipshare);
}

void ipshare_addperson(P_char ch, char *arg1, char *arg2, char *arg3) {
  char buf[MAX_STRING_LENGTH] = "";
  IPShare *find = NULL, **found = NULL, *item = NULL;

  CREATE(find, IPShare, 1);
  strcpy(find->name, arg1);
  strcpy(find->email, arg3);
  *(find->name) = toupper(*(find->name));

  SetSort(ipshare, (COMPARE_FCT) sort_by_email_comparef); // insure it's sorted right for the search

  found = (IPShare **) SetSearch(ipshare, &find, (COMPARE_FCT) sort_by_email_comparef);
  if (found) {
    sprintf(buf, "That email is already in use by &+B%s&n, try again!\n", (*found)->name);
    send_to_char(buf, ch);
    free(find);
    return;
  }

  SetSort(ipshare, (COMPARE_FCT) sort_by_name_comparef); // insure it's sorted right for the search

  found = (IPShare **) SetSearch(ipshare, &find, (COMPARE_FCT) sort_by_name_comparef);
  if (found) {
    send_to_char("They're already in the IP Share Database you fool!\n", ch);
    free(find);
    return;
  }

  if (!isdigit(*arg2)) {
    strcpy(find->name, arg2);
    *(find->name) = toupper(*(find->name));

    found = (IPShare **) SetSearch(ipshare, &find, (COMPARE_FCT) sort_by_name_comparef);
    if (!found) {
      *arg2 = toupper(*arg2);
      sprintf(buf, "&+B%s&n does not exist in the IP Share Database to add to.\n", arg2);
      send_to_char(buf, ch);
      free(find);
      return;
    }

    CREATE(item, IPShare, 1);

    item->groupno = (*found)->groupno;
    strcpy(item->email, arg3);
    *arg1 = toupper(*arg1);
    strcpy(item->name, arg1);
  } else if (*arg2 != '0') // they are telling us which group to add this person to via the number
  {
    SetSort(ipshare, (COMPARE_FCT) sort_by_group_comparef); // insure it's sorted right for the search

    find->groupno = atoi(arg2); // might need to check errno here someday

    found = (IPShare **) SetSearch(ipshare, &find, (COMPARE_FCT) sort_by_group_comparef);
    if (!found) {
      sprintf(buf, "Group number %d does not exist in the IP Share Database to add to.\n", find->groupno);
      send_to_char(buf, ch);
      free(find);
      return;
    }

    CREATE(item, IPShare, 1);

    item->groupno = (*found)->groupno;
    strcpy(item->email, arg3);
    *arg1 = toupper(*arg1);
    strcpy(item->name, arg1);
  } else if (*arg2 == '0') // groupno 0 requesting auto-assign
  {
    CREATE(item, IPShare, 1);

    item->groupno = groupno++;
    strcpy(item->email, arg3);
    *arg1 = toupper(*arg1);
    strcpy(item->name, arg1);
  } else {
    debuglog(51, DS_AZUTH, "ipshare_addperson: We have a problem houston!");
    send_to_char("Report this problem to Azuth! mention code 42\n", ch);
    free(find);
    return;
  }

  free(find);
  ipshare = SetAdd(ipshare, item);
  *arg1 = toupper(*arg1);
  *arg2 = toupper(*arg2);
  sprintf(buf, "Added %s to %s with e-mail %s\n", arg1, arg2, arg3);
  send_to_char(buf, ch);
  send_to_char("\n", ch);
  logit(LOG_IPSHARE, "%s added person %s to %s with e-mail %s", GET_NAME(ch), arg1, arg2, arg3);
  wizlog(GET_LEVEL(ch), "%s added person %s to %s with e-mail %s to IP share.", GET_NAME(ch), arg1, arg2, arg3);
  ipshare_write(ipshare);
}

UtilSet ipshare_load(UtilSet set) {
  int group = 0, rc = 0;
  FILE *fl = NULL;
  IPShare *item = NULL;
  char *p = NULL;
  char tmp[MAX_STRING_LENGTH] = "", names[1024] = "", email[128] = "";


  fl = fopen(IPSHARE_FILE, "r+t");
  if (!fl) {
    debuglog(51, DS_AZUTH, "Opps ipshare_database missing!");
    wizlog(51, "IP Share Database File missing: to correct this touch %s\n", IPSHARE_FILE);
    return NULL;
  }

  do {
    fgets(tmp, MAX_STRING_LENGTH - 1, fl);
    if (feof(fl))
      break;

    //      debuglog(51, DS_AZUTH, "fgets() [%s]", tmp);

    rc = sscanf(tmp, "%d %s %s", &group, email, names);
    if (rc == 3) {
      groupno = MAX(groupno, group + 1); // groupno is the next auto-assigned group number

      p = strtok(names, ",");
      if (p) {
        CREATE(item, IPShare, 1);

        item->groupno = group;
        strcpy(item->email, email);
        strcpy(item->name, p);

        set = SetAdd(set, item);
        p = strtok(NULL, ",");
      }

      while (p) {
        CREATE(item, IPShare, 1);

        item->groupno = group;
        strcpy(item->email, email);
        strcpy(item->name, p);

        set = SetAdd(set, item);
        p = strtok(NULL, ",");
      }
    } else
      logit(LOG_DEBUG, "IPSHARE read error: rc [%d]%s", rc, tmp);

  } while (!feof(fl));

  fclose(fl);

  SetSort(set, (COMPARE_FCT) sort_by_name_comparef); // makes the set searchable on load

  return set;
}

void ipshare_write(UtilSet set) {
  FILE *fl = NULL;
  IPShare *item = NULL;
  int x = 0;


  SetSort(ipshare, (COMPARE_FCT) sort_by_name_comparef); // makes set searchable if first write

  fl = fopen(IPSHARE_FILE, "w+t");
  if (!fl) {
    debuglog(51, DS_AZUTH, "Opps ipshare_database missing!");
    return;
  }

  for (x = 0; x < SetNitems(ipshare); x++) {
    SetRetreive(ipshare, x, (void *) &item);
    if (!item)
      continue;

    fprintf(fl, "%d %s %s\n", item->groupno, item->email, item->name);
  }

  fclose(fl);
}

void boot_ipshare(void) {
  if (!ipshare) {
    ipshare = SetCreate(200, 10);
    ipshare = ipshare_load(ipshare);
  }
}

void shutdown_ipshare(void) {
  if (ipshare) {
    SetFree(ipshare, free);
    ipshare = NULL;
  }
}

void do_stop_pet_follow(P_char ch, char *argument, int cmd) {
  if (!ch)
    return;

  stop_pet_follow(ch);

}

void do_stop_pc_follow(P_char ch, char *argument, int cmd) {
  if (!ch)
    return;

  stop_pc_follow(ch, argument);
}

