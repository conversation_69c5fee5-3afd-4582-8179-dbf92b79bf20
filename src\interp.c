/* ***************************************************************************
 *  File: interp.c                                             Part of Outcast *
 *  Usage: main command interpreter, and anciliary functions.                *
 *  Copyright  1990, 1991 - see 'license.doc' for complete information.      *
 *  Copyright  1994, 1995 - Outcast Systems Ltd.                             *
 *************************************************************************** */

#include <arpa/telnet.h>
#include <ctype.h>
#include <stdio.h>
#include <string.h>

#include "comm.h"
#include "db.h"
#include "interp.h"
#include "prototypes.h"
#include "spells.h"
#include "race_class.h"
#include "structs.h"
#include "utils.h"
#include "specs.include.h"

/* strnlen is now standard in string.h, no need for custom implementation */

/* external variables */

extern P_index mob_index;
extern P_index obj_index;
extern P_room world;
extern char debug_mode;
extern const int exp_table[TOTALCLASS][52];
extern int hometown[];
extern int no_specials;
extern int forced_command;
extern int pulse;               /* TAM 2/94 */

bool command_confirm;
int grant_cmds[MAX_GRANT_CMDS][6];
int num_grant_cmds = 0;

/* special case, won't be needed when these are everwhere */
int gond_proc(P_char, P_char, int, char *);

/*=========================================================================*/
/*
 *    Global variables
 */
/*=========================================================================*/

struct command_info cmd_info[MAX_CMD_LIST];

/* NOTE!  the order of this list is IMPORTANT! and must match up with the list
   of #define's in interp.h. */

const char *command[] =
{
   "north",                      /* 1 */
   "east",
   "south",
   "west",
   "up",
   "down",
   "enter",
   "exits",
   "kiss",
   "get",
   "drink",                      /* 11 */
   "eat",
   "wear",
   "wield",
   "look",
   "score",
   "say",
   "gshout",
   "tell",
   "inventory",
   "qui",                        /* 21 */
   "bounce",
   "smile",
   "dance",
   "kill",
   "cast",
   "laugh",
   "giggle",
   "shake",
   "project",
   "group",                      /* 31 */
   "scream",
   "insult",
   "comfort",
   "nod",
   "sigh",
   "sulk",
   "help",
   "who",
   "emote",
   "echo",                       /* 41 */
   "stand",
   "sit",
   "rest",
   "sleep",
   "wake",
   "force",
   "transfer",
   "hug",
   "snuggle",
   "cuddle",                     /* 51 */
   "nuzzle",
   "cry",
   "news",
   "equipment",
   "buy",
   "sell",
   "value",
   "list",
   "drop",
   "goto",                       /* 61 */
   "weather",
   "read",
   "pour",
   "grab",
   "remove",
   "put",
   "shutdow",
   "save",
   "hit",
   "string",                     /* 71 */
   "give",
   "quit",
   "stat",
   "innate",
   "time",
   "load",
   "purge",
   "shutdown",
   "idea",
   "typo",                       /* 81 */
   "bug",
   "whisper",
   "cackle",
   "at",
   "ask",
   "order",
   "sip",
   "taste",
   "snoop",
   "follow",                     /* 91 */
   "rent",
   "offer",
   "poke",
   "advance",
   "acc",
   "grin",
   "bow",
   "open",
   "close",
   "lock",                       /* 101 */
   "unlock",
   "mreport",
   "applaud",
   "blush",
   "burp",
   "chuckle",
   "clap",
   "cough",
   "curtsey",
   "fart",                       /* 111 */
   "flip",
   "fondle",
   "frown",
   "gasp",
   "glare",
   "groan",
   "grope",
   "hiccup",
   "lick",
   "love",                       /* 121 */
   "moan",
   "nibble",
   "pout",
   "purr",
   "ruffle",
   "shiver",
   "shrug",
   "sing",                       /* 129 - special case for bards */
   "slap",
   "smirk",                      /* 131 */
   "snap",
   "sneeze",
   "snicker",
   "sniff",
   "snore",
   "spit",
   "squeeze",
   "stare",
   "strut",
   "thank",                      /* 141 */
   "twiddle",
   "wave",
   "whistle",
   "wiggle",
   "wink",
   "yawn",
   "snowball",
   "write",
   "hold",
   "flee",                       /* 151 */
   "sneak",
   "hide",
   "backstab",
   "pick",
   "steal",
   "bash",
   "rescue",
   "kick",
   "french",
   "comb",                       /* 161 */
   "massage",
   "tickle",
   "practice",
   "pat",
   "examine",
   "take",
   "info",
   "spells",
   "practise",
   "curse",                      /* 171 */
   "use",
   "where",
   "levels",
   "reroll",
   "pray",
   ":",
   "beg",
   "bleed",
   "cringe",
   "dream",                      /* 181 */
   "fume",
   "grovel",
   "hop",
   "nudge",
   "peer",
   "point",
   "ponder",
   "punch",
   "snarl",
   "spank",                      /* 191 */
   "steam",
   "tackle",
   "taunt",
   "think",
   "whine",
   "worship",
   "yodel",
   "toggle",
   "wizmsg",
   "consider",                   /* 201 */
   "growl",
   "restore",
   "return",
   "switch",                     /* 205 */
   "quaff",
   "recite",
   "users",
   "pose",
   "silence",
   "wizhelp",                    /* 211 */
   "credits",
   "disband",
   "vis",
   "lflags",                     /* 215 */
   "poofin",
   "wizlist",
   "display",
   "echoa",
   "demote",                     /* 220 */
   "poofout",
   "circle",
   "balance",
   "wizlock",
   "deposit",                    /* 225 */
   "withdraw",
   "ignore",
   "drain",
   "title",
   "aggr",                       /* 230 */
   "gsay",
   "consent",
   "setbit",
   "hitall",
   "trap",                       /* 235 */
   "murder",
   "glance",
   "puke",
   "massproject",
   "fill",                       /* 240 */
   "ooc",
   "nokill",
   "page",
   "commands",
   "attributes",                 /* 245 */
   "rules",
   "track",
   "listen",
   "disarm",
   "pet",                        /* 250 */
   "delete",
   "ban",
   "allow",
   "play",
   "move",                       /* 255 */
   "bribe",
   "bonk",
   "calm",
   "rub",
   "censor",                     /* 260 */
   "choke",
   "drool",
   "flex",
   "jump",
   "lean",                       /* 265 */
   "moon",
   "ogle",
   "pant",
   "pinch",
   "push",                       /* 270 */
   "scare",
   "scold",
   "seduce",
   "shove",
   "shudder",                    /* 275 */
   "shush",
   "slobber",
   "smell",
   "sneer",
   "spin",                       /* 280 */
   "squirm",
   "stomp",
   "strangle",
   "stretch",
   "tap",                        /* 285 */
   "tease",
   "tip",
   "tweak",
   "twirl",
   "undress",                    /* 290 */
   "whimper",
   "exchange",
   "release",
   "search",
   "join",                       /* 295 */
   "camp",
   "secret",
   "lookup",
   "report",
   "split",                      /* 300 */
   "world",
   "junk",
   "petition",
   "do",
   "'",                          /* 305 */
   "caress",
   "bury",
   "donate",
   "shout",
   "disembark",                  /* 310 */
   "panic",
   "nog",
   "twibble",
   "bleh",
   "lightning",                  /* 315 */
   "sweep",
   "apologize",
   "afk",
   "lag",
   "touch",                      /* 320 */
   "scratch",
   "wince",
   "toss",
   "flame",
   "arch",                       /* 325 */
   "amaze",
   "bathe",
   "embrace",
   "brb",
   "ack",                        /* 330 */
   "cheer",
   "snort",
   "eyebrow",
   "bang",
   "pillow",                     /* 335 */
   "nap",
   "nose",
   "raise",
   "hand",
   "pull",                       /* 340 */
   "tug",
   "wet",
   "mosh",
   "wait",
   "hi5",                        /* 345 */
   "envy",
   "flirt",
   "bark",
   "whap",
   "roll",                       /* 350 */
   "blink",
   "doh",
   "gag",
   "grumble",
   "dropkick",                   /* 355 */
   "whatever",
   "fool",
   "noogie",
   "meditate",
   "smoke",                      /* 360 */
   "wheeze",
   "bird",
   "boggle",
   "hiss",
   "bite",                       /* 365 */
   "teleport",
   "bandage",
   "blow",
   "bored",
   "bye",                        /* 370 */
   "congratulate",
   "duck",
   "flutter",
   "goose",
   "gulp",                       /* 375 */
   "halo",
   "hello",
   "hickey",
   "hose",
   "hum",                        /* 380 */
   "impale",
   "jam",
   "kneel",
   "mourn",
   "protect",                    /* 385 */
   "puzzle",
   "roar",
   "rose",
   "salute",
   "skip",                       /* 390 */
   "swat",
   "tongue",
   "woops",
   "zone",
   "trip",                       /* 395 */
   "melt",
   "shapechange",
   "assist",
   "doorbash",
   "exp",                        /* 400 */
   "rofl",
   "agree",
   "happy",
   "pucker",
   "spam",                       /* 405 */
   "beer",
   "bodyslam",
   "sacrifice",
   "terminate",
   "cd",                         /* 410 */
   "memorize",
   "forget",
   "headbutt",
   "shadow",
   "ride",                       /* 415 */
   "mount",
   "dismount",
   "debug",
   "freeze",
   "bbl",                        /* 420 */
   "gape",
   "veto",
   "jk",
   "tiptoe",
   "grunt",                      /* 425 */
   "holdon",
   "imitate",
   "tango",
   "tarzan",
   "pounce",                     /* 430 */
   "cheek",
   "layhand",
#ifndef NEW_AWARE
   "awareness",
#else
   "",
#endif
   "selfpreservation",
   "springleap",                 /* 435 */
   "feigndeath",
   "chant",
   "drag",
   "speak",
   "reload",                     /* 440 */
   "dragonpunch",
   "revoke",
   "grant",
   "whod",
   "motd",                       /* 445 */
   "zreset",
   "full",
   "welcome",
   "introduce",
   "sweat",                      /* 450 */
   "mutter",
   "lucky",
   "ayt",
   "fidget",
   "fuzzy",                      /* 455 */
   "snoogie",
   "ready",
   "plonk",
   "hero",
   "lost",                       /* 460 */
   "clear",
   "flash",
   "curious",
   "hunger",
   "thirst",                     /* 465 */
   "echoz",
   "ptell",
   "scribe",
   "teach",
   "reinitphys",                 /* 470 */
   "finger",
   "accept",
   "decline",
   "summon",
   "clone",                      /* 475 */
   "apply",
   "zap",
   "alert",
   "recline",
   "knock",                      /* 480 */
   "skills",
   "powercast",
   "berserk",
   "faq",
   "disengage",                  /* 485 */
   "retreat",
   "inroom",
   "which",
   "revoketitle",
   "sethome",                    /* 490 */
   "notes",
   "wiznews",
   "abort",
   "ceasefire",
   "athrow",                     /* 495 */
   "afire",
   "throw",
   "fire",
   "ammo",                       /* 499 */
   "mindblast",                  /* 500 */
   "adrenalize",
   "enhance",
   "sustain",
   "disperse",
   "combatmind",                 /* 505 */
   "bodyshift",
   "amplify",
   "aurasight",
   "empathy",
   "equalibrium",                /* 510 */
   "",
   "battletrance",
   "dominate",
   "",
   "rift",                       /* 515 */
   "ultrablast",
   "dangersense",
   "projectforce",
   "detonate",
   "deathfield",                 /* 520 */
   "bodycontrol",
   "catfall",
   "flesharmor",
   "reduce",
   "expand",                     /* 525 */
   "shift",
   "massdominate",
   "globe",
   "tower",
   "attract",                    /* 530 */
   "synapticstatic",
   "alteraura",
   "canibalize",
   "skillenhance",
   "stasisfield",                /* 535 */
   "pask",
   "darkness",
   "collect",
   "prep",                       /* 539 */
   "chill",
   "snakebite",
   "tailsweep",
   "befriend reptile",
   "forage",
   "detecttrap",                 /* 545 */
   "disarmtrap",
   "sign",
   "escape",
   "rename",
   "loadchar",                   /* 550 */
   "getcart",
   "leavecart",
   "changelog",
   "scan",
   "shieldpunch",                /* 555 */
   "disguise",
   "store",
   "stable",
   "retrieve",
   "claim",                      /* 560 */
   "ccontrol",
   "acontrol",
   "assoc",
   "asclist",
   "ems",                        /* 565 */
   "reject",
   "outcast",
   "estat",
   "elist",
   "elook",                      /* 570 */
   "ehead",
   "radd",
   "rshow",
   "rdel",
   "rlist",                      /* 575 */
   "ekill",
   "efreeze",
   "ethaw",
   "ereset",
   "ehelp",                      /* 580 */
   "eforce",
   "estatus",
   "ungroup",
   "gget",
   "gadd",                       /* 585 */
   "gdel",
   "ggive",
   "gboggle",
   "gtell",
   "glist",                      /* 590 */
   "sojparse",
   "hcontrol",
   "house",
   "construct",
   "nhc",                        /* 595 */
   "sethelper",
   "proc",
   "dice",
   "justice",
   "lwitness",                   /* 600 */
   "emit",
   "charge",
   "function",
   "pay",
   "turnin",                     /* 605 */
   "crimereport",
   "pardon",
   "duel",
   "accuse",
   "loot",                       /* 610 */
   "tieup",
   "untie",
   "unbind",
   "virtuoso",
   "assassinate",                /* 615 */
   "garrote",
   "contract",
   "trophy",
   "recall",
   "jcontrol",                   /* 620 */
   "genlog",
#if 0
   "create",
   "delete",
   "edit",
   "makearea",                   /* 620 */
   "newarea",
#endif
   "olc",
   "olr",
   "olcdb",
   "statnopage",                 /* 625 */
   "mix",
   "lifetap",
   "mobgen",
   "achexit",
   "horde",                      /* 630 */
   "auction",
   "greset",
   "gcombat",
   "cstat",
   "cp",                         /* 635 */
   "undecline",
   "deny",
   "tithe",
   "chargepsp",
   "reply",                      /* 640 */
   "greport",
   "lore",
   "echot",
   "warchant",
   "arena",                      /* 645 */
   "qwiz",
   "mread",
   "mwrite",
   "mlist",
   "mdelete",                    /* 650 */
   "nuke",
   "prioritize",
   "accompany",                  /* 653 */
   "climb",                      /* 654 */
   "mreply",                     /* 655 */
   "ipshare",                    /* 656 */
   "addprestige",                /* 657 */
   "findquest",                  /* 658 */
   "loadmob",                    /* 659 */
   "allowgroup",                 /* 660 */
   "howl",                       /* 661 */
   "outflank",                   /* 662 */
   "strafe",                     /* 663 */
   "hire",                       /* 664 */
   "embark",                     /* 665 */
   "deliver",                    /* 666 */
   "appoint",                    /* 667 */
   "storage",                    /* 668 */
   "gamble",                     /* 669 */
   "empty",                      /* 670 */
   "reaggro",                    /* 671 */
   "deaggro",                    /* 672 */
   "createmob",                  /* 673 */
   "hamstring",                  /* 674 */
   "rptoggle",                   /* 675 */
   "depart",                     /* 676 */
   "reorder",                    /* 677 */
   "togpet",                     /* 678 */
   "togpc",        /* 679 */
   "setpk",        /* 680 */
   "eqrate",       /* 681 */
   "\n"
};

const char *fill[] =
{
   "in",
   "from",
   "with",
   "the",
   "on",
   "at",
   "to",
   "\n"
};

int search_block(char *arg, int arg_sz, const char **list, int exact)
{
   register int i, l;

   if(!arg)
      return -1;

   /* Make into lower case, and get length of string */
   for(l = 0; (l < arg_sz) && *(arg + l); l++)
      *(arg + l) = LOWER (*(arg + l));

   if(exact)
      {
      for(i = 0; **(list + i) != '\n'; i++)
         if(!str_cmp (arg, *(list + i)))
            return(i);
      }
   else
      {
      if(!l)
         l = 1;                    /* Avoid "" to match the first available string */
      for(i = 0; **(list + i) != '\n'; i++)
         if(!strn_cmp (arg, *(list + i), (unsigned) l))
            return(i);
      }

   return(-1);
}

int old_search_block(const char *argument, uint begin, uint length, const char **list, int mode)
{
   int guess, found, search;

   if(!argument)
      return -1;

   /* If the word contain 0 letters, then a match is already found */
   found = (length < 1);
   guess = 0;
   /* Search for a match */

   if(mode)
      while(!found && (*(list[guess]) != '\n'))
         {
         found = (length == strlen (list[guess]));
         for(search = 0; (search < length) && found; search++)
            found = (*(argument + begin + search) == *(list[guess] + search));
         guess++;
         }
   else
      {
      while(!found && *(list[guess]) != '\n')
         {
         found = 1;
         for(search = 0; (search < length) && found; search++)
            found = (*(argument + begin + search) == *(list[guess] + search));
         guess++;
         }
      }

   return(found ? guess : -1);
}

/* SAM 7-94, command confirmation */
void do_confirm(P_char ch, int yes)
{
   if(IS_NPC (ch) || !ch->desc) /* was a force, or npc doing it. */
      return;

   /* the putz just entered yes or no without a pending command */
   if(ch->desc->confirm_state != CONFIRM_AWAIT)
      {
      send_to_char ("What do you wish to confirm?\n", ch);
      return;
      }
   /* they said no */
   if(!yes)
      {
      ch->desc->confirm_state = CONFIRM_NONE;
      send_to_char ("Did not think you wanted to do that... :P\n", ch);
      return;
      }
   /* they said yes, so do it */
   ch->desc->confirm_state = CONFIRM_DONE;
   command_interpreter (ch, ch->desc->last_command);
}

/*
   **    Improvements/Additions
   **
   ** 1) disallow certain commands when paralyzed. --TAM
   ** 2) disallow all but stand command when berserked recently --TAM
   ** 3) disallow all commands for instant kill when used recently --TAM
   ** 4) slow rate which commands are parsed if affected by slow.  commands
   **    are ignored and thrown away. --TAM
   ** 5) add in ability to force certain commands to require confirmation
   **    to be executed. --SAM 7-94
   **
 */
void command_interpreter(P_char ch, char *argument)
{
   char *ch_ptr;
   int cmd, i, j, k, granted = FALSE;
   uint look_at, begin;
#if 0
   struct affected_type *af, *af_next_dude;
#endif

   if(ch->in_room == NOWHERE)
      return;

   //sprintf(Gbuf1, "&+WDEBUG -> command_interpreter called. argument: (%s)\n", argument);
   //send_to_char(Gbuf1, ch);

   if(debug_mode)
      cmdlog (ch, argument);

   if(IS_PC (ch) && IS_CSET (ch->only.pc->pcact, PLR_FROZEN) && !forced_command && (cmd != CMD_FREEZE))
      {
      send_to_char ("You are frozen! You can't do anything! Nah Nah! :P\n", ch);
      return;
      }
   /* Find first non blank */
   for(begin = 0; (*(argument + begin) == ' '); begin++);

   /* Find length of first word */
   for(look_at = 0; *(argument + begin + look_at) > ' '; look_at++)
      {
      /* Make all letters lower case AND find length */
      *(argument + begin + look_at) = LOWER (*(argument + begin + look_at));
      }

   if(IS_PC (ch) && ch->desc)
      if(ch->desc->confirm_state == CONFIRM_AWAIT)
         {
         if(*(argument + begin) == 'y')
            {
            do_confirm (ch, 1);
            return;
            }
         else if(*(argument + begin) == 'n')
            {
            do_confirm (ch, 0);
            return;
            }
         }
   cmd = old_search_block (argument, begin, look_at, command, 0);

   if(!cmd)
      return;

   if(IS_AFFECTED (ch, AFF_CASTING))
      {
      if(cmd != CMD_ABORT)
         {
         send_to_char ("You're busy spellcasting! \n", ch);
         return;
         }
      }
   if((IS_AFFECTED (ch, AFF_KNOCKED_OUT)) && (cmd != CMD_COMMANDS) && !forced_command)
      if((STAT_MASK & cmd_info[cmd].minimum_position) != STAT_DEAD)
         {
         send_to_char ("Being knocked unconscious strictly limits what you can do.\nTry typing 'commands'.\n", ch);
         return;
         }
   if(IS_AFFECTED (ch, AFF_BRAINDRAIN))
      {
      send_to_char ("But you are busy sucking on a brain already!\n", ch);
      return;
      }
   /* gee, why didn't anyone think of this before? JAB */
   if(!IS_TRUSTED (ch) && IS_SONIC_CMD (cmd) &&
      (is_silent (ch, FALSE) && !(GET_RACE (ch) == RACE_ILLITHID)) && !forced_command)
      {
      send_to_char ("Your lips move, but no sound is heard...\n", ch);
      return;
      }
   /* Check for ansi characters, mortals not allowed to use them to put color in says, shouts, gossips,
      titles, etc. SAM 6-94 */
   if((GET_LEVEL(GET_PLYR(ch)) < MINLVLIMMORTAL) && (cmd != CMD_ASSOC))
      {
      for(ch_ptr = argument; *ch_ptr != '\0'; ch_ptr++)
         {
         if(*ch_ptr == '&')
            switch(*(ch_ptr + 1))
               {
               case '+':
               case '-':
               case '=':
               case 'n':
               case 'N':
                  send_to_char ("Pardon? No ansi chars allowed as input.\n", ch);
                  return;
                  break;
               }
         }
      }
   if((cmd > 0) && (cmd_info[cmd].command_pointer != 0))
      {
      if(!MIN_POS (ch, cmd_info[cmd].minimum_position) ||
         (IS_FIGHTING (ch) && !cmd_info[cmd].in_battle))
         {
         if(GET_STAT (ch) < (cmd_info[cmd].minimum_position & STAT_MASK))
            switch(GET_STAT (ch))
               {
               case STAT_DEAD:
                  send_to_char ("Lie still; you are DEAD!!!\n", ch);
                  break;
               case STAT_INCAP:
               case STAT_DYING:
                  send_to_char ("You are in pretty bad shape, unable to do anything!\n", ch);
                  break;
               case STAT_SLEEPING:
                  send_to_char ("In your dreams, or what?\n", ch);
                  break;
               case STAT_RESTING:
                  send_to_char ("Nah... You feel too relaxed to do that...\n", ch);
                  break;
               }
         if(GET_POS (ch) < (cmd_info[cmd].minimum_position & 3))
            switch(GET_POS (ch))
               {
               case POS_PRONE:
                  send_to_char ("Sorry, you can't do that while laying around.\n", ch);
                  break;
               case POS_KNEELING:
                  send_to_char ("Maybe you should get up off your knees first?\n", ch);
                  break;
               case POS_SITTING:
                  send_to_char ("Maybe you should get on your feet first?\n", ch);
                  break;
               }
         if(IS_FIGHTING (ch) && !cmd_info[cmd].in_battle)
            send_to_char ("Sorry, you aren't allowed to do that in combat.\n", ch);

         if(IS_CSET(ch->specials.affects, AFF_GARROTING) && cmd_info[cmd].in_battle)
            send_to_char("How? You're busy garroting someone!\n", ch);

         return;
         }
      else
      /* Check for equalibrium? DMB 10/17/98 */
         {
         if((IS_AFFECTED (ch, AFF_MINOR_PARALYSIS) ||
             IS_AFFECTED (ch, AFF_MAJOR_PARALYSIS)) &&
            !CAN_CMD_PARALYSIS (cmd) && !IS_TRUSTED (ch) && !forced_command)
            {
            if((IS_NPC (ch) || IS_NPC (ch)) && ch->following)
               send_to_char ("It can't, it's paralyzed.\n", ch->following);
            else if(ch)
               send_to_char ("You can't!  You're paralyzed to the bone.\n", ch);
            return;
            }
         if(IS_AFFECTED(ch, AFF_BOUND) && CANT_CMD_BOUND(cmd) && !IS_TRUSTED(ch)
            && !forced_command)
            {
            if((IS_NPC(ch) || IS_NPC(ch)) && ch->following)
               send_to_char ("It's bound, it can't do much.\n", ch->following);
            else if(ch)
               send_to_char("You can't! You're tied up!\n", ch);
            return;
            }
#if 0
         /* if berserked recently, don't let em do anything, except stand */
         if(ch->specials.action_delays[ACT_DELAY_BERSERK] && (IS_NPC (ch) || GET_LEVEL (ch) <= 50) && (cmd != CMD_STAND))
            {
            send_to_char ("Your recent berserker rage has you too disoriented to do anything but fight.\n", ch);
            return;
            }
#endif
         // Allow some more communication while scribing --CRM
         if(IS_AFFECTED (ch, AFF_SCRIBING) &&
            (cmd != CMD_TELL &&
             cmd != CMD_TOGGLE &&
             cmd != CMD_ACC &&
             cmd != CMD_SAY &&
             cmd != CMD_OOC &&
             cmd != CMD_NCC &&
             cmd != CMD_MOVE &&
             cmd != CMD_LOOK &&
             cmd != CMD_WHISPER &&
             cmd != CMD_HELP &&
             cmd != CMD_INFO &&
             cmd != CMD_NEWS &&
             cmd != CMD_SCORE &&
             cmd != CMD_REPLY &&
             cmd != CMD_ATTRIBUTES &&
             cmd != CMD_PETITION) && !forced_command)
            {
            send_to_char ("You're busy scribing a spell into your spellbook!\n", ch);
            return;
            }
#if 0
         /* if instant killed recently, can't do anything but fight */
         if(ch->specials.action_delays[ACT_DELAY_INSTANTKILL])
            {
            if(ch->specials.fighting)
               {
               act ("You ARE too determined to kill $N at the moment.",
                    FALSE, ch, 0, ch->specials.fighting, TO_CHAR);
               return;
               }
            }
#endif
#if 0
         /* headbutt allowed once per 3 rds. of combat.  reset outisde of combat. --TAM */
         if(ch->specials.action_delays[ACT_DELAY_HEADBUTT] > 0)
            {
            if(IS_FIGHTING (ch))
               {
               act ("You're still spinning in circles from your last headbutt.",
                    FALSE, ch, 0, 0, TO_CHAR);
               if(IS_NPC(ch) && IS_PET(ch) && IS_AFFECTED(ch, AFF_ORDERED) &&
                  ch->following && IS_AFFECTED(ch, AFF_CHARM))
                  {
                  for(af = ch->affected; af; af = af_next_dude)
                     {
                     af_next_dude = af->next;
                     if(af->type == TYPE_ORDERED)
                        break;
                     }
                  if(af)
                     {
                     force_attach_order(ch, af->instruction);
                     CharWait(ch, PULSE_VIOLENCE / 4);
                     }
                  }
               return;
               }
            }
#endif

         if(((cmd_info[cmd].minimum_position & STAT_MASK) > STAT_SLEEPING) ||
            (cmd_info[cmd].command_pointer == do_action))
            {
            if(CMD_NUKES_HIDE(cmd) && IS_AFFECTED (ch, AFF_HIDE))
               {
               send_to_char("You stop trying to hide.\n", ch);
               REMOVE_CBIT (ch->specials.affects, AFF_HIDE);
               REMOVE_CBIT (ch->specials.affects, AFF_CAMO);
               }
            if((cmd < 7) && !IS_AFFECTED(ch, AFF_SNEAK))
               {
               REMOVE_CBIT (ch->specials.affects, AFF_HIDE);
               REMOVE_CBIT (ch->specials.affects, AFF_CAMO);
               }

            // Camo vapes on movement now
            if((cmd < 7) && IS_AFFECTED(ch, AFF_CAMO))
               {
               REMOVE_CBIT (ch->specials.affects, AFF_HIDE);
               REMOVE_CBIT (ch->specials.affects, AFF_CAMO);
               }
            if((cmd != CMD_PRAY) && (cmd != CMD_MEMORIZE) && (cmd != CMD_NCC) &&
               IS_AFFECTED (ch, AFF_MEDITATE))
               {
               send_to_char ("You stop meditating.\n", ch);
               REMOVE_CBIT (ch->specials.affects, AFF_MEDITATE);
               }
            if(!IS_TRUSTED(ch) && IS_CSET (ch->only.pc->pcact, PLR_AFK))
               REMOVE_CBIT (ch->only.pc->pcact, PLR_AFK);
            if(IS_AFFECTED(ch, AFF_CHARGING) && (cmd != CMD_CHARGE && cmd != CMD_MEDITATE))
               {
               REMOVE_CBIT (ch->specials.affects, AFF_CHARGING);
               nukeAllPSPCrystalsExcept(ch, NULL, FALSE);
               nukeChargeEvent(ch);
               }
            }
         if(!no_specials && (ch->in_room != NOWHERE) && special (ch, cmd, argument + begin + look_at))
            return;

         if(ch->in_room == NOWHERE)
            return;

#if 0                           /* no longer do we need such a hack -- DDW */
         /* Hack for item_teleport objects */
         if(check_item_teleport (ch, argument + begin + look_at, cmd))
            return;
#endif

         granted = is_granted(ch, cmd);

         /* made it so mobs god are in can use qwiz -Azuth */
         if(!granted)
            {
            if(cmd == CMD_QWIZ)
               {
               if(ch->desc && ch->desc->original)
                  {
                  if(!is_granted(ch->desc->original, cmd))
                     {
                     send_to_char ("Pardon?\n", ch);
                     return;
                     }
                  }
               else if(IS_MORPH(ch))
                  {
                  if(!is_granted(MORPH_ORIG(ch), cmd))
                     {
                     send_to_char ("Pardon?\n", ch);
                     return;
                     }
                  }
               else
                  {
                  send_to_char ("Pardon?\n", ch);
                  return;
                  }
               }
            else
               {
               send_to_char ("Pardon?\n", ch);
               return;
               }
            }

         //      if((ch) && (ch->desc))
         //      {
         //        sprintf(Gbuf1, "DEBUG -> command_interp:executing. confirm_state: (%d), cmd: (%d)\n",
         //          ch->desc->confirm_state, cmd);
         //        send_to_char(Gbuf1, ch);
         //      }

         /* execute the bloody thing!!! */
         if((cmd_info[cmd].req_confirm == 1) && (IS_NPC (ch) ||
                                                 (ch->desc->confirm_state == CONFIRM_DONE)))
            {
            if(ch->desc)
               ch->desc->confirm_state = CONFIRM_NONE;
            command_confirm = TRUE;
            ((*cmd_info[cmd].command_pointer) (ch, argument + begin + look_at, cmd));
            }
         else if(cmd_info[cmd].req_confirm == 1)
            {
            if(ch->desc)
               ch->desc->confirm_state = CONFIRM_AWAIT;
            strcpy (ch->desc->last_command, argument);
            command_confirm = FALSE;
            ((*cmd_info[cmd].command_pointer) (ch, argument + begin + look_at, cmd));
            }
         else
            {
            if(ch->desc)
               ch->desc->confirm_state = CONFIRM_NONE;
            ((*cmd_info[cmd].command_pointer) (ch, argument + begin + look_at, cmd));
            }
         }
      return;
      }
   /* Unknown or un-implemented command */

   if(IS_TRUSTED (ch) && sscanf (argument + begin, "%d ", &j) == 1)
      /* sick multicommand kludge, not yet supported 'officially',
         but I wasn't able to live without this. So sue me. -Torm */
      if((j >= 1) && (j <= 20) && !sscanf (argument + begin + look_at, " %d ", &k))
         {

         k = ch->in_room;
         for(i = 0; (i < j) && CAN_ACT (ch) && (k == ch->in_room); i++)
            {
            //sprintf(Gbuf1,
            //  "&+WDEBUG -> command_interp:torm code!!! O_O: arg: (%s), begin: (%d), look_at: (%d)\n",
            //  argument, begin, look_at);
            //send_to_char(Gbuf1, ch);
            command_interpreter (ch, argument + begin + look_at);
            }
         return;
         }
   if((cmd > 0) && (cmd_info[cmd].command_pointer == 0))
      send_to_char ("Sorry, but that command has yet to be implemented...\n", ch);
   else
      send_to_char ("Pardon?\n", ch);

   if(ch->desc)
      ch->desc->confirm_state = CONFIRM_NONE;
}

void argument_interpreter(char *argument, char *first_arg, char *second_arg)
{
   int look_at, found, begin;

   if(!argument)
      {
      *first_arg = '\0';
      *second_arg = '\0';
      return;
      }
   found = begin = 0;

   if(strlen (argument) >= MAX_INPUT_LENGTH)
      {
      logit (LOG_SYS, "too long arg in argument_interpreter.");
      *(first_arg) = '\0';
      *(second_arg) = '\0';
      return;
      }
   do
      {
      /* Find first non blank */
      for(; *(argument + begin) == ' '; begin++);

      /* Find length of first word */
      for(look_at = 0; *(argument + begin + look_at) > ' '; look_at++)
         /* Make all letters lower case, AND copy them to first_arg */
         *(first_arg + look_at) = LOWER (*(argument + begin + look_at));
      *(first_arg + look_at) = '\0';
      begin += look_at;

      }
   while(fill_word (first_arg));

   do
      {
      /* Find first non blank */
      for(; *(argument + begin) == ' '; begin++);

      /* Find length of first word */
      for(look_at = 0; *(argument + begin + look_at) > ' '; look_at++)
         /* Make all letters lower case, AND copy them to second_arg */
         *(second_arg + look_at) = LOWER (*(argument + begin + look_at));

      *(second_arg + look_at) = '\0';
      begin += look_at;

      }
   while(fill_word (second_arg));
}

int is_number(char *str)
{
   int look_at;

   if(*str == '\0')
      return(0);

   for(look_at = 0; *(str + look_at) != '\0'; look_at++)
      if((*(str + look_at) < '0') || (*(str + look_at) > '9'))
         return(0);
   return(1);
}

/* find the first sub-argument of a string, return pointer to first char in
   primary argument, following the sub-arg                     */
char *one_argument(char *argument, char *first_arg)
{
   int found, begin, look_at;
   size_t arg_len;

   found = begin = 0;

   if(!argument || !first_arg)
      {
      if(first_arg)
         *first_arg = '\0';
      return NULL;
      }
   
   /* Check if argument points to valid string */
   arg_len = strnlen(argument, MAX_INPUT_LENGTH + 1);
   if(arg_len >= MAX_INPUT_LENGTH)
      {
      logit (LOG_SYS, "too long arg in one_argument.");
      *(first_arg) = '\0';
      return NULL;
      }
   
   do
      {
      /* Find first non blank - check bounds */
      while(begin < arg_len && argument[begin] != '\0' && isspace(argument[begin]))
         begin++;
      
      /* Check if we reached the end */
      if(begin >= arg_len || argument[begin] == '\0')
         {
         *first_arg = '\0';
         return(argument + begin);
         }

      /* Find length of first word - check bounds */
      for(look_at = 0; 
          begin + look_at < arg_len && 
          argument[begin + look_at] != '\0' && 
          argument[begin + look_at] > ' ' &&
          look_at < MAX_INPUT_LENGTH - 1; 
          look_at++)
         {
         /* Make all letters lower case, AND copy them to first_arg */
         *(first_arg + look_at) = LOWER(argument[begin + look_at]);
         }

      *(first_arg + look_at) = '\0';
      begin += look_at;
      }
   while(fill_word(first_arg));

   return(argument + begin);
}

int fill_word(char *argument)
{
   return(search_block (argument, strlen (argument), fill, TRUE) >= 0);
}

/* Same as one_argument() except that it doesn't ignore fill words - Urdlen */
char *any_one_arg(char *argument, char *first_arg)
{
   skip_spaces(&argument);

   while(*argument && !isspace(*argument))
      {
      *(first_arg++) = LOWER(*argument);
      argument++;
      }

   *first_arg = '\0';

   return(argument);
}

/*
 * Same as one_argument() except that it takes two args and returns the rest.
 */
char *two_arguments(char *argument, char *first_arg, char *second_arg)
{
   return(one_argument(one_argument(argument, first_arg), second_arg));
}

/* determine if a given string is an abbreviation of another */
/*
 * Urdlen: Made this a bit faster using pure pointers instead of integer
 * increment.
 */
int is_abbrev(const char *arg1, const char *arg2)
{
   if(!*arg1)
      return(FALSE);

   for(; *arg1 && *arg2; arg1++, arg2++)
      if(LOWER(*arg1) != LOWER(*arg2))
         return(FALSE);

   if(!*arg1)
      return(TRUE);
   else
      return(FALSE);
}

/* return first 'word' plus trailing substring of input string */
/*
 * Cleaned this up a bit by adding any_one_arg() and using the existing
 * skip_spaces() function. -Urdlen
 */
void half_chop(char *string, char *arg1, char *arg2)
{
   if(!string)
      {
      *arg1 = '\0';
      *arg2 = '\0';
      return;
      }
   if(strlen (string) >= MAX_INPUT_LENGTH)
      {
      logit (LOG_SYS, "too long arg in half_chop.");
      *(arg1) = '\0';
      *(arg2) = '\0';
      return;
      }
   for(; isspace (*string); string++);

   for(; !isspace (*arg1 = *string) && *string; string++, arg1++);

   *arg1 = '\0';

   for(; isspace (*string); string++);

   for(; (*arg2 = *string); string++, arg2++);
   //  char *temp;
   //
   //  temp = any_one_arg(string, arg1);
   //  skip_spaces(&temp);
   //  strcpy(arg2, temp);
}

bool special(P_char ch, int cmd, char *arg)
{
   int j;
   register P_char k;
   register P_obj i;
   struct func_attachment *fn;

   /* special in room? */
   if(world[ch->in_room].funct)
      if((*world[ch->in_room].funct) (ch->in_room, ch, cmd, arg))
         return(1);

      /* previous proc might have killed ch */
   if(ch->in_room == NOWHERE)
      return 1;

   /* special in equipment list? */
   for(j = 0; j <= (MAX_WEAR - 1); j++)
      if(ch->equipment[j] && (ch->equipment[j]->R_num >= 0) &&
         (obj_index[ch->equipment[j]->R_num].spec_flag & IDX_COMMAND))
         {
         fn = obj_index[ch->equipment[j]->R_num].func;
         while(fn && ch->equipment[j] && (ch->equipment[j]->R_num >= 0))
            {
            if(!(fn->proc_flag & IDX_COMMAND) || !(*fn->func.obj) (ch->equipment[j], ch, cmd, arg))
               fn = fn->next;
            else
               return(1);
            }
         }

      /* previous proc might have killed ch */
   if(ch->in_room == NOWHERE)
      return 1;

   /* special in inventory? */
   for(i = ch->carrying; i; i = i->next_content)
      if(i && (i->R_num >= 0) && (obj_index[i->R_num].spec_flag & IDX_COMMAND))
         {
         fn = obj_index[i->R_num].func;
         while(fn && i && (i->R_num >= 0))
            {
            if(!(fn->proc_flag & IDX_COMMAND) || !(*fn->func.obj) (i, ch, cmd, arg))
               fn = fn->next;
            else
               return(1);
            }
         }

      /* previous proc might have killed ch */
   if(ch->in_room == NOWHERE)
      return 1;

   if((IS_NPC(ch) && !ALONE (ch)) || IS_PC(ch))
      {     /* dont process if mob is alone, but do so for PCs */
      LOOP_THRU_PEOPLE (k, ch)
#ifdef PCPROCS
      if(AWAKE (k) && IS_SET(GET_SPEC_FLAG(k), IDX_COMMAND))
         {
#else
      if(IS_NPC (k) && AWAKE (k) && (mob_index[k->nr].spec_flag & IDX_COMMAND))
         {
#endif
         fn = GET_SPEC_FN(k);
         //      while (fn && k && (k->nr >= 0)) {
         while(fn && k)
            {
            if(!(fn->proc_flag & IDX_COMMAND) || !(*fn->func.ch) (k, ch, cmd, arg))
               fn = fn->next;
            else
               return(1);
            }
         }
      }

   /* previous proc might have killed ch */
   if(ch->in_room == NOWHERE)
      return 1;

   /* special in object present? */
   for(i = world[ch->in_room].contents; i; i = i->next_content)
      if(i && (i->R_num >= 0) && (obj_index[i->R_num].spec_flag & IDX_COMMAND))
         {
         fn = obj_index[i->R_num].func;
         while(fn && i && (i->R_num >= 0))
            {
            if(!(fn->proc_flag & IDX_COMMAND) || !(*fn->func.obj) (i, ch, cmd, arg))
               fn = fn->next;
            else
               return(1);
            }
         }

   return(0);
}

/* 6 macros for command assignment:
   CMD_Y() -     may be used while fighting
   CMD_N() -     may NOT be used while fighting
   CMD_CNF_Y() - REQUIRES confirmation, and may be used while fighting
   CMD_CNF_N() - REQUIRES confirmation, and may NOT be used while fighting
   CMD_TRIG() -  Used for reserved keywords that don't DO anything, but are
   used to trigger specials.
   CMD_SOC() -   Social, calls do_action
   CMD_GRT() -   must be granted to be usable.

   first 4 have same format,
   (command number (macro from interp.h),
   minimum position/status,
   name of the routine to invoke for this command,
   minimum level to use this command)

   CMD_TRIG() only requires (command number, min level)
   CMD_SOC() only requires (command number, min position)

   for routines requiring confirmation, the burden of handling that
   confirmation falls on the routine, do NOT just add one, things WILL screw
   up (and probably crash).

   JAB */

#define CMD_N(number, min_pos, pointer, min_level) {   \
   cmd_info[(number)].command_pointer = (pointer);     \
   cmd_info[(number)].minimum_position = (min_pos);    \
   cmd_info[(number)].in_battle = FALSE;        \
   cmd_info[(number)].minimum_level = (min_level);      \
   cmd_info[(number)].grantable = 0;    \
   cmd_info[(number)].req_confirm = FALSE;}

#define CMD_Y(number, min_pos, pointer, min_level) {   \
   cmd_info[(number)].command_pointer = (pointer);     \
   cmd_info[(number)].minimum_position = (min_pos);    \
   cmd_info[(number)].in_battle = TRUE; \
   cmd_info[(number)].minimum_level = (min_level);      \
   cmd_info[(number)].grantable = 0;    \
   cmd_info[(number)].req_confirm = FALSE;}

#define CMD_CNF_Y(number, min_pos, pointer, min_level) {   \
   cmd_info[(number)].command_pointer = (pointer);     \
   cmd_info[(number)].minimum_position = (min_pos);    \
   cmd_info[(number)].in_battle = TRUE; \
   cmd_info[(number)].minimum_level = (min_level);      \
   cmd_info[(number)].grantable = 0;    \
   cmd_info[(number)].req_confirm = TRUE;}

#define CMD_CNF_N(number, min_pos, pointer, min_level) {   \
   cmd_info[(number)].command_pointer = (pointer);     \
   cmd_info[(number)].minimum_position = (min_pos);    \
   cmd_info[(number)].in_battle = FALSE;        \
   cmd_info[(number)].minimum_level = (min_level);      \
   cmd_info[(number)].grantable = 0;    \
   cmd_info[(number)].req_confirm = TRUE;}

#define CMD_TRIG(number, min_level) {   \
   cmd_info[(number)].command_pointer = do_not_here;     \
   cmd_info[(number)].minimum_position = STAT_DEAD + POS_PRONE;    \
   cmd_info[(number)].in_battle = TRUE; \
   cmd_info[(number)].minimum_level = (min_level);      \
   cmd_info[(number)].grantable = 0;    \
   cmd_info[(number)].req_confirm = FALSE;}

#define CMD_SOC(number, min_position) {   \
   cmd_info[(number)].command_pointer = do_action;     \
   cmd_info[(number)].minimum_position = (min_position);    \
   cmd_info[(number)].in_battle = TRUE; \
   cmd_info[(number)].minimum_level = 0;      \
   cmd_info[(number)].grantable = 0;    \
   cmd_info[(number)].req_confirm = FALSE;}

#define CMD_GRT(number, min_pos, pointer, min_level) {   \
   cmd_info[(number)].command_pointer = (pointer);     \
   cmd_info[(number)].minimum_position = (min_pos);    \
   cmd_info[(number)].in_battle = TRUE; \
   cmd_info[(number)].minimum_level = (min_level);      \
   cmd_info[(number)].grantable = -1;   \
   cmd_info[(number)].req_confirm = FALSE;}

void assign_command_pointers(void)
{
   int position;

   for(position = 0; position < MAX_CMD_LIST; position++)
      cmd_info[position].command_pointer = NULL;

   /* temporarily disabled (or not imped) commands */
#if 0
   CMD_N (CMD_SHAPECHANGE, STAT_RESTING + POS_SITTING, do_shapechange, 0);
   CMD_N (CMD_TRACK, STAT_NORMAL + POS_STANDING, do_track, 61);
   CMD_N (CMD_TRAP, STAT_NORMAL + POS_STANDING, do_trap, 0);
   CMD_Y (CMD_LAG, STAT_DEAD + POS_PRONE, do_lag, 54);
   CMD_Y (CMD_RELOAD, STAT_DEAD + POS_PRONE, do_text_reload, 57);
#endif


   /* wizcommands */

   CMD_GRT (CMD_QWIZ, STAT_DEAD + POS_PRONE, do_qwiz, 51);
   CMD_GRT (CMD_PREP, STAT_DEAD + POS_PRONE, do_prep, 57);
   CMD_GRT (CMD_FINDQUEST, STAT_DEAD + POS_PRONE, do_findquest, 57);
   CMD_GRT (CMD_ADDPRESTIGE, STAT_DEAD + POS_PRONE, do_addprestige, 52);  // -> 57 - 52
   CMD_GRT (CMD_ALLOWGROUP, STAT_DEAD + POS_PRONE, do_allowgroup, 57);   // 57 -> 54
   CMD_GRT (CMD_IPSHARE, STAT_DEAD + POS_PRONE, do_ipshare, 51);
   CMD_GRT (CMD_RELOAD, STAT_DEAD + POS_PRONE, do_text_reload, 56);     // 57 - > 56
   CMD_GRT (CMD_PROC, STAT_DEAD + POS_PRONE, do_proc, 55);
   CMD_GRT (CMD_FUNCTION, STAT_DEAD + POS_PRONE, do_function, 54);      // 55 - 54
   CMD_GRT (CMD_SETHELPER, STAT_DEAD + POS_PRONE, do_sethelper, 51);
   CMD_GRT (CMD_ACCEPT, STAT_DEAD + POS_PRONE, do_accept, 51);
   CMD_GRT (CMD_ADVANCE, STAT_DEAD + POS_PRONE, do_advance, 58);       // 59 -> 58
   CMD_GRT (CMD_ALLOW, STAT_DEAD + POS_PRONE, do_allow, 56);          // 59 - 56
   CMD_GRT (CMD_AT, STAT_DEAD + POS_PRONE, do_at, 51);
   CMD_GRT (CMD_BAN, STAT_DEAD + POS_PRONE, do_ban, 56);
   CMD_GRT (CMD_CD, STAT_DEAD + POS_PRONE, do_not_here, 57);         // 56 -> 57
   CMD_GRT (CMD_CHANGELOG, STAT_DEAD + POS_PRONE, do_changelog, 51);
   CMD_GRT (CMD_CLONE, STAT_DEAD + POS_PRONE, do_clone, 52);        // 54 - > 52
   CMD_GRT (CMD_CCONTROL, STAT_DEAD + POS_PRONE, do_code, 55);      // 55 -> 57
   CMD_GRT (CMD_DEBUG, STAT_DEAD + POS_PRONE, do_debug, 51);
   CMD_GRT (CMD_DECLINE, STAT_DEAD + POS_PRONE, do_decline, 51);
   CMD_GRT (CMD_DEMOTE, STAT_DEAD + POS_PRONE, do_demote, 58);     // 59 -> 58
   CMD_GRT (CMD_ECHO, STAT_DEAD + POS_PRONE, do_echo, 51);
   CMD_GRT (CMD_ECHOA, STAT_DEAD + POS_PRONE, do_echoa, 54);
   CMD_GRT (CMD_ECHOZ, STAT_DEAD + POS_PRONE, do_echoz, 52);
   CMD_GRT (CMD_FINGER, STAT_DEAD + POS_PRONE, do_finger, 52);    // 54 -> 52
   CMD_GRT (CMD_FORCE, STAT_DEAD + POS_PRONE, do_force, 52);      // 55 -> 52
   CMD_GRT (CMD_FREEZE, STAT_DEAD + POS_PRONE, do_freeze, 54);    // 54 -> 52
   CMD_GRT (CMD_GENLOG, STAT_DEAD + POS_PRONE, do_genlog, 51);    // 54 -> 51
   CMD_GRT (CMD_GOTO, STAT_DEAD + POS_PRONE, do_goto, 51);
   CMD_GRT (CMD_GSHOUT, STAT_DEAD + POS_PRONE, do_shout, 51);
   CMD_GRT (CMD_GTELL, STAT_DEAD + POS_PRONE, do_gtell, 51);
#ifdef KINGDOM
   CMD_GRT (CMD_HCONTROL, STAT_DEAD + POS_PRONE, do_hcontrol, 55);
#endif
   CMD_GRT (CMD_INROOM, STAT_DEAD + POS_PRONE, do_inroom, 51);
   CMD_GRT (CMD_KNOCK, STAT_DEAD + POS_PRONE, do_knock, 51);
   CMD_GRT (CMD_LEVELS, STAT_DEAD + POS_PRONE, do_levels, 51);
   CMD_GRT (CMD_LOADCHAR, STAT_DEAD + POS_PRONE, do_loadchar, 57);  // 58 -> 57
   CMD_GRT (CMD_LOADMOB, STAT_DEAD + POS_PRONE, do_loadmob, 52);    // 54 -> 52
   CMD_GRT (CMD_LFLAGS, STAT_DEAD + POS_PRONE, do_law_flags, 52);   // 53 -> 52
   CMD_GRT (CMD_LIGHTNING, STAT_DEAD + POS_PRONE, do_action, 51);
   CMD_GRT (CMD_LOAD, STAT_DEAD + POS_PRONE, do_load, 52);          // 54 - 52
   CMD_GRT (CMD_LOOKUP, STAT_DEAD + POS_PRONE, do_lookup, 52);
   CMD_GRT (CMD_LWITNESS, STAT_DEAD + POS_PRONE, do_list_witness, 52);
   CMD_GRT (CMD_OUTCAST, STAT_DEAD + POS_PRONE, do_outcast, 51);
   CMD_GRT (CMD_POOFIN, STAT_DEAD + POS_PRONE, do_poofIn, 51);
   CMD_GRT (CMD_POOFOUT, STAT_DEAD + POS_PRONE, do_poofOut, 51);
   CMD_GRT (CMD_PTELL, STAT_DEAD + POS_PRONE, do_ptell, 51);
   CMD_GRT (CMD_PURGE, STAT_DEAD + POS_PRONE, do_purge, 52);
   CMD_GRT (CMD_REINITPHYS, STAT_DEAD + POS_PRONE, do_reinitphys, 51);
   CMD_GRT (CMD_RELEASE, STAT_DEAD + POS_PRONE, do_release, 51);
   CMD_GRT (CMD_RENAME, STAT_DEAD + POS_PRONE, do_rename, 51);
   CMD_GRT (CMD_REROLL, STAT_DEAD + POS_PRONE, do_reroll, 56);
   CMD_GRT (CMD_RESTORE, STAT_DEAD + POS_PRONE, do_restore, 52);    // 54 -> 52
   CMD_GRT (CMD_SACRIFICE, STAT_DEAD + POS_PRONE, do_not_here, 57); // 60 -> 57
   CMD_GRT (CMD_SECRET, STAT_DEAD + POS_PRONE, do_secret, 52);
   CMD_GRT (CMD_SETBIT, STAT_DEAD + POS_PRONE, do_setbit, 55);
   CMD_GRT (CMD_SHUTDOW, STAT_DEAD + POS_PRONE, do_shutdow, 57);
   CMD_GRT (CMD_SHUTDOWN, STAT_DEAD + POS_PRONE, do_shutdown, 57);
   CMD_GRT (CMD_SILENCE, STAT_DEAD + POS_PRONE, do_silence, 52);
   CMD_GRT (CMD_SNOOP, STAT_DEAD + POS_PRONE, do_snoop, 52);
   CMD_GRT (CMD_SNOWBALL, STAT_DEAD + POS_PRONE, do_action, 51);
   CMD_GRT (CMD_STAT, STAT_DEAD + POS_PRONE, do_stat, 51);
   CMD_GRT (CMD_STAT_NOPAGE, STAT_DEAD + POS_PRONE, do_stat, 51);
   CMD_GRT (CMD_STRING, STAT_DEAD + POS_PRONE, do_string, 52);      // 54 -> 52
   CMD_GRT (CMD_SWITCH, STAT_DEAD + POS_PRONE, do_switch, 51);      // 54 -> 51
   CMD_GRT (CMD_ACONTROL, STAT_DEAD + POS_PRONE, do_acontrol, 52);  // 53 -> 52
   CMD_GRT (CMD_JCONTROL, STAT_DEAD + POS_PRONE, do_jcontrol, 53);
   CMD_GRT (CMD_TELEPORT, STAT_DEAD + POS_PRONE, do_teleport, 51);
   CMD_GRT (CMD_TERMINATE, STAT_DEAD + POS_PRONE, do_not_here, 57);
   CMD_GRT (CMD_TITLE, STAT_DEAD + POS_PRONE, do_title, 51);
   CMD_GRT (CMD_TRANSFER, STAT_DEAD + POS_PRONE, do_trans, 52);    // 54 -> 52
   CMD_GRT (CMD_USERS, STAT_DEAD + POS_PRONE, do_users, 51);
   CMD_GRT (CMD_WHERE, STAT_DEAD + POS_PRONE, do_where, 51);
   CMD_GRT (CMD_WHICH, STAT_DEAD + POS_PRONE, do_which, 51);
   CMD_GRT (CMD_EQRATE, STAT_DEAD + POS_PRONE, do_eqrate, 51);
   CMD_GRT (CMD_WIZLOCK, STAT_DEAD + POS_PRONE, do_wizlock, 54);   // 57 -> 54
   CMD_GRT (CMD_ZRESET, STAT_DEAD + POS_PRONE, do_zreset, 54);     // 57 -> 54
   CMD_GRT (CMD_SETHOME, STAT_DEAD + POS_PRONE, do_sethome, 52);
   CMD_GRT (CMD_CP, STAT_DEAD + POS_PRONE, do_control_panel, 59);
   CMD_GRT (CMD_UNDECLINE, STAT_DEAD + POS_PRONE, do_undecline, 52);  // 56 -> 52
   CMD_GRT (CMD_DENY, STAT_DEAD + POS_PRONE, do_deny, 53);
   CMD_GRT (CMD_ECHOT, STAT_DEAD + POS_PRONE, do_echot, 51);
   CMD_GRT (CMD_REAGGRO, STAT_DEAD + POS_PRONE, do_reaggro, 57);
   CMD_GRT (CMD_DEAGGRO, STAT_DEAD + POS_PRONE, do_deaggro, 57);
   CMD_GRT (CMD_CREATEMOB, STAT_DEAD + POS_PRONE, do_createmob, 52);
   CMD_GRT (CMD_RPTOGGLE, STAT_DEAD + POS_PRONE, do_rptoggle, 54);
   CMD_GRT (CMD_SETPK, STAT_DEAD + POS_PRONE, do_setpk, 55);
   CMD_Y (CMD_WIZHELP, STAT_DEAD + POS_PRONE, do_wizhelp, 51);
   CMD_Y (CMD_WIZMSG, STAT_DEAD + POS_PRONE, do_wizmsg, 51);

   /* commands requiring confirmation */

   CMD_CNF_N (CMD_JUNK, STAT_RESTING + POS_SITTING, do_junk, 51);
   CMD_CNF_N (CMD_QUIT, STAT_DEAD + POS_PRONE, do_quit, 0);
   CMD_CNF_Y (CMD_BERSERK, STAT_NORMAL + POS_STANDING, do_berserk, 1);

   /* level restricted commands */
   CMD_N (CMD_MOBGEN, STAT_NORMAL + POS_PRONE, do_mobgen, 59);
   CMD_N (CMD_BURY, STAT_NORMAL + POS_STANDING, do_bury, 51);
#ifdef ALPHA_MODE
   CMD_Y (CMD_OOC, STAT_SLEEPING + POS_PRONE, do_ooc, 1);
#else
   CMD_Y (CMD_OOC, STAT_SLEEPING + POS_PRONE, do_ooc, 3);
#endif

   CMD_N (CMD_SUMMON, STAT_NORMAL + POS_STANDING, do_summon_mount, 14);
   CMD_Y (CMD_GRANT, STAT_DEAD + POS_PRONE, do_grant, 59);
   CMD_Y (CMD_PAGE, STAT_NORMAL + POS_STANDING, do_page, 59);
   //CMD_Y (CMD_PREP, STAT_DEAD + POS_PRONE, do_prep, 57);
   CMD_Y (CMD_REVOKE, STAT_DEAD + POS_PRONE, do_revoke, 59);
   CMD_N (CMD_SKILLS, STAT_RESTING + POS_PRONE, do_skills, 0);
   CMD_N (CMD_SPELLS, STAT_RESTING + POS_PRONE, do_spells, 0);
   CMD_Y (CMD_TELL, STAT_RESTING + POS_PRONE, do_tell, 1);
#ifdef REPLY
   CMD_Y (CMD_REPLY, STAT_RESTING + POS_PRONE, do_reply, 1);
#else
   CMD_Y (CMD_REPLY, STAT_RESTING + POS_PRONE, do_command_stub, 1);
#endif
   CMD_N (CMD_HORDE, STAT_NORMAL + POS_STANDING, do_summon_horde, 0);
   CMD_Y (CMD_WIZNEWS, STAT_DEAD + POS_PRONE, do_news, 51);
   CMD_Y (CMD_CSTAT, STAT_RESTING + POS_SITTING, do_cstat, 51);
   //  CMD_Y (CMD_CP, STAT_RESTING + POS_SITTING, do_control_panel, 56);
#ifdef MEM_DEBUG
   CMD_Y (CMD_MREPORT, STAT_DEAD + POS_PRONE, do_mreport, 51);
#endif

   /* normal commands (not allowed while fighting) */
   CMD_N (CMD_ACHEXIT, STAT_NORMAL + POS_STANDING, do_acheron_exit, 0);
   CMD_N (CMD_APPLY, STAT_NORMAL + POS_STANDING, do_apply, 0);
   CMD_N (CMD_ASSASSINATE, STAT_NORMAL + POS_STANDING, do_assassinate, 0);
   CMD_N (CMD_ASSIST, STAT_NORMAL + POS_STANDING, do_assist, 0);
#ifndef NEW_AWARE
   CMD_N (CMD_AWARENESS, STAT_NORMAL + POS_STANDING, do_awareness, 0);
#endif
   CMD_N (CMD_BACKSTAB, STAT_NORMAL + POS_STANDING, do_backstab, 0);
   CMD_N (CMD_BALANCE, STAT_NORMAL + POS_STANDING, do_balance, 0);
   CMD_N (CMD_BANDAGE, STAT_NORMAL + POS_STANDING, do_bandage, 0);
   CMD_N (CMD_BODYSLAM, STAT_NORMAL + POS_STANDING, do_bodyslam, 0);
   CMD_N (CMD_CAMP, STAT_RESTING + POS_PRONE, do_camp, 0);
   CMD_N (CMD_CLOSE, STAT_RESTING + POS_SITTING, do_close, 0);
   CMD_N (CMD_CHARGE, STAT_RESTING + POS_PRONE, do_charge, 0);
   CMD_N (CMD_COMMANDS, STAT_SLEEPING + POS_PRONE, do_commands, 0);
#ifdef KINGDOM
   CMD_N (CMD_CONSTRUCT, STAT_NORMAL + POS_STANDING, do_construct, 25);
#endif
   CMD_N (CMD_CREDITS, STAT_DEAD + POS_PRONE, do_credits, 0);
   //  CMD_N (CMD_DEPART, STAT_NORMAL + POS_STANDING, do_quest_exit, 0);
   CMD_N (CMD_DEPOSIT, STAT_NORMAL + POS_STANDING, do_deposit, 0);
   CMD_N (CMD_DETECTTRAPS, STAT_RESTING + POS_PRONE, do_detect_traps, 0);
   CMD_N (CMD_DICE, STAT_RESTING + POS_PRONE, do_dice, 0);
   CMD_N (CMD_DUEL, STAT_NORMAL + POS_STANDING, do_duel, 0);
   CMD_N (CMD_DISARMTRAPS, STAT_RESTING + POS_PRONE, do_remove_traps, 0);
   CMD_N (CMD_DISGUISE, STAT_RESTING + POS_PRONE, do_disguise, 0);
   // This do command won't work because 'down' will take precedence. - Urdlen
   CMD_N (CMD_DO, STAT_RESTING + POS_PRONE, do_do, 0);
   CMD_N (CMD_DONATE, STAT_NORMAL + POS_STANDING, do_donate, 0);
   CMD_N (CMD_DOORBASH, STAT_NORMAL + POS_STANDING, do_doorbash, 0);
   CMD_N (CMD_DOWN, STAT_NORMAL + POS_PRONE, do_move, 0);
   CMD_N (CMD_DRAIN, STAT_NORMAL + POS_STANDING, do_drain, 0);
   CMD_N (CMD_DRAG, STAT_NORMAL + POS_STANDING, do_drag, 0);
   CMD_N (CMD_DRINK, STAT_RESTING + POS_SITTING, do_drink, 0);
   CMD_N (CMD_EAST, STAT_NORMAL + POS_PRONE, do_move, 0);
   CMD_N (CMD_EAT, STAT_RESTING + POS_PRONE, do_eat, 0);
   CMD_N (CMD_ENTER, STAT_NORMAL + POS_STANDING, do_enter, 0);
   CMD_N (CMD_EXAMINE, STAT_RESTING + POS_PRONE, do_examine, 0);
   CMD_N (CMD_EXP, STAT_NORMAL + POS_STANDING, do_practice, 0);
   CMD_N (CMD_FAQ, STAT_SLEEPING + POS_PRONE, do_faq, 0);
   CMD_N (CMD_FILL, STAT_NORMAL + POS_STANDING, do_fill, 0);
   CMD_N (CMD_FORAGE, STAT_NORMAL + POS_STANDING, do_forage, 0);
   CMD_N (CMD_SELFPRESERVE, STAT_NORMAL + POS_STANDING, do_self_preservation, 0);
   CMD_N (CMD_HELP, STAT_DEAD + POS_PRONE, do_help, 0);
   CMD_N (CMD_HIDE, STAT_RESTING + POS_SITTING, do_hide, 0);
#ifdef KINGDOM
   CMD_N (CMD_HOUSE, STAT_NORMAL + POS_PRONE, do_house, 0);
#endif
   CMD_N (CMD_INFO, STAT_SLEEPING + POS_PRONE, do_info, 0);
#ifdef NEWJUSTICE
   CMD_N (CMD_JUSTICE, STAT_SLEEPING + POS_PRONE, do_justice, 0);
#endif
   CMD_N (CMD_LISTEN, STAT_NORMAL + POS_STANDING, do_listen, 0);
   CMD_N (CMD_LOCK, STAT_RESTING + POS_SITTING, do_lock, 0);
   CMD_N (CMD_MEDITATE, STAT_RESTING + POS_KNEELING, do_meditate, 0);
   CMD_N (CMD_MIX, STAT_NORMAL + POS_SITTING, do_mix, 0);
   CMD_N (CMD_MOTD, STAT_SLEEPING + POS_PRONE, do_motd, 0);
   CMD_N (CMD_MOUNT, STAT_NORMAL + POS_STANDING, do_mount, 0);
   CMD_N (CMD_MREAD, STAT_SLEEPING + POS_PRONE, do_mread, 0);
   CMD_N (CMD_MWRITE, STAT_SLEEPING + POS_PRONE, do_mwrite, 0);
   CMD_N (CMD_MLIST, STAT_SLEEPING + POS_PRONE, do_mlist, 0);
   CMD_N (CMD_MDELETE, STAT_SLEEPING + POS_PRONE, do_mdelete, 0);
   CMD_N (CMD_MREPLY, STAT_SLEEPING + POS_PRONE, do_mreply, 0);
   CMD_N (CMD_NEWS, STAT_SLEEPING + POS_PRONE, do_news, 0);
   CMD_N (CMD_NORTH, STAT_NORMAL + POS_PRONE, do_move, 0);
   CMD_Y (CMD_OPEN, STAT_RESTING + POS_SITTING, do_open, 0);
   CMD_N (CMD_PICK, STAT_NORMAL + POS_STANDING, do_pick, 0);
   CMD_Y (CMD_PLAY, STAT_RESTING + POS_SITTING, do_play, 0);
#ifndef NEW_BARD
   CMD_N (CMD_VIRTUOSO, STAT_RESTING + POS_SITTING, do_virtuoso, 26);
#endif
   CMD_N (CMD_PRACTICE, STAT_RESTING + POS_KNEELING, do_practice, 0);
   CMD_N (CMD_PRACTISE, STAT_RESTING + POS_KNEELING, do_practice, 0);
   CMD_N (CMD_PUT, STAT_RESTING + POS_SITTING, do_put, 0);
   CMD_N (CMD_QUI, STAT_DEAD + POS_PRONE, do_qui, 0);
   CMD_N (CMD_READ, STAT_RESTING + POS_PRONE, do_read, 0);
   CMD_N (CMD_RECITE, STAT_RESTING + POS_PRONE, do_recite, 0);
   CMD_N (CMD_REORDER, STAT_RESTING + POS_SITTING, do_reorder, 0);
   CMD_N (CMD_REST, STAT_RESTING + POS_PRONE, do_rest, 0);
   CMD_N (CMD_RIDE, STAT_NORMAL + POS_STANDING, do_mount, 0);
   CMD_N (CMD_RULES, STAT_RESTING + POS_PRONE, do_rules, 0);
   CMD_N (CMD_SCRIBE, STAT_RESTING + POS_SITTING, do_scribe, 0);
   CMD_N (CMD_SEARCH, STAT_NORMAL + POS_STANDING, do_search, 0);
   CMD_N (CMD_SHADOW, STAT_NORMAL + POS_STANDING, do_shadow, 0);
   CMD_N (CMD_SHAPECHANGE, STAT_NORMAL + POS_STANDING, do_shapechange, 0);
   CMD_N (CMD_SIP, STAT_RESTING + POS_SITTING, do_sip, 0);
   CMD_N (CMD_SLEEP, STAT_SLEEPING + POS_PRONE, do_sleep, 0);
   CMD_N (CMD_SNEAK, STAT_NORMAL + POS_STANDING, do_sneak, 0);
   CMD_N (CMD_SOUTH, STAT_NORMAL + POS_PRONE, do_move, 0);
   CMD_N (CMD_ASSOC, STAT_RESTING + POS_PRONE, do_assoc, 0);
   CMD_N (CMD_CONTRACT, STAT_RESTING + POS_PRONE, do_contract, 0);
   CMD_N (CMD_SPLIT, STAT_RESTING + POS_SITTING, do_split, 0);
   CMD_N (CMD_STEAL, STAT_NORMAL + POS_STANDING, do_steal, 0);
   CMD_N (CMD_TAILSWEEP, STAT_NORMAL + POS_STANDING, do_tailsweep, 0);
   CMD_N (CMD_TASTE, STAT_RESTING + POS_PRONE, do_taste, 0);
   CMD_N (CMD_TEACH, STAT_RESTING + POS_SITTING, do_teach, 0);
   CMD_N (CMD_TIE_UP, STAT_NORMAL + POS_STANDING, do_tie_up, 0);
   CMD_N (CMD_TRACK, STAT_NORMAL + POS_STANDING, do_track, 0);
   CMD_N (CMD_UNBIND, STAT_RESTING + POS_PRONE, do_unbind, 0);
   CMD_N (CMD_UNTIE, STAT_NORMAL + POS_STANDING, do_untie, 0);
   CMD_N (CMD_UNLOCK, STAT_RESTING + POS_SITTING, do_unlock, 0);
   CMD_N (CMD_UP, STAT_NORMAL + POS_PRONE, do_move, 0);
   CMD_N (CMD_WEAR, STAT_RESTING + POS_SITTING, do_wear, 0);
   CMD_N (CMD_WEST, STAT_NORMAL + POS_PRONE, do_move, 0);
   CMD_N (CMD_WHISPER, STAT_RESTING + POS_SITTING, do_whisper, 0);
   CMD_N (CMD_WHO, STAT_DEAD + POS_PRONE, do_who, 0);
   CMD_N (CMD_WITHDRAW, STAT_NORMAL + POS_STANDING, do_withdraw, 0);
   CMD_N (CMD_WIZLIST, STAT_DEAD + POS_PRONE, do_wizlist, 0);
   CMD_N (CMD_WRITE, STAT_NORMAL + POS_STANDING, do_write, 0);
   CMD_N (CMD_CLAIM, STAT_NORMAL + POS_STANDING, do_claim, 0);
   CMD_N (CMD_GETCART, STAT_NORMAL + POS_STANDING, do_getcart, 0);
   CMD_N (CMD_LEAVECART, STAT_NORMAL + POS_STANDING, do_leavecart, 0);
   CMD_N (CMD_OLC, STAT_NORMAL + POS_STANDING, do_olc, 0);
   CMD_N (CMD_OLR, STAT_NORMAL + POS_STANDING, olc_room_expert_mode, 0);
   CMD_N (CMD_OLCDB, STAT_NORMAL + POS_STANDING, Manage_OLC_Database, 0);
   CMD_N (CMD_LORE, STAT_NORMAL + POS_SITTING, do_lore, 0);
   CMD_N (CMD_CLIMB, STAT_NORMAL + POS_SITTING, do_climb, 0);
   CMD_N (CMD_EMPTY, STAT_RESTING + POS_SITTING, do_empty, 0);
   CMD_N (CMD_HAMSTRING, STAT_NORMAL + POS_STANDING, do_hamstring, 0);

   /* normal commands (allowed while fighting) */
#ifdef NEW_BARD
   CMD_Y (CMD_ACCOMPANY, STAT_RESTING + POS_SITTING, do_accompany, 0);
#endif
   CMD_Y (CMD_WARCHANT, STAT_RESTING + POS_SITTING, do_warchant, 0);
   CMD_Y (CMD_EMIT, STAT_RESTING + POS_PRONE, do_command_stub, 0);
   CMD_Y (CMD_ACC, STAT_RESTING + POS_PRONE, do_acc, 0);
   //CMD_Y (CMD_QWIZ, STAT_RESTING + POS_PRONE, do_qwiz, 51);
   CMD_Y (CMD_ARENA, STAT_RESTING + POS_PRONE, do_acheron_channel, 0);
   CMD_Y (CMD_MINDBLAST, STAT_RESTING + POS_SITTING, do_mindblast, 0);
   CMD_Y (CMD_ADRENALIZE, STAT_RESTING + POS_SITTING, do_adrenalize, 0);
   CMD_Y (CMD_ENHANCE, STAT_RESTING + POS_SITTING, do_adrenalize, 0);
   CMD_Y (CMD_SUSTAIN, STAT_RESTING + POS_SITTING, do_sustain, 0);
   CMD_Y (CMD_COMBATMIND, STAT_RESTING + POS_SITTING, do_combatmind, 0);
   CMD_Y (CMD_AMPLIFY, STAT_RESTING + POS_SITTING, do_amplify, 0);
   CMD_Y (CMD_AURASIGHT, STAT_RESTING + POS_SITTING, do_aurasight, 0);
   CMD_Y (CMD_EQUALIBRIUM, STAT_RESTING + POS_SITTING, do_equalibrium, 0);
   /*  CMD_Y(CMD_TELEKINATE, STAT_RESTING + POS_SITTING, do_telekinate, 0); */
   CMD_Y (CMD_TRANCE, STAT_RESTING + POS_SITTING, do_battle_trance, 0);
   CMD_Y (CMD_DOMINATE, STAT_RESTING + POS_SITTING, do_dominate, 0);
   CMD_Y (CMD_RIFT, STAT_RESTING + POS_SITTING, do_rift, 0);
   CMD_Y (CMD_ULTRABLAST, STAT_RESTING + POS_SITTING, do_ultrablast, 0);
   CMD_Y (CMD_DANGERSENSE, STAT_RESTING + POS_SITTING, do_sense_danger, 0);
   CMD_Y (CMD_PROJECT_FORCE, STAT_RESTING + POS_SITTING, do_project_force, 0);
   CMD_Y (CMD_DETONATE, STAT_RESTING + POS_SITTING, do_detonate, 0);
   CMD_Y (CMD_DEATHFIELD, STAT_RESTING + POS_SITTING, do_death_field, 0);
   CMD_Y (CMD_BODYCONTROL, STAT_RESTING + POS_SITTING, do_body_control, 0);
   CMD_Y (CMD_CATFALL, STAT_RESTING + POS_SITTING, do_catfall, 0);
   CMD_Y (CMD_FLESHARMOR, STAT_RESTING + POS_SITTING, do_flesharmor, 0);
   CMD_Y (CMD_REDUCE, STAT_RESTING + POS_SITTING, do_reduction, 0);
   CMD_Y (CMD_EXPAND, STAT_RESTING + POS_SITTING, do_expansion, 0);
   CMD_Y (CMD_SHIFT, STAT_RESTING + POS_SITTING, do_shift, 0);
   CMD_Y (CMD_MASS_DOMINATE, STAT_RESTING + POS_SITTING, do_mass_domination, 0);
   CMD_Y (CMD_TOWER, STAT_RESTING + POS_SITTING, do_tower_of_iron_will, 0);
   CMD_Y (CMD_ATTRACTION, STAT_RESTING + POS_SITTING, do_attraction, 0);
   CMD_Y (CMD_SYNAPTIC_STATIC, STAT_RESTING + POS_SITTING, do_synaptic_static, 0);
   /* CMD_Y (CMD_ALTER_AURA, STAT_RESTING + POS_SITTING, do_alter_aura, 0); */
   CMD_Y (CMD_CANIBALIZE, STAT_RESTING + POS_SITTING, do_canibalize, 0);
   CMD_Y (CMD_SKILL_ENHANCE, STAT_RESTING + POS_SITTING, do_enhance_skill, 0);
   CMD_Y (CMD_STASIS_FIELD, STAT_RESTING + POS_SITTING, do_stasis_field, 0);
   CMD_Y (CMD_GLOBE, STAT_RESTING + POS_SITTING, do_metaglobe, 0);
   CMD_Y (CMD_DARKNESS, STAT_RESTING + POS_SITTING, do_globe_of_darkness, 0);
   CMD_Y (CMD_COLLECT, STAT_RESTING + POS_SITTING, Collect_Ammo, 0);
   CMD_Y (CMD_CHILLTOUCH, STAT_NORMAL + POS_STANDING, do_lich_chill_touch, 0);
   CMD_Y (CMD_SNAKEBITE, STAT_NORMAL + POS_STANDING, do_snakebite, 0);
   CMD_Y (CMD_BEFRIENDREPTILE, STAT_RESTING + POS_SITTING, do_charm_reptile, 0);
   CMD_Y (CMD_ABORT, STAT_DYING + POS_PRONE, do_abort, 0);
   CMD_Y (CMD_AGGR, STAT_RESTING + POS_PRONE, do_aggr, 0);
   CMD_Y (CMD_ALERT, STAT_RESTING + POS_PRONE, do_alert, 0);
   CMD_Y (CMD_ASK, STAT_RESTING + POS_SITTING, do_ask, 0);
   CMD_Y (CMD_ATTRIBUTES, STAT_SLEEPING + POS_PRONE, do_attributes, 0);
   CMD_Y (CMD_BASH, STAT_NORMAL + POS_STANDING, do_bash, 0);
   CMD_Y (CMD_BUG, STAT_DEAD + POS_PRONE, do_bug, 0);
   CMD_Y (CMD_CAST, STAT_RESTING + POS_SITTING, do_cast, 0);
   CMD_Y (CMD_CHANT, STAT_NORMAL + POS_STANDING, do_chant, 0);
   CMD_Y (CMD_CIRCLE, STAT_NORMAL + POS_STANDING, do_circle, 0);
   CMD_Y (CMD_CONSENT, STAT_DEAD + POS_PRONE, do_consent, 0);
   CMD_Y (CMD_CONSIDER, STAT_RESTING + POS_SITTING, do_consider, 0);
   CMD_Y (CMD_DISARM, STAT_NORMAL + POS_STANDING, do_disarm, 0);
   CMD_Y (CMD_DISBAND, STAT_SLEEPING + POS_PRONE, do_disband, 0);
   CMD_Y (CMD_DISENGAGE, STAT_NORMAL + POS_STANDING, do_disengage, 0);
   CMD_Y (CMD_DISMOUNT, STAT_NORMAL + POS_STANDING, do_dismount, 0);
   CMD_Y (CMD_DISPLAY, STAT_DEAD + POS_PRONE, do_display, 0);
   CMD_Y (CMD_DRAGONPUNCH, STAT_NORMAL + POS_STANDING, do_dragon_punch, 0);
   CMD_Y (CMD_DROP, STAT_RESTING + POS_PRONE, do_drop, 0);
   CMD_Y (CMD_EMOTE, STAT_RESTING + POS_PRONE, do_emote, 0);
   CMD_Y (CMD_EMOTE2, STAT_RESTING + POS_PRONE, do_emote, 0);
   CMD_Y (CMD_EQUIPMENT, STAT_SLEEPING + POS_PRONE, do_equipment, 0);
   CMD_Y (CMD_ESCAPE, STAT_NORMAL + POS_PRONE, do_escape, 0);
   CMD_Y (CMD_EXITS, STAT_RESTING + POS_SITTING, do_exits, 0);
   CMD_Y (CMD_FEIGNDEATH, STAT_RESTING + POS_PRONE, do_feign_death, 0);
   CMD_Y (CMD_FLEE, STAT_NORMAL + POS_PRONE, do_flee, 0);
   CMD_Y (CMD_FOLLOW, STAT_RESTING + POS_SITTING, do_follow, 0);
   CMD_Y (CMD_FORGET, STAT_RESTING + POS_PRONE, do_forget, 0);
   CMD_Y (CMD_GADD, STAT_RESTING + POS_SITTING, do_gadd, 0);
   CMD_Y (CMD_GARROTE, STAT_NORMAL + POS_STANDING, do_garrote, 0);
   CMD_Y (CMD_GDEL, STAT_RESTING + POS_SITTING, do_gdel, 0);
   CMD_Y (CMD_GGET, STAT_RESTING + POS_SITTING, do_gget, 0);
   CMD_Y (CMD_GGIVE, STAT_RESTING + POS_SITTING, do_ggive, 0);
   CMD_Y (CMD_GET, STAT_RESTING + POS_SITTING, do_get, 0);
   CMD_Y (CMD_GIVE, STAT_RESTING + POS_SITTING, do_give, 0);
   CMD_Y (CMD_GLANCE, STAT_RESTING + POS_PRONE, do_glance, 0);
   CMD_Y (CMD_GRAB, STAT_RESTING + POS_PRONE, do_grab, 0);
   CMD_Y (CMD_GROUP, STAT_RESTING + POS_PRONE, do_group, 0);
   CMD_Y (CMD_GSAY, STAT_RESTING + POS_PRONE, do_gsay, 0);
   CMD_Y (CMD_GREPORT, STAT_RESTING + POS_PRONE, do_greport, 0);
   CMD_Y (CMD_STORAGE, STAT_RESTING + POS_PRONE, do_storage, 0);
   CMD_Y (CMD_APPOINT, STAT_RESTING + POS_PRONE, do_appoint, 0);
   CMD_Y (CMD_GTOGGLE, STAT_DEAD + POS_PRONE, do_gtoggle, 0);
   CMD_Y (CMD_HEADBUTT, STAT_NORMAL + POS_STANDING, do_headbutt, 0);
   CMD_Y (CMD_HIT, STAT_NORMAL + POS_STANDING, do_hit, 0);
   CMD_Y (CMD_FIRE, STAT_NORMAL + POS_STANDING, Initiate_Missile_Combat, 0);
   CMD_Y (CMD_THROW, STAT_NORMAL + POS_STANDING, Initiate_Missile_Combat, 0);
   CMD_Y (CMD_CEASEFIRE, STAT_NORMAL + POS_STANDING, Initiate_Missile_Combat, 0);
   CMD_Y (CMD_AFIRE, STAT_NORMAL + POS_STANDING, Initiate_Missile_Combat, 0);
   CMD_Y (CMD_ATHROW, STAT_NORMAL + POS_STANDING, Initiate_Missile_Combat, 0);
   CMD_Y (CMD_AMMO, STAT_NORMAL + POS_STANDING, Initiate_Missile_Combat, 0);
   CMD_Y (CMD_HITALL, STAT_NORMAL + POS_STANDING, do_hitall, 0);
   CMD_Y (CMD_HOLD, STAT_RESTING + POS_PRONE, do_grab, 0);
   CMD_Y (CMD_IDEA, STAT_DEAD + POS_PRONE, do_idea, 0);
   CMD_Y (CMD_IGNORE, STAT_SLEEPING + POS_PRONE, do_ignore, 0);
   CMD_Y (CMD_INNATE, STAT_NORMAL + POS_STANDING, do_innate, 0);
   CMD_Y (CMD_INSULT, STAT_RESTING + POS_PRONE, do_insult, 0);
   CMD_Y (CMD_INVENTORY, STAT_RESTING + POS_SITTING, do_inventory, 0);
   CMD_Y (CMD_KICK, STAT_NORMAL + POS_STANDING, do_kick, 0);
   CMD_Y (CMD_KILL, STAT_NORMAL + POS_STANDING, do_kill, 0);
   CMD_Y (CMD_KNEEL, STAT_RESTING + POS_PRONE, do_kneel, 0);
   CMD_Y (CMD_LAYHAND, STAT_NORMAL + POS_STANDING, do_layhand, 0);
   CMD_Y (CMD_LIFETAP, STAT_NORMAL + POS_STANDING, do_lifetap, 0);
   CMD_Y (CMD_LOOK, STAT_RESTING + POS_PRONE, do_look, 0);
   CMD_Y (CMD_LOOT, STAT_RESTING + POS_PRONE, do_loot, 0);
   CMD_Y (CMD_GLIST, STAT_RESTING + POS_SITTING, do_glist, 0);
   CMD_Y (CMD_GCOMBAT, STAT_RESTING + POS_SITTING, do_gcombat, 51);
   CMD_Y (CMD_GRESET, STAT_RESTING + POS_SITTING, do_greset, 51);
   CMD_Y (CMD_MASS_PROJECT, STAT_RESTING + POS_SITTING, do_mass_project, 0);
   CMD_Y (CMD_MEMORIZE, STAT_RESTING + POS_KNEELING, do_memorize, 0);
   CMD_Y (CMD_MURDER, STAT_NORMAL + POS_STANDING, do_murder, 0);
   CMD_Y (CMD_NUKE, STAT_NORMAL + POS_STANDING, do_command_stub, 0);
   CMD_Y (CMD_NCC, STAT_RESTING + POS_PRONE, do_ncc, 0);
   CMD_Y (CMD_NOKILL, STAT_SLEEPING + POS_PRONE, do_nokill, 0);
   CMD_Y (CMD_ORDER, STAT_RESTING + POS_PRONE, do_order, 0);
   CMD_Y (CMD_PETITION, STAT_DEAD + POS_PRONE, do_petition, 0);
   CMD_Y (CMD_POSE, STAT_RESTING + POS_SITTING, do_pose, 0);
   CMD_Y (CMD_POUR, STAT_RESTING + POS_SITTING, do_pour, 0);
   CMD_Y (CMD_PRAY, STAT_RESTING + POS_KNEELING, do_memorize, 0);
   CMD_Y (CMD_PRIORITIZE, STAT_RESTING + POS_SITTING, do_prioritize, 0);
   CMD_Y (CMD_PROJECT, STAT_RESTING + POS_PRONE, do_project, 0);
   CMD_Y (CMD_PROJECT_ASK, STAT_RESTING + POS_SITTING, do_project_ask, 0);
   CMD_Y (CMD_QUAFF, STAT_RESTING + POS_SITTING, do_quaff, 0);
   CMD_Y (CMD_RECALL, STAT_NORMAL + POS_STANDING, do_recall, 0);
   CMD_Y (CMD_RECLINE, STAT_RESTING + POS_PRONE, do_recline, 0);
   CMD_Y (CMD_REMOVE, STAT_RESTING + POS_SITTING, do_remove, 0);
   CMD_Y (CMD_REPORT, STAT_RESTING + POS_PRONE, do_report, 0);
   CMD_Y (CMD_RESCUE, STAT_NORMAL + POS_STANDING, do_rescue, 0);
   CMD_Y (CMD_RETREAT, STAT_NORMAL + POS_STANDING, do_retreat, 0);
   CMD_Y (CMD_RETURN, STAT_DEAD + POS_PRONE, do_return, 0);
   CMD_Y (CMD_RUB, STAT_RESTING + POS_SITTING, do_rub, 0);
   CMD_Y (CMD_SAVE, STAT_SLEEPING + POS_PRONE, do_save, 0);
   CMD_Y (CMD_SAY, STAT_RESTING + POS_PRONE, do_say, 0);
   CMD_Y (CMD_SAY2, STAT_RESTING + POS_PRONE, do_say, 0);
   CMD_Y (CMD_SCAN, STAT_RESTING + POS_SITTING, do_scan, 0);
   CMD_Y (CMD_SCORE, STAT_DEAD + POS_PRONE, do_score, 0);
   CMD_Y (CMD_SHIELDPUNCH, STAT_NORMAL + POS_STANDING, do_shieldpunch, 0);
   CMD_Y (CMD_MOUNTED_CHARGE, STAT_NORMAL + POS_STANDING, do_mountedCharge, 0);
   CMD_Y (CMD_HOWL, STAT_NORMAL + POS_STANDING, do_howl, 0);
   CMD_Y (CMD_STRAFE, STAT_NORMAL + POS_STANDING, do_strafe, 0);
   CMD_Y (CMD_OUTFLANK, STAT_NORMAL + POS_STANDING, do_outflank, 0);
   CMD_Y (CMD_SHOUT, STAT_RESTING + POS_SITTING, do_yell, 0);
   CMD_Y (CMD_SIGN, STAT_RESTING + POS_PRONE, do_sign, 0);
   CMD_Y (CMD_SING, STAT_RESTING + POS_PRONE, do_bardcheck_action, 0);
   CMD_Y (CMD_SIT, STAT_RESTING + POS_PRONE, do_sit, 0);
   CMD_Y (CMD_SPEAK, STAT_SLEEPING + POS_PRONE, do_speak, 0);
   CMD_Y (CMD_SPRINGLEAP, STAT_RESTING + POS_PRONE, do_springleap, 0);
   CMD_Y (CMD_STAND, STAT_RESTING + POS_PRONE, do_stand, 0);
   CMD_Y (CMD_STOPPCFOLLOW, STAT_RESTING + POS_PRONE, do_stop_pc_follow, 0);
   CMD_Y (CMD_STOPPETFOLLOW, STAT_RESTING + POS_PRONE, do_stop_pet_follow, 0);
   CMD_Y (CMD_TAKE, STAT_RESTING + POS_SITTING, do_get, 0);
   CMD_Y (CMD_TIME, STAT_DEAD + POS_PRONE, do_time, 0);
   CMD_Y (CMD_TOGGLE, STAT_DEAD + POS_PRONE, do_toggle, 0);
   CMD_Y (CMD_TRIP, STAT_NORMAL + POS_STANDING, do_trip, 0);
   CMD_Y (CMD_TROPHY, STAT_DEAD + POS_PRONE, do_trophy, 0);
   CMD_Y (CMD_TYPO, STAT_DEAD + POS_PRONE, do_typo, 0);
   CMD_Y (CMD_UNGROUP, STAT_RESTING + POS_PRONE, do_ungroup, 0);
   CMD_Y (CMD_USE, STAT_RESTING + POS_SITTING, do_use, 0);
   CMD_Y (CMD_VIS, STAT_DEAD + POS_PRONE, do_vis, 0);
   CMD_Y (CMD_WAKE, STAT_SLEEPING + POS_PRONE, do_wake, 0);
   CMD_Y (CMD_WEATHER, STAT_RESTING + POS_PRONE, do_weather, 0);
   CMD_Y (CMD_WIELD, STAT_RESTING + POS_PRONE, do_wield, 0);
   CMD_Y (CMD_WORLD, STAT_DEAD + POS_PRONE, do_world, 0);

   /* 'commands' which exist only to trigger specials */

   CMD_TRIG (CMD_BRIBE, 0);
   CMD_TRIG (CMD_BUY, 0);
   CMD_TRIG (CMD_CLEAR, 0);
   CMD_TRIG (CMD_CRIME_REPORT, 0);
   CMD_TRIG (CMD_DELETE, 0);
   CMD_TRIG (CMD_DISEMBARK, 0);
   CMD_TRIG (CMD_EXCHANGE, 0);
   CMD_TRIG (CMD_JOIN, 0);
   CMD_TRIG (CMD_LIST, 0);
   CMD_TRIG (CMD_MOVE, 0);
   CMD_TRIG (CMD_OFFER, 0);
   CMD_TRIG (CMD_PARDON, 0);
   CMD_TRIG (CMD_PAY, 0);
   CMD_TRIG (CMD_RENT, 0);
   CMD_TRIG (CMD_SELL, 0);
   CMD_TRIG (CMD_TURN_IN, 0);
   CMD_TRIG (CMD_VALUE, 0);
   CMD_TRIG (CMD_ZAP, 0);
   CMD_TRIG (CMD_STORE, 0);
   CMD_TRIG (CMD_STABLE, 0);
   CMD_TRIG (CMD_RETRIEVE, 0);
   CMD_TRIG (CMD_AUCTION, 0);
   CMD_TRIG (CMD_TITHE, 0);
   CMD_TRIG (CMD_EMBARK, 0);
   CMD_TRIG (CMD_HIRE, 0);
   CMD_TRIG (CMD_DELIVER, 0);
   CMD_TRIG (CMD_GAMBLE, 0);

   /* socials (all call do_action, rather than a specific func) */

   CMD_SOC (CMD_ACCUSE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_ACK, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_AFK, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_AGREE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_AMAZE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_APOLOGIZE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_APPLAUD, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_ARCH, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_AYT, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_BANG, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_BARK, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_BATHE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_BBL, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_BEER, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_BEG, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_BIRD, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_BITE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_BLEED, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_BLINK, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_BLOW, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_BLUSH, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_BOGGLE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_BONK, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_BORED, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_BOUNCE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_BOW, STAT_NORMAL + POS_STANDING);
   CMD_SOC (CMD_BRB, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_BURP, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_BYE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_CACKLE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_CALM, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_CARESS, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_CENSOR, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_CHEEK, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_CHEER, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_CHOKE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_CHUCKLE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_CLAP, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_COMB, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_COMFORT, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_CONGRATULATE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_COUGH, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_CRINGE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_CRY, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_CUDDLE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_CURIOUS, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_CURSE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_CURTSEY, STAT_NORMAL + POS_STANDING);
   CMD_SOC (CMD_DANCE, STAT_NORMAL + POS_STANDING);
   CMD_SOC (CMD_DREAM, STAT_SLEEPING + POS_PRONE);
   CMD_SOC (CMD_DROOL, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_DROPKICK, STAT_NORMAL + POS_STANDING);
   CMD_SOC (CMD_DUCK, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_DUH, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_EMBRACE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_ENVY, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_EYEBROW, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_FART, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_FIDGET, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_FLAME, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_FLASH, STAT_NORMAL + POS_STANDING);
   CMD_SOC (CMD_FLEX, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_FLIP, STAT_NORMAL + POS_STANDING);
   CMD_SOC (CMD_FLIRT, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_FLUTTER, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_FONDLE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_FOOL, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_FRENCH, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_FROWN, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_FULL, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_FUME, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_FUZZY, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_GAG, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_GAPE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_GASP, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_GIGGLE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_GLARE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_GOOSE, STAT_NORMAL + POS_STANDING);
   CMD_SOC (CMD_GRIN, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_GROAN, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_GROPE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_GROVEL, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_GROWL, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_GRUMBLE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_GRUNT, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_GULP, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_HALO, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_HAND, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_HAPPY, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_HELLO, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_HERO, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_HI5, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_HICCUP, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_HICKEY, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_HISS, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_HOLDON, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_HOP, STAT_NORMAL + POS_STANDING);
   CMD_SOC (CMD_HOSE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_HUG, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_HUM, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_HUNGER, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_IMITATE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_IMPALE, STAT_NORMAL + POS_STANDING);
   CMD_SOC (CMD_INTRODUCE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_JAM, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_JK, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_JUMP, STAT_NORMAL + POS_STANDING);
   CMD_SOC (CMD_KISS, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_LAG, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_LAUGH, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_LEAN, STAT_NORMAL + POS_STANDING);
   CMD_SOC (CMD_LICK, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_LOST, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_LOVE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_LUCKY, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_MASSAGE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_MELT, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_MOAN, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_MOON, STAT_NORMAL + POS_STANDING);
   CMD_SOC (CMD_MOSH, STAT_NORMAL + POS_STANDING);
   CMD_SOC (CMD_MOURN, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_MUTTER, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_NAP, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_NIBBLE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_NOD, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_NOG, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_NOOGIE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_NOSE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_NUDGE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_NUZZLE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_OGLE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_PANIC, STAT_NORMAL + POS_STANDING);
   CMD_SOC (CMD_PANT, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_PAT, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_PEER, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_PET, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_PILLOW, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_PINCH, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_PLONK, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_POINT, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_POKE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_PONDER, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_POUNCE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_POUT, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_PROTECT, STAT_NORMAL + POS_STANDING);
   CMD_SOC (CMD_PUCKER, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_PUKE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_PULL, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_PUNCH, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_PURR, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_PUSH, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_PUZZLE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_RAISE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_READY, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_ROAR, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_ROFL, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_ROLL, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_ROSE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_RUFFLE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_SALUTE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_SCARE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_SCOLD, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_SCRATCH, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_SCREAM, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_SEDUCE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_SHAKE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_SHIVER, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_SHOVE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_SHRUG, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_SHUDDER, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_SHUSH, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_SIGH, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_SKIP, STAT_NORMAL + POS_STANDING);
   CMD_SOC (CMD_SLAP, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_SLOBBER, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_SMELL, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_SMILE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_SMIRK, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_SMOKE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_SNAP, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_SNARL, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_SNEER, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_SNEEZE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_SNICKER, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_SNIFF, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_SNOOGIE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_SNORE, STAT_SLEEPING + POS_PRONE);
   CMD_SOC (CMD_SNORT, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_SNUGGLE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_SPAM, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_SPANK, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_SPIN, STAT_NORMAL + POS_STANDING);
   CMD_SOC (CMD_SPIT, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_SQUEEZE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_SQUIRM, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_STARE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_STEAM, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_STOMP, STAT_NORMAL + POS_STANDING);
   CMD_SOC (CMD_STRANGLE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_STRETCH, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_STRUT, STAT_NORMAL + POS_STANDING);
   CMD_SOC (CMD_SULK, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_SWAT, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_SWEAT, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_SWEEP, STAT_NORMAL + POS_STANDING);
   CMD_SOC (CMD_TACKLE, STAT_NORMAL + POS_STANDING);
   CMD_SOC (CMD_TANGO, STAT_NORMAL + POS_STANDING);
   CMD_SOC (CMD_TAP, STAT_NORMAL + POS_STANDING);
   CMD_SOC (CMD_TARZAN, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_TAUNT, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_TEASE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_THANK, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_THINK, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_THIRST, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_TICKLE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_TIP, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_TIPTOE, STAT_NORMAL + POS_STANDING);
   CMD_SOC (CMD_TONGUE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_TOSS, STAT_NORMAL + POS_STANDING);
   CMD_SOC (CMD_TOUCH, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_TUG, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_TWEAK, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_TWIBBLE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_TWIDDLE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_TWIRL, STAT_NORMAL + POS_STANDING);
   CMD_SOC (CMD_UNDRESS, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_VETO, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_WAIT, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_WAVE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_WELCOME, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_WET, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_WHAP, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_WHATEVER, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_WHEEZE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_WHIMPER, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_WHINE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_WHISTLE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_WIGGLE, STAT_NORMAL + POS_STANDING);
   CMD_SOC (CMD_WINCE, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_WINK, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_WOOPS, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_WORSHIP, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_YAWN, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_YODEL, STAT_RESTING + POS_PRONE);
   CMD_SOC (CMD_ZONE, STAT_RESTING + POS_PRONE);
}

/* only used in this one routine, so nuke them now. JAB */

#undef CMD_CNF_N
#undef CMD_CNF_Y
#undef CMD_GRT
#undef CMD_N
#undef CMD_SOC
#undef CMD_TRIG
#undef CMD_Y

void GRANTSET(int cmd, int admin_lvl, int areas_lvl, int coder_lvl, int www_lvl, int q_lvl)
{
   if(num_grant_cmds >= MAX_GRANT_CMDS)
      {
      /* plonk! increase GRANT_BYTES! */
      dump_core ();
      }
   if((cmd > 0) && (cmd_info[cmd].grantable != -1))
      {
      /* something hosed up, somewhere */
      dump_core ();
      }
   if(cmd > 0)
      cmd_info[cmd].grantable = num_grant_cmds;  /* reverse lookup */
   grant_cmds[num_grant_cmds][0] = cmd;
   grant_cmds[num_grant_cmds][1] = admin_lvl;
   grant_cmds[num_grant_cmds][2] = areas_lvl;
   grant_cmds[num_grant_cmds][3] = coder_lvl;
   grant_cmds[num_grant_cmds][4] = www_lvl;
   grant_cmds[num_grant_cmds][5] = q_lvl;

   num_grant_cmds++;
}

void assign_grant_commands(void)
{
   int i, j;

   /* intialize the array */
   for(i = 0; i < MAX_GRANT_CMDS; i++)
      {
      grant_cmds[i][0] = 0;
      for(j = 1; j < 6; j++)
         grant_cmds[i][j] = 61;
      }

   /* touch these three and die slowly, painfully, horribly.  JAB */
   /* touch touch touch! Sorry, John, gotta add another sphere. EAK */

   GRANTSET (-1, 51, 51, 51, 51, 51);
   GRANTSET (-2, 51, 51, 51, 51, 51);
   GRANTSET (-3, 51, 51, 51, 51, 51);
   GRANTSET (-4, 51, 51, 51, 51, 51);
   GRANTSET (-5, 51, 51, 51, 51, 51);

   /* unlike other places, the order of these following lines is VERY
      important.  If you add a new granted command, add it to the end
      of this list.  If you DON'T, you will royally screw up existing
      granted commands.  You have been warned.  You can however fiddle
      the assigned levels freely, changing the levels will only affect
      future grant/revoke commands, no effect on already granted
      commands.  The assigned levels ONLY affect auto-granting, any
      grantable command can be granted to any player.  JAB */

   /*       command       admin   areas   coder  WWW  Quest */
   GRANTSET (CMD_ACCEPT, 51, 52, 52, 52, 52);
   GRANTSET (CMD_ADVANCE, 59, 59, 59, 59, 59);
   GRANTSET (CMD_ALLOW, 57, 59, 59, 59, 59);
   GRANTSET (CMD_AT, 51, 51, 51, 51, 51);
   GRANTSET (CMD_BAN, 57, 59, 59, 59, 59);
   GRANTSET (CMD_CD, 57, 57, 57, 57, 57);
   GRANTSET (CMD_CLONE, 54, 54, 54, 54, 53);
   GRANTSET (CMD_DEBUG, 52, 52, 51, 53, 52);
   GRANTSET (CMD_DECLINE, 51, 52, 52, 52, 52);
   GRANTSET (CMD_DEMOTE, 59, 59, 59, 59, 59);
   GRANTSET (CMD_ECHO, 51, 51, 51, 51, 51);
   GRANTSET (CMD_ECHOA, 54, 54, 54, 54, 54);
   GRANTSET (CMD_ECHOZ, 52, 52, 52, 52, 52);
   GRANTSET (CMD_FINGER, 52, 54, 54, 54, 54);
   GRANTSET (CMD_FORCE, 55, 55, 55, 55, 55);
   GRANTSET (CMD_FREEZE, 53, 56, 56, 56, 56);
   GRANTSET (CMD_GOTO, 51, 51, 51, 51, 51);
   GRANTSET (CMD_GSHOUT, 51, 51, 51, 51, 51);
   GRANTSET (CMD_KNOCK, 51, 51, 51, 51, 51);
   GRANTSET (CMD_LEVELS, 51, 51, 51, 51, 51);
   GRANTSET (CMD_LFLAGS, 53, 55, 55, 55, 53);
   GRANTSET (CMD_LIGHTNING, 51, 51, 51, 51, 51);
   GRANTSET (CMD_LOAD, 54, 54, 54, 54, 54);
   GRANTSET (CMD_LOOKUP, 52, 52, 52, 52, 52);
   GRANTSET (CMD_POOFIN, 51, 51, 51, 51, 51);
   GRANTSET (CMD_POOFOUT, 51, 51, 51, 51, 51);
   GRANTSET (CMD_PTELL, 51, 51, 51, 51, 51);
   GRANTSET (CMD_PURGE, 53, 53, 53, 53, 53);
   GRANTSET (CMD_REINITPHYS, 53, 56, 56, 56, 56);
   GRANTSET (CMD_RELEASE, 51, 51, 51, 51, 51);
   GRANTSET (CMD_REROLL, 56, 57, 57, 57, 57);
   GRANTSET (CMD_RESTORE, 54, 54, 54, 54, 54);
   GRANTSET (CMD_SACRIFICE, 57, 57, 57, 57, 57);
   GRANTSET (CMD_SECRET, 54, 54, 54, 54, 52);
   GRANTSET (CMD_GTELL, 51, 51, 51, 51, 51);
   GRANTSET (CMD_SETBIT, 56, 56, 56, 56, 56);
   GRANTSET (CMD_SHUTDOW, 58, 58, 58, 58, 58);
   GRANTSET (CMD_SHUTDOWN, 58, 58, 58, 58, 58);
   GRANTSET (CMD_SILENCE, 52, 55, 55, 55, 55);
   GRANTSET (CMD_SNOOP, 53, 55, 55, 55, 55);
   GRANTSET (CMD_SNOWBALL, 51, 51, 51, 51, 51);
   GRANTSET (CMD_STAT, 52, 52, 52, 52, 52);
   GRANTSET (CMD_STAT_NOPAGE, 52, 52, 52, 52, 52);
   GRANTSET (CMD_STRING, 56, 56, 56, 56, 54);
   GRANTSET (CMD_SWITCH, 54, 54, 54, 54, 52);
   GRANTSET (CMD_TELEPORT, 52, 52, 52, 52, 52);
   GRANTSET (CMD_TERMINATE, 58, 60, 60, 60, 60);
   GRANTSET (CMD_TITLE, 51, 51, 51, 51, 51);
   GRANTSET (CMD_TRANSFER, 52, 52, 52, 52, 52);
   GRANTSET (CMD_USERS, 51, 51, 51, 5, 51);
   GRANTSET (CMD_WHERE, 51, 52, 52, 52, 52);
   GRANTSET (CMD_WIZLOCK, 57, 57, 57, 57, 57);
   GRANTSET (CMD_ZRESET, 57, 54, 57, 57, 57);
   GRANTSET (CMD_INROOM, 51, 51, 51, 51, 51);
   GRANTSET (CMD_WHICH, 52, 52, 52, 52, 52);
   GRANTSET (CMD_SETHOME, 56, 57, 57, 57, 57);
   GRANTSET (CMD_RENAME, 53, 57, 57, 57, 57);
   GRANTSET (CMD_LOADCHAR, 58, 59, 59, 59, 59);
   GRANTSET (CMD_CHANGELOG, 52, 52, 51, 52, 52);
   GRANTSET (CMD_CCONTROL, 57, 57, 55, 59, 59);
   GRANTSET (CMD_ACONTROL, 53, 59, 59, 59, 59);
   GRANTSET (CMD_JCONTROL, 60, 60, 60, 60, 60);
   GRANTSET (CMD_OUTCAST, 57, 57, 57, 57, 55);
#ifdef KINGDOM
   GRANTSET (CMD_HCONTROL, 55, 55, 55, 55, 55);
#endif
   GRANTSET (CMD_SETHELPER, 52, 59, 59, 59, 59);
   GRANTSET (CMD_PROC, 58, 58, 54, 58, 58);
   GRANTSET (CMD_FUNCTION, 58, 58, 54, 58, 58);
   GRANTSET (CMD_LWITNESS, 53, 54, 54, 54, 54);
   GRANTSET (CMD_GENLOG, 54, 54, 54, 54, 54);
   GRANTSET (CMD_CP, 60, 60, 60, 60, 60);
   GRANTSET (CMD_UNDECLINE, 54, 56, 56, 56, 56);
   GRANTSET (CMD_DENY, 54, 56, 56, 56, 56);
   GRANTSET (CMD_ECHOT, 51, 51, 51, 51, 51);
   GRANTSET (CMD_RELOAD, 57, 57, 57, 57, 57);
   GRANTSET (CMD_IPSHARE, 51, 53, 53, 53, 53);
   GRANTSET (CMD_ADDPRESTIGE, 57, 57, 57, 57, 53);
   GRANTSET (CMD_FINDQUEST, 57, 57, 57, 57, 57);
   GRANTSET (CMD_LOADMOB, 54, 54, 54, 54, 52);
   GRANTSET (CMD_ALLOWGROUP, 57, 57, 57, 57, 54);
   GRANTSET (CMD_REAGGRO, 57, 57, 57, 57, 57);
   GRANTSET (CMD_DEAGGRO, 57, 57, 57, 57, 57);
   GRANTSET (CMD_PREP, 57, 57, 57, 57, 57);
   GRANTSET (CMD_QWIZ, 60, 60, 60, 60, 51);
   GRANTSET (CMD_CREATEMOB, 54, 54, 54, 54, 52);
   GRANTSET (CMD_RPTOGGLE, 60, 60, 60, 60, 57);
   GRANTSET (CMD_SETPK, 56, 56, 56, 56, 56);

   /* final check, for commands marked as 'grantable' but not listed above */
   for(i = 1; *command[i] != '\n'; i++)
      if(cmd_info[i].grantable == -1)
         {
         /* something hosed up, somewhere */
         dump_core ();
         }
}

