EMS Command Usage (Summary)

ems list [status|all]
  - Show pending-accept accounts by default
  - Optional filter: nullfile|unknown|empty|start|badname|badpin|bademail|reply|accept|frozen|thawed|declined|broken|deleted|okay|closed|all

ems lookup <name|email|status|pin|domain|all>
  - Lookup accounts in the master database

ems header <name> [list|N]
  - List or show a specific email header entry for a player

ems rlist <name>
  - List player record entries

ems rshow <name> [N|all]
  - Show a specific record or all

ems radd <name> 'Title' <text...>
  - Append a record entry with title and free text

ems rdelete <name> <N>
  - Delete record entry N

ems accept <name>
ems decline <name>
ems freeze <name>
ems thaw <name>
ems reset <name>
ems kill <name>
  - Update account status, with optional admin comment in the original command usage

ems status
  - Show EMS system statistics