/* ***************************************************************************
 *  File: stats.c                                           Part of Outcast    *
 *  Usage: This is code to display mud statistics                            *
 *  Copyright  1998 - Charles <PERSON> and Outcast Systems Ltd.              *
 *************************************************************************** */

/* Written by <PERSON><PERSON><PERSON> 8/13/98 */

#include <ctype.h>
#include <stdio.h>
#include <string.h>
#include <time.h>

#include "comm.h"
#include "db.h"
#include "events.h"
#include "interp.h"
#include "utils.h"
#ifdef NEWJUSTICE
   #include "newjustice.h"
#endif
#ifdef OLDJUSTICE
   #include "justice.h"
#endif
#include "prototypes.h"
#include "race_class.h"
#include "skillrec.h"
#include "spells.h"
#include "structs.h"

/* external variables */

extern P_char PC_list;
extern P_desc descriptor_list;
extern P_room world;
extern const char *class_types[];
extern const char *command[];
extern const char *connected_types[];
extern const char *dirs[];
extern const char *event_names[];
extern const char *fullness[];
extern const char *language_names[];
extern const char *month_name[];
extern const char *player_bits[];
extern const char *player_law_flags[];
extern const char *player_prompt[];
extern const char *weekdays[];
extern const char *where[];
extern const int boot_time;
extern const int exp_table[TOTALCLASS][52];
extern const int rev_dir[];
extern const struct stat_data stat_factor[];
extern const struct trait_data racial_traits[];
extern int avail_descs;
extern int help_array[27][2];
extern int info_array[27][2];
extern int max_descs;
extern int max_users_playing;
extern int number_of_quests;
extern int number_of_shops;
extern int pulse;
extern int top_of_world;
extern int top_of_zone_table;
extern int used_descs;
extern struct time_info_data time_info;
extern struct zone_data *zone_table;
extern u_int event_counter[];

/* This function is called by an EVENT_SPECIAL every 5 minutes.  It outputs */
/* some basic game stats (number of each class, race, uptime, etc) to a     */
/* comma-delimited text file for use by a separate perl script that converts*/
/* it into an html file.                                                    */
/* - CRM                                                                    */

void mud_info_web(void)
{
   char Gbuf1[MAX_STRING_LENGTH], Gbuf2[MAX_STRING_LENGTH];
   char Gbuf3[MAX_STRING_LENGTH];
   char *timestr;
   FILE *statfile;
   P_char t_char;
   struct tm *lt;
   struct time_info_data uptime;
   long ct;
   const char *suf;
   int weekday, day;
   int num_players = 0, i = 0;
   int cur_class = 0, cur_race = 0;
   int num_race[LAST_PC_RACE + 1], num_class[LAST_CLASS + 1], num_home[MAX_HOMETOWN + 1];
   int good_count = 0, evil_count = 0;

   Gbuf1[0] = 0;
   Gbuf3[0] = 0;

   /* Initialize the race stats array */
   for(i = 1; i <= LAST_PC_RACE; i++)
      {
      num_race[i] = 0;
      }

   /* Initialize the class stats array */
   for(i = 1; i <= LAST_CLASS; i++)
      {
      num_class[i] = 0;
      }

   /* Initialize the hometown stats array */
   for(i = 1; i <= MAX_HOMETOWN; i++)
      {
      num_home[i] = 0;
      }


   /* Main loop, populates the race and class arrays on one pass */

   for(t_char = PC_list; t_char; t_char = t_char->next)
      {
      if(!IS_NPC(t_char) && t_char->desc)
         {

         /* Increment the total number of players */
         num_players++;

         /* Get the PC's race and increment the matching array value */
         cur_race = GET_RACE(t_char);
         num_race[cur_race] += 1;

         /* Same as above, but with classes */
         cur_class = GET_CLASS(t_char);
         num_class[cur_class] += 1;

         /* Hometown Census! */
         if(CHAR_IN_TOWN(t_char))
            {
            num_home[CHAR_IN_TOWN(t_char)] += 1;
            }

         if(RACE_GOOD(t_char))
            good_count++;
         else
            evil_count++;
         }
      }

   /* Put the race stats in a holding string and add it to the main str */
   for(i = 1; i <= LAST_PC_RACE; i++)
      {
      sprintf(Gbuf2, "\"%d\",", num_race[i]);
      strcat(Gbuf3, Gbuf2);
      }

   /* Put the class stats in a holding string and add it to the main str */
   for(i = 1; i <= LAST_CLASS; i++)
      {
      sprintf(Gbuf2, "\"%d\",", num_class[i]);
      strcat(Gbuf3, Gbuf2);
      }

   /* Put the town stats in a holding string and add it to the main str */
   for(i = 1; i <= MAX_HOMETOWN; i++)
      {
      sprintf(Gbuf2, "\"%d\",", num_home[i]);
      strcat(Gbuf3, Gbuf2);
      }

   /* Add the current number of players to the string */
   sprintf(Gbuf2, "\"%d\",", num_players);
   strcat(Gbuf3, Gbuf2);

   /* Calculate uptime and add it to the string */
   ct = time(0);
   real_time_passed(&uptime, ct, boot_time);

   sprintf(Gbuf2, "\"%d:%s%d:%s%d\",",
           uptime.day * 24 + uptime.hour,
           (uptime.minute > 9) ? "" : "0", uptime.minute,
           (uptime.second > 9) ? "" : "0", uptime.second);

   strcat(Gbuf3, Gbuf2);

   /* Find real time and add it to the string */
   lt = localtime(&ct);
   if (!lt) {
      timestr = "[time error]";
   } else {
      timestr = asctime(lt);
      *(timestr + strlen(timestr) - 1) = '\0';
   }

   sprintf(Gbuf2, "\"%s\",", timestr);
   strcat(Gbuf3, Gbuf2);

   /* Good/Evil count */
   sprintf(Gbuf2, "\"%d\",\"%d\",", good_count, evil_count);
   strcat(Gbuf3, Gbuf2);

   /* Get game-time and add to the main str.. */
   sprintf(Gbuf2, "\"%d%s%s, on ",
           (time_info.hour % 12) ? (time_info.hour % 12) : 12, Gbuf1,
           (time_info.hour > 11) ? "pm" : "am");

   weekday = ((35 * time_info.month) + time_info.day + 1) % 7;

   strcat(Gbuf2, weekdays[weekday]);
   strcat(Gbuf3, Gbuf2);

   day = time_info.day + 1; /* day in [1..35] */

   if(day == 1)
      suf = "st";
   else if(day == 2)
      suf = "nd";
   else if(day == 3)
      suf = "rd";
   else if(day < 20)
      suf = "th";
   else if((day % 10) == 1)
      suf = "st";
   else if((day % 10) == 2)
      suf = "nd";
   else if((day % 10) == 3)
      suf = "rd";
   else
      suf = "th";

   sprintf(Gbuf2, " The %d%s Day of the %s, Year %d.\"\n",
           day, suf, month_name[time_info.month], time_info.year);
   strcat(Gbuf3, Gbuf2);

   /* Open the file, or log and return */
   statfile = fopen(WEB_STATS, "w");
   if(!statfile)
      {
      logit(LOG_DEBUG, "could not open mud_info.txt for writing");
      return;
      }

   /* Write the string to the file, close it, and we're done */
   fprintf(statfile, Gbuf3);
   fclose(statfile);

   /* Re-add the event */
   AddEvent(EVENT_SPECIAL, 1200, TRUE, mud_info_web, NULL);

}

/* Essentially the same as the previous function, but this version appends */
/* to a file, creating an hourly log of player/hometown stats.  I split    */
/* it into it's own function in anticipation of future collected player    */
/* statistics that might be unsuitable for the live version, ie average    */
/* player level, zone population, etc etc                      - 8/98 CRM  */

void mud_info_log(void)
{
   char Gbuf1[MAX_STRING_LENGTH], Gbuf2[MAX_STRING_LENGTH];
   char Gbuf3[MAX_STRING_LENGTH];
   char *timestr;
   FILE *statfile;
   P_char t_char;
   struct tm *lt;
   struct time_info_data uptime;
   long ct;
   const char *suf;
   int weekday, day;
   int num_players = 0, i = 0;
   int cur_class = 0, cur_race = 0;
   int num_race[LAST_PC_RACE + 1], num_class[LAST_CLASS + 1], num_home[MAX_HOMETOWN + 1];

   Gbuf1[0] = 0;
   Gbuf3[0] = 0;

   /* Initialize the race stats array */
   for(i = 1; i <= LAST_PC_RACE; i++)
      {
      num_race[i] = 0;
      }

   /* Initialize the class stats array */
   for(i = 1; i <= LAST_CLASS; i++)
      {
      num_class[i] = 0;
      }

   /* Initialize the hometown stats array */
   for(i = 1; i <= MAX_HOMETOWN; i++)
      {
      num_home[i] = 0;
      }


   /* Main loop, populates the race and class arrays on one pass */

   for(t_char = PC_list; t_char; t_char = t_char->next)
      {
      if(!IS_NPC(t_char) && t_char->desc)
         {

         /* Increment the total number of players */
         num_players++;

         /* Get the PC's race and increment the matching array value */
         cur_race = GET_RACE(t_char);
         num_race[cur_race] += 1;

         /* Same as above, but with classes */
         cur_class = GET_CLASS(t_char);
         num_class[cur_class] += 1;

         /* Hometown Census! */
         if(CHAR_IN_TOWN(t_char))
            {
            num_home[CHAR_IN_TOWN(t_char)] += 1;
            }
         }
      }

   /* Put the race stats in a holding string and add it to the main str */
   for(i = 1; i <= LAST_PC_RACE; i++)
      {
      sprintf(Gbuf2, "\"%d\",", num_race[i]);
      strcat(Gbuf3, Gbuf2);
      }

   /* Put the class stats in a holding string and add it to the main str */
   for(i = 1; i <= LAST_CLASS; i++)
      {
      sprintf(Gbuf2, "\"%d\",", num_class[i]);
      strcat(Gbuf3, Gbuf2);
      }

   /* Put the town stats in a holding string and add it to the main str */
   for(i = 1; i <= MAX_HOMETOWN; i++)
      {
      sprintf(Gbuf2, "\"%d\",", num_home[i]);
      strcat(Gbuf3, Gbuf2);
      }

   /* Add the current number of players to the string */
   sprintf(Gbuf2, "\"%d\",", num_players);
   strcat(Gbuf3, Gbuf2);

   /* Calculate uptime and add it to the string */
   ct = time(0);
   real_time_passed(&uptime, ct, boot_time);

   sprintf(Gbuf2, "\"%d:%s%d:%s%d\",",
           uptime.day * 24 + uptime.hour,
           (uptime.minute > 9) ? "" : "0", uptime.minute,
           (uptime.second > 9) ? "" : "0", uptime.second);

   strcat(Gbuf3, Gbuf2);

   /* Find real time and add it to the string */
   lt = localtime(&ct);
   if (!lt) {
      timestr = "[time error]";
   } else {
      timestr = asctime(lt);
      *(timestr + strlen(timestr) - 1) = '\0';
   }

   sprintf(Gbuf2, "\"%s\",", timestr);
   strcat(Gbuf3, Gbuf2);

   /* Get game-time and add to the main str.. */
   sprintf(Gbuf2, "\"%d%s%s, on ",
           (time_info.hour % 12) ? (time_info.hour % 12) : 12, Gbuf1,
           (time_info.hour > 11) ? "pm" : "am");

   weekday = ((35 * time_info.month) + time_info.day + 1) % 7;

   strcat(Gbuf2, weekdays[weekday]);
   strcat(Gbuf3, Gbuf2);

   day = time_info.day + 1; /* day in [1..35] */

   if(day == 1)
      suf = "st";
   else if(day == 2)
      suf = "nd";
   else if(day == 3)
      suf = "rd";
   else if(day < 20)
      suf = "th";
   else if((day % 10) == 1)
      suf = "st";
   else if((day % 10) == 2)
      suf = "nd";
   else if((day % 10) == 3)
      suf = "rd";
   else
      suf = "th";

   sprintf(Gbuf2, " The %d%s Day of the %s, Year %d.\"\n",
           day, suf, month_name[time_info.month], time_info.year);
   strcat(Gbuf3, Gbuf2);

   /* Open the file, or log and return */
   if((statfile = fopen("lib/logs/player-log/player_stats", "a")) == 0)
      {
      logit(LOG_DEBUG, "could not open lib/logs/player-log/player_stats for writing");
      return;
      }

   /* Write the string to the file, close it, and we're done */
   fprintf(statfile, Gbuf3);
   fclose(statfile);

   /* Re-add the event */
   AddEvent(EVENT_SPECIAL, 14400, TRUE, mud_info_log, NULL);

}

