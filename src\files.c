/* ***************************************************************************
 *  File: files.c                                              Part of Outcast *
 *  Usage: save/restore player and corpse information to/from disk           *
 *  Written by: <PERSON>,  Modified by: <PERSON>                       *
 *  Copyright  1994, 1995 - Outcast Systems Ltd.                             *
 *************************************************************************** */

#include "files.h"
#ifdef NEWJUSTICE
#include "newjustice.h"
#endif
#ifdef OLDJUSTICE
#include "justice.h"
#endif

static P_event save_event;
static char *ibuf;
static int corpse_room;
static int cache_room;
static int int_size = sizeof (int);
static int long_size = sizeof (long);
static int short_size = sizeof (short);
static int float_size = sizeof (float);
static int save_count;
static int stat_vers, obj_vers, aff_vers, witness_vers, skill_vers, saved_aff_bytes;

/* static function protoypes */

uint getInt(char **);
float getFloat(char **);
ush_int getShort(char **);
char *getString(char **);
int OpenPfile(P_char, char *, FILE **);
static bool writeObjectlist(P_obj, int);
static int convert_stat(int);
static int countEquip(P_char);
static int countInven(P_obj);
static int restoreAffects(char *, P_char);
static int restoreObjects(char *, P_char);
static int restoreSkills(char *, P_char);
static int restoreStatus(char *, P_char);
static int writeAffects(char *, struct affected_type *, int);
static int writeItems(char *, P_char);
static int writeSkills(char *, P_char);
static int writeStatus(char *, P_char);
static struct extra_descr_data *find_totem_description(P_obj);
static ulong ObjUniqueFlags(P_obj, P_obj);
static void recalc_base_hits(P_char);
static void reroll_basic_abilities(P_char);
static void writeObject(P_obj, int, ulong, int, int);
static int translateSaveRoom(P_char ch, int type, int room);
static int npcsave_readMobileData(P_char, P_char, char *, char *, int);
static int npcsave_constructBufferFromHeader(char **, struct npc_file_header *, int);
static int npcsave_restoreHeader(P_char, char **, struct npc_file_header *, int *);
static int npcsave_openFile(P_char, char *, int, FILE **);
static int npcsave_constructMobileData(P_char, int, int, char *);
static int npcsave_writeData(P_char, int, int, int, char *);
static int npcsave_selectName(const struct dirent *direntry);

#if 0                           /* not used, but waste not, want not. JAB */
static void convert_shaman_to_cleric(P_char);
static void nuke_berserkers(P_char);
static int confiscate_item(P_char, int);
static void confiscate_all(P_char);
#endif

extern struct mm_ds *dead_crime_pool;
extern struct mm_ds *dead_witness_pool;
extern int cc_prestige_charismaBonus;
extern struct pow_app_type pow_app[];
extern const struct stat_data stat_factor[];

extern const char *grantable_bits[];
extern int grant_cmds[MAX_GRANT_CMDS][6];

/* local globals */

static P_obj save_equip[MAX_WEAR];
char select_charname[512];
int select_charname_len;

typedef int (*EVENT_FUNC)(P_char, char *, int);

/* current save file method, all character data is written to a string, which is then written in one operation.
   Pluses:  Only one disk access per save so it's damn fast.  Minuses: if string gets too long, boom!  Max size is
   found in SAV_MAXSIZE define.  Data format for save files is found below after the #if 0.

   Addendum: writeCharacter and writeCorpse now verify that the write occurred correctly by reading it back and
   doing a memcmp on the two strings.  THEN it moves the *.new file into place, and removes the *.bak file.  This
   will prevent us from corrupting the file on disk (like when we run out of disk space).  It screams to the logs
   and to the status channel when they DON'T match.  A machine crash while we are moving files into place COULD
   cause problems, and of course if we screw up building the save string, we are gonna hose the pfile with complete
   confidence.  -JAB */

#if 0
/*  save file structure, if you change it, change this as well, so we know
   what the hell it's doing.

   key: b - byte, c - string, i - int, l - long, s - short.

   comments preceded by **.

   b SAV_SAVEVERS
   b sizeof(short)
   b sizeof(int)
   b sizeof(long)
   b save type

   i skill_offset
   i affect_offset
   i item_offset
   i size_offset

   i save room
   l save time

 ** unequip everything and remove all affects before saving char_data

   b SAV_STATVERS (stat_vers)
   c GET_NAME
   b only.pc->Pscreen_length
   c only.pc->pwd
   c player.short_descr
   c player.long_descr
   c player.description
   c GET_TITLE
   c GET_LAST_LOGIN (stat_vers 26+)
   b GET_CLASS
   s GET_CLASS_CHOICE
   b GET_RACE
   b GET_LEVEL
   b GET_SEX
   i GET_WEIGHT
   i points.base_height
   i GET_HOME
   i GET_BIRTHPLACE  (stat_vers 6+)
   i ch->psn         (stat_vers 30+)

   l player.time.birth
   i playing time (secs)
   l save time (again??)
   s perm_aging (stat_vers 15+)

   b TROPHYCOUNT (stat_vers < 22)
   b trophy_current (stat_vers < 22)
   i*trophy_stuff[] (stat_vers < 22)
   b*trophy_stuff[] (stat_vers < 22)

   s MAX_TONGUE
   b*MAX_TONGUE only.pc->talks[]

   b GET_STR  (stat_ver < 13)
   b GET_ADD  (stat_ver < 13)
   b GET_INT  (stat_ver < 13)
   b GET_WIS  (stat_ver < 13)
   b GET_DEX  (stat_ver < 13)
   b GET_CON  (stat_ver < 13)

   b STR    (stat_ver > 12)
   b DEX    (stat_ver > 12)
   b AGI    (stat_ver > 12)
   b CON    (stat_ver > 12)
   b POW    (stat_ver > 12)
   b INT    (stat_ver > 12)
   b WIS    (stat_ver > 12)
   b CHA    (stat_ver > 12)
   b KARMA  (stat_ver > 12)
   b LUCK   (stat_ver > 12)

   s GET_MANA
   s points.max_mana
   s GET_HIT
   s points.max_hit
   s GET_MOVE
   s points.max_move

   s 100  AC       (stat_vers < 11)
   b 0    hitroll  (stat_vers < 11)
   b 0    damroll  (stat_vers < 11)

   i GET_COPPER
   i GET_SILVER
   i GET_GOLD
   i GET_PLATINUM
   i GET_EXP
   i max_exp (stat_vers 15+)

   b ch->specials.position  (stat_vers < 11)
   b default position       (stat_vers < 11)

   i specials.act  (stat_vers < 23)
   b MAX_PACT  (stat_vers 23+)
   b[] only.pc->pcact (stat_vers 23+)

   i only.pc->spells_to_learn
   i specials.alignment

   b 3  # of conditions     (stat_vers < 11)
   b*3 specials.conditions[]

   c only.pc->poofIn
   c only.pc->poofOut

   b 5  # of saving throws  (stat_vers < 11)
   s*5 specials.apply_saving_throw[]  (stat_vers < 11)

   b only.pc->echo_toggle
   s only.pc->prompt
   l only.pc->wiz_invis
   l only.pc->law_flags  (pflags in stat_vers < 11)
   s only.pc->wimpy
   s only.pc->aggressive

   i GET_BALANCE_COPPER
   i GET_BALANCE_SILVER
   i GET_BALANCE_GOLD
   i GET_BALANCE_PLATINUM

   l only.pc->grant

   s only.pc->guild

   s TROPHYCOUNT (stat_vers 22+)
   {
   i Vnum (stat_vers 22+)
   i XP (stat_vers 22+)
   }*TROPHYCOUNT

   b SAV_SKILLVERS (skill_vers)

   s number of skills
   {
   b skills[].learned
   b skills[].memorized
   }*number of skills

   b number of spells to be memorized
   {
   s SPELL_xxx
   }*number of sspells to be memorized

   s MAX_TIMED_USAGES
   {
   l char_timed_usages[].time_of_first_use
   b char_timed_usages[].times_used
   }*MAX_TIMED_USAGES

   b SAV_AFFVERS (aff_vers)
   b AFF_BYTES (aff_vers 6+)

   s number of affects

   {
   b type
   s duration
   s modifier
   b location
   s sets_affs
   }*number of affects

 ** add back affects and reequip char if we are not extracting them

   b SAV_ITEMVERS (obj_vers)

   i number of items being saved

 ** equip first, then inven

 ** New method(s) of saving objects:
 ** note that order is NOT preserved, as it's arbitrary in the first place.

   {
 ** all objects have these first two values:

   b storage_type_flag
   i Vnum

 ** utility bytes, how many and meaning based on storage_type_flag

 ** if was worn:
   b location

 ** if has 'count':
   s count

 ** if item is non-stock:
   s unique_flags

 ** only the values that varied from 'stock' are saved, as follows:
   {
   c name
   c short_description
   c description
   c action_description
   i*4 value[0-3]
   b type
   i wear_flags
   i extra_flags
   i anti_flags
   i weight
   i cost
   i durability
   s sets_affs
   {
   b obj->affected[0].location
   b obj->affected[0].modifier
   b obj->affected[1].location
   b obj->affected[1].modifier
   }
   }

 ** if type == ITEM_SPELLBOOK & has some spells in it..
   b array length if 0, no stuff. just in case.
   byte_array [MAX_SKILLS/8+1] spells

 ** if an object contained other objects, the contained objects will follow
   the container, and the list will be terminated with a flag byte.  Nested
   containers are not a problem, as the routines are recursive.

 ** greatest savings are found with large numbers of the same 'stock' object
   (like iron rations), old system required 56 bytes for each, new system only
   needs 7 bytes for the lot of them.

 ** restore reads 'flag' and Vnum, loads one if possible, then alters just
   loaded object (if needed) and puts in it container or adds it to the equip
   list (equipping items is done last, so that worn containers have correct
   weight, etc).  If an item can no longer be worn (it changed flags since it
   was saved, etc), it stays in inventory.  If a container has changed it's
   holding capacity, or some of it's contents have changed weight, they also
   load in inventory, rather than overloading the container.

 ** FUTURE: If an item can't be loaded (it's been removed for game, it's
   zone isn't loaded, etc) a new object 'missing item token' is created
   and the saved data is written into it's long_description, this item
   itself can be saved, and used to restore the item later (if the zone is
   missing, an error was made, etc), the token weighs 0, is transient, and
   may not be altered in any way (except to be destroyed), is hidden, and
   may not be found, and does not count against number of items in inven.
   Basically, the player won't know it's there, but an immortal will see
   it if they look at the player (they load in inven)

 ** under old system, strung objects reverted and there was a 292 object
   limit.  under new system, limit is more like 2500+, but having lots of
   'strung' items will shrink that number alot.

 ** For most players, the save files will be much smaller (not to mention
   that containers now restore with contents intact.)

 */
#endif

#if 0


/* File structure for the pet rent file (The one containing multiple mobs)..

   Rent Header

   b SAV_PETSAVEVERS
   b number of rented pets
   {
   i offset
   i size
   i psn
   } * number of rented pets
   b 0xffh  - sync/check byte

   b SAV_SAVEVERS
   b sizeof(short)
   b sizeof(int)
   b sizeof(long)
   b save type

   i skill_offset
   i affect_offset
   i item_offset
   i size_offset

   i save room
   l save time

 ** unequip everything and remove all affects before saving char_data

   b SAV_STATVERS (stat_vers)
   i vnum
   c GET_NAME
   c player.short_descr
   c player.long_descr
   c player.description
   b GET_RACE
   b GET_LEVEL
   b GET_SEX
   i GET_WEIGHT
   i points.base_height
   i GET_HOME
   i GET_BIRTHPLACE  (stat_vers 6+)
   i ch->psn         (stat_vers 30+)

   l player.time.birth
   i playing time (secs)
   l save time (again??)
   s perm_aging (stat_vers 15+)

   b GET_STR  (stat_ver < 13)
   b GET_ADD  (stat_ver < 13)
   b GET_INT  (stat_ver < 13)
   b GET_WIS  (stat_ver < 13)
   b GET_DEX  (stat_ver < 13)
   b GET_CON  (stat_ver < 13)

   b STR    (stat_ver > 12)
   b DEX    (stat_ver > 12)
   b AGI    (stat_ver > 12)
   b CON    (stat_ver > 12)
   b POW    (stat_ver > 12)
   b INT    (stat_ver > 12)
   b WIS    (stat_ver > 12)
   b CHA    (stat_ver > 12)
   b KARMA  (stat_ver > 12)
   b LUCK   (stat_ver > 12)

   s GET_MANA
   s points.max_mana
   s GET_HIT
   s points.max_hit
   s GET_MOVE
   s points.max_move

   s 100  AC       (stat_vers < 11)
   b 0    hitroll  (stat_vers < 11)
   b 0    damroll  (stat_vers < 11)

   i GET_COPPER
   i GET_SILVER
   i GET_GOLD
   i GET_PLATINUM
   i GET_EXP
   i max_exp (stat_vers 15+)

   b ch->specials.position  (stat_vers < 11)
   b default position       (stat_vers < 11)

   i specials.alignment

   b 3  # of conditions     (stat_vers < 11)
   b*3 specials.conditions[]

   s MAX_TIMED_USAGES
   {
   l char_timed_usages[].time_of_first_use
   b char_timed_usages[].times_used
   }*MAX_TIMED_USAGES

   b SAV_AFFVERS (aff_vers)
   b AFF_BYTES (aff_vers 6+)

   s number of affects

   {
   b type
   s duration
   s modifier
   b location
   s sets_affs
   }*number of affects

 ** add back affects and reequip char if we are not extracting them

   b SAV_ITEMVERS (obj_vers)

   i number of items being saved

 ** equip first, then inven

 ** New method(s) of saving objects:
 ** note that order is NOT preserved, as it's arbitrary in the first place.

   {
 ** all objects have these first two values:

   b storage_type_flag
   i Vnum

 ** utility bytes, how many and meaning based on storage_type_flag

 ** if was worn:
   b location

 ** if has 'count':
   s count

 ** if item is non-stock:
   s unique_flags

 ** only the values that varied from 'stock' are saved, as follows:
   {
   c name
   c short_description
   c description
   c action_description
   i*4 value[0-3]
   b type
   i wear_flags
   i extra_flags
   i weight
   i cost
   i durability
   s sets_affs
   {
   b obj->affected[0].location
   b obj->affected[0].modifier
   b obj->affected[1].location
   b obj->affected[1].modifier
   }
   }
 */
#endif

#define GET_LONG(buf) getLong(&buf)

#define ObjectsMatch(obj, control) \
(((obj)->str_mask == (control)->str_mask) && \
 ((obj)->affected[0].location == (control)->affected[0].location) && \
 ((obj)->affected[0].modifier == (control)->affected[0].modifier) && \
 ((obj)->affected[1].location == (control)->affected[1].location) && \
 ((obj)->affected[1].modifier == (control)->affected[1].modifier) && \
 ((obj)->extra_flags == (control)->extra_flags) && \
 ((obj)->anti_flags == (control)->anti_flags) && \
 !memcmp((void *)(obj)->sets_affs, (void *)(control)->sets_affs, AFF_BYTES) && \
 ((obj)->value[0] == (control)->value[0]) && \
 ((obj)->value[1] == (control)->value[1]) && \
 ((obj)->value[2] == (control)->value[2]) && \
 ((obj)->value[3] == (control)->value[3]) && \
 ((obj)->wear_flags == (control)->wear_flags) && \
 ((obj)->weight == (control)->weight) && \
 ((obj)->cost == (control)->cost) && \
 ((obj)->durability == (control)->durability) && \
 ((obj)->trap_eff == (control)->trap_eff) && \
 ((obj)->trap_dam == (control)->trap_dam) && \
 ((obj)->trap_charge == (control)->trap_charge) && \
 ((obj)->trap_level == (control)->trap_level) && \
 ((obj)->type == (control)->type))


/* this table relates equipment position to the 'wear()' keyword */

int restore_wear[] ={
  -2, /* WEAR_LIGHT       *//* should not be used any longer! */
  1, /* WEAR_FINGER_R    */
  1, /* WEAR_FINGER_L    */
  2, /* WEAR_NECK_1      */
  2, /* WEAR_NECK_2      */
  3, /* WEAR_BODY        */
  4, /* WEAR_HEAD        */
  5, /* WEAR_LEGS        */
  6, /* WEAR_FEET        */
  7, /* WEAR_HANDS       */
  8, /* WEAR_ARMS        */
  14, /* WEAR_SHIELD      */
  9, /* WEAR_ABOUT       */
  10, /* WEAR_WAIST       */
  11, /* WEAR_WRIST_R     */
  11, /* WEAR_WRIST_L     */
  12, /* PRIMARY_WEAPON   */
  12, /* SECONDARY_WEAPON */
  13, /* HOLD             */
  15, /* WEAR_EYES        */
  16, /* WEAR_FACE        */
  17, /* WEAR_EARRING_R   */
  17, /* WEAR_EARRING_L   */
  18, /* WEAR_QUIVER      */
  19, /* GUILD_INSIGNIA   */
  20 /* WEAR_TAIL        */
};

unsigned long getLong(char **buf) {
  ulong l;

  bcopy(*buf, &l, long_size);

  l = ntohl(l);
  *buf += long_size;

  return l;
}

/* search obj's extra descriptions for one flagged with the totem description keyword */
static struct extra_descr_data *find_totem_description(P_obj obj) {
  struct extra_descr_data *tmp;

  for (tmp = obj->ex_description; tmp; tmp = tmp->next)
    /* This SHOULD be the 1st and only extra description. */
    if (tmp->keyword && tmp->description)
      if (!strcmp(tmp->keyword, "\003\002\003"))
        return tmp;

  return NULL;
}

/* following are functions that write data to disk (or prepare data for writing).  JAB */

static int writeStatus(char *buf, P_char ch) {
  char *start = buf;
  int tmp, tmpl, t_count, spell_write = 0;
  struct Trophy_data *troph;
  struct disease *dis;

  ADD_BYTE(buf, (char) SAV_STATVERS);
  NPC_ADD_INT(ch, buf, mob_index[ch->nr].virtual);
  ADD_STRING(buf, GET_NAME(ch));
  PC_ADD_BYTE(ch, buf, ch->only.pc->screen_length);
  PC_ADD_STRING(ch, buf, ch->only.pc->pwd);
  ADD_STRING(buf, ch->player.short_descr);
  ADD_STRING(buf, ch->player.long_descr);
  ADD_STRING(buf, ch->player.description);
  PC_ADD_STRING(ch, buf, GET_TITLE(ch));
  PC_ADD_STRING(ch, buf, GET_LAST_LOGIN(ch));
  ADD_BYTE(buf, GET_CLASS(ch));
  PC_ADD_SHORT(ch, buf, GET_CLASS_CHOICE(ch));
  ADD_BYTE(buf, GET_RACE(ch));
  ADD_BYTE(buf, GET_LEVEL(ch));
  ADD_BYTE(buf, GET_SEX(ch));
  ADD_INT(buf, ch->points.base_weight);
  ADD_INT(buf, ch->points.base_height);
  ADD_INT(buf, GET_HOME(ch));
  ADD_INT(buf, GET_BIRTHPLACE(ch));
  ADD_INT(buf, ch->psn);

  /* terrible horrible hack preventing liches from aging.. its transparent
   * throughout the code tho and involves the least amount of work so bite me:)
   *                                                        -- Diirinka */
  tmpl = time(0);
  if ((GET_RACE(ch) == RACE_LICH) && IS_PC(ch)) {
    tmp = ch->player.time.birth + (tmpl - ch->player.time.saved);
    ADD_INT(buf, tmp);
  } else
    ADD_INT(buf, ch->player.time.birth);

  tmp = ch->player.time.played + (int) (tmpl - ch->player.time.logon);
  ADD_INT(buf, tmp); /* player age in secs */
  ADD_INT(buf, tmpl); /* last save time */
  ADD_SHORT(buf, ch->player.time.perm_aging);

  ch->player.time.saved = tmpl;

  PC_ADD_SHORT(ch, buf, (short) MAX_TONGUE);
  for (tmp = 0; tmp < MAX_TONGUE; tmp++)
    PC_ADD_BYTE(ch, buf, GET_LANGUAGE(ch, tmp));

  ADD_BYTE(buf, ch->base_stats.Str);
  ADD_BYTE(buf, ch->base_stats.Dex);
  ADD_BYTE(buf, ch->base_stats.Agi);
  ADD_BYTE(buf, ch->base_stats.Con);
  ADD_BYTE(buf, ch->base_stats.Pow);
  ADD_BYTE(buf, ch->base_stats.Int);
  ADD_BYTE(buf, ch->base_stats.Wis);
  ADD_BYTE(buf, ch->base_stats.Cha);
  ADD_BYTE(buf, ch->base_stats.Karma);
  ADD_BYTE(buf, ch->base_stats.Luck);

  ADD_SHORT(buf, GET_MANA(ch));
  ADD_SHORT(buf, ch->points.base_mana);
  ADD_SHORT(buf, GET_HIT(ch));
  PC_ADD_BYTE(ch, buf, ch->only.pc->spells_memmed[MAX_CIRCLE]);
  ADD_SHORT(buf, ch->points.base_hit);
  ADD_SHORT(buf, GET_MOVE(ch));
  ADD_SHORT(buf, ch->points.base_move);

  ADD_INT(buf, GET_COPPER(ch));
  ADD_INT(buf, GET_SILVER(ch));
  ADD_INT(buf, GET_GOLD(ch));
  ADD_INT(buf, GET_PLATINUM(ch));
  ADD_INT(buf, GET_EXP(ch));
  ADD_INT(buf, ch->points.max_exp);

  if (IS_PC(ch)) {
    ADD_BYTE(buf, (ubyte) PACT_BYTES);
    for (tmp = 0; tmp < PACT_BYTES; tmp++)
      ADD_BYTE(buf, (ubyte) ch->only.pc->pcact[tmp]);
  } else {
    ADD_BYTE(buf, (ubyte) NPCACT_BYTES);
    for (tmp = 0; tmp < NPCACT_BYTES; tmp++)
      ADD_BYTE(buf, (ubyte) ch->only.npc->npcact[tmp]);
  }

  ADD_INT(buf, ch->specials.alignment);

  /* assoc stuff */
  if (IS_PC(ch)) {
    ADD_SHORT(buf, ch->only.pc->asc_num);
    ADD_INT(buf, ch->only.pc->asc_rank);
    ADD_INT(buf, ch->only.pc->time_left_guild);
    ADD_BYTE(buf, ch->only.pc->nb_left_guild);

    ADD_BYTE(buf, (ubyte) ASCM_BYTES);
    for (tmp = 0; tmp < ASCM_BYTES; tmp++)
      ADD_BYTE(buf, (ubyte) ch->only.pc->asc_bits[tmp]);
  }

  /* Prestige */
  if (IS_PC(ch)) {
    ADD_INT(buf, ch->only.pc->prestige);
  }

  for (tmp = 0; tmp < 3; tmp++)
    ADD_BYTE(buf, ch->specials.conditions[tmp]);

  PC_ADD_STRING(ch, buf, ch->only.pc->poofIn);
  PC_ADD_STRING(ch, buf, ch->only.pc->poofOut);

  PC_ADD_BYTE(ch, buf, ch->only.pc->echo_toggle);
  PC_ADD_SHORT(ch, buf, ch->only.pc->prompt);
  PC_ADD_INT(ch, buf, ch->only.pc->wiz_invis);
  PC_ADD_INT(ch, buf, ch->only.pc->law_flags);
  if (IS_PC(ch)) {
    PC_ADD_BYTE(ch, buf, (ubyte) LAW_FLAG_BYTES);
    for (tmp = 0; tmp < LAW_FLAG_BYTES; tmp++)
      PC_ADD_BYTE(ch, buf, (ubyte) ch->only.pc->law_flag[tmp]);
  }
  PC_ADD_SHORT(ch, buf, ch->only.pc->wimpy);
  PC_ADD_SHORT(ch, buf, ch->only.pc->aggressive);
  PC_ADD_SHORT(ch, buf, ch->only.pc->time_judge);

  PC_ADD_INT(ch, buf, GET_BALANCE_COPPER(ch)); /* bank account */
  PC_ADD_INT(ch, buf, GET_BALANCE_SILVER(ch)); /* bank account */
  PC_ADD_INT(ch, buf, GET_BALANCE_GOLD(ch)); /* bank account */
  PC_ADD_INT(ch, buf, GET_BALANCE_PLATINUM(ch)); /* bank account */

  if (IS_PC(ch)) {
    PC_ADD_BYTE(ch, buf, (ubyte) GRANT_BYTES);
    for (tmp = 0; tmp < GRANT_BYTES; tmp++)
      PC_ADD_BYTE(ch, buf, (ubyte) ch->only.pc->grant[tmp]);
  }

  if (IS_PC(ch)) {
    PC_ADD_BYTE(ch, buf, (ubyte) DEBUG_BYTES);
    for (tmp = 0; tmp < DEBUG_BYTES; tmp++)
      PC_ADD_BYTE(ch, buf, (ubyte) ch->only.pc->debug[tmp]);
  }

  if (IS_PC(ch)) {
    for (t_count = 0, troph = ch->only.pc->Trophies; troph; troph = troph->next)
      t_count++;
    ADD_SHORT(buf, t_count);
    for (troph = ch->only.pc->Trophies; troph; troph = troph->next) {
      ADD_INT(buf, troph->Vnum);
      ADD_INT(buf, troph->XP);
    }
  }

  /* Save disease a PC is infected with */
  if (IS_PC(ch)) {
    t_count = countDiseases(ch);
    ADD_SHORT(buf, t_count);
    for (dis = ch->only.pc->diseases; dis; dis = dis->next) {
      PC_ADD_INT(ch, buf, dis->type);
      PC_ADD_INT(ch, buf, dis->stage);
    }
  }

  if (IS_PC(ch)) {
    PC_ADD_BYTE(ch, buf, (byte) ch->only.pc->condensed_flags);
  }

  if (IS_PC(ch)) {
    for (spell_write = 0; spell_write < 12; spell_write++)
      PC_ADD_BYTE(ch, buf, ch->only.pc->available_spells[spell_write]);
  }

  if (IS_PC(ch))
    PC_ADD_INT(ch, buf, ch->only.pc->manaburn);

  return (int) (buf - start);
}

static int writeAffects(char *buf, struct affected_type *af, int type) {
  char *start = buf;
  int tmp;
  signed short count = 0;
  struct affected_type *first = af;

  if (type == 4) { /* death save */
    ADD_BYTE(buf, (char) SAV_AFFVERS);
    ADD_BYTE(buf, AFF_BYTES);
    ADD_SHORT(buf, 0);
    return (int) (buf - start);
  }
  while (af) {
    count++;
    af = af->next;
  }

  ADD_BYTE(buf, (char) SAV_AFFVERS);
  ADD_BYTE(buf, (char) AFF_BYTES);

  af = first;
  ADD_SHORT(buf, count);

  while (af) {
    if (af->type == SKILL_METAGLOBE) {
      af = af->next;
      continue;
    }
    ADD_SHORT(buf, af->type);
    ADD_SHORT(buf, af->duration);
    ADD_SHORT(buf, af->modifier);
    ADD_BYTE(buf, af->location);
    for (tmp = 0; tmp < AFF_BYTES; tmp++)
      ADD_BYTE(buf, (ubyte) af->sets_affs[tmp]);
    af = af->next;
  }

  return (int) (buf - start);
}

static int writeSkills(char *buf, P_char ch) {
  char *start = buf;
  int i, l_p, num = MAX_SKILLS;
  struct memorize_data *tmp;

  ADD_BYTE(buf, (char) SAV_SKILLVERS);

  /* Save the spell memorized, and skill usages info -DCL */

  ADD_SHORT(buf, (short) num);
  for (i = 0; i < num; i++) {
    ADD_BYTE(buf, ch->only.pc->skills[i].learned);
    ADD_BYTE(buf, ch->only.pc->skills[i].memorized);
  }

  /* let's do it right, instead. JAB */

  for (l_p = 0, tmp = ch->only.pc->memorize_list; tmp; tmp = tmp->next)
    l_p++;

  ADD_BYTE(buf, (ubyte) l_p); /* count of spells to be memmed */

  if (l_p) {
    for (tmp = ch->only.pc->memorize_list; tmp; tmp = tmp->next)
      if (tmp->spell < MAX_SKILLS)
        ADD_SHORT(buf, (ush_int) tmp->spell);
  }
  ADD_SHORT(buf, (short) MAX_TIMED_USAGES);

  for (i = 0; i < MAX_TIMED_USAGES; i++) {
    ADD_INT(buf, ch->only.pc->timed_usages[i].time_of_first_use);
    ADD_BYTE(buf, ch->only.pc->timed_usages[i].times_used);
  }

  return (int) (buf - start);
}

/* recursive count of all objects in inventory */

static int countInven(P_obj obj) {
  int count = 0;

  while (obj) {
    count++;
    if (obj->contains)
      count += countInven(obj->contains);
    obj = obj->next_content;
  }

  return count;
}

static int countEquip(P_char ch) {
  int i, count = 0;

  for (i = 0; i < MAX_WEAR; i++)
    if (ch->equipment[i]) {
      count++;
      if (ch->equipment[i]->contains)
        count += countInven(ch->equipment[i]->contains);
    } else if (save_equip[i]) {
      count++;
      if (save_equip[i]->contains)
        count += countInven(save_equip[i]->contains);
    }
  return count;
}

/* compares obj to control and sets flag bits for differences, returns 16 bit flag value.  JAB */

static ulong ObjUniqueFlags(P_obj obj, P_obj control) {
  ulong flag = (ulong) obj->str_mask;
  int i;

  for (i = 0; i < 4; i++)
    if (obj->value[i] != control->value[i])
      flag |= 1 << (i + 4);

  if (obj->type != control->type)
    flag |= O_U_TYPE;

  if (obj->trap_charge != control->trap_charge)
    flag |= O_U_TRAP;

  if (obj->wear_flags != control->wear_flags)
    flag |= O_U_WEAR;

  if (obj->extra_flags != control->extra_flags)
    flag |= O_U_EXTRA;

  if (obj->anti_flags != control->anti_flags)
    flag |= O_U_ANTI;

  if (obj->weight != control->weight)
    flag |= O_U_WEIGHT;

  if (obj->cost != control->cost)
    flag |= O_U_COST;

  if (obj->durability != control->durability)
    flag |= O_U_DURABILITY;

  if (memcmp((void *) obj->sets_affs, (void *) control->sets_affs, AFF_BYTES))
    flag |= O_U_SETB;

  if ((obj->affected[0].location != control->affected[0].location) ||
          (obj->affected[0].modifier != control->affected[0].modifier) ||
          (obj->affected[1].location != control->affected[1].location) ||
          (obj->affected[1].modifier != control->affected[1].modifier))
    flag |= O_U_AFFS;

  return flag;
}

/* write a list of objects (and recursively it's contents...)  JAB */

static bool writeObjectlist(P_obj obj, int loc) {
  P_obj t_obj = NULL, obj2 = NULL, obj_c = NULL, t_obj2 = NULL, w_obj;
  bool skip;
  byte o_f_flag;
  int i, done[4000], done_num = 0, cont_wgt, count;
  ulong o_u_flag;

  for (w_obj = obj; w_obj; w_obj = obj2) {
    obj2 = w_obj->next_content;
    obj_c = w_obj->contains;
    o_f_flag = o_u_flag = cont_wgt = 0;
    count = 1;

    if (t_obj && (t_obj->R_num != w_obj->R_num)) {
#ifdef ARTIFACT
      /* this prevents the artifact from being removed from the owner if a save occurs and a temp artifact is
         created -- DDW */
      tagBogusArtifact(t_obj);
#endif
      extract_obj(t_obj);
      t_obj = NULL;
    }
    if (!t_obj)
      if (!(t_obj = read_object(w_obj->R_num, REAL))) {
        logit(LOG_DEBUG, "writeObjectlist(): obj %d [%d] not loadable",
                w_obj->R_num, obj_index[w_obj->R_num].virtual);
        continue;
      }
    if (OBJ_WORN(w_obj) || (loc && (save_equip[loc - 1] == w_obj)))
      o_f_flag |= O_F_WORN;

    if (obj_c) {
      o_f_flag |= O_F_CONTAINS;
      if (w_obj->type != ITEM_QUIVER) {
        for (t_obj2 = obj_c; t_obj2; t_obj2 = t_obj2->next_content)
          cont_wgt += GET_OBJ_WEIGHT(t_obj2);
        w_obj->weight -= cont_wgt;
      }
    }
    if (w_obj->events) {
      LOOP_EVENTS(save_event, w_obj->events)
      if (save_event->type == EVENT_DECAY)
        break;
      if (save_event)
        o_f_flag |= O_F_DECAY;
    }
    if (w_obj->type == ITEM_SPELLBOOK && find_spell_description(w_obj))
      o_f_flag |= O_F_SPELLBOOK;

    if ((obj_index[w_obj->R_num].virtual >= 716) && (obj_index[w_obj->R_num].virtual <= 747) &&
            find_totem_description(w_obj))
      o_f_flag |= O_F_SPELLBOOK;

    if ((o_u_flag = ObjUniqueFlags(w_obj, t_obj)))
      o_f_flag |= O_F_UNIQUE;

    if (w_obj->extra_flags & ITEM_NORENT)
      o_f_flag |= O_F_NORENT;

    /* ok, to make this work properly, we need to check for stock (only) items at each level (main carried list,
       and each container count as a separate 'level' of storage) and store them all together, thus common things
       like rations, don't take up inordinate space.  But, to avoid huge cpu overhead, we have to keep track,
       since this is a recursive routine, we store Rnums of already counted objects, to avoid duplication. */

    if (o_f_flag) {

      /* by definition, if this flag is set, COUNT cannot apply without going to absurd lengths, so we just write
         it out, call to write it's contents (if any), and move on. */

      writeObject(w_obj, o_f_flag, o_u_flag, (ush_int) 1, loc);

      if (obj_c)
        w_obj->weight += cont_wgt;

      if (obj_c)
        if (!writeObjectlist(obj_c, (byte) 0))
          return FALSE;

      continue;

    } else {

      /* ok, to keep things sane cpu-wise, but allow us to reduce duplicate items to minimal storage requirements,
         we do this:

         the done[] array holds R_nums of non-unique items already counted and stored, if the current object is on
         this list, it skips it and doesn't store it again.  If current object is NOT already in the array, it adds
         it to the list, then scans the rest of the list for duplicates, counts them, and if they are contiguous,
         advances the object pointer so we don't have to look at it ever again.  This should (theoretically anyway)
         be lots faster than writing every object without checking.  Unfortunately, the need to check each item for
         uniqueness as well, can slow things down quite a bit.

         Note: count only applies to current 'level' of objects, if a player has 10 rations in inven and 12 more
         in a bag, there will be two entries for rations, one for 10 and another for 12.  Have to do it this way
         to avoid having to store pointers, etc.
       */

      for (i = done_num - 1; i >= 0; i--)
        if (done[i] == w_obj->R_num)
          break;

      /* found it, it's already been counted, so skip it */

      if ((i >= 0) && (done[i] == w_obj->R_num))
        continue;

      /* not in done[], so add to done[], then count copies after it.  */

      done[done_num++] = w_obj->R_num;
      t_obj2 = obj2;
      skip = TRUE;

      while (t_obj2) {
        if ((t_obj2->R_num == w_obj->R_num) && !t_obj2->contains && ObjectsMatch(t_obj2, t_obj) &&
                (t_obj2->type != ITEM_SPELLBOOK ||
                !find_spell_description(t_obj2) ||
                !find_totem_description(t_obj2))) {
          count++;
          if (count > 32000) {
            statuslog(51, "Some feeb has > 32,000 %s, find him and kill him, he is too clueless to live.",
                    w_obj->short_description);
            return FALSE;
          }
        } else
          skip = FALSE;

        t_obj2 = t_obj2->next_content;

        if (skip)
          obj2 = t_obj2;
      }

      if (count > 1)
        o_f_flag |= O_F_COUNT;
    }

    /* okie, we have all data, let's write it out */

    writeObject(w_obj, o_f_flag, o_u_flag, (ush_int) count, loc);

    if (obj_c)
      w_obj->weight += cont_wgt;
  }

  /* end of a list, need the marker byte */

  if (!loc)
    ADD_BYTE(ibuf, O_F_EOL);

  if (t_obj) {
#ifdef ARTIFACT
    /* this prevents the artifact from being removed from the owner if a save occurs and a temp artifact is
       created -- DDW */
    tagBogusArtifact(t_obj);
#endif
    extract_obj(t_obj);
  }
  return TRUE;
}

/* actually add the data for an object(s) to the buffer */

static void writeObject(P_obj obj, int o_f_flag, ulong o_u_flag, int count, int loc) {
  int i;
  struct extra_descr_data *tmp;

  save_count += count;

  ADD_BYTE(ibuf, o_f_flag);
  ADD_INT(ibuf, obj_index[obj->R_num].virtual);

  if (o_f_flag & O_F_WORN)
    ADD_BYTE(ibuf, loc);

  if (o_f_flag & O_F_COUNT)
    ADD_SHORT(ibuf, (ush_int) count);

  if (o_f_flag & O_F_DECAY) {
    if ((obj->type == ITEM_CORPSE) && (obj->value[1] == PC_CORPSE)) {
      ADD_INT(ibuf, (uint) MAX(event_time(save_event, T_PULSES), MAX_PC_CORPSE_TIME));
      ADD_INT(ibuf, obj->value[4]); /* Save the corpse loot flag dictating decay time */
    } else {
      ADD_INT(ibuf, MAX(1, event_time(save_event, T_PULSES)));
    }
  }
  if (o_u_flag) {
    ADD_INT(ibuf, o_u_flag);

    if (o_u_flag & O_U_KEYS)
      ADD_STRING(ibuf, obj->name);

    if (o_u_flag & O_U_DESC1)
      ADD_STRING(ibuf, obj->description);

    if (o_u_flag & O_U_DESC2)
      ADD_STRING(ibuf, obj->short_description);

    if (o_u_flag & O_U_DESC3)
      ADD_STRING(ibuf, obj->action_description);

    for (i = 0; i < 4; i++)
      if (o_u_flag & (1 << (i + 4)))
        ADD_INT(ibuf, obj->value[i]);

    if (o_u_flag & O_U_TRAP) {
      ADD_SHORT(ibuf, obj->trap_eff);
      ADD_SHORT(ibuf, obj->trap_dam);
      ADD_SHORT(ibuf, obj->trap_charge);
      ADD_SHORT(ibuf, obj->trap_level);
      ADD_SHORT(ibuf, obj->trap_dnum);
      ADD_SHORT(ibuf, obj->trap_dsize);

    }
    if (o_u_flag & O_U_TYPE)
      ADD_BYTE(ibuf, obj->type);

    if (o_u_flag & O_U_WEAR)
      ADD_INT(ibuf, obj->wear_flags);

    if (o_u_flag & O_U_EXTRA)
      ADD_INT(ibuf, obj->extra_flags);

    if (o_u_flag & O_U_ANTI)
      ADD_INT(ibuf, obj->anti_flags);

    if (o_u_flag & O_U_WEIGHT)
      ADD_INT(ibuf, obj->weight);

    if (o_u_flag & O_U_COST)
      ADD_INT(ibuf, obj->cost);

    if (o_u_flag & O_U_DURABILITY)
      ADD_INT(ibuf, obj->durability);

    if (o_u_flag & O_U_SETB)
      for (i = 0; i < AFF_BYTES; i++)
        ADD_BYTE(ibuf, (ubyte) obj->sets_affs[i]);

    if (o_u_flag & O_U_AFFS)
      for (i = 0; i < MAX_OBJ_AFFECT; i++) {
        ADD_BYTE(ibuf, obj->affected[i].location);
        ADD_BYTE(ibuf, obj->affected[i].modifier);
      }
  }
  if (o_f_flag & O_F_SPELLBOOK) {
    if (!(tmp = find_spell_description(obj))) { /* this _SHOULD_ not happen.. but will it? we shall see. */
      /* Well there's one case where it can happen now. */
      if ((tmp = find_totem_description(obj))) {
        ADD_STRING(ibuf, tmp->description);
      } else
        dump_core(); /* this case should have been handled before we got here. JAB */
    } else {
      ADD_BYTE(ibuf, (MAX_SKILLS / 8) + 1);
      for (i = 0; i < (MAX_SKILLS / 8) + 1; i++) {
        ADD_BYTE(ibuf, tmp->description[i]);
      }
    }
  }
}

/* this is a combo of writeCharacter and writeItems, stores the corpse,
   plus its contents in a separate dir to
   allow it to be reloaded in the event of a crash. */

void writeCorpse(P_obj corpse) {
  FILE *f;
  P_obj hold_content = NULL;
  bool bak, del_only = FALSE; /* return after unlinking existing */
  char *buf, *size_off, Gbuf1[MAX_STRING_LENGTH], Gbuf2[MAX_STRING_LENGTH], Gbuf3[MAX_STRING_LENGTH];
  int i_count = 0, t_stamp;
  static char buff[SAV_MAXSIZE * 2];
  struct stat statbuf;
  char test[MAX_STRING_LENGTH];

  if (!corpse || (corpse->type != ITEM_CORPSE) || (corpse->value[1] != PC_CORPSE))
    dump_core();

  /* unless corpse is on the ground, it doesn't get saved, and to prevent duplication, any current file gets
     nuked. JAB */

  if (!OBJ_ROOM(corpse))
    del_only = TRUE;
  else if ((corpse->loc.room <= NOWHERE) || (corpse->loc.room > top_of_world))
    dump_core();

  sprintf(Gbuf1, "%s/Corpses/", SAVE_DIR);

  if (sscanf(corpse->action_description, " %d %s ", &t_stamp, Gbuf2) != 2) {
    strcpy(Gbuf2, corpse->action_description);
  } else {
    strcpy(test, corpse->action_description);
    if (test[10] == ' ')
      strcpy(Gbuf2, (corpse->action_description + 11));
    else
      strcpy(Gbuf2, (corpse->action_description + 10));
  }

  for (buf = Gbuf2; *buf; buf++)
    *buf = LOWER(*buf);

  /* to make certain we save ALL player corpses, even geeks that die alot, we have to give them a serial number of
     sorts.  Since value[3] was unused for player corpses, it makes a handy counter.  This will be slow if they
     have lots and lots of corpses (we have to access disk for each one we check).  JAB */

  if (corpse->value[3] == NOWHERE) {

    /* first save for this corpse, gotta find an unused number, no need to get fancy, we'll just step up from 1,
       trying each in turn. */

    for (corpse->value[3] = 1;; corpse->value[3]++) {
      sprintf(Gbuf3, "%s/%s%d", Gbuf1, Gbuf2, corpse->value[3]);
      if (!stat(Gbuf3, &statbuf))
        continue; /* exists already */
      if (errno == ENOENT)
        break; /* no entry, just what we are looking for */

      logit(LOG_FILE, "Problem with player Corpses directory!\n");
      return;
    }
  }
  sprintf(Gbuf3, "%s/%s%d", Gbuf1, Gbuf2, corpse->value[3]);
  strcpy(Gbuf1, Gbuf3);
  strcat(Gbuf1, ".bak");

  if (stat(Gbuf3, &statbuf) == 0) {
    if (rename(Gbuf3, Gbuf1) == -1) {
      logit(LOG_FILE, "Problem with player Corpses directory!\n");
      return;
    }
    bak = 1;
  } else {
    if (errno != ENOENT) {
      logit(LOG_FILE, "Problem with player Corpses directory!\n");
      return;
    }
    bak = 0;
  }

  if (del_only) {
    if (bak)
      if (unlink(Gbuf1) == -1)
        logit(LOG_FILE, "Couldn't delete backup of Corpse file.\n");
    return;
  }
  f = fopen(Gbuf3, "w");
  if (!f) {
    logit(LOG_FILE, "Couldn't create Corpse save file!\n");
    return;
  }
  buf = buff;
  ADD_BYTE(buf, (char) (short_size));
  ADD_BYTE(buf, (char) (int_size));
  ADD_BYTE(buf, (char) (long_size));

  ADD_INT(buf, world[corpse->loc.room].number); /* reload room (VIRTUAL) */

  size_off = buf; /* needed to make sure it's not corrupt */
  ADD_INT(buf, (int) 0);

  /* have to hold the 'next_content' of corpse, as this is stuff in the room and not to be saved.  Replaced after
     it's saved.  JAB */

  hold_content = corpse->next_content;
  corpse->next_content = NULL;

  i_count = countInven(corpse);

  ADD_BYTE(buf, (char) SAV_ITEMVERS);
  ADD_INT(buf, i_count);

  ibuf = buf;
  save_count = 0;

  writeObjectlist(corpse, (byte) 0);

  corpse->next_content = hold_content;

  if (save_count != i_count)
    dump_core();

  ADD_INT(size_off, (int) (ibuf - buff));

  if (fwrite(buff, 1, (unsigned) (ibuf - buff), f) != (ibuf - buff)) {
    logit(LOG_FILE, "Couldn't write to Corpse save file!\n");
    fclose(f);
    return;
  }
  fclose(f);

  if (bak) {
    if (unlink(Gbuf1) == -1) {
      logit(LOG_FILE, "Couldn't delete backup of player file.\n");
    }
  }
}

int writeItems(char *buf, P_char ch) {
  char *start = buf;
  int count, i;
  int a, b; /* Added for easier debugging via GDB -Torm */

  ibuf = buf;

  ADD_BYTE(ibuf, (char) SAV_ITEMVERS);
  a = countEquip(ch); /* including contents of worn containers */
  b = countInven(ch->carrying);
  count = a + b;
  save_count = 0;

  ADD_INT(ibuf, count); /* total number of items being saved */

  /* writeObjectlist() writes the entire list, with recursive calls to handle containers in the list.  We call it
     for each piece of equip as a list of 1, because it's neater that way. */

  for (i = 0; i < MAX_WEAR; i++)
    if (save_equip[i])
      if (!writeObjectlist(save_equip[i], (byte) (i + 1)))
        return 0;

  if (!writeObjectlist(ch->carrying, (byte) 0))
    return 0;

  if (!(save_count == count)) {
    logit(LOG_EXIT, "assert: save counts don't match");
    dump_core();
  }
  return (int) (ibuf - start);
}

int writeCharacter(P_char ch, int type, int room) {
  FILE *f;
  P_obj obj, obj2;
  char *buf, *skill_off, *affect_off, *item_off, *size_off, *witness_off, *procs_off, *tmp;
#ifdef EVENT_SAVING
  char *event_off;
#endif
  char Gbuf1[MAX_STRING_LENGTH], Gbuf2[MAX_STRING_LENGTH], Gbuf3[MAX_STRING_LENGTH],
          chk_buf[SAV_MAXSIZE * 2];
  int i, bak, tmp_errno, chk_size, write_size;
  static char buff[SAV_MAXSIZE * 2];
  struct affected_type *af;
  struct stat statbuf;

  if (!ch || !GET_NAME(ch) || (IS_NPC(ch) && !IS_MORPH(ch))) {
    send_to_char("pfile error1\r\n", ch);
    return 0;
  }

  if (IS_MORPH(ch))
    ch = MORPH_ORIG(ch);

#ifdef SQL
  sql_update_pc(ch);
#endif

  /* ok, room given to this func, is real room number, to prevent
     mass confusion, xlate to virtual number before
     writing it to file.  JAB */

  room = translateSaveRoom(ch, type, room);

  /* this check assumes that the macros have not
     been fixed up for a different architecture type. */

  /*
  if ((sizeof (char) != 1) || (long_size != int_size)) {
    logit(LOG_DEBUG, "sizeof(char) must be 1 and int_size must == long_size for player saves!\n");
    send_to_char("pfile error2\r\n", ch);
    return 0;
  }
  */ 

  /* in case char reenters game immediately; handle rent/etc correctly */

  if (ch->desc)
    ch->desc->rtype = type;

  buf = buff;
  ADD_BYTE(buf, (char) SAV_SAVEVERS);
  ADD_BYTE(buf, (char) (short_size));
  ADD_BYTE(buf, (char) (int_size));
  ADD_BYTE(buf, (char) (long_size));

  ADD_BYTE(buf, (char) type);

  skill_off = buf;
  ADD_INT(buf, (int) 0);
  affect_off = buf;
  ADD_INT(buf, (int) 0);
  item_off = buf;
  ADD_INT(buf, (int) 0);
#ifdef EVENT_SAVING
  event_off = buf;
  ADD_INT(buf, (int) 0);
#endif
  witness_off = buf;
  ADD_INT(buf, (int) 0);
#ifdef PROC_SAVING
  procs_off = buf;
  ADD_INT(buf, (int) 0);
#endif

  size_off = buf;
  ADD_INT(buf, (int) 0);

  ADD_INT(buf, room); /* starting room (VIRTUAL) */

  ADD_INT(buf, time(0)); /* save time */

  /* unequip everything and remove affects before saving */

  for (i = 0; i < MAX_WEAR; i++)
    if (ch->equipment[i])
      save_equip[i] = unequip_char(ch, i, FALSE);
    else
      save_equip[i] = NULL;

  af = ch->affected;

  all_affects(ch, FALSE); /* reset to unaffected state */

  buf += writeStatus(buf, ch);

  ADD_INT(skill_off, (int) (buf - buff));
  buf += writeSkills(buf, ch);

  ADD_INT(affect_off, (int) (buf - buff));
  buf += writeAffects(buf, ch->affected, type);

  ADD_INT(item_off, (int) (buf - buff));
  if ((type == 2) || (type == 4)) {
    ADD_BYTE(buf, (char) SAV_ITEMVERS);
    ADD_INT(buf, 0); /* no items */
  } else
    buf += writeItems(buf, ch);

#ifdef EVENT_SAVING
  ADD_INT(event_off, (int) (buf - buff));
  buf += writeCharacterEvents(buf, ch);
#endif

  ADD_INT(witness_off, (int) (buf - buff));
  buf += writeWitness(buf, ch->specials.witnessed);

#ifdef PROC_SAVING
  ADD_INT(procs_off, (int) (buf - buff));
  buf += writeCharacterProcs(buf, ch);
#endif

  ADD_INT(size_off, (int) (buf - buff));

  /* if they are staying in game, re-equip them */

  if (type == 1) {
    for (i = 0; i < MAX_WEAR; i++)
      if (save_equip[i])
        equip_char(ch, save_equip[i], i, 1);
  } else {

    /* if not, nuke the equip and inven (it has already been saved) */

    for (i = 0; i < MAX_WEAR; i++)
      if (save_equip[i]) {
#ifdef ARTIFACT
        /* this prevents the artifact from being removed from the owner if a save occurs and a temp artifact is
         * created -- DDW */
        tagBogusArtifact(save_equip[i]);
#endif
        extract_obj(save_equip[i]);
        save_equip[i] = NULL;
      }
    for (obj = ch->carrying; obj; obj = obj2) {
      obj2 = obj->next_content;
#ifdef ARTIFACT
      /* this prevents the artifact from being removed from the owner if a save occurs and a temp artifact is
       * created -- DDW */
      tagBogusArtifact(obj);
#endif
      extract_obj(obj);
      obj = NULL;
    }
  }

  all_affects(ch, TRUE); /* reapply affects (including equip) */

  write_size = (int) (buf - buff);
  if (write_size >= SAV_MAXSIZE) {
    logit(LOG_PLAYER, "Could not save %s, file too large (%d bytes)", GET_NAME(ch), write_size);
    statuslog(GET_LEVEL(ch), "Could not save %s, file too large (%d bytes)", GET_NAME(ch), write_size);
    if (ch->in_room != NOWHERE)
      send_to_char("Too many objects to save, get rid of some junk!\n", ch);
    send_to_char("pfile error3\r\n", ch);
    return 0;
  }
  /* the Eileen clause: illegal strung player names just save to a junk dir */
  if (isalpha(*ch->player.name))
    sprintf(Gbuf1, "%s/%c/", SAVE_DIR, LOWER(*ch->player.name));
  else
    sprintf(Gbuf1, "%s/junk/", SAVE_DIR);

  tmp = Gbuf1 + strlen(Gbuf1);
  strcat(Gbuf1, GET_NAME(ch));

  for (; *tmp; tmp++)
    *tmp = LOWER(*tmp);
  strcpy(Gbuf2, Gbuf1);
  strcat(Gbuf2, ".bak");
  strcpy(Gbuf3, Gbuf1);
  strcat(Gbuf3, ".new");

  /* Gbuf1 now holds the last correctly saved pfile,
     Gbuf2 is where we save a backup of that file,
     Gbuf3 is where we write the new one.
   */

  /* cleanup time, check to see if we have a 'new' file leftover from a previous failed attempt.  If we do,
     just dispose of it. */

  if (!stat(Gbuf3, &statbuf)) {
    /* yup, have a failed earlier save attempt, get rid of it. */
    if (unlink(Gbuf3)) {
      logit(LOG_FILE, "Cannot remove %s.new, suspending saves of this character.", GET_NAME(ch));
      wizlog(51, "&+R&-LPANIC!&N  Cannot update %s's pfile!", GET_NAME(ch));
      send_to_char("pfile error4\r\n", ch);
      return 0;
    }
  }
  if (stat(Gbuf1, &statbuf) == 0) {
    /* pfile exists, let's see about making a backup */
    if (stat(Gbuf2, &statbuf) == 0) {
      /* whups, backup exists too, we be in trouble. */
      logit(LOG_FILE, "%s has both a pfile and a backup, suspending saves of this character.", GET_NAME(ch));
      wizlog(51, "&+R&-LPANIC!&N  %s has both a pfile and a backup!", GET_NAME(ch));
      send_to_char("pfile error5\r\n", ch);
      return 0;
    }
    if (rename(Gbuf1, Gbuf2) == -1) {
      tmp_errno = errno;
      logit(LOG_FILE, "Problem with player save files directory!\n rename failed, errno = %d\n", tmp_errno);
      wizlog(51, "&+R&-LPANIC!&N  Error backing up pfile for %s!", GET_NAME(ch));
      send_to_char("pfile error6\r\n", ch);
      return 0;
    }
    /* ok, we get here, we have a valid backup pfile, NO pfile. */
    bak = 1;
  } else {
    /* no pfile, may be a new char, or it may be a problem, let's see... */
    if (errno == EBADF) {
      /* NOTE: with the stat() function, only two errors are possible:
         EBADF   filedes is bad.
         ENOENT  File does not exist.
         EBADF should only occur using fstat().  Therefore, if I fall into here, I have some SERIOUS problems!  */

      logit(LOG_FILE, "Problem with player save files directory!\nstat failed, errno = %d\n", EBADF);
      wizlog(51, "&+R&-LPANIC!&N  Error finding pfile for %s!", GET_NAME(ch));
      send_to_char("pfile error6\r\n", ch);
      return 0;
    }
    if (stat(Gbuf2, &statbuf) == 0) {
      /* whups, no pfile, but we have a backup.  However, if we get HERE, then something is seriously wrong, or
         someone deleted the pfile but missed the backup. (restoring from a backup pfile is handled when READING a
         pfile, not writing it.  We yell and bomb out. */
      logit(LOG_FILE, "%s has a backup, but no pfile, suspending saves of this character.", GET_NAME(ch));
      wizlog(51, "&+R&-LPANIC!&N  %s has a backup with no pfile!", GET_NAME(ch));
      send_to_char("pfile error7\r\n", ch);
      return 0;
    }
    /* No pfile, no backup, we must be a new character, carry on... */
    bak = 0;
  }

  /* halfway home, if we get here, we have at most, one file (Gbuf2, the .bak file, which is the old pfile renamed.
     Now we open and write our data to Gbuf3 (the .new file).  */

  f = fopen(Gbuf3, "w");

  if (!f) {
    tmp_errno = errno;
    logit(LOG_FILE, "Couldn't create player save file!\nfopen failed, errno = %d\n", tmp_errno);
    wizlog(51, "&+R&-LPANIC!&N  Error creating pfile for %s!", GET_NAME(ch));
    bak -= 2;
  } else {
    if (fwrite(buff, 1, (unsigned) write_size, f) != write_size) {
      tmp_errno = errno;
      logit(LOG_FILE, "Couldn't write to player save file!\nfwrite failed, errno = %d\n", tmp_errno);
      wizlog(51, "&+R&-LPANIC!&N  Error writing pfile for %s!", GET_NAME(ch));
      bak -= 2;
    }
    fclose(f);
  }

  /* the new part, open the just written pfile, read it in, and memcmp what we just read with what we just wrote.
     If they match, off we go..., if not, we maybe have filesystem problems (like out of disk space). */

  if (stat(Gbuf3, &statbuf)
          || (statbuf.st_size >= SAV_MAXSIZE)
          || (statbuf.st_size < 4)
          || !(f = fopen(Gbuf3, "r"))) {
    /* eeek, file we just wrote is giving us problems */
    wizlog(51, "&+R&-LPANIC!&N  Filesystem in chaos, advise immediate shutdown!", GET_NAME(ch));
    if (statbuf.st_size >= SAV_MAXSIZE)
      wizlog(51, "&+RSave file is too large!&N", GET_NAME(ch));
    bak -= 2;
  } else {
    chk_size = fread(chk_buf, 1, SAV_MAXSIZE - 1, f);
    fclose(f);
    if ((chk_size != write_size) || !memcmp(buf, chk_buf, write_size)) {
      /* read and write down't match, something is bad wrong */
      wizlog(51, "&+R&-LPANIC!&N  Cannot write non-corrupt pfile for %s!", GET_NAME(ch));
      bak -= 2;
    }
  }

  /* ok, we have now:
   * written <name>.new with current save data
   * read what we just wrote, and confirmed it's correct.
   * now we check for our status and move either the .bak file, or the .new file into place as the new pfile
   * it's BARELY possible that we can fail AFTER this point (we can't rename either the .bak or .new file), but
   * it's highly unlikely, nevertheless, we check.  JAB */

  switch (bak) {
    case -2: /* save FAILED, and we have NO backup! */
      logit(LOG_FILE, "Unable to save %s, and no backup exists.", GET_NAME(ch));
      wizlog(51, "Unable to save %s, and no backup exists.", GET_NAME(ch));
      send_to_char("pfile error7\r\n", ch);
      return 0;

    case -1: /* save FAILED, but we have a backup */
      if (rename(Gbuf2, Gbuf1) == -1) {
        tmp_errno = errno;
        logit(LOG_FILE, "Unable to restore backup for %s!  rename failed, errno = %d\n", GET_NAME(ch), tmp_errno);
        wizlog(51, "&+R&-LPANIC!&N  Error restoring backup pfile for %s!", GET_NAME(ch));
      } else {
        logit(LOG_FILE, "%s restored from backup.", GET_NAME(ch));
        wizlog(51, "%s restored from backup.", GET_NAME(ch));
      }
      /* restored or not, the save still failed, so return 0 */
      send_to_char("pfile error7\r\n", ch);
      return 0;

    case 1: /* save worked, we rename *.new to *, THEN dispose of *.bak */
    case 0: /* save worked, no *.bak file was made to begin with */
      if (rename(Gbuf3, Gbuf1) == -1) {
        tmp_errno = errno;
        logit(LOG_FILE, "Unable to rename %s to %s, serious problems! (Error %d).", Gbuf3, Gbuf1, tmp_errno);
        wizlog(51, "&+R&-LPANIC!&N  Error saving pfile for %s!", GET_NAME(ch));
        if (bak == 1) {
          if (rename(Gbuf2, Gbuf1) == -1) {
            tmp_errno = errno;
            logit(LOG_FILE, "Also unable to rename %s to %s, VERY serious problems! (Error %d).",
                    Gbuf2, Gbuf1, tmp_errno);
            wizlog(51, "&+R&-LPANIC!&N  Error saving pfile for %s!", GET_NAME(ch));
          } else {
            logit(LOG_FILE, "Backup %s restored instead", Gbuf2);
          }
        }
        send_to_char("pfile error8\r\n", ch);
        return 0;
      } else {
        /* all kosher so far, no we remove the .bak file (if it exists) */
        if ((bak == 1) && unlink(Gbuf2)) {
          /* no rest for the weary, we can't remove the backup (probably owned by root, grrrr) */
          tmp_errno = errno;
          logit(LOG_FILE, "Unable to remove %s, sigh, errno %d", Gbuf2, tmp_errno);
          wizlog(51, "&+R&-LPANIC!&N  Error removing %s, someone go look eh?", Gbuf2);
          /* however, the save worked, so we fall through and return 1 */
        }
      }
      break;

    default:
      dump_core(); /* we screwed up somewhere */
  }

  return 1;
}

/* ok, room given to this func, is real room number, to prevent mass confusion, xlate to virtual number before
   writing it to file.  JAB */

int translateSaveRoom(P_char ch, int type, int room) {

  switch ((room == ch->in_room) ? 1 : (room == ch->specials.was_in_room) ? 2 : 0) {
    case 0:
      /* mystery room, make it NOWHERE */
      room = NOWHERE;
      break;

    case 1:
      /* room to save in is current room */
      if ((ch->in_room > 0) && (ch->in_room <= top_of_world))
        break; /* it's fine */

      room = ch->specials.was_in_room;

      /* change it and drop through to next */

    case 2:
      /* room to save in is 'was_in_room' */
      if ((room > 0) && (room <= top_of_world))
        break; /* it's fine */
      else if ((ch->in_room > 0) && (ch->in_room <= top_of_world)) {
        room = ch->in_room; /* switch to in_room */
        break;
      }
      break;
  }

  if (room == NOWHERE)
    room = real_room(GET_BIRTHPLACE(ch) ? GET_BIRTHPLACE(ch) : GET_HOME(ch));
  /* GET_HOME(ch) is now last rent spot */

  /* special case, 4 is death, no items, and they come back in birthplace */
  if (type == 4) {
    room = real_room(GET_BIRTHPLACE(ch));
    GET_HOME(ch) = GET_BIRTHPLACE(ch);
  }
  /* ok, unless I REALLY screwed up, at this point we have a valid 'real' room number to save in, or
     room == NOWHERE.  If it's real, we have to convert it to virtual number to save it properly.  JAB */

  if (room != NOWHERE)
    room = world[room].number;

  return room;
}

int deleteCharacter(P_char ch) {
  char *tmp, Gbuf1[MAX_STRING_LENGTH], Gbuf2[MAX_STRING_LENGTH];

  if (!ch || IS_NPC(ch))
    dump_core();

  sprintf(Gbuf1, "%s/%c/", SAVE_DIR, LOWER(*ch->player.name));
  tmp = Gbuf1 + strlen(Gbuf1);
  strcat(Gbuf1, GET_NAME(ch));
  for (; *tmp; tmp++)
    *tmp = LOWER(*tmp);
  strcpy(Gbuf2, Gbuf1);
  strcat(Gbuf2, ".bak");
  unlink(Gbuf1);
  unlink(Gbuf2);

  return TRUE;
}

/* following are functions that read data from disk (or process data read from disk).  JAB */

ush_int getShort(char **buf) {
  ush_int s;

  bcopy(*buf, &s, short_size);

  s = ntohs((ush_int) s);
  *buf += short_size;

  return s;
}

uint getInt(char **buf) {
  uint i;

  bcopy(*buf, &i, int_size);

  i = ntohl(i);
  *buf += int_size;

  return i;
}

float getFloat(char **buf) {
  float i;

  bcopy(*buf, &i, float_size);

  *buf += float_size;

  return i;
}

char *getString(char **buf) {
  int len;
  char *s;

  len = (int) GET_SHORT(*buf);
  if (len == 0)
    return 0;
  else {
#ifdef MEM_DEBUG
    mem_use[MEM_STRINGS] += len + 1;
#endif
    CREATE(s, char, (unsigned) (len + 1));

    strncpy(s, *buf, (unsigned) len);
    s[len] = 0;
    *buf += len;
  }
  return s;
}

static int restoreStatus(char *buf, P_char ch) {
  byte dummy_byte;
  char *start = buf, *str, *bah;
  int l_tmp, tmp, tmp2, j, tmpb, t_count, exp_hold, omg, i;
  struct Trophy_data *tmp_trophy;
  struct disease *dis;
  ubyte t_ub, savedGrantBytes, savedDebugBytes, savedLawFlagBytes;
  uint utmp;
  unsigned short s;
  int spell_read = 0;
#if 0
  struct savable_char_exec_data *sced;
#endif
  int add_mana = 0, count = 0;


  stat_vers = GET_BYTE(buf);
  if (stat_vers > (char) SAV_STATVERS) {
    logit(LOG_FILE, "Save file for %s status restore failed.", GET_NAME(ch));
    send_to_char("Your character file is in a format which the game doesn't know how\n"
            "to load. Please log on with another character and talk to a Forger.\n", ch);
    return 0;
  }
  /* restorePasswdOnly allocs name, so we have to free it to avoid leaking.  JAB */
  if (ch->player.name)
    free_string(ch->player.name);

  if (IS_NPC(ch)) {
    if (stat_vers < 33)
      tmp = GET_SHORT(buf);
    else
      tmp = GET_INTE(buf);
    ch->nr = real_mobile(tmp);
    /* uhh ohh bad.. cant find a mobile with this vnum */
    if (ch->nr == -1) {
      wizlog(51, "restoreStatus() - trying to restore a pet [vnum %d] that has been removed from world.mob file.", tmp);
      return 0;
    }
  }

  GET_NAME(ch) = GET_STRING(buf);
  ch->player.name[0] = (char) toupper(ch->player.name[0]);

  if (IS_PC(ch)) {
    if (stat_vers > 3)
      ch->only.pc->screen_length = (ubyte) GET_BYTE(buf);
    else
      ch->only.pc->screen_length = 24;
    str = GET_STRING(buf);
    if (!str) {
      send_to_char("How did you manage to nullify your password!??\nPlease contact a Forger!\n", ch);
      logit(LOG_FILE, "%s somehow managed to clear out his/her password field!", GET_NAME(ch));
      return 0;
    }
    strcpy(ch->only.pc->pwd, str);
    free_string(str);
    str = NULL;
  }
  ch->player.short_descr = GET_STRING(buf);
  ch->player.long_descr = GET_STRING(buf);
  ch->player.description = GET_STRING(buf);

  if (IS_PC(ch))
    GET_TITLE(ch) = GET_STRING(buf);
  if ((stat_vers > 25) && IS_PC(ch))
    GET_LAST_LOGIN(ch) = GET_STRING(buf);
  GET_CLASS(ch) = GET_BYTE(buf);
  if (IS_PC(ch)) {
    if (stat_vers > 23)
      GET_CLASS_CHOICE(ch) = (sh_int) GET_SHORT(buf);
    else
      GET_CLASS_CHOICE(ch) = 0;
  }
  GET_RACE(ch) = GET_BYTE(buf);
  GET_LEVEL(ch) = GET_BYTE(buf);

  if (GET_LEVEL(ch) > (MAXLVL + 1))
    GET_LEVEL(ch) = 1;

  GET_SEX(ch) = GET_BYTE(buf);

  if (stat_vers < 6) {
    tmpb = GET_BYTE(buf);
    tmpb = GET_BYTE(buf);
    set_char_size(ch);
  } else if (stat_vers < 21) {
    ch->points.base_weight = GET_SHORT(buf);
    ch->points.base_height = GET_SHORT(buf);
    set_char_size(ch);
  } else {
    ch->points.base_weight = GET_INTE(buf);
    ch->points.base_height = GET_INTE(buf);
  }
  if (stat_vers < 11)
    set_char_size(ch);

  GET_HOME(ch) = GET_INTE(buf);
  GET_BIRTHPLACE(ch) = 0;

  if (stat_vers > 8) {
    GET_BIRTHPLACE(ch) = GET_INTE(buf);
    if ((GET_RACE(ch) == RACE_GNOME) && (stat_vers < 14))
      GET_BIRTHPLACE(ch) = -1;
  }
  if (GET_BIRTHPLACE(ch) < 1) {
    tmp = find_hometown(ch);
    tmp2 = GET_HOME(ch);
    find_starting_location(ch, (tmp == HOME_CHOICE) ? HOME_WATERDEEP : tmp);
    GET_BIRTHPLACE(ch) = GET_HOME(ch);
    GET_HOME(ch) = tmp2;
    if (GET_BIRTHPLACE(ch) == NOWHERE)
      GET_BIRTHPLACE(ch) = 3001;
  }
  if (stat_vers >= 30)
    ch->psn = GET_INTE(buf);

  ch->player.time.birth = GET_INTE(buf);
  ch->player.time.played = GET_INTE(buf);
  ch->player.time.saved = GET_INTE(buf); /* last save time */
  ch->player.time.logon = time(0); /* set it */

  /* ok, due to a little system timing accident (it was suddenly 2036) play times got hosed, this should
     fix them (sorta). JAB */

  if (stat_vers == 18) {
    if ((uint) ch->player.time.played > BIT_31) {
      if ((uint) ch->player.time.played > BIT_32)
        ch->player.time.played = ch->player.time.played + 1259712000;
      else
        ch->player.time.played = ch->player.time.played - 1259712000;
    }
  }
  if (stat_vers > 14)
    ch->player.time.perm_aging = GET_SHORT(buf);

  /* OLD trophy system, data is incompatible, and is therefore junked. JAB */

  if (stat_vers < 16) {
    if (stat_vers > 9) {
      s = GET_BYTE(buf);
      tmpb = GET_BYTE(buf);
      for (tmp = 0; tmp < s; tmp++) {
        tmp2 = GET_INTE(buf); /* junk */
        tmp2 = GET_BYTE(buf); /* junk */
      }
    } else if (stat_vers > 7) {
      s = GET_BYTE(buf);
      tmpb = GET_BYTE(buf);
      for (tmp = 0; tmp < s; tmp++) {
        j = GET_INTE(buf);
      }
    }
  }

  if (IS_PC(ch)) {
    s = GET_SHORT(buf); /* number of tongues */
    for (tmp = 0; tmp < MAX(s, MAX_TONGUE); tmp++)
      if ((tmp < s) && (tmp < MAX_TONGUE)) {
        /* valid language number was stored */
        GET_LANGUAGE(ch, tmp) = GET_BYTE(buf);
      } else {
        if (tmp < s) {
          /* MAX_TONGUE is smaller than saved version, so read and discard the extra bytes */
          dummy_byte = GET_BYTE(buf);
        } else {
          /* number of languages has grown, make sure new ones are 0% */
          GET_LANGUAGE(ch, tmp) = 0;
        }
      }
  }
  if (stat_vers > 12) {
    ch->base_stats.Str = (ubyte) GET_BYTE(buf);
    ch->base_stats.Dex = (ubyte) GET_BYTE(buf);
    ch->base_stats.Agi = (ubyte) GET_BYTE(buf);
    ch->base_stats.Con = (ubyte) GET_BYTE(buf);
    ch->base_stats.Pow = (ubyte) GET_BYTE(buf);
    ch->base_stats.Int = (ubyte) GET_BYTE(buf);
    ch->base_stats.Wis = (ubyte) GET_BYTE(buf);
    ch->base_stats.Cha = (ubyte) GET_BYTE(buf);
    ch->base_stats.Karma = (ubyte) GET_BYTE(buf);
    ch->base_stats.Luck = (ubyte) GET_BYTE(buf);
    ch->curr_stats = ch->base_stats;
  } else {
    GET_C_STR(ch) = GET_BYTE(buf);
    GET_C_AGI(ch) = GET_BYTE(buf);
    GET_C_INT(ch) = GET_BYTE(buf);
    GET_C_WIS(ch) = GET_BYTE(buf);
    GET_C_DEX(ch) = GET_BYTE(buf);
    GET_C_CON(ch) = GET_BYTE(buf);
    reroll_basic_abilities(ch); /* sorts new ones to be same order as old */
  }

  GET_MANA(ch) = GET_SHORT(buf);
  ch->points.base_mana = GET_SHORT(buf);
  GET_HIT(ch) = GET_SHORT(buf);
  if (GET_HIT(ch) < 0)
    GET_HIT(ch) = 0;
  if (IS_PC(ch)) {
    if (stat_vers < 7)
      ch->only.pc->spells_memmed[MAX_CIRCLE] = 0;
    else
      ch->only.pc->spells_memmed[MAX_CIRCLE] = GET_BYTE(buf);
  }
  ch->points.base_hit = GET_SHORT(buf);
  if (ch->points.base_hit < 1)
    ch->points.base_hit = 1;
  if (stat_vers < 44) {
    if ((GET_CLASS(ch) == CLASS_THIEF) || (GET_CLASS(ch) == CLASS_ASSASSIN)) {
      recalcRogueHits(ch);
      send_to_char("As part of the rogue upgrades, your hitpoints have been "
              "rerolled.  Have a nice day.\n", ch);
    }
  }

  GET_MOVE(ch) = GET_SHORT(buf);
  ch->points.base_move = GET_SHORT(buf);

  if (stat_vers < 13)
    recalc_base_hits(ch);

  if (stat_vers < 11) {
    tmp = (int) GET_SHORT(buf); /* ignore armor   */
    dummy_byte = GET_BYTE(buf); /* ignore hitroll */
    dummy_byte = GET_BYTE(buf); /* ignore damroll */
  }
  GET_COPPER(ch) = GET_INTE(buf);
  GET_SILVER(ch) = GET_INTE(buf);
  GET_GOLD(ch) = GET_INTE(buf);
  GET_PLATINUM(ch) = GET_INTE(buf);

  if (stat_vers < 15) {
    GET_EXP(ch) = GET_INTE(buf);
    ch->points.max_exp = GET_EXP(ch);
  } else {
    GET_EXP(ch) = GET_INTE(buf);
    ch->points.max_exp = GET_INTE(buf);
  }


  SET_POS(ch, POS_STANDING + STAT_NORMAL);
  if (IS_NPC(ch))
    SET_DEFAULT_POS(ch, POS_STANDING + STAT_NORMAL);
  if (stat_vers < 11) {
    dummy_byte = GET_BYTE(buf); /* ignore saved position */
    dummy_byte = GET_BYTE(buf); /* ignore saved default_pos */
  }
  if (IS_PC(ch)) {
    t_ub = (ubyte) GET_BYTE(buf);
    for (tmp = 0; tmp < t_ub; tmp++)
      if (tmp < PACT_BYTES)
        ch->only.pc->pcact[tmp] = (ubyte) GET_BYTE(buf);
      else
        utmp = GET_BYTE(buf);

  } else {

    if (stat_vers > 39) {
      /* Restore NPCACT cbits */
      t_ub = (ubyte) GET_BYTE(buf);
      for (tmp = 0; tmp < t_ub; tmp++)
        if (tmp < NPCACT_BYTES)
          ch->only.npc->npcact[tmp] = (ubyte) GET_BYTE(buf);
        else
          utmp = GET_BYTE(buf);
    } else {

      utmp = (uint) GET_INTE(buf);
      for (i = 0; i < 32; i++)
        if (IS_SET(utmp, 1U << i))
          SET_CBIT(ch->only.npc->npcact, i + 1);
    }
  }

  ch->specials.alignment = GET_INTE(buf);

  if (IS_PC(ch) && stat_vers == 41) {
    CLEAR_CBITS(ch->only.pc->asc_bits, ASCM_BYTES);
    ch->only.pc->asc_rank = 0;
    ch->only.pc->asc_num = 0;
    tmp = (int) GET_SHORT(buf); /* Junk this stuff */
    tmp = (int) GET_INTE(buf);
    tmp = (int) GET_INTE(buf);
    dummy_byte = GET_BYTE(buf);
  } else if (IS_PC(ch) && stat_vers > 41) {
    ch->only.pc->asc_num = GET_SHORT(buf);
    ch->only.pc->asc_rank = GET_INTE(buf);
    GET_TIME_LEFT_GUILD(ch) = GET_INTE(buf);
    GET_NB_LEFT_GUILD(ch) = GET_BYTE(buf);

    /* Restore assoc cbits */
    t_ub = (ubyte) GET_BYTE(buf);
    for (tmp = 0; tmp < t_ub; tmp++)
      if (tmp < ASCM_BYTES)
        ch->only.pc->asc_bits[tmp] = (ubyte) GET_BYTE(buf);
      else
        utmp = GET_BYTE(buf);
  } else if (IS_PC(ch)) {
    CLEAR_CBITS(ch->only.pc->asc_bits, ASCM_BYTES);
    ch->only.pc->asc_rank = 0;
    ch->only.pc->asc_num = 0;
  }

  if (IS_PC(ch) && stat_vers < 45) {
    ch->only.pc->prestige = GET_B_CHA(ch) * cc_prestige_charismaBonus;
  } else if (IS_PC(ch) && stat_vers >= 45) {
    ch->only.pc->prestige = GET_INTE(buf);
  }
  if (GET_EXP(ch) <= 1)
    ch->only.pc->prestige = GET_B_CHA(ch) * cc_prestige_charismaBonus;

  for (tmp = 0; tmp < 3; tmp++) {
    ch->specials.conditions[tmp] = GET_BYTE(buf);
    if ((ch->specials.conditions[tmp] < 0) && !IS_TRUSTED(ch))
      ch->specials.conditions[tmp] = 0;
  }

  if (IS_PC(ch)) {
    ch->only.pc->poofIn = GET_STRING(buf);
    ch->only.pc->poofOut = GET_STRING(buf);
  }
  if ((stat_vers < 11) && IS_PC(ch)) { /* was saving throws, which are not saved now */
    dummy_byte = GET_BYTE(buf);
    for (tmp = 0; tmp < 5; tmp++)
      tmpb = GET_SHORT(buf);
  }
  if (IS_PC(ch)) {
    ch->only.pc->echo_toggle = GET_BYTE(buf);
    ch->only.pc->prompt = GET_SHORT(buf);
  }
  /* redid prompt, so reset to 'all' */

  if (IS_PC(ch)) {
    if (stat_vers < 5)
      ch->only.pc->prompt = (1 << 14) - PROMPT_NONE - PROMPT_VIS;
    else if (stat_vers < 11)
      ch->only.pc->prompt <<= 1;
  }
#ifndef PCS_USE_MANA
  if (IS_PC(ch)) {
    REMOVE_BIT(ch->only.pc->prompt, BIT_4); /* mana and maxmana in prompt */
    REMOVE_BIT(ch->only.pc->prompt, BIT_5);
  }
#endif
  if (IS_PC(ch)) {
    ch->only.pc->wiz_invis = GET_INTE(buf);
    if (stat_vers > 10) {
      ch->only.pc->law_flags = (uint) GET_INTE(buf);
      if (stat_vers < 18)
        ch->only.pc->law_flags = init_law_flags(ch); /* using the new system of law flags */
      if (stat_vers < 43) {
        for (tmp = 0; tmp < 16; tmp++)
          ch->only.pc->law_flag[tmp] = ((ch->only.pc->law_flags >> (((tmp) - 1) * 2)) & 3);
      } else {
        savedLawFlagBytes = (ubyte) GET_BYTE(buf);
        for (tmp = 0; tmp < savedLawFlagBytes; tmp++)
          ch->only.pc->law_flag[tmp] = (ubyte) GET_BYTE(buf);
      }
    } else {
      ch->only.pc->law_flags = init_law_flags(ch);
      for (tmp = 0; tmp < 16; tmp++)
        ch->only.pc->law_flag[tmp] = ((ch->only.pc->law_flags >> (((tmp) - 1) * 2)) & 3);

      /* have to xlate the 7 flags used to be pflags and are now only.pc->pcact */
      l_tmp = GET_INTE(buf);
      if (l_tmp & BIT_5)
        SET_CBIT(ch->only.pc->pcact, PLR_SILENCE);
      if (l_tmp & BIT_8)
        SET_CBIT(ch->only.pc->pcact, PLR_FROZEN);
      if (l_tmp & BIT_11)
        SET_CBIT(ch->only.pc->pcact, PLR_NO_KILL);
    }
  }

  if (IS_PC(ch)) {
    ch->only.pc->wimpy = GET_SHORT(buf);
    ch->only.pc->aggressive = GET_SHORT(buf);
    if (stat_vers > 38)
      ch->only.pc->time_judge = GET_SHORT(buf);
    else {
      ch->only.pc->time_judge = 0;
#ifdef NEWJUSTICE
      set_town_flag_justice(ch, TRUE);
#endif
    }
    GET_BALANCE_COPPER(ch) = GET_INTE(buf);
    GET_BALANCE_SILVER(ch) = GET_INTE(buf);
    GET_BALANCE_GOLD(ch) = GET_INTE(buf);
    GET_BALANCE_PLATINUM(ch) = GET_INTE(buf);
  }

  if (IS_PC(ch)) {
    if (stat_vers < 31) { /* restore for pre cbit grant system  - Alth Dec 98 */
      tmp = (uint) GET_INTE(buf);
      for (j = 0; j < 32; j++) {
        if (tmp & (1 << j))
          SET_CBIT(ch->only.pc->grant, j + 1);
      }
      tmp = (uint) GET_INTE(buf);
      for (j = 0; j < 32; j++) {
        if (tmp & (1 << j))
          SET_CBIT(ch->only.pc->grant, j + 1 + 32);
      }
    } else {
      savedGrantBytes = (ubyte) GET_BYTE(buf);
      for (tmp = 0; tmp < savedGrantBytes; tmp++)
        ch->only.pc->grant[tmp] = (ubyte) GET_BYTE(buf);
    }
  }

  if (IS_PC(ch)) {
    if (stat_vers < 32) {
      CLEAR_CBITS(ch->only.pc->debug, DEBUG_BYTES);
    } else {
      savedDebugBytes = (ubyte) GET_BYTE(buf);
      for (tmp = 0; tmp < savedDebugBytes; tmp++)
        ch->only.pc->debug[tmp] = (ubyte) GET_BYTE(buf);
    }
  }

  ch->specials.carry_weight = 0;
  ch->specials.carry_items = 0;

  ch->points.max_hit = hit_limit(ch);
  ch->points.max_mana = mana_limit(ch);
  ch->points.max_move = move_limit(ch);

  if ((stat_vers > 21) && IS_PC(ch)) {
    t_count = GET_SHORT(buf);
    exp_hold = MAX(1, (GET_EXP(ch) / 100));
    ch->only.pc->Trophies = NULL; /* paranoia */
    for (; t_count; t_count--) {
      CREATE(tmp_trophy, struct Trophy_data, 1);
      tmp_trophy->next = NULL;
      tmp_trophy->Vnum = GET_INTE(buf);
      tmp_trophy->XP = (uint) GET_INTE(buf);
      if ((tmp_trophy->Vnum < 1) || (tmp_trophy->Vnum > 99999) || (tmp_trophy == 0) ||
              (tmp_trophy->XP > BIT_31) || (tmp_trophy->XP == 0) || ((GET_EXP(ch) / tmp_trophy->XP) > 100000)) {
        /* clear the ones that are bogus or too wimpy */
        free((char *) tmp_trophy);
        continue;
      }
      tmp_trophy->next = ch->only.pc->Trophies;
      ch->only.pc->Trophies = tmp_trophy;
    }
  }
  if ((ch->points.base_height == 0) || (ch->points.base_weight == 0))
    set_char_size(ch);

  /* restore diseases */
  if ((stat_vers > 45) && IS_PC(ch)) {
    t_count = GET_SHORT(buf);
    ch->only.pc->diseases = NULL;
    for (; t_count; t_count--) {
      dis = cloneDiseaseFromTemplate(GET_INTE(buf));
      if (dis) {
        dis->stage = GET_INTE(buf);
        dis->next = ch->only.pc->diseases;
        ch->only.pc->diseases = dis;
      }
    }
  }

  if (IS_PC(ch)) {
    if ((stat_vers > 33) && (stat_vers < 37)) {
      bah = GET_STRING(buf);
      bah = GET_STRING(buf);
      bah = GET_STRING(buf);
      bah = GET_STRING(buf);
      omg = GET_INTE(buf);
      omg = GET_INTE(buf);
    }
  }

  if (IS_PC(ch)) {
    if ((stat_vers > 46)) /* 47 adds this  - Iyachtu */
      ch->only.pc->condensed_flags = GET_BYTE(buf);
    else ch->only.pc->condensed_flags = 1; /* sets to current melee cond - Iyachtu */
  }

#ifdef NEW_BARD
  ch->bard_singing = NULL;
  ch->song_singing = NULL;
  ch->accompany = NULL;
#endif

  if (IS_PC(ch)) {
    if (stat_vers > 47) {
      for (spell_read = 0; spell_read < 12; spell_read++)
        ch->only.pc->available_spells[spell_read] = (ubyte) GET_BYTE(buf);
    } else {
      for (spell_read = 0; spell_read < 12; spell_read++)
        ch->only.pc->available_spells[spell_read] = 0;
#if 0
      if (GET_CLASS(ch) == CLASS_BARD || GET_CLASS(ch) == CLASS_BATTLECHANTER) {
        sced = calloc(1, sizeof (struct savable_char_exec_data));
        sced->t_arg = calloc(5, 1);
        sced->t_arg[0] = 5;
        sced->t_arg_size = 5;
        sced->t_func = refresh_bard_spells;
        AddEvent(EVENT_SCHAR_EXECUTE, 5760, TRUE, ch, sced);
      }
#endif
    }
    if (stat_vers > 48) {
      ch->only.pc->manaburn = GET_INTE(buf);
    } else
      ch->only.pc->manaburn = 0;
  }

  if (stat_vers < 49 && (GET_CLASS(ch) == CLASS_BARD || GET_CLASS(ch) == CLASS_BATTLECHANTER)) {
    if (GET_CLASS(ch) == CLASS_BARD) {
      if (GET_B_INT(ch) < 80)
        GET_B_INT(ch) = 80;
    } else if (GET_CLASS(ch) == CLASS_BATTLECHANTER) {
      if (GET_B_WIS(ch) < 80)
        GET_B_WIS(ch) = 80;
    }
    add_mana = STAT_INDEX(GET_B_CHA(ch) * stat_factor[GET_RACE(ch)].Cha / 100) * 5;
    for (count = 1; (count <= GET_LEVEL(ch)); count++)
      add_mana = add_mana + 5 + pow_app[STAT_INDEX(GET_B_CHA(ch) * stat_factor[GET_RACE(ch)].Cha / 100)].bonus;
    all_affects(ch, FALSE);
    ch->points.base_mana = MAX(0, add_mana);
    ch->points.base_mana = MIN(ch->points.base_mana, racial_data[(int) GET_RACE(ch)].max_mana);
    all_affects(ch, TRUE);
  }

  return (int) (buf - start);
}

static int restoreAffects(char *buf, P_char ch) {
  char *start = buf;
  int tmp, utmp;
  short count;
  struct affected_type af;

  if ((aff_vers = GET_BYTE(buf)) > (char) SAV_AFFVERS) {
    logit(LOG_FILE, "Save file for %s affects restore failed.", GET_NAME(ch));
    send_to_char("Your character file is in a format which the game doesn't know how\n"
            "to load. Please log on with another character and talk to a Forger.\n", ch);
    return 0;
  }
  if (aff_vers > 5)
    saved_aff_bytes = GET_BYTE(buf);

  count = GET_SHORT(buf);
  for (; count > 0; count--) {
    bzero(&af, sizeof (af));
    if (aff_vers > 4)
      af.type = GET_SHORT(buf);
    else
      af.type = (unsigned char) GET_BYTE(buf);

    af.duration = GET_SHORT(buf);
    af.modifier = GET_SHORT(buf);
    af.location = GET_BYTE(buf);
    if (aff_vers < 6) {
      /* old ones are 32 bit uints */
      utmp = GET_INTE(buf);
      for (tmp = 0; tmp < 32; tmp++)
        if (IS_SET(utmp, 1U << tmp))
          SET_CBIT(af.sets_affs, tmp + 1);
      utmp = GET_INTE(buf);
      for (tmp = 32; tmp < 64; tmp++)
        if (IS_SET(utmp, 1U << (tmp - 32)))
          SET_CBIT(af.sets_affs, tmp + 1);
    } else {
      for (tmp = 0; tmp < saved_aff_bytes; tmp++)
        if (tmp < AFF_BYTES)
          af.sets_affs[tmp] = (ubyte) GET_BYTE(buf);
        else
          utmp = GET_BYTE(buf);
      if (aff_vers < 9) {
        /* read the new affects2 */
        for (tmp = 0; tmp < saved_aff_bytes; tmp++) {
          utmp = GET_BYTE(buf);
        }
      }
    }

    /* changed the hell out of AFF and AFF2 flags, much simpler to just nuke saved affects, than try to
       compensate. JAB */

    if ((aff_vers > 3) && (af.type != SPELL_CASTER_WATER_EMBODIMENT) &&
            (af.type != SPELL_CASTER_FIRE_EMBODIMENT) && (af.type != SPELL_CASTER_EARTH_EMBODIMENT) &&
            (af.type != SPELL_CASTER_AIR_EMBODIMENT) && (af.type != SPELL_ELEMENTAL_WATER) &&
            (af.type != SPELL_ELEMENTAL_FIRE) && (af.type != SPELL_ELEMENTAL_EARTH) &&
            (af.type != SPELL_ELEMENTAL_AIR) && (af.type != SPELL_TIME_STOP))
      affect_to_char(ch, &af);
  }

  if (ch->in_room != NOWHERE)
    affect_total(ch);

  /* ok, some innate powers just set bits, so we need to reset those */

  if (racial_traits[GET_RACE(ch)].infravision)
    SET_CBIT(ch->specials.affects, AFF_INFRAVISION);
  if ((racial_traits[GET_RACE(ch)].ultravision) && (GET_CLASS(ch) != CLASS_LICH))
    SET_CBIT(ch->specials.affects, AFF_ULTRAVISION);
  if (HAS_INNATE(ch, INNATE_INFRAVISION))
    SET_CBIT(ch->specials.affects, AFF_INFRAVISION);
  if (HAS_INNATE(ch, INNATE_ANTI_GOOD)) {
    SET_CBIT(ch->specials.affects, AFF_PROTECT_GOOD);
    SET_CBIT(ch->specials.affects, AFF_DETECT_GOOD);
  }
  if (HAS_INNATE(ch, INNATE_ANTI_EVIL)) {
    SET_CBIT(ch->specials.affects, AFF_PROTECT_EVIL);
    SET_CBIT(ch->specials.affects, AFF_DETECT_EVIL);
  }
  return (int) (buf - start);
}

static int restoreSkills(char *buf, P_char ch) {
  byte dummy_byte;
  char *start = buf;
  int i, n, l_p, maxnum = MAX_SKILLS;

  skill_vers = GET_BYTE(buf);
  if (skill_vers > (char) SAV_SKILLVERS) {
    logit(LOG_FILE, "Save file for %s skills restore failed.", GET_NAME(ch));
    send_to_char("Your character file is in a format which the game doesn't know how\n"
            "to load. Please log on with another character and talk to a Forger.\n", ch);
    return 0;
  }
  /* Allow memorized spells and skill usages to be saved. -DCL    */
  /* version 1 - save spell learned only                          */
  /* version 2 - save spell learned, memorized, and skill usages. */
  /* version 3 - no change, except to nuke _all_ previous proficiencies.
     this is done due to change to learn/pay prac system. */
  /* version 4 - restore spells being memorized too! */
  /* version 6 - changed number of skill saved from 256 to 384  -MK */
  /* version 7 - skill number changed from ubyte to ush_int - MK */

  n = (int) GET_SHORT(buf); /* the actual MAX_SKILLS at the time of saving, so this is what we restore. */

  if (n > maxnum) {
    logit(LOG_FILE, "Not all %s skills could be loaded.", GET_NAME(ch));
    send_to_char("Not all your skills could be loaded. Please report this to a Forger.\n", ch);
  }
  /* this handles the normal restore, as well as the case of stored MAX_SKILLS being greater than the current
     MAX_SKILLS (unlikely) */
  for (i = 0; i < n; i++) {
    if (i >= maxnum) {
      dummy_byte = GET_BYTE(buf);
      if (skill_vers != 1)
        dummy_byte = GET_BYTE(buf);
    } else {
      ch->only.pc->skills[i].learned = GET_BYTE(buf);
      if (skill_vers < 3)
        ch->only.pc->skills[i].learned = 0;
      if (skill_vers == 1)
        ch->only.pc->skills[i].memorized = 0;
      else
        ch->only.pc->skills[i].memorized = GET_BYTE(buf);
    }
  }

  /* if current MAX_SKILLS is greater than the saved MAX_SKILLS, we zero out the extras */
  if (n < maxnum)
    for (i = n; i < maxnum; i++) {
      ch->only.pc->skills[i].learned = 0;
      ch->only.pc->skills[i].memorized = 0;
    }
  /* ok, the skills have been read in, to make things prettier, we loop again if we want to massage the
     skill numbers.  JAB */

  for (i = 0; i < maxnum; i++) {
    if (!IS_TRUSTED(ch) && (ch->only.pc->skills[i].learned > 0)) {
      if (pindex2Skill[i] == -1)
        logit(LOG_FILE, "Nuking non-existent skill #%d from %s (was %d)",
              i, GET_NAME(ch), ch->only.pc->skills[i].learned);
      else if ((skills[pindex2Skill[i]].class[GET_CLASS(ch) - 1].maxlearn <= 0) ||
              ((skills[pindex2Skill[i]].class[GET_CLASS(ch) - 1].rlevel > GET_LEVEL(ch) && skills[pindex2Skill[i]].class[GET_CLASS(ch) - 1].base_skill != SKILL_BASE_SPECIAL)))
        logit(LOG_FILE, "Nuking %s from %s (was %d)",
              skills[pindex2Skill[i]].name, GET_NAME(ch), ch->only.pc->skills[i].learned);
      else
        continue;
      ch->only.pc->skills[i].learned = 0;
      ch->only.pc->skills[i].memorized = 0;
    }
    /* fix the clerics and shaman's that got messed up */
    if (((GET_CLASS(ch) == CLASS_CLERIC) || (GET_CLASS(ch) == CLASS_SHAMAN)) &&
            (i == SKILL_2H_BLUDGEON) && (ch->only.pc->skills[i].learned == 0))
      ch->only.pc->skills[i].learned = CharMaxSkill(ch, pindex2Skill[i]);
  }

  if (skill_vers < 3)
    NewbySkillSet(ch);

  recount_spells_in_use(ch);

  if (skill_vers == 4) {
    do {
      n = GET_BYTE(buf);
      if ((n > 0) && (n < 255))
        if (!((pindex2Skill[n] >= 0) &&
                (!IS_SPELL(pindex2Skill[n]) || (GetSpellCircle(ch, pindex2Skill[n]) > GetMaxCircle_char(ch)))))
          add_new_memorize_spell(ch, n);
    } while (n != 0);
  } else if (skill_vers > 4) {
    l_p = (int) GET_BYTE(buf);
    for (; l_p > 0; l_p--) {
      if (skill_vers < 7)
        n = (int) GET_BYTE(buf);
      else
        n = (int) GET_SHORT(buf);
      if ((n < 0) || (n >= maxnum)) {
        logit(LOG_FILE, "Invalid spell (%d) in %s's memorization list", n, GET_NAME(ch));
        continue;
      }
      if ((pindex2Skill[n] >= 0) && IS_SPELL(pindex2Skill[n]) &&
              (GetSpellCircle(ch, pindex2Skill[n]) <= GetMaxCircle_char(ch)))
        add_new_memorize_spell(ch, n);
    }
  }
  if (skill_vers > 1) {
    n = (int) GET_SHORT(buf);
    for (i = 0; i < MAX(n, MAX_TIMED_USAGES); i++) {
      if ((i < MAX_TIMED_USAGES) && (i < n)) {
        ch->only.pc->timed_usages[i].time_of_first_use = GET_INTE(buf);
        ch->only.pc->timed_usages[i].times_used = GET_BYTE(buf);
      } else {
        if (i < MAX_TIMED_USAGES) {
          ch->only.pc->timed_usages[i].time_of_first_use = 0;
          ch->only.pc->timed_usages[i].times_used = 0;
        }
        if (i < n) {
          GET_INTE(buf);
          dummy_byte = GET_BYTE(buf);
        }
      }
    }
  }
  return (int) (buf - start);
}

/*
 * -1 = char doesn't exist, -2 = problem reading file, other = save type
 */

int restorePasswdOnly(P_char ch, char *name) {
  FILE *f = NULL;
  char buff[SAV_MAXSIZE], *str, *buf;
  int size, csize, type, room, r_val = 0, sav_vers;

  if (!name || !ch)
    return 0;

  r_val = OpenPfile(ch, name, &f);
  if ((r_val < 0) || !f)
    return r_val;

  buf = buff;
  size = fread(buf, 1, SAV_MAXSIZE - 1, f);
  fclose(f);
  if (size < 4) {
    logit(LOG_FILE, "Warning: Save file less than 4 bytes.");
    goto sferror;
  }
  sav_vers = GET_BYTE(buf);
  if (sav_vers > (char) SAV_SAVEVERS) {
    logit(LOG_FILE, "Save file of %s is in an newer format.", name);
    send_to_char("Your character file is in a newer format which the game doesn't know how\n"
            "to load.  Please log on with another character and talk to a Forger.\n", ch);
    return -2;
  }
  if ((GET_BYTE(buf) != short_size) || (GET_BYTE(buf) != int_size) || (GET_BYTE(buf) != long_size)) {
    logit(LOG_FILE, "Save file of %s is in an old format.", name);
    send_to_char("Your character file was created on a machine of a different architecture\n"
            "type than the current one; loading such a file is not yet supported.\n"
            "Please talk to a Forger.\n", ch);
    return -2;
  }
  if (size < (5 * int_size + 5 * sizeof (char) +long_size)) {
    logit(LOG_FILE, "Warning: Save file is only %d bytes.", size);
    goto sferror;
  }
  type = (int) GET_BYTE(buf);
  GET_INTE(buf); /* skill offset */
  GET_INTE(buf); /* affect offset */
  GET_INTE(buf); /* item offset */
#ifdef EVENT_SAVING
  if (sav_vers > 1)
    GET_INTE(buf); /* events offset */
#endif
  if (sav_vers > 2)
    GET_INTE(buf); /* witness data offset */
#ifdef PROC_SAVING
  if (sav_vers > 3)
    GET_INTE(buf); /* proc offset */
#endif
  csize = GET_INTE(buf);
  if (size != csize) {
    logit(LOG_FILE, "restorePasswdOnly() Warning: file size %d doesn't match csize %d.", size, csize);
    goto sferror;
  }
  room = GET_INTE(buf); /* virtual room they saved/rented in */

  GET_INTE(buf); /* save time */
  stat_vers = GET_BYTE(buf);
  if (stat_vers > (char) SAV_STATVERS) {
    logit(LOG_FILE, "Save file for %s status restore failed.", GET_NAME(ch));
    send_to_char("Your character file is in a format which the game doesn't know how\n"
            "to load. Please log on with another character and talk to a Forger.\n", ch);
    return -2;
  }
  if (ch->player.name)
    free_string(ch->player.name);

  GET_NAME(ch) = GET_STRING(buf);

  ch->only.pc->screen_length = (ubyte) GET_BYTE(buf);

  str = GET_STRING(buf);
  if (!str) {
    send_to_char("How did you manage to nullify your password!??\nPlease contact a Forger!\n", ch);
    fprintf(stderr, "%s somehow managed to clear out his/her password field!\n", GET_NAME(ch));
    logit(LOG_FILE, "%s somehow managed to clear out his/her password field!", GET_NAME(ch));
    return -2;
  }
  strcpy(ch->only.pc->pwd, str);
  free_string(str);
  str = NULL;

  /* kludge sorta, this version is the one that expires all the old passwords, and forces them to enter a
     new, secure password.  type is saved in the descriptor struct, and makes for a handy way to tell what's
     going on.  Doing this acts like the password has been 'expired'. JAB */

  if (stat_vers < 20)
    type += 20;

  return type;

sferror:
  fprintf(stderr, "restorePasswdOnly(): Problem restoring save file of: %s\n", name);
  logit(LOG_FILE, "restorePasswdOnly(): Problem restoring save file of %s.", name);
  send_to_char("There is something wrong with your save file!  Please talk to a Forger.\n", ch);
  return -2;
}

/*
 * -1 = char doesn't exist -2 = problem reading file other = save type
 */


int restoreCharOnly(P_char ch, char *name) {
  FILE *f = NULL;
  char buff[SAV_MAXSIZE], *buf;
  int start, size, csize, skill_off, affect_off, item_off, procs_off, type, room, r_val, sav_vers, btmp = 0;
#ifdef EVENT_SAVING
  int events_off;
#endif
  int witness_off;

  if (!name || !ch)
    return 0;

  r_val = OpenPfile(ch, name, &f);
  if ((r_val < 0) || !f) {
    logit(LOG_FILE, "OpenPfile for %s returned %d", name, r_val);
    return r_val;
  }
  buf = buff;
  size = fread(buf, 1, SAV_MAXSIZE - 1, f);
  fclose(f);
  if (size < 4) {
    logit(LOG_FILE, "Warning: Save file less than 4 bytes.");
    goto sferror;
  }
  sav_vers = GET_BYTE(buf);
  if (sav_vers > (char) SAV_SAVEVERS) {
    logit(LOG_FILE, "Save file of %s is in an newer format.", name);
    send_to_char("Your character file is in a newer format which the game doesn't know how\n"
            "to load.  Please log on with another character and talk to a Forger.\n", ch);
    return -2;
  }
  if ((GET_BYTE(buf) != short_size) || (GET_BYTE(buf) != int_size) || (GET_BYTE(buf) != long_size)) {
    logit(LOG_FILE, "Save file of %s is in an old format.", name);
    send_to_char("Your character file was created on a machine of a different architecture\n"
            "type than the current one; loading such a file is not yet supported.\n"
            "Please talk to a Forger.\n", ch);
    return -2;
  }
  if (size < (5 * int_size + 5 * sizeof (char) +long_size)) {
    logit(LOG_FILE, "Warning: Save file is only %d bytes.", size);
    goto sferror;
  }
  type = (int) GET_BYTE(buf);
  skill_off = GET_INTE(buf);
  affect_off = GET_INTE(buf);
  item_off = GET_INTE(buf);
#ifdef EVENT_SAVING
  if (sav_vers > 1)
    events_off = GET_INTE(buf); /* events offset */
#endif
  if (sav_vers > 2) /* witness offset */
    witness_off = GET_INTE(buf);
#ifdef PROC_SAVING
  if (sav_vers > 3)
    procs_off = GET_INTE(buf); /* spec procs offset */
#endif

  csize = GET_INTE(buf);
  if (size != csize) {
    logit(LOG_FILE, "restoreCharOnly() Warning: file size %d doesn't match csize %d.", size, csize);
    goto sferror;
  }
  room = GET_INTE(buf); /* virtual room they saved/rented in */
  ch->specials.was_in_room = real_room(room);
  ch->only.pc->camp_room = 0;

  GET_INTE(buf);
  start = (int) (buf - buff);
  if ((restoreStatus(buf, ch) + start) != skill_off) {
    logit(LOG_FILE, "Warning: restoreStatus() not match offset.");
    goto sferror;
  }
  if ((stat_vers < 21) && ((GET_CLASS(ch) == CLASS_NECROMANCER) || (GET_CLASS(ch) == CLASS_ANTIPALADIN)))
    ch->specials.was_in_room = real_room(GET_HOME(ch));

  if (type == 4) /* return from death, flaked out. JAB */
    SET_POS(ch, POS_PRONE + STAT_SLEEPING);

  if ((restoreSkills(buff + skill_off, ch) + skill_off) != affect_off) {
    logit(LOG_FILE, "Warning: restoreSkills() not match offset.");
    goto sferror;
  }

  if ((restoreAffects(buff + affect_off, ch) + affect_off) != item_off) {
    logit(LOG_FILE, "Warning: restoreAffects() not match offset.");
    goto sferror;
  }

  // Convert to new grantset
  if ((GET_LEVEL(ch) > MAXLVLMORTAL) && (stat_vers < 50))
    convertGrantFlags(ch);

  // Strip NAMEBAN
  if (IS_CSET(ch->only.pc->pcact, PLR_NAMEBAN))
    REMOVE_CBIT(ch->only.pc->pcact, PLR_NAMEBAN);

  /* Neuter Illithids (too small for it's own function) - Shev */
  if (GET_RACE(ch) == RACE_ILLITHID && GET_SEX(ch) != SEX_NEUTRAL) {
    logit(LOG_PLAYER, "%s being neutered (Level %d)", GET_NAME(ch), GET_LEVEL(ch));
    GET_SEX(ch) = SEX_NEUTRAL;
    send_to_char("To account for the fact that the Illithid race is sexless, your race has been set\n"
            "to Neuter.  Have a nice day.\n", ch);
  }

  /* Switch necros to spec necro..                    */
  /* This is a kludgey damn temporary patch...  - CRM */
  if ((GET_CLASS(ch) == CLASS_NECROMANCER) && GET_CHAR_SKILL(ch, SKILL_SPEC_SUMMONING)) {
    ch->only.pc->skills[SKILL_SPEC_NECROMANCY].learned = ch->only.pc->skills[SKILL_SPEC_SUMMONING].learned;
    ch->only.pc->skills[SKILL_SPEC_SUMMONING].learned = 0;
    logit(LOG_PLAYER, "%s auto-switched to spec necro", GET_NAME(ch));
    send_to_char("Your specialization has been automatically switched to necromancy,"
            "as the animation spells are all of that type now.  Have a nice day.\n", ch);
  }

#if 1
  // Switch Conjies to Elies
  if ((GET_CLASS(ch) == CLASS_CONJURER))
    convert_conjurer_to_elementalist(ch);
#endif

  // Set Defense Skill
  if (!GET_CHAR_SKILL(ch, SKILL_DEFENSE)) {
    switch (GET_CLASS(ch)) {

      case CLASS_WARRIOR:
        btmp = ((int) (ch->only.pc->skills[SKILL_SHIELDBLOCK].learned));
        ch->only.pc->skills[SKILL_DEFENSE].learned = ((byte) MIN(90, btmp));
        break;

      case CLASS_PALADIN:
      case CLASS_ANTIPALADIN:
        btmp = ((int) (ch->only.pc->skills[SKILL_MOUNTED_COMBAT].learned));
        ch->only.pc->skills[SKILL_DEFENSE].learned = ((byte) MIN(90, btmp));
        break;

      case CLASS_RANGER:
      case CLASS_DIRERAIDER:
        btmp = ((int) (ch->only.pc->skills[SKILL_PARRY].learned));
        ch->only.pc->skills[SKILL_DEFENSE].learned = ((byte) MIN(80, btmp));
        break;

      case CLASS_BARD:
      case CLASS_BATTLECHANTER:
      case CLASS_ROGUE:
        btmp = ((int) (ch->only.pc->skills[SKILL_DODGE].learned));
        ch->only.pc->skills[SKILL_DEFENSE].learned = ((byte) MIN(65, btmp));
        break;

    }
  }

#ifdef ALPHA_MODE
  // temp turn on ooc for player, yea forced on entry for all, oh well
  SET_CBIT(ch->only.pc->pcact, PLR_OOC);
#endif

  /* Turn off NCC for non-newbies and non-helpers */
  if (IS_CSET(ch->only.pc->pcact, PLR_NCC) && (GET_LEVEL(ch) > 20) &&
          !IS_CSET(ch->only.pc->pcact, PLR_HELPER) && !IS_TRUSTED(ch)) {
    REMOVE_CBIT(ch->only.pc->pcact, PLR_NCC);
  }

  // Strip the GROUPALLOW flag.
  if (IS_CSET(ch->only.pc->pcact, PLR_GROUPALLOW))
    REMOVE_CBIT(ch->only.pc->pcact, PLR_GROUPALLOW);

  /* Blah, outcast good races from DK (used to be ZK) */
  /* if (RACE_GOOD(ch)) {  */
  if (PC_TOWN_JUSTICE_FLAGS(ch, HOME_ZHENTIL) == JUSTICE_IS_OUTCAST) {
    PC_SET_TOWN_JUSTICE_FLAGS(ch, JUSTICE_IS_KNOWN, HOME_ZHENTIL);

    /*   PC_SET_TOWN_JUSTICE_FLAGS(ch, JUSTICE_IS_OUTCAST, HOME_DOBLUTH);  */
  }

#if 0
  /* Another lame temp patch..gotta remove this around 9/10/99 -- CRM */
  if ((GET_RACE(ch) == RACE_DROW) && !IS_TRUSTED(ch) &&
          (GET_BIRTHPLACE(ch) != guild_locations[HOME_DOBLUTH][GET_CLASS(ch)])) {
    GET_BIRTHPLACE(ch) = guild_locations[HOME_DOBLUTH][GET_CLASS(ch)];
    send_to_char("&=LRYour hometown has been switched to Dobluth Kyor.\n", ch);
  }
#endif

  // Switch Liches to their racial hometowns
  if (IS_PC(ch) && (GET_CLASS(ch) == CLASS_LICH) && (GET_RACE(ch) != RACE_HUMAN)) {
    if (GET_RACE(ch) == RACE_DROW)
      GET_BIRTHPLACE(ch) = guild_locations[HOME_DOBLUTH][CLASS_NECROMANCER];
    else if (GET_RACE(ch) == RACE_YUANTI)
      GET_BIRTHPLACE(ch) = guild_locations[HOME_HYSSK][CLASS_NECROMANCER];
    //send_to_char("\n&=LRYour hometown has been switched to your racial town!\n", ch);
  }

  // Move everyone out of BS
  if (IS_PC(ch) && (GET_RACE(ch) == RACE_HUMAN) &&
          (GET_BIRTHPLACE(ch) == guild_locations[HOME_BLOODSTONE][GET_CLASS(ch)])) {
    if (GET_CLASS(ch) == CLASS_LICH) {
      GET_BIRTHPLACE(ch) = guild_locations[HOME_VIPERSTONGUE][GET_CLASS(ch)];
      send_to_char("\n&=LBDue to the destruction of Bloodstone, you have taken up residence in the far-flung Viperstongue Outpose.\n", ch);
    } else {
      GET_BIRTHPLACE(ch) = guild_locations[HOME_BALDURS][GET_CLASS(ch)];
      send_to_char("\n&=LBDue to the destruction of Bloodstone, you have taken up residence in the fine city of Baldur's Gate!&N\n", ch);
    }
  }

  /* Handle bard flag for NO_SING - Iyachtu */
  if (IS_PC(ch) && (IS_CSET(ch->only.pc->pcact, PLR_NO_SING)))
    REMOVE_CBIT(ch->only.pc->pcact, PLR_NO_SING);


  return type;

sferror:
  fprintf(stderr, "restoreCharOnly(): Problem restoring save file of: %s\n", name);
  logit(LOG_FILE, "restoreCharOnly(): Problem restoring save file of %s.", name);
  send_to_char("There is something wrong with your save file!  Please talk to a Forger.\n", ch);
  return -2;
}

/*
 * -1 = ran out of rent money -2 = couldn't read file (tried to rename to name.bad) >= 0 = rent amount charged
 */

int
restoreItemsOnly(P_char ch, int flatrate) {
  FILE *f = NULL;
  byte dummy_byte;
  char Gbuf1[MAX_STRING_LENGTH], Gbuf2[MAX_STRING_LENGTH], buff[SAV_MAXSIZE],
          *buf;
  int size, csize, skill_off, affect_off, item_off, procs_off, cost, tmp, r_val, sav_vers;
  int events_off, witness_off;
  //  P_obj  obj; // for reordering inventory at un-renting

  /*  int witness_off; */

  if (!ch)
    return -2;

  r_val = OpenPfile(ch, GET_NAME(ch), &f);
  if ((r_val < 0) || !f)
    return r_val;

  buf = buff;
  size = fread(buf, 1, SAV_MAXSIZE - 1, f);
  fclose(f);
  if (size < 4)
    goto sferror;
  sav_vers = GET_BYTE(buf);
  if (sav_vers > (char) SAV_SAVEVERS) {
    logit(LOG_FILE, "Save version does not match %d.", SAV_SAVEVERS);
    goto sferror;
  }
  if ((GET_BYTE(buf) != short_size) || (GET_BYTE(buf) != int_size) || (GET_BYTE(buf) != long_size)) {
    logit(LOG_FILE, "Save file in different machine format.");
    goto sferror;
  }
  if (size < (5 * int_size + 5 * sizeof (char) +long_size)) {
    logit(LOG_FILE, "Save file is too small %d.", size);
    goto sferror;
  }
  dummy_byte = GET_BYTE(buf);
  skill_off = GET_INTE(buf);
  affect_off = GET_INTE(buf);
  item_off = GET_INTE(buf);
#ifdef EVENT_SAVING
  if (sav_vers > 1)
    events_off = GET_INTE(buf); /* events offset */
#endif
  if (sav_vers > 2) /* witness data offset */
    witness_off = GET_INTE(buf);
#ifdef PROC_SAVING
  if (sav_vers > 3)
    procs_off = GET_INTE(buf);
#endif

  csize = GET_INTE(buf);
  if (size != csize) {
    logit(LOG_FILE, "Save file size %d not match size read %d.", size, csize);
    goto sferror;
  }
  cost = 0;

  for (tmp = 0; tmp < MAX_WEAR; tmp++)
    save_equip[tmp] = NULL;

  if (!restoreObjects(buff + item_off, ch))
    goto sferror;

  for (tmp = 0; tmp < MAX_WEAR; tmp++)
    if (save_equip[tmp] != NULL)
      wear(ch, save_equip[tmp], restore_wear[tmp], 0);

  /* will return to this when I'm better equipped to
     for(obj = ch->carrying; obj; obj = obj->next)
        {
        if(OBJ_IS_CONTAINER(obj))
           obj->contains = reverse_obj_list(obj->contains, TRUE); // TRUE means recurse containers
        }
   */
  return cost;

sferror:
  fprintf(stderr, "Problem restoring inventory of: %s\n", GET_NAME(ch));
  logit(LOG_FILE, "Problem restoring inventory of %s.", GET_NAME(ch));
  strcpy(Gbuf2, Gbuf1);
  strcat(Gbuf2, ".bad");
  rename(Gbuf1, Gbuf2); /* attempt to keep a copy of the file */
  return -2;
}

/* Restore witness record  */

int restoreWitness(char *buf, P_char ch) {
  wtns_rec *rec;
  char *start = buf;
  int count;

  if ((witness_vers = GET_BYTE(buf)) > (char) SAV_WTNSVERS) {
    logit(LOG_FILE, "Save file for %s witness restore failed.", GET_NAME(ch));
    send_to_char("Your witness record is munged. Please log on with another character and talk to a God.\n", ch);
    return 0;
  }
  count = GET_INTE(buf);
  for (; count > 0; count--) {
    if (!dead_witness_pool)
      dead_witness_pool = mm_create("WITNESS", sizeof (wtns_rec), offsetof(wtns_rec, next), 1);

    rec = mm_get(dead_witness_pool);
    rec->attacker = GET_STRING(buf);
    rec->victim = GET_STRING(buf);
    rec->time = GET_INTE(buf);
    rec->crime = GET_INTE(buf);
    rec->room = GET_INTE(buf);

    /* Ok we remove all the record that are 1 month old and more TASFALEN3 */
    if (((rec->time + SECS_PER_MUD_MONTH) < time(NULL)) || (rec->crime <= CRIME_LAST_FAKE)) {
      str_free(rec->attacker);
      str_free(rec->victim);
      mm_release(dead_witness_pool, rec);

    } else {
      rec->next = ch->specials.witnessed;
      ch->specials.witnessed = rec;
    }
  }

  return (int) (buf - start);
}

static int restoreObjects(char *buf, P_char ch) {
  P_obj obj, c_obj = NULL, t_obj = NULL;
  bool dummy_obj;
  byte dummy_byte, o_f_flag;
  int tmp, count, i, loc, obj_count = 0, V_num, i_count, ignore = 0;
  struct extra_descr_data *t_desc;
  struct obj_data d_obj;
  uint t_ex;
  ulong o_u_flag;

  obj_vers = (int) GET_BYTE(buf);
  if (obj_vers > SAV_ITEMVERS) {
    if (ch) {
      logit(LOG_FILE, "Item save versions don't match (%d, %d) for %s.", obj_vers, SAV_ITEMVERS, GET_NAME(ch));
      send_to_char("Your objects are in a format which the game doesn't know how\n"
              "to load. Please log on with another character and talk to a Forger.\n", ch);
    } else {
      logit(LOG_FILE, "Item save versions don't match (%d, %d) for pcorpse.", obj_vers, SAV_ITEMVERS);
    }
    return 0;
  }
  count = GET_INTE(buf);

  /* due to vast changes, much easier to start fresh with version 5 */

  for (; obj_count <= count;) {

    dummy_obj = FALSE;
    o_u_flag = 0;
    loc = 0;
    i_count = 1;
    o_f_flag = GET_BYTE(buf);

    if (o_f_flag & O_F_EOL) {
      if (!c_obj && !ignore) { /* end of the whole list */
        if (obj_count != count)
          return 0;
        else if (ch) {
          /* ok, simplest hack to make sure carried weight is correct, have to do this because
           obj_to_obj does not correctly updated carried weight (and would be ugly to fix).  JAB */
          GET_CARRYING_W(ch) = 0;
          for (obj = ch->carrying; obj; obj = obj->next_content)
            GET_CARRYING_W(ch) += GET_OBJ_WEIGHT(obj);
        }
        return 1;
      }
      if (!ignore) {
        if (OBJ_INSIDE(c_obj))
          c_obj = c_obj->loc.inside;
        else
          c_obj = NULL;
      } else
        ignore--;

      continue;
    }
    V_num = GET_INTE(buf);

    obj = read_object(V_num, VIRTUAL);

    if (!obj || (obj->extra_flags & ITEM_NORENT) || (o_f_flag & O_F_NORENT)) {
      if (!obj)
        logit(LOG_OBJ, "Could not load object #%d for %s.", V_num, (ch) ? GET_NAME(ch) : "pcorpse");
      else {
        /* NORENT objects get nuked quietly, it USED to not save them, but that caused major trauma when
           the NORENT object was a container (especialy in the equipment).  JAB */
        extract_obj(obj);
        obj = NULL;
      }
      obj = &d_obj;
      bzero(obj, sizeof (struct obj_data));

      dummy_obj = TRUE;

      /* have to fudge, if we can't load a container, we have to keep track or the list will end
         prematurely. */
      if (o_f_flag & O_F_CONTAINS)
        ignore++;
    }
    if (o_f_flag & O_F_WORN) {
      loc = GET_BYTE(buf);
      if (!dummy_obj) {
        if ((loc > 0) && (loc < MAX_WEAR))
          save_equip[loc - 1] = obj;
      }
    }
    if (o_f_flag & O_F_COUNT)
      i_count = GET_SHORT(buf);

    if (o_f_flag & O_F_DECAY) {
      tmp = (int) GET_INTE(buf);
      if (obj_vers > 19)
        obj->value[4] = GET_INTE(buf); /* restore looted/unlooted flag */
      if (!dummy_obj)
        AddEvent(EVENT_DECAY, tmp, TRUE, obj, 0);
    }
    if (o_f_flag & O_F_UNIQUE) {
      o_u_flag = GET_INTE(buf);
      obj->str_mask = (o_u_flag & 15);

      if (obj->str_mask) {
        if (o_u_flag & O_U_KEYS)
          obj->name = GET_STRING(buf);

        if (o_u_flag & O_U_DESC1)
          obj->description = GET_STRING(buf);

        if (o_u_flag & O_U_DESC2)
          obj->short_description = GET_STRING(buf);

        if (o_u_flag & O_U_DESC3)
          obj->action_description = GET_STRING(buf);
      }
      if ((obj_vers < 7) && (obj->type != ITEM_CORPSE)) {
        t_obj = obj;
        obj = &d_obj;
      }
      if (o_u_flag & 240) {
        if (obj_vers == 5)
          tmp = obj->value[1];
        for (i = 0; i < 4; i++)
          if (o_u_flag & (1 << (i + 4))) {
            tmp = GET_INTE(buf);
            if ((obj_vers > 11) || (obj->type == ITEM_SPELLBOOK))
              obj->value[i] = tmp;
          }
      }
      if (o_u_flag & O_U_TRAP) {
        if (obj_vers > 17) {
          obj->trap_eff = GET_SHORT(buf);
          obj->trap_dam = GET_SHORT(buf);
          obj->trap_charge = GET_SHORT(buf);
          obj->trap_level = GET_SHORT(buf);
          if (obj_vers > 18) {
            obj->trap_dnum = GET_SHORT(buf);
            obj->trap_dsize = GET_SHORT(buf);
          }
        }
      }
      if (o_u_flag & O_U_TYPE) {
        tmp = GET_BYTE(buf);
        if (obj_vers > 11)
          obj->type = tmp;
      }
      if (o_u_flag & O_U_WEAR) {
        tmp = GET_INTE(buf);
        if (obj_vers > 11)
          obj->wear_flags = tmp;
      }
      if (o_u_flag & O_U_EXTRA) {
        t_ex = GET_INTE(buf);
        if ((obj_vers < 11) && IS_SET(obj->extra_flags, ITEM_TWOHANDS))
          SET_BIT(t_ex, ITEM_TWOHANDS);
        if ((obj_vers < 16) && ((V_num == 1452) || (V_num == 11599) || (V_num == 15266)))
          REMOVE_BIT(t_ex, ITEM_NOSUMMON);
        if (obj_vers > 11)
          obj->extra_flags = t_ex;
      }
      if (o_u_flag & O_U_ANTI) {
        if (obj_vers > 16)
          obj->anti_flags = GET_INTE(buf);
      }
      if (o_u_flag & O_U_WEIGHT) {
        i = GET_INTE(buf);

        /* I hosed things up, patch for now */
        if (obj_vers < 10)
          i = obj->weight;

        /* handle weight change for objects */
        if (obj_vers > 5) {
          obj->weight = i;
        } else if (obj->type == ITEM_DRINKCON) {
          obj->weight -= tmp; /* base weight of drink con. */
          obj->weight += obj->value[1];
        }
      }
      if (o_u_flag & O_U_COST) {
        tmp = GET_INTE(buf);
        obj->cost = tmp;
      }

      if (o_u_flag & O_U_DURABILITY) {
        tmp = GET_INTE(buf);
        obj->durability = tmp;
      }

      if (o_u_flag & O_U_SETB) {
        for (i = 0; i < saved_aff_bytes; i++)
          obj->sets_affs[i] = (ubyte) GET_BYTE(buf);
        REMOVE_CBIT(obj->sets_affs, AFF_GROUP_CACHED);
      }
      if (o_u_flag & O_U_AFFS)
        for (i = 0; i < MAX_OBJ_AFFECT; i++) {
          tmp = GET_BYTE(buf);
          obj->affected[i].location = tmp;
          tmp = GET_BYTE(buf);
          obj->affected[i].modifier = tmp;
        }
      if (t_obj) {
        obj = t_obj;
        t_obj = NULL;
      }
    }
    if (o_f_flag & O_F_SPELLBOOK) {
      if (obj->type == ITEM_SPELLBOOK) {
        loc = GET_BYTE(buf);
        if (loc) { /* create fake spell description thing */
#ifdef MEM_DEBUG
          mem_use[MEM_E_DSCR] += sizeof (struct extra_descr_data);
          mem_use[MEM_STRINGS] += (loc + 1 + 4);
#endif
          CREATE(t_desc, struct extra_descr_data, 1);

          t_desc->next = obj->ex_description;
          obj->ex_description = t_desc;
          t_desc->keyword = str_dup("\03\01\03");

          CREATE(t_desc->description, char, (loc + 1));
          /* this assumes we will never decrease the number of skills -- Diirinka May 31, 97 */
          /* bad assumption, sorta, since I split spells and skills apart -- Gond 8/26/97 */
          for (i = 0; i < loc; i++)
            t_desc->description[i] = GET_BYTE(buf);

          /* CREATE uses calloc, no need to pad with zeros. JAB */

        }
      } else if ((obj_index[obj->R_num].virtual >= 716) && (obj_index[obj->R_num].virtual <= 747)) {
        /* smile, it's a totem!  check for and get the owner's name string. */
#ifdef MEM_DEBUG
        mem_use[MEM_E_DSCR] += sizeof (struct extra_descr_data);
#endif
        CREATE(t_desc, struct extra_descr_data, 1);

        t_desc->next = obj->ex_description;
        obj->ex_description = t_desc;
        t_desc->keyword = str_dup("\003\002\003");
        t_desc->description = GET_STRING(buf);

      } else {
        /* was causing some silly things before.. but now fixed: if item _was_ spellbook and had desc,
           will have no longer. */
        loc = GET_BYTE(buf);
        for (i = 0; i < loc; i++)
          dummy_byte = GET_BYTE(buf);
      }
    }
    obj_count += i_count;

    if (!dummy_obj) {
      do {
        if (c_obj && ((GET_OBJ_WEIGHT(obj) + GET_OBJ_WEIGHT(c_obj) <= c_obj->value[0]) || !ch)) {
          obj_to_obj(obj, c_obj);
          if (!OBJ_INSIDE_OBJ(obj, c_obj))
            logit(LOG_DEBUG, "failed obj to obj in restoreObjects (%s), %s into %s.",
                  GET_NAME(ch), obj->name, c_obj->name);
        } else {
          if (ch) {
            obj_to_char(obj, ch);
            if (!OBJ_CARRIED_BY(obj, ch))
              logit(LOG_DEBUG, "failed obj to char in restoreObjects (%s), %s", GET_NAME(ch), obj->name);
          } else if (obj->type == ITEM_CONTAINER) {
            obj_to_room(obj, cache_room);
            if (!OBJ_IN_ROOM(obj, cache_room))
              logit(LOG_DEBUG, "failed obj to room in restoreObjects %s %s", obj->name, cache_room);
          } else {
            obj_to_room(obj, corpse_room);
            if (!OBJ_IN_ROOM(obj, corpse_room))
              logit(LOG_DEBUG, "failed obj to room in restoreObjects %s %d", obj->name, corpse_room);
          }
        }
        if (--i_count)
          obj = read_object(V_num, VIRTUAL);
      } while (i_count);

      if (o_f_flag & O_F_CONTAINS)
        c_obj = obj;
    } else {
      /* dummy obj */
      logit(LOG_DEBUG, "dummy obj in restoreObjects (%s), %d.", ch ? GET_NAME(ch) : "corpse", V_num);
    }
  }


  /* ok, simplest hack to make sure carried weight is correct, have to do this because obj_to_obj
     does not correctly update carried weight (and would be ugly to fix).  JAB */

  if (ch) {
    GET_CARRYING_W(ch) = 0;
    for (obj = ch->carrying; obj; obj = obj->next_content)
      GET_CARRYING_W(ch) += GET_OBJ_WEIGHT(obj);
  }
  return 1;
}

#if 0                           /* not used, but waste not, want not. JAB */

static int
confiscate_item(P_char ch, int debt) {
  int value = 2, i;
  P_obj cobj = 0, obj, obj2;

  /* find most expensive item first */
  obj = ch->carrying;
  while (obj) {
    if (obj->cost > value && !obj->contains) {
      cobj = obj;
      value = cobj->cost;
    }
    if (OBJ_INSIDE(obj))
      if (obj->next_content)
        obj = obj->next_content;
      else
        obj = obj->loc.inside->next_content;
    else
      obj = obj->next_content;
  }

  if (!value || !cobj)
    return 0;

  /* if most expensive item won't cover it, grab it */

  if (((value * 3) / 4) < debt) {
    act("Confiscating your $o", FALSE, ch, cobj, 0, TO_CHAR);
    for (i = 0; i < MAX_WEAR; i++)
      if (save_equip[i] == cobj)
        save_equip[i] = NULL;
    if (cobj->contains) {
      obj = cobj->contains;
      while (obj) {
        obj2 = obj->next_content;
        obj_from_obj(obj);
        obj_to_char(obj, ch);
        obj = obj2;
      }
    }
    extract_obj(cobj);
    return ((value * 3) / 4);
  }
  /* if we get here most expensive item is worth more than the debt, so find
     the LEAST expensive item that will cover the debt */

  obj = ch->carrying;
  cobj = NULL;
  while (obj) {
    if ((obj->cost < value) &&
            (((obj->cost * 3) / 4) >= debt) && !cobj->contains) {
      cobj = obj;
      value = cobj->cost;
    }
    if (OBJ_INSIDE(obj))
      if (obj->next_content)
        obj = obj->next_content;
      else
        obj = obj->loc.inside->next_content;
    else
      obj = obj->next_content;
  }
  if (!cobj)
    return 0;
  act("Confiscating your $o", FALSE, ch, cobj, 0, TO_CHAR);
  for (i = 0; i < MAX_WEAR; i++)
    if (save_equip[i] == cobj)
      save_equip[i] = NULL;
#if 0
  if (cobj->contains) {
    obj = cobj->contains;
    for (obj = cobj->contains; obj; obj = obj2) {
      obj2 = obj->next_content;
      obj_from_obj(obj);
      obj_to_char(obj, ch);
    }
  }
#endif
  extract_obj(cobj);
  return ((value * 3) / 4);
}

static void
confiscate_all(P_char ch) {
  int i;
  P_obj obj, obj2;

  for (i = 0; i < MAX_WEAR; i++) {
    save_equip[i] = NULL;
    if (ch->equipment[i]) {
      extract_obj(ch->equipment[i]);
      ch->equipment[i] = NULL;
    }
  }
  obj = ch->carrying;
  while (obj) {
    obj2 = obj->next_content;
    extract_obj(obj);
    obj = obj2;
  }
}

int
rentObjects(char *buf) {
  int tmp, count, cost = 0, skip;
  byte dummy_byte;

  tmp = (int) GET_BYTE(buf);
  if (tmp != SAV_ITEMVERS && tmp != 2)
    return 0;
  count = GET_INTE(buf);
  for (; count > 0; count--) {
    skip = 0;
    dummy_byte = GET_BYTE(buf);
    GET_SHORT(buf);
    if (GET_INTE(buf) == 0)
      skip = 1;
    tmp = (int) GET_BYTE(buf);
    for (; tmp > 0; tmp--)
      GET_INTE(buf);
    dummy_byte = GET_BYTE(buf);
    for (tmp = 0; tmp < 4; tmp++)
      GET_INTE(buf);
    if (skip)
      GET_INTE(buf);
    else
      cost += GET_INTE(buf);
    GET_INTE(buf);
    for (i = 0; i < aff_count; i++)
      dump_core(); /* sets_affs is a cbit array */
    GET_INTE(buf); /* sets_affs */
    tmp = (int) GET_BYTE(buf);
    for (; tmp > 0; tmp--) {
      dummy_byte = GET_BYTE(buf);
      dummy_byte = GET_BYTE(buf);
    }
  }
  return cost;
}

#endif

/* routine called at boottime, goes through Corpses dir and loads all player
   corpses back into the game. */

void
restoreCorpses(void) {
  FILE *flist, *f;
  char Gbuf1[MAX_STRING_LENGTH], Gbuf2[MAX_STRING_LENGTH], Gbuf3[MAX_STRING_LENGTH],
          buff[SAV_MAXSIZE], *buf;
  int size, csize, tmp;
  struct stat statbuf;

  sprintf(Gbuf1, "%s/Corpses", SAVE_DIR);
  if (stat(Gbuf1, &statbuf) == -1) {
    perror("Corpses dir");
    return;
  }
  sprintf(Gbuf2, "%s/corpse_list", SAVE_DIR);
  if (stat(Gbuf2, &statbuf) == 0) {
    unlink(Gbuf2);
  } else if (errno != ENOENT) {
    perror("corpse_list");
    return;
  }
  sprintf(Gbuf3, "/bin/ls -1 %s > %s", Gbuf1, Gbuf2);
  system(Gbuf3); /* ls a list of Corpses dir into corpse_list */
  flist = fopen(Gbuf2, "r");
  if (!flist)
    return;

  while (fscanf(flist, " %s \n", Gbuf2) != EOF) {
    sprintf(Gbuf3, "%s/%s", Gbuf1, Gbuf2);
    f = fopen(Gbuf3, "r");
    if (!f) {
      logit(LOG_CORPSE, "Could not restore Corpse file %s", Gbuf2);
      continue;
    }
    buf = buff;
    size = fread(buf, 1, SAV_MAXSIZE - 1, f);
    fclose(f);

    if (size < 4)
      goto cf_error;

    if ((GET_BYTE(buf) != short_size) || (GET_BYTE(buf) != int_size) || (GET_BYTE(buf) != long_size)) {
      logit(LOG_FILE, "Save file %s in different machine format.", Gbuf2);
      goto cf_error;
    }
    if (size < (5 * int_size + 5 * sizeof (char) +long_size)) {
      logit(LOG_FILE, "Corpse file %s is too small (%d).", Gbuf2, size);
      goto cf_error;
    }
    /* WHACK!  The sound of a hack in progress.  Ok, without doing this, restoreObjects will put an empty corpse
       in the target room, which is then saved (empty).  Result, unless something saves the corpse again it will
       be empty after one crash/reboot, very annoying.  So we save a backup, and copy the backup into place, after
       the restoreObjects call. Two extra system calls (rename) but that's cheaper than most other solutions.
       JAB */

    sprintf(Gbuf2, "%s.bak", Gbuf3);
    if (rename(Gbuf3, Gbuf2) == -1) {
      logit(LOG_FILE, "Problem with player Corpses directory!\n");
      return;
    }
    tmp = GET_INTE(buf);
    if ((corpse_room = real_room(tmp)) == NOWHERE) {
      logit(LOG_FILE, "No room %d to load %s, loading into room 0", tmp, Gbuf2);
      corpse_room = 0;
    }
    csize = GET_INTE(buf);

    if (size != csize) {
      logit(LOG_FILE, "Corpse file %s size %d not match size read %d.", Gbuf2, size, csize);
      goto cf_error;
    }
    if (restoreObjects(buf, 0)) {
      /* Hack part Deux, put the loaded pcorpse back */
      unlink(Gbuf3);
      rename(Gbuf2, Gbuf3);
      continue;
    }
cf_error:
    fprintf(stderr, "Problem restoring corpse: %s\n", Gbuf2);
    logit(LOG_FILE, "Problem restoring corpse: %s.", Gbuf2);
  }

  fclose(flist);
}

static int convert_stat(int old_stat) {
  if (old_stat < 3)
    return 1;

  switch (old_stat) {
    case 3:
      return (number(1, 9));
      break;
    case 4:
      return (number(10, 15));
      break;
    case 5:
      return (number(16, 21));
      break;
    case 6:
      return (number(22, 27));
      break;
    case 7:
      return (number(28, 33));
      break;
    case 8:
      return (number(34, 39));
      break;
    case 9:
      return (number(40, 45));
      break;
    case 10:
      return (number(46, 50));
      break;
    case 11:
      return (number(51, 55));
      break;
    case 12:
      return (number(56, 61));
      break;
    case 13:
      return (number(62, 67));
      break;
    case 14:
      return (number(68, 73));
      break;
    case 15:
      return (number(74, 79));
      break;
    case 16:
      return (number(80, 85));
      break;
    case 17:
      return (number(86, 91));
      break;
    default:
      return (number(92, 100));
      break;
  }
}

/* stopgap code, this converts old style stats to new style, on a basically 1-to-1 basis.  Then fills
   in the new ones based on class. */

static void reroll_basic_abilities(P_char ch) {
  static const int old_stat_bonus[PC_RACES][5] ={
    {0, 0, 0, 0, 0}, /* none */
    {0, 0, 0, 0, 0}, /* human */
    {4, -4, -1, 0, 1}, /* barbarian */
    {0, 2, 3, 1, -1}, /* drow */
    {-2, 3, 0, 1, -1}, /* elf */
    {2, -2, 0, 0, 2}, /* dwarf */
    {1, -1, 1, -1, 1}, /* duergar */
    {-5, -1, 0, 5, 1}, /* halfling */
    {-5, 4, 2, 1, -1}, /* gnome */
    {5, -4, -4, -1, 2}, /* ogre */
    {3, -4, -4, -1, 5}, /* troll */
    {-1, 1, 0, 1, -1}, /* half-elf */
    {0, 0, 0, 0, 0} /* Illithid */
  };

  if (GET_C_STR(ch) > 18)
    GET_C_AGI(ch) = 100;
  else if (GET_C_STR(ch) < 18)
    GET_C_AGI(ch) = 0;

  /* normalize the old stats */

  GET_C_STR(ch) = BOUNDED(3, (GET_C_STR(ch) - old_stat_bonus[GET_RACE(ch)][0]), 18);
  GET_C_INT(ch) = BOUNDED(3, (GET_C_INT(ch) - old_stat_bonus[GET_RACE(ch)][1]), 18);
  GET_C_WIS(ch) = BOUNDED(3, (GET_C_WIS(ch) - old_stat_bonus[GET_RACE(ch)][2]), 18);
  GET_C_DEX(ch) = BOUNDED(3, (GET_C_DEX(ch) - old_stat_bonus[GET_RACE(ch)][3]), 18);
  GET_C_CON(ch) = BOUNDED(3, (GET_C_CON(ch) - old_stat_bonus[GET_RACE(ch)][4]), 18);

  GET_C_AGI(ch) = BOUNDED(0, GET_C_AGI(ch), 100);

  while ((GET_C_STR(ch) < 18) && (GET_C_AGI(ch) > 0)) {
    GET_C_STR(ch)++;
    if (GET_C_AGI(ch) > 10)
      GET_C_AGI(ch) -= 10;
    else
      GET_C_AGI(ch) = 0;
  }

  if (GET_C_AGI(ch) > 0)
    ch->base_stats.Str = BOUNDED(92, 90 + GET_C_AGI(ch) / 10, 100);
  else
    ch->base_stats.Str = MIN(92, convert_stat(GET_C_STR(ch)));

  ch->base_stats.Int = MAX(convert_stat(GET_C_INT(ch)), min_stats_for_class[GET_CLASS(ch)][5]);
  ch->base_stats.Wis = MAX(convert_stat(GET_C_WIS(ch)), min_stats_for_class[GET_CLASS(ch)][6]);
  ch->base_stats.Con = MAX(convert_stat(GET_C_CON(ch)), min_stats_for_class[GET_CLASS(ch)][3]);

  /* Dex and Agility, derived from old Dex */

  if ((GET_CLASS(ch) == CLASS_THIEF) || (GET_CLASS(ch) == CLASS_ASSASSIN) ||
          (GET_CLASS(ch) == CLASS_ROGUE)) {
    ch->base_stats.Dex = MAX(convert_stat(GET_C_DEX(ch)), min_stats_for_class[GET_CLASS(ch)][1]);
    ch->base_stats.Agi =
            BOUNDED(min_stats_for_class[GET_CLASS(ch)][2],
            number((int) (ch->base_stats.Dex * .85), (int) (ch->base_stats.Dex * 1.15)),
            100);
  } else {
    ch->base_stats.Agi = MAX(convert_stat(GET_C_DEX(ch)), min_stats_for_class[GET_CLASS(ch)][2]);
    ch->base_stats.Dex =
            BOUNDED(min_stats_for_class[GET_CLASS(ch)][1],
            number((int) (ch->base_stats.Agi * .85), (int) (ch->base_stats.Agi * 1.15)),
            100);
  }

  /* ok, at this point, we have converted the 'old' stats, more or less 1-for-1 to 'new'.  Now we have to generate
     'new' values for the completely new stats.  To keep the screaming to a minimum, we bottom cap these stats
     based on class. */

  /* Power */

  do {
    ch->base_stats.Pow = dice(3, 34) - 2;
  } while (ch->base_stats.Pow < min_stats_for_class[GET_CLASS(ch)][4]);

  /* Charisma */

  do {
    ch->base_stats.Cha = dice(3, 34) - 2;
  } while (ch->base_stats.Cha < min_stats_for_class[GET_CLASS(ch)][7]);


  ch->base_stats.Str = 100;
  ch->base_stats.Dex = 100;
  ch->base_stats.Agi = 100;
  ch->base_stats.Con = 100;
  ch->base_stats.Pow = 100;
  ch->base_stats.Int = 100;
  ch->base_stats.Wis = 100;
  ch->base_stats.Cha = 100;
  ch->base_stats.Karma = 100;
  ch->base_stats.Luck = 100;

  ch->curr_stats = ch->base_stats;
}

/* more stopgap, con is now dynamic, so have to recalc max hits, without con bonus */

static void recalc_base_hits(P_char ch) {
  int lvl = 0, hits = 0, total = 0;

  while (lvl < GET_LEVEL(ch)) {
    lvl++;

    /* hitpoint gain */

    switch (GET_CLASS(ch)) {
      case CLASS_BERSERKER:
      case CLASS_WARRIOR:
      case CLASS_RANGER:
      case CLASS_PALADIN:
      case CLASS_ANTIPALADIN:
        if (lvl == 1)
          hits = 28;
        else if (lvl < 26)
          hits = number(7, 10);
        else
          hits = 4;
        break;
      case CLASS_MERCENARY:
        if (lvl == 1)
          hits = 24;
        else if (lvl < 26)
          hits = number(6, 10);
        else
          hits = 3;
        break;
      case CLASS_MONK:
      case CLASS_BARD:
        if (lvl == 1)
          hits = 22;
        else if (lvl < 26)
          hits = number(6, 9);
        else
          hits = 3;
        break;
      case CLASS_ASSASSIN:
      case CLASS_THIEF:
      case CLASS_ROGUE:
        if (lvl == 1)
          hits = 16;
        else if (lvl < 26)
          hits = number(4, 6);
        else
          hits = 2;
        break;
      case CLASS_CLERIC:
      case CLASS_DRUID:
        if (lvl == 1)
          hits = 22;
        else if (lvl < 26)
          hits = number(5, 8);
        else
          hits = 3;
        break;
      case CLASS_CONJURER:
      case CLASS_SHAMAN:
        if (lvl == 1)
          hits = 14;
        else if (lvl < 26)
          hits = number(3, 5);
        else
          hits = 2;
        break;
      case CLASS_SORCERER:
        if (lvl == 1)
          hits = 12;
        else if (lvl < 26)
          hits = number(2, 4);
        else
          hits = 1;
        break;
      case CLASS_NECROMANCER:
        if (lvl == 1)
          hits = 12;
        else if (lvl < 41)
          hits = number(2, 4);
        else
          hits = 1;
        break;
    }

    hits += racial_data[(int) GET_RACE(ch)].hp_bonus;

    if (lvl == 1)
      total = hits;
    else
      total += MAX(1, hits);
  }

  ch->points.base_hit = MAX(1, total);
}

#if 0

/* one-shot function to turn all berserkers into warriors, setting skills and hits appropriately. */

static void
nuke_berserkers(P_char ch) {
  int i;

  if (!ch || IS_NPC(ch) || (GET_CLASS(ch) != CLASS_BERSERKER))
    dump_core();

  /* berserker be gone. */
  GET_CLASS(ch) = CLASS_WARRIOR;

  /* ok, basically only difference between berserkers and warriors, is a few skills.  Which of course is
     the main reason they are being nuked.  So we just scan through the char's skill list, zeroing out
     those skills not posessed by warriors, and setting the appropriate skill level on the skills warriors
     DO have.  As a sop, they get their skills set to the current maximum level.  JAB */
  for (i = 0; i < MAX_SKILLS; i++) {
    if (pindex2Skill[i] < 0)
      continue;

    if (!skills[pindex2Skill[i]].class[CLASS_WARRIOR - 1].rlevel ||
            (skills[pindex2Skill[i]].class[CLASS_WARRIOR - 1].rlevel > GET_LEVEL(ch))) {
      ch->only.pc->skills[i].learned = 0;
    } else {
      ch->only.pc->skills[i].learned = CharMaxSkill(ch, pindex2Skill[i]);
    }
  }

  /* tell them about it. */
  send_to_char("For reasons of balance, the berserker class has been removed from the game.\n"
          "Existing Berserkers are being converted into Warriors of equal level.  In order\n"
          "to minimize the trauma, all of your new Warrior skills are being set to their\n"
          "maximum (for your level).  Skills that you already had as a Berserker will be\n"
          "unchanged.  There are three exceptions, Berserk, Chant and Regeneration are\n"
          "not warrior skills, these will be set to zero.\n", ch);

  /* if their birthplace is still the appropriate berserker guild, set it to the correct warrior guild
     instead.  This is a bit of a kludge, we scan the birthplace table to see if their birthplace matches,
     if it does, we lookup the warrior equiv, it it doesn't exist, we use the default. */
  for (i = 1; i <= LAST_HOME; i++) {
    if (guild_locations[i][CLASS_BERSERKER] == GET_BIRTHPLACE(ch)) {
      if (guild_locations[i][CLASS_WARRIOR] == -1) {
        GET_BIRTHPLACE(ch) = guild_locations[i][CLASS_NONE];
        send_to_char("Your birthplace has been reset to the center of your hometown.\n", ch);
      } else {
        GET_BIRTHPLACE(ch) = guild_locations[i][CLASS_WARRIOR];
        send_to_char("Your birthplace has been reset to the Warrior guild in your hometown.\n", ch);
      }
      break;
    }
  }

  logit(LOG_PLAYER, "%s converted from Berserker to Warrior (Level %d)", GET_NAME(ch), GET_LEVEL(ch));
}

/* one-shot function to turn all gnome/halfling shamans into clerics, setting skills and hits appropriately. */

static void
convert_shaman_to_cleric(P_char ch) {
  int i, j;

  if (!ch || IS_NPC(ch) || (GET_CLASS(ch) != CLASS_SHAMAN))
    dump_core();

  logit(LOG_PLAYER, "%s being converted from Shaman to Cleric (Level %d)", GET_NAME(ch), GET_LEVEL(ch));

  /* berserker be gone. */
  GET_CLASS(ch) = CLASS_CLERIC;

  for (i = 0, j = 0; i < MAX_SKILLS; i++) {
    if ((j = pindex2Skill[i]) < 0)
      continue;

    /* first, nuke any setbit skills/spells, including quest spells, they'll be reduced to non-quest shaman only */
    if (!skills[j].class[CLASS_SHAMAN - 1].rlevel || (skills[j].class[CLASS_SHAMAN - 1].rlevel > GET_LEVEL(ch))) {
      if (ch->only.pc->skills[i].learned)
        logit(LOG_PLAYER, "%s's %s set from %d to 0.",
              GET_NAME(ch), skills[j].name, ch->only.pc->skills[i].learned);
      ch->only.pc->skills[i].learned = 0;
      continue;
    }
    /* now go through, set any skills/spells that shamans and clerics don't share, to 0 */
    if (skills[j].class[CLASS_SHAMAN - 1].rlevel && (!skills[j].class[CLASS_CLERIC - 1].rlevel ||
            (skills[j].class[CLASS_CLERIC - 1].rlevel > GET_LEVEL(ch)))) {
      if (ch->only.pc->skills[i].learned)
        logit(LOG_PLAYER, "%s's %s set from %d to 0.",
              GET_NAME(ch), skills[j].name, ch->only.pc->skills[i].learned);
      ch->only.pc->skills[i].learned = 0;
      continue;
    }
    /* set skills that clerics have but shamans don't to maximum */
    if (!skills[j].class[CLASS_SHAMAN - 1].rlevel && skills[j].class[CLASS_CLERIC - 1].rlevel &&
            (skills[j].class[CLASS_CLERIC - 1].rlevel <= GET_LEVEL(ch))) {
      logit(LOG_PLAYER, "%s's %s set from %d to %d.",
              GET_NAME(ch), skills[j].name, ch->only.pc->skills[i].learned, CharMaxSkill(ch, j));
      ch->only.pc->skills[i].learned = CharMaxSkill(ch, j);
      continue;
    }
    if (skills[j].class[CLASS_SHAMAN - 1].maxlearn <= skills[j].class[CLASS_CLERIC - 1].maxlearn)
      continue;

    /* ok, only two cases left: both classes have the same spell/skill, but they may have level differences */
    if (skills[j].class[CLASS_CLERIC - 1].maxlearn &&
            (skills[j].class[CLASS_SHAMAN - 1].maxlearn > skills[j].class[CLASS_CLERIC - 1].maxlearn))
      logit(LOG_PLAYER, "%s's %s set from %d to %d.",
            GET_NAME(ch), skills[j].name, ch->only.pc->skills[i].learned, CharMaxSkill(ch, j));
    ch->only.pc->skills[i].learned = CharMaxSkill(ch, j); /* max for cleric */
  }

  /* tell them about it. */
  send_to_char("To correct a long-standing problem, Gnome and Halfling Shamans have been\n"
          "converted to Clerics of the same level.  Skills and spells will now be for a\n"
          "Cleric.  To ease the transition, cleric skills that shamans do not have, are\n"
          "set to the maximum for your level.  The largest change will be for characters\n"
          "of level 20 and above.  You can now choose to specialize.  Check the help for\n"
          "'specialize' in the game before you do anything rash.  To avoid confusion, your\n"
          "memorization list has been cleared, and any memorized spells that clerics don't\n"
          "have, have been cleared.\n", ch);

  /* if their birthplace is still the appropriate shaman guild, set it to the correct cleric guild
     instead.  This is a bit of a kludge, we scan the birthplace table to see if their birthplace matches,
     if it does, we lookup the cleric equiv, it it doesn't exist, we use the default. */
  for (i = 1; i <= LAST_HOME; i++) {
    if (guild_locations[i][CLASS_SHAMAN] == GET_BIRTHPLACE(ch)) {
      if (guild_locations[i][CLASS_CLERIC] == -1) {
        GET_BIRTHPLACE(ch) = guild_locations[i][CLASS_NONE];
        send_to_char("Your birthplace has been reset to the center of your hometown.\n", ch);
      } else {
        GET_BIRTHPLACE(ch) = guild_locations[i][CLASS_CLERIC];
        send_to_char("Your birthplace has been reset to the Cleric guild in your hometown.\n", ch);
      }
      break;
    }
  }

  delete_memorize_list(ch, ch->only.pc->memorize_list);
  ch->only.pc->memorize_list = 0;

  for (i = 1; i <= numSkills; i++)
    ch->only.pc->skills[i].memorized = 0;
  for (i = 0; i < MAX_CIRCLE; i++)
    ch->only.pc->spells_memmed[i] = 0;

  logit(LOG_PLAYER, "%s converted from Shaman to Cleric (Level %d)", GET_NAME(ch), GET_LEVEL(ch));
}

#endif

/* this replaces code in 4 restoreXXX functions, pass it the calling char (usually the one being restored), the
 * name of the char to restore (in case it's NOT the calling char), and the address of the FILE handle.  It parses
 * the name into the filename, and does all the consistancy checks, then opens the file.
 *
 * Returns:  0 - file is open for reading, 'f' is a valid file handle
 *          -1 - file doesn't exist, 'f' is NULL.
 *          -2 - some sort of filesystem problem prevents using this file, 'f' is NULL.
 * JAB */

int
OpenPfile(P_char ch, char *name, FILE ** f) {
  char Gbuf1[MAX_STRING_LENGTH], Gbuf2[MAX_STRING_LENGTH], tbuf[MAX_INPUT_LENGTH],
          *tbuf_p;
  struct stat statbuf;

  tbuf_p = tbuf;
  *f = NULL; /* just in case we can't open it */

  strcpy(tbuf, name);
  for (; *tbuf_p; tbuf_p++)
    *tbuf_p = LOWER(*tbuf_p);
  tbuf_p = tbuf;
  sprintf(Gbuf1, "%s/%c/%s", SAVE_DIR, *tbuf, tbuf);

  strcpy(Gbuf2, Gbuf1);
  strcat(Gbuf2, ".new");

  if (stat(Gbuf2, &statbuf) == 0) {
    /* we had an abortive save attempt, just nuke it */
    if (unlink(Gbuf2))
      logit(LOG_FILE, "Cannot remove redundant file %s.", Gbuf2);
  }
  if (stat(Gbuf1, &statbuf) != 0) {
    /* it ain't there, or it's munged, let's look for a backup and restore it if we find it */
    strcpy(Gbuf2, Gbuf1);
    strcat(Gbuf2, ".bak");
    if (stat(Gbuf2, &statbuf) != 0)
      return -1;
    if (rename(Gbuf2, Gbuf1) == 0) {
      logit(LOG_FILE, "%s restored from %s.", Gbuf1, Gbuf2);
    } else
      return -1;
  }
  if (statbuf.st_size >= SAV_MAXSIZE) {
    logit(LOG_FILE, "Save file of %s is too large at %d bytes.", name, statbuf.st_size);
    wizlog(51, "Save file of %s is too large at %d bytes.", name, statbuf.st_size);
    send_to_char("You character file is corrupt, please login another character\n"
            "and request assitance using petition.\n", ch);
    return -2;
  }
  *f = fopen(Gbuf1, "r");
  if (!*f)
    return -2;

  /* we have a valid pfile, check for a (now unneeded) .bak file and nuke it if we find it */
  strcpy(Gbuf2, Gbuf1);
  strcat(Gbuf2, ".bak");
  if (stat(Gbuf2, &statbuf) == 0) {
    if (unlink(Gbuf2))
      logit(LOG_FILE, "Cannot remove redundant file %s.", Gbuf2);
  }
  return 0;
}



/****************************************************************************************************/
/*  Pet rent stuff                                                                                  */

/****************************************************************************************************/



int listRentedMobiles(P_char owner, char *buf, int savetype) {
  FILE *f;
  P_char mob;
  char *bufptr, *orgsrc;
  int i, r_val, size, numPets, petSaveVersion, room, zone, town, n, psn;
  struct npc_file_header header[SAV_MAXPETS];
  char srcbuf[SAV_MAXPETFILESIZE], *src, *psnptr, msgbuf[MAX_STRING_LENGTH];
  struct dirent **namelist;


  if (!owner || IS_NPC(owner))
    dump_core();

  buf[0] = 0;
  bufptr = buf;

  switch (savetype) {

    case SAV_TYPE_RENT:
    {

      r_val = npcsave_openFile(owner, GET_NAME(owner), SAV_TYPE_RENT, &f);
      if (r_val == -1)
        return NPCSAVE_FILENOTFOUND;
      if (r_val == -2)
        return NPCSAVE_FILESYSTEMERROR;

      src = srcbuf;
      size = fread(src, 1, SAV_MAXPETFILESIZE - 1, f);
      fclose(f);

      bzero((void *) header, sizeof (struct npc_file_header) * SAV_MAXPETS);
      numPets = npcsave_restoreHeader(owner, &src, (struct npc_file_header *) &header, &petSaveVersion);

      if (!numPets)
        return NPCSAVE_MOBNOTFOUND;

      orgsrc = src;

      sprintf(bufptr, "\n&+BCurrently Rented Pets for: &N&+L%s\n", GET_NAME(owner));
      for (i = 0; i < numPets; i++) {
        mob = GetNewChar(NEW_NPC);
        r_val = npcsave_readMobileData(mob, owner, NULL, src + header[i].offset, header[i].size);
        if (r_val < 0) {
          /* hack around to diag a problem in pet rent where restoreStatus tries to
           * call real_mobile() with a vnum of a NPC that doesnt exist in world.mob ..
           * ie the ch->nr gets set to -1 which signifies its a PC.. now we go into
           * RemoveFromCharList() and it bombs since it cant find the struct */
          if (mob->nr == -1) {
            wizlog(51, "listRentedMobiles(): problem restoring %s pet rent data.", GET_NAME(owner));
            mob->nr = 0;
          }
          RemoveFromCharList(mob);
          free_char(mob);
          return NPCSAVE_CORRUPTFILE;
        }
        room = real_room(header[i].saved_in);
        zone = world[room].zone;
        town = zone_table[zone].hometown;

        sprintf(bufptr + strlen(bufptr), "&+L[&N%d&+L][&N%d&+L] in %s &N %s\n", i + 1, mob->psn, hometown_names[town], GET_NAME(mob));
        bufptr += strlen(bufptr);
        RemoveFromCharList(mob);
        free_char(mob);
      }
      break;
    }

    case SAV_TYPE_CLAIM:
    {

      /* construct a search mask for scandir */
      strcpy(select_charname, GET_NAME(owner));
      strcat(select_charname, ".");
      *select_charname = LOWER(*select_charname);
      select_charname_len = strlen(select_charname);

      /* find all matching files and stick em in namelist */
      n = scandir(PET_CLAIM_DIR, &namelist, (void *) npcsave_selectName, 0);

      if (!n)
        return NPCSAVE_MOBNOTFOUND;

      /* ugh, ugly.. go thru every file found and temp restore */
      *msgbuf = 0;
      sprintf(bufptr, "\n&+BClaimable Pets for: &N&+L%s\n", GET_NAME(owner));
      for (i = 0; i < n; i++) {
        psnptr = namelist[i]->d_name + select_charname_len;
        if (!psnptr) {
          wizlog(51, "Corrupt pet filename [%s] in: %s", namelist[i]->d_name, PET_CLAIM_DIR);
          logit(LOG_DEBUG, "Corrupt pet filename [%s] in: %s", namelist[i]->d_name, PET_CLAIM_DIR);
          continue;
        }

        psn = atol(psnptr);

        mob = restoreMobileOnly(owner, GET_NAME(owner), psn, SAV_TYPE_CLAIM);
        if (!mob)
          continue;

        sprintf(bufptr + strlen(bufptr), "&+L[&N%d&+L][&N%d&+L]&N %s\n", i + 1, mob->psn, GET_NAME(mob));

        /* gotta remove it from char list since restoreMobileOnly does the alloc */
        RemoveFromCharList(mob);
        free_char(mob);
      }

      break;
    }

    default:
      logit(LOG_EXIT, "listRentedMobiles() undefined case detected");
      dump_core();
  }

  return TRUE;
}

int deleteMobile(P_char owner, int psn, int savetype) {

  FILE *f;
  int deleteSlot = -1, i, r_val, size, numPets, petSaveVersion;
  char *tmp, Gbuf1[MAX_STRING_LENGTH], Gbuf2[MAX_STRING_LENGTH];
  struct npc_file_header header[SAV_MAXPETS], org_header[SAV_MAXPETS];

  char srcbuf[SAV_MAXPETFILESIZE], *src;
  char dstbuf[SAV_MAXPETFILESIZE], *dst;

  if (!owner)
    dump_core();

  switch (savetype) {

    case SAV_TYPE_RENT:
    {

      r_val = npcsave_openFile(owner, GET_NAME(owner), SAV_TYPE_RENT, &f);
      if (r_val == -1)
        return NPCSAVE_FILENOTFOUND;
      if (r_val == -2)
        return NPCSAVE_FILESYSTEMERROR;

      src = srcbuf;
      dst = dstbuf;
      size = fread(src, 1, SAV_MAXPETFILESIZE - 1, f);
      fclose(f);

      bzero((void *) header, sizeof (struct npc_file_header) * SAV_MAXPETS);
      numPets = npcsave_restoreHeader(owner, &src, (struct npc_file_header *) &header, &petSaveVersion);

      if (!numPets)
        return NPCSAVE_MOBNOTFOUND;

      /* Find the requested PSN */
      deleteSlot = -1;
      for (i = 0; i < numPets; i++) {
        if (header[i].psn == psn) {
          deleteSlot = i;
          break;
        }
      }

      if (deleteSlot == -1)
        return NPCSAVE_MOBNOTFOUND;

      if (numPets == 1) {
        deleteRentFile(owner);
        return TRUE;
      }
      bcopy(header, org_header, sizeof (struct npc_file_header) * SAV_MAXPETS);

      /* Fix the header info of the mobile we're deleting */
      for (i = deleteSlot; i < numPets; i++) {
        if (deleteSlot >= (SAV_MAXPETS - 1))
          break;
        header[i].offset = header[i + 1].offset - org_header[deleteSlot].size;
        header[i].size = header[i + 1].size;
        header[i].psn = header[i + 1].psn;
      }

      /* Write the fixed header info into the dst buffer */
      size = npcsave_constructBufferFromHeader(&dst, (struct npc_file_header *) &header, numPets - 1);

      /* Go and bcopy all the data block except the one we want erased.. */
      for (i = 0; i < numPets; i++) {
        if (i == deleteSlot)
          continue;
        bcopy(src + org_header[i].offset, dst, org_header[i].size);
        dst += org_header[i].size;
      }

      npcsave_writeData(owner, psn, SAV_TYPE_RENT, (dst - dstbuf), dstbuf);

      break;
    }

    case SAV_TYPE_INGAME:
    case SAV_TYPE_CLAIM:
    {

      if (savetype == SAV_TYPE_INGAME)
        sprintf(Gbuf1, "%s/", PET_INGAME_SAVE_DIR);
      else {
        if (savetype == SAV_TYPE_CLAIM)
          sprintf(Gbuf1, "%s/", PET_CLAIM_DIR);
        else
          dump_core();
      }

      tmp = Gbuf1 + strlen(Gbuf1);
      sprintf(tmp, "%s.%d", GET_NAME(owner), psn);
      for (; *tmp; tmp++)
        *tmp = LOWER(*tmp);
      strcpy(Gbuf2, Gbuf1);
      strcat(Gbuf2, ".bak");
      unlink(Gbuf1);
      unlink(Gbuf2);
      break;
    }

    default:
      logit(LOG_EXIT, "deleteMobile(): unknown case detected.");
      dump_core();
  }

  return TRUE;

}

/* Deletes the rent file belonging to the given P_char */

int deleteRentFile(P_char ch) {
  char *tmp, Gbuf1[MAX_STRING_LENGTH], Gbuf2[MAX_STRING_LENGTH];

  if (!ch || IS_NPC(ch))
    dump_core();

  sprintf(Gbuf1, "%s/%c/", PET_RENT_SAVE_DIR, LOWER(*ch->player.name));
  tmp = Gbuf1 + strlen(Gbuf1);
  strcat(Gbuf1, GET_NAME(ch));
  for (; *tmp; tmp++)
    *tmp = LOWER(*tmp);
  strcpy(Gbuf2, Gbuf1);
  strcat(Gbuf2, ".bak");
  unlink(Gbuf1);
  unlink(Gbuf2);

  return TRUE;
}

/*
 * Restores a blank NPC without any equipment.. restoretype decides were the mobile
 * data is taken from.. SAV_TYPE_RENT takes it from the rent file, while SAV_TYPE_INGAME
 * would grab it from the Mobiles/ingame directory (restores pets that were in the game
 * when we crashed)
 *
 * In case of SAV_TYPE_INGAME, we wont have the owner in all cases so we have to settle for
 * passing the owners name string which will be used to restore the saved file together with
 * the psn.. Afterwards, we can do whatever we want with the returned P_char..
 *
 * For SAV_TYPE_RENT, we have to pass the owner p_char, while the char *name ptr can be NULL
 *
 * Need the psn in both cases as thats what we key on while searching for the mob in order to
 * avoid reading/decoding the whole mobile, for SAV_TYPE_RENT, headers contain the psn's for
 * each saved mob so thats the only thing we gotta look at, while for SAV_TYPE_INGAME, the
 * file extension consists of the psn, so stating alone is enough to check if it exists..
 *
 */

P_char restoreMobileOnly(P_char owner, char *name, int psn, int restoretype) {
  P_char pet;
  FILE *f = NULL;
  int size, i, r_val;
  int numPets = 0, petSaveVersion;
  int restoreSlot = -1;
  char buf[MAX_STRING_LENGTH];
  char srcbuf[SAV_MAXPETFILESIZE], *src;
  struct npc_file_header header[SAV_MAXPETS];


  if ((!owner && (restoretype == SAV_TYPE_RENT)) || (!name && (restoretype == SAV_TYPE_INGAME)))
    return 0;

  switch (restoretype) {

    case SAV_TYPE_RENT:
    {

      r_val = npcsave_openFile(owner, GET_NAME(owner), SAV_TYPE_RENT, &f);
      if (r_val < 0)
        return 0;

      /* suck the whole thing in one shot */
      src = srcbuf;
      size = fread(src, 1, SAV_MAXPETFILESIZE - 1, f);
      fclose(f);

      bzero((void *) header, sizeof (struct npc_file_header) * SAV_MAXPETS);
      numPets = npcsave_restoreHeader(owner, &src, (struct npc_file_header *) &header, &petSaveVersion);

      if (numPets > SAV_MAXPETS) {
        wizlog(GET_LEVEL(owner), "%s pet rent file is corrupt. Number of pets found in the header is greater the the currently allowed maximum.", GET_NAME(owner));
        logit(LOG_FILE, "Pet Rent: %s pet rent file header is corrupt. numPets > SAV_MAXPETS", GET_NAME(owner));
        return 0; /*NPCSAVE_CORRUPTFILE; */
      }
      /* Find the requested PSN */
      restoreSlot = -1;
      for (i = 0; i < numPets; i++) {
        if (header[i].psn == psn) {
          restoreSlot = i;
          break;
        }
      }

      if (restoreSlot == -1)
        return 0; /*NPCSAVE_MOBNOTFOUND */

      pet = GetNewChar(NEW_NPC);
      src += header[restoreSlot].offset;
      r_val = npcsave_readMobileData(pet, owner, NULL, src, header[restoreSlot].size);

      if (r_val < 0) {
        /* hack around to diag a problem in pet rent where restoreStatus tries to
         * call real_mobile() with a vnum of a NPC that doesnt exist in world.mob ..
         * ie the ch->nr gets set to -1 which signifies its a PC.. now we go into
         * RemoveFromCharList() and it bombs since it cant find the struct */
        if (pet->nr == -1) {
          wizlog(51, "listRentedMobiles(): problem restoring %s pet rent data.", GET_NAME(owner));
          pet->nr = 0;
        }
        RemoveFromCharList(pet);
        free_char(pet);
        return 0; /*NPCSAVE_CORRUPTFILE */
      }
      break;
    }

    case SAV_TYPE_INGAME:
    {

      if (!*name) {
        logit(LOG_EXIT, "restoreMobileOnly: null name string passed while trying to restore a single mobile");
        dump_core();
      }
      sprintf(buf, "%s.%d", name, psn);
      r_val = npcsave_openFile(NULL, buf, SAV_TYPE_INGAME, &f);
      if (r_val < 0)
        return 0;

      src = srcbuf;
      size = fread(src, 1, SAV_MAXPETSIZE - 1, f);
      fclose(f);

      if (size <= 0) {
        wizlog(51, "Error: Failed read of '%s' ingame saved pet rent file.", buf);
        return 0;
      }
      pet = GetNewChar(NEW_NPC);
      r_val = npcsave_readMobileData(pet, NULL, buf, src, size);

      if (r_val < 0) {
        /* hack around to diag a problem in pet rent where restoreStatus tries to
         * call real_mobile() with a vnum of a NPC that doesnt exist in world.mob ..
         * ie the ch->nr gets set to -1 which signifies its a PC.. now we go into
         * RemoveFromCharList() and it bombs since it cant find the struct */
        if (pet->nr == -1) {
          wizlog(51, "listRentedMobiles(): problem restoring %s pet rent data.", GET_NAME(owner));
          pet->nr = 0;
        }
        RemoveFromCharList(pet);
        free_char(pet);
        return 0; /*NPCSAVE_CORRUPTFILE */
      }
      break;
    }

    case SAV_TYPE_CLAIM:
    {

      if (!*name) {
        logit(LOG_EXIT, "restoreMobileOnly: null name string passed while trying to restore a single mobile");
        dump_core();
      }
      sprintf(buf, "%s.%d", name, psn);
      r_val = npcsave_openFile(NULL, buf, SAV_TYPE_CLAIM, &f);
      if (r_val < 0)
        return 0;

      src = srcbuf;
      size = fread(src, 1, SAV_MAXPETSIZE - 1, f);
      fclose(f);

      if (size <= 0) {
        wizlog(51, "Error: Failed read of '%s' pet (claim) file.", buf);
        return 0;
      }
      pet = GetNewChar(NEW_NPC);
      r_val = npcsave_readMobileData(pet, NULL, buf, src, size);

      if (r_val < 0) {
        /* hack around to diag a problem in pet rent where restoreStatus tries to
         * call real_mobile() with a vnum of a NPC that doesnt exist in world.mob ..
         * ie the ch->nr gets set to -1 which signifies its a PC.. now we go into
         * RemoveFromCharList() and it bombs since it cant find the struct */
        if (pet->nr == -1) {
          wizlog(51, "listRentedMobiles(): problem restoring %s pet rent data.", GET_NAME(owner));
          pet->nr = 0;
        }
        RemoveFromCharList(pet);
        free_char(pet);
        return 0; /*NPCSAVE_CORRUPTFILE */
      }
      break;
    }

    default:
      logit(LOG_EXIT, "restoreMobileOnly(): unknown case number");
      dump_core();

  }

  return pet;
}

int npcsave_readMobileData(P_char ch, P_char owner, char *filename, char *src, int data_size) {
  char *orgsrc;
  int start, csize, skill_off, affect_off, item_off, type, room, tmp, sav_vers;

  orgsrc = src;

  ch->nr = 0; /* Force the thing to be a NPC */

  sav_vers = GET_BYTE(src);
  if (sav_vers > SAV_SAVEVERS) {
    if (owner) {
      logit(LOG_FILE, "Save file of %s`s pets is in a newer format.", GET_NAME(owner));
      send_to_char("Your pet file is in a newer format which the game doesn't know how\n"
              "to load.  Please log on with another character and talk to a Forger.\n", owner);
    } else {
      logit(LOG_FILE, "Error restoring ingame saved pet file: %s", filename);
    }
    return -2;
  }
  if ((GET_BYTE(src) != short_size) || (GET_BYTE(src) != int_size) || (GET_BYTE(src) != long_size)) {
    if (owner) {
      logit(LOG_FILE, "Save file of %s is in an old format.", GET_NAME(owner));
      send_to_char("Your pet file was created on a machine of a different architecture\n"
              "type than the current one; loading such a file is not yet supported.\n"
              "Please talk to a Forger.\n", owner);
    } else {
      logit(LOG_FILE, "Error restoring an ingame saved pet file, invalid data size type in: %s", filename);
    }
    return -2;
  }
  if (data_size < (5 * int_size + 5 * sizeof (char) +long_size)) {
    logit(LOG_FILE, "Warning: Save file is only %d bytes.", data_size);
    goto sferror;
  }
  type = (int) GET_BYTE(src);
  skill_off = GET_INTE(src);
  affect_off = GET_INTE(src);
  item_off = GET_INTE(src);
  csize = GET_INTE(src);
  if (data_size != csize) {
    logit(LOG_FILE, "npcsave_readMobileData() Warning: file size %d doesn't match csize %d.", data_size, csize);
    goto sferror;
  }
  room = GET_INTE(src); /* virtual room they saved/rented in */
  ch->specials.was_in_room = real_room(room);

  GET_INTE(src);
  start = (int) (src - orgsrc);
  tmp = restoreStatus(src, ch);
  if ((tmp + start) != skill_off) {
    logit(LOG_FILE, "Warning: restoreStatus() doesn't match offset boundary.");
    goto sferror;
  }
  if (type == 4) /* return from death, flaked out. JAB */
    SET_POS(ch, POS_PRONE + STAT_SLEEPING);

#ifdef NPCS_SAVE_SKILLS
  if ((restoreSkills(orgsrc + skill_off, ch) + skill_off) != affect_off) {
    logit(LOG_FILE, "Warning: restoreSkills() doesn't match offset boundary.");
    goto sferror;
  }
#endif

  if ((restoreAffects(orgsrc + affect_off, ch) + affect_off) != item_off) {
    logit(LOG_FILE, "Warning: restoreAffects() doesn't match offset boundary.");
    goto sferror;
  }
  return type;

sferror:
  if (owner) {
    fprintf(stderr, "Problem restoring pet save file of: %s\n", GET_NAME(owner));
    logit(LOG_FILE, "Problem restoring pet save file of %s.", GET_NAME(owner));
    send_to_char("There is something wrong with your pet's save file!  Please talk to an admin.\n", owner);
  } else {
    fprintf(stderr, "Problem restoring ingame pet save file of: %s\n", filename);
    logit(LOG_FILE, "Problem restoring ingame pet save file of %s.", filename);
  }
  return -2;
}

/* pet       - pointer to the pet we want to save
 * savetype  - SAV_TYPE_RENT - meaning we are renting the mobile using a receptionist sort of a proc
 *           - SAV_TYPE_INGAME - trying to save a mobile which is still actively participating in the game
 *                             - it has to be following a PC for two reasons, gotta know the filename to
 *                             - save it to and need a way to figure out an unique psn.. It wouldnt be too
 *                             - hard to extend it to NPCs not following anyone, not now tho <g>
 * room      - virtual room we'll be saving the NPC in..
 *
 * Returns:  NPCSAVE_SUCCESS   - if successful (includes are in files.h)
 *           val < 0           - if failed, again check the return against the error defines if you need
 *                                to know why it failed..
 */

int writeMobile(P_char pet, int savetype, int room) {
  FILE *f;
  int size, i, data_size, r_val;
  int numPets = 0, petSaveVersion;
  int saveInSlot = 0;
  P_char owner;
  char srcbuf[SAV_MAXPETFILESIZE], *src;
  char dstbuf[SAV_MAXPETFILESIZE], *dst;
  struct npc_file_header header[SAV_MAXPETS];
  char data[SAV_MAXPETSIZE * 2];

  if (!pet)
    dump_core();

  if (pet->following && IS_PC(pet->following)) {
    owner = pet->following;
    if (IS_NPC(owner)) {
      send_to_char("You cannot rent out PCs", owner);
      return NPCSAVE_INVALIDOWNER;
    }
  } else {
    if (hasAutoSaveEvent(pet)) {
      removeAutoSaveEvent(pet);
      logit(LOG_DEBUG, "Attempting to rent//save a mobile not following a PC [%s]", GET_NAME(pet));
      wizlog(51, "ERROR: Attempting to autosave [%s] without an owner in [%d].", GET_NAME(pet), world[pet->in_room].number);
      return NPCSAVE_INVALIDOWNER;
    }
    dump_core();
  }

  switch (savetype) {
    case SAV_TYPE_RENT:
    {

      r_val = npcsave_openFile(owner, GET_NAME(owner), SAV_TYPE_RENT, &f);
      if (r_val == -1) /* file doesnt exist */
        numPets = 0;
      if (r_val == -2) /* File system error */
        return NPCSAVE_FILESYSTEMERROR;

      bzero((void *) header, sizeof (struct npc_file_header) * SAV_MAXPETS);
      if (r_val == 0) {
        /* Read the whole thing in one shot */
        src = srcbuf;
        size = fread(src, 1, SAV_MAXPETFILESIZE - 1, f);
        fclose(f);
        numPets = npcsave_restoreHeader(owner, &src, (struct npc_file_header *) &header, &petSaveVersion);
      }
      /* Check if any of the psn's is greater then the current ch->only.pc->psn counter */
      /*   can happen if an old pfile was restored while keeping current pet file */
      for (i = 0; i < numPets; i++) {
        if (header[i].psn >= owner->psn)
          owner->psn = header[i].psn + 1;
      }

      /* Make sure there is no mob with the same PSN in the file */
      if (pet->psn == 0)
        pet->psn = ++owner->psn;
      else
        for (i = 0; i < numPets; i++) {
          if (header[i].psn == pet->psn) {
            logit(LOG_DEBUG, "%s's pet file already contains a pet with the same PSN.", GET_NAME(owner));
            statuslog(51, "ERROR: Sanity check failed saving %s's pet [PSN %d already in the header]", GET_NAME(owner), pet->psn);
            send_to_char("Failed saving your pet, file already contains a pet with identical PSN. Please notify an administrator.\n", owner);
            return NPCSAVE_CORRUPTFILE;
          }
        }

      /* Find a free slot in the header.. Assumes that the header slots are used in a continuous block
       * with no free slots inbetween the used ones */
      if (numPets >= SAV_MAXPETS)
        return NPCSAVE_NOTENOUGHSLOTS;
      else {
        saveInSlot = numPets;
        /* double check if the header slot is really free */
        if (header[saveInSlot].offset)
          dump_core(); /* Something's out of sync.. We are trying to save, using a slot already taken by another mobile */
      }

      /* Construct the mobile data buffer and update the header structs */
      data_size = npcsave_constructMobileData(pet, 0, pet->in_room, data);
      header[saveInSlot].size = data_size;
      header[saveInSlot].psn = pet->psn;
      header[saveInSlot].saved_in = room;
      if (saveInSlot == 0)
        header[saveInSlot].offset = 0;
      else
        header[saveInSlot].offset = header[saveInSlot - 1].offset + header[saveInSlot - 1].size;

      numPets++;
      dst = dstbuf;

      size = npcsave_constructBufferFromHeader(&dst, (struct npc_file_header *) &header, numPets);

      /* copy the data for existing mobiles if they exist
       * size = total size of all data blocks kept track of by the header (that includes the newly
       * created block in *data)
       * data_size = the size of the newly created data block pointer to by *data
       */
      if (r_val == 0) {
        bcopy(src, dst, size - data_size);
        dst += size - data_size;
      }
      /* Append the new data buffer to whatever we got at this point */
      bcopy(data, dst, data_size);
      dst += data_size;
      data_size = dst - dstbuf;
      npcsave_writeData(owner, pet->psn, savetype, data_size, dstbuf);

      break;
    }

    case SAV_TYPE_INGAME:
    {

      if (pet->psn == 0)
        pet->psn = ++owner->psn;

      data_size = npcsave_constructMobileData(pet, 1, pet->in_room, data);
      npcsave_writeData(owner, pet->psn, savetype, data_size, data);

    }
  }

  return NPCSAVE_SUCCESS;
}

/* Initializes buf using info passed via the header array */

/* Returns the total size of the data blocks */

int npcsave_constructBufferFromHeader(char **buf, struct npc_file_header *header, int numPets) {
  int i, size;

  ADD_BYTE(*buf, (char) SAV_PETSAVEVERS);
  ADD_BYTE(*buf, (char) numPets);

  for (size = 0, i = 0; i < numPets; i++) {
    ADD_INT(*buf, (int) header[i].offset);
    ADD_INT(*buf, (int) header[i].size);
    ADD_INT(*buf, (int) header[i].psn);
    ADD_INT(*buf, (int) header[i].saved_in);
    size += header[i].size;
  }

  ADD_BYTE(*buf, (char) 0xff);

  return size;
}

/* Restores the info about each pet saved in the data file..
 * Returns:  Number of pets found
 *           -1 on errors
 *           header - initialized structure                    */

int npcsave_restoreHeader(P_char ch, char **buf, struct npc_file_header *header, int *petSaveVersion) {
  int i;
  ubyte numPets, dummy;

  *petSaveVersion = GET_BYTE(*buf);
  numPets = GET_BYTE(*buf);

  for (i = 0; i < numPets; i++) {
    header[i].offset = GET_INTE(*buf);
    header[i].size = GET_INTE(*buf);
    header[i].psn = GET_INTE(*buf);
    header[i].saved_in = GET_INTE(*buf);
  }

  dummy = GET_BYTE(*buf);
  if (dummy != 255) {
    logit(LOG_DEBUG, "Error while parsing the header of %s's pet file. 0xFF marker not found.", GET_NAME(ch));
    return -1;
  }
  return (int) numPets;
}

/* this replaces code in 4 restoreXXX functions, pass it the calling char (usually the one being restored), the
 * name of the char to restore (in case it's NOT the calling char), and the address of the FILE handle.  It parses
 * the name into the filename, and does all the consistancy checks, then opens the file.
 *
 * Returns:  0 - file is open for reading, 'f' is a valid file handle
 *          -1 - file doesn't exist, 'f' is NULL.
 *          -2 - some sort of filesystem problem prevents using this file, 'f' is NULL.
 * JAB */

/* Copied and modified to open mob rent files..
 * P_char ch -  can be NULL if not available.. Its used only for error messages in case
 *              something goes wrong
 * char *name - points to a string containing the owners name for RENT savetype
 *            - points to a string containing 'ownername.psn' for INGAME savetype
 */

static int npcsave_openFile(P_char ch, char *name, int savetype, FILE ** f) {
  char Gbuf1[MAX_STRING_LENGTH], Gbuf2[MAX_STRING_LENGTH], tbuf[MAX_INPUT_LENGTH],
          *tbuf_p;
  struct stat statbuf;

  tbuf_p = tbuf;
  *f = NULL; /* just in case we can't open it */

  strcpy(tbuf, name);
  for (; *tbuf_p; tbuf_p++)
    *tbuf_p = LOWER(*tbuf_p);
  tbuf_p = tbuf;

  switch (savetype) {
    case SAV_TYPE_RENT:
      sprintf(Gbuf1, "%s/%c/%s", PET_RENT_SAVE_DIR, *tbuf, tbuf);
      break;
    case SAV_TYPE_INGAME:
      sprintf(Gbuf1, "%s/%s", PET_INGAME_SAVE_DIR, tbuf);
      break;
    case SAV_TYPE_CLAIM:
      sprintf(Gbuf1, "%s/%s", PET_CLAIM_DIR, tbuf);
      break;
    default:
      logit(LOG_DEBUG, "npcsave_openFile: unrecognized save type");
      dump_core();
  }

  strcpy(Gbuf2, Gbuf1);
  strcat(Gbuf2, ".new");

  if (stat(Gbuf2, &statbuf) == 0) {
    /* we had an abortive save attempt, just nuke it */
    if (unlink(Gbuf2))
      logit(LOG_FILE, "Cannot remove redundant pet file %s.", Gbuf2);
  }
  if (stat(Gbuf1, &statbuf) != 0) {
    /* it ain't there, or it's munged, let's look for a backup and restore it if we find it */
    strcpy(Gbuf2, Gbuf1);
    strcat(Gbuf2, ".bak");
    if (stat(Gbuf2, &statbuf) != 0)
      return -1;
    if (rename(Gbuf2, Gbuf1) == 0) {
      logit(LOG_FILE, "NPC Pet: %s restored from %s.", Gbuf1, Gbuf2);
    } else
      return -1;
  }
  if (statbuf.st_size >= SAV_MAXSIZE) {
    logit(LOG_FILE, "Save file of %s's pets is too large at %d bytes.", name, statbuf.st_size);
    wizlog(51, "Save file of %s's pets is too large at %d bytes.", name, statbuf.st_size);
    if (ch)
      send_to_char("You pet file file is corrupt, request assitance using petition.\n", ch);
    return -2;
  }
  *f = fopen(Gbuf1, "r");
  if (!*f)
    return -2;

  /* we have a valid petfile, check for a (now unneeded) .bak file and nuke it if we find it */
  strcpy(Gbuf2, Gbuf1);
  strcat(Gbuf2, ".bak");
  if (stat(Gbuf2, &statbuf) == 0) {
    if (unlink(Gbuf2))
      logit(LOG_FILE, "Cannot remove redundant pet file %s.", Gbuf2);
  }
  return 0;
}

int npcsave_constructMobileData(P_char ch, int type, int room, char *buff) {
  P_obj obj, obj2;
  char *buf, *skill_off, *affect_off, *item_off, *size_off;
  int i;
  struct affected_type *af;

  if (!ch || IS_PC(ch) || !GET_NAME(ch) || !ch->following)
    return 0;

  room = translateSaveRoom(ch, type, room);

  /* this check assumes that the macros have not been fixed up for a different architecture type. */
  if ((sizeof (char) != 1) || (long_size != int_size)) {
    logit(LOG_DEBUG, "sizeof(char) must be 1 and int_size must == long_size for player saves!\n");
    return 0;
  }
  buf = buff;
  ADD_BYTE(buf, (char) SAV_SAVEVERS);
  ADD_BYTE(buf, (char) (short_size));
  ADD_BYTE(buf, (char) (int_size));
  ADD_BYTE(buf, (char) (long_size));

  ADD_BYTE(buf, (char) type);

  skill_off = buf;
  ADD_INT(buf, (int) 0);
  affect_off = buf;
  ADD_INT(buf, (int) 0);
  item_off = buf;
  ADD_INT(buf, (int) 0);
  size_off = buf;
  ADD_INT(buf, (int) 0);

  ADD_INT(buf, room); /* starting room (VIRTUAL) */

  ADD_INT(buf, time(0)); /* save time */

  /* unequip everything and remove affects before saving */

  for (i = 0; i < MAX_WEAR; i++)
    if (ch->equipment[i])
      save_equip[i] = unequip_char(ch, i, FALSE);
    else
      save_equip[i] = NULL;

  af = ch->affected;

  all_affects(ch, FALSE); /* reset to unaffected state */

  buf += writeStatus(buf, ch);

  ADD_INT(skill_off, (int) (buf - buff));

#ifdef NPCS_SAVE_SKILLS
  buf += writeSkills(buf, ch);
#endif

  ADD_INT(affect_off, (int) (buf - buff));

  buf += writeAffects(buf, ch->affected, type);

  ADD_INT(item_off, (int) (buf - buff));

  if ((type == 2) || (type == 4)) {
    ADD_BYTE(buf, (char) SAV_ITEMVERS);
    ADD_INT(buf, 0); /* no items */
  } else
    buf += writeItems(buf, ch);

  ADD_INT(size_off, (int) (buf - buff));

  /* if they are staying in game, re-equip them */

  if (type == 1) {
    for (i = 0; i < MAX_WEAR; i++)
      if (save_equip[i])
        equip_char(ch, save_equip[i], i, 1);
  } else {

    /* if not, nuke the equip and inven (it has already been saved) */

    for (i = 0; i < MAX_WEAR; i++)
      if (save_equip[i]) {
#ifdef ARTIFACT
        /* this prevents the artifact from being removed from
           the owner if a save occurs and a temp artifact is
         * created -- DDW */
        tagBogusArtifact(save_equip[i]);
#endif
        extract_obj(save_equip[i]);
        save_equip[i] = NULL;
      }
    for (obj = ch->carrying; obj; obj = obj2) {
      obj2 = obj->next_content;
#ifdef ARTIFACT
      /* this prevents the artifact from being removed from
         the owner if a save occurs and a temp artifact is
       * created -- DDW */
      tagBogusArtifact(obj);
#endif
      extract_obj(obj);
      obj = NULL;
    }
  }

  all_affects(ch, TRUE); /* reapply affects (including equip) */
  return (buf - buff);
}

int npcsave_writeData(P_char owner, int psn, int savetype, int write_size, char *buff) {
  FILE *f;
  char *buf, *tmp;
  char Gbuf1[MAX_STRING_LENGTH], Gbuf2[MAX_STRING_LENGTH], Gbuf3[MAX_STRING_LENGTH],
          chk_buf[SAV_MAXSIZE * 2];
  int bak, tmp_errno, chk_size;
  struct stat statbuf;

  if (!owner)
    dump_core();

  buf = buff;

  if (savetype == SAV_TYPE_INGAME) {
    if (write_size >= SAV_MAXPETSIZE) {
      logit(LOG_PLAYER, "Could not 'ingame' save %s's pet, file too large (%d bytes)", GET_NAME(owner), write_size);
      statuslog(GET_LEVEL(owner), "Could not 'ingame' save %s's pet,, file too large (%d bytes)", GET_NAME(owner), write_size);
      if ((owner->in_room != NOWHERE))
        send_to_char("Too many objects on your pet to rent save, get rid of some junk!", owner);
      return 0;
    }
  } else {
    if (savetype == SAV_TYPE_RENT) {
      if (write_size >= SAV_MAXPETFILESIZE) {
        logit(LOG_PLAYER, "Could not save %s's pet rent data, file too large (%d bytes)", GET_NAME(owner), write_size);
        statuslog(GET_LEVEL(owner), "Could not save %s's pet rent data, file too large (%d bytes)", GET_NAME(owner), write_size);
        if (owner->in_room != NOWHERE)
          send_to_char("Your pet rent file is too large, get rid of some junk stored on the pets in order to save it.\n", owner);
        return 0;
      }
    }
  }

#ifdef EILEEN_CLAUSE
  /* the Eileen clause: illegal strung player names just save to a junk dir */
  if (isalpha(*ch->player.name))
    sprintf(Gbuf1, "%s/%c/", SAVE_DIR, LOWER(*ch->player.name));
  else
    sprintf(Gbuf1, "%s/junk/", SAVE_DIR);
#endif

  switch (savetype) {
    case SAV_TYPE_RENT:
      sprintf(Gbuf1, "%s/%c/", PET_RENT_SAVE_DIR, LOWER(*owner->player.name));
      tmp = Gbuf1 + strlen(Gbuf1);
      strcat(Gbuf1, GET_NAME(owner));
      break;
    case SAV_TYPE_INGAME:
      sprintf(Gbuf1, "%s/", PET_INGAME_SAVE_DIR);
      tmp = Gbuf1 + strlen(Gbuf1);
      if (psn <= 0) {
        logit(LOG_EXIT, "Trying to save a NPC without a PSN");
        dump_core();
      }
      sprintf(Gbuf1 + strlen(Gbuf1), "%s.%d", GET_NAME(owner), psn);
      break;
    default:
      logit(LOG_EXIT, "Illegal save type in npcsave_writeData");
      dump_core();
  }

  for (; *tmp; tmp++)
    *tmp = LOWER(*tmp);
  strcpy(Gbuf2, Gbuf1);
  strcat(Gbuf2, ".bak");
  strcpy(Gbuf3, Gbuf1);
  strcat(Gbuf3, ".new");

  /* Gbuf1 now holds the last correctly saved pet file,
     Gbuf2 is where we save a backup of that file,
     Gbuf3 is where we write the new one.
   */

  /* cleanup time, check to see if we have a 'new' file leftover from a previous failed attempt.  If we do,
     just dispose of it. */

  if (!stat(Gbuf3, &statbuf)) {
    /* yup, have a failed earlier save attempt, get rid of it. */
    if (unlink(Gbuf3)) {
      logit(LOG_FILE, "Cannot remove %s.new, suspending saves of this character [%s]", GET_NAME(owner), Gbuf3);
      wizlog(51, "&+R&-LPANIC!&N  Cannot update %s's pet file! [%s]", GET_NAME(owner), Gbuf3);
      return 0;
    }
  }
  if (stat(Gbuf1, &statbuf) == 0) {
    /* pfile exists, let's see about making a backup */
    if (stat(Gbuf2, &statbuf) == 0) {
      /* whups, backup exists too, we be in trouble. */
      logit(LOG_FILE, "%s has both a pet file and a backup, suspending saves of this character. [%s][%s]", GET_NAME(owner), Gbuf1, Gbuf2);
      wizlog(51, "&+R&-LPANIC!&N  %s has both a pet file and a backup! [%s][%s]", GET_NAME(owner), Gbuf1, Gbuf2);
      return 0;
    }
    if (rename(Gbuf1, Gbuf2) == -1) {
      tmp_errno = errno;
      logit(LOG_FILE, "Failed rename in pet save directory!\n rename failed, errno = %d [%s] to [%s]\n", tmp_errno, Gbuf1, Gbuf2);
      wizlog(51, "&+R&-LPANIC!&N  Error backing up pet file for %s! [%s] to [%s]", GET_NAME(owner), Gbuf1, Gbuf2);
      return 0;
    }
    /* ok, we get here, we have a valid backup pfile, NO pfile. */
    bak = 1;
  } else {
    /* no pfile, may be a new char, or it may be a problem, let's see... */
    if (errno == EBADF) {
      /* NOTE: with the stat() function, only two errors are possible:
         EBADF   filedes is bad.
         ENOENT  File does not exist.
         EBADF should only occur using fstat().  Therefore, if I fall into here, I have some SERIOUS problems!  */

      logit(LOG_FILE, "Problem with pet save directory!\nstat failed on %s, errno = %d\n", Gbuf1, EBADF);
      wizlog(51, "&+R&-LPANIC!&N  Error finding pfile for %s! [%s]", GET_NAME(owner), Gbuf1);
      return 0;
    }
    if (stat(Gbuf2, &statbuf) == 0) {
      /* whups, no pfile, but we have a backup.  However, if we get HERE, then something is seriously wrong, or
         someone deleted the pfile but missed the backup. (restoring from a backup pfile is handled when READING a
         pfile, not writing it.  We yell and bomb out. */
      logit(LOG_FILE, "%s has a backup, but no pet file, suspending saves. [%s]", GET_NAME(owner), Gbuf2);
      wizlog(51, "&+R&-LPANIC!&N  %s has a backup with no pet file!", GET_NAME(owner), Gbuf2);
      return 0;
    }
    /* No pfile, no backup, we must be a new character, carry on... */
    bak = 0;
  }

  /* halfway home, if we get here, we have at most, one file (Gbuf2, the .bak file, which is the old pfile renamed.
     Now we open and write our data to Gbuf3 (the .new file).  */

  f = fopen(Gbuf3, "w");

  if (!f) {
    tmp_errno = errno;
    logit(LOG_FILE, "Couldn't create pet save file!\nfopen failed on [%s], errno = %d\n", Gbuf3, tmp_errno);
    wizlog(51, "&+R&-LPANIC!&N  Error creating pet file for %s! [%s]", GET_NAME(owner), Gbuf3);
    bak -= 2;
  } else {
    if (fwrite(buff, 1, (unsigned) write_size, f) != write_size) {
      tmp_errno = errno;
      logit(LOG_FILE, "Couldn't write to pet save file!\nfwrite failed on [%s], errno = %d\n", Gbuf3, tmp_errno);
      wizlog(51, "&+R&-LPANIC!&N  Error writing pet file for %s! [%s]", GET_NAME(owner), Gbuf3);
      bak -= 2;
    }
    fclose(f);
  }

  /* the new part, open the just written pfile, read it in, and memcmp what we just read with what we just wrote.
     If they match, off we go..., if not, we maybe have filesystem problems (like out of disk space). */

  if (stat(Gbuf3, &statbuf)
          || (statbuf.st_size >= SAV_MAXSIZE)
          || (statbuf.st_size < 4)
          || !(f = fopen(Gbuf3, "r"))) {
    /* eeek, file we just wrote is giving us problems */
    wizlog(51, "&+R&-LPANIC!&N  Filesystem in chaos, advise immediate shutdown!", GET_NAME(owner));
    bak -= 2;
  } else {
    chk_size = fread(chk_buf, 1, SAV_MAXSIZE - 1, f);
    fclose(f);
    if ((chk_size != write_size) || (memcmp(buff, chk_buf, write_size) != 0)) {
      /* read and write down't match, something is bad wrong */
      wizlog(51, "&+R&-LPANIC!&N  Cannot write non-corrupt pet file for %s! [%s]", GET_NAME(owner), Gbuf3);
      bak -= 2;
    }
  }

  /* ok, we have now:
   * written <name>.new with current save data
   * read what we just wrote, and confirmed it's correct.
   * now we check for our status and move either the .bak file, or the .new file into place as the new pfile
   * it's BARELY possible that we can fail AFTER this point (we can't rename either the .bak or .new file), but
   * it's highly unlikely, nevertheless, we check.  JAB */

  switch (bak) {
    case -2: /* save FAILED, and we have NO backup! */
      logit(LOG_FILE, "Unable to save %s's pet file, and no backup exists.", GET_NAME(owner));
      wizlog(51, "Unable to save %s's pet file, and no backup exists.", GET_NAME(owner));
      return 0;

    case -1: /* save FAILED, but we have a backup */
      if (rename(Gbuf2, Gbuf1) == -1) {
        tmp_errno = errno;
        logit(LOG_FILE, "Unable to restore backup for %s's pet file!  rename failed on [%s] to [%s], errno = %d\n", GET_NAME(owner), Gbuf2, Gbuf1, tmp_errno);
        wizlog(51, "&+R&-LPANIC!&N  Error restoring backup pfile for %s! Failed rename of [%s] to [%s]", GET_NAME(owner), Gbuf2, Gbuf1);
      } else {
        logit(LOG_FILE, "%s's petfile restored from backup.", GET_NAME(owner));
        wizlog(51, "%s's petfile restored from backup.", GET_NAME(owner));
      }
      /* restored or not, the save still failed, so return 0 */
      return 0;

    case 1: /* save worked, we rename *.new to *, THEN dispose of *.bak */
    case 0: /* save worked, no *.bak file was made to begin with */
      if (rename(Gbuf3, Gbuf1) == -1) {
        tmp_errno = errno;
        logit(LOG_FILE, "Unable to rename %s to %s, serious problems! (Error %d).", Gbuf3, Gbuf1, tmp_errno);
        wizlog(51, "&+R&-LPANIC!&N  Error saving pet file for %s! Failed rename from [%s] to [%s]", GET_NAME(owner), Gbuf3, Gbuf1);
        if (bak == 1) {
          if (rename(Gbuf2, Gbuf1) == -1) {
            tmp_errno = errno;
            logit(LOG_FILE, "Also unable to rename %s to %s, VERY serious problems! (Error %d).",
                    Gbuf2, Gbuf1, tmp_errno);
            wizlog(51, "&+R&-LPANIC!&N  Error saving pet file for %s! Failed rename from [%s] to [%s]", GET_NAME(owner), Gbuf2, Gbuf1);
          } else {
            logit(LOG_FILE, "Backup %s restored instead", Gbuf2);
          }
        }
        return 0;
      } else {
        /* all kosher so far, no we remove the .bak file (if it exists) */
        if ((bak == 1) && unlink(Gbuf2)) {
          /* no rest for the weary, we can't remove the backup (probably owned by root, grrrr) */
          tmp_errno = errno;
          logit(LOG_FILE, "Unable to remove %s, sigh, errno %d", Gbuf2, tmp_errno);
          wizlog(51, "&+R&-LPANIC!&N  Error removing %s, someone go look eh?", Gbuf2);
          /* however, the save worked, so we fall through and return 1 */
        }
      }
      break;

    default:
      dump_core(); /* we screwed up somewhere */
  }

  return 1;
}

int stopPetSave(P_char leader, P_char mob) {

  if (!leader || !mob)
    dump_core();

  if (IS_NPC(leader))
    return FALSE;

  if (!mob->following)
    return FALSE;

  if (mob->following != leader)
    return FALSE;

  if (!IS_CSET(mob->only.npc->npcact, ACT_SAVE))
    return FALSE;

  removeAutoSaveEvent(mob);

  deleteMobile(leader, mob->psn, SAV_TYPE_INGAME);

  return TRUE;
}

int npcsave_selectName(const struct dirent *direntry) {
  if (!strncmp(select_charname, direntry->d_name, select_charname_len))
    return TRUE;
  else
    return FALSE;
}

/* restores all the mobiles saved in the Mobile/ingame or whatever directory..
 * uses the name of the owner to scan for approperiate files, restore the mobile
 * and make it follow the owner..
 * WARNING: calling it while the player is already in the game having some pets follow
 *          him will duplicate them as they are being saved on the fly.. Basicaly
 *          its ment to be called only from enter_game() ..
 *
 *  NOT USED ATM  - alth Jan 99
 *
 */

int moveIngameMobilesToClaim(P_char owner) {
  struct dirent **namelist;
  char *psnptr, msgbuf[MAX_STRING_LENGTH];
  P_char mob;
  int n, i, psn;

  if (!owner) {
    logit(LOG_EXIT, "restoreCrashedMobiles: called with a NULL owner");
    dump_core();
  }
  if (IS_NPC(owner)) {
    logit(LOG_EXIT, "restoreCrashedMobiles: called with owner being a NPC");
    dump_core();
  }
  strcpy(select_charname, GET_NAME(owner));
  strcat(select_charname, ".");
  *select_charname = LOWER(*select_charname);
  select_charname_len = strlen(select_charname);

  n = scandir(PET_INGAME_SAVE_DIR, &namelist, (void *) npcsave_selectName, 0);

  *msgbuf = 0;
  for (i = 0; i < n; i++) {
    psnptr = namelist[i]->d_name + select_charname_len;
    if (!psnptr) {
      wizlog(51, "Corrupt pet filename [%s] in: %s", namelist[i]->d_name, PET_INGAME_SAVE_DIR);
      logit(LOG_DEBUG, "Corrupt pet filename [%s] in: %s", namelist[i]->d_name, PET_INGAME_SAVE_DIR);
      continue;
    }
    psn = atol(psnptr);

    mob = restoreMobileOnly(owner, GET_NAME(owner), psn, SAV_TYPE_INGAME);
    if (!mob)
      continue;

    sprintf(msgbuf, "Restoring %s&N in: [%s&N]\n", GET_NAME(mob), world[mob->specials.was_in_room].name);

    deleteMobile(owner, mob->psn, SAV_TYPE_INGAME);
    char_to_room(mob, mob->specials.was_in_room, 0);

    if (!PowerCheck(owner, mob)) {
      act("$N&N wrenches $Mself free from your control.", FALSE, owner, 0, mob, TO_CHAR);
    } else {
      add_follower(mob, owner, TRUE);

      /* Need to restore the charm affect if it doesnt exist.. */
      /* Problem/Note: stuff like elementals or undead have their charm set via
       * an expiring affect structure.. We dont want to set a permanent charm in those cases..
       * FIX FIX FIX FIX FIX FIX FIX FIX FIX FIX FIX FIX FIX FIX FIX FIX FIX FIX
       * FIX FIX FIX FIX FIX FIX FIX FIX FIX FIX FIX FIX FIX FIX FIX FIX FIX FIX
       * FIX FIX FIX FIX FIX FIX FIX FIX FIX FIX FIX FIX FIX FIX FIX FIX FIX FIX
       * FIX FIX FIX FIX FIX FIX FIX FIX FIX FIX FIX FIX FIX FIX FIX FIX FIX FIX
       */
      if (!IS_AFFECTED(mob, AFF_CHARM))
        SET_CBIT(mob->specials.affects, AFF_CHARM);
    }
  }

  if (*msgbuf)
    send_to_char(msgbuf, owner);

  return n;
}

/* restores all mobiles stored in the ingame/ subdirectory back into the game..
 * its ment to be called while booting.. all npc's are restored back into rooms
 * they were last saved in.. the PC has to come and claim em in order to have em
 * follow again.. Basicaly it kills a couple nasty birds with one stone.. Dont have
 * to clean stale files from the ingame directory etc etc
 *
 *  NOT USED ATM  - alth Jan 99
 *
 */

int restoreCrashedMobilesOnBoot(void) {
  int r_val, size;
  FILE *f;
  DIR *ingameDir;
  struct dirent *pet;
  P_char mob;
  char *mobData, mobDataBuf[SAV_MAXPETFILESIZE];


  if ((ingameDir = opendir(PET_INGAME_SAVE_DIR)) == NULL) {
    logit(LOG_FILE, "failed opendir() call in restoreCrashedMobilesOnBoot()  errno: %d", errno);
    fprintf(stderr, "ERROR restoring pets after a crash! Trouble opening %s directory. errno %d\n", PET_INGAME_SAVE_DIR, errno);
    dump_core();
  }


  while ((pet = readdir(ingameDir))) {
    if (pet->d_name[0] != '.') {
      /* do your magic baby */

      r_val = npcsave_openFile(NULL, pet->d_name, SAV_TYPE_INGAME, &f);
      if (r_val < 0)
        return 0;

      mobData = mobDataBuf;
      size = fread(mobData, 1, SAV_MAXPETSIZE - 1, f);
      fclose(f);

      if (size <= 0) {
        logit(LOG_FILE, "Error: Failed read of '%s' ingame saved pet rent file.", pet->d_name);
        continue;
      }

      mob = GetNewChar(NEW_NPC);
      //      r_val = npcsave_readMobileData (mob, NULL, buf, mobData, size);

      if (r_val < 0) {
        RemoveFromCharList(mob);
        free_char(mob);
        return 0; /*NPCSAVE_CORRUPTFILE */
      }
    }
  }

  return TRUE;

}

/* internal fn used to move the contents of the Mobiles/ingame/ directory right after a reboot (most
 * likely due to a crash) to Mobiles/claim/ .. now anything in Mobiles/claim/ is game to be retrieved
 * or rather claimed by players at some sort of a special place..
 * called from boot_db()
 */

int npcsave_MoveIngameToClaim(void) {
  struct stat statbuf;
  char Gbuf1[MAX_STRING_LENGTH];

  if (stat(PET_INGAME_SAVE_DIR, &statbuf) != 0) {
    logit(LOG_FILE, "failed stat() call in npcsave_MoveIngameToClaim()  errno: %d", errno);
    fprintf(stderr, "ERROR: failed check for existence of [%s] subdirectory. Go make one eh?\n", PET_INGAME_SAVE_DIR);
    dump_core();
  }

  if (stat(PET_CLAIM_DIR, &statbuf) != 0) {
    logit(LOG_FILE, "failed stat() call in npcsave_MoveIngameToClaim()  errno: %d", errno);
    fprintf(stderr, "ERROR: failed check for existence of [%s] subdirectory. Go make one eh?\n", PET_CLAIM_DIR);
    dump_core();
  }

  /* construct a system cmd and execute it - only if there are files to move */
  sprintf(Gbuf1, "for f in %s/*; do [ -f \"$f\" ] && mv \"$f\" %s/ || true; done", PET_INGAME_SAVE_DIR, PET_CLAIM_DIR);

  /* execute the command */
  system(Gbuf1);

  return TRUE;

}

/******************************************************************************************/


int writeCart(P_char ch) {
  FILE *f;
  P_obj obj = NULL, hold_content = NULL;
  bool bak = FALSE; /* return after unlinking existing */
  char Gbuf1[MAX_STRING_LENGTH], Gbuf2[MAX_STRING_LENGTH], temp[MAX_STRING_LENGTH];
  int count = 0;
  static char buf[SAV_MAXSIZE * 2];
  struct stat statbuf;


  sprintf(Gbuf1, "%s/Carts/%s", SAVE_DIR, GET_NAME(ch));

  /* sprintf(Gbuf1, "%s.cart", GET_NAME(ch)); */

  if (!stat(Gbuf1, &statbuf)) { /* we already have a cart file for this player */

    if (!ch->specials.cart) { /* if player don't have a cart kill the file! */
      unlink(Gbuf1);
      return (1);
    }
    bak = TRUE;
    strcpy(Gbuf2, Gbuf1);
    strcat(Gbuf2, ".bak");

    if (rename(Gbuf1, Gbuf2) == -1) {
      logit(LOG_FILE, "Problem with player Carts directory!\n");
      return (0);
    }
  } else if (errno == ENOENT) {
    bak = FALSE; /* no entry */
    if (!ch->specials.cart)
      return (1);
  } else if (errno != ENOENT) {
    logit(LOG_FILE, "Problem with player Carts directory!\n");
    return (0);
  }
  f = fopen(Gbuf1, "w");

  if (!f) {
    logit(LOG_FILE, "Couldn't create Cart save file!\n");
    return (0);
  }
  buf[0] = 0;


  sprintf(temp, "%d\n", world[ch->specials.cart->loc.room].number);

  strcat(buf, temp);

  /* have to hold the 'next_content' of cart, as this is stuff in the room and not to be saved.  Replaced after
     it's saved.  Ilsie */

  hold_content = ch->specials.cart->next_content;
  ch->specials.cart->next_content = NULL;

  count = countInven(ch->specials.cart) - 1;

  sprintf(temp, "Cart:\n%d, %d\n", obj_index[ch->specials.cart->R_num].virtual, count);
  strcat(buf, temp);

  obj = ch->specials.cart->contains;

  while (obj) {
    sprintf(temp, "%d\n", obj_index[obj->R_num].virtual);
    strcat(buf, temp);
    obj = obj->next_content;
  }

  ch->specials.cart->next_content = hold_content;


  if (fwrite(buf, 1, strlen(buf), f) != strlen(buf)) {
    logit(LOG_FILE, "Couldn't write to Cart save file!\n");
    fclose(f);
    return (0);
  }
  fclose(f);

  if (bak) {
    if (unlink(Gbuf2) == -1)
      logit(LOG_FILE, "Couldn't delete backup of player file.\n");
  }
  return (1);
}

int get_line_from_string(char *buf, char *string, int offset) {
  int count = 0;

  while (buf[offset]) {
    if (buf[offset] != '\n')
      string[count] = buf[offset];
    else {
      string[count] = 0;
      return (count + 1);
    }
    count++;
    offset++;
  }

  return 0;
}

void restoreCart(P_char ch) {
  FILE *f;
  char Gbuf1[MAX_STRING_LENGTH], Gbuf2[MAX_STRING_LENGTH], buf[SAV_MAXSIZE],
          temp[MAX_STRING_LENGTH];
  int size, room, count, offset, value[10];
  struct stat statbuf;
  P_obj cart = NULL, obj = NULL;


  sprintf(Gbuf1, "%s/Carts/%s", SAVE_DIR, GET_NAME(ch));
  printf("%s", Gbuf1);
  if (stat(Gbuf1, &statbuf)) {
    logit(LOG_FILE, "Could not restore Cart file %s, it doesn't exist or there's some other prob\n", Gbuf1);
    return;
  }
  f = fopen(Gbuf1, "r");
  if (!f) {
    logit(LOG_FILE, "Could not restore Cart file %s", Gbuf1);
    return;
  }
  size = fread(buf, 1, SAV_MAXSIZE - 1, f);
  fclose(f);

  if (size < 6) {
    logit(LOG_FILE, "Cart file %s is too small (%d).", Gbuf1, size);
    goto cf_error;
  }
  /* WHACK!  The sound of a hack in progress.  Ok, without doing this, restoreObjects will put an empty corpse
     in the target room, which is then saved (empty).  Result, unless something saves the corpse again it will
     be empty after one crash/reboot, very annoying.  So we save a backup, and copy the backup into place, after
     the restoreObjects call. Two extra system calls (rename) but that's cheaper than most other solutions.
     JAB */

  sprintf(Gbuf2, "%s.bak", Gbuf1);
  if (rename(Gbuf1, Gbuf2) == -1) {
    logit(LOG_FILE, "Problem with player Carts directory!\n");
    return;
  }
  offset = 0;
  offset += get_line_from_string(buf, temp, 0);

  if (!temp)
    goto cf_error;

  room = atoi(temp);

  offset += get_line_from_string(buf, temp, offset);

  if (!temp)
    goto cf_error;


  if (!strcmp(temp, "Cart:")) {
    offset += get_line_from_string(buf, temp, offset);

    sscanf(temp, "%d, %d", &value[0], &count);

    if (!(cart = read_object(value[0], VIRTUAL)))
      goto cf_error;

    if (!cart || (GET_ITEM_TYPE(cart) != ITEM_CART)) {
      logit(LOG_FILE, "Problems with cart file %s, cart vnum is wrong.\n", Gbuf1);
      return;
    }

    room = real_room(room);
    if (room == -1)
      room = ch->in_room;

    obj_to_room(cart, room);
    ch->specials.cart = cart;
    cart->owner = ch;

    for (; count; count--) {
      offset += get_line_from_string(buf, temp, offset);

      value[0] = atoi(temp);
      obj = 0;

      if (!(obj = read_object(value[0], VIRTUAL)))
        goto cf_error;

      if (!obj || (GET_ITEM_TYPE(obj) != ITEM_COMMODITY)) {
        logit(LOG_FILE, "Problems with cart file %s, obj vnum is wrong.\n", Gbuf1);
        return;
      }
      obj_to_obj(obj, cart);
    }
  }
  /* Hack part Deux, put the loaded pcorpse back */
  unlink(Gbuf2);
  rename(Gbuf2, Gbuf1);


  return;

cf_error:
  fprintf(stderr, "Problem restoring cart: %s\n", Gbuf1);
  logit(LOG_FILE, "Problem restoring cart: %s.", Gbuf1);

}

bool cartfile_exists(P_char ch) {
  char Gbuf1[MAX_STRING_LENGTH];
  FILE *f;

  sprintf(Gbuf1, "%s/Carts/%s", SAVE_DIR, GET_NAME(ch));

  if ((f = fopen(Gbuf1, "r"))) {
    fclose(f);
    return TRUE;
  }

  return FALSE;
}

int saveCodeControlBits(void) {
  FILE *f;
  int i, write_size, bak, tmp_errno;
  char buff[SAV_MAXSIZE * 2], *buf;
  char Gbuf1[MAX_STRING_LENGTH], Gbuf2[MAX_STRING_LENGTH], Gbuf3[MAX_STRING_LENGTH];
  struct stat statbuf;

  buf = buff;

  ADD_BYTE(buf, (char) SAV_CODECTRLVERS);
  ADD_BYTE(buf, (char) CODE_CONTROL_BYTES);
  for (i = 0; i < CODE_CONTROL_BYTES; i++) {
    ADD_BYTE(buf, (char) sets_code_control[i]);
  }

  write_size = (int) (buf - buff);
  if (write_size >= SAV_MAXSIZE) {
    logit(LOG_FILE, "Could not save the Code Control file. Too big [%d]", write_size);
    wizlog(51, "&+R&-LPANIC!&N Cannot save the Code Control file. Too big [%d]", write_size);
    return 0;
  }
  /* Enter da whole saving Shabang! */
  strcpy(Gbuf1, CODE_CONTROL_FILE);
  strcpy(Gbuf2, CODE_CONTROL_FILE);
  strcat(Gbuf2, ".bak");
  strcpy(Gbuf3, CODE_CONTROL_FILE);
  strcat(Gbuf3, ".new");

  /* Gbuf1 now holds the last correctly saved pfile,
     Gbuf2 is where we save a backup of that file,
     Gbuf3 is where we write the new one.
   */

  /* cleanup time, check to see if we have a 'new' file leftover from a previous failed attempt.  If we do,
     just dispose of it. */
  if (!stat(Gbuf3, &statbuf)) {
    /* yup, have a failed earlier save attempt, get rid of it. */
    if (unlink(Gbuf3)) {
      logit(LOG_FILE, "Cannot remove %s.new, suspending saves of code control bits.");
      wizlog(51, "&+R&-LPANIC!&N  Cannot update the code control file!");
      return 0;
    }
  }
  if (stat(Gbuf1, &statbuf) == 0) {
    /* file exists, let's see about making a backup */
    if (stat(Gbuf2, &statbuf) == 0) {
      /* whups, backup exists too, we be in trouble. */
      logit(LOG_FILE, "Both a backup and a code control file exist at the same time. You can think of it as BAD!");
      wizlog(51, "&+R&-LPANIC!&N Both a backup and a code control file exist at the same time. You can think of it as being really BAD!");
      return 0;
    }
    if (rename(Gbuf1, Gbuf2) == -1) {
      tmp_errno = errno;
      logit(LOG_FILE, "Failed rename() while saving code control bits, errno = %d\n", tmp_errno);
      wizlog(51, "&+R&-LPANIC!&N  Error backing up the code control file!");
      return 0;
    }
    /* ok, we get here, we have a valid backup pfile, NO file. */
    bak = 1;
  } else {
    /* no file, may be a new char, or it may be a problem, let's see... */
    if (errno == EBADF) {
      /* NOTE: with the stat() function, only two errors are possible:
         EBADF   filedes is bad.
         ENOENT  File does not exist.
         EBADF should only occur using fstat().  Therefore, if I fall into here, I have some SERIOUS problems!  */

      logit(LOG_FILE, "Problem with code control bits file directory!\nstat failed, errno = %d\n", EBADF);
      wizlog(51, "&+R&-LPANIC!&N  Error finding the code control file!");
      return 0;
    }
    if (stat(Gbuf2, &statbuf) == 0) {
      /* whups, no pfile, but we have a backup.  However, if we get HERE, then something is seriously wrong, or
         someone deleted the pfile but missed the backup. (restoring from a backup pfile is handled when READING a
         pfile, not writing it.  We yell and bomb out. */
      logit(LOG_FILE, "A backup of the code control bits file exists but the normal save file does not.");
      wizlog(51, "&+R&-LPANIC!&N  Only a backup of the Code Control file exists!");
      return 0;
    }
    /* No pfile, no backup, we must be a new character, carry on... */
    bak = 0;
  }

  /* halfway home, if we get here, we have at most, one file (Gbuf2, the .bak file, which is the old file renamed.
     Now we open and write our data to Gbuf3 (the .new file).  */

  f = fopen(Gbuf3, "w");

  if (!f) {
    tmp_errno = errno;
    logit(LOG_FILE, "Couldn't create Code Control save file!\nfopen failed, errno = %d\n", tmp_errno);
    wizlog(51, "&+R&-LPANIC!&N  Error creating Code Control file!");
    bak -= 2;
  } else {
    if (fwrite(buff, 1, (unsigned) write_size, f) != write_size) {
      tmp_errno = errno;
      logit(LOG_FILE, "Couldn't write the Code Control file!\nfwrite failed, errno = %d\n", tmp_errno);
      wizlog(51, "&+R&-LPANIC!&N  Error writing Code Control file!");
      bak -= 2;
    }
    fclose(f);
  }


  /* ok, we have now:
   * written <name>.new with current save data
   * read what we just wrote, and confirmed it's correct.
   * now we check for our status and move either the .bak file, or the .new file into place as the new pfile
   * it's BARELY possible that we can fail AFTER this point (we can't rename either the .bak or .new file), but
   * it's highly unlikely, nevertheless, we check.  JAB */

  switch (bak) {
    case -2: /* save FAILED, and we have NO backup! */
      logit(LOG_FILE, "Unable to save the Code Control bits, and no backup exists.");
      wizlog(51, "Unable to save  the Code Control bits and no backup exists.");
      return 0;

    case -1: /* save FAILED, but we have a backup */
      if (rename(Gbuf2, Gbuf1) == -1) {
        tmp_errno = errno;
        logit(LOG_FILE, "Unable to restore backup of the Code Control bits!  rename failed, errno = %d\n", tmp_errno);
        wizlog(51, "&+R&-LPANIC!&N  Error restoring backup of the Code Control bits!");
      } else {
        logit(LOG_FILE, "Code Control file restored from backup.");
        wizlog(51, "Code Control bits restored from backup.");
      }
      /* restored or not, the save still failed, so return 0 */
      return 0;

    case 1: /* save worked, we rename *.new to *, THEN dispose of *.bak */
    case 0: /* save worked, no *.bak file was made to begin with */
      if (rename(Gbuf3, Gbuf1) == -1) {
        tmp_errno = errno;
        logit(LOG_FILE, "Unable to rename %s to %s, serious problems! (Error %d).", Gbuf3, Gbuf1, tmp_errno);
        wizlog(51, "&+R&-LPANIC!&N  Error saving the Code Control bits file");
        if (bak == 1) {
          if (rename(Gbuf2, Gbuf1) == -1) {
            tmp_errno = errno;
            logit(LOG_FILE, "Also unable to rename %s to %s, VERY serious problems! (Error %d).",
                    Gbuf2, Gbuf1, tmp_errno);
            wizlog(51, "&+R&-LPANIC!&N  Error saving the Code Control bits!");
          } else {
            logit(LOG_FILE, "Backup %s restored instead", Gbuf2);
          }
        }
        return 0;
      } else {
        /* all kosher so far, no we remove the .bak file (if it exists) */
        if ((bak == 1) && unlink(Gbuf2)) {
          /* no rest for the weary, we can't remove the backup (probably owned by root, grrrr) */
          tmp_errno = errno;
          logit(LOG_FILE, "Unable to remove %s, sigh, errno %d", Gbuf2, tmp_errno);
          wizlog(51, "&+R&-LPANIC!&N  Error removing %s, someone go look eh?", Gbuf2);
          /* however, the save worked, so we fall through and return 1 */
        }
      }
      break;

    default:
      dump_core(); /* we screwed up somewhere */
  }

  return TRUE;

}

/*  Returns:  -1   something got messed up while trying to figure out whether
 *                 previous saves failed or suceeded
 *            -2   file open error
 *            -3   screwed up file contents
 *                                                   -- Altherog Dec 98
 */

int restoreCodeControlBits(void) {
  int i, numberOfCCBytes, size, codeVer;
  char buff[SAV_MAXSIZE * 2], *buf;
  char Gbuf1[MAX_STRING_LENGTH], Gbuf2[MAX_STRING_LENGTH];
  struct stat statbuf;
  FILE *f = NULL;

  strcpy(Gbuf1, CODE_CONTROL_FILE);
  strcpy(Gbuf2, Gbuf1);
  strcat(Gbuf2, ".new");

  if (stat(Gbuf2, &statbuf) == 0) {
    /* we had an abortive save attempt, just nuke it */
    if (unlink(Gbuf2)) {
      logit(LOG_FILE, "Cannot remove redundant Code Control file %s.", Gbuf2);
      wizlog(51, "&+R&-LWARNING!&N Cannot remove redundant Code Control file %s.", Gbuf2);
    }
  }
  if (stat(Gbuf1, &statbuf) != 0) {
    /* it ain't there, or it's munged, let's look for a backup and restore it if we find it */
    strcpy(Gbuf2, Gbuf1);
    strcat(Gbuf2, ".bak");
    if (stat(Gbuf2, &statbuf) != 0)
      return -1;
    if (rename(Gbuf2, Gbuf1) == 0) {
      logit(LOG_FILE, "%s restored from %s.", Gbuf1, Gbuf2);
    } else
      return -1;
  }
  f = fopen(Gbuf1, "r");
  if (!f)
    return -2;

  /* we have a valid pfile, check for a (now unneeded) .bak file and nuke it if we find it */
  strcpy(Gbuf2, Gbuf1);
  strcat(Gbuf2, ".bak");
  if (stat(Gbuf2, &statbuf) == 0) {
    if (unlink(Gbuf2))
      logit(LOG_FILE, "Cannot remove redundant file %s.", Gbuf2);
  }
  buf = buff;
  size = fread(buf, 1, SAV_MAXSIZE - 1, f);
  fclose(f);

  if (size < 2) {
    fprintf(stderr, "Code Control file too small! size = %d", size);
    logit(LOG_FILE, "Code Control file too small! size = %d", size);
    return -3;
  }
  codeVer = (int) GET_BYTE(buf);
  numberOfCCBytes = (int) GET_BYTE(buf);

  if (size < numberOfCCBytes + 2) {
    fprintf(stderr, "Code Control file too small! size = %d", size);
    logit(LOG_FILE, "Code Control file too small! size = %d", size);
    return -3;
  }
  // Clear the array in case we end up restoring an old version with less cbits
  for (i = 0; i < CODE_CONTROL_BYTES; i++)
    sets_code_control[i] = (char) 0;

  for (i = 0; i < numberOfCCBytes; i++)
    sets_code_control[i] = (char) GET_BYTE(buf);
  return 0;
}

/****************************************
 * TOWN JUSTICE AND JAIL FILE ROUTINES
 *
 ****************************************/

int writeWitness(char *buf, wtns_rec * rec) {
  wtns_rec *first = rec;
  char *start = buf;
  int count = 0;

  while (rec) {
    count++;
    rec = rec->next;
  }
  ADD_BYTE(buf, (char) SAV_WTNSVERS);

  rec = first;
  ADD_INT(buf, count);

  while (rec) {
    ADD_STRING(buf, rec->attacker);
    ADD_STRING(buf, rec->victim);
    ADD_INT(buf, rec->time);
    ADD_INT(buf, rec->crime);
    ADD_INT(buf, rec->room);

    rec = rec->next;
  }

  return (int) (buf - start);
}

/* function to save and restore toen justice record */
#ifdef NEWJUSTICE

int writeTownJustice(int town_nr) {
  FILE *f;
  char *buf;
  char Gbuf1[MAX_STRING_LENGTH], Gbuf2[MAX_STRING_LENGTH];
  int bak;
  static char buff[SAV_MAXSIZE * 2];
  struct stat statbuf;
  crm_rec *crec;
  int count = 0;

  if ((sizeof (char) != 1) || (int_size != long_size)) {
    logit(LOG_DEBUG, "sizeof(char) must be 1 and int_size must == long_size "
            "for player saves!\n");
    return 0;
  }
  buf = buff;

  crec = hometowns[town_nr - 1].crime_list;

  while (crec) {
    count++;
    crec = crec->next;
  }

  ADD_BYTE(buf, (char) SAV_WTNSVERS);
  ADD_BYTE(buf, (char) (short_size));
  ADD_BYTE(buf, (char) (int_size));
  ADD_BYTE(buf, (char) (long_size));

  crec = hometowns[town_nr - 1].crime_list;

  ADD_INT(buf, count);

  while (crec) {
    ADD_STRING(buf, crec->attacker);
    ADD_STRING(buf, crec->victim);
    ADD_INT(buf, crec->time);
    ADD_INT(buf, crec->crime);
    ADD_INT(buf, crec->room);
    ADD_INT(buf, crec->status);
    ADD_INT(buf, crec->money);
    crec = crec->next;
  }

  if ((int) (buf - buff) > SAV_MAXSIZE) {
    logit(LOG_PLAYER, "Could not save %d, file too large (%d bytes)",
            town_nr, (int) (buf - buff));
    return 0;
  }
  sprintf(Gbuf1, "%s/Justice/%d", SAVE_DIR, town_nr);

  strcpy(Gbuf2, Gbuf1);
  strcat(Gbuf2, ".bak");

  if (stat(Gbuf1, &statbuf) == 0) {
    if (rename(Gbuf1, Gbuf2) == -1) {
      int tmp_errno;
      tmp_errno = errno;
      logit(LOG_FILE, "Problem with justice save files directory!\n");
      logit(LOG_FILE, "   rename failed, errno = %d\n", tmp_errno);
      wizlog(51, "&+R&-LALERT!&N  Error backing up justice for %d!", town_nr);
      return 0;
    }
    bak = 1;
  } else {
    if (errno != ENOENT) {
      int tmp_errno;
      tmp_errno = errno;

      /*
       * NOTE: with the stat() function, only two errors are
       * possible: EBADF   filedes is bad. ENOENT  File does not
       * exist. Now, EBADF should only occur using fstat().
       * Therefore, if I fall into here, I have some SERIOUS
       * problems!
       */

      logit(LOG_FILE, "Problem with justice save files directory!\n");
      logit(LOG_FILE, "   stat failed, errno = %d\n", tmp_errno);
      wizlog(51, "&+R&-LALERT!&N  Error finding justice for %d!", town_nr);
      return 0;
    }
    /*
     * in this case, no original pfile existed.  Probably a new
     * char... so don't panic.
     */
    bak = 0;
  }

  f = fopen(Gbuf1, "w");

  if (!f) {
    int tmp_errno;
    tmp_errno = errno;
    logit(LOG_FILE, "Couldn't create justice save file!\n");
    logit(LOG_FILE, "   fopen failed, errno = %d\n", tmp_errno);
    wizlog(51, "&+R&-LALERT!&N  Error creating justice for %d!", town_nr);
    bak -= 2;
  } else {
    if (fwrite(buff, 1, (unsigned) (buf - buff), f) != (buf - buff)) {
      int tmp_errno;
      tmp_errno = errno;
      logit(LOG_FILE, "Couldn't write to justice save file!\n");
      logit(LOG_FILE, "   fwrite failed, errno = %d\n", tmp_errno);
      wizlog(51, "&+R&-LALERT!&N  Error writing justice for %d!", town_nr);
      fclose(f);
      bak -= 2;
    } else
      fclose(f);
  }

  switch (bak) {
    case 1: /* save worked, just get rid of the backup */
      if (unlink(Gbuf2) == -1) /* not a critical error  */
        logit(LOG_FILE, "Couldn't delete backup of justice file.\n");

    case 0: /* save worked, no backup was made to begin with
     */
      break;

    case -1: /* save FAILED, but we have a backup */
      if (rename(Gbuf2, Gbuf1) == -1) {
        int tmp_errno;
        tmp_errno = errno;
        logit(LOG_FILE, " Unable to restore backup!  Argh!");
        logit(LOG_FILE, "    rename failed, errno = %d\n", tmp_errno);
        raise(SIGSEGV);
      } else
        wizlog(51, "        Backup restored.");
      /*
       * restored or not, the save still failed, so return 0
       */
      return 0;

    case -2: /* save FAILED, and we have NO backup! */
      logit(LOG_FILE, " No restore file was made!");
      wizlog(51, "        No backup file available");
      return 0;
  }

  return 1;
}

int deleteTownJustice(int id) {
  char Gbuf1[MAX_STRING_LENGTH], Gbuf2[MAX_STRING_LENGTH];

  if (id < 0) {
    logit(LOG_EXIT, "invalid justice id %d in deleteTownJustice", id);
    raise(SIGSEGV);
  }
  sprintf(Gbuf1, "%s/Justice/%d", SAVE_DIR, id);
  strcpy(Gbuf2, Gbuf1);
  strcat(Gbuf2, ".bak");
  unlink(Gbuf1);
  unlink(Gbuf2);

  return TRUE;
}

int restoreTownJustice(int town_nr) {
  FILE *f;
  char buff[SAV_MAXSIZE];
  char *buf = buff;
  int start, size, count;
  char Gbuf1[MAX_STRING_LENGTH];
  crm_rec *crec;
  char temp;

  if (!town_nr) {
    return 0;
  }
  sprintf(Gbuf1, "%s/Justice/%d", SAVE_DIR, town_nr);

  f = fopen(Gbuf1, "r");
  if (!f) {
    logit(LOG_DEBUG, "Justice %d savefile does not exist!", town_nr);
    return 0;
  }
  size = fread(buf, 1, SAV_MAXSIZE, f);
  fclose(f);
  if (size < 4) {
    logit(LOG_FILE, "Warning: Save file less than 4 bytes.");
  }
  temp = GET_BYTE(buf);

  if ((GET_BYTE(buf) != short_size) || (GET_BYTE(buf) != int_size) ||
          (GET_BYTE(buf) != long_size)) {
    wizlog(51, "Bad file sizing for %d", town_nr);
    return 0;
  }
  if (size < 5 * int_size + 5 * sizeof (char) +long_size) {
    logit(LOG_FILE, "Warning: Save file is only %d bytes.", size);
  }
  count = GET_INTE(buf);
  for (; count > 0; count--) {
    if (!dead_crime_pool)
      dead_crime_pool = mm_create("CRIME",
            sizeof (crm_rec),
            offsetof(crm_rec,
            next), 1);

    crec = mm_get(dead_crime_pool);
    crec->attacker = GET_STRING(buf);
    crec->victim = GET_STRING(buf);
    crec->time = GET_LONG(buf);
    crec->crime = GET_INTE(buf);
    crec->room = GET_INTE(buf);
    crec->status = GET_INTE(buf);
    crec->money = GET_INTE(buf);
    crec->next = hometowns[town_nr - 1].crime_list;
    hometowns[town_nr - 1].crime_list = crec;
  }

  return (int) (buf - start);
}

void restore_town_justice(void) {
  FILE *flist;
  char Gbuf1[MAX_STRING_LENGTH], Gbuf2[MAX_STRING_LENGTH];
  char Gbuf3[MAX_STRING_LENGTH];
  struct stat statbuf;

  sprintf(Gbuf1, "%s/Justice", SAVE_DIR);
  if (stat(Gbuf1, &statbuf) == -1) {
    logit(LOG_FILE, "Justice dir");
    return;
  }
  sprintf(Gbuf2, "%s/justice_list", SAVE_DIR);
  if (stat(Gbuf2, &statbuf) == 0) {
    unlink(Gbuf2);
  } else if (errno != ENOENT) {
    logit(LOG_FILE, "justice list");
    return;
  }
  sprintf(Gbuf3, "/bin/ls -1 %s > %s", Gbuf1, Gbuf2);
  system(Gbuf3);
  flist = fopen(Gbuf2, "r");
  if (!flist) {
    logit(LOG_FILE, "Trouble opening justice files.");
    return;
  }
  while (fscanf(flist, " %s \n", Gbuf2) != EOF) {
    restoreTownJustice(atoi(Gbuf2));
  }
  clean_town_justice();
}

/* save items if a player goes to jail */

int writeJailItems(P_char ch) {
  FILE *f;
  P_obj obj, obj2;
  char *buf, *item_off, *size_off, *tmp;
  char Gbuf1[MAX_STRING_LENGTH], Gbuf2[MAX_STRING_LENGTH];
  int i, bak;
  static char buff[SAV_MAXSIZE * 2];
  struct stat statbuf;

  if (IS_MORPH(ch))
    ch = MORPH_ORIG(ch);

  if (!ch || !GET_NAME(ch))
    return 0;

  /*
   * this check assumes that the macros have not been fixed up for ** a
   * different architecture type.
   */

  if ((sizeof (char) != 1) || (int_size != long_size)) {
    logit(LOG_DEBUG, "sizeof(char) must be 1 and int_size must == long_size "
            "for player saves!\n");
    return 0;
  }
  /*
   * in case char reenters game immediately; handle rent/etc correctly
   */

  buf = buff;
  ADD_BYTE(buf, (char) SAV_SAVEVERS);
  ADD_BYTE(buf, (char) (short_size));
  ADD_BYTE(buf, (char) (int_size));
  ADD_BYTE(buf, (char) (long_size));

  item_off = buf;
  ADD_INT(buf, (int) 0);
  size_off = buf;
  ADD_INT(buf, (int) 0);

  /*
   * unequip everything and remove affects before saving
   */

  for (i = 0; i < MAX_WEAR; i++)
    if (ch->equipment[i])
      save_equip[i] = unequip_char(ch, i, FALSE);
    else
      save_equip[i] = NULL;

  all_affects(ch, FALSE); /*
                                  * reset to unaffected state
                                  */

  ADD_INT(item_off, (int) (buf - buff));

  buf += writeItems(buf, ch);

  ADD_INT(size_off, (int) (buf - buff));

  /*
   * Nuke the equip and inven (it has already been saved)
   */

  for (i = 0; i < MAX_WEAR; i++)
    if (save_equip[i]) {
      extract_obj(save_equip[i]);
      save_equip[i] = NULL;
    }
  for (obj = ch->carrying; obj; obj = obj2) {
    obj2 = obj->next_content;
    extract_obj(obj);
    obj = NULL;
  }

  all_affects(ch, TRUE); /*
                                  * reapply affects (including equip)
                                  */

  if ((int) (buf - buff) > SAV_MAXSIZE) {
    logit(LOG_PLAYER, "Could not save %s, jail file too large (%d bytes)",
            GET_NAME(ch), (int) (buf - buff));
    return 0;
  }
  sprintf(Gbuf1, "%s/Justice/", SAVE_DIR);
  tmp = Gbuf1 + strlen(Gbuf1);
  strcat(Gbuf1, GET_NAME(ch));
  for (; *tmp; tmp++)
    *tmp = LOWER(*tmp);
  strcpy(Gbuf2, Gbuf1);
  strcat(Gbuf2, ".bak");

  if (stat(Gbuf1, &statbuf) == 0) {
    if (rename(Gbuf1, Gbuf2) == -1) {
      int tmp_errno;
      tmp_errno = errno;
      logit(LOG_FILE, "Problem with jail player save files directory!\n");
      logit(LOG_FILE, "   rename failed, errno = %d\n", tmp_errno);
      wizlog(51, "&+R&-LALERT!&N  Error backing up jail file for %s!",
              GET_NAME(ch));
      return 0;
    }
    bak = 1;
  } else {
    if (errno != ENOENT) {
      int tmp_errno;
      tmp_errno = errno;

      /*
       * NOTE: with the stat() function, only two errors are
       * possible: EBADF   filedes is bad. ENOENT  File does not
       * exist. Now, EBADF should only occur using fstat().
       * Therefore, if I fall into here, I have some SERIOUS
       * problems!
       */

      logit(LOG_FILE, "Problem with jail player save files directory!\n");
      logit(LOG_FILE, "   stat failed, errno = %d\n", tmp_errno);
      wizlog(51, "&+R&-LALERT!&N  Error finding jail file for %s!",
              GET_NAME(ch));
      return 0;
    }
    /*
     * in this case, no original pfile existed.  Probably a new
     * char... so don't panic.
     */
    bak = 0;
  }

  f = fopen(Gbuf1, "w");

  /*
   * NOTE:  From this point on, if the save is not successful, then the
   * jail file will be corrupted.  While its nice to return an error, THERE
   * IS STILL A FUCKED UP JAIL PLAYER FILE!  Making the backup is a complete
   * fucking waste if you don't DO anything with it!  It will just get
   * overwritten the next time the character tries to save! Therefore,
   * I'm adding code that will rename the backup to the original if the
   * save wasn't successful.  At the same time, I'd like to officially
   * request that whoever had the wonderful idea of making a backup, but
   * not using it, be sac'ed repeatedly. (neb)
   */

  if (!f) {
    int tmp_errno;
    tmp_errno = errno;
    logit(LOG_FILE, "Couldn't create jail player save file!\n");
    logit(LOG_FILE, "   fopen failed, errno = %d\n", tmp_errno);
    wizlog(51, "&+R&-LALERT!&N  Error creating jail file for %s!",
            GET_NAME(ch));
    bak -= 2;
  } else {
    if (fwrite(buff, 1, (unsigned) (buf - buff), f) != (buf - buff)) {
      int tmp_errno;
      tmp_errno = errno;
      logit(LOG_FILE, "Couldn't write to jail player save file!\n");
      logit(LOG_FILE, "   fwrite failed, errno = %d\n", tmp_errno);
      wizlog(51, "&+R&-LALERT!&N  Error writing jail file for %s!",
              GET_NAME(ch));
      fclose(f);
      bak -= 2;
    } else
      fclose(f);
  }

  switch (bak) {
    case 1: /*
                                     * save worked, just get rid of the backup
                                     */
      if (unlink(Gbuf2) == -1) /*
                                       * not a critical error
                                     */
        logit(LOG_FILE, "Couldn't delete backup of jail player file.\n");

    case 0: /*
                                     * save worked, no backup was made to
                                     * begin with
                                     */
      break;

    case -1: /*
                                     * save FAILED, but we have a backup
                                     */
      if (rename(Gbuf2, Gbuf1) == -1) {
        int tmp_errno;
        tmp_errno = errno;
        logit(LOG_FILE, " Unable to restore backup!  Argh!");
        logit(LOG_FILE, "    rename failed, errno = %d\n", tmp_errno);
        wizlog(51, "&+R&-LALERT!&N  Error restoring backup jail file for %s!",
                GET_NAME(ch));
        raise(SIGSEGV);
      } else
        wizlog(51, "        Backup restored.");
      /*
       * restored or not, the save still failed, so return 0
       */
      return 0;

    case -2: /*
                                     * save FAILED, and we have NO backup!
                                     */
      logit(LOG_FILE, " No restore file was made!");
      wizlog(51, "        No backup file available");
      return 0;
  }

  return 1;
}

int deleteJailItems(P_char ch) {
  FILE *f;
  char buff[SAV_MAXSIZE];
  char *buf = buff;
  char Gbuf1[MAX_STRING_LENGTH];

  if (!ch)
    return -2;

  strcpy(buff, GET_NAME(ch));
  for (; *buf; buf++)
    *buf = LOWER(*buf);
  buf = buff;
  sprintf(Gbuf1, "%s/Justice/%s", SAVE_DIR, buff);

  f = fopen(Gbuf1, "r");
  if (!f)
    return -2;

  fclose(f);

  if (unlink(Gbuf1) == -1)
    logit(LOG_FILE, "Couldn't delete jail player file of %s after release.\n",
          GET_NAME(ch));

  return TRUE;

}

int restoreJailItems(P_char ch) {
  FILE *f;
  char buff[SAV_MAXSIZE];
  char *buf = buff;
  int size, csize, item_off, tmp;
  char Gbuf1[MAX_STRING_LENGTH], Gbuf2[MAX_STRING_LENGTH];
  char b_savevers;

  if (!ch)
    return -2;

  strcpy(buff, GET_NAME(ch));
  for (; *buf; buf++)
    *buf = LOWER(*buf);
  buf = buff;
  sprintf(Gbuf1, "%s/Justice/%s", SAVE_DIR, buff);

  f = fopen(Gbuf1, "r");
  if (!f)
    return -2;

  buf = buff;
  size = fread(buf, 1, SAV_MAXSIZE, f);
  fclose(f);
  if (size < 4) {
    fprintf(stderr, "Problem restoring jail player file of: %s\n",
            GET_NAME(ch));
    logit(LOG_FILE, "Problem restoring jail player file of %s.", GET_NAME(ch));
    strcpy(Gbuf2, Gbuf1);
    strcat(Gbuf2, ".bad");
    rename(Gbuf1, Gbuf2);
    return -2;
  }
  b_savevers = GET_BYTE(buf);

  if ((GET_BYTE(buf) != short_size) || (GET_BYTE(buf) != int_size) ||
          (GET_BYTE(buf) != long_size)) {
    logit(LOG_FILE, "Save file in different machine format.");
    fprintf(stderr, "Problem restoring jail player file of: %s\n",
            GET_NAME(ch));
    logit(LOG_FILE, "Problem restoring jail player file of %s.", GET_NAME(ch));
    strcpy(Gbuf2, Gbuf1);
    strcat(Gbuf2, ".bad");
    rename(Gbuf1, Gbuf2);
    return -2;
  }
  if (size < (5 * int_size + 5 * sizeof (char) +long_size)) {
    logit(LOG_FILE, "Save file is too small %d.", size);
    fprintf(stderr, "Problem restoring jail player file of: %s\n",
            GET_NAME(ch));
    logit(LOG_FILE, "Problem restoring jail player file of %s.", GET_NAME(ch));
    strcpy(Gbuf2, Gbuf1);
    strcat(Gbuf2, ".bad");
    rename(Gbuf1, Gbuf2);
    return -2;
  }
  item_off = GET_INTE(buf);
  csize = GET_INTE(buf);
  if (size != csize) {
    logit(LOG_FILE, "Save file size %d not match size read %d.", size, csize);
    fprintf(stderr, "Problem restoring jail player file of: %s\n",
            GET_NAME(ch));
    logit(LOG_FILE, "Problem restoring jail player file of %s.", GET_NAME(ch));
    strcpy(Gbuf2, Gbuf1);
    strcat(Gbuf2, ".bad");
    rename(Gbuf1, Gbuf2);
    return -2;
  }
  /*
   * This shit axed, no need for rent on a real mud. --MIAX
   */

  for (tmp = 0; tmp < MAX_WEAR; tmp++)
    save_equip[tmp] = NULL;

  if (!restoreObjects(buff + item_off, ch)) {
    fprintf(stderr, "Problem restoring jail player file of: %s\n",
            GET_NAME(ch));
    logit(LOG_FILE, "Problem restoring jail player file of %s.", GET_NAME(ch));
    strcpy(Gbuf2, Gbuf1);
    strcat(Gbuf2, ".bad");
    rename(Gbuf1, Gbuf2);
    return -2;
  }

  for (tmp = 0; tmp < MAX_WEAR; tmp++)
    if (save_equip[tmp] != NULL)
      wear(ch, save_equip[tmp], restore_wear[tmp], 0);

  if (unlink(Gbuf1) == -1)
    logit(LOG_FILE, "Couldn't delete jail player file of %s after release.\n",
          GET_NAME(ch));

  return TRUE;
}
#endif

#ifdef KINGDOM

/**************************************************************************************
 * House and Kingdom save and restore routines
 *
 */

/* house construction Q */
int writeConstructionQ() {
  FILE *f;
  char *buf;
  static char buff[SAV_MAXSIZE * 2];
  P_house_upgrade current_job;
  char fname[MAX_STRING_LENGTH];
  int count = 0;

  buf = buff;
  current_job = house_upgrade_list;

  while (current_job) {
    count++;
    current_job = current_job->next;
  }
  current_job = house_upgrade_list;

  ADD_INT(buf, count);
  while (current_job) {
    ADD_INT(buf, current_job->vnum);
    ADD_INT(buf, current_job->time);
    ADD_INT(buf, current_job->type);
    ADD_INT(buf, current_job->location);
    ADD_INT(buf, current_job->guild);
    ADD_INT(buf, current_job->exit_dir);
    ADD_INT(buf, current_job->door);
    ADD_STRING(buf, current_job->door_keyword);
    current_job = current_job->next;
  }
  if ((int) (buf - buff) > SAV_MAXSIZE) {
    logit(LOG_HOUSE, "Could not save contruction Q");
    return 0;
  }
  sprintf(fname, "%s/House/HouseConstructionQ", SAVE_DIR);
  if (!(f = fopen(fname, "w")))
    return 0;
  fwrite(buff, 1, (unsigned) (buf - buff), f);
  fclose(f);
  return 1;
}

int loadConstructionQ() {
  FILE *f;
  char buff[SAV_MAXSIZE * 2];
  char *buf = buff;
  struct house_upgrade_rec *current_job;
  char fname[MAX_STRING_LENGTH];
  int size, count;

  house_upgrade_list = NULL;
  current_job = 0;
  sprintf(fname, "%s/House/HouseConstructionQ", SAVE_DIR);
  if (!(f = fopen(fname, "r")))
    return 0;
  size = fread(buf, 1, SAV_MAXSIZE, f);
  fclose(f);
  count = GET_INTE(buf);
  for (size = 0; size < count; size++) {
    if (!dead_construction_pool)
      dead_construction_pool =
            mm_create("CONSTRUCTION", sizeof (struct house_upgrade_rec),
            offsetof(struct house_upgrade_rec, next),
            1);
    current_job = mm_get(dead_construction_pool);
    current_job->vnum = GET_INTE(buf);
    current_job->time = GET_INTE(buf);
    current_job->type = GET_INTE(buf);
    current_job->location = GET_INTE(buf);
    current_job->guild = GET_INTE(buf);
    current_job->exit_dir = GET_INTE(buf);
    current_job->door = GET_INTE(buf);
    current_job->door_keyword = GET_STRING(buf);
    if (current_job->type != HCONTROL_DESC_ROOM) {
      current_job->next = house_upgrade_list;
      house_upgrade_list = current_job;
    }
  };
  return 1;
}

/*************************************************************/

/* fonction to save and restore house */
int writeHouse(P_house house) {
  FILE *f;
  char *buf, housefile[MAX_STRING_LENGTH];
  char Gbuf1[MAX_STRING_LENGTH], Gbuf2[MAX_STRING_LENGTH];
  int bak;
  static char buff[SAV_MAXSIZE * 2];
  struct stat statbuf;
  int count = 0, tmp;

  if ((sizeof (char) != 1) || (int_size != long_size)) {
    logit(LOG_HOUSE, "sizeof(char) must be 1 and int_size must == long_size for player saves!\n");
    return 0;
  }
  buf = buff;

  ADD_BYTE(buf, (char) SAV_HOUSEVERS);
  ADD_BYTE(buf, (char) (short_size));
  ADD_BYTE(buf, (char) (int_size));
  ADD_BYTE(buf, (char) (long_size));

  ADD_INT(buf, house->vnum);
  ADD_INT(buf, house->built_on);
  ADD_SHORT(buf, house->mode);
  ADD_SHORT(buf, house->type);
  ADD_BYTE(buf, house->construction);
  ADD_STRING(buf, house->owner);

  ADD_INT(buf, house->num_of_guests);

  for (count = 0; count < house->num_of_guests; count++)
    ADD_STRING(buf, house->guests[count]);

  house->num_of_rooms = MIN(house->num_of_rooms, 20);
  ADD_INT(buf, house->num_of_rooms);
  for (count = 0; count < house->num_of_rooms; count++) {
    ADD_INT(buf, house->room_vnums[count]);
    write_guild_room(house->room_vnums[count], house->owner_guild);
  }
  ADD_INT(buf, house->owner_guild);
  ADD_INT(buf, house->last_payment);
  ADD_SHORT(buf, house->size);
  ADD_INT(buf, house->upgrades);
  ADD_INT(buf, house->exit_num);
  ADD_STRING(buf, house->entrance_keyword);
  ADD_INT(buf, house->mouth_vnum);
  ADD_INT(buf, house->teleporter1_room);
  ADD_INT(buf, house->teleporter1_dest);
  ADD_INT(buf, house->teleporter2_room);
  ADD_INT(buf, house->teleporter2_dest);
  ADD_INT(buf, house->inn_vnum);
  ADD_INT(buf, house->fountain_vnum);
  ADD_INT(buf, house->heal_vnum);
  ADD_INT(buf, house->board_vnum);
  ADD_INT(buf, house->wizard_golems);
  ADD_INT(buf, house->warrior_golems);
  ADD_INT(buf, house->cleric_golems);
  ADD_INT(buf, house->shop_vnum);
  ADD_INT(buf, house->holy_fount_vnum);
  ADD_INT(buf, house->unholy_fount_vnum);
  ADD_INT(buf, house->secret_entrance);
  if (house->type == HCONTROL_CASTLE) {
    ADD_SHORT(buf, (short) MAX_CONTROLLED_LAND);
    for (tmp = 0; tmp < MAX_CONTROLLED_LAND; tmp++)
      ADD_INT(buf, house->controlled_land[tmp]);
  }
  if ((int) (buf - buff) > SAV_MAXSIZE) {
    logit(LOG_HOUSE, "Could not save %d, file too large (%d bytes)",
            house->vnum, (int) (buf - buff));
    return 0;
  }
  //  sprintf(housefile, "%d", house->vnum);
  sprintf(housefile, "%d", house->room_vnums[0]);
  sprintf(Gbuf1, "%s/House/HouseRoom/%s", SAVE_DIR, housefile);

  strcpy(Gbuf2, Gbuf1);
  strcat(Gbuf2, ".bak");

  if (stat(Gbuf1, &statbuf) == 0) {
    if (rename(Gbuf1, Gbuf2) == -1) {
      int tmp_errno;
      tmp_errno = errno;
      logit(LOG_HOUSE, "Problem with house save files directory!\n");
      logit(LOG_HOUSE, "   rename failed, errno = %d\n", tmp_errno);
      wizlog(FORGER, "&+R&-LPANIC!&N  Error backing up house for %d!", house->vnum);
      return 0;
    }
    bak = 1;
  } else {
    if (errno != ENOENT) {
      int tmp_errno;
      tmp_errno = errno;
      logit(LOG_HOUSE, "Problem with house save files directory!\n");
      logit(LOG_HOUSE, "   stat failed, errno = %d\n", tmp_errno);
      wizlog(FORGER, "&+R&-LPANIC!&N  Error finding house for %d!", house->vnum);
      return 0;
    }
    bak = 0;
  }

  f = fopen(Gbuf1, "w");

  if (!f) {
    int tmp_errno;
    tmp_errno = errno;
    logit(LOG_HOUSE, "Couldn't create house save file!\n");
    logit(LOG_HOUSE, "   fopen failed, errno = %d\n", tmp_errno);
    wizlog(FORGER, "&+R&-LPANIC!&N  Error creating house for %d!", house->vnum);
    bak -= 2;
  } else {
    if (fwrite(buff, 1, (unsigned) (buf - buff), f) != (buf - buff)) {
      int tmp_errno;
      tmp_errno = errno;
      logit(LOG_HOUSE, "Couldn't write to house save file!\n");
      logit(LOG_HOUSE, "   fwrite failed, errno = %d\n", tmp_errno);
      wizlog(FORGER, "&+R&-LPANIC!&N  Error writing house for %d!", house->vnum);
      fclose(f);
      bak -= 2;
    } else
      fclose(f);
  }

  switch (bak) {
    case 1: /* save worked, just get rid of the backup */
      if (unlink(Gbuf2) == -1) /* not a critical error  */
        logit(LOG_HOUSE, "Couldn't delete backup of house file.\n");

    case 0: /* save worked, no backup was made to begin with */
      break;

    case -1: /* save FAILED, but we have a backup */
      if (rename(Gbuf2, Gbuf1) == -1) {
        int tmp_errno;
        tmp_errno = errno;
        logit(LOG_HOUSE, " Unable to restore backup!  Argh!");
        logit(LOG_HOUSE, "    rename failed, errno = %d\n", tmp_errno);
        raise(SIGSEGV);
      } else
        wizlog(FORGER, "        Backup restored.");
      /*
       * restored or not, the save still failed, so return 0
       */
      return 0;

    case -2: /* save FAILED, and we have NO backup! */
      logit(LOG_HOUSE, " No restore file was made!");
      wizlog(FORGER, "        No backup file available");
      return 0;
  }
  return 1;
}

int deleteHouse(char *id) {
  char Gbuf1[MAX_STRING_LENGTH], Gbuf2[MAX_STRING_LENGTH];

  sprintf(Gbuf1, "%s/House/HouseRoom/%s", SAVE_DIR, id);
  strcpy(Gbuf2, Gbuf1);
  strcat(Gbuf2, ".bak");
  unlink(Gbuf1);
  unlink(Gbuf2);

  return TRUE;
}

int restoreHouse(char *house_nr) {
  FILE *f;
  char buff[SAV_MAXSIZE];
  char *buf = buff;
  int start, size, count, tmp, s, dummy_int, version;
  char Gbuf1[MAX_STRING_LENGTH];
  P_house house;
  P_obj obj = NULL;

  if (!house_nr) {
    return 0;
  }
  sprintf(Gbuf1, "%s/House/HouseRoom/%s", SAVE_DIR, house_nr);

  f = fopen(Gbuf1, "r");
  if (!f) {
    logit(LOG_HOUSE, "House %s savefile does not exist!", house_nr);
    return 0;
  }
  size = fread(buf, 1, SAV_MAXSIZE, f);
  fclose(f);
  if (size < 4) {
    logit(LOG_HOUSE, "Warning: Save file less than 4 bytes.");
  }
  version = GET_BYTE(buf);

  if ((GET_BYTE(buf) != short_size) || (GET_BYTE(buf) != int_size) ||
          (GET_BYTE(buf) != long_size)) {
    wizlog(FORGER, "Ouch. Bad file sizing for %s", house_nr);
    return 0;
  }
  if (size < 5 * int_size + 5 * sizeof (char) +long_size) {
    logit(LOG_HOUSE, "Warning: Save file is only %d bytes.", size);
  }
  if (!dead_house_pool)
    dead_house_pool = mm_create("HOUSE", sizeof (struct house_control_rec),
          offsetof(struct house_control_rec, next), 1);
  house = mm_get(dead_house_pool);
  house->vnum = GET_INTE(buf);
  house->built_on = GET_INTE(buf);
  house->mode = GET_SHORT(buf);
  house->type = GET_SHORT(buf);
  house->construction = GET_BYTE(buf);
  house->owner = GET_STRING(buf);
  house->num_of_guests = GET_INTE(buf);

  for (count = 0; count < house->num_of_guests; count++)
    house->guests[count] = GET_STRING(buf);

  house->num_of_rooms = GET_INTE(buf);
  house->num_of_rooms = MIN(house->num_of_rooms, 20);
  for (count = 0; count < house->num_of_rooms; count++) {
    house->room_vnums[count] = GET_INTE(buf);
    if (house->room_vnums[count] < START_HOUSE_VNUM || house->room_vnums[count] > END_HOUSE_VNUM) {
      logit(LOG_HOUSE, "Troubles with room number for house %d.", house->vnum);
      logit(LOG_HOUSE, "TEST, %d.", house->num_of_rooms);
      logit(LOG_HOUSE, "TEST, %d.", house->room_vnums[count]);
      house->room_vnums[count] = -1;
      count--;
      house->num_of_rooms--;
    }
  }
  house->owner_guild = GET_INTE(buf);
  house->last_payment = GET_INTE(buf);
  /*  house->last_payment = 0;                 temp to clear them all */
  house->size = GET_SHORT(buf);
  house->upgrades = GET_INTE(buf);
  house->exit_num = GET_INTE(buf);
  house->entrance_keyword = GET_STRING(buf);
  house->mouth_vnum = GET_INTE(buf);
  house->teleporter1_room = GET_INTE(buf);
  house->teleporter1_dest = GET_INTE(buf);
  house->teleporter2_room = GET_INTE(buf);
  house->teleporter2_dest = GET_INTE(buf);
  house->inn_vnum = GET_INTE(buf);
  house->fountain_vnum = GET_INTE(buf);
  house->heal_vnum = GET_INTE(buf);
  house->board_vnum = GET_INTE(buf);
  house->wizard_golems = GET_INTE(buf);
  house->warrior_golems = GET_INTE(buf);
  house->cleric_golems = GET_INTE(buf);
  house->shop_vnum = GET_INTE(buf);
  house->holy_fount_vnum = GET_INTE(buf);
  house->unholy_fount_vnum = GET_INTE(buf);
  house->secret_entrance = GET_INTE(buf);
  if (house->type == HCONTROL_CASTLE) {
    s = GET_SHORT(buf); /* number of controlled lands */
    for (tmp = 0; tmp < MAX(s, MAX_CONTROLLED_LAND); tmp++) {
      if ((tmp < s) && (tmp < MAX_CONTROLLED_LAND)) {
        house->controlled_land[tmp] = GET_INTE(buf);
        if ((obj = read_object(HOUSE_FLAG, VIRTUAL)));
        obj_to_room(obj, real_room0(house->controlled_land[tmp]));
      } else {
        if (tmp < s) {
          /* MAX_CONTROLLED_LAND is smaller than saved version, so read and
           * discard the extra bytes */
          dummy_int = GET_INTE(buf);
        } else {
          /* number has grown, make sure new ones are nulled */
          house->controlled_land[tmp] = -1;
        }
      }
    }
  }
  house->sack_list = 0;
  house->next = first_house;
  first_house = house;

  /* fix incomplete houses */
  if (house->upgrades == -1)
    house->upgrades = 0;
  if (house->owner_guild == -1)
    house->owner_guild = 0;

  /* sets the flags, builds the walls, etc */
  construct_castle(house);

  return (int) (buf - start);
}

void restore_house(void) {
  FILE *flist;
  char Gbuf1[MAX_STRING_LENGTH], Gbuf2[MAX_STRING_LENGTH];
  char Gbuf3[MAX_STRING_LENGTH];
  struct stat statbuf;

  sprintf(Gbuf1, "%s/House/HouseRoom", SAVE_DIR);
  if (stat(Gbuf1, &statbuf) == -1) {
    perror("restore_house::stat()");
    logit(LOG_HOUSE, "Cannot open house directory [%s]", Gbuf1);
    return;
  }
  sprintf(Gbuf2, "%s/house_list", SAVE_DIR);
  if (stat(Gbuf2, &statbuf) == 0) {
    unlink(Gbuf2);
  } else if (errno != ENOENT) {
    perror("restore_house::stat()");
    logit(LOG_HOUSE, "Cannot open house list [%s]", Gbuf2);
    return;
  }
  sprintf(Gbuf3, "/bin/ls -1 %s > %s", Gbuf1, Gbuf2);
  system(Gbuf3);
  flist = fopen(Gbuf2, "r");
  if (!flist) {
    logit(LOG_HOUSE, "Troubles opening house files. [%s]", Gbuf2);
    return;
  }
  while (fscanf(flist, " %s \n", Gbuf2) != EOF) {
    restoreHouse(Gbuf2);
  }
}

void writeHouseObj(P_obj house_obj, int room_nr) {
  FILE *f;
  bool bak;
  char *buf, *size_off;
  char Gbuf1[MAX_STRING_LENGTH]; /*, Gbuf2[MAX_STRING_LENGTH]; */
  char Gbuf3[MAX_STRING_LENGTH];
  int i_count = 0;
  static char buff[SAV_MAXSIZE * 2];
  struct stat statbuf;

  if (!house_obj)
    return;

  sprintf(Gbuf1, "%s/House/HouseObj", SAVE_DIR);

  sprintf(Gbuf3, "%s/%d", Gbuf1, room_nr);
  strcpy(Gbuf1, Gbuf3);
  strcat(Gbuf1, ".bak");

  if (stat(Gbuf3, &statbuf) == 0) {
    if (rename(Gbuf3, Gbuf1) == -1) {
      logit(LOG_HOUSE, "Problem with player house_objs directory!\n");
      return;
    }
    bak = 1;
  } else {
    if (errno != ENOENT) {
      logit(LOG_HOUSE, "Problem with player house_objs directory!\n");
      return;
    }
    bak = 0;
  }

  f = fopen(Gbuf3, "w");
  if (!f) {
    logit(LOG_HOUSE, "Couldn't create house_obj save file!\n");
    return;
  }
  buf = buff;
  ADD_BYTE(buf, (char) (short_size));
  ADD_BYTE(buf, (char) (int_size));
  ADD_BYTE(buf, (char) (long_size));

  ADD_INT(buf, world[house_obj->loc.room].number);

  size_off = buf;

  ADD_INT(buf, (int) 0);

  i_count = countInven(house_obj);

  ADD_BYTE(buf, (char) SAV_ITEMVERS);
  ADD_INT(buf, i_count);

  ibuf = buf;
  save_count = 0;

  writeObjectlist(house_obj, (byte) 0);

  if (save_count != i_count)
    dump_core();

  ADD_INT(size_off, (int) (ibuf - buff));

  if (fwrite(buff, 1, (unsigned) (ibuf - buff), f) != (ibuf - buff)) {
    logit(LOG_HOUSE, "Couldn't write to house_obj save file!\n");
    fclose(f);
    return;
  }
  fclose(f);

  if (bak) {
    if (unlink(Gbuf1) == -1) {
      logit(LOG_HOUSE, "Couldn't delete backup of house obj file.\n");
    }
  }
}

void restoreHouseObj(void) {
  FILE *flist, *f;
  char Gbuf1[MAX_STRING_LENGTH], Gbuf2[MAX_STRING_LENGTH];
  char Gbuf3[MAX_STRING_LENGTH], buff[SAV_MAXSIZE], *buf;
  int size, csize, tmp;
  struct stat statbuf;

  sprintf(Gbuf1, "%s/House/HouseObj", SAVE_DIR);
  if (stat(Gbuf1, &statbuf) == -1) {
    perror("house_objs dir");
    return;
  }
  sprintf(Gbuf2, "%s/house_obj_list", SAVE_DIR);
  if (stat(Gbuf2, &statbuf) == 0) {
    unlink(Gbuf2);
  } else if (errno != ENOENT) {
    perror("house_obj_list");
    return;
  }
  sprintf(Gbuf3, "/bin/ls -1 %s > %s", Gbuf1, Gbuf2);
  system(Gbuf3); /* ls a list of house_objs dir into house_obj_list */
  flist = fopen(Gbuf2, "r");
  if (!flist)
    return;

  while (fscanf(flist, " %s \n", Gbuf2) != EOF) {
    sprintf(Gbuf3, "%s/%s", Gbuf1, Gbuf2);
    f = fopen(Gbuf3, "r");
    if (!f) {
      logit(LOG_HOUSE, "Could not restore house_obj file %s", Gbuf2);
      continue;
    }
    buf = buff;
    size = fread(buf, 1, SAV_MAXSIZE, f);
    fclose(f);

    if (size < 4) {
      fprintf(stderr, "Problem restoring house_obj: %s\n", Gbuf2);
      logit(LOG_HOUSE, "Problem restoring house_obj: %s.", Gbuf2);
      continue;
    }

    if ((GET_BYTE(buf) != short_size) || (GET_BYTE(buf) != int_size) ||
            (GET_BYTE(buf) != long_size)) {
      logit(LOG_HOUSE, "Save file %s in different machine format.", Gbuf2);
      fprintf(stderr, "Problem restoring house_obj: %s\n", Gbuf2);
      logit(LOG_HOUSE, "Problem restoring house_obj: %s.", Gbuf2);
      continue;
    }
    if (size < (5 * int_size + 5 * sizeof (char) +long_size)) {
      logit(LOG_HOUSE, "house_obj file %s is too small (%d).", Gbuf2, size);
      fprintf(stderr, "Problem restoring house_obj: %s\n", Gbuf2);
      logit(LOG_HOUSE, "Problem restoring house_obj: %s.", Gbuf2);
      continue;
    }
    sprintf(Gbuf2, "%s.bak", Gbuf3);
    if (rename(Gbuf3, Gbuf2) == -1) {
      logit(LOG_HOUSE, "Problem with player house_objs directory!\n");
      return;
    }
    tmp = GET_INTE(buf);
    if ((corpse_room = real_room(tmp)) == NOWHERE) {
      logit(LOG_HOUSE, "No room %d to load %s, loading into room 0",
              tmp, Gbuf2);
      corpse_room = 0;
    }
    csize = GET_INTE(buf);

    if (size != csize) {
      logit(LOG_HOUSE, "house_obj file %s size %d not match size read %d.",
              Gbuf2, size, csize);
      fprintf(stderr, "Problem restoring house_obj: %s\n", Gbuf2);
      logit(LOG_HOUSE, "Problem restoring house_obj: %s.", Gbuf2);
      continue;
    }
    //    if (restoreObjects(buf, 0, 0)) {
    if (restoreObjects(buf, 0)) {
      /*
       * Hack part Deux, put the loaded house_obj back
       */
      unlink(Gbuf3);
      rename(Gbuf2, Gbuf3);
      continue;
    }
  }
  fclose(flist);
}

int deleteHouseObj(int vnum) {
  char Gbuf1[MAX_STRING_LENGTH]; /*, Gbuf2[MAX_STRING_LENGTH]; */

  if (vnum < 0) {
    logit(LOG_HOUSE, "invalid house obj vnum %d in deleteHouseObj", vnum);
    raise(SIGSEGV);
  }
  sprintf(Gbuf1, "%s/House/HouseObj/%d", SAVE_DIR, vnum);
  unlink(Gbuf1);

  return TRUE;
}

#endif


#ifdef PK_BALANCE

int saveSkillAdjustments(void) {
  FILE *f;
  int i, write_size, bak, tmp_errno;
  char buff[SAV_MAXSIZE * 2], *buf;
  char Gbuf1[MAX_STRING_LENGTH], Gbuf2[MAX_STRING_LENGTH], Gbuf3[MAX_STRING_LENGTH];
  struct stat statbuf;

  buf = buff;

  ADD_BYTE(buf, (char) SAV_SKILLADJVERS);
  ADD_INT(buf, MAX_SKILLS);
  for (i = 0; i < MAX_SKILLS; i++) {
    if (sets_skill_adjustments[i] == 0.0)
      sets_skill_adjustments[i] = 1.0;
    ADD_FLOAT(buf, sets_skill_adjustments[i]);
  }

  write_size = (int) (buf - buff);
  if (write_size >= SAV_MAXSIZE) {
    logit(LOG_FILE, "Could not save the Skill Adjustments file. Too big [%d]", write_size);
    wizlog(51, "&+R&-LPANIC!&N Cannot save the Skill Adjustments file. Too big [%d]", write_size);
    return 0;
  }
  /* Enter da whole saving Shabang! */
  strcpy(Gbuf1, SKILL_ADJ_FILE);
  strcpy(Gbuf2, SKILL_ADJ_FILE);
  strcat(Gbuf2, ".bak");
  strcpy(Gbuf3, SKILL_ADJ_FILE);
  strcat(Gbuf3, ".new");

  /* Gbuf1 now holds the last correctly saved pfile,
     Gbuf2 is where we save a backup of that file,
     Gbuf3 is where we write the new one.
   */

  /* cleanup time, check to see if we have a 'new' file leftover from a previous failed attempt.  If we do,
     just dispose of it. */
  if (!stat(Gbuf3, &statbuf)) {
    /* yup, have a failed earlier save attempt, get rid of it. */
    if (unlink(Gbuf3)) {
      logit(LOG_FILE, "Cannot remove %s.new, suspending saves of skill adjustment data.");
      wizlog(51, "&+R&-LPANIC!&N  Cannot update the skill adjustment file!");
      return 0;
    }
  }
  if (stat(Gbuf1, &statbuf) == 0) {
    /* file exists, let's see about making a backup */
    if (stat(Gbuf2, &statbuf) == 0) {
      /* whups, backup exists too, we be in trouble. */
      logit(LOG_FILE, "Both a backup and a  skill adjustment file exist at the same time. You can think of it as BAD!");
      wizlog(51, "&+R&-LPANIC!&N Both a backup and a skill adjustment file exist at the same time. You can think of it as being really BAD!");
      return 0;
    }
    if (rename(Gbuf1, Gbuf2) == -1) {
      tmp_errno = errno;
      logit(LOG_FILE, "Failed rename() while saving skill adjustment data, errno = %d\n", tmp_errno);
      wizlog(51, "&+R&-LPANIC!&N  Error backing up the skill adjustment file!");
      return 0;
    }
    /* ok, we get here, we have a valid backup pfile, NO file. */
    bak = 1;
  } else {
    /* no file, may be a new char, or it may be a problem, let's see... */
    if (errno == EBADF) {
      /* NOTE: with the stat() function, only two errors are possible:
         EBADF   filedes is bad.
         ENOENT  File does not exist.
         EBADF should only occur using fstat().  Therefore, if I fall into here, I have some SERIOUS problems!  */

      logit(LOG_FILE, "Problem with skill adjustment file directory!\nstat failed, errno = %d\n", EBADF);
      wizlog(51, "&+R&-LPANIC!&N  Error finding the skill adjustment file!");
      return 0;
    }
    if (stat(Gbuf2, &statbuf) == 0) {
      /* whups, no pfile, but we have a backup.  However, if we get HERE, then something is seriously wrong, or
         someone deleted the pfile but missed the backup. (restoring from a backup pfile is handled when READING a
         pfile, not writing it.  We yell and bomb out. */
      logit(LOG_FILE, "A backup of the skill adjustment file exists but the normal save file does not.");
      wizlog(51, "&+R&-LPANIC!&N  Only a backup of the skill adjustment file exists!");
      return 0;
    }
    /* No pfile, no backup, we must be a new character, carry on... */
    bak = 0;
  }

  /* halfway home, if we get here, we have at most, one file (Gbuf2, the .bak file, which is the old file renamed.
     Now we open and write our data to Gbuf3 (the .new file).  */

  f = fopen(Gbuf3, "w");

  if (!f) {
    tmp_errno = errno;
    logit(LOG_FILE, "Couldn't create skill adjustment save file!\nfopen failed, errno = %d\n", tmp_errno);
    wizlog(51, "&+R&-LPANIC!&N  Error creating Code Control file!");
    bak -= 2;
  } else {
    if (fwrite(buff, 1, (unsigned) write_size, f) != write_size) {
      tmp_errno = errno;
      logit(LOG_FILE, "Couldn't write the skill adjustment file!\nfwrite failed, errno = %d\n", tmp_errno);
      wizlog(51, "&+R&-LPANIC!&N  Error writing skill adjustment file!");
      bak -= 2;
    }
    fclose(f);
  }


  /* ok, we have now:
   * written <name>.new with current save data
   * read what we just wrote, and confirmed it's correct.
   * now we check for our status and move either the .bak file, or the .new file into place as the new pfile
   * it's BARELY possible that we can fail AFTER this point (we can't rename either the .bak or .new file), but
   * it's highly unlikely, nevertheless, we check.  JAB */

  switch (bak) {
    case -2: /* save FAILED, and we have NO backup! */
      logit(LOG_FILE, "Unable to save the skill adjustment data, and no backup exists.");
      wizlog(51, "Unable to save the skill adjustment data and no backup exists.");
      return 0;

    case -1: /* save FAILED, but we have a backup */
      if (rename(Gbuf2, Gbuf1) == -1) {
        tmp_errno = errno;
        logit(LOG_FILE, "Unable to restore backup of the skill adjustment file!  rename failed, errno = %d\n", tmp_errno);
        wizlog(51, "&+R&-LPANIC!&N  Error restoring backup of the skill adjustment data!");
      } else {
        logit(LOG_FILE, " Skill adjustment file restored from backup.");
        wizlog(51, "Skill adjustment bits restored from backup.");
      }
      /* restored or not, the save still failed, so return 0 */
      return 0;

    case 1: /* save worked, we rename *.new to *, THEN dispose of *.bak */
    case 0: /* save worked, no *.bak file was made to begin with */
      if (rename(Gbuf3, Gbuf1) == -1) {
        tmp_errno = errno;
        logit(LOG_FILE, "Unable to rename %s to %s, serious problems! (Error %d).", Gbuf3, Gbuf1, tmp_errno);
        wizlog(51, "&+R&-LPANIC!&N  Error saving the skill adjustment file");
        if (bak == 1) {
          if (rename(Gbuf2, Gbuf1) == -1) {
            tmp_errno = errno;
            logit(LOG_FILE, "Also unable to rename %s to %s, VERY serious problems! (Error %d).",
                    Gbuf2, Gbuf1, tmp_errno);
            wizlog(51, "&+R&-LPANIC!&N  Error saving the skill adjustment file!");
          } else {
            logit(LOG_FILE, "Backup %s restored instead", Gbuf2);
          }
        }
        return 0;
      } else {
        /* all kosher so far, no we remove the .bak file (if it exists) */
        if ((bak == 1) && unlink(Gbuf2)) {
          /* no rest for the weary, we can't remove the backup (probably owned by root, grrrr) */
          tmp_errno = errno;
          logit(LOG_FILE, "Unable to remove %s, sigh, errno %d", Gbuf2, tmp_errno);
          wizlog(51, "&+R&-LPANIC!&N  Error removing %s, someone go look eh?", Gbuf2);
          /* however, the save worked, so we fall through and return 1 */
        }
      }
      break;

    default:
      dump_core(); /* we screwed up somewhere */
  }

  return TRUE;

}

/*  Returns:  -1   something got messed up while trying to figure out whether
 *                 previous saves failed or suceeded
 *            -2   file open error
 *            -3   screwed up file contents
 *                                                   -- Altherog Dec 98
 */

int restoreSkillAdjustments(void) {
  int i, numberOfBytes, size, skillAdjVer;
  char buff[SAV_MAXSIZE * 2], *buf;
  char Gbuf1[MAX_STRING_LENGTH], Gbuf2[MAX_STRING_LENGTH];
  struct stat statbuf;
  FILE *f = NULL;

  strcpy(Gbuf1, SKILL_ADJ_FILE);
  strcpy(Gbuf2, Gbuf1);
  strcat(Gbuf2, ".new");

  if (stat(Gbuf2, &statbuf) == 0) {
    /* we had an abortive save attempt, just nuke it */
    if (unlink(Gbuf2)) {
      logit(LOG_FILE, "Cannot remove redundant skill adjustment file %s.", Gbuf2);
      wizlog(51, "&+R&-LWARNING!&N Cannot remove redundant skill adjustment file %s.", Gbuf2);
    }
  }
  if (stat(Gbuf1, &statbuf) != 0) {
    /* it ain't there, or it's munged, let's look for a backup and restore it if we find it */
    strcpy(Gbuf2, Gbuf1);
    strcat(Gbuf2, ".bak");
    if (stat(Gbuf2, &statbuf) != 0) {
      for (i = 0; i < MAX_SKILLS; i++)
        sets_skill_adjustments[i] = (float) 1.0;
      return 0;
    }
    if (rename(Gbuf2, Gbuf1) == 0) {
      logit(LOG_FILE, "%s restored from %s.", Gbuf1, Gbuf2);
    } else {
      for (i = 0; i < MAX_SKILLS; i++)
        sets_skill_adjustments[i] = (float) 1.0;
      return 0;
    }
  }
  f = fopen(Gbuf1, "r");
  if (!f)
    return -2;

  /* we have a valid pfile, check for a (now unneeded) .bak file and nuke it if we find it */
  strcpy(Gbuf2, Gbuf1);
  strcat(Gbuf2, ".bak");
  if (stat(Gbuf2, &statbuf) == 0) {
    if (unlink(Gbuf2))
      logit(LOG_FILE, "Cannot remove redundant file %s.", Gbuf2);
  }
  buf = buff;
  size = fread(buf, 1, SAV_MAXSIZE - 1, f);
  fclose(f);

  if (size < 2) {
    fprintf(stderr, "Skill adjustment file too small! size = %d", size);
    logit(LOG_FILE, "Skill adjustment file too small! size = %d", size);
    return -3;
  }

  skillAdjVer = (int) GET_BYTE(buf);
  numberOfBytes = (int) GET_INTE(buf);

  if (size < numberOfBytes + 2) {
    fprintf(stderr, "Skill adjustment file too small! size = %d", size);
    logit(LOG_FILE, "Skill adjustment file too small! size = %d", size);
    return -3;
  }
  // Clear the array in case we end up restoring an old version with less cbits
  for (i = 0; i < MAX_SKILLS; i++)
    sets_skill_adjustments[i] = (float) 1.0;

  for (i = 0; i < numberOfBytes; i++)
    sets_skill_adjustments[i] = (float) GET_FLOAT(buf);
  return 0;
}
#endif

void recalcRogueHits(P_char ch) {
  int adjusted_hits, OneHpLevels = 0;

  /* basically, we need to adjust existing rogue hit points to account for
     the fact that we increased them as part of rogue upgrades. Each level
     after 25, they get one extra hit point.  Each level before 26, they
     get 2 extra (for a total boost of 75 hp's for a level 50).  */

  adjusted_hits = GET_LEVEL(ch);

  if (adjusted_hits > 25)
    OneHpLevels = (adjusted_hits - 25);

  adjusted_hits -= OneHpLevels;
  adjusted_hits *= 2;
  adjusted_hits += OneHpLevels;

  ch->points.base_hit += adjusted_hits;

  return;

}

/*-------------------------------------------------------------
------------------------Auction Save & Restore Code------------
---------------------Borrowed from house code - Kel------------
--------------------------------------------------------------*/
void writeAuctionObj(P_char ch, P_obj auction_obj, int auction_idx) {
  FILE *f;
  bool bak;
  char *buf, *size_off;
  char Gbuf1[MAX_STRING_LENGTH];
  char Gbuf3[MAX_STRING_LENGTH];
  int virtual;
  static char buff[SAV_MAXSIZE * 2];
  struct stat statbuf;

  if (!auction_obj) {
    logit(LOG_AUCTION, "Problem with Auction can't find object vnum!");
    return;
  }

  sprintf(Gbuf1, "%s/Auction/AuctionObj", SAVE_DIR);

  virtual = (auction_obj->R_num >= 0) ? obj_index[auction_obj->R_num].virtual : 0;

  /*debuggin stuff*/
  /*sprintf(tbuf, "Auction Board number: %d You Are: %s Item vnum is: %d ", auction_idx, GET_NAME(ch), virtual); */
  /*send_to_char(tbuf, ch);      */

  sprintf(Gbuf3, "%s/%d%s%d", Gbuf1, auction_idx, GET_NAME(ch), virtual);
  strcpy(Gbuf1, Gbuf3);
  strcat(Gbuf1, ".bak");

  if (stat(Gbuf3, &statbuf) == 0) {
    if (rename(Gbuf3, Gbuf1) == -1) {
      logit(LOG_AUCTION, "Problem with Auction AuctionObj's directory!\n");
      return;
    }
    bak = 1;
  } else {
    if (errno != ENOENT) {
      logit(LOG_AUCTION, "Problem with Auction AuctionObj's directory!\n");
      return;
    }
    bak = 0;
  }

  f = fopen(Gbuf3, "w");
  if (!f) {
    logit(LOG_AUCTION, "Couldn't create AuctionObj save file!\n");
    return;
  }
  buf = buff;
  ADD_BYTE(buf, (char) (short_size));
  ADD_BYTE(buf, (char) (int_size));
  ADD_BYTE(buf, (char) (long_size));

  size_off = buf;

  ADD_INT(buf, (int) 0);

  ADD_BYTE(buf, (char) SAV_ITEMVERS);

  ibuf = buf;

  writeObjectlist(auction_obj, (byte) 0);

  ADD_INT(size_off, (int) (ibuf - buff));

  if (fwrite(buff, 1, (unsigned) (ibuf - buff), f) != (ibuf - buff)) {
    logit(LOG_AUCTION, "Couldn't write to auction_obj save file!\n");
    fclose(f);
    return;
  }
  fclose(f);

  if (bak) {
    if (unlink(Gbuf1) == -1) {
      logit(LOG_AUCTION, "Couldn't delete backup of auction obj file.\n");
    }
  }
}

void restoreAuctionObj(void) {
  FILE *flist, *f;
  char Gbuf1[MAX_STRING_LENGTH], Gbuf2[MAX_STRING_LENGTH];
  char Gbuf3[MAX_STRING_LENGTH], buff[SAV_MAXSIZE], *buf;
  int size, csize, tmp;
  struct stat statbuf;

  sprintf(Gbuf1, "%s/Auction/AuctionObj", SAVE_DIR);
  if (stat(Gbuf1, &statbuf) == -1) {
    perror("auction_objs dir");
    return;
  }
  sprintf(Gbuf2, "%s/auction_obj_list", SAVE_DIR);
  if (stat(Gbuf2, &statbuf) == 0) {
    unlink(Gbuf2);
  } else if (errno != ENOENT) {
    perror("auction_obj_list");
    return;
  }
  sprintf(Gbuf3, "/bin/ls -1 %s > %s", Gbuf1, Gbuf2);
  system(Gbuf3); /* ls a list of auction_objs dir into auction_obj_list */
  flist = fopen(Gbuf2, "r");
  if (!flist)
    return;

  while (fscanf(flist, " %s \n", Gbuf2) != EOF) {
    sprintf(Gbuf3, "%s/%s", Gbuf1, Gbuf2);
    f = fopen(Gbuf3, "r");
    if (!f) {
      logit(LOG_AUCTION, "Could not restore auction_obj file %s", Gbuf2);
      continue;
    }
    buf = buff;
    size = fread(buf, 1, SAV_MAXSIZE, f);
    fclose(f);

    if (size < 4) {
      fprintf(stderr, "Problem restoring auction_obj: %s\n", Gbuf2);
      logit(LOG_AUCTION, "Problem restoring auction_obj: %s.", Gbuf2);
      continue;
    }

    if ((GET_BYTE(buf) != short_size) || (GET_BYTE(buf) != int_size) ||
            (GET_BYTE(buf) != long_size)) {
      logit(LOG_AUCTION, "Save file %s in different machine format.", Gbuf2);
      fprintf(stderr, "Problem restoring auction_obj: %s\n", Gbuf2);
      logit(LOG_AUCTION, "Problem restoring auction_obj: %s.", Gbuf2);
      continue;
    }
    if (size < (5 * int_size + 5 * sizeof (char) +long_size)) {
      logit(LOG_AUCTION, "auction_obj file %s is too small (%d).", Gbuf2, size);
      fprintf(stderr, "Problem restoring auction_obj: %s\n", Gbuf2);
      logit(LOG_AUCTION, "Problem restoring auction_obj: %s.", Gbuf2);
      continue;
    }
    sprintf(Gbuf2, "%s.bak", Gbuf3);
    if (rename(Gbuf3, Gbuf2) == -1) {
      logit(LOG_AUCTION, "Problem with player auction_objs directory!\n");
      return;
    }
    tmp = GET_INTE(buf);
    if ((corpse_room = real_room(tmp)) == NOWHERE) {
      logit(LOG_AUCTION, "No room %d to load %s, loading into room 0",
              tmp, Gbuf2);
      corpse_room = 0;
    }
    csize = GET_INTE(buf);

    if (size != csize) {
      logit(LOG_AUCTION, "auction_obj file %s size %d not match size read %d.",
              Gbuf2, size, csize);
      fprintf(stderr, "Problem restoring auction_obj: %s\n", Gbuf2);
      logit(LOG_AUCTION, "Problem restoring auction_obj: %s.", Gbuf2);
      continue;
    }
    //    if (restoreObjects(buf, 0, 0)) {
    if (restoreObjects(buf, 0)) {
      /*
       * Hack part Deux, put the loaded auction_obj back
       */
      unlink(Gbuf3);
      rename(Gbuf2, Gbuf3);
      continue;
    }
  }
  fclose(flist);
}

int deleteAuctionObj(int vnum) {
  char Gbuf1[MAX_STRING_LENGTH]; /*, Gbuf2[MAX_STRING_LENGTH]; */

  if (vnum < 0) {
    logit(LOG_AUCTION, "invalid auction obj vnum %d in deleteAuctionObj", vnum);
    raise(SIGSEGV);
  }
  sprintf(Gbuf1, "%s/Auction/AuctionObj/%d", SAVE_DIR, vnum);
  unlink(Gbuf1);

  return TRUE;
}


// Storage Stuff -- CRM

void writeStorageCache(P_obj cache) {
  FILE *f;
  P_obj hold_content = NULL;
  char *buf, *size_off, Gbuf1[MAX_STRING_LENGTH];
  int i_count = 0;
  static char buff[SAV_MAXSIZE * 2];
  char name_save[MAX_STRING_LENGTH], hold[MAX_STRING_LENGTH];
  char sign;

  // Sanity Check
  if (!cache || (cache->type != ITEM_CONTAINER))
    dump_core();

  // Sanity Check part deux
  if ((cache->loc.room <= NOWHERE) || (cache->loc.room > top_of_world))
    dump_core();

  // String the name of the cache, for access reasons
  sscanf(cache->name, "%s ", name_save);

  strcpy(hold, cache->name);

  free_string(cache->name);

  cache->name = strdup(name_save);

  // Grab first letter of the name, for directory sorting
  sign = LOWER(*name_save);

  // Append room number to player name for file identifier
  sprintf(Gbuf1, "%s/Storage/%c/%s.%d", SAVE_DIR, sign, name_save, world[cache->loc.room].number);

  f = fopen(Gbuf1, "w");
  if (!f) {
    logit(LOG_FILE, "Couldn't create Cache save file!\n");
    return;
  }

  buf = buff;
  ADD_BYTE(buf, (char) (short_size));
  ADD_BYTE(buf, (char) (int_size));
  ADD_BYTE(buf, (char) (long_size));

  ADD_INT(buf, world[cache->loc.room].number); /* reload room (VIRTUAL) */

  size_off = buf; /* needed to make sure it's not corrupt */
  ADD_INT(buf, (int) 0);

  /* have to hold the 'next_content' of corpse, as this is stuff in the room and not to be saved.  Replaced after
     it's saved.  JAB */

  hold_content = cache->next_content;
  cache->next_content = NULL;

  i_count = countInven(cache);

  ADD_BYTE(buf, (char) SAV_ITEMVERS);
  ADD_INT(buf, i_count);

  ibuf = buf;
  save_count = 0;

  writeObjectlist(cache, (byte) 0);

  cache->next_content = hold_content;

  if (save_count != i_count)
    dump_core();

  ADD_INT(size_off, (int) (ibuf - buff));

  if (fwrite(buff, 1, (unsigned) (ibuf - buff), f) != (ibuf - buff)) {
    logit(LOG_FILE, "Couldn't write to Cache save file!\n");
    fclose(f);
    return;
  }
  fclose(f);


  free_string(cache->name);
  cache->name = strdup(hold);
}

void restoreStorageCache(P_char ch, P_char tch) {
  FILE *f;
  char Gbuf1[MAX_STRING_LENGTH], buff[SAV_MAXSIZE], *buf;
  int size, csize, tmp;
  struct stat statbuf;
  char name_save[MAX_INPUT_LENGTH];
  char sign;

  // Sanity Check
  if (!(ch))
    dump_core();


  if (!tch)
    tch = ch;

  strcpy(name_save, GET_NAME(ch));

  sign = LOWER(*name_save);

  // Setup the file string
  sprintf(Gbuf1, "%s/Storage/%c/%s_cache.%d", SAVE_DIR, sign, GET_NAME(ch), world[tch->in_room].number);

  // Der, doesn't exist
  if (stat(Gbuf1, &statbuf) == -1)
    return;

  f = fopen(Gbuf1, "r");
  if (!f) {
    logit(LOG_DEBUG, "Could not restore Cache file %s", Gbuf1);
    return;
  }

  buf = buff;
  size = fread(buf, 1, SAV_MAXSIZE - 1, f);
  fclose(f);

  if (size < 4)
    goto cf_error;

  if ((GET_BYTE(buf) != short_size) || (GET_BYTE(buf) != int_size) || (GET_BYTE(buf) != long_size)) {
    logit(LOG_FILE, "Save file %s in different machine format.", Gbuf1);
    goto cf_error;
  }

  if (size < (5 * int_size + 5 * sizeof (char) +long_size)) {
    logit(LOG_FILE, "Cache file %s is too small (%d).", Gbuf1, size);
    goto cf_error;
  }

  tmp = GET_INTE(buf);

  if ((cache_room = real_room(tmp)) == NOWHERE) {
    logit(LOG_FILE, "No room %d to load %s, loading into room 0", tmp, Gbuf1);
    cache_room = 0;
  }

  csize = GET_INTE(buf);

  if (size != csize) {
    logit(LOG_FILE, "Cache file %s size %d not match size read %d.", Gbuf1, size, csize);
    goto cf_error;
  }

  restoreObjects(buf, 0);
  return;

cf_error:
  fprintf(stderr, "Problem restoring cache: %s\n", Gbuf1);
  logit(LOG_FILE, "Problem restoring cache: %s.", Gbuf1);

}

bool VerifyStorage(P_char ch) {

  char Gbuf1[MAX_STRING_LENGTH];
  struct stat statbuf;
  char sign;

  strcpy(Gbuf1, GET_NAME(ch));

  sign = LOWER(*Gbuf1);

  if (!(ch) || (ch->in_room) <= NOWHERE) {
    logit(LOG_EXIT, "Bogus parms in VerifyStorage!");
    dump_core();
  }

  sprintf(Gbuf1, "%s/Storage/%c/%s_cache.%d", SAVE_DIR, sign, GET_NAME(ch), world[ch->in_room].number);

  // Der, doesn't exist
  if (stat(Gbuf1, &statbuf) == -1)
    return FALSE;
  else
    return TRUE;
}

bool VerifyStorageRemote(P_char ch, P_char tch) {

  char Gbuf1[MAX_STRING_LENGTH];
  struct stat statbuf;
  char sign;

  if (!(ch)) {
    logit(LOG_EXIT, "Bogus parms in VerifyStorage!");
    dump_core();
  }

  strcpy(Gbuf1, GET_NAME(ch));

  sign = LOWER(*Gbuf1);

  sprintf(Gbuf1, "%s/Storage/%c/%s_cache.%d", SAVE_DIR, sign, GET_NAME(ch), world[tch->in_room].number);

  // Der, doesn't exist
  if (stat(Gbuf1, &statbuf) == -1)
    return FALSE;
  else
    return TRUE;
}

// Silently revoke all commands and regrant based on level/sphere

void convertGrantFlags(P_char ch) {

  int sphere = 0, loopvar = 0;

  // Sanity Check
  if (!(ch) || (GET_LEVEL(ch) < 51) || IS_NPC(ch)) {
    logit(LOG_EXIT, "convertGrantFlags(): bogus parms, dumping core...");
    dump_core();
  }

  // Clear 'em
  CLEAR_CBITS(ch->only.pc->grant, GRANT_BYTES);

  // Snag their sphere
  if (IS_CSET(ch->only.pc->pcact, PLR_G_ADMIN))
    sphere = 1;
  else if (IS_CSET(ch->only.pc->pcact, PLR_G_AREAS))
    sphere = 2;
  else if (IS_CSET(ch->only.pc->pcact, PLR_G_CODER))
    sphere = 3;
  else if (IS_CSET(ch->only.pc->pcact, PLR_G_WWW))
    sphere = 4;
  else if (IS_CSET(ch->only.pc->pcact, PLR_G_QUEST))
    sphere = 5;

  // Set sphere bit
  SET_CBIT(ch->only.pc->grant, sphere);

  // Loop through the granted commands and give em their stuff
  for (loopvar = 5; *grantable_bits[loopvar] != '\n'; loopvar++) {
    if (grant_cmds[loopvar - 1][sphere] <= GET_LEVEL(ch))
      SET_CBIT(ch->only.pc->grant, loopvar);
  }

  // Log it and we're done
  logit(LOG_PLAYER, "%s wiz commands successfully converted.", GET_NAME(ch));
}

