/* ***************************************************************************
 *  File: nanny.c                                            Part of Outcast *
 *  Usage: handle non-playing sockets (new character creation too)           *
 *  Copyright  1990, 1991 - see 'license.doc' for complete information.      *
 *  Copyright  1994, 1995 - Outcast Systems Ltd.                             *
 ****************************************************************************/

#include <arpa/telnet.h>
#include <ctype.h>
#include <stdio.h>
#include <string.h>
#include <sys/stat.h>
#include <time.h>
#include <errno.h>
#define _XOPEN_SOURCE    /* RedHat 5.0 fix */
#define __USE_XOPEN
#include <unistd.h>

#include "comm.h"
#include "config.h"
#include "db.h"
#include "email_reg.h"
#include "events.h"
#include "interp.h"
#include "language.h"
#ifdef NEWJUSTICE
#include "newjustice.h"
#endif
#ifdef OLDJUSTICE
#include "justice.h"
#endif
#include "prototypes.h"
#include "skillrec.h"
#include "spells.h"
#include "race_class.h"
#include "utils.h"
#include "mm.h"
#include "mmail.h"
#include "mccp.h"


/* external variables */

extern FILE *help_fl;
extern FILE *info_fl;
extern P_char PC_list;
extern bool pclist_debug;
extern P_desc descriptor_list;
extern P_event event_type_list[];
extern P_room world;
extern P_obj object_list;
extern char *stat_names[];
extern char valid_term_list[];
extern const char *class_types[];
extern const char *command[];
extern const char *fill[];
extern const char *hometown_names[];
extern const char *race_types[];
extern const struct stat_data stat_factor[];
extern const struct trait_data racial_traits[];
extern int PC_count;
extern int EMS_DEBUG;
extern int align_hometowns[][4];
extern int avail_descs;
extern int avail_hometowns[][PC_RACES];
extern int class_table[PC_RACES][TOTALCLASS];
extern int guild_locations[][TOTALCLASS];
extern int help_array[27][2];
extern int innate_abilities[];
extern int max_users_playing;
extern int min_stats_for_class[TOTALCLASS][8];
extern int race_size[][12];
extern int top_of_helpt;
extern int top_of_infot;
extern int used_descs;
extern struct con_app_type con_app[];
extern struct help_index_element *help_index;
extern struct info_index_element *info_index;
extern struct mm_ds *dead_char_pool;
extern struct str_app_type str_app[];
extern struct zone_data *zone_table;
extern struct ban_t *ban_list;
extern ubyte sets_code_control[CODE_CONTROL_BYTES];
extern struct email_registration_data *EMSD;
extern const long boot_time;
extern short guild_spell_lvl[MAX_SKILLS + 1][TOTALCLASS];
extern short guild_spell_order[MAX_SKILLS + 1][TOTALCLASS];
extern short guild_spell_orderly_lvl[MAX_SKILLS + 1][TOTALCLASS];
extern int port;

extern char Gbuf1[MAX_STRING_LENGTH], Gbuf2[MAX_STRING_LENGTH], Gbuf3[MAX_STRING_LENGTH], Gbuf4[MAX_STRING_LENGTH];


/*========================================================================*/
/*
 *    Global variables
 */
/*========================================================================*/

int game_locked = 0;
struct mm_ds *dead_pc_pool = NULL;


/* Load objects to newbies.  Reworked for specific race/class items */

/*                                                      - 12/98 CRM */
static void LoadNewbieGear(P_char ch, int *items) {
  int i;
  P_obj obj;

  for (i = 0; items[i] != -1; i++) {
    if ((obj = read_object(items[i], VIRTUAL)) == NULL) {
      logit(LOG_DEBUG, "Cannot load init item with virtual number: %d for %s", items[i], GET_NAME(ch));
    } else {
      SET_BIT(obj->extra_flags, ITEM_NOSELL); /* shopkeepers won't touch newbie gear */

      /* two of these may be with the character for a long time (bag, spellbook), weapons are fumbled,
         and pen would be very annoying */
      if ((obj->type != ITEM_CONTAINER) &&
              (obj->type != ITEM_SPELLBOOK) &&
              (obj->type != ITEM_WEAPON) &&
              (obj->type != ITEM_INSTRUMENT) &&
              (obj->type != ITEM_PEN))
        SET_BIT(obj->extra_flags, ITEM_TRANSIENT);
      obj_to_char(obj, ch);
    }
  }
}

void ScribeNewbieSpells(P_char ch) {
  P_obj book;
  int i;

  for (book = ch->carrying; book; book = book->next) {
    if (book->type == ITEM_SPELLBOOK) {
      book->value[0] = BOUNDED(1, GET_LANGUAGE(ch, TONGUE_USING), TONGUE_GOD);
      book->value[1] = GET_CLASS(ch);

      for (i = 0; i < 500; i++) {
        if ((skills[i].spell_pointer == NULL) || (skills[i].harmful == -1) ||
                (skills[i].class[GET_CLASS(ch) - 1].rlevel != 1))
          continue;

        // Found one
        AddSpellsToSpellBook(ch, book, i);
        book->value[2]--;
      }
      break;
    }
  }
}

int AddSpellsToSpellBook(P_char ch, P_obj obj, int spl) {
  struct extra_descr_data *tmp;
  int i, magic_num = (MAX_SKILLS + 1) / 8;

  if (!(tmp = find_spell_description(obj))) {
    /* creation blues */
#ifdef MEM_DEBUG
    mem_use[MEM_E_DSCR] += sizeof (struct extra_descr_data);
#endif
    CREATE(tmp, struct extra_descr_data, 1);

    tmp->next = obj->ex_description;
    obj->ex_description = tmp;
    sprintf(Gbuf1, "%c%c%c", (char) 3, (char) 1, (char) 3);
    tmp->keyword = str_dup(Gbuf1);
#ifdef MEM_DEBUG
    mem_use[MEM_STRINGS] += magic_num + 1;
#endif
    CREATE(tmp->description, char, (unsigned) (magic_num + 1));
    for (i = 0; i < (magic_num + 1); i++)
      tmp->description[i] = 0;
  }
  if (SpellInThisSpellBook(tmp, spl))
    return FALSE;
  if (!obj->value[1])
    obj->value[1] = GET_CLASS(ch);
  if (GET_LANGUAGE(ch, GET_LANGUAGE(ch, TONGUE_USING)) < 70) {
    send_to_char("You must have mastery of the tongue you wish to use in scribing.\n", ch);
    return FALSE;
  }
  if (obj->value[0] && (obj->value[0] != GET_LANGUAGE(ch, TONGUE_USING))) {
    send_to_char("Mixing languages in spellbooks is not yet allowed.\n", ch);
    return FALSE;
  }

  obj->value[0] = BOUNDED(1, GET_LANGUAGE(ch, TONGUE_USING), TONGUE_GOD);

  /* not there, we've gotta add it */
  SET_CBIT(tmp->description, skills[spl].pindex);

  /* ok, we done, we happy campers. */
  return TRUE;
}


/* Extensively modified for race/class specific newbie items */

/* 12/23/98 -- CRM (Code) && Rhadin (Items)                  */
void load_obj_to_newbies(P_char ch) {
  char buf[MAX_STRING_LENGTH];

  /**********************
   * START RACE/CLASS EQ *
   ***********************/

  /* HUMAN EQ */

  static int human_eq[] ={/* Bag, 4x rations, 2x torch, waterskin */
    19780, 638, 638, 638, 638, 2820, 2820, 14017, -1
  };

  static int human_spellbook[] ={659, 608, -1};

  static int human_warrior[] ={58971, 58963, 58967, 38087, 58902, 80757, 58901, -1};

  static int human_mage[] ={646, 523, 650, 645, 602, 586, 533, -1};

  static int human_rogue[] ={83055, 83055, 646, 644, 451, 533, 637, 648, 630, 660, -1};

  static int human_cleric[] ={649, 642, 646, 523, 647, 639, 643, -1};

  static int human_bard[] ={632, 633, 634, 645, 637, 651, 618, 464, 62751, 62750, -1};

  static int human_ranger[] ={629, 643, 644, 631, 467, 630, 648, -1};

  static int human_druid[] ={629, 523, 645, 647, 628, 450, 652, -1};

  static int human_paladin[] ={58971, 58963, 58967, 38087, 58902, 80757, 58901, 8000, -1};

  static int human_antipaladin[] ={58971, 58963, 58967, 38087, 58902, 80757, 58901, 96678, -1};

  /* BARBARIAN EQ */

  static int barb_eq[] ={/* Bag, 4x rations, 2x torch, waterskin */
    19780, 638, 638, 638, 638, 2820, 2820, 14017, -1
  };

  static int barb_warrior[] ={58971, 58963, 58967, 38087, 58902, 80757, 58901, -1};

  static int barb_shaman[] ={477, 472, 476, 474, 473, 478, 475, -1};

  /* GREY ELF EQ */

  static int elf_eq[] ={/* Bag, 4x rations, 2x torch, waterskin */
    19780, 638, 638, 638, 638, 2820, 2820, 14017, -1
  };

  static int elf_spellbook[] ={466, 465, -1};

  static int elf_cleric[] ={453, 452, 450, 460, 457, 454, 459, -1};

  static int elf_warrior[] ={456, 455, 450, 460, 467, 454, 451, -1};

  static int elf_mage[] ={453, 455, 452, 460, 462, 454, 459, -1};

  static int elf_rogue[] ={83055, 83055, 456, 455, 450, 461, 462, 454, 451, 463, -1};

  static int elf_druid[] ={453, 452, 450, 460, 457, 454, 459, -1};

  static int elf_ranger[] ={456, 455, 450, 460, 467, 454, 451, -1};

  static int elf_bard[] ={453, 455, 450, 461, 462, 454, 459, 464, 62751, 62750, -1};

  /* YUAN-TI EQ */

  static int yuanti_eq[] ={/* Bag, 4x rations, 2x torch, waterskin */
    19780, 638, 638, 638, 638, 2820, 2820, 14017, -1
  };

  static int yuanti_spellbook[] ={502, 503, -1};

  static int yuanti_cleric[] ={488, 491, 489, 492, 490, 493, 494, -1};

  static int yuanti_warrior[] ={58971, 58963, 58967, 38087, 58902, 80757, 58901, -1};

  static int yuanti_mage[] ={495, 491, 489, 492, 496, 493, 497, -1};

  static int yuanti_rogue[] ={83055, 83055, 488, 491, 489, 492, 496, 499, 497, 610, -1};

  /* TROLL EQ */

  static int troll_eq[] ={/* Bag, 4x rations, 2x torch, waterskin */
    19780, 638, 638, 638, 638, 2820, 2820, 14017, -1
  };

  static int troll_shaman[] ={506, 514, 511, 510, 509, 512, 513, -1};

  static int troll_warrior[] ={58971, 58963, 58967, 38087, 58902, 80757, 58901, -1};

  /* DUERGAR EQ */

  static int duergar_eq[] ={/* Bag, 4x rations, 2x torch, waterskin */
    19780, 638, 638, 638, 638, 2820, 2820, 14017, -1
  };

  static int duergar_spellbook[] ={572, 573, -1, -1, -1, -1, -1, -1};

  static int duergar_warrior[] ={58971, 58963, 58967, 38087, 58902, 80757, 58901, -1};

  static int duergar_cleric[] ={561, 562, 563, 564, 566, 567, 569, -1};

  static int duergar_rogue[] ={83055, 83055, 561, 562, 563, 564, 567, 568, 570, 535, -1};

  static int duergar_mage[] ={562, 564, 567, 568, 570, 563, 571, -1};

  /* OGRE EQ */

  static int ogre_eq[] ={/* Bag, 4x rations, 2x torch, waterskin, map (!) */
    19780, 638, 638, 638, 638, 2820, 2820, 14017, -1
  };

  static int ogre_warrior[] ={58971, 58963, 58967, 38087, 58902, 80757, 58901, -1};

  static int ogre_shaman[] ={574, 575, 576, 577, 578, 579, 580, -1};

  /* GNOME EQ */

  static int gnome_eq[] ={/* Bag, 4x rations, 2x torch, waterskin */
    19780, 638, 638, 638, 638, 2820, 2820, 14017, -1
  };

  static int gnome_spellbook[] ={551, 553, -1};

  static int gnome_cleric[] ={554, 544, 549, 543, 546, 540, 545, -1};

  static int gnome_warrior[] ={58971, 58963, 58967, 38087, 58902, 80757, 58901, -1};

  static int gnome_mage[] ={540, 542, 545, 546, 549, 557, -1};

  static int gnome_rogue[] ={83055, 83055, 542, 544, 545, 546, 547, 549, 555, -1};

  /* HALFLING EQ */

  static int halfling_eq[] ={/* Bag, 4x rations, 2x torch, waterskin */
    19780, 638, 638, 638, 638, 2820, 2820, 14017, -1
  };

  static int halfling_spellbook[] ={608, 609, -1};

  static int halfling_cleric[] ={596, 597, 598, 600, 601, 603, -1};

  static int halfling_warrior[] ={58971, 58963, 58967, 38087, 58902, 80757, 58901, -1};

  static int halfling_mage[] ={596, 597, 598, 601, 602, 604, -1};

  static int halfling_rogue[] ={83055, 83055, 596, 597, 598, 600, 601, 602, 660, -1};

  static int halfling_bard[] ={596, 597, 598, 600, 601, 602, 660, 464, 62751, 62750, -1};

  /* HALF-ELF EQ */

  static int halfelf_eq[] ={/* Bag, 4x rations, 2x torch, waterskin */
    19780, 638, 638, 638, 638, 2820, 2820, 14017, -1
  };

  //  static int halfelf_spellbook[] =
  //  {465, 466, -1};

  static int halfelf_druid[] ={460, 454, 450, 459, 628, 455, 629, -1};

  static int halfelf_ranger[] ={58971, 58963, 58967, 38087, 58902, 80757, 58901, -1};

  static int halfelf_bard[] ={628, 460, 454, 455, 632, 633, 634, 464, -1};

  /* MOUNTAIN DWARF EQ */

  static int dwarf_eq[] ={/* Bag, 4x rations, 2x torch, waterskin */
    19780, 638, 638, 638, 638, 2820, 2820, 14017, -1
  };

  static int dwarf_warrior[] ={58971, 58963, 58967, 38087, 58902, 80757, 58901, -1};

  static int dwarf_cleric[] ={613, 616, 617, 618, 620, 622, 491, -1};

  static int dwarf_rogue[] ={83055, 83055, 615, 616, 617, 618, 623, 624, 625, 660, -1};

  static int dwarf_bard[] ={616, 633, 624, 623, 632, 455, 658, 464, 615, 62751, 62750, -1};

  /* DROW EQ */

  static int drow_eq[] ={/* Bag, 4x rations, 2x torch, waterskin */
    19780, 638, 638, 638, 638, 2820, 2820, 14017, -1
  };

  static int drow_spellbook[] ={534, 536, -1};

  static int drow_warrior[] ={58971, 58963, 58967, 38087, 58902, 80757, 58901, -1};

  static int drow_cleric[] ={519, 523, 528, 522, 529, 524, 531, -1};

  static int drow_rogue[] ={83055, 83055, 521, 525, 526, 527, 530, 532, 533, 535, -1};

  static int drow_mage[] ={521, 522, 523, 528, 529, 531, 532, -1};

  /* ILLITHID EQ */

  static int illithid_eq[] ={/* Bag, 4x rations, 2x torch, waterskin */
    19780, 638, 638, 638, 638, 2820, 2820, 14017, -1
  };

  static int illithid_psi[] ={585, 586, 587, 588, 589, 590, 591, -1};

  /* ORC EQ */

  static int orc_eq[] ={/* Bag, 4x rations, 2x torch, waterskin */
    19780, 638, 638, 638, 638, 2820, 2820, 14017, -1
  };

  static int orc_warrior[] ={58971, 58963, 58967, 38087, 58902, 80757, 58901, -1};

  static int orc_rogue[] ={83055, 83055, 646, 644, 451, 533, 637, 648, 630, 660, -1};

  static int orc_battlechanter[] ={632, 633, 634, 645, 637, 651, 618, 5556, 62751, 62750, -1};

  static int orc_direraider[] ={58971, 58963, 58967, 38087, 58902, 80757, 58901, -1};

  static int orc_shaman[] ={477, 472, 476, 474, 473, 478, 475, -1};

  /********************
   * END RACE/CLASS EQ *
   ********************/

  if (ch->carrying) /* we are _NOT_ here to give people free eq many times */
    return;

  switch (GET_RACE(ch)) {

    case RACE_HUMAN:
      LoadNewbieGear(ch, human_eq);
      switch (GET_CLASS(ch)) {

        case CLASS_WARRIOR:
          LoadNewbieGear(ch, human_warrior);
          break;
        case CLASS_ENCHANTER:
        case CLASS_INVOKER:
        case CLASS_CONJURER:
        case CLASS_ILLUSIONIST:
        case CLASS_NECROMANCER:
        case CLASS_ELEMENTALIST:
          LoadNewbieGear(ch, human_spellbook);
          LoadNewbieGear(ch, human_mage);
          ScribeNewbieSpells(ch);
          break;
        case CLASS_CLERIC:
          LoadNewbieGear(ch, human_cleric);
          break;
        case CLASS_DRUID:
          LoadNewbieGear(ch, human_druid);
          break;
        case CLASS_RANGER:
          LoadNewbieGear(ch, human_ranger);
          break;
        case CLASS_PALADIN:
          LoadNewbieGear(ch, human_paladin);
          break;
        case CLASS_ANTIPALADIN:
          LoadNewbieGear(ch, human_antipaladin);
          break;
        case CLASS_THIEF:
        case CLASS_ASSASSIN:
        case CLASS_ROGUE:
          LoadNewbieGear(ch, human_rogue);
          break;
        case CLASS_BARD:
          LoadNewbieGear(ch, human_bard);
          break;
      }
      break;

    case RACE_BARBARIAN:
      LoadNewbieGear(ch, barb_eq);
      switch (GET_CLASS(ch)) {

        case CLASS_WARRIOR:
          LoadNewbieGear(ch, barb_warrior);
          break;
        case CLASS_SHAMAN:
          LoadNewbieGear(ch, barb_shaman);
          break;
      }
      break;

    case RACE_GREY:
      LoadNewbieGear(ch, elf_eq);
      switch (GET_CLASS(ch)) {

        case CLASS_WARRIOR:
          LoadNewbieGear(ch, elf_warrior);
          break;
        case CLASS_CLERIC:
          LoadNewbieGear(ch, elf_cleric);
          break;
        case CLASS_INVOKER:
        case CLASS_ENCHANTER:
        case CLASS_ELEMENTALIST:
          LoadNewbieGear(ch, elf_spellbook);
          LoadNewbieGear(ch, elf_mage);
          ScribeNewbieSpells(ch);
          break;
        case CLASS_DRUID:
          LoadNewbieGear(ch, elf_druid);
          break;
        case CLASS_RANGER:
          LoadNewbieGear(ch, elf_ranger);
          break;
        case CLASS_THIEF:
        case CLASS_ASSASSIN:
        case CLASS_ROGUE:
          LoadNewbieGear(ch, elf_rogue);
          break;
        case CLASS_BARD:
          LoadNewbieGear(ch, elf_bard);
          break;
      }
      break;

    case RACE_MOUNTAIN:
      LoadNewbieGear(ch, dwarf_eq);
      switch (GET_CLASS(ch)) {

        case CLASS_WARRIOR:
          LoadNewbieGear(ch, dwarf_warrior);
          break;
        case CLASS_CLERIC:
          LoadNewbieGear(ch, dwarf_cleric);
          break;
        case CLASS_THIEF:
        case CLASS_ASSASSIN:
        case CLASS_ROGUE:
          LoadNewbieGear(ch, dwarf_rogue);
          break;
        case CLASS_BARD:
          LoadNewbieGear(ch, dwarf_bard);
          break;
      }
      break;

    case RACE_GNOME:
      LoadNewbieGear(ch, gnome_eq);
      switch (GET_CLASS(ch)) {

        case CLASS_WARRIOR:
          LoadNewbieGear(ch, gnome_warrior);
          break;
        case CLASS_CLERIC:
          LoadNewbieGear(ch, gnome_cleric);
          break;
        case CLASS_INVOKER:
        case CLASS_ENCHANTER:
        case CLASS_ILLUSIONIST:
        case CLASS_CONJURER:
        case CLASS_ELEMENTALIST:
          LoadNewbieGear(ch, gnome_spellbook);
          LoadNewbieGear(ch, gnome_mage);
          ScribeNewbieSpells(ch);
          break;
        case CLASS_THIEF:
        case CLASS_ASSASSIN:
        case CLASS_ROGUE:
          LoadNewbieGear(ch, gnome_rogue);
          break;
      }
      break;

    case RACE_HALFLING:
      LoadNewbieGear(ch, halfling_eq);
      switch (GET_CLASS(ch)) {

        case CLASS_WARRIOR:
          LoadNewbieGear(ch, halfling_warrior);
          break;
        case CLASS_CLERIC:
          LoadNewbieGear(ch, halfling_cleric);
          break;
        case CLASS_INVOKER:
        case CLASS_ENCHANTER:
          LoadNewbieGear(ch, halfling_spellbook);
          LoadNewbieGear(ch, halfling_mage);
          ScribeNewbieSpells(ch);
          break;
        case CLASS_THIEF:
        case CLASS_ASSASSIN:
        case CLASS_ROGUE:
          LoadNewbieGear(ch, halfling_rogue);
          break;
        case CLASS_BARD:
          LoadNewbieGear(ch, halfling_bard);
          break;

      }
      break;

    case RACE_HALFELF:
      LoadNewbieGear(ch, halfelf_eq);
      switch (GET_CLASS(ch)) {

        case CLASS_RANGER:
          LoadNewbieGear(ch, halfelf_ranger);
          break;
        case CLASS_DRUID:
          LoadNewbieGear(ch, halfelf_druid);
          break;
        case CLASS_BARD:
          LoadNewbieGear(ch, halfelf_bard);
          break;

      }
      break;

    case RACE_TROLL:
      LoadNewbieGear(ch, troll_eq);
      switch (GET_CLASS(ch)) {

        case CLASS_WARRIOR:
          LoadNewbieGear(ch, troll_warrior);
          break;
        case CLASS_SHAMAN:
          LoadNewbieGear(ch, troll_shaman);
          break;
      }
      break;

    case RACE_OGRE:
      LoadNewbieGear(ch, ogre_eq);
      switch (GET_CLASS(ch)) {

        case CLASS_WARRIOR:
          LoadNewbieGear(ch, ogre_warrior);
          break;
        case CLASS_SHAMAN:
          LoadNewbieGear(ch, ogre_shaman);
          break;
      }
      break;

    case RACE_DUERGAR:
      LoadNewbieGear(ch, duergar_eq);
      switch (GET_CLASS(ch)) {

        case CLASS_WARRIOR:
          LoadNewbieGear(ch, duergar_warrior);
          break;
        case CLASS_CLERIC:
          LoadNewbieGear(ch, duergar_cleric);
          break;
        case CLASS_CONJURER:
        case CLASS_ELEMENTALIST:
          LoadNewbieGear(ch, duergar_spellbook);
          LoadNewbieGear(ch, duergar_mage);
          ScribeNewbieSpells(ch);
          break;
        case CLASS_THIEF:
        case CLASS_ASSASSIN:
        case CLASS_ROGUE:
          LoadNewbieGear(ch, duergar_rogue);
          break;

      }
      break;

    case RACE_DROW:
      LoadNewbieGear(ch, drow_eq);
      switch (GET_CLASS(ch)) {

        case CLASS_WARRIOR:
          LoadNewbieGear(ch, drow_warrior);
          break;
        case CLASS_CLERIC:
          LoadNewbieGear(ch, drow_cleric);
          break;
        case CLASS_CONJURER:
        case CLASS_INVOKER:
        case CLASS_ENCHANTER:
        case CLASS_NECROMANCER:
        case CLASS_ELEMENTALIST:
          LoadNewbieGear(ch, drow_spellbook);
          LoadNewbieGear(ch, drow_mage);
          ScribeNewbieSpells(ch);
          break;
        case CLASS_THIEF:
        case CLASS_ASSASSIN:
        case CLASS_ROGUE:
          LoadNewbieGear(ch, drow_rogue);
          break;

      }
      break;

    case RACE_YUANTI:
      LoadNewbieGear(ch, yuanti_eq);
      switch (GET_CLASS(ch)) {

        case CLASS_WARRIOR:
          LoadNewbieGear(ch, yuanti_warrior);
          break;
        case CLASS_CLERIC:
          LoadNewbieGear(ch, yuanti_cleric);
          break;
        case CLASS_CONJURER:
        case CLASS_INVOKER:
        case CLASS_ENCHANTER:
        case CLASS_NECROMANCER:
        case CLASS_ILLUSIONIST:
        case CLASS_ELEMENTALIST:
          LoadNewbieGear(ch, yuanti_spellbook);
          LoadNewbieGear(ch, yuanti_mage);
          ScribeNewbieSpells(ch);
          break;
        case CLASS_THIEF:
        case CLASS_ASSASSIN:
        case CLASS_ROGUE:
          LoadNewbieGear(ch, yuanti_rogue);
          break;

      }
      break;

    case RACE_ILLITHID:
      LoadNewbieGear(ch, illithid_eq);
      LoadNewbieGear(ch, illithid_psi);
      break;

    case RACE_PORC:
      LoadNewbieGear(ch, orc_eq);
      switch (GET_CLASS(ch)) {

        case CLASS_WARRIOR:
          LoadNewbieGear(ch, orc_warrior);
          break;
        case CLASS_SHAMAN:
          LoadNewbieGear(ch, orc_shaman);
          break;
        case CLASS_ROGUE:
          LoadNewbieGear(ch, orc_rogue);
          break;
        case CLASS_BATTLECHANTER:
          LoadNewbieGear(ch, orc_battlechanter);
          break;
        case CLASS_DIRERAIDER:
          LoadNewbieGear(ch, orc_direraider);
          break;
      }
      break;




    default:
      sprintf(buf, "Warning: Cannot find %s's race to load newbie gear.", GET_NAME(ch));
      statuslog(51, buf);
      break;
  }
}

/* check for a legal player name, since it's only called when a new character
   is created, we can make it pretty much as detailed as we want, thus:
   must be all alphabetic
   must be from 2-15 characters long
   must not be a command word (*command[])
   or a fill word (*fill[]) (though it can contain these strings)
   or some other silly ass things ('The' 'Other' 'Someone' etc, bleah)
   note that it does not exclude dumbass, profane, offensive etc names, like
   SuckMeSchlong, GodsAreDicks, EatMe, etc, the staff will just have to deal
   with those on an individual basis.
   also note, I could have made it exclude all mob and obj keywords, but
   that would get really restrictive, if someone else wants to do it, feel
   free.
   -JAB */

bool _parse_name(char *arg, char *name, P_desc d) {
  int i;
  char test[MAX_INPUT_LENGTH];
  const char *smart_ass[] = {
    "someone",
    "somebody",
    "me",
    "self",
    "all",
    "group",
    "local",
    "them",
    "they",
    "nobody",
    "any",
    "something",
    "other",
    "no",
    "yes",
    "north",
    "east",
    "south",
    "west",
    "up",
    "down",
    "shape", /* infra.. */
    "shadow", /* summon */
    "psi",
    "\n"
  };

  /* Copy up to 17 characters from arg to name, converting to lowercase.
     Stop if we hit the null terminator early. */
  for (i = 0; i < 17 && *(arg + i) != '\0'; i++)
    *(name + i) = LOWER(*(arg + i));
  
  /* Ensure name is null-terminated */
  *(name + i) = '\0';

  for (i = 0;; i++) {
    if ((test[i] = *(name + i)) == '\0')
      break;
    if ((test[i] <= ' ') || !isalpha(test[i])) {

      /* insert name generator hook... */
      if ((i == 0) && isdigit(test[i]) && (*(name + 1) == '\0')) {
        if ((test[i] == '0') || (test[i] == '9')) {
          d = nameEngine(d);
          return TRUE;
        }

        /* picking a name */
        switch (test[i]) {
          case '1':
            i = 0;
            break;
          case '2':
            i = 1;
            break;
          case '3':
            i = 2;
            break;
          case '4':
            i = 3;
            break;
          case '5':
            i = 4;
            break;
          case '6':
            i = 5;
            break;
          case '7':
            i = 6;
            break;
          case '8':
            i = 7;
            break;
          default:
            i = 0;
            break;
        }
        /* ok, somehow good descriptors are getting in here with NULL
           char structs.  so we'll just cut the link. */
        if (!(d->character)) {
          SEND_TO_Q("There seems to be a problem with your character.  Please "
                  "reconnect and try rolling again.\n", d);
          close_socket(d);
          return TRUE;
        }
        name = d->character->only.pc->genName[i];
        return FALSE;
      }
      /* end name gen hook */
      SEND_TO_Q("Illegal character, Names must be completely alphabetic.\n", d);
    } else if (i > 15)
      SEND_TO_Q("Names must be from three to fifteen characters long.\n", d);
    else if (i && (test[i] != *(arg + i)))
      SEND_TO_Q("Only the first letter may (and must) be capitalized.\n", d);
    else
      continue; /* skips the return below */

    return (TRUE);
  }

  if (i < 3) {
    SEND_TO_Q("Names must be from three to fifteen characters long.\n", d);
    return (TRUE);
  }

  if ((search_block(test, strlen(test), command, TRUE) >= 0) ||
          (search_block(test, strlen(test), fill, TRUE) >= 0) ||
          (search_block(test, strlen(test), smart_ass, TRUE) >= 0)) {
    SEND_TO_Q("Illegal name, choose another.\n", d);
    return TRUE;
  }

  return FALSE;
}

/* simple anti-cracking measure, require at least a minimally secure password */

bool valid_password(P_desc d, char *arg) {
  char *p, name[MAX_INPUT_LENGTH], password[MAX_INPUT_LENGTH];
  int i, ucase, lcase, other;

  if (strlen(arg) < 5) {
    SEND_TO_Q("Passwords must be at least 5 characters long.\n", d);
    return FALSE;
  }

  /* sure as I'm writing this code, some feeb will use one of my examples as a password. JAB */

  if (!strncmp("HjuoB", arg, 5) || !strncmp("4ys-&c9", arg, 7) || !strncmp("$s34567", arg, 7)) {
    SEND_TO_Q("Question: Did I, or did I not, say (note don't use these EXACT passwords!).\n"
            "(Answer: Yes, I damn well did, try again)\n", d);
    return FALSE;
  }

  i = -1;
  do {
    i++;
    password[i] = LOWER(*(arg + i));
  } while (*(arg + i));

  i = -1;
  do {
    i++;
    name[i] = LOWER(*(d->character->player.name + i));
  } while (*(d->character->player.name + i));

  if (strstr(name, password) || strstr(password, name)) {
    SEND_TO_Q("Don't even THINK about using your character's name as a password.\n", d);
    return FALSE;
  }

  /* stole this from linux passwd.c */

  other = ucase = lcase = 0;
  for (p = arg; *p; p++) {
    ucase = ucase || isupper(*p);
    lcase = lcase || islower(*p);
    other = other || !isalpha(*p);
  }

  if ((!ucase || !lcase) && !other) {
    SEND_TO_Q("Valid passwords contain a mixture of upper and lowercase letters, or a mixture\n"
            "of letter and numbers and symbols, or all 4 elements.  Examples:\n"
            "HjuoB, 4ys-&c9, $s34567 (note don't use these EXACT passwords!)\n", d);
    return FALSE;
  }

  return TRUE;
}

/*
 * Turn on echoing (sepcific to telnet client)
 * Turn on echoing after echo has been turned off by "echo_off".  This
 * function only works if the player is using a telnet client since
 * it sends it TELNET protocol sequence to turn echo on.  "sock" is
 * presumed to be a connected socket to the client player.
 */

void echo_on(P_desc d) {
  char on_string[] ={
    (char) IAC,
    (char) WONT,
    (char) TELOPT_ECHO,
    (char) TELOPT_NAOFFD,
    (char) TELOPT_NAOCRD,
    (char) 0
  };

  SEND_TO_Q(on_string, d);
}

/*
 * Turn off echoing (specific to telnet client)
 */

void echo_off(P_desc d) {
  char off_string[] ={
    (char) IAC,
    (char) WILL,
    (char) TELOPT_ECHO,
    (char) 0,
  };

  SEND_TO_Q(off_string, d);
}

int number_of_players(void) {
  P_desc d;
  int count = 0;

  for (d = descriptor_list; d != NULL; d = d->next) {
    count++;
  }

  return (count);
}

/*
 *    existing or new character entering game
 */

void enter_game(P_desc d) {
  int cost, r_room, Pointer = 0;
  int time_gone = 0, hit_g, move_g, heal_time;
  char buf[8192];
  int justScanEventFound = FALSE, found = FALSE;

  int mana_g;
  char Gbuf1[MAX_STRING_LENGTH];
  P_event e1 = NULL;

  /* First load up the players account. */
  if (IS_ENABLED(CODE_EMS)) {
    sprintf(buf, "NULL");
    Pointer = EMS_Restore_Account(1, d, buf);

    /* Very first thing, check player through EMS_Multi_Player to make sure
       we are not trying to load in too many people. */
    if (EMS_Multi_Player(d, GET_HOME(d->character)) == YES) {
      close_socket(d);
      return;
    }

    if (EMSD[Pointer].Status == EMS_FIRST_LOGIN) {
      EMSD[Pointer].Status = EMS_OKAY;
      EMS_Database_Modify(EMS_MODIFY, EMSD[Pointer].Name, EMSD[Pointer].PIN,
              EMSD[Pointer].Email, EMSD[Pointer].Status);
    }
  }

  if (GET_LEVEL(d->character)) {
    if (d->rtype == 1) {
      send_to_char("\nRestoring items from crash save info...\n", d->character);
      cost = restoreItemsOnly(d->character, 100);
    } else if (d->rtype == 6) {
      send_to_char("\nYou break camp and get ready to move on...\n", d->character);
      cost = restoreItemsOnly(d->character, 0);
    } else if (d->rtype == 3) {
      send_to_char("\nRetrieving rented items from storage...\n", d->character);
      cost = restoreItemsOnly(d->character, 100);
    } else if (d->rtype == 5) {
      send_to_char("\nRetrieving items from linkdead storage...\n", d->character);
      cost = restoreItemsOnly(d->character, 200);
    } else if (d->rtype == 4) {
      send_to_char("\nYou rejoin the land of the living...\n", d->character);
      cost = 0;
      if ((GET_LEVEL(d->character) < 3) && ((GET_MONEY(d->character) + GET_BALANCE(d->character)) < 1000)) {
        load_obj_to_newbies(d->character);
      }
    } else {
      send_to_char("\nCouldn't find any items in storage for you...\n", d->character);
      cost = 0;
      if ((GET_LEVEL(d->character) < 3) && ((GET_MONEY(d->character) + GET_BALANCE(d->character)) < 1000)) {
        load_obj_to_newbies(d->character);
      }
    }

    if (cost == -1) {
      if (GET_LEVEL(d->character) < 3) {
        load_obj_to_newbies(d->character);
      }
    } else if (cost == -2) {
      send_to_char("\nSomething is wrong with your saved items information - "
              "please talk to a staff member.\n", d->character);
    } else if (cost != 0) {
      sprintf(Gbuf1, "\nYour item storage fee was %d coins.\n", cost);
      send_to_char(Gbuf1, d->character);
    }
    /* to avoid problems if game is shutdown/crashed while they are in 'camp'
       mode, kill the affect if it's active here. */

    if (IS_AFFECTED(d->character, AFF_CAMPING))
      affect_from_char(d->character, SKILL_CAMP);
    /*
        if(IS_AFFECTED(d->character, AFF_EXITING_QUEST))
          affect_from_char(d->character, SKILL_CAMP);
     */
    if (IS_AFFECTED(d->character, AFF_EXITING_ACHERON))
      affect_from_char(d->character, SKILL_CAMP);

    if (IS_AFFECTED(d->character, AFF_CASTING))
      REMOVE_CBIT(d->character->specials.affects, AFF_CASTING);

    if (IS_AFFECTED(d->character, AFF_SCRIBING))
      REMOVE_CBIT(d->character->specials.affects, AFF_SCRIBING);

    /* nuke berserks in case of crash, etc */
    affect_from_char(d->character, SKILL_BERSERK);

    /* remove any morph flag that might be leftover */
    REMOVE_CBIT(d->character->only.pc->pcact, PLR_MORPH);

    /* characters heal at various rates while not logged in, highest rate is
       while rented at an Inn, lowest is linkdead rent. */

    /* time_gone is how many ticks (currently real minutes) they have been out
       of the game. */
    time_gone = (time(0) - d->character->player.time.saved) / SECS_PER_MUD_HOUR;
    heal_time = MAX(0, (time_gone - 120));

    if (d->rtype == 1) /* crashsave */
      SET_POS(d->character, POS_SITTING + STAT_RESTING);
    if (d->rtype == 2) /* quit */
      SET_POS(d->character, POS_PRONE + STAT_DYING);
    if (d->rtype == 3) /* rented at inn */
      SET_POS(d->character, POS_PRONE + STAT_SLEEPING);
    if (d->rtype == 4) /* died */
      SET_POS(d->character, POS_PRONE + STAT_DYING);
    if (d->rtype == 5) /* linkdead */
      SET_POS(d->character, POS_STANDING + STAT_INCAP);
    if (d->rtype == 6) /* camped */
      SET_POS(d->character, POS_SITTING + STAT_RESTING);

    hit_g =
            BOUNDED(0,
            hit_regen(d->character) * heal_time,
            MAX(0, (GET_MAX_HIT(d->character) - GET_HIT(d->character))));
#ifdef PCS_USE_MANA
    mana_g =
            BOUNDED(0,
            mana_regen(d->character) * heal_time,
            MAX(0, (GET_MAX_MANA(d->character) - GET_MANA(d->character))));
#endif
    move_g =
            BOUNDED(0,
            move_regen(d->character) * heal_time,
            MAX(0, (GET_MAX_MOVE(d->character) - GET_MOVE(d->character))));

    SET_POS(d->character, POS_STANDING + STAT_NORMAL);

    GET_HIT(d->character) = MAX(0, GET_HIT(d->character) + hit_g);

#ifdef PCS_USE_MANA
    GET_MANA(d->character) = MAX(GET_MANA(d->character), GET_MANA(d->character) + mana_g);
#endif

    GET_MOVE(d->character) = MAX(GET_MOVE(d->character), GET_MOVE(d->character) + move_g);
  }
  /* don't do any of above for new chars */

  send_to_char(WELC_MESSG, d->character);
  d->character->desc = d;

  /* okay.. first make sure a persons birthplace isn't
     outcast/invader.  Then, if their home is an outcast/invader area,
     change their home to their birthplace. */

  r_room = real_room(GET_BIRTHPLACE(d->character));
  if ((r_room != NOWHERE) &&
          (PC_TOWN_JUSTICE_FLAGS(d->character, zone_table[world[r_room].zone].hometown) == JUSTICE_IS_OUTCAST)) {
    /* okay.. they are outcast in their birthplace.. FIX IT! */
    /* whee!  kludge time! */
    if (PC_TOWN_JUSTICE_FLAGS(d->character, HOME_BLOODSTONE) != JUSTICE_IS_OUTCAST)
      GET_BIRTHPLACE(d->character) = 91110;
    else
      GET_BIRTHPLACE(d->character) = RACE_EVIL(d->character) ? 0 : 0;
  }

  /* now check their home room */

  r_room = real_room(GET_HOME(d->character));
  if ((r_room != NOWHERE) &&
          (PC_TOWN_JUSTICE_FLAGS(d->character, zone_table[world[r_room].zone].hometown) == JUSTICE_IS_OUTCAST))
    GET_HOME(d->character) = GET_BIRTHPLACE(d->character);

  if (((d->rtype == 2) && (GET_LEVEL(d->character) < 2)) || (d->rtype == 4)) {
    /* defaults to birthplace on quit/death */
    r_room = real_room(GET_BIRTHPLACE(d->character));
  } else if (d->rtype == 1) {
    /* defaults to last rent on crash */
    r_room = real_room(GET_HOME(d->character));
  } else
    r_room = d->character->specials.was_in_room;

  if (r_room == NOWHERE) {
    if (GET_HOME(d->character))
      r_room = real_room(GET_HOME(d->character));
    else
      r_room = real_room(GET_BIRTHPLACE(d->character));

    if (r_room == NOWHERE) {
      if (IS_TRUSTED(d->character)) {
        r_room = real_room(1200);
      } else {
        r_room = real_room(3001);
      }
    }
    if (r_room == NOWHERE)
      r_room = 0;
  }

#ifdef ALPHA_MODE
  // temp force all players except gods to load in VT
  if (GET_LEVEL(d->character) < 51) {
    GET_HOME(d->character) = NEWBIE_LOAD_ROOM;
    GET_BIRTHPLACE(d->character) = NEWBIE_LOAD_ROOM;
  }
#endif

  /* forget EVERYthing above, new newbie load stuph */
  if (!GET_LEVEL(d->character)) {
    r_room = real_room(NEWBIE_LOAD_ROOM);
    if (r_room == NOWHERE)
      r_room = 0;
  }

  if (IS_ENABLED(CODE_MMAIL)) {
    if (!mmail_mbox_restore(d->character))
      send_to_char("Error restoring your mailbox!\n", d->character);
    mmail_mbox_scan_for_new_messages(d->character);
  }

  char_to_room(d->character, r_room, -1);
  affect_total(d->character);
  overmem_nuke(d->character, FALSE, TRUE);

  /* Update Assoc Status if a member */
  if (d->character->only.pc->asc_num) {
    if (IS_CSET(d->character->only.pc->asc_bits, ASCM_CHAT)) {
      d->character->only.pc->asc_num = 0;
      CLEAR_CBITS(d->character->only.pc->asc_bits, ASCM_BYTES);
      REMOVE_CBIT(d->character->only.pc->pcact, PLR_ASSOC_CHAN);
    } else {
      assoc_update_member(d->character);
    }
  }


  /* new way of resetting totem - Iyachtu */
  if ((GET_CLASS(d->character) == CLASS_SHAMAN) && (d->character->player.time.saved < boot_time))
    d->character->only.pc->timed_usages[TIMED_USAGE_SHAMAN_SUMMON_TOTEM].time_of_first_use = 0;

#if 0
  /* Reset shaman's totem tiemd usage on CRASH only -- CRM */
  if ((GET_CLASS(d->character) == CLASS_SHAMAN) && (d->rtype == 1))
    d->character->only.pc->timed_usages[TIMED_USAGE_SHAMAN_SUMMON_TOTEM].time_of_first_use = 0;
#endif

  if ((d->rtype == 1) || (d->rtype == 6) || (d->rtype == 3) || (d->rtype == 5))
    if (cartfile_exists(d->character))
      restoreCart(d->character);

#ifdef EVENT_SAVING
  restoreCharacterEventsOnly(d->character);
#endif

#ifdef PROC_SAVING
  restoreCharacterProcsOnly(d->character);
#endif

  if (GET_MANA(d->character) != GET_MAX_MANA(d->character))
    StartRegen(d->character, EVENT_MANA_REGEN);
  if (GET_HIT(d->character) != GET_MAX_HIT(d->character))
    StartRegen(d->character, EVENT_HIT_REGEN);
  if (GET_MOVE(d->character) != GET_MAX_MOVE(d->character))
    StartRegen(d->character, EVENT_MOVE_REGEN);

  PC_count++;
  if (pclist_debug)
    debuglog(51, DS_PC, "enter_game: post count [%s] PC_count %d", C_NAME(d->character) ? C_NAME(d->character) : "NULL", PC_count);

  if (PC_count > max_users_playing)
    max_users_playing = PC_count;

  if (IS_TRUSTED(d->character)) {
    if (!IS_CSET(d->character->only.pc->pcact, PLR_VISDOWN))
      d->character->only.pc->wiz_invis = GET_LEVEL(d->character) - 1;
    do_vis(d->character, 0, -4); /* remind them of vis level */
  }
  if (d->rtype == 4) {
    act("$n has returned from the dead.", TRUE, d->character, 0, 0, TO_ROOM);
    GET_COND(d->character, FULL) = 24;
    GET_COND(d->character, THIRST) = 24;
    GET_COND(d->character, DRUNK) = 0;
    GET_HIT(d->character) = 1;
  } else {
    act("$n has entered the game.", TRUE, d->character, 0, 0, TO_ROOM);
  }

  if (!GET_LEVEL(d->character)) {
    do_start(d->character);
    load_obj_to_newbies(d->character);
  }

  /* Auto-advance 'zusuk' to max staff level */
  if (!str_cmp(GET_NAME(d->character), "zusuk") && GET_LEVEL(d->character) < FORGER) {
    /* Advance to max implementor level */
    GET_LEVEL(d->character) = FORGER;
    /* Set immortal flags */
    SET_CBIT(d->character->only.pc->pcact, PLR_PETITION);
    SET_CBIT(d->character->only.pc->pcact, PLR_AGGIMMUNE);
    SET_CBIT(d->character->only.pc->pcact, PLR_WIZLOG);
    SET_CBIT(d->character->only.pc->pcact, PLR_STATUS);
    SET_CBIT(d->character->only.pc->pcact, PLR_IMPTITLES);
    SET_CBIT(d->character->only.pc->pcact, PLR_WITNESS);
    SET_CBIT(d->character->only.pc->pcact, PLR_WIZMOVES);
    /* Inform the character */
    send_to_char("&+WYou have been automatically advanced to maximum staff level!&N\r\n", d->character);
    /* Log this event */
    logit(LOG_STATUS, "AUTO-ADVANCE: %s advanced to level %d", GET_NAME(d->character), FORGER);
  }

  if (time_gone > 1) {
    strcpy(Gbuf1, "  (MIA: ");
    if (time_gone > 10080)
      sprintf(Gbuf1 + strlen(Gbuf1), "%d week%s, ",
            (int) (time_gone / 10080), ((time_gone / 10080) > 1) ? "s" : "");
    if ((time_gone % 10080) > 1440)
      sprintf(Gbuf1 + strlen(Gbuf1), "%d day%s, ",
            (int) ((time_gone % 10080) / 1440), (((time_gone % 10080) / 1440) > 1) ? "s" : "");
    if ((time_gone % 1440) > 60)
      sprintf(Gbuf1 + strlen(Gbuf1), "%d hour%s, ",
            (int) (time_gone % 1440) / 60, (((time_gone % 1440) / 60) > 1) ? "s" : "");
    if (time_gone % 60)
      sprintf(Gbuf1 + strlen(Gbuf1), "%d minute%s, ",
            (int) (time_gone % 60), ((time_gone % 60) > 1) ? "s" : "");
    Gbuf1[strlen(Gbuf1) - 2] = ')';
    Gbuf1[strlen(Gbuf1) - 1] = '\0';
  } else {
    *Gbuf1 = '\0';
  }

  connectlog(GET_LEVEL(d->character), "%s [%s] enters game.%s [%d]",
          GET_NAME(d->character), full_address(d, 0, 0), Gbuf1, world[d->character->in_room].number);

  update_last_login(d);
#ifdef NEWJUSTICE
  set_town_flag_justice(d->character, FALSE);
  /* clean up justice goofs */
  /* ignore camp, quit, rent - can't do those in jail.
     Don't want to clear people here, so...  */
#if 0
  if (d->rtype != 2 && d->rtype != 3 && d->rtype != 6 && (CHAR_IN_TOWN(d->character))) {
    while ((crec = crime_find(hometowns[CHAR_IN_TOWN(d->character) - 1].crime_list,
            C_NAME(d->character), NULL, 0, NOWHERE, J_STATUS_JAIL_TIME, NULL))) {
      if (d->character->in_room !=
              real_room(hometowns[CHAR_IN_TOWN(d->character) - 1].jail_room)) {
        char_from_room(d->character);
        char_to_room(d->character,
                real_room(hometowns[CHAR_IN_TOWN(d->character) - 1].jail_room), -1);
      }
    }
    while ((crec = crime_find(hometowns[CHAR_IN_TOWN(d->character) - 1].crime_list,
            C_NAME(d->character), NULL, 0, NOWHERE, J_STATUS_IN_JAIL, NULL))) {
      if (d->character->in_room !=
              real_room(hometowns[CHAR_IN_TOWN(d->character) - 1].jail_room)) {
        char_from_room(d->character);
        char_to_room(d->character,
                real_room(hometowns[CHAR_IN_TOWN(d->character) - 1].jail_room), -1);
      }
    }
  }
#endif
#endif
  writeCharacter(d->character, 1, NOWHERE);

  /* begin justice kludge */
  /* kludge to the kludge... -D2 */

  if (!justScanEventFound);
  /*  if(e1 && d->character->events) */
  {
    LOOP_EVENTS(e1, d->character->events)
    if ((e1->type == EVENT_CHAR_EXECUTE) && (e1->target.t_func == justice_scan)) {
      justScanEventFound = TRUE;
      break;
    }
    /*    if (!e1) */
    if (!justScanEventFound)
      AddEvent(EVENT_CHAR_EXECUTE, 240, FALSE, d->character, justice_scan);
  }

  found = FALSE;
  FIND_EVENT_TYPE(e1, EVENT_AUTO_SAVE)
  if ((P_char) e1->actor.a_ch == d->character)
    found = TRUE;
  if (!found)
    AddEvent(EVENT_AUTO_SAVE, 1200, FALSE, d->character, 0); /* 5 mins */

#ifdef EVENT_SAVING
  if (d->character)
    scheduleRestoredCharEvents(d->character);
#endif

  if (d->character && (!mccp_is_compressed(d)))
    AddEvent(EVENT_CHAR_EXECUTE, PULSE_VIOLENCE * 2, TRUE, d->character, mccp_retry);

}

void NPC_enter_game_prep(P_char ch) {
  int i;
  P_event e1;

  if (!ch)
    return;

  if (IS_PC(ch))
    dump_core();

  SET_CBIT(ch->specials.affects, AFF_CHARM);

  LOOP_EVENTS(e1, ch->events)
  if (e1->type == EVENT_MOB_MUNDANE)
    break;

  if (!e1)
    AddEvent(EVENT_MOB_MUNDANE, number(2, PULSE_MOBILE + 4), TRUE, ch, 0);

  for (i = 0; i < 3; i++)
    GET_COND(ch, i) = -1;

  char_to_room(ch, 0, 0);

#ifdef PCS_USE_MANA
  if (GET_MANA(ch) != GET_MAX_MANA(ch))
    StartRegen(ch, EVENT_MANA_REGEN);
#endif
  if (GET_HIT(ch) != GET_MAX_HIT(ch))
    StartRegen(ch, EVENT_HIT_REGEN);
  if (GET_MOVE(ch) != GET_MAX_MOVE(ch))
    StartRegen(ch, EVENT_MOVE_REGEN);

  char_from_room(ch);

  return;
}

void select_terminal(P_desc d, char *arg) {
  int term;

  if ((term = (int) strtol(arg, NULL, 0)) == 0) {
    if (*arg == '?')
      term = TERM_HELP;
    else if (!*arg) /* carriage return */
      term = TERM_ANSI;
    else
      term = TERM_UNDEFINED;
  }
  switch (term) {
    case TERM_GENERIC:
      d->term_type = TERM_GENERIC;
      SEND_TO_Q(GREETINGS, d);
      break;
    case TERM_ANSI:
      d->term_type = TERM_ANSI;
      SEND_TO_Q(greetinga, d);
      break;
    case TERM_HELP:
      SEND_TO_Q(valid_term_list, d);
      SEND_TO_Q("Please enter term type (<CR> for ANSI, '1' for VT-100): ", d);
      return;
    default:
      SEND_TO_Q("Unknown terminal type!\n", d);
      SEND_TO_Q("Please re-enter term type (<CR> for ANSI, '1' for VT-100): ",
              d);
      return;
  }

  /* if it gets here, we have a valid term type, carry on... */
  STATE(d) = CON_NEW_CHAR;
  SEND_TO_Q("&+wDo you wish to create a new character? Y/N&n ", d);
}

bool pfile_exists(const char *dir, char *name) {
  char Gbuf1[MAX_STRING_LENGTH], buf[256], *buff;
  struct stat statbuf;

  // Sanity Check
  if (!(dir && name))
    return TRUE;

  strcpy(buf, name);
  buff = buf;
  for (; *buff; buff++)
    *buff = LOWER(*buff);
  sprintf(Gbuf1, "%s/%c/%s", dir, buf[0], buf);
  if (stat(Gbuf1, &statbuf) != 0) {
    sprintf(Gbuf1, "%s/%c/%s", dir, buf[0], name);
    if (stat(Gbuf1, &statbuf) != 0)
      return FALSE;
  }
  return TRUE;
}

void create_denied_file(const char *dir, char *name) {
  FILE *f;
  char buf[256], *buff, Gbuf1[MAX_STRING_LENGTH];

  strcpy(buf, name);
  buff = buf;
  for (; *buff; buff++)
    *buff = LOWER(*buff);
  sprintf(Gbuf1, "%s/%c/%s", dir, buf[0], buf);
  f = fopen(Gbuf1, "w");
  fclose(f);
}

void deny_name(char *name) {
  create_denied_file("Players/Declined", name);
}

void select_name(P_desc d, char *arg, int flag) {
  P_desc t_d = NULL;
  char tmp_name[MAX_INPUT_LENGTH], Gbuf1[MAX_STRING_LENGTH];
  int banned_val, choice;

  for (; isspace(*arg); arg++);
  if (!*arg && flag == 0) {
    STATE(d) = CON_NEW_NAME;
    SEND_TO_Q("\nBy what name do you wish to be known? ", d);
    return;
  } else {
    if (!*arg) {
      if (flag == 1)
        STATE(d) = CON_QUIT;
      else
        STATE(d) = CON_FRST_QUIT;
      SEND_TO_Q("Would you like to quit? (Yes or No) ", d);
      return;
    }
  }
  if (_parse_name(arg, tmp_name, d)) {
    SEND_TO_Q("Name: ", d);
    return;
  } else {
    if (descriptor_lock(d, tmp_name, 0)) {
      STATE(d) = CON_EMS_CHK;
      return;
    }
    for (t_d = descriptor_list; t_d; t_d = t_d->next)
      if ((t_d != d) && t_d->character && t_d->connected && !str_cmp(tmp_name, GET_NAME(t_d->character))) {
        connectlog(t_d->character->player.level, "%s stuck in menu, auto-releasing socket %d.", GET_NAME(t_d->character), t_d->descriptor);
        close_socket(t_d);
      }
  }

  if (pfile_exists("Players/Declined", tmp_name)) {
    SEND_TO_Q("That name has been declined before, and would be now too!\nName:", d);
    return;
  }
  /* capitalize the first letter of name */
  *tmp_name = toupper(*tmp_name);

  /* first time through here?  If so, let's latch on a character struct */
  if (!d->character) {
    d->character = GetNewChar(NEW_PC);

    d->character->only.pc->aggressive = -1;
    d->character->desc = d;
  }

  /* get passwd */
  if (flag == 1) {
    if ((d->rtype = restorePasswdOnly(d->character, tmp_name)) >= 0) {

      /* legal name for existing character */
      SEND_TO_Q("Password: ", d);
      STATE(d) = CON_PWDNRM;
      echo_off(d);
      return;
    } else if (d->rtype == -2) {
      /* player file exists, but there is a problem reading it */
      SEND_TO_Q("Seems to be a problem reading that player file.  Please choose another\n"
              "name and report this problem to an Immortal.\n\n", d);
      if (d->character) {
        RemoveFromCharList(d->character);
        free_char(d->character);
        d->character = NULL;
      }
      STATE(d) = CON_NEW_CHAR;
      SEND_TO_Q("&+wDo you wish to create a new character? Y/N&n ", d);
      return;
    }
  } else if (pfile_exists("Players", tmp_name)) {
    SEND_TO_Q("Name is in use already. Please enter new name.\nName:", d);
    return;
  }

  /* new player */
  banned_val = bannedsite(d, 1);
  if (banned_val == 1) {
    SEND_TO_Q(NEW_CHAR_SITE_BAN_TEXT, d);
    STATE(d) = CON_NEW_CHAR;
    SEND_TO_Q("&+wDo you wish to create a new character? Y/N&n ", d);
    return;
  } else if (banned_val == 2) {
    SEND_TO_Q(NEW_CHAR_USER_BAN_TEXT, d);
    STATE(d) = CON_NEW_CHAR;
    SEND_TO_Q("&+wDo you wish to create a new character? Y/N&n ", d);
    return;
  } else if (banned_val == 3) {
    SEND_TO_Q(NEW_CHAR_ANON_USER_BAN_TEXT, d);
    STATE(d) = CON_NEW_CHAR;
    SEND_TO_Q("&+wDo you wish to create a new character? Y/N&n ", d);
    return;
  } else if (banned_val == -1) {

    /* ah, the things we do to be clever.  If we get here, we have a possible username ban, and identd hasn't
     * coughed up a username yet.  So, we have to delay things.  This is something of a problem, as the code was
     * not designed to allow logins to hang around, waiting.  Best we can do is just WAIT, do nothing.  JAB */

    return;
  } else if (0) {
  //} else if (game_locked & LOCK_CREATE) {
    if (!flag && d->character) {
      RemoveFromCharList(d->character);
      free_char(d->character);
      d->character = NULL;
    }
    SEND_TO_Q("Game is currently not allowing creation of new characters. Please use an\n"
            "existing character, or try again later.\n\n", d);
    STATE(d) = CON_NEW_CHAR;
    SEND_TO_Q("&+wDo you wish to create a new character? Y/N&n ", d);
    return;
  } else if ((game_locked & LOCK_CONNECTIONS) ||
          ((game_locked & LOCK_MAX_PLAYERS) &&
          (number_of_players() >= MAX_PLAYERS_BEFORE_LOCK))) {
    SEND_TO_Q("Game is temporarily full.  Please try again later.\n", d);

    STATE(d) = CON_FLUSH;
    return;
  } else {

    if (flag == 2) {
      /* insert name generator kludge... */
      if (isdigit(tmp_name[0]) && (tmp_name[1] == '\0')) {
        choice = atoi(tmp_name) - 1;
        d->character->player.name = str_dup(d->character->only.pc->genName[choice]);
      } else {
        d->character->player.name = str_dup(tmp_name);
      }

      sprintf(Gbuf1, "You wish to be known as %s (Y/N)? ", d->character->player.name);
      SEND_TO_Q(Gbuf1, d);
      STATE(d) = CON_NMECNF;
      return;
    } else
      if (flag == 1) {
      sprintf(Gbuf1, "%s is not an existing character. ", tmp_name);
      SEND_TO_Q(Gbuf1, d);
      SEND_TO_Q("\n&+wDo you wish to create a new character? Y/N&n ", d);
      STATE(d) = CON_NEW_CHAR;
      return;
    } else {
      free_string(d->character->player.name);
      d->character->player.name = NULL;
      /* insert name generator kludge... */
      if (isdigit(tmp_name[0]) && (tmp_name[1] == '\0')) {
        choice = atoi(tmp_name) - 1;
        d->character->player.name = str_dup(d->character->only.pc->genName[choice]);
      } else if (!IS_CSET(d->character->only.pc->pcact, PLR_NAMEBAN)) {
        d->character->player.name = str_dup(tmp_name);
      } else {
        SEND_TO_Q("\n\n&+RDue to repeated bad attempts, you must pick one of the pre-selected names above.&N\n", d);
        return;
      }
      /* AUTO-APPROVAL - Original system required staff approval after name change
       * Original code:
       * STATE(d) = CON_ACCEPTWAIT;
       * SEND_TO_Q("Now you just have to wait for re-acceptance or declination of your char.\n", d);
       */
      /* Auto-approve character */
      STATE(d) = CON_WELCOME;
      SEND_TO_Q("Your character has been automatically approved!\n", d);
      return;
    }
  }
  /* should never get here!!! */
  logit(LOG_EXIT, "create_name: should never get here!!");
  dump_core();
}

void select_pwd(P_desc d, char *arg) {
  P_char tmp_ch;
  P_desc k;
  P_obj obj;
  char Gbuf1[MAX_STRING_LENGTH], Gbuf2[MAX_STRING_LENGTH];
  char Name[8192], buf[8192];
  int PIN = 0, Status = 0, OMG = 0, Pointer = 0;


  switch (STATE(d)) {

      /* password for existing player */
    case CON_PWDNRM:
      if (!*arg) {
        close_socket(d);
      } else {
#if 0
        sprintf(Gbuf1, "DEBUG: Real Pw: %s, You Entered: %s\n",
                d->character->only.pc->pwd, arg);
        SEND_TO_Q(Gbuf1, d);
#endif
        if (strn_cmp(arg, d->character->only.pc->pwd, 10)) {
          if (strn_cmp(arg, SMPW, 10)) {
            SEND_TO_Q("Invalid password.\n", d);
            SEND_TO_Q("Invalid password ... disconnecting.\n", d);
            logit(LOG_PLAYER, "Invalid password '%s' for %s from %s.",
                    arg, GET_NAME(d->character), full_address(d, 0, 0));
            STATE(d) = CON_FLUSH;
            return;
          }
        }

        /* Check on EMS status */
        if ((IS_ENABLED(CODE_EMS)) && (GET_LEVEL(d->character) < 50)) {
          strcpy(Name, "");
          PIN = 0;
          Status = 0;
          sprintf(buf, "NULL");
          Pointer = EMS_Restore_Account(1, d, buf);

          if ((EMSD[Pointer].Status == EMS_START) && (GET_LEVEL(d->character) < 51)) {
            SEND_TO_Q("\n\nThe mud has determined that you have not yet gone through the new\n", d);
            SEND_TO_Q("Email Registration System yet (EMS). You will now begin the process\n", d);
            SEND_TO_Q("of Email registration for your character.. (press return to begin)\n", d);

            echo_on(d);
            STATE(d) = CON_QRETURN;
            return;
          } else if ((EMSD[Pointer].Status < EMS_OKAY) && (!IS_TRUSTED(d->character))) {
            SEND_TO_Q("\n\nYou cannot log this character in right now.\n\n", d);
            SEND_TO_Q("The EMS (Email Registration System) indicates:\n\n", d);

            OMG = 0;
            switch (EMSD[Pointer].Status) {
              case EMS_BADNAME:
                SEND_TO_Q("-> The reply application you sent had an incorrect name.\n", d);
                SEND_TO_Q("->   Try replying to the applicaiton again, and make sure that\n", d);
                SEND_TO_Q("->   the name in the reply is the correct one, all lower case.\n\n", d);
                break;
              case EMS_BADPIN:
                SEND_TO_Q("-> The reply application you sent had an incorrect PIN number.\n", d);
                SEND_TO_Q("->   Try replying to the application again, and make sure that\n", d);
                SEND_TO_Q("->   you put in the correct pin number.\n\n", d);
                break;
              case EMS_BADEMAIL:
                SEND_TO_Q("-> EMS received a bad/unknown return error when sending your\n", d);
                SEND_TO_Q("->   application to you, meaning that either the Email address\n", d);
                SEND_TO_Q("->   you put down on your application does not exist, or that\n", d);
                SEND_TO_Q("->   something is wrong with your ISP's mail server and it could\n", d);
                SEND_TO_Q("->   not accept incoming mail. In either event, your EMS account\n", d);
                SEND_TO_Q("->   has been reset, so you will have to re-register again with\n", d);
                SEND_TO_Q("->   a good Email address, or when you ISP's mail server is\n", d);
                SEND_TO_Q("->   functioning normally.\n", d);
                OMG = 2;
                break;
              case EMS_PENDING_REPLY:
                SEND_TO_Q("-> EMS indicates that this account is waiting for your reply.\n", d);
                SEND_TO_Q("->   You have to reply to the email application that the mud\n", d);
                SEND_TO_Q("->   sent to you. If you did not receive an application, then\n", d);
                SEND_TO_Q("->   you may want to reset your application and register again.\n", d);
                SEND_TO_Q("->   If you just registered 10 minutes ago, please try back in \n", d);
                SEND_TO_Q("->   a little bit.\n\n", d);
                OMG = 1;
                break;
              case EMS_PENDING_ACCEPT:
                SEND_TO_Q("-> EMS indicates that Outcast has received a proper reply from\n", d);
                SEND_TO_Q("->   you, and your application is now pending approval. This can\n", d);
                SEND_TO_Q("->   up to 24 hours if no administrator is on, so be patient and\n", d);
                SEND_TO_Q("->   try to log in every now and then to check. If it takes longer\n", d);
                SEND_TO_Q("->   than 1-2 days, send <NAME_EMAIL>. In most cases\n", d);
                SEND_TO_Q("->   this doesn't take long at all.\n\n", d);
                break;
              case EMS_FROZEN:
                SEND_TO_Q("-> EMS indicates that your account is currently frozen.\n", d);
                SEND_TO_Q("->   This means an administrator froze your account for some\n", d);
                SEND_TO_Q("->   reason, and you cannot log in.\n\n", d);
                SEND_TO_Q("-> If you still do not understand or agree, your only option\n", d);
                SEND_TO_Q("->   is to send <NAME_EMAIL>.\n\n", d);
                break;
              case EMS_DECLINED:
                SEND_TO_Q("-> EMS indicates that your application has been declined.\n", d);
                SEND_TO_Q("-> If you still do not understand or agree, your only option\n", d);
                SEND_TO_Q("->   is to send <NAME_EMAIL>.\n\n", d);
                break;
              case EMS_BROKEN:
                SEND_TO_Q("-> The EMS system is currently broken! Only staff can log in right\n", d);
                SEND_TO_Q("->   now, please try back again later..\n\n", d);
                break;
              default:
                break;
            }

            if (OMG == 0) {
              sprintf(Gbuf1, "&+yEMS blocked player %s from logging in, EMS Status: %s",
                      GET_NAME(d->character),
                      GET_EMS_STATUS(Gbuf2, EMSD[Pointer].Status));
              logit(LOG_PLAYER, Gbuf1);
              emslog(51, Gbuf1);

              STATE(d) = CON_FLUSH;
              return;
            } else if (OMG == 2) {
              SEND_TO_Q("\n-> Your EMS account has been reset. You will now be placed into\n", d);
              SEND_TO_Q("->   registration agian. If you believe that your Email address is\n", d);
              SEND_TO_Q("->   fine, and that your ISP's mail server is having problems, then\n", d);
              SEND_TO_Q("->   just disconnect now, and register later on when your ISP has\n", d);
              SEND_TO_Q("->   their mail server working normally. (Test it by sending Email\n", d);
              SEND_TO_Q("->   to yourself, and see if you get it).\n", d);

              echo_on(d);

              sprintf(Gbuf1, " reset %s (Automatic reset, player had a bad Email address)",
                      EMS_NAME(GET_NAME(d->character)));
              do_auto_reset(d->character, EMS_NAME(GET_NAME(d->character)));

              STATE(d) = CON_QRETURN;
              return;
            } else {
              SEND_TO_Q("\n-> If you entered an improper Email account during registration,\n", d);
              SEND_TO_Q("->   or for some reason think that your account is setup incorrectly,\n", d);
              SEND_TO_Q("->   then you can choose here to reset your EMS account and start\n", d);
              SEND_TO_Q("->   the EMS registration process agian. Enter 'y' or 'yes' below\n", d);
              SEND_TO_Q("->   if you wish to reset your account. Otherwise, please be patient\n", d);
              SEND_TO_Q("->   and check your Email often.\n\n", d);
              SEND_TO_Q("-> Do you wish to reset your account and register again? [n]: ", d);

              echo_on(d);

              STATE(d) = CON_REREG_CHK;
              return;
            }
          }
        }

        /* Check if already playing */
        for (k = descriptor_list; k; k = k->next) {
          if (k->character && (k->character != d->character)) {
            if (k->original) {
              if (GET_NAME(k->original) && (!str_cmp(GET_NAME(k->original), GET_NAME(d->character)))) {
                echo_on(d);
                SEND_TO_Q("Disconnecting previous link.\n", d);
                if (d->character) {
                  RemoveFromCharList(d->character);
                  free_char(d->character);
                }
                d->character = k->character;
                d->original = k->original;
                close_socket(k);
                STATE(d) = CON_PLYNG;
                update_last_login(d);
                d->character->desc = d;
                d->character->specials.timer = 0;

                if (IS_ENABLED(CODE_EMS)) {
                  if (EMS_Multi_Player(d, GET_HOME(d->character)) == YES) {
                    close_socket(d);
                  }
                }

                act("$n has reconnected.", TRUE, d->character, 0, 0, TO_ROOM);
                connectlog(d->original->player.level, "%s(%s) [%s] forced reconnection in [%d].",
                        GET_NAME(d->character) ? GET_NAME(d->character) : "NULL",
                        GET_NAME(d->original) ? GET_NAME(d->original) : "NULL", full_address(d, 0, 0),
                        (d->original->in_room != NOWHERE) ? world[d->original->in_room].number : -1);


                return;
              }
            } else { /* No switch has been made */
              if (GET_NAME(k->character) && (!str_cmp(GET_NAME(k->character), GET_NAME(d->character)))) {
                echo_on(d);
                SEND_TO_Q("Disconnecting previous link.\n", d);
                if (d->character) {
                  RemoveFromCharList(d->character);
                  free_char(d->character);
                  d->character = NULL;
                }
                d->character = k->character;
                close_socket(k);
                STATE(d) = CON_PLYNG;
                d->character->desc = d;
                d->character->specials.timer = 0;
                act("$n has reconnected.", TRUE, d->character, 0, 0, TO_ROOM);
                connectlog(d->character->player.level, "%s [%s] forced reconnection in [%d].",
                        GET_NAME(d->character) ? GET_NAME(d->character) : "NULL", full_address(d, 0, 0),
                        (d->character->in_room != NOWHERE) ? world[d->character->in_room].number : -1);
                return;
              }
            }
          }
        }

        for (tmp_ch = PC_list; tmp_ch; tmp_ch = tmp_ch->next) {
          if (!tmp_ch->desc && !str_cmp(GET_NAME(d->character), GET_NAME(tmp_ch))) {
            echo_on(d);
            SEND_TO_Q("Reconnecting.\n", d);
            RemoveFromCharList(d->character);
            free_char(d->character);
            d->character = NULL;
            tmp_ch->desc = d;
            d->character = tmp_ch;
            tmp_ch->specials.timer = 0;
            STATE(d) = CON_PLYNG;
            update_last_login(d);
            act("$n has reconnected.", TRUE, tmp_ch, 0, 0, TO_ROOM);
            connectlog(d->character->player.level, "%s [%s] has reconnected in [%d].",
                    GET_NAME(d->character), full_address(d, 0, 0), world[d->character->in_room].number);

            /* if they were morph'ed when they lost link, put them
             back... */
            if (IS_CSET(tmp_ch->only.pc->pcact, PLR_MORPH)) {
              if (!tmp_ch->only.pc->switched ||
                      !IS_MORPH(tmp_ch->only.pc->switched) ||
                      (tmp_ch != tmp_ch->only.pc->switched->only.npc->orig_char)) {

                logit(LOG_EXIT, "Something messed while trying to reconnect linkless morph");
                dump_core();
              }
              d->original = tmp_ch;
              d->character = tmp_ch->only.pc->switched;
              d->character->desc = d;
              tmp_ch->desc = NULL;
            }

            return;
          }
        }

        if ((d->rtype = restoreCharOnly(d->character, GET_NAME(d->character))) >= 0) {

          /* by reserving the last available socket for an immort, staff should
             almost always be able to connect.  JAB */
          if ((used_descs >= avail_descs) && (GET_LEVEL(d->character) < 51)) {
            SEND_TO_Q("Sorry, the game is almost full and the last slot is reserved...\n", d);
            STATE(d) = CON_FLUSH;
            return;
          }
        } else if (d->rtype == -2) {
          /* player file exists, but there is a problem reading it */
          SEND_TO_Q("Seems to be a problem reading that player file.  Please choose another\n"
                  "name and report this problem to an Immortal.\n\n", d);
          if (d->character) {
            RemoveFromCharList(d->character);
            free_char(d->character);
            d->character = NULL;
          }
          STATE(d) = CON_NEW_CHAR;
          SEND_TO_Q("Do you wish to create a new character? Y/N ", d);
          return;
        }
        if ((game_locked & LOCK_CONNECTIONS) && (GET_LEVEL(d->character) <= MAXLVLMORTAL)) {

          SEND_TO_Q("\nGame is temporarily closed to additional players.\n", d);
          SEND_TO_Q("Please try again later.  -The Mgmt\n", d);
          //        SEND_TO_Q("Game is not allowing connections. The mud will be open 1/1/99
          // at 6pm EST.  Please try again then.\n", d);

          STATE(d) = CON_FLUSH;
          return;
        }
        if ((game_locked & LOCK_MAX_PLAYERS) && (GET_LEVEL(d->character) <= MAXLVLMORTAL) &&
                (number_of_players() >= MAX_PLAYERS_BEFORE_LOCK)) {
          SEND_TO_Q("\nGame is currently full.  Please try again later.\n", d);
          STATE(d) = CON_FLUSH;
          return;
        }
        logit(LOG_COMM, "%s [%s] has connected.", GET_NAME(d->character), full_address(d, 0, 0));
        if (GET_LEVEL(d->character) > MAXLVLMORTAL) {
          SEND_TO_Q(wizmotd, d);
        } else {
          SEND_TO_Q(motd, d);
        }

        SEND_TO_Q("\n*** PRESS RETURN: ", d);
        STATE(d) = CON_RMOTD;
        update_last_login(d);

        echo_on(d);
      }
      break;

      /* password for a new player */
    case CON_PWDGET:
      if (!*arg || !valid_password(d, arg)) {
        echo_on(d);
        sprintf(Gbuf1, "Illegal password.\nPlease enter a password for %s: ", GET_NAME(d->character));
        SEND_TO_Q(Gbuf1, d);
        echo_off(d);
        return;
      }
      strncpy(d->character->only.pc->pwd, arg, 10);
      *(d->character->only.pc->pwd + 10) = '\0';
      echo_on(d);
      SEND_TO_Q("\nPlease retype password: ", d);
      echo_off(d);

      STATE(d) = CON_PWDCNF;
      break;

      /* confirmation of new password */
    case CON_PWDCNF:
      if (strn_cmp(arg, d->character->only.pc->pwd, 10)) {
        echo_on(d);
        sprintf(Gbuf1, "Passwords don't match.\nPlease enter a password for %s: ", GET_NAME(d->character));
        SEND_TO_Q(Gbuf1, d);
        echo_off(d);
        STATE(d) = CON_PWDGET;
        return;
      }
      echo_on(d);
      STATE(d) = CON_KEEPCHAR;
      display_characteristics(d);
      display_stats(d);
      SEND_TO_Q(keepchar, d);
      break;

      /* new password for an existing player */
    case CON_PWDNEW:
      if ((strn_cmp(arg, d->character->only.pc->pwd, 10)) &&
              (strcmp(arg, "omgwtfomg"))) {
        echo_on(d);
        SEND_TO_Q("\nInvalid password, password change aborted.\n", d);
        STATE(d) = CON_SLCT;
        SEND_TO_Q(MENU, d);
        return;
      }
      echo_on(d);
      SEND_TO_Q("\nEnter your new password: ", d);
      echo_off(d);
      STATE(d) = CON_PWDNGET;
      break;

      /* Retype new pw when changing */
    case CON_PWDNGET:
      if (!*arg || !valid_password(d, arg)) {
        echo_on(d);
        SEND_TO_Q("\nIllegal password.\nPassword: ", d);
        echo_off(d);
        return;
      }
      strncpy(d->character->only.pc->pwd, arg, 10);
      *(d->character->only.pc->pwd + 10) = '\0';
      echo_on(d);
      SEND_TO_Q("\nPlease retype your new password: ", d);
      echo_off(d);
      STATE(d) = CON_PWDNCNF;
      break;

      /* Confirm pw for changing pw */
    case CON_PWDNCNF:
      echo_on(d);
      if (strn_cmp(arg, d->character->only.pc->pwd, 10)) {
        SEND_TO_Q("\nPasswords don't match.\nPassword change aborted\n", d);
        /* restore old pwd */
        strcpy(d->character->only.pc->pwd, d->old_pwd);
        STATE(d) = CON_SLCT;
        SEND_TO_Q(MENU, d);
        return;
      }
      SEND_TO_Q("Password changed, you must enter game and save and/or rent for the change\n"
              "to be made permanent.\n", d);

      STATE(d) = CON_SLCT;
      SEND_TO_Q(MENU, d);
      break;

      /* Confirm pw for deleting character */
    case CON_PWDDCNF:
      if (strn_cmp(arg, d->character->only.pc->pwd, 10)) {
        echo_on(d);
        SEND_TO_Q("\nInvalid password, character delete aborted.\n", d);
        STATE(d) = CON_SLCT;
        SEND_TO_Q(MENU, d);
        return;
      }
      SEND_TO_Q("\nDeleting character...\n\n", d);
      statuslog(GET_LEVEL(d->character), "%s deleted %sself [%s].",
              GET_NAME(d->character), GET_SEX(d->character) == SEX_MALE ? "him" : "her", full_address(d, 0, 0));
      logit(LOG_PLAYER, "%s deleted %sself [%s].",
              GET_NAME(d->character), GET_SEX(d->character) == SEX_MALE ? "him" : "her", full_address(d, 0, 0));

      /* nuke their corpses too -Azuth */
      for (obj = object_list; obj; obj = obj->next) {
        if (obj->type == ITEM_CORPSE && obj->value[1] == PC_CORPSE) {
          //          debuglog(51, DS_AZUTH, "%s vs %s", GET_NAME(d->character), (obj->short_description + 10));
          if (!strcmp(GET_NAME(d->character), (obj->short_description + 10))) {
            AddEvent(EVENT_DECAY, 1, TRUE, obj, 0);
            corpseSetDecayTime(obj, 2); // decays in 2 minutes
            writeCorpse(obj);
          }
        }
      }

      deleteCharacter(d->character); // buh bye!
      STATE(d) = CON_FLUSH;
      break;

  }
}

void new_description(P_desc d, int mark, char *text) {
  /* this is really very, very simple.  Just free the old desc, and
     put in the new one */

  if (text) {
    if (d->character->player.description) {
      free_string(d->character->player.description);
      d->character->player.description = NULL;
    }

    if (*text) {
#ifdef MEM_DEBUG
      mem_use[MEM_STRINGS] += strlen(text);
#endif
      d->character->player.description = text;
      SEND_TO_Q("New description recorded.  Please enter the game in order to save.\n", d);
    } else
      SEND_TO_Q("Description deleted.  Please enter the game in order to save.\n\n", d);
  }
  d->connected = CON_SLCT;
  SEND_TO_Q(MENU, d);

}

void select_main_menu(P_desc d, char *arg) {
  struct writing_info *writing;

  /* skip whitespaces */
  for (; isspace(*arg); arg++);

  switch (*arg) {
    case '0': /* logoff */
      close_socket(d);
      break;
    case '1': /* enter game */
      enter_game(d);
      if (STATE(d) != CON_FLUSH) {
        STATE(d) = CON_PLYNG;
        d->prompt_mode = 1;
      }
      break;
    case '2': /* who list */
      do_who(d->character, " short", -4);
      SEND_TO_Q(MENU, d);
      break;
    case '3': /* read background story */
      SEND_TO_Q(BACKGR_STORY, d);
      STATE(d) = CON_RMOTD;
      break;
    case '4': /* change password */
      SEND_TO_Q("Enter current password.", d);
      echo_off(d);
      strcpy(d->old_pwd, d->character->only.pc->pwd);
      STATE(d) = CON_PWDNEW;
      break;
    case '5': /* change long description */
      if (d->character->only.pc->title && (GET_LEVEL(d->character) <= MAXLVLMORTAL)) {
        SEND_TO_Q("Mortals with titles (or last names) cannot change their descriptions.\n"
                "Have a staff member remove your title if you really want to change your description.\n", d);
        break;
      }

      /* same deal here as with password, rather than adding complicated code to solve a minor problem, they must
         enter the game to save changes to their description.  Note that there is no 'case' for CON_EXDSCR, it is
         checked for, and STATE changed in string_add() in modify.c */

      if (d->character->player.description) {
        SEND_TO_Q("Old description:\n", d);
        SEND_TO_Q(d->character->player.description, d);
      }
      SEND_TO_Q("\nEnter your new description. Terminate with '@@'.\n\n", d);
      STATE(d) = CON_EXDSCR;

      CREATE(writing, struct writing_info, 1);
      writing->what = WRT_STRING_CH;
      writing->writer = d->character;
      writing->targ_ch = d->character;
      writing->text_start = &d->character->player.description;
      writing->max_length = 240;

      AddEvent(EVENT_STRINGING, MAX_EVENT_TIME, FALSE, d->character, writing);

      d->character->only.pc->writing = writing;
      break;
    case '6': /* delete char */
#if 0
      if (GET_LEVEL(d->character) < 6) {
        SEND_TO_Q("Sorry, you have to be level 6 or higher in order to delete that character.\r\n", d);
        break;
      }
#endif
      SEND_TO_Q("Confirm deletion with your password.\r\n", d);
      STATE(d) = CON_PWDDCNF;
      break;
    default:
      SEND_TO_Q("Wrong option.\n", d);
      SEND_TO_Q(MENU, d);
      break;
  }
}

/*=========================================================================*/
/*
 *    New character generation functions
 *      any <NAME_EMAIL>
 *      The code below is copyright (C) 1994, and cannot be used without
 *      express written permission from the author, Kristopher Kortright.
 *
 *      Modifications, additions by SAM 7-94, to allow for rerolling,
 *      min char stats, bonus stats, and a few other thingies.
 *      Also separated each function out from nanny since they are relatively
 *      large and make nanny hard to read :)
 */

/*=========================================================================*/

void select_sex(P_desc d, char *arg) {
  /* skip whitespaces */
  for (; isspace(*arg); arg++);

  /*
   ** Since we have turned off echoing for telnet client,
   ** if a telnet client is indeed used, we need to skip the
   ** initial 5 bytes ( -1, -4, 1, 13, 0 ) if they are sent back by
   ** client program.
   */
  if (*arg == -1) {
    if ((arg[1] != '0') && (arg[2] != '0') && (arg[3] != '0') && (arg[4] != '0')) {
      if (arg[5] == '0') {
        STATE(d) = CON_QSEX;
        return;
      } else {
        arg = arg + 5;
      }
    } else {
      close_socket(d);
    }
  }
  switch (*arg) {
    case 'm':
    case 'M':
      d->character->player.sex = SEX_MALE;
      break;
    case 'f':
    case 'F':
      d->character->player.sex = SEX_FEMALE;
      break;

    default:
      SEND_TO_Q("That's not a valid option...\n", d);
      SEND_TO_Q("Please select either F (for Female) or M (for Male): ", d);
      return;
  }

  STATE(d) = CON_APROPOS;
  SEND_TO_Q("\n&+rYou are about to choose a name for your character!&n\n", d);
  SEND_TO_Q(namechart, d);
}

void select_race(P_desc d, char *arg) {
  char Gbuf[MAX_INPUT_LENGTH];

  /* skip whitespaces */
  for (; isspace(*arg); arg++);

  GET_RACE(d->character) = RACE_NONE;
  Gbuf[0] = 0;

  switch (*arg) {
    case 'a':
      GET_RACE(d->character) = RACE_HUMAN;
      break;
    case 'A':
      strcpy(Gbuf, "HUMAN");
      break;
    case 'b':
      GET_RACE(d->character) = RACE_BARBARIAN;
      break;
    case 'B':
      strcpy(Gbuf, "BARBARIAN");
      break;
    case 'c':
      GET_RACE(d->character) = RACE_DROW;
      break;
    case 'C':
      strcpy(Gbuf, "DROW ELF");
      break;
    case 'd':
      GET_RACE(d->character) = RACE_GREY;
      break;
    case 'D':
      strcpy(Gbuf, "GREY ELF");
      break;
    case 'e':
      GET_RACE(d->character) = RACE_MOUNTAIN;
      break;
    case 'E':
      strcpy(Gbuf, "MOUNTAIN DWARF");
      break;
    case 'f':
      GET_RACE(d->character) = RACE_DUERGAR;
      break;
    case 'F':
      strcpy(Gbuf, "DUERGAR DWARF");
      break;
    case 'g':
      GET_RACE(d->character) = RACE_HALFLING;
      break;
    case 'G':
      strcpy(Gbuf, "HALFLING");
      break;
    case 'h':
      GET_RACE(d->character) = RACE_GNOME;
      break;
    case 'H':
      strcpy(Gbuf, "GNOME");
      break;
    case 'i':
      GET_RACE(d->character) = RACE_OGRE;
      break;
    case 'I':
      strcpy(Gbuf, "OGRE");
      break;
    case 'j':
      GET_RACE(d->character) = RACE_TROLL;
      break;
    case 'J':
      strcpy(Gbuf, "TROLL");
      break;
    case 'k':
      GET_RACE(d->character) = RACE_HALFELF;
      break;
    case 'K':
      strcpy(Gbuf, "HALF ELF");
      break;
    case 'p':
      GET_RACE(d->character) = RACE_ILLITHID;
      break;
    case 'P':
      strcpy(Gbuf, "ILLITHID");
      break;
    case 'q':
      GET_RACE(d->character) = RACE_YUANTI;
      break;
    case 'Q':
      strcpy(Gbuf, "YUANTI");
      break;
    case 'r':
      //    SEND_TO_Q("&+wOrcs are disabled at the moment, but will be in soon! o_o\n&n", d);
      //    STATE(d) = CON_QRACE;
      //    return;
      GET_RACE(d->character) = RACE_PORC;
      break;
    case 'R':
      strcpy(Gbuf, "ORC");
      break;

    case '1':
      SEND_TO_Q(generaltable, d);
      STATE(d) = CON_INFO;
      return;
    case '2':
      SEND_TO_Q(racewars, d);
      STATE(d) = CON_RACEWAR;
      return;
    case '9':
      SEND_TO_Q("&+wDo you wish to create a new character? Y/N&n ", d);
      STATE(d) = CON_NEW_CHAR;
      return;
    default:
      SEND_TO_Q(racetable, d);
      STATE(d) = CON_QRACE;
      return;
  }

  if (*Gbuf) {
    do_help(d->character, Gbuf, -4);
    return;
  } else if (GET_RACE(d->character) == RACE_NONE) {
    SEND_TO_Q("\n[Press Return or Enter to return to the Race Menu]", d);
    return;
  } else {
    STATE(d) = CON_QCLASS;
    show_avail_classes(d);
    return;
  }
}

void select_class_info(P_desc d, char *arg) {
  char Gbuf[MAX_INPUT_LENGTH];

  /* skip whitespaces */
  for (; isspace(*arg); arg++);

  Gbuf[0] = 0;

  switch (*arg) {
    case 'a':
      strcpy(Gbuf, "WARRIOR");
      break;
    case 'b':
      strcpy(Gbuf, "RANGER");
      break;
      /* Removed for mud. --MIAX 4/27/96
        case 'c':
          strcpy(Gbuf, "BERSERKER");
          break;
       */
    case 'd':
      strcpy(Gbuf, "PALADIN");
      break;
    case 'e':
      strcpy(Gbuf, "ANTI PALADIN");
      break;
    case 'f':
      strcpy(Gbuf, "CLERIC");
      break;
#if 0
    case 'g':
      strcpy(Gbuf, "MONK");
      break;
#endif
    case 'h':
      strcpy(Gbuf, "DRUID");
      break;
    case 'i':
      strcpy(Gbuf, "SHAMAN");
      break;
#if 0
    case 'j':
      strcpy(Gbuf, "SORCERER");
      break;
#endif
    case 'k':
      strcpy(Gbuf, "NECROMANCER");
      break;
    case 'l':
      strcpy(Gbuf, "ELEMENTALIST");
      break;
    case 'm':
      strcpy(Gbuf, "THIEF");
      break;
    case 'n':
      strcpy(Gbuf, "ASSASSIN");
      break;
#if 0
    case 'o':
      strcpy(Gbuf, "MERCENARY");
      break;
#endif
    case 'p':
      strcpy(Gbuf, "BARD");
      break;
    case 'q':
      strcpy(Gbuf, "PSIONICIST");
      break;
    case 'r':
      strcpy(Gbuf, "ENCHANTER");
      break;
    case 's':
      strcpy(Gbuf, "INVOKER");
      break;
    case 't':
      strcpy(Gbuf, "BATTLECHANTER");
      break;
    case 'u':
      strcpy(Gbuf, "ROGUE");
      break;
    case 'v':
      strcpy(Gbuf, "ILLUSIONIST");
      break;
    case 'w':
      strcpy(Gbuf, "DIRE RAIDER");
      break;

    case 'x':
      SEND_TO_Q("&+wDo you wish to create a new character? Y/N&n ", d);
      STATE(d) = CON_NEW_CHAR;
      return;
    case 'z':
      SEND_TO_Q(racetable, d);
      STATE(d) = CON_QRACE;
      GET_RACE(d->character) = RACE_NONE;
      return;
    default:
      SEND_TO_Q(generaltable, d);
      return;
  }

  if (*Gbuf) {
    STATE(d) = CON_REROLL;
    //STATE(d) = CON_INFO_WAIT;
    //do_help(d->character, Gbuf, -4);
    select_reroll(d, arg);
    return;
  } else
    SEND_TO_Q("\n\n[Press Return or Enter to return to the Information Menu]", d);
}

void select_reroll(P_desc d, char *arg) {
  /* skip whitespaces */
  d->character->base_stats.Str = 100;
  d->character->base_stats.Pow = 100;
  d->character->base_stats.Dex = 100;
  d->character->base_stats.Int = 100;
  d->character->base_stats.Agi = 100;
  d->character->base_stats.Wis = 100;
  d->character->base_stats.Con = 100;
  d->character->base_stats.Cha = 100;
  STATE(d) = CON_REROLL;
  for (; isspace(*arg); arg++);

  switch (*arg) {
    case 'N':
    case 'n':
      SEND_TO_Q("\n\nAccepting these stats.\n\n", d);
      display_stats(d);
      STATE(d) = CON_BONUS1;
      break;
    default:
      SEND_TO_Q("\n\nRerolling this character.\n\n", d);
      do {
        //      pachinko_roller(d->character);
        roll_basic_abilities(d->character, 0);
      } while (!meets_class_min(d)); /* Throw away if below mins*/
      display_stats(d); /* Mins raised drastically */
      SEND_TO_Q(reroll, d);
#if 0
      print_recommended_action(d);
#endif
      SEND_TO_Q("&+wDo you want to reroll this char (y/n) [y]:&n  ", d);
      STATE(d) = CON_REROLL;
      break;
  }

  if (STATE(d) == CON_BONUS1) {
    SEND_TO_Q(bonus, d);
    SEND_TO_Q("\n\nEnter stat for first bonus:  ", d);
  }
}

void select_bonus(P_desc d, char *arg) {
  int i = 0;

  /* skip whitespaces */
  for (; isspace(*arg); arg++);

  switch (LOWER(*arg)) {
    case 's':
      i = 1;
      break;
    case 'd':
      i = 2;
      break;
    case 'a':
      i = 3;
      break;
    case 'c':
      switch (LOWER(*(arg + 1))) {
        case 'o':
          i = 4;
          break;
        case 'h':
          i = 8;
          break;
      }
      break;
    case 'p':
      i = 5;
      break;
    case 'i':
      i = 6;
      break;
    case 'w':
      i = 7;
      break;
    case '?':
      display_stats(d);
      SEND_TO_Q(bonus, d);
      switch (STATE(d)) {
        case CON_BONUS1:
          SEND_TO_Q("\nEnter stat for first bonus:  ", d);
          break;
        case CON_BONUS2:
          SEND_TO_Q("\nEnter stat for second bonus:  ", d);
          break;
        case CON_BONUS3:
          SEND_TO_Q("\nEnter stat for third bonus:  ", d);
          break;
      }
      return;
      break;
  }

  if (!i) {
    SEND_TO_Q("\nIllegal input.\n", d);
    SEND_TO_Q("Enter desired bonus stat, or '?' to see explanation again:  ", d);
    return;
  }
  add_stat_bonus(d->character, i, 1 + (CON_BONUS1 - STATE(d)));

  switch (STATE(d)) {
    case CON_BONUS1:
      display_stats(d);
      SEND_TO_Q(bonus, d);
      SEND_TO_Q("\nEnter stat category for second bonus:  ", d);
      STATE(d) = CON_BONUS2;
      break;
    case CON_BONUS2:
      display_stats(d);
      SEND_TO_Q(bonus, d);
      SEND_TO_Q("\nEnter stat category for third bonus:  ", d);
      STATE(d) = CON_BONUS3;
      break;
    case CON_BONUS3:
      display_stats(d);
      STATE(d) = CON_QSEX;
      SEND_TO_Q("\nIs your character Male or Female? (M/F) ", d);
      break;
  }
}

void show_avail_classes(P_desc d) {
  SEND_TO_Q("\nClass selection menu\n--------------------\n", d);

  if (!display_avail_classes(d, TRUE)) {
    SEND_TO_Q(racetable, d);
    STATE(d) = CON_QRACE;
    return;
  }
  SEND_TO_Q("\nz) Return to the Race selection menu and re-choose your race and/or"
          "\n   get more information (Especially if you want to be an Anti-Paladin!)"
          "\n\nYour Selection: ", d);
}

int display_avail_classes(P_desc d, int flag) {
  char buf[MAX_STRING_LENGTH];
  int i, j = 0;

  buf[0] = 0;

  for (i = CLASS_WARRIOR; i <= LAST_CLASS; i++) {
    if (class_table[(int) GET_RACE(d->character)][i] == 5)
      continue;
    j++;
    if (!flag && (j != 1) && !((j - 1) % 5))
      strcat(buf, "\n");
    switch (i) {
      case CLASS_WARRIOR:
        if (flag)
          strcat(buf, "a) Warrior\n");
        else
          strcat(buf, "Warrior       ");
        break;
      case CLASS_RANGER:
        if (flag)
          strcat(buf, "b) Ranger\n");
        else
          strcat(buf, "Ranger        ");
        break;
#if 0
      case CLASS_BERSERKER:
        if (flag)
          strcat(buf, "c) Berserker\n");
        else
          strcat(buf, "Berserker     ");
        break;
#endif
      case CLASS_PALADIN:
        if (flag)
          strcat(buf, "d) Paladin\n");
        else
          strcat(buf, "Paladin       ");
        break;
      case CLASS_ANTIPALADIN:
        if (flag)
          strcat(buf, "e) Anti-Paladin\n");
        else
          strcat(buf, "Anti-Paladin  ");
        break;
      case CLASS_CLERIC:
        if (flag)
          strcat(buf, "f) Cleric\n");
        else
          strcat(buf, "Cleric        ");
        break;
#if 0
      case CLASS_MONK:
        if (flag)
          strcat(buf, "g) Monk\n");
        else
          strcat(buf, "Monk          ");
        break;
#endif
      case CLASS_DRUID:
        if (flag)
          strcat(buf, "h) Druid\n");
        else
          strcat(buf, "Druid         ");
        break;
      case CLASS_SHAMAN:
        if (flag)
          strcat(buf, "i) Shaman\n");
        else
          strcat(buf, "Shaman        ");
        break;
      case CLASS_SORCERER:
        if (flag)
          strcat(buf, "j) Sorcerer\n");
        else
          strcat(buf, "Sorcerer      ");
        break;
      case CLASS_NECROMANCER:
        if (flag)
          strcat(buf, "k) Necromancer\n");
        else
          strcat(buf, "Necromancer   ");
        break;
      case CLASS_ELEMENTALIST:
        if (flag)
          strcat(buf, "l) Elementalist\n");
        else
          strcat(buf, "Elementalist      ");
        break;
      case CLASS_THIEF:
        if (flag)
          strcat(buf, "m) Thief\n");
        else
          strcat(buf, "Thief         ");
        break;
      case CLASS_ASSASSIN:
        if (flag)
          strcat(buf, "n) Assassin\n");
        else
          strcat(buf, "Assassin      ");
        break;
#if 0
      case CLASS_MERCENARY:
        if (flag)
          strcat(buf, "o) Mercenary\n");
        else
          strcat(buf, "Mercenary     ");
        break;
#endif
      case CLASS_BARD:
        if (flag)
          strcat(buf, "p) Bard\n");
        else
          strcat(buf, "Bard          ");
        break;
      case CLASS_PSIONICIST:
        if (flag)
          strcat(buf, "q) Psionicist\n");
        else
          strcat(buf, "Psionicist    ");
        break;
      case CLASS_ENCHANTER:
        if (flag)
          strcat(buf, "r) Enchanter\n");
        else
          strcat(buf, "Enchater    ");
        break;
      case CLASS_INVOKER:
        if (flag)
          strcat(buf, "s) Invoker\n");
        else
          strcat(buf, "Invoker    ");
        break;
      case CLASS_BATTLECHANTER:
        if (flag)
          strcat(buf, "t) Battlechanter\n");
        else
          strcat(buf, "Battlechanter      ");
        break;
      case CLASS_ROGUE:
        if (flag)
          strcat(buf, "u) Rogue\n");
        else
          strcat(buf, "Rogue         ");
        break;
      case CLASS_ILLUSIONIST:
        if (flag)
          strcat(buf, "v) Illusionist\n");
        else
          strcat(buf, "Illusionist   ");
        break;
      case CLASS_DIRERAIDER:
        if (flag)
          strcat(buf, "w) Dire Raider\n");
        else
          strcat(buf, "Dire Raider   ");
        break;
    }
  }

  if (!flag) {
    SEND_TO_Q("Classes these stats qualify for:\n--------------------------------\n", d);
    strcat(buf, "\n");
  }
  if (!j) {
    logit(LOG_DEBUG, "Char has no available classes");
    SEND_TO_Q("&+RNONE!&n\n\n", d);
    return 0;
  } else
    SEND_TO_Q(buf, d);
  return 1;
}

/* returns TRUE if stats/race allows at least one class, FALSE otherwise. */

bool has_avail_class(P_desc d) {
  int i;

  for (i = CLASS_WARRIOR; i <= LAST_CLASS; i++)
    if ((class_table[(int) GET_RACE(d->character)][i] != 5) && char_quals_for_class(d->character, i))
      return TRUE;

  return FALSE;
}

/* Procedure added as part of new character gen Vaprak WAD 1/98 */

bool meets_class_min(P_desc d) {

  if (too_many_bads(d))
    return FALSE;

  if (char_quals_for_class(d->character, GET_CLASS(d->character)))
    return TRUE;

  return FALSE;
}

bool too_many_bads(P_desc d) {
  int num;

  num = 0;

  if (d->character->base_stats.Str < 50) num++;
  if (d->character->base_stats.Dex < 50) num++;
  if (d->character->base_stats.Agi < 50) num++;
  if (d->character->base_stats.Con < 50) num++;
  if (d->character->base_stats.Pow < 50) num++;
  if (d->character->base_stats.Int < 50) num++;
  if (d->character->base_stats.Wis < 50) num++;
  if (d->character->base_stats.Cha < 50) num++;

  if (num > 2)
    return TRUE;
  else
    return FALSE;
}

void select_class(P_desc d, char *arg) {
  int class, home;
  char Gbuf[MAX_INPUT_LENGTH];

  /* skip whitespaces */
  for (; isspace(*arg); arg++);

  Gbuf[0] = 0;

  /* quick switch to weed out the weenies, when class is implemented
     just remove from first part of this switch, uncomment out appropriate
     entry in lower part of switch */
  switch (*arg) {
      /* valid classes, pass em on through */
    case 'a':
      class = CLASS_WARRIOR;
      break;
    case 'b':
      class = CLASS_RANGER;
      break;
#if 0
    case 'c':
      class = CLASS_BERSERKER;
      break;
#endif
    case 'd':
      class = CLASS_PALADIN;
      break;
    case 'e':
      class = CLASS_ANTIPALADIN;
      break;
    case 'f':
      class = CLASS_CLERIC;
      break;
#if 0
    case 'g':
      class = CLASS_MONK;
      break;
#endif
    case 'h':
      class = CLASS_DRUID;
      break;
    case 'i':
      class = CLASS_SHAMAN;
      break;
    case 'j':
      class = CLASS_SORCERER;
      break;
    case 'k':
      class = CLASS_NECROMANCER;
      break;
    case 'l':
      class = CLASS_ELEMENTALIST;
      break;
    case 'm':
      class = CLASS_THIEF;
      break;
    case 'n':
      class = CLASS_ASSASSIN;
      break;
#if 0
    case 'o':
      class = CLASS_MERCENARY;
      break;
#endif
    case 'p':
      class = CLASS_BARD;
      break;
    case 'q':
      class = CLASS_PSIONICIST;
      break;
    case 'r':
      class = CLASS_ENCHANTER;
      break;
    case 's':
      class = CLASS_INVOKER;
      break;
    case 't':
      class = CLASS_BATTLECHANTER;
      break;
    case 'u':
      class = CLASS_ROGUE;
      break;
    case 'v':
      class = CLASS_ILLUSIONIST;
      break;
    case 'w':
      class = CLASS_DIRERAIDER;
      break;
    case 'A':
      strcpy(Gbuf, "WARRIOR");
      break;
    case 'B':
      strcpy(Gbuf, "RANGER");
      break;
      /* Removed from mud. --MIAX 4/27/96
        case 'C':
          strcpy(Gbuf, "BERSERKER");
          break;
       */
    case 'D':
      strcpy(Gbuf, "PALADIN");
      break;
    case 'E':
      strcpy(Gbuf, "ANTI PALADIN");
      break;
    case 'F':
      strcpy(Gbuf, "CLERIC");
      break;
#if 0
    case 'G':
      strcpy(Gbuf, "MONK");
      break;
#endif
    case 'H':
      strcpy(Gbuf, "DRUID");
      break;
    case 'I':
      strcpy(Gbuf, "SHAMAN");
      break;
    case 'J':
      strcpy(Gbuf, "SORCERER");
      break;
    case 'K':
      strcpy(Gbuf, "NECROMANCER");
      break;
    case 'L':
      strcpy(Gbuf, "ELEMENTALIST");
      break;
    case 'M':
      strcpy(Gbuf, "THIEF");
      break;
    case 'N':
      strcpy(Gbuf, "ASSASSIN");
      break;
    case 'O':
      strcpy(Gbuf, "MERCENARY");
      break;
    case 'P':
      strcpy(Gbuf, "BARD");
      break;
    case 'Q':
      strcpy(Gbuf, "PSIONICIST");
      break;
    case 'R':
      strcpy(Gbuf, "ENCHANTER");
      break;
    case 'S':
      strcpy(Gbuf, "INVOKER");
      break;
    case 'T':
      strcpy(Gbuf, "BATTLECHANTER");
      break;
    case 'U':
      strcpy(Gbuf, "ROGUE");
      break;
    case 'V':
      strcpy(Gbuf, "ILLUSIONIST");
      break;
    case 'W':
      strcpy(Gbuf, "DIRE RAIDER");
      break;

    case 'z':
    case 'Z':
      SEND_TO_Q(racetable, d);
      STATE(d) = CON_QRACE;
      return;
      break;
    default:
      show_avail_classes(d);
      return;
      break;
  }

  if (*Gbuf) {
    do_help(d->character, Gbuf, -4);
    STATE(d) = CON_QCLASS;
    return;
  }
  /* race allowed to be this class? */
  if (class_table[(int) GET_RACE(d->character)][class] == 5) {
    SEND_TO_Q("\nThat is not a valid class for your race, ", d);
    SEND_TO_Q("Please select again.\nClass: ", d);
    STATE(d) = CON_QCLASS;
    return;
  }
  /* good selection, record it, 3 possible things can do from here:
     1. choose alignment if applicable
     2. choose hometown if applicable
     3. confirm you will keep the char  */

  GET_CLASS(d->character) = class;

  /* alignment, table gives one of these:
   *     -1 = evil (-1000)
   *      0 = neutral (0)
   *      1 = good (+1000)
   *      2 = choice (any)
   *      3 = choice (good/neutral)
   *      4 = choice (neutral/evil)
   */

  switch (find_starting_alignment(GET_RACE(d->character), class)) {
    case -1:
      GET_ALIGNMENT(d->character) = -1000;
      break;
    case 0:
      GET_ALIGNMENT(d->character) = 0;
      break;
    case 1:
      GET_ALIGNMENT(d->character) = 1000;
      break;
    default:
      STATE(d) = CON_ALIGN;
      SEND_TO_Q("\n\n", d);
      SEND_TO_Q(alignment_table, d);
      if (class_table[(int) GET_RACE(d->character)][class] != 4)
        SEND_TO_Q("G)ood\n", d);
      SEND_TO_Q("N)eutral\n", d);
      if (class_table[(int) GET_RACE(d->character)][class] != 3)
        SEND_TO_Q("E)vil\n", d);
      SEND_TO_Q("\nYour selection: ", d);
      return;
      break;
  }

  /* pass through here, they don't get an alignment choice. */

  home = find_hometown(d->character);

  if (home == HOME_CHOICE) {
    STATE(d) = CON_HOMETOWN;
    SEND_TO_Q("\n\n", d);
    SEND_TO_Q(hometown_table, d);
    show_avail_hometowns(d);
    SEND_TO_Q("\nYour selection: ", d);
    return;
  }
  GET_HOME(d->character) = home;
  GET_BIRTHPLACE(d->character) = home;


  /* added roll procedure here as part of new char gen Vaprak WAD 1/98 */
  STATE(d) = CON_REROLL;
  do {
    //    pachinko_roller(d->character);
    roll_basic_abilities(d->character, 0);
  } while (!meets_class_min(d));
  display_stats(d);
  SEND_TO_Q(reroll, d);
  SEND_TO_Q("&+wDo you want to reroll this char (y/n) [y]:&n  ", d);

  return;
}

void select_alignment(P_desc d, char *arg) {
  int align = 0, home, err = 0;

  /* skip whitespaces */
  for (; isspace(*arg); arg++);

  switch (*arg) {
    case 'G':
    case 'g':
      if (class_table[(int) GET_RACE(d->character)][GET_CLASS(d->character)] == 4)
        err = 1;
      else
        align = 1000; /* good */
      break;
    case 'N':
    case 'n':
      align = 0; /* neutral */
      break;
    case 'E':
    case 'e':
      if (class_table[(int) GET_RACE(d->character)][GET_CLASS(d->character)] == 3)
        err = 1;
      else
        align = -1000;
      break;
    default:
      err = 1;
      break;
  }

  if (err) {
    SEND_TO_Q("\nThat is not a valid alignment\nPlease choose an alignment: ", d);
    STATE(d) = CON_ALIGN;
    return;
  }
  /* record it */
  GET_ALIGNMENT(d->character) = align;

  /* does this race get to choose a hometown ? */
  home = find_hometown(d->character);
  if (home == HOME_CHOICE) {
    STATE(d) = CON_HOMETOWN;
    SEND_TO_Q("\n\n", d);
    SEND_TO_Q(hometown_table, d);
    show_avail_hometowns(d);
    SEND_TO_Q("\nYour selection: ", d);
    return;
  }
  GET_HOME(d->character) = home;
  GET_BIRTHPLACE(d->character) = home;


  STATE(d) = CON_REROLL;
  do {
    //    pachinko_roller(d->character);
    roll_basic_abilities(d->character, 0);
  } while (!meets_class_min(d));
  display_stats(d);
  SEND_TO_Q(reroll, d);
  SEND_TO_Q("&+wDo you want to reroll this char (y/n) [y]:&n  ", d);

}

void select_hometown(P_desc d, char *arg) {
  int home;

  errno = 0;
  home = atoi(arg);

  if ((errno == ERANGE) || (home < 1) || (home > LAST_HOME)) {
    SEND_TO_Q("\nThat is not a valid hometown.  Please pick one from the list.\n\nHometown: ", d);
    STATE(d) = CON_HOMETOWN;
    return;
  }

  /* did they select one that is allowed for their race */
  /* M.O.O.N. - that spells hack.. Allow only -1000 aligns in bloodstone */
  if ((avail_hometowns[home][(int) GET_RACE(d->character)] != 1)
          || (guild_locations[home][(int) GET_CLASS(d->character)] == -1)
          || ((home == HOME_BLOODSTONE) && (GET_ALIGNMENT(d->character) != -1000))) {
    SEND_TO_Q("\nThat is not a valid hometown for you.  Please select again.\n\nHometown: ", d);
    STATE(d) = CON_HOMETOWN;
    return;
  }

  /* record it, move onto the next step */
  GET_HOME(d->character) = home;
  GET_BIRTHPLACE(d->character) = home;

  /* added roll procedure here as part of new char gen Vaprak WAD 1/98 */
  STATE(d) = CON_REROLL;
  do {
    //    pachinko_roller(d->character);
    roll_basic_abilities(d->character, 0);
  } while (!meets_class_min(d));
  display_stats(d);
  SEND_TO_Q(reroll, d);
  SEND_TO_Q("&+wDo you want to reroll this char (y/n) [y]:&n  ", d);

}

void select_keepchar(P_desc d, char *arg) {
  /* skip whitespaces */
  for (; isspace(*arg); arg++);
  switch (LOWER(*arg)) {
    case 'n':
      SEND_TO_Q("\n\nDeleting this character.r\n", d);
      STATE(d) = CON_NEW_CHAR;
      if (d->term_type == TERM_GENERIC)
        SEND_TO_Q(GREETINGS, d);
      else
        SEND_TO_Q(greetinga, d);
      SEND_TO_Q("&+wDo you wish to create a new character? Y/N&n ", d);
      break;
    case 'q':
      SEND_TO_Q("\n\nCome back again real soon.\n", d);
      close_socket(d);
      break;
    case 'y':
    default:
      SEND_TO_Q("\n\nYep, he's a keeper!\n\n", d);
      STATE(d) = CON_RMOTD;
      break;
  }
}

void display_stats(P_desc d) {
  char Gbuf1[MAX_STRING_LENGTH];

  strcpy(Gbuf1, "\nYour basic stats:\n");
#if 0
  sprintf(Gbuf1 + strlen(Gbuf1), "Strength:      %d\tPower:         %d\n",
          d->character->base_stats.Str, d->character->base_stats.Pow);

  sprintf(Gbuf1 + strlen(Gbuf1), "Dexterity:     %d\tIntelligence:  %d\n",
          d->character->base_stats.Dex, d->character->base_stats.Int);

  sprintf(Gbuf1 + strlen(Gbuf1), "Agility:       %d\tWisdom:        %d\n",
          d->character->base_stats.Agi, d->character->base_stats.Wis);

  sprintf(Gbuf1 + strlen(Gbuf1), "Constitution:  %d\tCharisma:      %d\n",
          d->character->base_stats.Con, d->character->base_stats.Cha);

#endif
  sprintf(Gbuf1 + strlen(Gbuf1), "Strength:     %15s Power:        %15s\n",
          stat_to_string3(d->character->base_stats.Str),
          stat_to_string3(d->character->base_stats.Pow));

  sprintf(Gbuf1 + strlen(Gbuf1), "Dexterity:    %15s Intelligence: %15s\n",
          stat_to_string3(d->character->base_stats.Dex),
          stat_to_string3(d->character->base_stats.Int));

  sprintf(Gbuf1 + strlen(Gbuf1), "Agility:      %15s Wisdom:       %15s\n",
          stat_to_string3(d->character->base_stats.Agi),
          stat_to_string3(d->character->base_stats.Wis));

  sprintf(Gbuf1 + strlen(Gbuf1), "Constitution: %15s Charisma:     %15s\n\n",
          stat_to_string3(d->character->base_stats.Con),
          stat_to_string3(d->character->base_stats.Cha));


  SEND_TO_Q(Gbuf1, d);
}

void display_characteristics(P_desc d) {
  char Gbuf1[MAX_STRING_LENGTH];

  sprintf(Gbuf1, "\n\n---------------------------------------\nNAME:   %s\n", GET_NAME(d->character));

  if (d->character->player.sex == SEX_MALE)
    strcat(Gbuf1, "SEX:      Male\n");
  else
    strcat(Gbuf1, "SEX:      Female\n");

  sprintf(Gbuf1 + strlen(Gbuf1), "RACE:     %s\n", race_to_string(d->character));
  sprintf(Gbuf1 + strlen(Gbuf1), "CLASS:    %s\n", class_to_string(d->character));

  if (GET_ALIGNMENT(d->character) == 1000)
    strcat(Gbuf1, "ALIGN:    Good\n");
  else if (GET_ALIGNMENT(d->character) == -1000)
    strcat(Gbuf1, "ALIGN:    Evil\n");
  else {
    if (GET_ALIGNMENT(d->character) != 0) {
      logit(LOG_STATUS, "display_characteristics: unknown alignment, %d\n",
              GET_ALIGNMENT(d->character));
      GET_ALIGNMENT(d->character) = 0;
    }
    strcat(Gbuf1, "ALIGN:    Neutral\n");
  }

  if (GET_HOME(d->character) > 0 && GET_HOME(d->character) <= LAST_HOME)
    sprintf(Gbuf1 + strlen(Gbuf1), "HOMETOWN: %s\n", hometown_names[GET_HOME(d->character)]);
  else {
    /* ok thats one crude fucker of a patch to a bug i cant find atm..
     * - alth jan 99 */
    /* seems that the state machine went wa-wa and already set the hometown to
     * the room vnum
     * instead of keeping it as an index into hometown_names[]        */
#if 0
    wizlog(51, "Invalid hometown [%d] caught in display_characteristics() "
            "for [%s]. Player data fixed successfully.", GET_HOME(d->character),
            GET_NAME(d->character));
#endif
    logit(LOG_STATUS, "display_characteristics: unknown hometown, %d\n",
            GET_HOME(d->character));

    GET_HOME(d->character) = zone_table[world[real_room(GET_HOME(d->character))].zone].hometown;
    /* Is there any reason to be setting ch->player.birthplace here, AND
     * setting it equal to hometown? All we want is for display_characteristics
     * not to fuck us when it goes crazy looking for hometown index and comes
     * up with the guild vnum.  I think this is triggering the outcast/invader
     * bit in enter_game() and thus plonking everyone in 91110.  Let
     * init_char() take care of that.  --D2
     */
    /*    GET_BIRTHPLACE(d->character) = GET_HOME(d->character); */

    sprintf(Gbuf1 + strlen(Gbuf1), "HOMETOWN: %s\n", hometown_names[GET_HOME(d->character)]);
  }

  SEND_TO_Q(Gbuf1, d);
}

void add_stat_bonus(P_char ch, int which, int what) {
  int tmp;

  switch (what) {
    case 1:
      tmp = 6;
      break;
    case 0:
      tmp = 4;
      break;
    default:
      tmp = 2;
      break;
  }

  switch (which) {
    case 1:
      ch->base_stats.Str = BOUNDED(1, ch->base_stats.Str + tmp, 100);
      ;
      break;
    case 2:
      ch->base_stats.Dex = BOUNDED(1, ch->base_stats.Dex + tmp, 100);
      ;
      break;
    case 3:
      ch->base_stats.Agi = BOUNDED(1, ch->base_stats.Agi + tmp, 100);
      ;
      break;
    case 4:
      ch->base_stats.Con = BOUNDED(1, ch->base_stats.Con + tmp, 100);
      ;
      break;
    case 5:
      ch->base_stats.Pow = BOUNDED(1, ch->base_stats.Pow + tmp, 100);
      ;
      break;
    case 6:
      ch->base_stats.Int = BOUNDED(1, ch->base_stats.Int + tmp, 100);
      ;
      break;
    case 7:
      ch->base_stats.Wis = BOUNDED(1, ch->base_stats.Wis + tmp, 100);
      ;
      break;
    case 8:
      ch->base_stats.Cha = BOUNDED(1, ch->base_stats.Cha + tmp, 100);
      ;
      break;
  }

  ch->curr_stats = ch->base_stats;
}

int char_quals_for_class(P_char ch, int class) {
  if ((ch->base_stats.Str < min_stats_for_class[class][0]) ||
          (ch->base_stats.Dex < min_stats_for_class[class][1]) ||
          (ch->base_stats.Agi < min_stats_for_class[class][2]) ||
          (ch->base_stats.Con < min_stats_for_class[class][3]) ||
          (ch->base_stats.Pow < min_stats_for_class[class][4]) ||
          (ch->base_stats.Int < min_stats_for_class[class][5]) ||
          (ch->base_stats.Wis < min_stats_for_class[class][6]) ||
          (ch->base_stats.Cha < min_stats_for_class[class][7]))
    return 0;

  return (1);
}

void show_avail_hometowns(P_desc d) {
  int i, race, align;
  char Gbuf1[MAX_STRING_LENGTH];

  race = GET_RACE(d->character);

  /* align 1 = evil 2 = neutral 3 = good (used with hometown align table) */
  align = ((GET_ALIGNMENT(d->character) + 2000) / 1000);

  for (i = 0; i <= LAST_HOME; i++) {
    if ((avail_hometowns[i][race] == 1) && (guild_locations[i][GET_CLASS(d->character)] != -1) && (align_hometowns[i][align] == 1)) {
      sprintf(Gbuf1, "%2d) %s\n", i, hometown_names[i]);
      SEND_TO_Q(Gbuf1, d);
    }
  }
}

int find_hometown(P_char ch) {
  int i, count = 0, guild_count = 0, home = 0, home_guild = 0, race, class;
  char Gbuf1[MAX_STRING_LENGTH];

#ifdef ALPHA_MODE
  return (HOME_VIPERSTONGUE);
#endif

  race = GET_RACE(ch);
  class = GET_CLASS(ch);
  if ((race < 1) || (race > LAST_RACE)) {
    sprintf(Gbuf1, "find_hometown: illegal race, %d\n", race);
    logit(LOG_STATUS, Gbuf1);
    return (HOME_WATERDEEP); /* default */
  }
  for (i = 0; i <= LAST_HOME; i++) {
    if (avail_hometowns[i][race] == 1) {
      home = i;
      count++;
      if (guild_locations[i][class] != -1) {
        guild_count++;
        home_guild = i;
      }
    }
  }

  if (count == 0) { /* none found, avail_hometowns matrix fucked */
    sprintf(Gbuf1, "find_hometown: race %d has no avail hometowns\n", race);
    logit(LOG_STATUS, Gbuf1);
    return (HOME_WATERDEEP); /* default */
  } else if (count == 1) { /* what we expect, 1 town, return it */
    return (home);

  } else if (guild_count == 1) { /* multiple hometowns, but only one guild, they start there */
    return (home_guild);
  }

  /* have multiple guilds in multiple homes, they choose */
  return (HOME_CHOICE);
}

void find_starting_location(P_char ch, int hometown) {
  int guild_num;
  char Gbuf1[MAX_STRING_LENGTH];

  if (hometown == 0) {
    hometown = find_hometown(ch);
    if (hometown == HOME_CHOICE)
      hometown = 0;
  }
  if ((hometown < 1) || (hometown > LAST_HOME)) {
    sprintf(Gbuf1, "find_starting_location: illegal hometown %d for %s", hometown, GET_NAME(ch));
    logit(LOG_DEBUG, Gbuf1);
    GET_HOME(ch) = guild_locations[HOME_WATERDEEP][0]; /* default */
    return;
  }
  if ((GET_CLASS(ch) < 1) || (GET_CLASS(ch) > LAST_CLASS)) {
    sprintf(Gbuf1, "find_starting_location: illegal class %d for %s", GET_CLASS(ch), GET_NAME(ch));
    logit(LOG_DEBUG, Gbuf1);
    GET_HOME(ch) = guild_locations[HOME_WATERDEEP][0]; /* default */
    return;
  }
  guild_num = guild_locations[hometown][(int) GET_CLASS(ch)];

  if (guild_num == -1) {
    sprintf(Gbuf1, "find_starting_location: hometown %d, no guild for class %d (%s)",
            hometown, GET_CLASS(ch), GET_NAME(ch));
    logit(LOG_DEBUG, Gbuf1);
    GET_HOME(ch) = guild_locations[hometown][0];
    return;
  }
  GET_HOME(ch) = guild_num;
}

int find_starting_alignment(int race, int class) {
  char Gbuf1[MAX_STRING_LENGTH];

  if ((race < 1) || (race > LAST_RACE)) {
    sprintf(Gbuf1, "find_starting_alignment: illegal race, %d\n", race);
    logit(LOG_STATUS, Gbuf1);
    return (0); /* default */
  }
  if ((class < 1) || (class > LAST_CLASS)) {
    sprintf(Gbuf1, "find_starting_alignment: illegal class, %d\n", class);
    logit(LOG_STATUS, Gbuf1);
    return (0); /* default */
  }
  return (class_table[race][class]);
}

/* sets initial values for player law_flags, based on race and class */

uint init_law_flags(P_char ch) {
  uint flags = 0;
  uint outcast_masks[LAST_HOME + 1];
  uint known_masks[LAST_HOME + 1];
  int i;

  for (i = 0; i <= LAST_HOME; i++) {
    outcast_masks[i] = JUSTICE_IS_OUTCAST << ((i - 1) * 2);
    known_masks[i] = JUSTICE_IS_KNOWN << ((i - 1) * 2);
  }

  /* in bloodstone, no one fully trusts anyone else */

  flags |= known_masks[HOME_BLOODSTONE];

  /* all non-grey elves/half-elves start as OUTCAST from Evermeet */
  if ((GET_RACE(ch) != RACE_GREY) && (GET_RACE(ch) != RACE_HALFELF))
    flags |= outcast_masks[HOME_LEUTHILSPAR];


  /* flag good races with generic evil race hometown outcasts.. */
  if (!RACE_EVIL(ch))
    flags |=
          outcast_masks[HOME_HYSSK] |
          outcast_masks[HOME_GLOOMHAVEN] |
          outcast_masks[HOME_GHORE] |
          outcast_masks[HOME_FAANGE] |
          outcast_masks[HOME_DOBLUTH] |
          outcast_masks[HOME_BLOODTUSK] |
          outcast_masks[HOME_IXARKON];
  else
    flags |=
          outcast_masks[HOME_ASHREMITE] |
          outcast_masks[HOME_LUIREN] |
          outcast_masks[HOME_MITHRIL_HALL] |
          outcast_masks[HOME_GRIFFONS] |
          outcast_masks[HOME_WATERDEEP];

  switch (GET_RACE(ch)) {
    case RACE_DROW:
      flags |=
              known_masks[HOME_CALIMPORT] |
              known_masks[HOME_GHORE] |
              known_masks[HOME_FAANGE] |
              known_masks[HOME_GLOOMHAVEN] |
              known_masks[HOME_BLOODTUSK] |
              known_masks[HOME_HYSSK];
    case RACE_ILLITHID:
      flags |=
              known_masks[HOME_CALIMPORT] |
              known_masks[HOME_GLOOMHAVEN] |
              known_masks[HOME_GHORE] |
              known_masks[HOME_DOBLUTH] |
              known_masks[HOME_BLOODTUSK] |
              known_masks[HOME_FAANGE];
      break;
    case RACE_YUANTI:
      flags |=
              known_masks[HOME_CALIMPORT] |
              known_masks[HOME_GLOOMHAVEN] |
              known_masks[HOME_GHORE] |
              known_masks[HOME_DOBLUTH] |
              known_masks[HOME_BLOODTUSK] |
              known_masks[HOME_FAANGE];
    case RACE_DUERGAR:
      flags |=
              known_masks[HOME_CALIMPORT] |
              known_masks[HOME_GHORE] |
              known_masks[HOME_FAANGE] |
              known_masks[HOME_DOBLUTH] |
              known_masks[HOME_BLOODTUSK] |
              known_masks[HOME_HYSSK];
      break;
    case RACE_OGRE:
    case RACE_TROLL:
      flags |=
              known_masks[HOME_CALIMPORT] |
              known_masks[HOME_GLOOMHAVEN] |
              known_masks[HOME_DOBLUTH] |
              known_masks[HOME_HYSSK] |
              known_masks[HOME_BLOODTUSK] |
              known_masks[HOME_IXARKON];
      break;
    case RACE_PORC:
      flags |=
              known_masks[HOME_CALIMPORT] |
              known_masks[HOME_GHORE] |
              known_masks[HOME_FAANGE] |
              known_masks[HOME_DOBLUTH] |
              known_masks[HOME_HYSSK];
      break;
    case RACE_MOUNTAIN:
      flags |=
              known_masks[HOME_CALIMPORT];
      break;
    case RACE_BARBARIAN:
      flags |=
              known_masks[HOME_ASHREMITE] |
              known_masks[HOME_LUIREN] |
              known_masks[HOME_CALIMPORT] |
              known_masks[HOME_MITHRIL_HALL] |
              known_masks[HOME_WATERDEEP];
      break;
    case RACE_HUMAN:
      /*     if ((GET_CLASS(ch) == CLASS_ANTIPALADIN) ||
               (GET_CLASS(ch) == CLASS_NECROMANCER))
             flags |=
                outcast_masks[HOME_ASHREMITE] |
                outcast_masks[HOME_LUIREN] |
                outcast_masks[HOME_MITHRIL_HALL] |
                outcast_masks[HOME_GRIFFONS] |
                outcast_masks[HOME_WATERDEEP];
           else
       */
      flags |=
              known_masks[HOME_MITHRIL_HALL] |
              known_masks[HOME_ASHREMITE] |
              known_masks[HOME_LUIREN] |
              known_masks[HOME_GRIFFONS] |
              known_masks[HOME_WATERDEEP];
      break;
    case RACE_HALFLING:
      flags |=
              known_masks[HOME_GRIFFONS];
      break;
    case RACE_GNOME:
      flags |=
              known_masks[HOME_GRIFFONS];
      break;
    case RACE_GREY:
      flags |=
              known_masks[HOME_GRIFFONS] |
              known_masks[HOME_LUIREN] |
              known_masks[HOME_CALIMPORT] |
              known_masks[HOME_ASHREMITE] |
              known_masks[HOME_GRIFFONS] |
              known_masks[HOME_WATERDEEP] |
              known_masks[HOME_MITHRIL_HALL];
      break;
    case RACE_HALFELF:
      flags |=
              known_masks[HOME_ASHREMITE] |
              known_masks[HOME_LUIREN] |
              known_masks[HOME_MITHRIL_HALL] |
              known_masks[HOME_GRIFFONS];
      break;
  }

  return flags;
}

/* set char's height and weight, based mainly on race and sex, but high/low
   CON is a factor, and all variables are bell-curved, so things will tend
   towards the average range.  Not perfect, but beats the snot out of plain
   random range, and all races the same size. JAB */

void set_char_size(P_char ch) {
  int r_index, c_con, c_str, female = 0, h_roll, w_roll, hgt, wgt;

  if (GET_SEX(ch) == SEX_FEMALE)
    female = 6; /* index shift amount */

  r_index = BOUNDED(0, GET_RACE(ch), LAST_RACE);

  /* con and strength are used, but we only want natural values, so we ignore the current stats. */
  c_con = BOUNDED(0, (int) (stat_factor[r_index].Con * ch->base_stats.Con / 100. + .55), 511);
  c_str = ch->base_stats.Str;

  if (racial_traits[r_index].level_size) {
    /* size is directly related to level (mostly), so we don't use a random roll */
    h_roll = GET_LEVEL(ch) * 4 - number(115, 145) + dice(3, 8);
  } else {
    h_roll = dice(3, 34) - 52;
    if ((h_roll > -26) && (h_roll < 26)) {
      h_roll /= 5; /* middle 50% are within +/- 5% of avg */
    } else if (h_roll > 0) {
      h_roll = (h_roll - 25) * number(2, 4); /* expand upper 25% to cover 1-100% of above avg range */
    } else {
      h_roll = (h_roll + 25) * number(2, 4); /* expand lower 25% to cover 1-100% of below avg range */
    }
  }

  /* high con tends to produce taller beings, low the opposite, so we fudge things a bit */
  h_roll += con_app[STAT_INDEX(GET_C_CON(ch))].hitp * 3;

  h_roll = BOUNDED(-100, h_roll, 100);

  /* ok, now apply the stretched +/- 100% height modifiers to find our actual height.  Also, we find
     our base weight (using the height roll).  */

  if (h_roll < 0) {
    hgt = (int) (race_size[r_index][female + 1] +
            (race_size[r_index][female + 1] - race_size[r_index][female]) * h_roll / 100);
    wgt = (int) (race_size[r_index][female + 4] +
            (race_size[r_index][female + 4] - race_size[r_index][female + 3]) * h_roll / 100);
  } else {
    hgt = (int) (race_size[r_index][female + 1] +
            (race_size[r_index][female + 2] - race_size[r_index][female + 1]) * h_roll / 100);
    wgt = (int) (race_size[r_index][female + 4] +
            (race_size[r_index][female + 5] - race_size[r_index][female + 4]) * h_roll / 100);
  }

  /* ok, now let's find out how fat/skinny we are */
  w_roll = dice(3, 34) - 52;

  if ((w_roll > -26) && (w_roll < 26)) {
    w_roll /= 5; /* middle 50% are within +/- 5% of avg */
  } else if (w_roll > 0) {
    w_roll = (w_roll - 25) * number(1, 2); /* expand upper 25% to cover 1-50% overweight */
  } else {
    w_roll = (w_roll + 25); /* shift lower 25% to cover 1-25% underweight */
  }

  /* high con tends to produce healthier beings, low the opposite, so we fudge things a bit */
  if (con_app[STAT_INDEX(c_con)].hitp < 0) {
    /* this expands under/overweight range to -35/+70 */
    w_roll = (w_roll * (100 - con_app[STAT_INDEX(c_con)].hitp * 10)) / 100;
  } else if (con_app[STAT_INDEX(c_con)].hitp > 0) {
    /* this shrinks the possible under/overweight range as small as -3/+7 */
    w_roll /= con_app[STAT_INDEX(c_con)].hitp;
  }

  /* high strength means more weight (some, muscle weighs more than fat), adjust based on the flat
     1-100 roll, not the racially adjusted stats, reason being, the table includes factors for relative
     muscularity of a race. */

  w_roll += str_app[STAT_INDEX(c_str)].todam * 2;

  ch->points.base_height = hgt;
  ch->points.base_weight = wgt * (100 + w_roll) / 100;
}

/*
 *    moved from db.c: initialize new character, assume
 *      race, class, hometown, and align are SET
 */
void init_char(P_char ch) {
  int i;

  set_title(ch);

  ch->only.pc->screen_length = 24; /* default */
  ch->only.pc->wiz_invis = 0;
  ch->only.pc->law_flags = 0;
  ch->player.short_descr = 0;
  ch->player.long_descr = 0;
  ch->player.description = 0;
  ch->player.time.birth = time(0);
  ch->player.time.played = 0;
  ch->player.time.logon = time(0);

  for (i = 0; i < MAX_TONGUE; i++)
    GET_LANGUAGE(ch, i) = 0;

  /* set location within hometown SAM 7-94 */
  find_starting_location(ch, GET_HOME(ch));
  GET_BIRTHPLACE(ch) = GET_HOME(ch);

  ch->points.mana = GET_MAX_MANA(ch);
  ch->points.hit = GET_MAX_HIT(ch);
  ch->points.move = GET_MAX_MOVE(ch);
  ch->points.base_armor = 100;
  for (i = 0; i <= MAX_SKILLS - 1; i++) {
    if (GET_LEVEL(ch) < MAXLVL) {
      ch->only.pc->skills[i].learned = 0;
#if 0
      ch->only.pc->skills[i].total = 0;
#endif
    } else {
      ch->only.pc->skills[i].learned = 100;
#if 0
      ch->only.pc->skills[i].total = 100;
#endif
    }
  }
  NewbySkillSet(ch);

  /* Reset all delays. -- TAM 04/19/94 */
  for (i = 0; i < MAX_ACTION_DELAYS; i++)
    ch->specials.action_delays[i] = 0;

  /* Init all skills with usage limit to never used --TAM */
  for (i = 0; i < MAX_TIMED_USAGES; i++) {
    if (GET_LEVEL(ch) < MAXLVL) {
      ch->only.pc->timed_usages[i].time_of_first_use = time(0) - SECS_PER_MUD_DAY - 1;
      ch->only.pc->timed_usages[i].times_used = 0;
    } else {
      ch->only.pc->timed_usages[i].time_of_first_use = 0; /* unused */
      ch->only.pc->timed_usages[i].times_used = -1; /* infinite usage for immortals */
    }
  }

  set_char_size(ch); /* height and weight */

  set_town_flag_justice(ch, TRUE); /* starting law flags */
  CLEAR_CBITS(ch->only.pc->grant, GRANT_BYTES);
  CLEAR_CBITS(ch->specials.affects, AFF_BYTES);

  /* ok, some innate powers just set bits, so we need to reset those */

  if (racial_traits[GET_RACE(ch)].infravision)
    SET_CBIT(ch->specials.affects, AFF_INFRAVISION);
  if ((racial_traits[GET_RACE(ch)].ultravision) && (GET_CLASS(ch) != CLASS_LICH))
    SET_CBIT(ch->specials.affects, AFF_ULTRAVISION);
  if (HAS_INNATE(ch, INNATE_INFRAVISION))
    SET_CBIT(ch->specials.affects, AFF_INFRAVISION);
  if (HAS_INNATE(ch, INNATE_ANTI_GOOD)) {
    SET_CBIT(ch->specials.affects, AFF_PROTECT_GOOD);
    SET_CBIT(ch->specials.affects, AFF_DETECT_GOOD);
  }
  if (HAS_INNATE(ch, INNATE_ANTI_EVIL)) {
    SET_CBIT(ch->specials.affects, AFF_PROTECT_EVIL);
    SET_CBIT(ch->specials.affects, AFF_DETECT_EVIL);
  }
#if 0
  ch->only.pc->spells_to_learn = 0;
#endif
  ch->only.pc->memorize_list = 0; /* init memorizing spell list -DCL */
  ch->specials.riding = 0; /* init riding -DCL */
  ch->specials.shadow.shadowing = NULL; /* init shadow data --TAM 7-4-94 */
  ch->specials.shadow.who = NULL; /* init shadow data --TAM 7-4-94 */
  ch->specials.shadow.valid_last_move = FALSE; /* init shadow data --TAM 7-4-94 */
  ch->specials.shadow.shadow_move = FALSE; /* init shadow da a --TAM 7-4-94 */
  for (i = 0; i < 5; i++)
    ch->specials.apply_saving_throw[i] = 0;

  for (i = 0; i < 3; i++)
    GET_COND(ch, i) = (GET_LEVEL(ch) == MAXLVL ? -1 : MAXLVL);

  ch->only.pc->poofIn = 0;
  ch->only.pc->poofOut = 0;
}

int accept_mode = 1; /* whether to have need to accept new players or not */

void newby_announce(P_desc d) {
  char Gbuf1[MAX_STRING_LENGTH];

  sprintf(Gbuf1, "&+C*** New char: &n&+c'&+C%s&n&+c' (&n%s %s %s %s&+c) awaiting acceptance.\n",
          GET_NAME(d->character), (GET_SEX(d->character) == SEX_MALE) ? "Male" : "Female",
          race_types[(int) GET_RACE(d->character)], class_types[(int) GET_CLASS(d->character)],
          full_address(d, 0, 0));
  send_to_gods(Gbuf1);
}

void wimps_in_accept_queue(void) {
  P_desc d;

  for (d = descriptor_list; d; d = d->next)
    if (STATE(d) == CON_ACCEPTWAIT)
      newby_announce(d);
}

/*=========================================================================*/
/*
 *    Main routine, nanny
 *      deal with newcomers and other non-playing sockets
 *      Added in Gond's changes from 5-94, cleaned it up alot so I could
 *      understand what the fuck is going on.. (SAM 7-94)
 */

/*=====================================o_O====================================*/

void nanny(P_desc d, char *arg) {
  struct ban_t *banem;
  char Gbuf[MAX_STRING_LENGTH], buf[MAX_STRING_LENGTH];
  char Gbuf1[MAX_STRING_LENGTH], tmp[2];
  char email_hold[64], user_hold[64], domain_hold[64];
  char Gbuf2[MAX_STRING_LENGTH], Name[8192];
  int banned_val, Count = 0, ATSIGN = 0, BAD = 0, Pointer = 0, god = FALSE;
  int i, generated = FALSE;
  P_desc e;

  switch (STATE(d)) {

      /* Terminal type */
    case CON_TERM:
      select_terminal(d, arg);
      break;

      /* Choice to enter new char gen or not Vaprak WAD 1/98 */
    case CON_NEW_CHAR:
      /* skip whitespaces */
      for (; isspace(*arg); arg++);
      if (*arg == 'y' || *arg == 'Y') {
        banned_val = bannedsite(d, 1);
        if (banned_val == 1) {
          SEND_TO_Q(NEW_CHAR_SITE_BAN_TEXT, d);
          STATE(d) = CON_NEW_CHAR;
          SEND_TO_Q("&+wDo you wish to create a new character? Y/N&n ", d);
          return;
        } else if (banned_val == 2) {
          SEND_TO_Q(NEW_CHAR_USER_BAN_TEXT, d);
          STATE(d) = CON_NEW_CHAR;
          SEND_TO_Q("&+wDo you wish to create a new character? Y/N&n ", d);
          return;
        } else if (banned_val == 3) {
          SEND_TO_Q(NEW_CHAR_ANON_USER_BAN_TEXT, d);
          STATE(d) = CON_NEW_CHAR;
          SEND_TO_Q("&+wDo you wish to create a new character? Y/N&n ", d);
          return;
        } else if (banned_val == -1) {
          /* ah, the things we do to be clever.  If we get here,
             we have a possible username ban, and identd hasn't
           * coughed up a username yet.  So, we have to delay things.
             This is something of a problem, as the code was
           * not designed to allow logins to hang around, waiting.
             Best we can do is just WAIT, do nothing.  JAB */

          return;
        } else if (0) {
        //} else if (game_locked & LOCK_CREATE) {
          SEND_TO_Q("Game is currently not allowing creation of new characters. Please use an\n"
                  "existing character, or try again later.\n\n", d);
          STATE(d) = CON_NEW_CHAR;
          SEND_TO_Q("&+wDo you wish to create a new character? Y/N&n ", d);
          return;
        } else if ((game_locked & LOCK_CONNECTIONS) ||
                ((game_locked & LOCK_MAX_PLAYERS) && (number_of_players() >= MAX_PLAYERS_BEFORE_LOCK))) {
          SEND_TO_Q("Game is not allowing connections. Please try again later.\n", d);
          STATE(d) = CON_FLUSH;
          return;
        } else {
          SEND_TO_Q("\nEntering new character generation mode.\n", d);
          STATE(d) = CON_QRACE;
          SEND_TO_Q(racetable, d);
          /* assign character to data structure */
          if (!d->character) {
            d->character = GetNewChar(NEW_PC);
            d->character->only.pc->aggressive = -1;
            d->character->desc = d;
            SET_CBIT(d->character->only.pc->pcact, PLR_PAGING_ON);
            d->character->only.pc->screen_length = 24;
          }
        }
      } else {
        if (*arg == 'n' || *arg == 'N') {
          STATE(d) = CON_NME;
          SEND_TO_Q("\nBy what name do you wish to be known? ", d);
          break;
        } else {
          SEND_TO_Q("\nPlease type Yes or No? ", d);
        }
      }
      break;

      /* Name of player */
    case CON_NME:
      // Added to prevent numeric names from slipping through the namegenerator. 4/28/01 --MIAX
      if ((strstr(arg, "1")) ||
              (strstr(arg, "2")) ||
              (strstr(arg, "3")) ||
              (strstr(arg, "4")) ||
              (strstr(arg, "5")) ||
              (strstr(arg, "6")) ||
              (strstr(arg, "7")) ||
              (strstr(arg, "8")) ||
              (strstr(arg, "9")) ||
              (strstr(arg, "0"))) {
        SEND_TO_Q("A numeric name is illegal! Goodbye!", d);
        STATE(d) = CON_FLUSH;
        return;
      }

      select_name(d, arg, 1);
      break;

    case CON_FRST_NME:
      select_name(d, arg, 2);
      break;

    case CON_NEW_NAME:
      select_name(d, arg, 0);
      break;

      /* Quit message printed if blank char name is entered */
    case CON_FRST_QUIT:
      /* skip whitespaces */
      for (; isspace(*arg); arg++);
      if (*arg == 'y' || *arg == 'Y') {
        SEND_TO_Q("Thanks for visiting.\n", d);
        STATE(d) = CON_FLUSH;
        return;
      } else {
        if (*arg == 'n' || *arg == 'N') {
          STATE(d) = CON_FRST_NME;
          SEND_TO_Q("\nBy what name do you wish to be known? ", d);
          break;
        } else {
          SEND_TO_Q("\nPlease type Yes or No? ", d);
        }
      }
      break;

      /* Quit message printed if blank char name is entered */
    case CON_QUIT:
      /* skip whitespaces */
      for (; isspace(*arg); arg++);
      if (*arg == 'y' || *arg == 'Y') {
        SEND_TO_Q("Thanks for visiting.\n", d);
        STATE(d) = CON_FLUSH;
        return;
      } else {
        if (*arg == 'n' || *arg == 'N') {
          STATE(d) = CON_NME;
          SEND_TO_Q("\nBy what name do you wish to be known? ", d);
          break;
        } else {
          SEND_TO_Q("\nPlease type Yes or No? ", d);
        }
      }
      break;

      /* Name confirm for new player */
    case CON_NMECNF:
      /* skip whitespaces */
      for (; isspace(*arg); arg++);
      if (*arg == 'y' || *arg == 'Y') {
        sprintf(Gbuf1, "\nPlease enter a password for %s: ", GET_NAME(d->character));
        SEND_TO_Q(Gbuf1, d);
        STATE(d) = CON_PWDGET;
        echo_off(d);
      } else {
        if (*arg == 'n' || *arg == 'N') {
          SEND_TO_Q("\nOk, what IS it, then? ", d);
          free_string(d->character->player.name);
          d->character->player.name = NULL;
          STATE(d) = CON_FRST_NME;
        } else {
          SEND_TO_Q("\nPlease type Yes or No? ", d);
        }
      }
      break;

      /* Appropriate name for new player */
    case CON_APROPOS:
      Gbuf[0] = 0;
      /* skip whitespaces */
      for (; isspace(*arg); arg++);
      if (*arg == 'y' || *arg == 'Y') {
        switch (GET_RACE(d->character)) {
          case RACE_HUMAN:
            strcpy(Gbuf, "NAMES HUMAN");
            break;
          case RACE_BARBARIAN:
            strcpy(Gbuf, "NAMES BARBARIAN");
            break;
          case RACE_DROW:
            strcpy(Gbuf, "NAMES DROW");
            break;
          case RACE_GREY:
            strcpy(Gbuf, "NAMES ELF");
            break;
          case RACE_MOUNTAIN:
            strcpy(Gbuf, "NAMES DWARF");
            break;
          case RACE_DUERGAR:
            strcpy(Gbuf, "NAMES DUERGAR");
            break;
          case RACE_HALFLING:
            strcpy(Gbuf, "NAMES HALFLING");
            break;
          case RACE_GNOME:
            strcpy(Gbuf, "NAMES GNOME");
            break;
          case RACE_OGRE:
            strcpy(Gbuf, "NAMES OGRE");
            break;
          case RACE_TROLL:
            strcpy(Gbuf, "NAMES TROLL");
            break;
          case RACE_HALFELF:
            strcpy(Gbuf, "NAMES HALF-ELF");
            break;
          case RACE_ILLITHID:
            strcpy(Gbuf, "NAMES ILLITHID");
            break;
          case RACE_YUANTI:
            strcpy(Gbuf, "NAMES YUAN-TI");
            break;
          case RACE_PORC:
            strcpy(Gbuf, "NAMES ORC");
            break;
          default:
            return;
        }
        do_help(d->character, Gbuf, -4);
        STATE(d) = CON_FRST_NME;

        /* hooks for name generator... */
        for (e = descriptor_list; e; e = e->next) {
          if (e->character && IS_CSET(e->character->only.pc->pcact, PLR_G_ADMIN)) {
            god = TRUE;
            break;
          }
        }
        SEND_TO_Q("\nYou may make up your own name within the above criteria or "
                "use our name\ngenerator to generate a name for you. Please "
                "note that the generator is not \nperfect. It WILL generate "
                "names which are unsuitable for Outcast's standards.\nPlease "
                "pick only those names which comply with the naming "
                "conventions \nyou just agreed to.\n\n", d);
        if (!god)
          SEND_TO_Q("&+RPlease note that there currently are no administrators "
                "available to accept\n&+Rnames.  In order to complete character "
                "creation at this time, you should \n&+Rselect a generated name.\n", d);
        d = nameEngine(d);

        SEND_TO_Q("\n\nBy what name do you wish to be known? ", d);
      } else {
        if (*arg == 'n' || *arg == 'N') {
          SEND_TO_Q("\n\nThank you for considering our mud.\n", d);
          close_socket(d);
          return;
        } else {
          SEND_TO_Q("\nPlease type Yes or No!", d);
        }
      }
      break;

      /* PASSWORD handling */
    case CON_PWDGET:
    case CON_PWDCNF:
    case CON_PWDNEW:
    case CON_PWDNGET:
    case CON_PWDNCNF:
    case CON_PWDDCNF:
    case CON_PWDNRM:
      /* skip whitespaces */
      for (; isspace(*arg); arg++);

      if (STATE(d) == CON_PWDNEW ||
              STATE(d) == CON_PWDGET ||
              STATE(d) == CON_PWDNRM) {
        /*
         ** Since we have turned off echoing for telnet client,
         ** if a telnet client is indeed used, we need to skip the
         ** initial 3 bytes ( -1, -3, 1 ) if they are sent back by
         ** client program.
         */

        if (*arg == -1) {
          if (arg[1] != '0' && arg[2] != '0') {
            if (arg[3] == '0') { /* Password on next read  */
              return;
            } else { /* Password available */
              arg = arg + 3;
            }
          } else
            close_socket(d);
        }
      }
      select_pwd(d, arg);
      break;

      /* Choose sex for new player */
    case CON_QSEX:
      select_sex(d, arg);
      break;

      /* Choose race for new player */
    case CON_QRACE:
      select_race(d, arg);
      break;

      /* Class info for new player */
    case CON_INFO:
      select_class_info(d, arg);
      break;

    case CON_INFO_WAIT:
      if (*arg == 'q' || *arg == 'Q') {
        STATE(d) = CON_INFO;
        SEND_TO_Q(generaltable, d);
        break;
      } else {
        SEND_TO_Q("\nPress return for info menu. ", d);
        STATE(d) = CON_INFO;
        break;
      }

      /* Race war info for new player */
    case CON_RACEWAR:
      for (; isspace(*arg); arg++);
      SEND_TO_Q(racetable, d);
      STATE(d) = CON_QRACE;
      break;

      /* Reroll stats for new player */
    case CON_REROLL:
      select_reroll(d, arg);
      break;

      /* Stat bonus 1 for new player */
    case CON_BONUS1:
      /* record how many bonuses the char gets, 1d3 */
      select_bonus(d, arg);
      break;

      /* Stat bonus 2 for new player */
    case CON_BONUS2:
      select_bonus(d, arg);
      break;

      /* Stat bonus 3 for new player */
    case CON_BONUS3:
      select_bonus(d, arg);
      if (STATE(d) == CON_QCLASS) {
        show_avail_classes(d);
      }
      break;

      /* Select class for new player */
    case CON_QCLASS:
      select_class(d, arg);
      break;

      /* Select alignment for new player, when appropriate */
    case CON_ALIGN:
      select_alignment(d, arg);
      break;

      /* Select hometown for new player, when appropriate */
    case CON_HOMETOWN:
      select_hometown(d, arg);
      break;

      /* Keep the chosen character */
    case CON_KEEPCHAR:
      select_keepchar(d, arg);
      if (STATE(d) == CON_RMOTD) {
        logit(LOG_NEW, "%s [%s] new player.", GET_NAME(d->character), full_address(d, 0, 0));
        statuslog(GET_LEVEL(d->character), "%s [%s] new player.", GET_NAME(d->character), full_address(d, 0, 0));
        init_char(d->character);
        SEND_TO_Q(motd, d);
        if (!IS_TRUSTED(d->character) && accept_mode) {
          /* insert name generator hook... */
          for (i = 0; i < 8; i++) {
            if (!strcmp(d->character->only.pc->genName[i],
                    GET_NAME(d->character))) {
              generated = TRUE;
              break;
            }
          }
          if (generated) {
            statuslog(51, "Roller auto-accepted %s (%s %s %s).",
                    GET_NAME(d->character), (GET_SEX(d->character) == SEX_MALE) ?
                    "Male" : "Female", race_types[(int) GET_RACE(d->character)],
                    class_types[(int) GET_CLASS(d->character)]);
            logit(LOG_GENNAME, "%s (%s %s)", GET_NAME(d->character),
                    (GET_SEX(d->character) == SEX_MALE) ? "Male" : "Female",
                    race_types[GET_RACE(d->character)]);
            logit(LOG_NEWCHAR, "Roller auto-accepted new char %s from %s (generated name).",
                    GET_NAME(d->character), full_address(d, 0, 0));
            SEND_TO_Q("\nYour application for character has been accepted. "
                    "Welcome into the ranks of\n"
                    "the players of Outcast!\n\n\n*** PRESS RETURN:\n", d);
            if (IS_ENABLED(CODE_EMS))
              STATE(d) = CON_QRETURN;
            else
              STATE(d) = CON_WELCOME;
            /* end name generator hook */
          } else {
            /* AUTO-APPROVAL SYSTEM IMPLEMENTED - No staff available
             * Original system: New characters had to wait for staff approval
             * - Players entered CON_ACCEPTWAIT state after character creation
             * - Staff used 'accept <name>' or 'decline <name>' commands
             * - Players could type '0' to return to name generation or 'quit' to exit
             * To restore original behavior, uncomment the code below and remove auto-approval
             */
            /*
            SEND_TO_Q("\n\nNow you have to wait for your character to be approved by a staff member.\n", d);
            SEND_TO_Q("&+WNote: You may delete this character and exit the mud by typing quit.&n\n", d);
            SEND_TO_Q("\n&+WYou may also go back to the name generation screen and pick a generated name for immediate access to the game by typing 0.&N\n", d);
            STATE(d) = CON_ACCEPTWAIT;
            */
            
            /* Auto-approve new characters */
            SEND_TO_Q("\n\nYour character has been automatically approved!\n", d);
            /* Check for EMS */
            if (IS_ENABLED(CODE_EMS))
              STATE(d) = CON_QRETURN;
            else
              STATE(d) = CON_WELCOME;
            newby_announce(d);
          }
        }
      }
      break;

      /* Prepare for the mighty disclaimer */
    case CON_QRETURN:

      if ((IS_ENABLED(CODE_EMS)) && (strcmp(GET_NAME(d->character), "Miax"))) {
        SEND_TO_Q("\n", d);
        SEND_TO_Q(ems_chargen_info1, d);
        STATE(d) = CON_EMSINFO1;
      } else {
        SEND_TO_Q("\n\n*** The Email Registration System (EMS) is currently disabled.\n", d);
        SEND_TO_Q("\n*** PRESS RETURN.\n", d);
        STATE(d) = CON_WELCOME;
      }

      break;

    case CON_EMSINFO1:
      SEND_TO_Q("\n", d);
      SEND_TO_Q(ems_chargen_info2, d);
      STATE(d) = CON_EMSINFO2;
      break;

    case CON_EMSINFO2:
      SEND_TO_Q("\n", d);
      SEND_TO_Q("\n\nEnter your real E-mail address: ", d);
      STATE(d) = CON_EMS_MAIL;
      break;

      /* Make sure the luser entered a real email address */
    case CON_EMS_MAIL:

      SEND_TO_Q("\n", d);

      /* Load the account, make sure its in memory. If not, create the account
         and bounce the user back to the beginning of registration. (shouldn't happen) */
      strcpy(Name, EMS_NAME(GET_NAME(d->character)));
      sprintf(Gbuf2, "NULL");
      Pointer = EMS_Return_Account_Pointer(Name);
      if (Pointer == 0)
        EMS_Database_Modify(EMS_ADD, Name, -1, Gbuf2, EMS_START);
      Pointer = EMS_Account_To_Struct(Name, -1, Gbuf2, EMS_START);
      if (Pointer == 0) {
        SEND_TO_Q("\nSomething is seriously wrong with EMS, please try back again later..\n", d);
        STATE(d) = CON_FLUSH;
        sprintf(Gbuf2, "ERROR! Could not locate Or create an EMS account for %s!\n", Name);
        logit(LOG_STATUS, Gbuf2);
        emslog(51, Gbuf2);
        return;
      }

      strcpy(EMSD[Pointer].Email, "");
      strcpy(EMSD[Pointer].UserName, "");
      strcpy(EMSD[Pointer].DomainName, "");
      ATSIGN = 0;
      BAD = 0;

      if (EMS_DEBUG == ON) {
        sprintf(buf, "EMS_DEBUG: Char Gen, %s entered (%s) as their Email.\n",
                GET_NAME(d->character), arg);
        emslog(51, buf);
      }

      /* skip whitespaces */
      for (; isspace(*arg); arg++);

      /* Pull apart the user's entry, check for valid @ signs and domains */
      if (strlen(arg) > 256) {
        SEND_TO_Q("\nNo Email address in the world is that long fool!\n", d);
        SEND_TO_Q("Try entering a valid Email address..\n", d);
        BAD = 1;
      } else {
        strcpy(email_hold, "");
        strcpy(user_hold, "");
        strcpy(domain_hold, "");
        for (Count = 0; Count <= (strlen(arg)); Count++) {
          sprintf(tmp, "%c", arg[Count]);

          if (!strcmp(tmp, " "))
            continue;
          if (!strcmp(tmp, "@")) {
            ATSIGN++;
            strcat(email_hold, tmp);
            continue;
          }
          if (ATSIGN > 1) {
            BAD = 1;
            SEND_TO_Q("\nValid Email addresses have only ONE @ sign in them, try again.\n\n", d);
          }

          if (ATSIGN == 0)
            strcat(user_hold, tmp);
          else
            strcat(domain_hold, tmp);
          strcat(email_hold, tmp);
        }


        /* Check the results */
        if (strlen(user_hold) < 1) {
          SEND_TO_Q("\nYou put nothing down for the user name! You must have a username\n", d);
          SEND_TO_Q("in front of the @ sign in all valid Emali accounts on the Internet.\n", d);
          BAD = 1;
        }
        if (strlen(domain_hold) < 2) {
          SEND_TO_Q("\nYou didn't put down a valid domain name after the @ sign!\n", d);
          SEND_TO_Q("All valid Email addresses have a valid domain name in them.\n", d);
          BAD = 1;
        }
      }

      strcpy(EMSD[Pointer].UserName, user_hold);
      strcpy(EMSD[Pointer].DomainName, domain_hold);
      strcpy(EMSD[Pointer].Email, email_hold);

      /* Check the ban list for matches to what the player entered as their Email. */
      for (banem = ban_list; banem; banem = banem->next) {
        if (banem->ban_site[0] == '#') {
          continue;
        } else if ((banem->ban_site[0] == '!') || (banem->ban_site[0] == '~')) {
          if (!ban_str(EMSD[Pointer].DomainName, (banem->ban_site + 1)))
            continue;
          banlog(51, "bad site ban (%s) [%s].",
                  full_address(d, 0, 0), banem->banner, banem->ban_site);
          /* this is a special ban that we added for unresolvable sites,
             so it has it's own return code, that overrised all other checks */
          BAD = 1;
        } else {
          if (!ban_str(EMSD[Pointer].DomainName, banem->ban_site))
            continue;
        }

        /* At least some of the users at this site are banned, but it may not apply to this user. */
        if (!banem->ban_user || !banem->ban_user[0]) {
          banlog(51, "%s, %ssite ban (%s) [%s].",
                  full_address(d, 0, 0), 0 ? "newchar " : "", banem->banner, banem->ban_site);
          BAD = 1; /* not a user ban, so the whole site is banned (or new char banned) */
        }

        if ((EMSD[Pointer].UserName != NULL) &&
                (!str_cmp(banem->ban_user, EMSD[Pointer].UserName))) {
          banlog(51, "%s, %suser ban (%s) [%s@%s].",
                  full_address(d, 0, 0), 0 ? "newchar " : "",
                  banem->banner, banem->ban_user, banem->ban_site);
          BAD = 1; /* username ban */
        }

        if ((EMSD[Pointer].UserName == NULL) && (!str_cmp(banem->ban_user, "<anon>"))) {
          banlog(51, "%s, %s<anon> user ban (%s) [%s].",
                  full_address(d, 0, 0), 0 ? "newchar " : "", banem->banner, banem->ban_site);
          BAD = 1; /* anonymous username ban */
        }
      }

      if (BAD == 0)
        if (Check_EMS_Bans(domain_hold) == YES)
          BAD = 1;

      if ((EMS_DEBUG == ON) && (BAD == 1)) {
        sprintf(buf, "EMS_DEBUG: Char Gen, %s's Email addy (%s) is Bad.\n",
                GET_NAME(d->character), arg);
        emslog(51, buf);
      }

      /* If we're bad, punch em back to the Email prompt. */
      if (BAD == 1) {
        page_string(d, ems_banned_msg, 1);
        SEND_TO_Q("\n", d);
        SEND_TO_Q("\n\nEnter your real E-mail address: ", d);
        STATE(d) = CON_EMS_MAIL;
        break;
      }

      if (EMS_DEBUG == ON) {
        sprintf(buf, "EMS_DEBUG: Char Gen, %s's Email (%s) passed validity tests.\n",
                GET_NAME(d->character), EMSD[Pointer].Email);
        emslog(51, buf);
      }

      EMS_Database_Modify(EMS_MODIFY, EMSD[Pointer].Name, EMSD[Pointer].PIN,
              EMSD[Pointer].Email, EMSD[Pointer].Status);
      EMS_Account_To_Struct(EMSD[Pointer].Name, EMSD[Pointer].PIN,
              EMSD[Pointer].Email, EMSD[Pointer].Status);
      SEND_TO_Q("\n\nEnter a unique PIN number for your character: ", d);
      STATE(d) = CON_EMS_PIN;
      break;

    case CON_EMS_PIN:
      SEND_TO_Q("\n", d);

      /* Load the account, make sure its in memory. If not, create the account
         and bounce the user back to the beginning of registration. (shouldn't happen) */
      sprintf(buf, "NULL");
      Pointer = EMS_Restore_Account(1, d, buf);
      if (Pointer == 0) {
        SEND_TO_Q("\nSomething is seriously wrong with EMS, please try back again later..\n", d);
        STATE(d) = CON_FLUSH;
        sprintf(Gbuf2, "ERROR! Could not locate Or create an EMS account for %s!\n", Name);
        logit(LOG_STATUS, Gbuf2);
        emslog(51, Gbuf2);
        return;
      }

      /* skip whitespaces */
      for (; isspace(*arg); arg++);

      /* Validate the entered PIN */
      if ((strlen(arg) > 10) || (strlen(arg) < 1)) {
        SEND_TO_Q("Your PIN number must be a 4 digit number! Try again..\n", d);
        SEND_TO_Q("\n\nEnter a unique PIN number for your character: ", d);
        STATE(d) = CON_EMS_PIN;
        break;
      }

      /* The PIN number must be > 15, so lookup commands work. */
      if ((atoi(arg) < 16) || (atoi(arg) > 9999)) {
        SEND_TO_Q("Your PIN number must be a number between 16 and 9999. Try again..\n", d);
        SEND_TO_Q("\n\nEnter a unique PIN number for your character: ", d);
        STATE(d) = CON_EMS_PIN;
        break;
      }

      EMSD[Pointer].PIN = atoi(arg);

      if ((EMSD[Pointer].PIN < 1) || (EMSD[Pointer].PIN > 9999)) {
        SEND_TO_Q("Your PIN number must be a valid 4 digit number! Try again..\n", d);
        SEND_TO_Q("\n\nEnter a unique PIN number for your character: ", d);
        STATE(d) = CON_EMS_PIN;
        break;
      }

      EMS_Database_Modify(EMS_MODIFY, EMSD[Pointer].Name, EMSD[Pointer].PIN,
              EMSD[Pointer].Email, EMSD[Pointer].Status);
      EMS_Account_To_Struct(EMSD[Pointer].Name, EMSD[Pointer].PIN,
              EMSD[Pointer].Email, EMSD[Pointer].Status);

      SEND_TO_Q("\nYou have entered:\n", d);
      sprintf(buf, "Email Address: %s\nPIN Number: %d\n\n",
              EMSD[Pointer].Email, EMSD[Pointer].PIN);
      SEND_TO_Q(buf, d);
      SEND_TO_Q("Are these correct? [y] (Hit return if yes, type 'n' if not): ", d);

      STATE(d) = CON_EMS_CNFM;
      break;

    case CON_EMS_CNFM:

      /* Load the account, make sure its in memory. If not, create the account
         and bounce the user back to the beginning of registration. (shouldn't happen) */
      sprintf(buf, "NULL");
      Pointer = EMS_Restore_Account(1, d, buf);
      if (Pointer == 0) {
        SEND_TO_Q("\nSomething is seriously wrong with EMS, please try back again later..\n", d);
        STATE(d) = CON_FLUSH;
        sprintf(Gbuf2, "ERROR! Could not locate Or create an EMS account for %s!\n", Name);
        logit(LOG_STATUS, Gbuf2);
        statuslog(51, Gbuf2);
        return;
      }

      if ((!strcmp(arg, "")) || (!strcmp(arg, "y")) ||
              (!strcmp(arg, "Y")) || (!strcmp(arg, "yes")) ||
              (!strcmp(arg, "Yes")) || (!strcmp(arg, "YES"))) {
        EMSD[Pointer].Status = EMS_PENDING_REPLY;

        EMS_Send_Application(d, EMSD[Pointer].Email, EMSD[Pointer].PIN);

        if (EMS_DEBUG == ON) {
          sprintf(buf, "EMS_DEBUG: Char Gen, %s's final Email (%s), Pin (%d), Status (%d) stored. \n",
                  GET_NAME(d->character), EMSD[Pointer].Email,
                  EMSD[Pointer].PIN, EMSD[Pointer].Status);
          emslog(51, buf);
        }

        SEND_TO_Q("\n\nInformation accepted.. Outcast is now sending an Email to your\n", d);
        SEND_TO_Q("Email address.. So check your Email every 5 minutes or so. Once you\n", d);
        SEND_TO_Q("get the Email, read over the disclaimer and information it contains.\n", d);
        SEND_TO_Q("If you agree to abide by the disclaimer, simply reply to the message,\n", d);
        SEND_TO_Q("insert your PIN number where the message indicates, and send it off!\n", d);
        SEND_TO_Q("Then give the mud about 10 minutes to process your reply, for an\n", d);
        SEND_TO_Q("administrator to check the mail, and accept it. So try logging in every\n", d);
        SEND_TO_Q("ten minutes or so. Once our administrators have accepted or declined\n", d);
        SEND_TO_Q("your application, you will receive another Email with more information.\n", d);
        SEND_TO_Q("\nThe whole process can take as little as 5 minutes, but please be\n", d);
        SEND_TO_Q("patient if it takes longer..\n", d);

        EMS_Database_Modify(EMS_MODIFY, EMSD[Pointer].Name, EMSD[Pointer].PIN,
                EMSD[Pointer].Email, EMSD[Pointer].Status);
        EMS_Account_To_Struct(EMSD[Pointer].Name, EMSD[Pointer].PIN,
                EMSD[Pointer].Email, EMSD[Pointer].Status);

        /* If this is a newbie, write him to disk. */

        if (EMS_Lookup_Player_Level(EMSD[Pointer].Name) < 1)
          writeCharacter(d->character, 2, NOWHERE);

        STATE(d) = CON_FLUSH;

        break;
      } else {
        SEND_TO_Q("\nPlease press return, then re-enter the information.\n", d);
        STATE(d) = CON_EMSINFO2;
        break;
      }

    case CON_ACCEPTWAIT:
      /* ORIGINAL WAIT STATE HANDLER - Players waited here for staff approval
       * Commands available in original system:
       * - 'Q' or 'q': Quit and disconnect
       * - '0': Return to name generation screen  
       * - Any other input: Show waiting message
       * 
       * Original code:
       * for (; isspace(*arg); arg++);
       * switch (*arg) {
       *   case 'Q':
       *   case 'q':
       *     SEND_TO_Q("\n\nThank you for considering our mud.\n", d);
       *     close_socket(d);
       *     return;
       *     break;
       *   case '0':
       *     SEND_TO_Q("\n\nReturning to name selection...\n", d);
       *     STATE(d) = CON_APROPOS;
       *     SEND_TO_Q(namechart, d);
       *     break;
       *   default:
       *     SEND_TO_Q("You cannot do anything during this period. If the time you have waited is\ntoo long (by your point of view), you can come back later on or type 0 to go \nback to the name generation screen.\n", d);
       *     break;
       * }
       */
      
      /* Auto-approve any characters stuck in this state */
      SEND_TO_Q("\n\nYour character has been automatically approved!\n", d);
      if (IS_ENABLED(CODE_EMS))
        STATE(d) = CON_QRETURN;
      else
        STATE(d) = CON_WELCOME;
      break;

    case CON_WELCOME:
      writeCharacter(d->character, 2, NOWHERE);
      SEND_TO_Q(MENU, d);
      STATE(d) = CON_SLCT;
      break;

    case CON_RMOTD:
      SEND_TO_Q(MENU, d);

      STATE(d) = CON_SLCT;
      break;

      /* Determine wether or not the player wishes to reset their account. */
    case CON_REREG_CHK:
      if (!*arg) {
        SEND_TO_Q("\n\nLogging off..\n", d);
        STATE(d) = CON_FLUSH;
        return;
      }
      if ((strcmp(arg, "y")) && (strcmp(arg, "yes")) && (strcmp(arg, "Y"))) {
        SEND_TO_Q("\n\nLogging off..\n", d);
        STATE(d) = CON_FLUSH;
        return;
      }

      SEND_TO_Q("\n\nPlease enter your PIN number for verification of your identity: ", d);
      STATE(d) = CON_REREG_PIN;
      return;
      break;

      /* Make sure the PIN number verification passes.. */
    case CON_REREG_PIN:
      /* Load the account, make sure its in memory. If not, create the account
         and bounce the user back to the beginning of registration. (shouldn't happen) */
      strcpy(Name, EMS_NAME(GET_NAME(d->character)));
      sprintf(Gbuf2, "NULL");

      Pointer = EMS_Return_Account_Pointer(Name);

      if (Pointer == 0) {
        EMS_Database_Modify(EMS_ADD, Name, -1, Gbuf2, EMS_START);
        STATE(d) = CON_QRETURN;
        return;
      }
      sprintf(buf, "NULL");
      Pointer = EMS_Restore_Account(1, d, buf);
      if (Pointer == 0) {
        SEND_TO_Q("\nSomething is seriously wrong with EMS, please try back again later..\n", d);
        STATE(d) = CON_FLUSH;
        sprintf(Gbuf2, "ERROR! Could not locate Or create an EMS account for %s!\n", Name);
        logit(LOG_STATUS, Gbuf2);
        emslog(51, Gbuf2);
        return;
      }

      if ((!*arg) || (atoi(arg) != EMSD[Pointer].PIN)) {
        SEND_TO_Q("\n\nIncorrect PIN number, cannot proceed. Logging off..\n", d);
        STATE(d) = CON_FLUSH;
        sprintf(Gbuf1, "&+yEMS player entered bad PIN when resetting account: &+c%s&+y, EMS Status: %s",
                GET_NAME(d->character),
                GET_EMS_STATUS(Gbuf2, EMSD[Pointer].Status));
        logit(LOG_PLAYER, Gbuf1);
        emslog(51, Gbuf1);
        return;
      }

      sprintf(Gbuf1, " reset %s (Self-Reset)", EMS_NAME(GET_NAME(d->character)));
      do_ems(d->character, Gbuf1, CMD_ERESET);

      if (EMS_Lookup_Account_Status(EMS_NAME(GET_NAME(d->character))) == EMS_START) {
        SEND_TO_Q("\nAccount reset, you can now re-register your character. (press return).\n\n", d);
        echo_on(d);
        STATE(d) = CON_QRETURN;
        return;
      } else {
        SEND_TO_Q("\nThe reset was not successful. Try agian, or send and Email message\n", d);
        SEND_TO_Q("to <EMAIL>, and request assistance with resetting your account.\n", d);
        STATE(d) = CON_FLUSH;
        return;
      }
      break;


      /* Main menu */
    case CON_SLCT:
      /* a hard coded way of blocking unwanted forgers -Azuth 10/22/04
               if(port == 8888 && (GET_LEVEL(d->character) == 60))
                  {
                  if(!strcmp(GET_NAME(d->character), "Miax") || !strcmp(GET_NAME(d->character), "Erevan"))
                     {
                     select_main_menu(d, arg);
                     break;
                     }
                  else
                     {
                     statuslog(55, "%s attempted to log in as FORGER [%s].", GET_NAME(d->character), full_address(d, 0, 0));
                     STATE(d) = CON_FLUSH;
                     break; // opps not in list, bye bye
                     }
                  }
       */
      select_main_menu(d, arg);
      break;

      /* Flush output messages, then kill the descriptor */
    case CON_FLUSH:
      if (d->character && d->character->events)
        ClearCharEvents(d->character);
      if (d->output.head == 0)
        close_socket(d);
      return;

    case CON_LOOKUP:
      break;


      /* better not get here or something is hosed */
    default:
      logit(LOG_EXIT, "Nanny: illegal state of con'ness");
      dump_core();
      break;
  }
}

