/* ***************************************************************************
 *  File: email_reg.c                                        Part of Outcast *
 *  Usage: email parsing engine, application management.                     *
 *  Copyright  1998,1999 - Kristopher Kortright                              *
 *************************************************************************** */

/* ************************************************************************* */
/*                                                                           */
/*        Written by <PERSON><PERSON><PERSON> (Miax) for use with Outcast.       */
/*                                                                           */
/*     COPYRIGHT NOTICE: This code for Outcast is Copyright(c) 1999, by      */
/*    Kris<PERSON><PERSON> <PERSON>. This code is NOT freeware or shareware,    */
/* and may NOT be used in any form without expressly written permission from */
/* the author. Waterdeep(tm) is a registered trade mark of TSR incorporated, */
/*    and may not be used for resale or profit in any way, shape, or form.   */
/*        K. Kortright may be reached via <NAME_EMAIL>     */
/*                      and <EMAIL>                                 */
/*                                                                           */
/*************************************************************************** */

#include <errno.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#ifdef _WIN32
  /* Windows does not have unistd.h or sys/wait.h; keep includes portable */
  #include <io.h>
  #include <windows.h>
#else
  #include <unistd.h>
  #include <sys/wait.h>
#endif
#include <ctype.h>
#include <sys/stat.h>
#include <sys/types.h>

#include "comm.h"
#include "config.h"
#include "db.h"
#include "email_reg.h"
#include "events.h"
#include "interp.h"
#include "language.h"
#include "prototypes.h"
#include "spells.h"
#include "race_class.h"
#include "utils.h"
#ifdef NEWJUSTICE
   #include "newjustice.h"
#endif
#ifdef OLDJUSTICE
   #include "justice.h"
#endif
#include "mm.h"

struct email_registration_data *EMSD;
struct ems_message_data *EMD;
struct ems_words *WORDS;

#if 0
static int ct;
static char *tmstr;
#endif

int EMS_DEBUG = OFF;
int Master_Email_Counter = 0;
int Email_Registration_Database_Top = 0;
int EMS_Struct_Max_Records = 0;
extern int top_of_world;
extern char Email_EMS_Return_Notice_Top;
extern char Email_EMS_Return_Notice_Bottom;
extern P_char NPC_list;
extern P_char PC_list;
extern P_index mob_index;
extern const struct code_control_tags cctags[];
extern ubyte sets_code_control[CODE_CONTROL_BYTES];
extern struct zone_data *zone_table;
extern P_room world;
extern int PC_count;
extern int max_users_playing;


/* Begin routines */

void EMS_Bootup(void)
{
   FILE *db;
   char data[8192], buf[8192];
   char a[8192], b[8192], c[8192], d[8192];
   int Records = 0, Count = 0, Loop = 0, Status = 0, Pointer = 0;
   unsigned int Accepts = 0;

   /* First make sure our database is good */
   if(!(db = EMS_Open_Database(db)))
      return;

   /* Read record-count first */
   if(fgets(data, 8192, db) == NULL) {
      fclose(db);
      return;
   }
   Records = atoi(data);

   /* Count up records for the size of the EMS struct */
   Accepts = 0;
   for(Count = 1; Count <= Records; Count++)
   {
      Loop++;
      if(fgets(data, 8192, db) == NULL)
      {
         sprintf(buf, "*** EMS Warning: EMS master database is too short. %d of %d records found.",
                 (Loop - 1), Records);
         logit(LOG_EMAILREG, buf);
         break;
      }

      if(sscanf(data, "%8191s %8191s %8191s %8191s", a, b, c, d) == 4)
      {
         Status = atoi(d);
         if(Status == EMS_PENDING_ACCEPT)
            Accepts++;
      }
   }
   rewind(db);

   /* Hard allocation sizing */
   if(Accepts < 250)
      EMS_Struct_Max_Records = 250;
   else
      EMS_Struct_Max_Records = (Accepts + 250);

   sprintf(buf, "*** EMS callocing %d possible records in the EMS struct..", EMS_Struct_Max_Records);
   logit(LOG_STATUS, buf);
   fprintf(stderr, "%s", buf);

   CREATE(EMSD, struct email_registration_data, EMS_Struct_Max_Records);
   CREATE(WORDS, struct ems_words, 10);

   /* Now read in data from the EMS database, into the on-line EMS struct */
   Loop = 0;
   if(fgets(data, 8192, db) == NULL) {
      fclose(db);
      return;
   }
   Records = atoi(data);

   Email_Registration_Database_Top = 0;
   for(Count = 1; Count <= Records; Count++)
   {
      Loop++;

      if(fgets(data, 8192, db) == NULL)
      {
         sprintf(buf, "*** EMS Warning: EMS master database is too short. %d of %d records found.",
                 (Loop - 1), Records);
         logit(LOG_EMAILREG, buf);
         break;
      }

      if(sscanf(data, "%8191s %8191s %8191s %8191s", a, b, c, d) == 4)
      {
         Status = atoi(d);
         if(Status == EMS_PENDING_ACCEPT)
         {
            Pointer = EMS_Account_To_Struct(a, atoi(b), c, atoi(d));
            EMSD[Pointer].ID = Count;
         }
      }
   }
   fclose(db);

   sprintf(buf, "*** EMS stored %d records in the EMS accept struct.",
           Email_Registration_Database_Top);
   logit(LOG_EMAILREG, buf);
   fprintf(stderr, "%s\n", buf);

   /* Now read in all of our text data files */
   EMS_File_To_Struct(EMS_RETURN_HEADER, ems_header);
   EMS_File_To_Struct(EMS_RETURN_FOOTER, ems_footer);
   EMS_File_To_Struct(EMS_DISCLAIMER, ems_disclaimer);
   EMS_File_To_Struct(EMS_REPLY_START, ems_reply_start);
   EMS_File_To_Struct(EMS_REPLY_BADNAME, ems_reply_badname);
   EMS_File_To_Struct(EMS_REPLY_BADPIN, ems_reply_badpin);
   EMS_File_To_Struct(EMS_REPLY_BADEMAIL, ems_reply_bademail);
   EMS_File_To_Struct(EMS_REPLY_CLOSED, ems_reply_closed);
   EMS_File_To_Struct(EMS_REPLY_PENDING_REPLY, ems_reply_pending_reply);
   EMS_File_To_Struct(EMS_REPLY_PENDING_ACCEPT, ems_reply_pending_accept);
   EMS_File_To_Struct(EMS_REPLY_FROZEN, ems_reply_frozen);
   EMS_File_To_Struct(EMS_REPLY_THAWED, ems_reply_thawed);
   EMS_File_To_Struct(EMS_REPLY_DECLINED, ems_reply_declined);
   EMS_File_To_Struct(EMS_REPLY_BROKEN, ems_reply_broken);
   EMS_File_To_Struct(EMS_REPLY_DELETED, ems_reply_deleted);
   EMS_File_To_Struct(EMS_REPLY_OOPS, ems_reply_oops);
   EMS_File_To_Struct(EMS_REPLY_OKAY, ems_reply_okay);
   EMS_File_To_Struct(EMS_CHARGEN_INFO1, ems_chargen_info1);
   EMS_File_To_Struct(EMS_CHARGEN_INFO2, ems_chargen_info2);
   EMS_File_To_Struct(EMS_BANNED_MSG, ems_banned_msg);
   EMS_File_To_Struct(EMS_USAGE, ems_usage);
   EMS_File_To_Struct(EMS_MULTI_PLAYER, ems_multi_player);
}


/* Similar to GET_NAME, but returns the name in all lower case */
char *EMS_NAME(char *name)
{
   /* Return a static lowercase copy of the given name */
   static char player[MAX_STRING_LENGTH];
   size_t i, n;

   if(!name) return NULL;

   n = strlen(name);
   if(n >= sizeof(player)) n = sizeof(player) - 1;

   for(i = 0; i < n; i++) {
      player[i] = LOWER(name[i]);
   }
   player[n] = '\0';

   return player;
}


/* Send routine, this initializes a new account */
void EMS_Send_Application(P_desc d, char *EmailAddress, int PIN)
{
   FILE *outbound;
   char buf[MAX_STRING_LENGTH], player[MAX_STRING_LENGTH];
   char file[MAX_STRING_LENGTH];


   /* Who are we? */
   sprintf(player, "%s", EMS_NAME(GET_NAME(d->character)));

   Master_Email_Counter = EMS_Increment_Counter(Master_Email_Counter);
   sprintf(file, "%s/outgoing/%s.%d", EMAIL_REG_FILES, player, Master_Email_Counter);


   /* Add the entry into the outgoing email database */
   if(!(outbound = fopen(OUTGOING_EMAIL, "r+")))
      {
      if(!(outbound = fopen(OUTGOING_EMAIL, "w")))
         {
         sprintf(buf, "EMS Error! Cannot write to Outbound Email Database!");
         logit(LOG_EMAILREG, buf);
         emslog(51, buf);
         return;
         }
      }


   sprintf(buf, "%s %s \n", EmailAddress, file);
   fseek(outbound, 0L, SEEK_END);
   fputs(buf, outbound);
   fclose(outbound);


   /* Create the body of the message to send */
   remove(file);

   if(!(outbound = fopen(file, "w")))
      {
      sprintf(buf, "Cannot open return notice temp file [%s]!! (Name: %s, From: %s)",
              file, player, EmailAddress);
      logit(LOG_EMAILREG, buf);
      emslog(51, buf);
      return;
      }

   fputs(ems_header, outbound);

   fputs(ems_disclaimer, outbound);

   sprintf(buf, "My Character Name is: %s  <-- Make sure this is correct!\n", player);
   fputs(buf, outbound);
   sprintf(buf, "My PIN Number is: XXXX  <-- Replace the XXXX with your PIN Number.\n");
   fputs(buf, outbound);

   fputs(ems_footer, outbound);

   fclose(outbound);

   /* Write this to the master EMS database and player file database. */
   EMS_Database_Modify(EMS_ADD, player, PIN, EmailAddress, EMS_PENDING_REPLY);
}


void EMS_Database_Modify(int Action, char *name, int pin, char *email, int status)
{
   FILE *in, *out;
   char buf[MAX_STRING_LENGTH], data[MAX_STRING_LENGTH], omg[64];
   char a[8192], b[8192], c[8192], d[8192], file[8192];
   int Count = 0, Loop = 0, Records = 0, Pointer = 0, Found = 0;


   /* First make sure the database is good */
   if(!(in = EMS_Open_Database(in)))
      return;

   /* To protect the database from corruption, the backup database will always be
      one transaction behind the primary. Instead of synching up the databases
      after each modification, we synch them up before the modification. Then we
      perform the modification. If the modification process corrupts the primary
      database, we know our backup is good. This is done via cron, so the backup
      database is never written to by the mud instance. */

   /* Now kick open the temporary. */
   sprintf(file, "%s.%s", EMS_DATABASE, name);
   remove(file);
   if(!(out = fopen(file, "w")))
      {
      sprintf(buf,"*** EMS ERROR! Cannot open EMS temp. database! (%s), Disabling EMS!\n",
              EMS_DATABASE);
      logit(LOG_STATUS, buf);
      fprintf(stderr, "%s", buf);
      emslog(51, buf);

      fclose(in);
      EMS_Auto_Disable();
      return;
      }

   /* Check the database first to see if the player already has an account */
   Found = 0;
   fgets(data, 8192, in);
   Records = atoi(data);
   for(Count = 1; Count <= Records; Count++)
      {
      Loop++;

      fgets(data, 8192, in);
      if((data == NULL) || (feof(in)))
         break;

      sscanf(data, "%8191s %8191s %8191s %8191s \n", a, b, c, d);

      if(!strcmp (a, name))
         Found = Count;
      }
   rewind(in);
   fgets(data, 8192, in);


   /* Read through the database, copying over all the non-relevant contents, and
      writing in the one record that changed. Extremely primitive databasing
      scheme, and a terrible resource hog, but it will do with a small number
      of records just fine until I get Oracle done --MIAX */
   if((Action == EMS_DELETE) && (Found == 0))
      {
      sprintf(buf, "Attempted delete on non-existent account. Account was: %s", name);
      emslog(51, buf);
      logit(LOG_EMAILREG, buf);
      fclose(in);
      fclose(out);
      return;
      }
   if((Action == EMS_ADD) && (Found > 0))
      Action = EMS_MODIFY;

   if(Action == EMS_DELETE)
      Loop = (Records - 1);
   else if(Action == EMS_ADD)
      Loop = (Records + 1);
   else
      Loop = Records;
   sprintf(buf, "%d\n", Loop);
   fputs(buf, out);

   for(Count = 1; Count <= Records; Count++)
      {
      Loop++;

      fgets(data, 8192, in);
      if((data == NULL) || (feof(in)))
         {
         sprintf(buf, "*** EMS Warning: EMS master database is too short. %d of %d records found.",
                 (Loop - 1), Records);
         logit(LOG_EMAILREG, buf);
         break;
         }

      sscanf(data, "%8191s %8191s %8191s %8191s \n", a, b, c, d);

      if(!strcmp (a, name))
         {
         if(Action == EMS_MODIFY)
            {
            if(!strcmp (email, "NULL"))
               strcpy(email, c);
            if(pin == 999)
               pin = atoi(b);
            if(status == 999)
               status = atoi(d);
            sprintf(buf, "%s %d %s %d \n", name, pin, email, status);
            fputs(buf, out);
            }
         }
      else
         {
         fputs(data, out);
         }
      }

   if(Action == EMS_ADD)
      {
      sprintf(buf, "%s %d %s %d \n", name, pin, email, status);
      fputs(buf, out);
      }
   fclose(in);
   fclose(out);

   sprintf(buf, "%s.old", EMS_DATABASE);
   rename(EMS_DATABASE, buf);
   rename(file, EMS_DATABASE);

   if(Action == EMS_ADD)
      sprintf(data, "Add");
   else if(Action == EMS_MODIFY)
      sprintf(data, "Modify");
   else
      sprintf(data, "Delete");

   Pointer = EMS_Account_To_Struct(name, pin, email, status);

   EMS_Player_File_Modify(name, pin, email, status);

   sprintf(buf, "&+y*** database %s for: &+c%s&+y (&+c%s&+y), Status set to: &+C%s",
           data, name, email, GET_EMS_STATUS(omg, status));
   logit(LOG_EMAILREG, buf);

   return;
}


void EMS_Player_File_Modify(char *name, int pin, char *email, int status)
{
   FILE *maildb, *lockfile;
   char buf[MAX_STRING_LENGTH], data[MAX_STRING_LENGTH];
   char LOCK[256], file[MAX_STRING_LENGTH];

   /* Verify that the Applicants file exists. */
   sprintf(file, "%s/%c/%s.db", EMAIL_REG_FILES, name[0], name);

   /* Verify that the Applicants file exists. */
   sprintf(LOCK, "%s.locked", file);

   /* Make sure the player's database isn't locked up by the mainmud! O_o */
   remove(LOCK);

   /* If we cant open the players lockfile, then either the disk is full, or something
      else is fucked up with disk writes and we don't want to try and process this app.
      Send the broken notice to the user so they know to try again later. */
   if(!(lockfile = fopen(LOCK, "w")))
      {
      sprintf(buf, "EMS Fatal! Cannot open lockfile for: %s.", name);
      logit(LOG_EMAILREG, buf);
      fprintf(stderr, "%s", buf);
      sprintf(buf, "   Sending notice to player to try again later.");
      logit(LOG_EMAILREG, buf);
      fprintf(stderr, "%s\n", buf);

      strcpy(buf, "");
      sprintf(data, "NONE");
      EMS_Return_Notice(name, email, buf, EMS_BROKEN, data);
      return;
      }
   fclose(lockfile);


   if(!(maildb = fopen(file, "r+")))
      {
      if(!(maildb = fopen(file, "w")))
         {
         sprintf(buf, "File Error! Cannot open char file for writing! App. is hosed: %s (%s)",
                 name, email);
         logit(LOG_EMAILREG, buf);
         sprintf(data, "%s\n", buf);
         emslog(51, data);
   
         strcpy(buf, "");
         sprintf(data, "NONE");
         EMS_Return_Notice(name, email, buf, EMS_BROKEN, data);
         return;
         }
      }

   sprintf(data, "%d %s %d %s \n", status, name, pin, email);
   fseek(maildb, 0L, SEEK_END);
   fputs(data, maildb);
   fclose(maildb);

   remove(LOCK);

   return;
}


/* This routine reads in new accept messages from the accept-transfer file,
   stores them in the on-line EMS database, logs the account status change
   to the master database, and prints the accept request to god status. */
void EMS_Process_Mail(void)
{
   FILE *transfer, *log;
   char buf[MAX_STRING_LENGTH], data[MAX_STRING_LENGTH];
   char hold[MAX_STRING_LENGTH], ack[8192];
   char a[8192], b[8192], c[8192], d[8192], info[8192];
   char e[8192], f[8192], g[8192], h[8192];
   int Pointer = 0, Count = 0, Loop = 0, Status = 0;
   int BadMail = NO, BadMails = 0, Control = 0;
   int Incoming = 0, Outgoing = 0, blahs = 0;


   /* First look for the presence of the transfer-data file */
   if(!(!(transfer = fopen(EMS_DATA_TRANSFER, "r"))))
      {
      Count = 0;
      Loop = 0;
      /* Now parse through the transfer file one entry at a time, and process accordingly. */
      for(;;)
         {
         strcpy(data, "");
         strcpy(a, "");
         strcpy(b, "");
         strcpy(c, "");
         strcpy(d, "");
         fgets(data, 8192, transfer);
         if((data == NULL) || (feof(transfer)))
            break;

         strcpy(a, "");
         strcpy(b, "");
         strcpy(c, "");
         strcpy(d, "");
         sscanf(data, "%s %s %s %s %s \n", a, b, c, d, e);

         if((!a) || (!b) || (!c) || (!d))
            continue;

         if(!strcmp (a, "STATS:"))
            {
            Incoming = (Incoming + atoi(b));
            Outgoing = (Outgoing + atoi(c));
            blahs = (blahs + atoi(d));
            Control = (Control + atoi(e));
            continue;
            }

         /* Store the char's information into the memory struct */

         sprintf(b, "%s", EMS_NAME(b));
         Count++;

         /*
         fprintf(stderr, "PROCESSMAIL1: Name: (%s), Status: (%s), PIN: (%s), Email: (%s)\n",
           b, a, c, d);
         */

         strcpy(info, "");
         sprintf(info, "%s", EMS_Lookup_Account(b, info));

         /*
         fprintf(stderr, "PROCESSMAIL6: Raw Account Read: (%s)\n", info);
         */

         if(strcmp (info, "NULL"))
            {
            strcpy(e, "");
            strcpy(f, "");
            strcpy(g, "");
            strcpy(h, "");
            sscanf(info, "%s %s %s %s", e, f, g, h);
            if(h)
               Status = atoi(h);
            else
               Status = -10;
            BadMail = NO;

            /*
            fprintf(stderr, "PROCESSMAIL2: Looked up data: [%s %s %s %d]\n", e, f, g, Status);
            */

            /* If this is a control message, and we are waiting for the players
               reply, then assume their Email address was input wrong, or is not
               valid, tag the players account as such so they know what happened
               the next time they log in. */
            if((Status == EMS_PENDING_REPLY) && (atoi(a) == EMS_BADEMAIL))
               {
               sprintf(buf, "&+y&+C%s&n&+y's email address was returned as bad.. (Email: &+c%s&+y) Their account status has been flagged as having a bad address.",
                       b, d);
               emslog(51, buf);
               BadMail = YES;
               BadMails++;

               /*
               fprintf(stderr,
                 "PROCESSMAIL5: BadMail, Name: %s, Email: %s, Status: %d, Msg: %s, Admin: %s\n",
                 b, d, Status, buf, data);
               */
               }
            /* In this case, we have already processed the players first bad Email,
               so ignore everything else that comes in until the player re-registers */
            else if(atoi(a) == EMS_BADEMAIL)
               {
               sprintf(buf,
                       "&+y&+C%s&n&+y sent a message using known bad Email address, message ignored.", b);
               emslog(51, buf);
               continue;
               }
            /* If the user's status does not need an Email update, ignore it */
            else if((Status != EMS_PENDING_REPLY) && (Status > -4))
               {
               sprintf(buf, "Your account status indicates that you did not\n");
               sprintf(ack, "need to re-send in your application, I'm ignoring it.");
               strcat(buf, ack);
               sprintf(data, "NONE");
               EMS_Return_Notice(b, d, buf, Status, data);

               /*
               fprintf(stderr, "PROCESSMAIL3: NoNeed, Name: %s, Email: %s, Status: %d, Msg: %s, Admin: %s\n",
                 b, d, Status, buf, data);
               */

               sprintf(buf, "&+y&+C%s&n&+y re-sent in their original applicaiton: (&+c%s&n&+y). Their account status: &+c%s&+y, message was bounced back to the user and ignored.",
                       b, d, GET_EMS_STATUS(data, Status));
               emslog(51, buf);
               continue;
               }
            }

         /* Pull the account into memory, modify it per the email, pin, and status
            sent to us from Sojparse. */
         Pointer = EMS_Account_To_Struct(b, atoi(c), d, atoi(a));

         /* Send the return receipt, or flag account as Bad Email. O_O */
         if(BadMail == NO)
            {
            strcpy(buf, "");
            strcpy(data, "NONE");
            EMS_Return_Notice(b, d, buf, EMS_PENDING_ACCEPT, data);

            /*
            fprintf(stderr, "PROCESSMAIL4: PendAcc, Name: %s, Email: %s, Status: %d, Msg: %s, Admin: %s\n",
              b, d, Status, buf, data);
            */

            }
         else
            {
            EMSD[Pointer].Status = EMS_BADEMAIL;
            }

         /* Finally, store the database change to the master database */
         EMS_Database_Modify(EMS_MODIFY, EMSD[Pointer].Name, EMSD[Pointer].PIN,
                             EMSD[Pointer].Email, EMSD[Pointer].Status);
         }


      if((Incoming + Outgoing + blahs + Control) > 0)
         {
         sprintf(buf,
                 "&+mSojparse: &+yIncoming: &+C%d&n&+y, Outgoing: &+C%d&n&+y, Control: &+C%d&n&+y, Bad: &+C%d",
                 Incoming, Outgoing, Control, blahs);
         logit(LOG_STATUS, buf);
         }

      fclose(transfer);
      remove(EMS_DATA_TRANSFER);
      }


   /* Now throw all pending applications to status */
   for(Count = 1; Count <= Email_Registration_Database_Top; Count++)
      {
      if(EMSD[Count].Status == EMS_PENDING_ACCEPT)
         {
         sprintf(buf,"&+yApplicant awaiting acceptance: &+C%s&n&+y (&+c%s&+y).",
                 EMSD[Count].Name, EMSD[Count].Email);
         logit(LOG_EMAILREG, buf);
         emslog(51, buf);
         }
      }


   /* Now toss any critical messages from Sojparse to the Statuslog */
   if(!(log = fopen(SOJPARSE_CRIT_LOG, "r")))
      return;

   strcpy(hold, "");
   for(;;)
      {
      fgets(data, 8192, log);
      if((data == NULL) || (feof(log)))
         break;

      if(strlen (data) > 1)
         {
         sprintf(buf, "&+mSojparse: &n&+y%s", data);
         strcat(hold, buf);
         }
      }
   fclose(log);
   remove(SOJPARSE_CRIT_LOG);

   emslog(51, hold);

   return;
}


/* The return notice function, send something back to the player if the
   application was declined, or encountered a problem. */
void EMS_Return_Notice(char *Name, char *Email, char *Message, int Status, char *Admin)
{
   FILE *outbound;
   char file[8192], buf[8192], extra[MAX_STRING_LENGTH];
   char Hold[MAX_STRING_LENGTH];


   Master_Email_Counter = EMS_Increment_Counter(Master_Email_Counter);
   sprintf(file, "%s/outgoing/%s.%d", EMAIL_REG_FILES, Name, Master_Email_Counter);
   remove(file);

   /* First store the info in the outbound email database */
   if(!(outbound = fopen(OUTGOING_EMAIL, "r+")))
      {
      if(!(outbound = fopen(OUTGOING_EMAIL, "w")))
         {
         sprintf(buf, "EMS Error! Cannot write to Outbound Email Database!");
         logit(LOG_EMAILREG, buf);
         emslog(51, buf);
         return;
         }
      }

   /*
   fprintf(stderr, "DEBUG (Return Notice): file: (%s), Name: (%s), Counter: (%d)\n",
     file, Name, Master_Email_Counter);
   */

   strcpy(Hold, "");

   sprintf(buf, "%s %s %s %d \n", Email, file, Name, Status);
   fseek(outbound, 0L, SEEK_END);
   fputs(buf, outbound);
   fclose(outbound);

   /* Now create the body of the message */
   remove(file);
   if(!(outbound = fopen(file, "w")))
      {
      sprintf(buf, "Cannot open return notice temp file!! (Name: %s, From: %s)",
              Name, Email);
      logit(LOG_EMAILREG, buf);
      return;
      }

   /* We always start with the standard Outcast EMS header (with spam disclaimer) */
   strcat(Hold, ems_header);

   /* Now add the players name as the intended recipient */
   sprintf(buf, "\nThis message applies to the Outcast player named: %s\n", Name);
   strcat(Hold, buf);

   /* Choose the message to send, or send Message if status is -1 */
   if(Status == -1)
      {
      strcat(Hold, Message);
      }
   else
      {
      switch(Status)
         {
         case EMS_START:
            strcat(Hold, ems_reply_start);
            break;
         case EMS_BADNAME:
            strcat(Hold, ems_reply_badname);
            break;
         case EMS_BADPIN:
            strcat(Hold, ems_reply_badpin);
            break;
         case EMS_BADEMAIL:
            strcat(Hold, ems_reply_bademail);
            break;
         case EMS_PENDING_REPLY:
            strcat(Hold, ems_reply_pending_reply);
            break;
         case EMS_PENDING_ACCEPT:
            strcat(Hold, ems_reply_pending_accept);
            break;
         case EMS_FROZEN:
            strcat(Hold, ems_reply_frozen);
            break;
         case EMS_THAWED:
            strcat(Hold, ems_reply_thawed);
            break;
         case EMS_DECLINED:
            strcat(Hold, ems_reply_declined);
            break;
         case EMS_DELETED:
            strcat(Hold, ems_reply_deleted);
            break;
         case EMS_BROKEN:
            strcat(Hold, ems_reply_broken);
            break;
         case EMS_OKAY:
            strcat(Hold, ems_reply_okay);
            break;
         case EMS_FIRST_LOGIN:
            strcat(Hold, ems_reply_okay);
            break;
         case EMS_CLOSED:
            strcat(Hold, ems_reply_closed);
            break;
         default:
            strcat(Hold, ems_reply_oops);
            break;
         }
      }

   strcpy(buf, " ");
   if(strcmp (Admin, "NONE"))
      {
      sprintf(extra, "\nHandling Administrator: %s\n", Admin);
      strcat(buf, extra);
      }
   else
      {
      sprintf(extra, "\nThis was an automated responce from the Outcast EMS System.\n");
      strcat(buf, extra);
      }

   if(Message)
      {
      sprintf(extra, "Addition Comments from the Administrator: %s\n", Message);
      strcat(buf, extra);
      }
   else
      {
      sprintf(extra, "There were no additional messages from the administration staff.\n");
      strcat(buf, extra);
      }

   strcat(Hold, buf);

   strcat(Hold, ems_footer);

   fputs(Hold, outbound);
   fclose(outbound);

   return;
}

char *GET_EMS_STATUS(char *Var, int Status)
{
   char buf[256];

   switch(Status)
      {
      case -3:
         sprintf(buf, "NULL FILE");
         break;
      case -2:
         sprintf(buf, "UNKNOWN");
         break;
      case -1:
         sprintf(buf, "EMPTY RECORD");
         break;
      case 0:
         sprintf(buf, "START");
         break;
      case 1:
         sprintf(buf, "BADNAME");
         break;
      case 2:
         sprintf(buf, "BADPIN");
         break;
      case 3:
         sprintf(buf, "BAD EMAIL");
         break;
      case 4:
         sprintf(buf, "PEND REPLY");
         break;
      case 5:
         sprintf(buf, "PEND ACCEPT");
         break;
      case 6:
         sprintf(buf, "FROZEN");
         break;
      case 7:
         sprintf(buf, "THAWED");
         break;
      case 8:
         sprintf(buf, "DECLINED");
         break;
      case 9:
         sprintf(buf, "BROKEN");
         break;
      case 10: sprintf(buf, "DELETED"); break;
      case 11: sprintf(buf, "OKAY"); break;
      case 12:
         sprintf(buf, "CLOSED");
         break;
      case 14:
         sprintf(buf, "FIRST LOGIN");
         break;
      default:
         sprintf(buf, "O_O");
         break;
      }

   strcpy(Var, buf);
   return(Var);
}

int EMS_Restore_Account(int Mode, P_desc d, char *Name)
{
   FILE *in;
   char buf[MAX_STRING_LENGTH], data[MAX_STRING_LENGTH];
   char a[8192], b[8192], c[8192], e[8192];
   int Records = 0, Count = 0, Good = 0, Pointer = 0;

   /* First make sure our database is good */
   if(!(in = EMS_Open_Database(in)))
      return 0;

   if(Mode == 1)
      sprintf(Name, "%s", EMS_NAME(GET_NAME(d->character)));

   /* Search the master database for the record */
   fgets(data, 8192, in);
   Records = atoi(data);
   Pointer = 0;

   for(Count = 1; Count <= Records; Count++)
      {
      fgets(data, 8192, in);
      if((data == NULL) || (feof(in)))
         break;

      sscanf(data, "%8191s %8191s %8191s %8191s \n", a, b, c, e);

      if(!str_cmp (a, Name))
         {
         Good = 1;
         Pointer = EMS_Account_To_Struct(a, atoi(b), c, atoi(e));
         break;
         }
      }
   fclose(in);

   if(Good == 0)
      {
      sprintf(buf, "&+yWarning: Could not find EMS record for &+C%s&n&+y, registration enforced.",
              GET_NAME(d->character));
      logit(LOG_EMAILREG, buf);
      emslog(51, buf);
      }

   if((!strcmp (GET_NAME(d->character), "Miax")) && (Good == 1))
      {
      sprintf(buf, "\nEMS Account Restore: %s, Email: %s, PIN: %s, Status: %s\n",
              a, c, b, e);
      SEND_TO_Q(buf, d);
      }

   return Pointer;
}


/* Usage information for the EMS commands */
void EMS_Usage(P_char ch)
{
   page_string(ch->desc, ems_usage, 1);
   return;
}


/* The command interfce for EMS. It is a huge routine right now,
   with all the goodies packed into one place. It should be broken
   out into several smaller routines, but I am lazy and this is how
   it was done, so suffer. Its documented well, so you wont suffer
   much. Omg this is a huge routine. Fuck it, it works, suck me. o_o  */
void do_ems(P_char ch, char *arg, int cmd)
{
   FILE *db;
   char Gbuf1[MAX_STRING_LENGTH], Gbuf2[MAX_STRING_LENGTH];
   char Gbuf3[MAX_STRING_LENGTH], letter[2], l2[2], letter2[2];
   char reason[MAX_STRING_LENGTH], letter3[2];
   char buf[MAX_STRING_LENGTH], Line[MAX_STRING_LENGTH];
   char tmp[2], word[8192];
   int Count = 0, Loop = 0, Pointer = 0, Omg = 0, pos = 0;
   P_char victim;
   P_desc hold = NULL;
   P_obj t_obj1, t_obj2;


   strcpy(Gbuf1, "");
   strcpy(Gbuf2, "");
   strcpy(Gbuf3, "");
   sscanf(arg, "%s %s %s \n", Gbuf1, Gbuf2, Gbuf3);

   if((!arg) || (!Gbuf1) || (!strcmp (Gbuf1, "")))
      {
      EMS_Usage(ch);
      return;
      }

   strcpy(Line, "");

   sprintf(letter, "%c", Gbuf1[0]);
   sprintf(letter2, "%c", Gbuf1[1]);
   sprintf(letter3, "%c", Gbuf1[2]);
   if(Gbuf2)
      sprintf(l2, "%c", Gbuf2[0]);


   /* Command Summary */
   else if((!strcmp (letter, "?")) || ((!strcmp (letter, "h")) && (!strcmp (letter2, "e"))))
      {
      EMS_Usage(ch);
      return;
      }


   /* Listing Commands */
   if((!strcmp (Gbuf1, "list")) || ((!strcmp (letter, "l")) && (!strcmp (letter2, "i"))))
      {
      do_ems_list(ch, Gbuf1, Gbuf2, Gbuf3);
      return;
      }


   /* Lookup commands */
   else if((!strcmp (Gbuf1, "lookup")) ||
           ((!strcmp (letter, "l")) && (!strcmp (letter2, "o"))))
      {
      do_ems_lookup(ch, Gbuf1, Gbuf2, Gbuf3);
      return;
      }


   /* Header listing functions */
   else if((!strcmp (Gbuf1, "header")) || (!strcmp (letter, "h")))
      {
      do_ems_header(ch, Gbuf1, Gbuf2, Gbuf3);
      return;
      }


   /* Status of the EMS system */
   else if((!strcmp (letter, "s")) && (!strcmp (letter2, "t")))
      {
      sprintf(buf, "Unknown");
      do_ems_status(ch, buf);
      return;
      }


   /* The rest are changes to EMS accounts.. First locate the player in memory or
      on disk. If the player is on disk and not in memory, we'll load it into memory */
   Pointer = EMS_Return_Account_Pointer(Gbuf2);
   if(Pointer == 0)
      {
      sprintf(buf, "&+cI could not find that account name. &+Lo_O\n");
      send_to_char(buf, ch);
      return;
      }


   /* Process the commentary */
   if((!Gbuf3) || (!strcmp (Gbuf3, "")))
      sprintf(reason, "No Reason Given.");
   else
      {
      Count = 0;
      strcpy(word, "");
      strcpy(reason, "");
      for(Loop = 0; Loop <= strlen(arg); Loop++)
         {
         strcpy(word, buf);
         sprintf(buf, "%c", arg[Loop]);
         if(Count > 2)
            strcat(reason, buf);
         if((!strcmp (buf, " ")) && (strcmp (word, " ")))
            Count++;
         }
      }


   /* Player Record Commands */
   if((!strcmp (letter, "r")) && (strcmp (letter2, "e")))
      {
      do_ems_records(ch, Gbuf1, Gbuf2, Gbuf3, arg, letter2, letter3);
      return;
      }


   /* Modify Database or Player Records */
   sprintf(buf, "NOCHANGE");


   /* Account is Accepted! :D */
   if((!strcmp (letter, "a")) || ((!strcmp (letter, "f")) && (!strcmp (letter2, "o"))))
      {
      if((EMSD[Pointer].Status == EMS_OKAY) || (EMSD[Pointer].Status == EMS_FIRST_LOGIN))
         {
         send_to_char("&+cThat player has already been accepted. o_o\n", ch);
         return;
         }
      else if((EMSD[Pointer].Status != EMS_PENDING_ACCEPT) && (!strcmp (letter, "a")))
         {
         send_to_char("&+cEMS decline can only be used on players who are Pending Acceptance.\n", ch);
         return;
         }

      EMSD[Pointer].Status = EMS_OKAY;

      Omg = EMS_Account_To_Struct(EMSD[Pointer].Name, EMSD[Pointer].PIN,
                                  EMSD[Pointer].Email, EMSD[Pointer].Status);
      EMS_Database_Modify(EMS_MODIFY, EMSD[Omg].Name, EMSD[Omg].PIN,
                          EMSD[Omg].Email, EMSD[Omg].Status);

      EMS_Return_Notice(EMSD[Omg].Name, EMSD[Omg].Email, reason, EMS_OKAY, GET_NAME(ch));

      if(!strcmp (letter, "a"))
         sprintf(buf,"&+y&+c%s&+y accepts EMS account for: &+c%s&+y, (&+c%s&+y). Comment: &+c%s",
                 GET_NAME(ch), EMSD[Omg].Name, EMSD[Omg].Email, reason);
      else
         sprintf(buf,"&+y&+c%s&+y FORCES acceptance of EMS account for: &+c%s&+y, (&+c%s&+y). Comment: &+c%s",
                 GET_NAME(ch), EMSD[Omg].Name, EMSD[Omg].Email, reason);
      logit(LOG_EMAILREG, buf);
      emslog(51, buf);

      /* Add this into player's record. */
      if(!strcmp (letter, "a"))
         sprintf(buf, " raa %s '%s Accepts EMS account for: %s (%s)' %s\n",
                 EMSD[Omg].Name, GET_NAME(ch), EMSD[Omg].Name, EMSD[Omg].Email, reason);
      else
         sprintf(buf, " raa %s '%s FORCED Acceptance of EMS account for: %s (%s)' %s\n",
                 EMSD[Omg].Name, GET_NAME(ch), EMSD[Omg].Name, EMSD[Omg].Email, reason);
      do_ems(ch, buf, cmd);

      return;
      }


   /* Account is Declined.. No Soup For You! O_O */
   if(!strcmp (letter, "d"))
      {
      if(EMSD[Pointer].Status == EMS_DECLINED)
         {
         send_to_char("&+cThat player has already been declined you fool! o_O\n", ch);
         return;
         }
      else if(EMSD[Pointer].Status != EMS_PENDING_ACCEPT)
         {
         send_to_char("&+cEMS decline can only be used on players who are Pending Acceptance.\n", ch);
         return;
         }

      EMSD[Pointer].Status = EMS_START;

      Omg = EMS_Account_To_Struct(EMSD[Pointer].Name, EMSD[Pointer].PIN,
                                  EMSD[Pointer].Email, EMSD[Pointer].Status);
      EMS_Database_Modify(EMS_MODIFY, EMSD[Omg].Name, EMSD[Omg].PIN,
                          EMSD[Omg].Email, EMSD[Omg].Status);

      EMS_Return_Notice(EMSD[Omg].Name, EMSD[Omg].Email, reason, EMS_DECLINED, GET_NAME(ch));

      sprintf(buf,"&+y&+c%s&+y declined EMS account for: &+c%s&+y, (&+c%s&+y) Comment: &+c%s",
              GET_NAME(ch), EMSD[Omg].Name, EMSD[Omg].Email, reason);
      logit(LOG_EMAILREG, buf);
      emslog(51, buf);

      /* Add this into player's record. */
      sprintf(buf, " raa %s '%s Declined EMS account for: %s (%s)' %s\n",
              EMSD[Omg].Name, GET_NAME(ch), EMSD[Omg].Name, EMSD[Omg].Email, reason);
      do_ems(ch, buf, cmd);

      return;
      }


   /* FreezeEm! oSo Co_old !! */
   if(!strcmp (letter, "f"))
      {
      if(EMSD[Pointer].Status == EMS_FROZEN)
         {
         send_to_char("&+cThat player has already been frozen, &+Cslush head! o_O\n", ch);
         send_to_char("&+CWhat are you, Mr. Freeze?!\n", ch);
         return;
         }

      EMSD[Pointer].Status = EMS_FROZEN;

      Omg = EMS_Account_To_Struct(EMSD[Pointer].Name, EMSD[Pointer].PIN,
                                  EMSD[Pointer].Email, EMSD[Pointer].Status);
      EMS_Database_Modify(EMS_MODIFY, EMSD[Omg].Name, EMSD[Omg].PIN,
                          EMSD[Omg].Email, EMSD[Omg].Status);

      EMS_Return_Notice(EMSD[Omg].Name, EMSD[Omg].Email, reason, EMS_FROZEN, GET_NAME(ch));

      sprintf(buf,"&+y&+c%s&+y Froze EMS account for: &+c%s&+y, (&+c%s&+y) Comment: &+c%s",
              GET_NAME(ch), EMSD[Omg].Name, EMSD[Omg].Email, reason);
      logit(LOG_EMAILREG, buf);
      emslog(51, buf);

      victim = get_char(EMSD[Omg].Name);
      if(victim)
         {
         sprintf(buf, " %s", EMSD[Omg].Name);
         do_freeze(ch, buf, CMD_FREEZE);
         }

      /* Add this into player's record. */
      sprintf(buf, " raa %s '%s Froze EMS account for: %s (%s)' %s\n",
              EMSD[Omg].Name, GET_NAME(ch), EMSD[Omg].Name, EMSD[Omg].Email, reason);
      do_ems(ch, buf, cmd);

      return;
      }


   /* Thaw the po basta's */
   if(!strcmp (letter, "t"))
      {
      if((EMSD[Pointer].Status == EMS_OKAY) || (EMSD[Pointer].Status == EMS_FIRST_LOGIN))
         {
         send_to_char("&+cThat player has already been thawed, go eat some ice. o_O\n", ch);
         return;
         }
      else if(EMSD[Pointer].Status != EMS_FROZEN)
         {
         send_to_char("&+cHow can you thaw someone who isn't frozen? o_O\n", ch);
         send_to_char("&+CMaybe you should freeze-dry them first. ;)\n", ch);
         return;
         }

      EMSD[Pointer].Status = EMS_OKAY;

      Omg = EMS_Account_To_Struct(EMSD[Pointer].Name, EMSD[Pointer].PIN,
                                  EMSD[Pointer].Email, EMSD[Pointer].Status);
      EMS_Database_Modify(EMS_MODIFY, EMSD[Omg].Name, EMSD[Omg].PIN,
                          EMSD[Omg].Email, EMSD[Omg].Status);

      EMS_Return_Notice(EMSD[Omg].Name, EMSD[Omg].Email, reason, EMS_THAWED, GET_NAME(ch));

      sprintf(buf,"&+y&+c%s&+y Thaws EMS account for: &+c%s&+y, (&+c%s&+y) Comment: &+c%s",
              GET_NAME(ch), EMSD[Omg].Name, EMSD[Omg].Email, reason);
      logit(LOG_EMAILREG, buf);
      emslog(51, buf);

      /* Add this into player's records. */
      sprintf(buf, " raa %s '%s Thawed EMS account for: %s (%s)' %s\n",
              EMSD[Omg].Name, GET_NAME(ch), EMSD[Omg].Name, EMSD[Omg].Email, reason);
      do_ems(ch, buf, cmd);

      return;
      }


   /* Reset application */
   if((!strcmp (letter, "r")) && (!strcmp (letter2, "e")))
      {
      if(EMSD[Pointer].Status == EMS_START)
         {
         send_to_char("&+cThat player does not need to be reset. o_o\n", ch);
         return;
         }

      EMSD[Pointer].Status = EMS_START;

      Omg = EMS_Account_To_Struct(EMSD[Pointer].Name, EMSD[Pointer].PIN,
                                  EMSD[Pointer].Email, EMSD[Pointer].Status);
      EMS_Database_Modify(EMS_MODIFY, EMSD[Omg].Name, EMSD[Omg].PIN,
                          EMSD[Omg].Email, EMSD[Omg].Status);

      EMS_Return_Notice(EMSD[Omg].Name, EMSD[Omg].Email, reason, EMS_START, GET_NAME(ch));

      sprintf(buf,"&+y&+c%s&+y Resets EMS account for: &+c%s&+y, (&+c%s&+y) Comment: &+c%s",
              GET_NAME(ch), EMSD[Omg].Name, EMSD[Omg].Email, reason);
      logit(LOG_EMAILREG, buf);
      emslog(51, buf);

      /* Add this into player's record. */
      sprintf(buf, " raa %s '%s Reset EMS account for: %s (%s)' %s\n",
              EMSD[Omg].Name, GET_NAME(ch), EMSD[Omg].Name, EMSD[Omg].Email, reason);
      do_ems(ch, buf, cmd);

      return;
      }


   /* Killem! Like, evaporate em and stuff */
   if(!strcmp (letter, "k"))
      {
      if(GET_LEVEL(ch) < 59)
         {
         send_to_char("Yeah right, your not even close to being that sweet. o_o\n", ch);
         return;
         }

      if(EMSD[Pointer].Status == EMS_EMPTY)
         {
         send_to_char("&+cThat player has already been deleted fool! o_O\n", ch);
         return;
         }

      /* Wipe them out of the EMS databases */
      EMSD[Pointer].Status = EMS_EMPTY;
      EMS_Database_Modify(EMS_DELETE, EMSD[Pointer].Name, EMSD[Pointer].PIN,
                          EMSD[Pointer].Email, EMSD[Pointer].Status);
      EMS_Return_Notice(EMSD[Pointer].Name, EMSD[Pointer].Email, reason, EMS_DELETED, GET_NAME(ch));

      /* If they're in the game, Nuke em. O_O */
      victim = get_char(EMSD[Pointer].Name);
      if((victim) && (GET_LEVEL(victim) < GET_LEVEL(ch)))
         {
         for(pos = 0; pos < MAX_WEAR; pos++)
            if(victim->equipment[pos])
               {
               t_obj1 = unequip_char(victim, pos, FALSE);
               extract_obj(t_obj1);
               }

         for(t_obj1 = victim->carrying; t_obj1; t_obj1 = t_obj2)
            {
            t_obj2 = t_obj1->next_content;
            obj_from_char(t_obj1);
            extract_obj(t_obj1);
            }

         hold = victim->desc;
         if(IS_PC(victim))
            {
            if(hold && !hold->connected)
               {
               SEND_TO_Q("\n\nCharacter is deleted!\n", hold);
               hold->connected = CON_FLUSH;
               }
            logit(LOG_PLAYER, "%s deleted by %s.", GET_NAME(victim), GET_NAME(ch));
            deleteCharacter(victim);
            }

         extract_char(victim);

         if(hold)
            hold->character = NULL;
         }

      /* Move player databases to Nuked directory */
      sprintf(Gbuf1, "%s/%s/%s.db", EMAIL_REG_FILES, buf, EMSD[Pointer].Name);
      sprintf(Gbuf2, "%s/Nuked/%s.db.%s", EMAIL_REG_FILES,
              EMSD[Pointer].Name, GetFileTime(tmp));
      rename(Gbuf1, Gbuf2);
      sprintf(Gbuf1, "%s/%s/%s.headers", EMAIL_REG_FILES, buf, EMSD[Pointer].Name);
      sprintf(Gbuf2, "%s/Nuked/%s.header.%s", EMAIL_REG_FILES,
              EMSD[Pointer].Name, GetFileTime(tmp));
      rename(Gbuf1, Gbuf2);
      sprintf(Gbuf1, "%s/%s/%s.records", EMAIL_REG_FILES, buf, EMSD[Pointer].Name);
      sprintf(Gbuf2, "%s/Nuked/%s.records.%s", EMAIL_REG_FILES,
              EMSD[Pointer].Name, GetFileTime(tmp));
      rename(Gbuf1, Gbuf2);

      /* Add an entr into the Nuked char database */
      if(!(db = fopen(NUKED_DATABASE, "r+")))
         {
         sprintf(buf, "EMS Error! Cannot open Nuked char database! Event will not be added there.");
         emslog(51, buf);
         logit(LOG_EMAILREG, buf);
         }
      else
         {
         fseek(db, 0L, SEEK_END);
         sprintf(buf, "* %s %d %s %s \n", EMSD[Pointer].Name,
                 EMSD[Pointer].PIN, EMSD[Pointer].Email, GetTime(tmp));
         fputs(buf, db);
         sprintf(buf, "%s \n", reason);
         fputs(buf, db);
         fclose(db);
         }

      sprintf(buf,"&+y&+c%s&+y Deletes EMS account for: &+c%s&+y, (&+c%s&+y) Comment: &+c%s",
              GET_NAME(ch), EMSD[Pointer].Name, EMSD[Pointer].Email, reason);
      logit(LOG_EMAILREG, buf);
      emslog(51, buf);
      send_to_char("&=LWAccount deleted(killed)&n&+W, notice sent to player, players database moved into nuked char's area.\n", ch);

      /* Delete the player file! */
      sprintf(Gbuf1, "%c", EMSD[Pointer].Name[0]);
      sprintf(Gbuf2, "%s/%s/%s", SAVE_DIR, Gbuf1, EMSD[Pointer].Name);
      unlink(Gbuf2);

      /* Erase him from the struct */
      EMS_Account_From_Struct(EMSD[Pointer].Name);

      return;
      }

   return;
}



/* EMS status command, display status of all major EMS functions. */
void do_ems_status(P_char ch, char *Email)
{
   FILE *db;
   char data[MAX_STRING_LENGTH], Gbuf1[MAX_STRING_LENGTH];
   char Output[MAX_STRING_LENGTH];
   char a[8192], b[8192], c[8192], d[8192], e[8192];
   char f[8192], g[8192], h[8192], i[8192], j[8192];
   int List_Types[32], List_Len[32], List_Count = 0;
   unsigned int SojP_Types[8], SojP_Len[8];
   int Count = 0, Records = 0, Spaces = 0, TEK = 0;


   for(Count = 0; Count <= 32; Count++)
      List_Types[Count] = 0;

   sprintf(Output, "&+CStatus of the EMS system:\n\n");

   /* First generate statistics from the Master Database. */
   if(!(db = EMS_Open_Database(db)))
      return;

   fgets(data, 8192, db);
   Records = atoi(data);
   for(Count = 1; Count <= Records; Count++)
      {
      fgets(data, 8192, db);
      if((data == NULL) || (feof(db)))
         {
         break;
         }
      sscanf(data, "%8191s %8191s %8191s %8191s \n", a, b, c, d);

      if(atoi(d) >= 0)
         List_Count++;

      List_Types[(atoi(d) + 3)]++;
      }
   fclose(db);

   List_Types[14] = (List_Types[14] + List_Types[17]);

   /* Determine the length of each value for correct spacing in the output text */
   for(Count = 0; Count <= 31; Count++)
      {
      if(List_Types[Count] <= 9)
         List_Len[Count] = 1;
      else if(List_Types[Count] <= 99)
         List_Len[Count] = 2;
      else if(List_Types[Count] <= 999)
         List_Len[Count] = 3;
      else if(List_Types[Count] <= 9999)
         List_Len[Count] = 4;
      else if(List_Types[Count] <= 99999)
         List_Len[Count] = 5;

      if(List_Len[Count] == 0)
         List_Len[Count] = 1;
      }

   sprintf(Gbuf1, "&+yAccounts stored in the Player Database (Total: &+C%d&n&+y):\n\n", Records);
   strcat(Output, Gbuf1);

   strcpy(data, "");
   Spaces = (5 - List_Len[14]);
   for(Count = 0; Count <= Spaces; Count++)
      strcat(data, " ");
   sprintf(Gbuf1, "&+y   Registered:    &+C%d%s &n&+yUnRegistered:   &+C%d\n",
           List_Types[14], data, List_Types[3]);
   strcat(Output, Gbuf1);

   strcpy(data, "");
   Spaces = (5 - List_Len[7]);
   for(Count = 0; Count <= Spaces; Count++)
      strcat(data, " ");
   sprintf(Gbuf1, "&+y   Pending Reply: &+C%d%s &n&+yPending Accept: &+C%d\n",
           List_Types[7], data, List_Types[8]);
   strcat(Output, Gbuf1);

   strcpy(data, "");
   Spaces = (5 - List_Len[9]);
   for(Count = 0; Count <= Spaces; Count++)
      strcat(data, " ");
   sprintf(Gbuf1, "&+y   Frozen:        &+C%d%s &n&+yDeclined:       &+C%d\n",
           List_Types[9], data, List_Types[11]);
   strcat(Output, Gbuf1);

   strcpy(data, "");
   Spaces = (5 - List_Len[0]);
   for(Count = 0; Count <= Spaces; Count++)
      strcat(data, " ");
   sprintf(Gbuf1, "&+y   NullFile:      &+C%d%s &n&+yUnknown:        &+C%d\n",
           List_Types[0], data, List_Types[1]);
   strcat(Output, Gbuf1);

   strcpy(data, "");
   Spaces = (5 - List_Len[2]);
   for(Count = 0; Count <= Spaces; Count++)
      strcat(data, " ");
   sprintf(Gbuf1, "&+y   Empty:         &+C%d%s &n&+yClosed:         &+C%d\n\n",
           List_Types[2], data, List_Types[15]);
   strcat(Output, Gbuf1);


   if(!(db = Open_Email_Database(db)))
      return;

   fgets(data, 8192, db);
   if((data == NULL) || (feof(db)))
      {
      fclose(db);
      return;
      }
   fclose(db);
   sscanf(data, "%8191s %8191s %8191s %8191s %8191s %8191s %8191s %8191s %8191s %8191s \n", a, b, c, d, e, f, g, h, i, j);

   TEK = atol(b);
   SojP_Types[1] = atol(c);
   SojP_Types[2] = atol(d);
   SojP_Types[3] = atol(e);
   SojP_Types[4] = atol(f);
   SojP_Types[5] = atol(g);
   SojP_Types[6] = atol(h);
   SojP_Types[7] = atol(i);
   SojP_Types[8] = atol(j);

   sprintf(Gbuf1, "&+yStatistical Sojparse Data (Total Emails known: &+C%d&n&+y):\n\n", TEK);
   strcat(Output, Gbuf1);

   for(Count = 1; Count <= 8; Count++)
      {
      if(SojP_Types[Count] <= 9)
         SojP_Len[Count] = 1;
      else if(SojP_Types[Count] <= 99)
         SojP_Len[Count] = 2;
      else if(SojP_Types[Count] <= 999)
         SojP_Len[Count] = 3;
      else if(SojP_Types[Count] <= 9999)
         SojP_Len[Count] = 4;
      else if(SojP_Types[Count] <= 99999)
         SojP_Len[Count] = 5;
      else if(SojP_Types[Count] <= 999999)
         SojP_Len[Count] = 6;
      else if(SojP_Types[Count] <= 9999999)
         SojP_Len[Count] = 7;
      else if(SojP_Types[Count] <= 99999999)
         SojP_Len[Count] = 8;
      else if(SojP_Types[Count] <= 999999999)
         SojP_Len[Count] = 9;

      if(List_Len[Count] == 0)
         List_Len[Count] = 1;
      }

   strcpy(data, "");
   Spaces = (5 - SojP_Len[1]);
   for(Count = 0; Count <= Spaces; Count++)
      strcat(data, " ");
   sprintf(Gbuf1, "&+y   Total Messages Sent:     &+C%d%s &n&+yMessages Sent Out Today: &+C%d\n",
           SojP_Types[1], data, SojP_Types[1]);
   strcat(Output, Gbuf1);

   strcpy(data, "");
   Spaces = (5 - SojP_Len[3]);
   for(Count = 0; Count <= Spaces; Count++)
      strcat(data, " ");
   sprintf(Gbuf1, "&+y   Total Messages Received: &+C%d%s &n&+yMessages Received Today: &+C%d\n",
           SojP_Types[3], data, SojP_Types[4]);
   strcat(Output, Gbuf1);

   strcpy(data, "");
   Spaces = (5 - SojP_Len[5]);
   for(Count = 0; Count <= Spaces; Count++)
      strcat(data, " ");
   sprintf(Gbuf1, "&+y   Total Spammers Blocked:  &+C%d%s &n&+ySpammers Blocked Today:  &+C%d\n",
           SojP_Types[5], data, SojP_Types[6]);
   strcat(Output, Gbuf1);

   strcpy(data, "");
   Spaces = (5 - SojP_Len[7]);
   for(Count = 0; Count <= Spaces; Count++)
      strcat(data, " ");
   sprintf(Gbuf1, "&+y   Total Control Messages:  &+C%d%s &n&+yControl Messages Today:  &+C%d\n",
           SojP_Types[7], data, SojP_Types[8]);
   strcat(Output, Gbuf1);

   sprintf(Gbuf1, "\n&+y   Date stamp of last Email database modification: &+c%s\n",
           GetTimeFromFileNameTime(a));
   strcat(Output, Gbuf1);


   send_to_char(Output, ch);

   return;
}

/* EMS list command, get data from the memory database */
void do_ems_list(P_char ch, char *Gbuf1, char *Gbuf2, char *Gbuf3)
{
   char Output[MAX_STRING_LENGTH], Line[MAX_STRING_LENGTH], buf[8192];
   int Count = 0, Mode = 0, Option = 0, Omg = 0, Loop = 0;

   /* List all accounts pending approval */
   strcpy(Output, "");
   if((!Gbuf2) || (!strcmp (Gbuf2, "")))
      {
      for(Count = 1; Count <= Email_Registration_Database_Top; Count++)
         {
         if(EMSD[Count].Status == EMS_PENDING_ACCEPT)
            {
            Loop++;
            EMS_Create_Output_Text(Line, Count, EMSD[Count].Name, EMSD[Count].PIN,
                                   EMSD[Count].Email, EMSD[Count].Status);
            strcat(Output, Line);
            }
         }
      page_string(ch->desc, Output, 1);
      sprintf(buf, "&+cTotal EMS accounts pending acceptance: &+C%d\n", Loop);
      send_to_char(buf, ch);
      return;
      }

   /* Determine which kind of listing we want */
   Mode = LIST_NULL;
   if((Gbuf2 != NULL) && (strcmp (Gbuf2, "")))
      {
      if(!strcmp (Gbuf2, "nullfile"))
         Option = -3;
      else if(!strcmp (Gbuf2, "unknown"))
         Option = -2;
      else if(!strcmp (Gbuf2, "empty"))
         Option = -1;
      else if(!strcmp (Gbuf2, "start"))
         Option = 0;
      else if(!strcmp (Gbuf2, "badname"))
         Option = 1;
      else if(!strcmp (Gbuf2, "badpin"))
         Option = 2;
      else if(!strcmp (Gbuf2, "bademail"))
         Option = 3;
      else if(!strcmp (Gbuf2, "reply"))
         Option = 4;
      else if(!strcmp (Gbuf2, "accept"))
         Option = 5;
      else if(!strcmp (Gbuf2, "frozen"))
         Option = 6;
      else if(!strcmp (Gbuf2, "thawed"))
         Option = 7;
      else if(!strcmp (Gbuf2, "declined"))
         Option = 8;
      else if(!strcmp (Gbuf2, "broken"))
         Option = 9;
      else if(!strcmp (Gbuf2, "deleted"))
         Option = 10;
      else if(!strcmp (Gbuf2, "okay"))
         Option = 11;
      else if(!strcmp (Gbuf2, "closed"))
         Option = 12;
      else if(!strcmp (Gbuf2, "all"))
         Option = 13;
      else
         Option = -5;

      if((Option > -4) && (Option < 13))
         Mode = LIST_STATUS;
      else if(Option == 13)
         Mode = LIST_ALL;
      }

   Omg = 0;
   strcpy(Output, "");
   for(Count = 1; Count <= Email_Registration_Database_Top; Count++)
      {
      if(((Mode == LIST_STATUS) && (EMSD[Count].Status == Option)) || (Mode == LIST_ALL))
         {
         EMS_Create_Output_Text(Line, Count, EMSD[Count].Name, EMSD[Count].PIN,
                                EMSD[Count].Email, EMSD[Count].Status);
         strcat(Output, Line);
         Omg++;
         }
      }

   page_string(ch->desc, Output, 1);
   sprintf(buf, "&+cTotal Records in Memory: &+C%d&n&+c, Record Displayed: &+C%d\n",
           Email_Registration_Database_Top, Omg);
   send_to_char(buf, ch);

   return;
}


/* Monster EMS lookup, scan the entire master database and print statistics. */
void do_ems_lookup(P_char ch, char *Gbuf1, char *Gbuf2, char *Gbuf3)
{
   FILE *db;
   char Output[1024000], Line[MAX_STRING_LENGTH];
   char buf[MAX_STRING_LENGTH], data[MAX_STRING_LENGTH];
   char UserName[8192], DomainName[8192], tmp[2];
   char TmpUserName[8192], TmpDomainName[8192];
   char a[MAX_STRING_LENGTH], b[MAX_STRING_LENGTH];
   char c[MAX_STRING_LENGTH], d[MAX_STRING_LENGTH];
   unsigned int Mode = 0, Option = 0, Heh = 0, Wtf = 0;
   unsigned int Records = 0, Count = 0, Omg = 0;

   if(Gbuf2)
      {
      /* Determine which kind of listing we want */
      Mode = LOOKUP_NULL;

      if((atoi(Gbuf2) > 12) && (atoi(Gbuf2) < 9999))
         Mode = LOOKUP_PIN;
      else if((atoi(Gbuf2) > 0) && (atoi(Gbuf2) < 13))
         Mode = LOOKUP_STATUS;
      else
         {
         /* Now parse the input, find out if its a domain or name we are hunting for. */
         Omg = -1;
         strcpy(UserName, "");
         strcpy(DomainName, "");
         for(Wtf = 0; Wtf <= strlen(Gbuf2); Wtf++)
            {
            sprintf(tmp, "%c", Gbuf2[Wtf]);
            if(!strcmp (tmp, "@"))
               {
               Omg = Wtf;
               if(Omg == 0)
                  Mode = LOOKUP_DOMAIN_NAME;
               else if(Omg == (strlen(Gbuf2) - 1))
                  Mode = LOOKUP_USER_NAME;
               else
                  Mode = LOOKUP_EMAIL;
               continue;
               }
            if(Omg != 0)
               strcat(UserName, tmp);
            else
               strcat(DomainName, tmp);
            }
         if(Omg == -1)
            Mode = LOOKUP_NAME;


         /* Now do the name search. First see if the name matches any of our known
            status flags. If it does, then we want a list of all accounts that have
            the same status as the god wants. If not, we want a specific char. record */
         if(Mode == LOOKUP_NAME)
            {
            if(!strcmp (Gbuf2, "nullfile"))
               Option = -3;
            else if(!strcmp (Gbuf2, "unknown"))
               Option = -2;
            else if(!strcmp (Gbuf2, "empty"))
               Option = -1;
            else if(!strcmp (Gbuf2, "start"))
               Option = 0;
            else if(!strcmp (Gbuf2, "badname"))
               Option = 1;
            else if(!strcmp (Gbuf2, "badpin"))
               Option = 2;
            else if(!strcmp (Gbuf2, "bademail"))
               Option = 3;
            else if(!strcmp (Gbuf2, "reply"))
               Option = 4;
            else if(!strcmp (Gbuf2, "accept"))
               Option = 5;
            else if(!strcmp (Gbuf2, "frozen"))
               Option = 6;
            else if(!strcmp (Gbuf2, "thawed"))
               Option = 7;
            else if(!strcmp (Gbuf2, "declined"))
               Option = 8;
            else if(!strcmp (Gbuf2, "broken"))
               Option = 9;
            else if(!strcmp (Gbuf2, "deleted"))
               Option = 10;
            else if(!strcmp (Gbuf2, "okay"))
               Option = 11;
            else if(!strcmp (Gbuf2, "closed"))
               Option = 12;
            else if(!strcmp (Gbuf2, "first"))
               Option = 14;
            else if(!strcmp (Gbuf2, "all"))
               Option = 15;
            else
               Option = -5;

            if((Option > -4) && (Option < 15))
               Mode = LOOKUP_STATUS;
            else if(Option == 15)
               Mode = LOOKUP_ALL;
            }
         }

      /* If we didn't pick a mode, go home. */
      if(Mode == LOOKUP_NULL)
         {
         EMS_Usage(ch);
         return;
         }

      /* First make sure our database is good */
      if(!(db = EMS_Open_Database(db)))
         return;

      /* Scroll through the database one line at a time. */
      Omg = 0;
      fgets(data, 8192, db);
      Records = atoi(data);
      strcpy(Output, "");

      for(;;)
         {
         Count++;
         if(Count > Records)
            break;

         fgets(data, 8192, db);
         if((data == NULL) || (feof(db)))
            {
            sprintf(buf, "&+CEMS database is trunkated, should have %d records, %d were read.\n",
                    Records, Count);
            send_to_char(buf, ch);
            break;
            }

         /* Parse the data */
         strcpy(a, "");
         strcpy(b, "");
         strcpy(c, "");
         strcpy(d, "");
         sscanf(data, "%s %s %s %s \n", a, b, c, d);
         strcpy(data, "");

         /* If were just looking for a fragment of the Email address,
            such as hostname or domain name, then chop up each entry
            in the master database for analysis. */
         if((Mode == LOOKUP_USER_NAME) || (Mode == LOOKUP_DOMAIN_NAME))
            {
            strcpy(TmpUserName, "");
            strcpy(TmpDomainName, "");
            Heh = -1;
            for(Wtf = 0; Wtf <= strlen(c); Wtf++)
               {
               sprintf(tmp, "%c", c[Wtf]);
               if(!strcmp (tmp, "@"))
                  {
                  Heh = Wtf;
                  continue;
                  }
               if((Mode == LOOKUP_USER_NAME) && (Heh == -1))
                  strcat(TmpUserName, tmp);
               else if((Mode == LOOKUP_DOMAIN_NAME) && (Heh > -1))
                  strcat(TmpDomainName, tmp);
               }
            }

         /* Now were ready to print the entry, maybe. This conditional
            determines weather or not we print this database record to
            the god or not, depending on what mode the god wants. */
         if((Mode == LOOKUP_ALL) ||
            ((Mode == LOOKUP_NAME) && (!strcmp (a, Gbuf2))) ||
            ((Mode == LOOKUP_STATUS) && (atoi(d) == Option)) ||
            ((Mode == LOOKUP_PIN) && (!strcmp(b, Gbuf2))) ||
            ((Mode == LOOKUP_EMAIL) && (!strcmp (c, Gbuf2))) ||
            ((Mode == LOOKUP_USER_NAME) && (!strcmp (TmpUserName, UserName))) ||
            ((Mode == LOOKUP_DOMAIN_NAME) && (!strcmp (TmpDomainName, DomainName))))
            {
            strcpy(Line, "");
            EMS_Create_Output_Text(Line, Count, a, atoi(b), c, atoi(d));
            strcat(Output, Line);
            Omg++;
            }
         }

      if((Mode == LOOKUP_ALL) || (Mode == LOOKUP_STATUS) ||
         (Mode == LOOKUP_USER_NAME) || (Mode == LOOKUP_DOMAIN_NAME))
         {
         sprintf(buf, "&+cTotal Records in Master Database: &+C%d&n&+c, total displayed: &+C%d\n",
                 Records, Omg);
         strcat(Output, buf);
         }

      page_string(ch->desc, Output, 1);

      fclose(db);

      if((Mode == LOOKUP_NAME) || (Mode == LOOKUP_EMAIL))
         Email_Database_Lookup(ch, Gbuf2, Mode);

      }
   else
      {
      EMS_Usage(ch);
      }

   return;
}



/* EMS data from the player header files. */
void do_ems_header(P_char ch, char *Gbuf1, char *Gbuf2, char *Gbuf3)
{
   FILE *header;
   char reason[MAX_STRING_LENGTH], buf[MAX_STRING_LENGTH];
   char a[8192], b[8192], c[8192], d[8192], e[8192];
   char f[8192], g[8192], h[8192], i[8192], j[8192];
   char data[8192], letter[2], ack[8192];
   int Omg = 0, Count = 0;


   if((!Gbuf2) || (strlen (Gbuf2) < 1) || (!strcmp (Gbuf2, "list")))
      {
      send_to_char("&+CIncorrect usage of the EMS command.\n\n", ch);
      EMS_Usage(ch);
      return;
      }

   sprintf(buf, "%s/%c/%s.headers", EMAIL_REG_FILES, Gbuf2[0], Gbuf2);
   if(!(header = fopen(buf, "r")))
      {
      sprintf(buf, "&+CNo Email headers found on file user: &+C%s\n", Gbuf2);
      send_to_char(buf, ch);
      return;
      }

   /* Determine what usage of the header to display */
   Omg = 0;
   if((!Gbuf3) || (!strcmp (Gbuf3, "")))
      {
      Omg = -2;                      /* Display most recent header */
      sprintf(buf, "\n&+cDisplaying most recent Email header for: &+C%s&n&+c.\n", Gbuf2);
      send_to_char(buf, ch);
      }
   else
      {
      strcpy(letter, "");
      sprintf(letter, "%c", Gbuf3[0]);
      if((!strcmp (Gbuf3, "list")) || (!strcmp (letter, "l")))
         {
         sprintf(buf, "\n&+cDisplaying list of Email headers for: &+C%s&n&+c.\n", Gbuf2);
         send_to_char(buf, ch);
         Omg = -1;                    /* Display list of headers for this player */
         }
      else
         {
         sprintf(buf, "\n&+cDisplay Email header #&+C%s&n&+c for: &+C%s&n&+c.\n", Gbuf2, Gbuf3);
         send_to_char(buf, ch);
         Omg = atoi(Gbuf3);           /* Display selected header */
         }
      if((Omg < -1) || (Omg > 999))
         {
         send_to_char("&+cRequested header does not exist.\n", ch);
         return;
         }
      }

   /* Now show the user the requested information */
   Count = 0;
   strcpy(a, "");
   strcpy(b, "");
   strcpy(data, "");
   strcpy(reason, "");
   for(;;)
      {
      fgets(data, 8192, header);
      if((data == NULL) || (feof(header)))
         break;

      sscanf(data, "%8191s %8191s \n", a, b);
      if(!strcmp (a, "EMAIL_HEADER"))
         {
         if(Omg == Count)
            break;

         Count++;
         strcpy(reason, "");

         if((Omg > 0) && (Omg != Count))
            continue;

         sscanf(data, "%8191s %8191s %8191s %8191s %8191s %8191s %8191s %8191s %8191s %8191s \n", a, b, c, d, e, f, g, h, i, j);
         sprintf(buf, "%s %s %s %s %s", e, f, g, h, i);
         sprintf(Gbuf1,
                 "&+yHeader #&+C%d&n&+y: Sent from: &+c%s&+y, Status: &+c%s&+y, Dated: &+c%s\n",
                 Count, b, GET_EMS_STATUS(ack, atoi(c)), buf);
         if((Omg == -1) || (Omg == Count))
            send_to_char(Gbuf1, ch);

         continue;
         }
      else
         {
         strcat(reason, data);
         }
      }

   if(Omg == -1)
      return;

   if(Omg == -2)
      send_to_char(Gbuf1, ch);

   send_to_char(reason, ch);

   fclose(header);
   return;
}


/* Locate a record */
int EMS_Return_Account_Pointer(char *Gbuf2)
{
   FILE *db;
   char data[8192], a[8192], b[8192], c[8192], d[8192];
   int Pointer = 0, Count = 0, Records = 0;


   Pointer = 0;
   for(Count = 1; Count <= Email_Registration_Database_Top; Count++)
      {
      if(!strcmp (Gbuf2, EMSD[Count].Name))
         {
         Pointer = Count;
         break;
         }
      }

   if(Pointer == 0)
      {
      /* First make sure our database is good */
      if(!(db = EMS_Open_Database(db)))
         return 0;

      fgets(data, 8192, db);
      Records = atoi(data);
      for(Count = 1; Count <= Records; Count++)
         {
         fgets(data, 8192, db);
         if((data == NULL) || (feof(db)))
            break;

         sscanf(data, "%8191s %8191s %8191s %8191s \n", a, b, c, d);

         if(!strcmp (Gbuf2, a))
            {
            Pointer = EMS_Account_To_Struct(a, atoi(b), c, atoi(d));
            break;
            }
         }
      fclose(db);
      }

   return Pointer;
}


/* Player Records functions */
void do_ems_records(P_char ch, char *Gbuf1, char *Gbuf2, char *Gbuf3,
                    char *arg, char *letter2, char *letter3)
{
   FILE *record, *record_tmp;
   char buf[MAX_STRING_LENGTH], reason[MAX_STRING_LENGTH];
   char data[8192], file[8192], title[8192], ack[8192], tmp[2];
   char a[8192], b[8192], c[8192], d[8192], e[8192], f[8192];
   int Mode = 0, RecordData = 0, Count = 0, Loop = 0;
   int Dork = 0, Laugh = 0, Total = 0, Pointer = 0;


   sprintf(buf, "Records: (%s/%s/%s)(%s)(%s)(%s)",
           Gbuf1, Gbuf2, Gbuf3, arg, letter2, letter3);
   debuglog(51, DS_EMS, buf);

   /* Choose which record commands (Mode) we want. */
   Mode = RECORD_NULL;
   if(!strcmp (letter2, "l"))
      {
      Mode = RECORD_LIST;
      }
   else if(!strcmp (letter2, "s"))
      {
      Mode = RECORD_SHOW;
      }
   else if(!strcmp (letter2, "a"))
      {
      Mode = RECORD_ADD;
      if(!strcmp (letter3, "a"))
         {
         Mode = RECORD_AUTO_INSERT;
         }
      }
   else if(!strcmp (letter2, "d"))
      {
      Mode = RECORD_DELETE;
      }
   else
      {
      send_to_char("&+CWhat Record command you looking for?\n", ch);
      EMS_Usage(ch);
      return;
      }

   Pointer = EMS_Restore_Account(2, ch->desc, Gbuf2);
   if(Pointer == 0)
      {
      sprintf(buf, "&+COops! Cannot find %s's account!\n",
              EMSD[Pointer].Name);
      emslog(51, buf);
      logit(LOG_STATUS, buf);
      return;
      }

   /* Make sure the player record is available, prep the temporary file. */
   sprintf(tmp, "%c", EMSD[Pointer].Name[0]);
   sprintf(file, "%s/%s/%s.records", EMAIL_REG_FILES, tmp, EMSD[Pointer].Name);
   sprintf(buf, "%s.tmp", file);

   RecordData = YES;
   if(!(record = fopen(file, "r+")))
      {
      RecordData = NO;
      if(!(record = fopen(file, "w")))
         {
         sprintf(buf, "&+COops! Cannot open %s's records file for some reason. &+LO_O.\n",
                 EMSD[Pointer].Name);
         emslog(51, buf);
         logit(LOG_STATUS, buf);
         return;
         }
      }

   /* Does the record contain data? */
   if(RecordData == YES)
      {
      fgets(data, 8192, record);
      if((data == NULL) || (feof(record)))
         {
         RecordData = NO;
         }
      }
   rewind(record);


   if((RecordData == NO) && (Mode != RECORD_ADD) && (Mode != RECORD_AUTO_INSERT))
      {
      send_to_char("&+cThat player's record is empty.\n", ch);
      fclose(record);
      return;
      }

   /* Make sure our temporary record is available. */
   if(Mode == RECORD_DELETE)
      {
      if((!Gbuf3) || (!strcmp (Gbuf3, "")))
         {
         send_to_char("&+CYou must specify which record to delete, use it's number under rlist.\n",
                      ch);
         fclose(record);
         return;
         }

      remove(buf);
      if(!(record_tmp = fopen(buf, "w")))
         {
         send_to_char("&+CAck, cannot open the record temporary file for this user! &+LO_O.\n", ch);
         fclose(record);
         return;
         }
      }

   /* If were just adding, scroll to the end of the file and plop it down. */
   if((Mode == RECORD_ADD) || (Mode == RECORD_AUTO_INSERT))
      {
      if((!Gbuf3) || (!strcmp (Gbuf3, "")))
         {
         send_to_char("&+CYou need to add some text behind 'add' fool. o_o\n", ch);
         fclose(record);
         return;
         }

      /* Process the commentary */
      Count = 0;
      Dork = 0;
      Laugh = 0;
      strcpy(title, "");
      strcpy(reason, "");

      for(Loop = 0; Loop <= strlen(arg); Loop++)
         {
         sprintf(buf, "%c", arg[Loop]);
         if(Count > 2)
            {
            if(Dork == 2)
               {
               Dork = 3;
               continue;
               }

            if(!strcmp (buf, "'"))
               {
               Dork++;
               continue;
               }

            if(Dork == 1)
               {
               strcat(title, buf);
               if(!strcmp (buf, " "))
                  Laugh++;
               }
            else
               {
               strcat(reason, buf);
               }
            }
         if(!strcmp (buf, " "))
            Count++;
         }
      if((Dork == 0) && (Count > 2))
         sprintf(title, "No Title");

      if(Laugh > 10)
         {
         send_to_char("Message titles can be no longer than 10 words.\n", ch);
         return;
         }
      sprintf(buf, "### %s %s %s", GET_NAME(ch), GetTime(ack), title);
      sprintf(ack, "\n");
      fseek(record, 0L, SEEK_END);
      fputs(buf, record);
      fputs(ack, record);
      fputs(ack, record);
      fputs(reason, record);
      fputs(ack, record);
      if(Mode == RECORD_ADD)
         fputs(ack, record);
      fclose(record);

      if(Mode == RECORD_ADD)
         {
         sprintf(buf, "&+cAdded this record to &+C%s&n&+c's file:\n", EMSD[Pointer].Name);
         send_to_char(buf, ch);
         sprintf(buf, " rshow %s", EMSD[Pointer].Name);
         do_ems(ch, buf, CMD_EMS);
         }

      return;
      }

   /* For everything else, go through the record line by line and decide what to
      do as we go along. */
   if((Mode == RECORD_LIST) || (Mode == RECORD_SHOW) || (Mode == RECORD_DELETE))
      {
      Count = 0;
      Laugh = 0;
      Total = 0;

      if(!strcmp (Gbuf3, "all"))
         Laugh = 1;

      for(;;)
         {
         strcpy(buf, "");
         fgets(data, 8192, record);
         if((data == NULL) || (feof(record)))
            break;
         sscanf(data, "%s %s \n", buf, ack);
         if(!strcmp (buf, "###"))
            Total++;
         }
      rewind(record);

      for(;;)
         {
         fgets(data, 8192, record);
         if((data == NULL) || (feof(record)))
            break;
         for(Dork = 1; Dork <= 10; Dork++)
            strcpy(WORDS[Dork].Text, "");
         strcpy(a, "");
         sscanf(data, "%8191s %8191s %8191s %8191s %8191s %8191s %8191s %8191s %8191s %8191s %8191s %8191s %8191s %8191s %8191s %8191s \n",
                a, b, c, d, e, f, WORDS[1].Text, WORDS[2].Text, WORDS[3].Text,
                WORDS[4].Text, WORDS[5].Text, WORDS[6].Text, WORDS[7].Text,
                WORDS[8].Text, WORDS[9].Text, WORDS[10].Text);

         /* Found an Index mark */
         if(!strcmp (a, "###"))
            {
            Count++;

            if((atoi(Gbuf3) < 9999) && (atoi(Gbuf3) > 0) &&
               (Count > atoi(Gbuf3)) && (Mode == RECORD_SHOW))
               break;

            strcpy(Gbuf2, "");
            for(Dork = 1; Dork <= 10; Dork++)
               {
               if((WORDS[Dork].Text != NULL) && (strcmp (WORDS[Dork].Text, "")))
                  {
                  sprintf(buf, "%s ", WORDS[Dork].Text);
                  strcat(Gbuf2, buf);
                  }
               }
            if(strlen(Gbuf2) < 2)
               sprintf(Gbuf2, "No Title");
            sprintf(buf, "%s %s %s %s", c, d, e, f);

            sprintf(Gbuf1,
                    "&+y#&+C%d&n&+y By: &+c%s&+y on &+L%s&n&+y: &+c%s\n",
                    Count, b, buf, Gbuf2);

            if((Mode == RECORD_LIST) || (Laugh == 1))
               {
               send_to_char(Gbuf1, ch);
               }
            else if(((Mode == RECORD_SHOW) && (atoi(Gbuf3) == Count)) ||
                    ((Mode == RECORD_SHOW) && (Total == Count)))
               {
               send_to_char(Gbuf1, ch);
               send_to_char("\n", ch);
               }
            else if(Mode == RECORD_DELETE)
               {
               if(atoi(Gbuf3) == Count)
                  {
                  if((Mode == RECORD_DELETE) && (GET_LEVEL(ch) < 59) &&
                     (strcmp (b, GET_NAME(ch))))
                     {
                     send_to_char("You aren't awesome enough to delete that record.\n", ch);
                     fclose(record);
                     fclose(record_tmp);
                     return;
                     }

                  sprintf(buf, "&+yDeleting record #&+C%d&n&+y from &+c%s&+y's database.\n",
                          Count, EMSD[Pointer].Name);
                  send_to_char(buf, ch);
                  }
               else
                  {
                  fputs(data, record_tmp);
                  }
               }

            continue;
            }

         if(((Mode == RECORD_SHOW) && (atoi(Gbuf3) == Count)) || (Laugh == 1) ||
            ((Mode == RECORD_SHOW) && (Count == Total)))
            send_to_char(data, ch);

         if(Mode == RECORD_DELETE)
            if(atoi(Gbuf3) != Count)
               fputs(data, record_tmp);
         }
      fclose(record);

      if(Mode == RECORD_DELETE)
         {
         Count--;
         fclose(record_tmp);
         sprintf(Gbuf1, "%s.tmp", file);
         sprintf(Gbuf2, "%s.old", file);
         rename(file, Gbuf2);
         rename(Gbuf1, file);
         }

      sprintf(buf, "&+cTotal Records in Players Database: &+C%d&n&+y.\n", Count);
      send_to_char(buf, ch);
      }

   return;
}





/* Lookup an EMS account, return a string with the data values separated by spaces. */
char *EMS_Lookup_Account(char *name, char *info)
{
   FILE *db;
   char buf[MAX_STRING_LENGTH], data[8192];
   char a[8192], b[8192], c[8192], d[8192];
   int Count = 0, Records = 0;

   sprintf(info, "NULL");

   for(Count = 1; Count <= Email_Registration_Database_Top; Count++)
      {
      if(!strcmp (name, EMSD[Count].Name))
         {
         sprintf(info,"%s %d %s %d",
                 EMSD[Count].Name, EMSD[Count].PIN,
                 EMSD[Count].Email, EMSD[Count].Status);
         /*
         fprintf(stderr, "PROCESS_LOOK: (%s)\n", info);
         */
         return(info);
         }
      }

   /* First make sure our database is good */
   if(!(db = EMS_Open_Database(db)))
      return(info);

   fgets(data, 8192, db);
   Records = atoi(data);
   for(Count = 1; Count <= Records; Count++)
      {
      fgets(data, 8192, db);
      if((data == NULL) || (feof(db)))
         {
         sprintf(buf, "&+CEMS database is trunkated, should have %d records, %d were read.\n",
                 Records, Count);
         emslog(51, buf);
         fclose(db);
         return(info);
         }
      sscanf(data, "%8191s %8191s %8191s %8191s \n", a, b, c, d);

      if(!strcmp (a, name))
         {
         sprintf(info, "%s %s %s %s", a, b, c, d);
         /*
         fprintf(stderr, "PROCESS_LOOK: (%s) (%s) (%s) (%s)\n", a, b, c, d);
         */
         fclose(db);
         return(info);
         }
      }
   fclose(db);


   return(info);
}


/* Lookup an EMS account, return just the account status as an integer. */
int EMS_Lookup_Account_Status(char *name)
{
   FILE *db;
   char buf[MAX_STRING_LENGTH], data[8192];
   char a[8192], b[8192], c[8192], d[8192];
   int Count = 0, Records = 0;

   for(Count = 1; Count <= Email_Registration_Database_Top; Count++)
      {
      if(!strcmp (name, EMSD[Count].Name))
         {
         return(EMSD[Count].Status);
         }
      }

   /* First make sure our database is good */
   if(!(db = EMS_Open_Database(db)))
      return(-10);

   fgets(data, 8192, db);
   Records = atoi(data);
   for(Count = 1; Count <= Records; Count++)
      {
      fgets(data, 8192, db);
      if((data == NULL) || (feof(db)))
         {
         sprintf(buf, "&+CEMS database is trunkated, should have %d records, %d were read.\n",
                 Records, Count);
         emslog(51, buf);
         fclose(db);
         return(-10);
         }
      sscanf(data, "%8191s %8191s %8191s %8191s \n", a, b, c, d);

      if(!strcmp (a, name))
         {
         fclose(db);
         return(atoi(d));
         }
      }
   fclose(db);

   return(-10);
}


/* This stores an EMS record in the memory EMS struct, and expands the
   struct if it has run out of room */
int EMS_Account_To_Struct(char *name, int pin, char *email, int status)
{
   int Pointer = 0, Count = 0;

   Pointer = 0;
   if(Email_Registration_Database_Top > 0)
      {
      for(Count = 1; Count <= Email_Registration_Database_Top; Count++)
         {
         if(!strcmp (name, EMSD[Count].Name))
            {
            Pointer = Count;
            break;
            }
         }

      if(Pointer == 0)
         {
         for(Count = 1; Count <= Email_Registration_Database_Top; Count++)
            {
            if(EMSD[Count].Status == EMS_EMPTY)
               {
               Pointer = Count;
               break;
               }
            }
         }
      }

   if(Pointer == 0)
      {
      if((Email_Registration_Database_Top + 1) > EMS_Struct_Max_Records)
         {
         EMS_Struct_Max_Records = (EMS_Struct_Max_Records + 50);

         RECREATE(EMSD, struct email_registration_data, EMS_Struct_Max_Records);
         }

      Email_Registration_Database_Top++;
      Pointer = Email_Registration_Database_Top;
      }

   EMSD[Pointer].PIN = pin;
   EMSD[Pointer].Status = status;
   strcpy(EMSD[Pointer].Name, name);
   strcpy(EMSD[Pointer].Email, email);

   return Pointer;
}


void EMS_Account_From_Struct(char *name)
{
   int Pointer = 0, Count = 0;

   Pointer = 0;
   for(Count = 1; Count <= Email_Registration_Database_Top; Count++)
      {
      if(!strcmp (name, EMSD[Count].Name))
         {
         EMSD[Count].Status = EMS_EMPTY;
         EMSD[Count].PIN = 0;
         strcpy(EMSD[Count].Name, "");
         strcpy(EMSD[Count].Email, "");

         break;
         }
      }
   return;
}


char *EMS_File_To_Struct(char *file, char *stor)
{
   FILE *in;
   char data[8192], buf[MAX_STRING_LENGTH];

   if(!(in = fopen(file, "r")))
      {
      sprintf(buf, "*** EMS Error! Cannot open text file (%s), storing null data in its place.",
              file);
      logit(LOG_EMAILREG, buf);
      fprintf(stderr, "%s", buf);
      return NULL;
      }

   strcpy(stor, "");
   for(;;)
      {
      fgets(data, 8192, in);

      if((data == NULL) || (feof(in)))
         break;

      strcat(stor, data);
      }

   fclose(in);

   return stor;
}


/* A Test! o_o */
#ifndef _WIN32
int new_system(const char *cmdstring)
{
   pid_t pid;
   int   status;

   if(cmdstring == NULL)
      return 1;

   if((pid = fork()) < 0)
   {
      status = -1;
   }
   else if(pid == 0)
   {
      execl("/bin/sh", "sh", "-c", cmdstring, (char *)0);
      dump_core();
   }
   else
   {
      while(waitpid(pid, &status, 0) < 0)
      {
         if(errno != EINTR)
         {
            status = -1;
            break;
         }
      }
   }

   return status;
}
#else
/* Stub on Windows to keep build green; POSIX new_system is unavailable */
int new_system(const char *cmdstring)
{
   (void)cmdstring;
   return -1;
}
#endif


unsigned int EMS_Increment_Counter(unsigned int Num)
{
   if((Num + 1) > MAX_COUNTER)
      Num = 0;
   else
      Num++;

   return Num;
}

void do_reject(P_char ch, char *arg, int cmd)
{
   char buf[MAX_STRING_LENGTH];

   sprintf(buf, " decline %s ", arg);

   do_ems(ch, buf, CMD_EMS);

   return;
}

char *GetTime(char *In)
{
   char a[64], b[64], c[64], d[64], e[64];
   struct tm *ptr;
   time_t lt;

   lt = time(NULL);
   ptr = localtime(&lt);

   sprintf(In, "%s", asctime(ptr));

   sscanf(In, "%s %s %s %s %s \n", a, b, c, d, e);
   sprintf(In, "%s %s %s %s", a, b, c, d);

   return(In);
}

char *GetFileTime(char *In)
{
   char a[64], b[64], c[64], d[64], e[64];
   struct tm *ptr;
   time_t lt;

   lt = time(NULL);
   ptr = localtime(&lt);

   sprintf(In, "%s", asctime(ptr));

   sscanf(In, "%s %s %s %s %s \n", a, b, c, d, e);
   sprintf(In, "%s_%s_%s", a, b, c);

   return(In);
}


/* Multi-play checker. This looks for two things.. One, it checks all players in the
   game (linkdead or not), looking for two people with the same Email address. If it
   finds them, it alarms. It also looks for same-site players, and prints a warning
   to status if it finds them. This should make the admins reeeeel happy. o_o */
int EMS_Multi_Player(P_desc player, int Room)
{
   char buf[MAX_STRING_LENGTH], Name[MAX_STRING_LENGTH];
   P_char t_ch, target;
   P_desc d;
   int Fools = 0, Inn = NO, Pointer = 0, Other_Pointer = 0;
   struct func_attachment *fn;


   /* Temporary testing block on Multi-Play check */
   return NO;

   strcpy(Name, "");
   sprintf(Name, "%s", EMS_NAME(GET_NAME(player->character)));
   Pointer = EMS_Return_Account_Pointer(Name);
   if(Pointer == 0)
      return 0;

   if(EMSD[Pointer].Status == EMS_START)
      return 0;

   for(t_ch = PC_list; t_ch; t_ch = t_ch->next)
      {
      if(t_ch->desc)
         d = t_ch->desc;
      else if(t_ch->only.pc->switched && t_ch->only.pc->switched->desc)
         d = t_ch->only.pc->switched->desc;
      else
         d = NULL;

      if(!d)
         continue;

      if(d && d->connected)
         continue;  /* not in game yet */

      if(!t_ch || (t_ch->in_room < 0) || (t_ch->in_room >= top_of_world))
         continue;  /* not in game */

      if(d == player)
         continue;  /* This is us! */

      if(!GET_NAME(d->character))
         continue;

      strcpy(Name, "");
      sprintf(Name, "%s", EMS_NAME(GET_NAME(d->character)));
      Other_Pointer = EMS_Return_Account_Pointer(Name);
      if(Pointer == 0)
         continue;

      if((!EMSD[Pointer].Email) || (!strcmp (EMSD[Pointer].Email, "NULL")) ||
         (!EMSD[Other_Pointer].Email) || (!strcmp (EMSD[Other_Pointer].Email, "NULL")))
         return 0;

      if((!strcmp (EMSD[Pointer].Email, EMSD[Other_Pointer].Email)) &&
         (GET_LEVEL(d->character) < 50) && (GET_LEVEL(player->character) < 50))
         {
         Fools++;

         if(t_ch->in_room != Room)
            {
            sprintf(buf,
                    "&+CMULTI-PLAYER: &+W%s &n&+cand &+W%s&n&+c both logged in, and have the same Email address!",
                    GET_NAME(d->character), GET_NAME(player->character));
            emslog(51, buf);
            sprintf(buf, "&+CMULTI-PLAYER: &n&+cThey are not in the same room, %s got warned/disconnected.",
                    GET_NAME(player->character));
            emslog(51, buf);

            page_string(player, ems_multi_player, 0);

            sprintf(buf, "&+CWarning! %s just tried to log in while in a different room!\n",
                    GET_NAME(player->character));

            send_to_char(buf, t_ch);
            send_to_char("&+CThis is not allowed, you can only log in two chars at the same\n", t_ch);
            send_to_char("&+Ctime in an INN room. Read the help-multiplayer rules.\n", t_ch);

            return YES;
            }

         /* Check to see if we are standing next to a receptionist mob. */
         Inn = NO;
         for(target = NPC_list; target; target = target->next)
            {
            if((target->in_room == NOWHERE) || (target->in_room != t_ch->in_room))
               continue;

            if(mob_index[target->nr].func)
               {
               fn = mob_index[target->nr].func;

               while(fn)
                  {
                  if(!strcmp (fn->name, "receptionist"))
                     {
                     Inn = YES;
                     break;
                     }
                  fn = fn->next;
                  }
               }
            }

         if(Inn == NO)
            {
            sprintf(buf,
                    "&+CMULTI-PLAYER: &+W%s &n&+cand &+W%s&n&+c both logged in, and have the same Email address!",
                    GET_NAME(d->character), GET_NAME(player->character));
            emslog(51, buf);
            sprintf(buf, "&+CMULTI-PLAYER: &n&+cThey are not in an INN room, %s got warned/disconnected. ",
                    GET_NAME(player->character));
            emslog(51, buf);

            page_string(player, ems_multi_player, 0);

            sprintf(buf, "&+CWarning! %s just tried to log in, but your not in an INN room!\n",
                    GET_NAME(player->character));
            send_to_char(buf, t_ch);
            send_to_char("&+CThis is not allowed, you can only log in two chars at the same\n", t_ch);
            send_to_char("&+Ctime in an INN room. Read the help-multiplayer rules.\n", t_ch);

            return YES;
            }
         else
            {
            /*
                    sprintf(buf, "&+CMULTI-PLAYER: &n&+cBoth in the same INN, they have been warned/flagged.");
                    emslog(51, buf);

                    EMSD[Pointer].Status = EMS_MULTI_PLAYING;
                    EMSD[Other_Pointer].Status = EMS_MULTI_PLAYING;
                    EMS_Account_To_Struct(EMSD[Pointer].Name, EMSD[Pointer].PIN,
                                          EMSD[Pointer].Email, EMSD[Pointer].Status);
                    EMS_Account_To_Struct(EMSD[Other_Pointer].Name, EMSD[Other_Pointer].PIN,
                                          EMSD[Other_Pointer].Email, EMSD[Other_Pointer].Status);
            */
            return NO;
            }
         }

      /*
          if((EMSD[Pointer].PIN == EMSD[Other_Pointer].PIN) &&
             (t_ch->in_room == Room))
          {
            sprintf(buf, "&+CMULTI-PLAYER &n&+cWarning-> &+W%s&n&+c and &+W%s&n&+c have the same Pin number and are in the same room, suspect they are multi-playing.",
              GET_NAME(player->character), GET_NAME(t_ch));
            emslog(51, buf);
          }

          if((EMSD[Pointer].PIN == EMSD[Other_Pointer].PIN) &&
             (!strcmp (full_hostname(t_ch->desc, 50, 0), full_hostname(player, 50, 0))))
          {
            sprintf(buf, "&+CMULTI-PLAYER &n&+cWarning-> &+W%s&n&+c and &+W%s&n&+c have the same Pin number and are logged in from the same site, suspect they are multi-playing.",
              GET_NAME(player->character), GET_NAME(t_ch));
            emslog(51, buf);

            sprintf(buf, "DEBUG: full_host for t_ch: (%s), full_host for player: (%s)",
              full_hostname(t_ch->desc, 50, 0), full_hostname(player, 50, 0));
            emslog(51, buf);

          }
      */
      }

   return NO;
}


/* Alias commands for EMS, makes all the EMS sub-commands grantable and thus
   more controllable when distributed to others. */
void do_ems_alias(P_char ch, char *arg, int cmd)
{
   char Gbuf1[MAX_STRING_LENGTH];

   if(cmd == CMD_ESTAT)
      sprintf(Gbuf1, " status%s", arg);
   else if(cmd == CMD_ELIST)
      sprintf(Gbuf1, " list%s", arg);
   else if(cmd == CMD_ELOOK)
      sprintf(Gbuf1, " lookup%s", arg);
   else if(cmd == CMD_EHEAD)
      sprintf(Gbuf1, " header%s", arg);
   else if(cmd == CMD_RADD)
      sprintf(Gbuf1, " radd%s", arg);
   else if(cmd == CMD_RSHOW)
      sprintf(Gbuf1, " rshow%s", arg);
   else if(cmd == CMD_RDEL)
      sprintf(Gbuf1, " rdelete%s", arg);
   else if(cmd == CMD_RLIST)
      sprintf(Gbuf1, " rlist%s", arg);
   else if(cmd == CMD_EKILL)
      sprintf(Gbuf1, " kill%s", arg);
   else if(cmd == CMD_EFREEZE)
      sprintf(Gbuf1, " freeze%s", arg);
   else if(cmd == CMD_ETHAW)
      sprintf(Gbuf1, " thaw%s", arg);
   else if(cmd == CMD_ERESET)
      sprintf(Gbuf1, " reset%s", arg);
   else if(cmd == CMD_EHELP)
      sprintf(Gbuf1, " ?%s", arg);
   else if(cmd == CMD_EFORCE)
      sprintf(Gbuf1, " force%s", arg);
   else if(cmd == CMD_ESTATUS)
      sprintf(Gbuf1, " status%s", arg);
   else
      {
      send_to_char("&+CI don't understand which command your looking for, type: ems for help.\n", ch);
      return;
      }

   do_ems(ch, Gbuf1, cmd);

   return;
}


/* This routine takes a player's account information, and creates a line of text
   that is formatted to exact lengths, so everything lines up nice in a show command. */
char *EMS_Create_Output_Text(char *Line, int Record, char *Name, int PIN, char *Email, int Status)
{
   char Gbuf1[MAX_STRING_LENGTH], Gbuf2[MAX_STRING_LENGTH], tmp[16];
   int Count = 0, Loop = 0;


   /* First add in the Record Number field. */
   sprintf(Gbuf1, "%d", Record);
   Count = strlen(Gbuf1);
   if(Count == 1)
      sprintf(Line, "&n&+y#&+c0000%d&n&+y: ", Record);
   else if(Count == 2)
      sprintf(Line, "&n&+y#&+c000%d&n&+y: ", Record);
   else if(Count == 3)
      sprintf(Line, "&n&+y#&+c00%d&n&+y: ", Record);
   else if(Count == 4)
      sprintf(Line, "&n&+y#&+c0%d&n&+y: ", Record);
   else if(Count == 5)
      sprintf(Line, "&n&+y#&+c%d&n&+y: ", Record);
   else
      sprintf(Line, "&n&+y#&+c ??? &n&+y: ");


   /* Process the Name field */
   sprintf(Gbuf2, "&+y&+g%s &n&+L", Name);
   Count = (15 - strlen(Name));
   strcat(Line, Gbuf2);

   sprintf(tmp, "-");
   for(Loop = 1; Loop <= Count; Loop++)
      strcat(Line, tmp);


   /* Process the Pin Number field */
   sprintf(Gbuf1, "%d", PIN);
   Count = strlen(Gbuf1);
   if(Count == 1)
      sprintf(Gbuf2, "&n&+y PIN: &+c000%d&n&+y, ", PIN);
   else if(Count == 2)
      sprintf(Gbuf2, "&n&+y PIN: &+c00%d&n&+y, ", PIN);
   else if(Count == 3)
      sprintf(Gbuf2, "&n&+y PIN: &+c0%d&n&+y, ", PIN);
   else if(Count == 4)
      sprintf(Gbuf2, "&n&+y PIN: &+c%d&n&+y, ", PIN);
   else
      sprintf(Gbuf2, "&n&+y PIN: &+c ?? &n&+y, ");
   strcat(Line, Gbuf2);


   /* Process the Status Field */
   sprintf(Gbuf1, "%s", GET_EMS_STATUS(Gbuf1, Status));
   sprintf(Gbuf2, "Status: &+c%s&+L ", Gbuf1);
   strcat(Line, Gbuf2);
   Count = (10 - strlen(Gbuf1));

   sprintf(tmp, "-");
   for(Loop = 0; Loop <= Count; Loop++)
      strcat(Line, tmp);


   /* Process the Email field */
   sprintf(Gbuf2, "&n&+y Email: &+c%s\n", Email);
   strcat(Line, Gbuf2);

   return(Line);
}


/* This little routine automatically disables EMS if there is a problem
   with the system. This is different from older code, instead of crashing
   the entire mud, we just turn EMS off and let people know whats up. */
void EMS_Auto_Disable(void)
{
   int i, tagNum = -1;
   char name[MAX_STRING_LENGTH], buf[MAX_STRING_LENGTH];

   sprintf(name, "ems");

   /* search for the tag in cctags[] in order to find proper cbit index */
   for(i = 0; cctags[i].name != NULL; i++)
      {
      if(!strcmp (cctags[i].name, name))
         {
         tagNum = cctags[i].type;
         break;
         }
      }

   /* If we cant turn off EMS, we have to crash right here. If EMS caused enough
      of a problem that we called the auto shutdown for EMS, and we can't shutdown
      EMS, then that qualifies as enough of a problem to drop the mud. */
   if(tagNum == -1)
      {
      sprintf(buf, "*** ERROR! Unable to turn off EMS through code control!\n");
      logit(LOG_STATUS, buf);
      dump_core();
      }

   REMOVE_CBIT (sets_code_control, tagNum);
   if(saveCodeControlBits () != TRUE)
      {
      sprintf (name, "&+R&-LPANIC!&N Failed to save the code control data!");
      logit(LOG_STATUS, buf);
      }

   return;
}


/* This function handles opening the EMS database, verifying it's content is good,
   and repairing/over-writing bad databases. */
FILE *EMS_Open_Database(FILE *db)
{
   char buf[MAX_STRING_LENGTH], data[8192];
   int Bad = 0, Records = 0;

   /* Make sure the database is there. If not, check the backup. */
   if(!(db = fopen(EMS_DATABASE, "r")))
      {
      sprintf(buf, "*** EMS ERROR! Cannot open primary EMS database (%s), trying backup..\n",
              EMS_DATABASE);
      logit(LOG_STATUS, buf);
      fprintf(stderr, "%s", buf);

      if(!(db = fopen(EMS_BACKUP_DATABASE, "r")))
         {
         sprintf(buf,
                 "*** EMS ERROR! Cannot open either of the EMS databases! (%s and %s), Disabling EMS!\n",
                 EMS_DATABASE, EMS_BACKUP_DATABASE);
         logit(LOG_STATUS, buf);
         fprintf(stderr, "%s", buf);

         EMS_Auto_Disable();
         return NULL;
         }
      else
         {
         sprintf(buf, "*** Backup database okay, over-writing primary with the good backup.\n");
         logit(LOG_STATUS, buf);
         fprintf(stderr, "%s", buf);

         unlink(EMS_DATABASE);
         rename(EMS_BACKUP_DATABASE, EMS_DATABASE);
         /* removed new_system(buf) which executed a meaningless shell command */
         }
      }


   /* Check the database for consistancy. */
   Bad = 0;
   fgets(data, 8192, db);
   if((data == NULL) || (feof(db)))
      Bad = 1;
   else
      {
      sscanf(data, "%s \n", buf);
      Records = atoi(buf);

      if((Records < 0) || (Records > MAX_RECORDS))
         Bad = 1;
      }

   if(Bad == 1)
      {
      sprintf(buf, "*** EMS ERROR! EMS database is corrupt! (%s), Checking Backup..\n",
              EMS_DATABASE);
      logit(LOG_STATUS, buf);
      fprintf(stderr, "%s", buf);

      /* The primary is no good, wax it. */
      fclose(db);
      unlink(EMS_DATABASE);

      if(!(db = fopen(EMS_BACKUP_DATABASE, "r")))
         {
         sprintf(buf,
                 "*** EMS ERROR! Backup EMS database is Corrupt! (%s and %s).\n",
                 EMS_DATABASE, EMS_BACKUP_DATABASE);
         logit(LOG_STATUS, buf);
         fprintf(stderr, buf);
         sprintf(buf, "*** EMS FATAL: Both databases are bad, Disabling EMS!\n");
         logit(LOG_STATUS, buf);
         fprintf(stderr, "%s", buf);

         unlink(EMS_BACKUP_DATABASE);
         EMS_Auto_Disable();
         return NULL;
         }

      /* Okay make sure the backup's data is consistant. */
      Bad = 0;
      fgets(data, 8192, db);
      if((data == NULL) || (feof(db)))
         Bad = 1;
      else
         {
         sscanf(data, "%s \n", buf);
         Records = atoi(buf);

         if((Records < 0) || (Records > MAX_RECORDS))
            Bad = 1;
         }

      if(Bad == 1)
         {
         sprintf(buf, "*** EMS ERROR! EMS backup database is corrupt! (%s)\n",
                 EMS_BACKUP_DATABASE);
         logit(LOG_STATUS, buf);
         fprintf(stderr, "%s", buf);
         sprintf(buf, "*** EMS FATAL: Both databases are bad, Disabling EMS!\n");
         logit(LOG_STATUS, buf);
         fprintf(stderr, buf);

         fclose(db);
         unlink(EMS_BACKUP_DATABASE);
         EMS_Auto_Disable();
         return NULL;
         }
      else
         {
         sprintf(buf, "*** Backup database okay, over-writing primary with the good backup.\n");
         logit(LOG_STATUS, buf);
         fprintf(stderr, "%s", buf);

         /* Good, the backup is okay, fix the primary */
         unlink(EMS_DATABASE);
         rename(EMS_BACKUP_DATABASE, EMS_DATABASE);
         /* removed new_system(buf) which executed a meaningless shell command */
         }
      }

   rewind(db);
   return(db);
}


/* This little routine checks our pre-defined bans for free-sites. */
int Check_EMS_Bans(char *site)
{
   FILE *db;
   char buf[MAX_STRING_LENGTH], data[8192];
   int Bad = 0;

   if(!(db = fopen(EMS_BANS, "r")))
      {
      sprintf(buf, "Error! Cannot open EMS bans file! Ignoring check..\n");
      emslog(51, buf);
      logit(LOG_STATUS, buf);

      return NO;
      }

   for(;;)
      {
      fgets(data, 8192, db);

      if((data == NULL) || (feof(db)))
         break;

      sscanf(data, "%s \n", buf);

      if(!strcmp (site, buf))
         {
         Bad = 1;
         break;
         }
      }

   fclose(db);

   if(Bad == 0)
      return NO;
   else
      return YES;

}


void EMS_Setup_Newbie(P_desc d, int Mode)
{
   int cost, r_room;
   int time_gone = 0, hit_g, move_g, heal_time;

#ifdef PCS_USE_MANA
   int mana_g;
#endif
   char Gbuf1[MAX_STRING_LENGTH];

   if(GET_LEVEL(d->character))
      {
      if(d->rtype == 1)
         cost = restoreItemsOnly(d->character, 100);
      else if(d->rtype == 6)
         cost = restoreItemsOnly(d->character, 0);
      else if(d->rtype == 3)
         cost = restoreItemsOnly(d->character, 100);
      else if(d->rtype == 5)
         cost = restoreItemsOnly(d->character, 200);
      else if(d->rtype == 4)
         {
         cost = 0;
         if((GET_LEVEL(d->character) < 3) &&
            ((GET_MONEY(d->character) + GET_BALANCE(d->character)) < 1000))
            load_obj_to_newbies(d->character);
         }
      else
         {
         cost = 0;
         if((GET_LEVEL(d->character) < 3) &&
            ((GET_MONEY(d->character) + GET_BALANCE(d->character)) < 1000))
            load_obj_to_newbies(d->character);
         }

      if(cost == -1)
         {
         if(GET_LEVEL(d->character) < 3)
            load_obj_to_newbies(d->character);
         }

      /* to avoid problems if game is shutdown/crashed while they are in 'camp'
         mode, kill the affect if it's active here. */

      if(IS_AFFECTED(d->character, AFF_CAMPING))
         affect_from_char(d->character, SKILL_CAMP);
      if(IS_AFFECTED(d->character, AFF_CASTING))
         REMOVE_CBIT(d->character->specials.affects, AFF_CASTING);
      if(IS_AFFECTED(d->character, AFF_SCRIBING))
         REMOVE_CBIT(d->character->specials.affects, AFF_SCRIBING);

      /* nuke berserks in case of crash, etc */
      affect_from_char(d->character, SKILL_BERSERK);

      /* remove any morph flag that might be leftover */
      REMOVE_CBIT(d->character->only.pc->pcact, PLR_MORPH);

      /* characters heal at various rates while not logged in, highest rate is
         while rented at an Inn, lowest is linkdead rent. */

      /* time_gone is how many ticks (currently real minutes) they have been out
         of the game. */
      time_gone = (time(0) - d->character->player.time.saved) / SECS_PER_MUD_HOUR;
      heal_time = MAX(0, (time_gone - 120));

      if(d->rtype == 1)          /* crashsave */
         SET_POS(d->character, POS_SITTING + STAT_RESTING);
      if(d->rtype == 2)          /* quit */
         SET_POS(d->character, POS_PRONE + STAT_DYING);
      if(d->rtype == 3)          /* rented at inn */
         SET_POS(d->character, POS_PRONE + STAT_SLEEPING);
      if(d->rtype == 4)          /* died */
         SET_POS(d->character, POS_PRONE + STAT_DYING);
      if(d->rtype == 5)          /* linkdead */
         SET_POS(d->character, POS_STANDING + STAT_INCAP);
      if(d->rtype == 6)          /* camped */
         SET_POS(d->character, POS_SITTING + STAT_RESTING);

      hit_g =
      BOUNDED(0,
              hit_regen(d->character) * heal_time,
              MAX(0, (GET_MAX_HIT(d->character) - GET_HIT(d->character))));
#ifdef PCS_USE_MANA
      mana_g =
      BOUNDED(0,
              mana_regen(d->character) * heal_time,
              MAX(0, (GET_MAX_MANA(d->character) - GET_MANA(d->character))));
#endif
      move_g =
      BOUNDED(0,
              move_regen(d->character) * heal_time,
              MAX(0, (GET_MAX_MOVE(d->character) - GET_MOVE(d->character))));

      SET_POS(d->character, POS_STANDING + STAT_NORMAL);

      GET_HIT(d->character) = MAX(0, GET_HIT(d->character) + hit_g);

#ifdef PCS_USE_MANA
      GET_MANA(d->character) = MAX(GET_MANA(d->character), GET_MANA(d->character) + mana_g);
#endif

      GET_MOVE(d->character) = MAX(GET_MOVE(d->character), GET_MOVE(d->character) + move_g);
      }
   /* don't do any of above for new chars */

   d->character->desc = d;

   /* okay.. first make sure a persons birthplace isn't
      outcast/invader.  Then, if their home is an outcast/invader area,
      change their home to their birthplace. */

   r_room = real_room(GET_BIRTHPLACE(d->character));
   if((r_room != NOWHERE) &&
      (PC_TOWN_JUSTICE_FLAGS(d->character, zone_table[world[r_room].zone].hometown) ==
       JUSTICE_IS_OUTCAST))
      {
      /* okay.. they are outcast in their birthplace.. FIX IT! */
      /* whee!  kludge time! */
      if(PC_TOWN_JUSTICE_FLAGS(d->character, HOME_BLOODSTONE) != JUSTICE_IS_OUTCAST)
         GET_BIRTHPLACE(d->character) = 91110;
      else
         GET_BIRTHPLACE(d->character) = RACE_EVIL(d->character) ? 0 : 0;
      }

   /* now check their home room */

   r_room = real_room(GET_HOME(d->character));
   if((r_room != NOWHERE) &&
      (PC_TOWN_JUSTICE_FLAGS(d->character, zone_table[world[r_room].zone].hometown) ==
       JUSTICE_IS_OUTCAST))
      GET_HOME(d->character) = GET_BIRTHPLACE(d->character);

   if(((d->rtype == 2) && (GET_LEVEL(d->character) < 2)) || (d->rtype == 4))
      r_room = real_room(GET_BIRTHPLACE(d->character));
   else if(d->rtype == 1)
      r_room = real_room(GET_HOME(d->character));
   else
      r_room = d->character->specials.was_in_room;

   if(r_room == NOWHERE)
      {
      if(GET_HOME(d->character))
         r_room = real_room(GET_HOME(d->character));
      else
         r_room = real_room(GET_BIRTHPLACE(d->character));

      if(r_room == NOWHERE)
         {
         if(IS_TRUSTED(d->character))
            {
            r_room = real_room(1200);
            }
         else
            {
            r_room = real_room(3001);
            }
         }
      if(r_room == NOWHERE)
         r_room = 0;
      }

   char_to_room(d->character, r_room, -2);
   affect_total(d->character);
   overmem_nuke(d->character, FALSE, TRUE);

   if((d->rtype == 1) || (d->rtype == 6) || (d->rtype == 3) || (d->rtype == 5))
      if(Mode == 0)
         if(cartfile_exists(d->character))
            restoreCart(d->character);

#ifdef EVENT_SAVING
   restoreCharacterEventsOnly(d->character);
#endif

#ifdef PROC_SAVING
   restoreCharacterProcsOnly(d->character);
#endif

   if(GET_MANA(d->character) != GET_MAX_MANA(d->character))
      StartRegen(d->character, EVENT_MANA_REGEN);
   if(GET_HIT(d->character) != GET_MAX_HIT(d->character))
      StartRegen(d->character, EVENT_HIT_REGEN);
   if(GET_MOVE(d->character) != GET_MAX_MOVE(d->character))
      StartRegen(d->character, EVENT_MOVE_REGEN);

   PC_count++;
   if(PC_count > max_users_playing)
      max_users_playing = PC_count;

   if(IS_TRUSTED(d->character))
      {
      d->character->only.pc->wiz_invis = GET_LEVEL(d->character) - 1;
      do_vis(d->character, 0, -4);        /* remind them of vis level */
      }
   if(d->rtype == 4)
      {
      GET_COND(d->character, FULL) = 24;
      GET_COND(d->character, THIRST) = 24;
      GET_COND(d->character, DRUNK) = 0;
      GET_HIT(d->character) = 1;
      }

   if(!GET_LEVEL(d->character))
      {
      do_start(d->character);
      load_obj_to_newbies(d->character);
      }

   *Gbuf1 = '\0';

   update_last_login(d);
   writeCharacter(d->character, 1, NOWHERE);

   return;

}


/* Command to display the Sojparse log */
void EMS_Sojparse_Log(P_char ch, char *arg, int cmd)
{
   FILE *elog;
   char Output[MAX_STRING_LENGTH], data[8192], buf[MAX_STRING_LENGTH];
   char a[8192], b[8192], c[2], d[2], tmp[2];
   char e[8192], f[8192], g[2], h[2];
   int Count = 0, Marker = 0, Found = 0;

   if((elog = fopen(SOJPARSE_LOG, "r")) != NULL)
      {
      strcpy(Output, "");

      if((!arg) || (strlen(arg) < 1))
         {
         send_to_char("&+CDisplaying the most recent 1024 lines of the Sojparse log..\n", ch);

         for(Count = 1; Count <= 1024; Count++)
            {
            fgets(data, 8192, elog);
            if((data == NULL) || (feof(elog)))
               break;
            if(strlen(data) > 1)
               strcat(Output, data);
            }
         }
      else
         {
         arg = one_argument(arg, buf);

         strcpy(a, "");
         strcpy(b, "");
         strcpy(c, "");
         strcpy(d, "");
         strcpy(e, "");
         strcpy(f, "");
         strcpy(g, "");
         strcpy(h, "");
         sprintf(a, "%c", buf[0]);
         sprintf(b, "%c", buf[1]);
         sprintf(c, "%c", buf[2]);
         sprintf(d, "%c", buf[3]);
         sprintf(e, "%c", buf[4]);
         sprintf(f, "%c", buf[5]);
         sprintf(g, "%c", buf[6]);
         sprintf(h, "%c", buf[7]);

         Marker = 8;
         if(strlen(h) < 1)
            Marker = 7;
         if(strlen(g) < 1)
            Marker = 6;
         if(strlen(f) < 1)
            Marker = 5;
         if(strlen(e) < 1)
            Marker = 4;
         if(strlen(d) < 1)
            Marker = 3;
         if(strlen(c) < 1)
            Marker = 2;
         if(strlen(b) < 1)
            Marker = 1;
         if(strlen(a) < 1)
            {
            send_to_char("What are you looking for?\n", ch);
            return;
            }

         for(;;)
            {
            fgets(data, 8192, elog);
            if((data == NULL) || (feof(elog)))
               break;

            Found = 0;
            for(Count = 0; Count <= strlen(data); Count++)
               {
               sprintf(tmp, "%c", data[Count]);
               if(!strcmp(tmp, a))
                  {
                  if(Marker == 1)
                     {
                     Found = 1;
                     break;
                     }

                  sprintf(tmp, "%c", data[Count+1]);
                  if(!strcmp(tmp, b))
                     {
                     if(Marker == 2)
                        {
                        Found = 1;
                        break;
                        }

                     sprintf(tmp, "%c", data[Count+2]);
                     if(!strcmp(tmp, c))
                        {
                        if(Marker == 3)
                           {
                           Found = 1;
                           break;
                           }

                        sprintf(tmp, "%c", data[Count+3]);
                        if(!strcmp (tmp, d))
                           {
                           if(Marker == 4)
                              {
                              Found = 1;
                              break;
                              }

                           sprintf(tmp, "%c", data[Count+4]);
                           if(!strcmp (tmp, e))
                              {
                              if(Marker == 5)
                                 {
                                 Found = 1;
                                 break;
                                 }

                              sprintf(tmp, "%c", data[Count+5]);
                              if(!strcmp (tmp, f))
                                 {
                                 if(Marker == 6)
                                    {
                                    Found = 1;
                                    break;
                                    }

                                 sprintf(tmp, "%c", data[Count+6]);
                                 if(!strcmp (tmp, g))
                                    {
                                    if(Marker == 7)
                                       {
                                       Found = 1;
                                       break;
                                       }

                                    sprintf(tmp, "%c", data[Count+7]);
                                    if(!strcmp (tmp, h))
                                       {
                                       if(Marker == 8)
                                          {
                                          Found = 1;
                                          break;
                                          }

                                       }
                                    }
                                 }
                              }
                           }
                        }
                     }
                  }
               }
            if(Found == 1)
               {
               if(strlen(Output) > MAX_STRING_LENGTH - 100)
                  {
                  strcat(Output, "&+rList too long to display all matches found.\n&N");
                  break;
                  }
               strcat(Output, data);
               }
            }
         }

      fclose(elog);
      page_string(ch->desc, Output, 1);
      return;
      }
   else
      {
      send_to_char("&+yNo entries in the Sojparse log to view.\n", ch);
      return;
      }
}




FILE *Open_Email_Database(FILE *edb)
{
   FILE *temp;
   char buf[8192], data[8192], ack[8192];


   /* First open the spam database */
   if(!(edb = fopen(EMAIL_DATABASE, "r")))
      {
      sprintf(buf, "EMS5000: Unable to open email database. (%s)", EMAIL_DATABASE);
      logit(LOG_STATUS, buf);
      fprintf(stderr, "%s", buf);

      /* Okay, try to create one. */
      remove(EMAIL_DATABASE);

      if(!(temp = fopen(EMAIL_DATABASE, "w")))
         {
         sprintf(buf, "EMS5003: Unable to create new email database. (%s)\n", EMAIL_DATABASE);
         logit(LOG_STATUS, buf);
         fprintf(stderr, "%s", buf);
         return NULL;
         }
      else
         {
         /* EDB INDEX VARIABLES: DateLastMod, TotalEmailsKnown,
                                 TotalMessagesSent, MessagesSentToday,
                                 TotalMessagesReceived, MessagesReceivedToday,
                                 TotalSpammerBlocks, SpammerBlocksToday          */
         sprintf(ack, "%s 0 0 0 0 0 0 0 0 0 \n", GetTimeForFileNames(buf));
         fputs(ack, temp);
         fclose(temp);
#ifdef _WIN32
         Sleep(1000);
#else
         sleep(1);
#endif

         if(!(edb = fopen(EMAIL_DATABASE, "r")))
            {
            sprintf(buf, "EMS5004: Unable to open re-created email database. (%s)\n", EMAIL_DATABASE);
            logit(LOG_STATUS, buf);
            fprintf(stderr, "%s", buf);
            }
         }
      }

   /* Pull out the index marker. */
   fgets(data, 8192, edb);
   if((data == NULL) || (feof(edb)))
      {
      sprintf(buf, "EMS5001: Unable to read the opened email database! (%s)\n", EMAIL_DATABASE);
      logit(LOG_STATUS, buf);
      fprintf(stderr, "%s", buf);
      fclose(edb);
      return NULL;
      }

   rewind(edb);
   return(edb);
}




/* This version of GetTime() returns the date as a numberical value
   that is always incrementing, even past year 2,000, so that it may
   be used in comparisons between time stamps easily. --MIAX 4/98    */

char *GetTimeForFileNames(char *ack)
{
   char tmp1[32], tmp2[32], tmp3[32], tmp4[32], tmp5[32];
   char ch1[2], ch2[2], ch3[2], ch4[2], ch5[2], ch6[2];
   char buf[128], lime[64];
   struct tm *ptr;
   time_t lt;

   lt = time(NULL);
   ptr = localtime(&lt);

   sscanf(asctime(ptr), "%s %s %s %s %s \n",
          tmp1, tmp2, tmp3, tmp4, tmp5);

   /* First make our timestamp id. */
   sprintf(ch1, "%c", tmp4[0]);
   sprintf(ch2, "%c", tmp4[1]);
   sprintf(ch3, "%c", tmp4[3]);
   sprintf(ch4, "%c", tmp4[4]);
   sprintf(ch5, "%c", tmp4[6]);
   sprintf(ch6, "%c", tmp4[7]);

   strcpy(buf, "");
   sprintf(buf, "%s%s%s%s%s%s", ch1, ch2, ch3, ch4, ch5, ch6);

   if(!strcmp (tmp2, "Jan"))
      sprintf(lime, "01");
   else if(!strcmp (tmp2, "Feb"))
      sprintf(lime, "02");
   else if(!strcmp (tmp2, "Mar"))
      sprintf(lime, "03");
   else if(!strcmp (tmp2, "Apr"))
      sprintf(lime, "04");
   else if(!strcmp (tmp2, "May"))
      sprintf(lime, "05");
   else if(!strcmp (tmp2, "Jun"))
      sprintf(lime, "06");
   else if(!strcmp (tmp2, "Jul"))
      sprintf(lime, "07");
   else if(!strcmp (tmp2, "Aug"))
      sprintf(lime, "08");
   else if(!strcmp (tmp2, "Sep"))
      sprintf(lime, "09");
   else if(!strcmp (tmp2, "Oct"))
      sprintf(lime, "10");
   else if(!strcmp (tmp2, "Nov"))
      sprintf(lime, "11");
   else if(!strcmp (tmp2, "Dec"))
      sprintf(lime, "12");

   strcpy(ack, "");
   sprintf(ack, "%s%s%s%s", tmp5, lime, tmp3, buf);

   return(ack);
}

/* A reversal of the FileName time for nice output. o_o */
char *GetTimeFromFileNameTime(char *ack)
{
   char ch1[2], ch2[2], ch3[2], ch4[2], ch5[2], ch6[2];
   char ch7[2], ch8[2], ch9[2], ch10[2], ch11[2], ch12[2];
   char ch13[2], ch14[2], year[64], month[64], day[64], hour[64];
   char datime[8192];


   strcpy(datime, ack);

   /* First make our timestamp id. */
   sprintf(ch1, "%c", datime[0]);
   sprintf(ch2, "%c", datime[1]);
   sprintf(ch3, "%c", datime[2]);
   sprintf(ch4, "%c", datime[3]);
   sprintf(ch5, "%c", datime[4]);
   sprintf(ch6, "%c", datime[5]);
   sprintf(ch7, "%c", datime[6]);
   sprintf(ch8, "%c", datime[7]);
   sprintf(ch9, "%c", datime[8]);
   sprintf(ch10, "%c", datime[9]);
   sprintf(ch11, "%c", datime[10]);
   sprintf(ch12, "%c", datime[11]);
   sprintf(ch13, "%c", datime[12]);
   sprintf(ch14, "%c", datime[13]);

   sprintf(month, "%s%s", ch5, ch6);
   sprintf(year, "%s%s%s%s", ch1, ch2, ch3, ch4);
   sprintf(day, "%s%s", ch7, ch8);
   sprintf(hour, "%s%s:%s%s", ch9, ch10, ch11, ch12);

   if(!strcmp (month, "01"))
      sprintf(month, "January");
   else if(!strcmp (month, "02"))
      sprintf(month, "February");
   else if(!strcmp (month, "03"))
      sprintf(month, "March");
   else if(!strcmp (month, "04"))
      sprintf(month, "April");
   else if(!strcmp (month, "05"))
      sprintf(month, "May");
   else if(!strcmp (month, "06"))
      sprintf(month, "Jun");
   else if(!strcmp (month, "07"))
      sprintf(month, "July");
   else if(!strcmp (month, "08"))
      sprintf(month, "August");
   else if(!strcmp (month, "09"))
      sprintf(month, "September");
   else if(!strcmp (month, "10"))
      sprintf(month, "October");
   else if(!strcmp (month, "11"))
      sprintf(month, "November");
   else if(!strcmp (month, "12"))
      sprintf(month, "December");

   sprintf(ack, "%s on %s %s, %s", hour, month, day, year);

   return(ack);
}




/* Auto reset of application, usually when a player has a bad Email address. */
void do_auto_reset(P_char ch, char *Name)
{
   char buf[8192], reason[8192];
   int Pointer = 0, Omg = 0;

   if((Pointer = EMS_Return_Account_Pointer(Name)) == 0)
      {
      sprintf(buf, "&+cCould not locate account for auto-reset! &+Lo_O\n");
      emslog(51, buf);
      return;
      }

   EMSD[Pointer].Status = EMS_START;

   Omg = EMS_Account_To_Struct(EMSD[Pointer].Name, EMSD[Pointer].PIN,
                               EMSD[Pointer].Email, EMSD[Pointer].Status);
   EMS_Database_Modify(EMS_MODIFY, EMSD[Omg].Name, EMSD[Omg].PIN,
                       EMSD[Omg].Email, EMSD[Omg].Status);

   sprintf(buf, "NONE");;
   sprintf(reason, "Auto-reset of account by Outcast MUD");
   EMS_Return_Notice(EMSD[Omg].Name, EMSD[Omg].Email, reason, EMS_START, buf);

   sprintf(buf,"&+yAuto Reset of EMS player account: &+c%s&+y, (&+c%s&+y) Comment: &+c%s",
           EMSD[Omg].Name, EMSD[Omg].Email, reason);
   logit(LOG_EMAILREG, buf);
   emslog(51, buf);

   /* Add this into player's record. */
   sprintf(buf, " raa %s 'Auto-reset of player EMS account: %s (%s)' %s\n",
           EMSD[Omg].Name, EMSD[Omg].Name, EMSD[Omg].Email, reason);
   do_ems(ch, buf, CMD_ERESET);
}



/* Email database lookup and display - omgwtf */

/* Email DateLastMod TotalMessagesSent MessagesSentToday TotalMessagesReceived
    MessagesReceivedToday TotalSpammerBlocks SpammerBlocksToday */

/* Character record field:
     Email, DateLastMod,
     TotalMessagesSent, MessagesSentToday, TotalMessagesReceived
     MessagesReceivedToday, TotalSpammerBlocks, SpammerBlocksToday
     (Then 15 variables representing the different EMS status modes) */

void Email_Database_Lookup(P_char ch, char *Query, int Mode)
{
   FILE *db;
   char data[MAX_STRING_LENGTH], last_email[MAX_STRING_LENGTH];
   char Output[MAX_STRING_LENGTH], Gbuf1[MAX_STRING_LENGTH];
   char Result[MAX_STRING_LENGTH], junk[MAX_STRING_LENGTH];
   char date[MAX_STRING_LENGTH];
   char a[8192], b[8192], c[8192], d[8192], e[8192];
   char f[8192], g[8192], h[8192], i[8192], j[8192], k[8192];
   int List_Types[8], List_Len[8], Listed = 0;
   unsigned int SojP_Types[8], SojP_Len[8];
   int Count = 0, Spaces = 0, Entries = 0;


   /* Prep variables, open database */
   for(Count = 0; Count <= 8; Count++)
      {
      List_Types[Count] = 0;
      List_Len[Count] = 0;
      SojP_Types[Count] = 0;
      SojP_Len[Count] = 0;
      }
   strcpy(Result, "");
   strcpy(data, "");
   strcpy(Output, "");

   if(!(db = Open_Email_Database(db)))
      {
      send_to_char("&+C\nERROR! Could not open Email database! Seek help. When? NOW!\n", ch);
      return;
      }

   if(Mode == LOOKUP_NAME)
      {
      sprintf(Query, "%s", EMS_NAME(Query));
      sprintf(Output, "\n&+yStatistical data from &+mSojparse &+yfor Player: &+C%s&n\n\n", Query);
      }
   else
      {
      sprintf(Output, "\n&+yStatistical data from &+mSojparse &+yfor Email Address: &+C%s&n\n\n", Query);
      }

   /* loop through file, find target Name or Email address, sum up
      data from all instances of target into one set of values. */
   for(;;)
      {
      if(Listed == 1)
         break;

      fgets(data, 8192, db);
      if((data == NULL) || (feof(db)))
         break;

      /* Empty the variables */
      strcpy(a, "");
      strcpy(b, "");
      strcpy(c, "");
      strcpy(d, "");
      strcpy(e, "");
      strcpy(f, "");
      strcpy(g, "");
      strcpy(h, "");
      strcpy(i, "");
      strcpy(j, "");
      strcpy(k, "");

      /* Parse the line */
      sscanf(data, "%s %s %s %s %s %s %s %s %s %s %s \n", a, b, c, d, e, f, g, h, i, j, k);
      if(!d)
         sprintf(d, "0");
      if(!e)
         sprintf(e, "0");
      if(!f)
         sprintf(f, "0");
      if(!g)
         sprintf(g, "0");
      if(!h)
         sprintf(h, "0");
      if(!i)
         sprintf(i, "0");
      if(!j)
         sprintf(j, "0");
      if(!k)
         sprintf(k, "0");

      /* Interpret the data */
      /* If were looking for a player, hang onto the last Email address read from the database. */
      if((!strcmp (a, "Email:")) && (Mode == LOOKUP_NAME))
         {
         strcpy(last_email, "");
         strcpy(last_email, data);
         continue;
         }

      /* If were looking for a player, and we find it, parse a line for it, and the
         Email address under which it was found. */
      else if((!strcmp(a, "Player:")) && (!strcmp (b, Query)) && (Mode == LOOKUP_NAME))
         {
         Entries++;
         SojP_Types[1] += atol(d);
         SojP_Types[2] += atol(e);
         SojP_Types[3] += atol(f);
         SojP_Types[4] += atol(g);
         SojP_Types[5] += atol(h);
         SojP_Types[6] += atol(i);
         SojP_Types[7] += atol(j);
         SojP_Types[8] += atol(k);

         strcpy(a, "");
         strcpy(date, "");
         strcpy(junk, "");
         sscanf(last_email, "%8191s %8191s %8191s %8191s \n", junk, a, date, junk);
         if(!date)
            sprintf(date, "Unknown");
         if(!a)
            sprintf(a, "Unknown");

         sprintf(Gbuf1, "&+gMatch found under Email address: &+C%s&n&+g, Last seen on: &+c%s\n",
                 a, GetTimeFromFileNameTime(date));
         strcat(Result, Gbuf1);
         sprintf(Gbuf1, "&+y-> Player Stats: ");
         strcat(Result, Gbuf1);
         sprintf(Gbuf1, "&+ySent: &+c%s&+y/&+c%s&+y, Received: &+c%s&+y/&+c%s&+y, ", d, e, f, g);
         strcat(Result, Gbuf1);
         sprintf(Gbuf1, "&+yControl: &+c%s&+y/&+c%s&+y, Blocked: &+c%s&+y/&+c%s\n", h, i, j, k);
         strcat(Result, Gbuf1);

         sscanf(last_email, "%8191s %8191s %8191s %8191s %8191s %8191s %8191s %8191s %8191s %8191s %8191s \n", a, b, c, d, e, f, g, h, i, j, k);
         if(!d)
            sprintf(d, "U");
         if(!e)
            sprintf(e, "U");
         if(!f)
            sprintf(f, "U");
         if(!g)
            sprintf(g, "U");
         if(!h)
            sprintf(h, "U");
         if(!i)
            sprintf(i, "U");
         if(!j)
            sprintf(j, "U");
         if(!k)
            sprintf(k, "U");

         sprintf(Gbuf1, "&+y->  Email Stats: ");
         strcat(Result, Gbuf1);
         sprintf(Gbuf1, "&+ySent: &+c%s&+y/&+c%s&+y, Received: &+c%s&+y/&+c%s&+y, ", d, e, f, g);
         strcat(Result, Gbuf1);
         sprintf(Gbuf1, "&+yControl: &+c%s&+y/&+c%s&+y, Blocked: &+c%s&+y/&+c%s\n", h, i, j, k);
         strcat(Result, Gbuf1);

         continue;
         }

      /* If were looking for an Email, and we find it, parse a line of data for it. */
      else if((!strcmp (a, "Email:")) && (!strcmp (b, Query)) && (Mode == LOOKUP_EMAIL))
         {
         SojP_Types[1] = atol(d);
         SojP_Types[2] = atol(e);
         SojP_Types[3] = atol(f);
         SojP_Types[4] = atol(g);
         SojP_Types[5] = atol(h);
         SojP_Types[6] = atol(i);
         SojP_Types[7] = atol(j);
         SojP_Types[8] = atol(k);
         strcpy(date, c);

         for(;;)
            {
            fgets(data, 8192, db);
            if((data == NULL) || (feof(db)))
               break;

            strcpy(a, "");
            strcpy(b, "");
            strcpy(c, "");
            strcpy(d, "");
            strcpy(e, "");
            strcpy(f, "");
            strcpy(g, "");
            strcpy(h, "");
            strcpy(i, "");
            strcpy(j, "");
            strcpy(k, "");
            sscanf(data, "%8191s %8191s %8191s %8191s %8191s %8191s %8191s %8191s %8191s %8191s %8191s \n", a, b, c, d, e, f, g, h, i, j, k);

            if(!strcmp (a, "Email:"))
               break;

            sprintf(Gbuf1, "&+yPlayer that has this Email: &+C%s&n&+y, Last seen on: &+c%s\n",
                    b, GetTimeFromFileNameTime(c));
            strcat(Result, Gbuf1);
            sprintf(Gbuf1, "&+y-> Player Stats: ");
            strcat(Result, Gbuf1);
            sprintf(Gbuf1, "&+ySent: &+c%s&+y/&+c%s&+y, Received: &+c%s&+y/&+c%s&+y, ", d, e, f, g);
            strcat(Result, Gbuf1);
            sprintf(Gbuf1, "&+yControl: &+c%s&+y/&+c%s&+y, Blocked: &+c%s&+y/&+c%s\n", h, i, j, k);
            strcat(Result, Gbuf1);
            }
         Listed = 1;
         break;
         }
      }

   fclose(db);

   if((Mode == LOOKUP_EMAIL) && (Listed == NO))
      {
      send_to_char("&+cNo entries found under that Email address in the Email database..\n", ch);
      return;
      }

   /* Now determine lengths for the master summation variable parse below */
   for(Count = 1; Count <= 8; Count++)
      {
      if(SojP_Types[Count] <= 9)
         SojP_Len[Count] = 1;
      else if(SojP_Types[Count] <= 99)
         SojP_Len[Count] = 2;
      else if(SojP_Types[Count] <= 999)
         SojP_Len[Count] = 3;
      else if(SojP_Types[Count] <= 9999)
         SojP_Len[Count] = 4;
      else if(SojP_Types[Count] <= 99999)
         SojP_Len[Count] = 5;
      else if(SojP_Types[Count] <= 999999)
         SojP_Len[Count] = 6;
      else if(SojP_Types[Count] <= 9999999)
         SojP_Len[Count] = 7;
      else if(SojP_Types[Count] <= 99999999)
         SojP_Len[Count] = 8;
      else if(SojP_Types[Count] <= 999999999)
         SojP_Len[Count] = 9;

      if(List_Len[Count] == 0)
         List_Len[Count] = 1;
      }

   /* Create the output for the summation variables. */
   strcpy(data, "");
   Spaces = (5 - SojP_Len[1]);
   for(Count = 0; Count <= Spaces; Count++)
      strcat(data, " ");
   sprintf(Gbuf1, "&+y   Total Messages Sent:     &+C%d%s &n&+yMessages Sent Out Today: &+C%d\n",
           SojP_Types[1], data, SojP_Types[1]);
   strcat(Output, Gbuf1);

   strcpy(data, "");
   Spaces = (5 - SojP_Len[3]);
   for(Count = 0; Count <= Spaces; Count++)
      strcat(data, " ");
   sprintf(Gbuf1, "&+y   Total Messages Received: &+C%d%s &n&+yMessages Received Today: &+C%d\n",
           SojP_Types[3], data, SojP_Types[4]);
   strcat(Output, Gbuf1);

   strcpy(data, "");
   Spaces = (5 - SojP_Len[5]);
   for(Count = 0; Count <= Spaces; Count++)
      strcat(data, " ");
   sprintf(Gbuf1, "&+y   Total Spammers Blocked:  &+C%d%s &n&+ySpammers Blocked Today:  &+C%d\n",
           SojP_Types[5], data, SojP_Types[6]);
   strcat(Output, Gbuf1);

   strcpy(data, "");
   Spaces = (5 - SojP_Len[7]);
   for(Count = 0; Count <= Spaces; Count++)
      strcat(data, " ");
   sprintf(Gbuf1, "&+y   Total Control Messages:  &+C%d%s &n&+yControl Messages Today:  &+C%d\n\n",
           SojP_Types[7], data, SojP_Types[8]);
   strcat(Output, Gbuf1);

   /* Combine the data for output */
   if(Mode == LOOKUP_EMAIL)
      {
      strcat(Output, Result);
      sprintf(Gbuf1, "\n&+y   Date of the last Email received from this address: &+c%s\n",
              GetTimeFromFileNameTime(date));
      strcat(Output, Gbuf1);
      }
   else if((Mode == LOOKUP_NAME) && (Entries == 0))
      {
      send_to_char("\n&+c   No entries found under that player name..\n", ch);
      return;
      }
   else if(Mode == LOOKUP_NAME)
      {
      strcat(Output, Result);
      sprintf(Gbuf1, "\n&+yNumber Email addresses received from this player: &+C%d\n",
              Entries);
      strcat(Output, Gbuf1);
      }

   send_to_char(Output, ch);

   return;
}


int EMS_Lookup_Player_Level(char *arg)
{
   P_char t_ch;
   int Level;

   t_ch = GetNewChar (NEW_PC);
   t_ch->only.pc->aggressive = -1;

   skip_spaces(&arg);
   if((restoreCharOnly (t_ch, arg) < 0) || !t_ch)
      {
      RemoveFromCharList (t_ch);
      free_char (t_ch);
      return -1;
      }

   Level = GET_LEVEL(t_ch);

   RemoveFromCharList (t_ch);
   free_char (t_ch);

   return Level;
}


#if 0  /* commented out, was generating warnings, alth */
/* Based on Gond's board code, with some cleanup and improvements. */
void EMS_Write_Message(P_char ch, char *arg, int cmd)
{
   char buf[MAX_STRING_LENGTH];
   int MessageNumber = -1, Count = 0;
   struct writing_info *writing;


   if((!arg) || (!*arg) || (!ch) || (!ch->desc) || IS_NPC(ch) || (ch->in_room == NOWHERE))
      return;

   for(Count = 0; Count <= MAX_EMS_MESSAGES; Count++)
      {
      if(EMD[Count].Status == VACANT)
         {
         MessageNumber = Count;
         EMD[Count].Status = OCCUPIED;
         break;
         }
      }

   /* Sanity Checks */
   if(MessageNumber >= MAX_EMS_MESSAGES)
      {
      send_to_char("&+cThe EMS message buffer is full, try again later.\n", ch);
      return;
      }
   if(ch->only.pc->writing)
      {
      send_to_char("&+cSorry, you can only write one thing at a time!\n", ch);
      return;
      }

   /* skip blanks */
   for(; isspace(*arg); arg++);
   if(!*arg)
      {
      send_to_char("Usage:\n"
                   "write [board] <title>\n"
                   "   If you exclude the [board] argument, the first board in the room is assumed.\n"
                   "   The <title> argument is required for boards.\n", ch);
      return;
      }

   /* Must leave room for number, name, and date. */
   if(strlen(arg) > 55)
      {
      send_to_char("&+cMessage titles must be less than 56 characters long.\n", ch);
      return;
      }

   send_to_char("&+CWrite your message. Terminate with '@@'.\n\n", ch);
   act("&+c$n starts to write an EMS message, be quiet. o_o", TRUE, ch, 0, 0, TO_ROOM);

   ct = time(0);
   tmstr = asctime(localtime((long *)&ct));
   *(tmstr + strlen(tmstr) - 9) = '\0';  /* kill seconds and year */

   sprintf(buf, "[%s (%s)] %s", tmstr, GET_NAME(ch), arg);
   EMD[MessageNumber].Title = str_dup(buf);
   EMD[MessageNumber].Body = NULL;
   EMD[MessageNumber].Not_Saved = 1;
   EMD[MessageNumber].Author = ch;

   CREATE(writing, struct writing_info, 1);
   writing->what = WRT_EMS;
   writing->writer = ch;
   writing->text_start = &EMD[MessageNumber].Body;
   writing->max_length = MAX_MESSAGE_LENGTH;
   writing->board_idx = 0;
   writing->msg_nr = MessageNumber;

   ch->only.pc->writing = writing;

   /* note, no EVENT_STRINGING for board messages, just the
      writing_info struct, EVENT_STRINGING on a char indicates
    * that the CHARACTER is being strung, not that the character is stringing. */
}
#endif

