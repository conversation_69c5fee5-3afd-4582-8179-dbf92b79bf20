/* ***************************************************************************
 *  File: socials.c                                          Part of Outcast *
 *  Usage: Mob Socials                                                       *
 *  Copyright  1997  Outcast                                                   *
 *  Written by:  <PERSON>herog June 12th, 1997                                    *
 *************************************************************************** */

#include <stdio.h>
#include <string.h>
#include <ctype.h>
#include <stdarg.h>
#include <errno.h>

#include "comm.h"
#include "db.h"
#include "interp.h"
#include "prototypes.h"
#include "specs.include.h"
#include "skillrec.h"
#include "specs.prototypes.h"
#include "spells.h"
#include "structs.h"
#include "utils.h"
#include "events.h"
#include "path.h"

/* external variables */

extern P_index mob_index;
extern P_index obj_index;
extern P_room world;
extern int top_of_mobt;
extern const char *item_types[];
extern const char *command[];
extern const struct stat_data stat_factor[];
extern struct str_app_type str_app[];
extern struct zone_data *zone_table;
extern struct time_info_data time_info;
extern struct path_lookup_struct path_lookup[];
extern struct path_info_struct path_info[];
extern int total_paths;

struct commandStruct
   {
   int key;
   const char *cmd;
   int len;                   /* The length of the cmd string */
   };

const struct commandStruct soc_keys[] = {
   {SOC_PERIODIC,      "PERIODIC",   8},
   {SOC_TRIGGER,       "TRIGGER",    7},
   {SOC_TIMED,         "TIMED",      5},
   {SOC_LIST,          "LIST",       4},
   {SOC_PATH,          "PATH",       4},
   {-1,                "",           0}
};

const struct commandStruct soc_periodicKeys[] = {
   {SOC_FLAG,          "FLAG:",      5},
   {SOC_DELAY,         "DELAY:",     6},
   {SOC_CHANCE,        "CHANCE:",    7},
   {SOC_ACTION,        "ACTION:",    7},
   {SOC_DONE,          "DONE",       4},
   {-1,                "",           0}
};

const struct commandStruct soc_triggerKeys[] = {
   {SOC_FLAG,          "FLAG:",      5},
   {SOC_DELAY,         "DELAY:",     6},
   {SOC_CHANCE,        "CHANCE:",    7},
   {SOC_TRIGGER,       "TRIGGER:",   8},
   {SOC_ACTION,        "ACTION:",    7},
   {SOC_DONE,          "DONE",       4},
   {-1,                "",           0}
};

const struct commandStruct soc_listKeys[] = {
   {SOC_FLAG,          "FLAG:",      5},
   {SOC_DELAY,         "DELAY:",     6},
   {SOC_CHANCE,        "CHANCE:",    7},
   {SOC_ACTION,        "ACTION:",    7},
   {SOC_DONE,          "DONE",       4},
   {SOC_DONELIST,      "LISTDONE",   8},
   {-1,                "",           0}
};

const struct commandStruct soc_pathKeys[] = {
   {SOC_ID,            "ID:",        3},
   {SOC_TYPE,          "TYPE:",      5},
   {SOC_DELAY,         "DELAY:",     6},
   {SOC_ROOMS,         "ROOMS:",     6},
   {SOC_DIRS,          "DIRS:",      5},
   {SOC_DONE,          "DONE",       4},
   {-1,                "",           0}
};

const struct commandStruct soc_timedKeys[] = {
   {SOC_FLAG,          "FLAG:",      5},
   {SOC_DELAY,         "DELAY:",     6},
   {SOC_CHANCE,        "CHANCE:",    7},
   {SOC_HOUR,          "HOUR:",      5},
   {SOC_ACTION,        "ACTION:",    7},
   {SOC_DONE,          "DONE",       4},
   {-1,                "",           0}
};

/* Used only during bootup to keep track of what procs to assign to a mobile
 * Saves some time as we wont have to go thru the chained commands for each mob */


struct socials_head *socials_index[SOCIALS_INDEX_SIZE];
unsigned long line = 0;
unsigned long num_of_socials = 0;
unsigned long num_of_social_periodics = 0;
unsigned long num_of_social_triggers = 0;
unsigned long num_of_social_lists = 0;
unsigned long num_of_social_path = 0;
unsigned long num_of_social_timed = 0;
unsigned long num_of_social_errors = 0;


int vnum, rnum;
int save;
FILE *sf;

char sBuf[MAX_STRING_LENGTH];
char tBuf[MAX_STRING_LENGTH];
char sErr[MAX_STRING_LENGTH];

struct socData_periodic *socDP;
struct socList          *socDL;
struct socData_trigger  *socDR;
struct socData_path     *socDG;
struct socData_timed    *socDT;

/* temp array used to keep track what commands have been loaded for the mob..
 * used again when assigning the procs */
ubyte set_command_index[SOCIALS_INDEX_SIZE];


void boot_the_socials(void)
{
   int    i;
   char   cmd[256];


   if(!(sf = fopen(SOCIALS_FILE, "r")))
      {
      logit(LOG_BOOT, "Error in boot socials: Cannot open the world.soc file.\n");
      return;
      }



   num_of_socials = 0;

   bzero(&socials_index[0], (int) (sizeof(struct socials_head *) * SOCIALS_INDEX_SIZE));
   bzero(set_command_index, (int) (sizeof(ubyte) * SOCIALS_INDEX_SIZE));


   for(;;)
      {
      cmd[0] = 0;
      vnum   = 0;
      rnum   = 0;


      /* Get the MOB header plus vnum */
      if(!findNextMobHeader())
         {
         assign_the_socials();
         break;
         }

      sscanf(sBuf, "%s %d %s\n", cmd, &vnum, cmd);
      if(vnum < 0)
         {
         logSocialError("Invalid mob vnum for line: %s\n", sBuf);
         continue;
         }


      /* Check if its loaded this boot.. */
      if((rnum = real_mobile(vnum)) == -1)  /* mob not loaded.. skip it.. */
         continue;

      for(i = 0;;i++)
         {
         if(soc_keys[i].key == -1)
            {
            logSocialError("Invalid command, cannot find a match: %s\n", sBuf);
            break;
            }

         if(!str_cmp(cmd, soc_keys[i].cmd))
            {
            switch(soc_keys[i].key)
               {

               case SOC_PERIODIC:
                  loadPeriodic();
                  break;

               case SOC_TRIGGER:
                  loadTriggers();
                  break;

               case SOC_TIMED:
                  loadTimed();
                  break;

               case SOC_LIST:
                  loadPeriodicList();
                  break;

               case SOC_PATH:
                  loadPath();
                  break;

               default:
                  dump_core();
               }
            break;
            }
         }
      }

   /* get some stats for do_world */

   for(i = 0; i < SOCIALS_INDEX_SIZE; i++)
      {
      if(socials_index[i])
         {
         num_of_socials++;
         if(socials_index[i]->periodic)
            num_of_social_periodics++;
         if(socials_index[i]->trigger)
            num_of_social_triggers++;
         if(socials_index[i]->list)
            num_of_social_lists++;
         if(socials_index[i]->path)
            num_of_social_path++;
         if(socials_index[i]->timed)
            num_of_social_timed++;
         if(socials_index[i]->error)
            num_of_social_errors++;
         }
      }

}

/* Find the next line with the MOB: header.. sBuf is a global so it will
 * be already filled for the use by the caller..
 */
int findNextMobHeader(void)
{

   for(;;)
      {
      if(fget_line(sf, sBuf, MAX_STRING_LENGTH) == EOF)
         return FALSE;

      if(!strncmp(sBuf, "MOB:", 4))
         return TRUE;
      }

}

void logSocialError(const char *format,...)
{

   va_list args;
   struct socials_head *head;
   char Gbuf[MAX_STRING_LENGTH];


   va_start(args, format);
   vsprintf(sBuf, format, args);
   sBuf[strlen(sBuf)] = 0;
   va_end(args);

   logit(LOG_DEBUG, "%s", sBuf);

   head = socials_index[rnum];
   if(!head)
      {
      CREATE(head, struct socials_head, 1);
      socials_index[rnum] = head;
      }

   if(head->error)
      {
      strcpy(Gbuf, head->error);
      strcat(Gbuf, sBuf);
      free(head->error);
      head->error = strdup(Gbuf);

      }
   else
      head->error = strdup(sBuf);

   return;

}


void path_trigger_socials(P_char mob, int path_id, int path_trigger)
{
   struct socData_trigger *data, *orgData;
   bool pathChain = FALSE, found = FALSE;
   int i = 0;

   /* Want this as close to the start of the fn as possible */
   if(path_trigger && (!socials_index[mob->nr] || !IS_CSET(socials_index[mob->nr]->cmd_list, path_trigger)))
      return;

   if(AWAKE(mob) && (mob->in_room != NOWHERE))
      {

      /* get the first damn element from the head.. */
      data = socials_index[mob->nr]->trigger;

      if(!data)
         {
         logit(LOG_DEBUG, "[socials_proc]: No socials data exists for mob vnum: %d\n", mob_index[mob->nr].virtual);
         dump_core();
         }

      /* This one loops thru all the different commands */
      do
         {
         if(path_trigger == data->trigger_action &&
            (data->chance ? (!number(0, data->chance)) : TRUE))
            {

            orgData = data;

            do
               {

               if(data->arg)
                  i = atoi(data->arg);

               // Find where we left off
               if(((data->response_action) == CMD_SOC_PATH) && (i == path_id))
                  {
                  found = TRUE;
                  data = data->chainNext;
                  continue;
                  }
               // This is following the path we just finished, execute the cmds
               if(found)
                  {

                  // Flag it so we can bail out afterwards
                  if((data->response_action) == CMD_SOC_PATH)
                     pathChain = TRUE;

                  if(IS_FIGHTING(mob))
                     {
                     if(!IS_SET(data->flag, NON_COMBAT_ONLY))
                        socials_doAction(mob, NULL, data->arg, data->delay, data->response_action, data->arg, data->flag, path_trigger);
                     }
                  else
                     {
                     if(!IS_SET(data->flag, COMBAT_ONLY))
                        socials_doAction(mob, NULL, data->arg, data->delay, data->response_action, data->arg, data->flag, path_trigger);
                     }

                  // Hit another path, bail out after it's been initted
                  if(pathChain)
                     return;

                  }

               data = data->chainNext;  /* chain to next action in reply to this trigger */
               } while(data) ;
            data = orgData;
            }
         data = data->next;
         } while(data) ;

      }
}

/* loadPath(): Load a path from the soc file -- CRM*/
void loadPath(void)
{
   char *valptr;
   int type = -1, delay = -1, id = -1;
   int i = 0, j = 0, offset = 0, path_id = -1, test = 0;
   int rooms[MAX_PATH_ROOMS], dirs[MAX_PATH_ROOMS];
   bool watchForStop = FALSE;
   char tmp[8192];

   if(total_paths >= MAX_PATHS)
      {
      logit(LOG_DEBUG, "Maximum paths exceeded!!!");
      return;
      }

   // Init rooms/dirs
   for(j = 0; j < MAX_PATH_ROOMS; j++)
      {
      rooms[j] = -1;
      dirs[j] = -1;
      }

   // Start primary loop
   for(;;)
      {

      // Get next line from the file
      fget_line(sf, sBuf, MAX_STRING_LENGTH);

      for(i = 0;;i++)
         {
         if(soc_pathKeys[i].key == -1)
            {
            logSocialError("Invalid path command for mobile %d, cannot find a match: %s\n", vnum, sBuf);
            break;
            }

         if(!strncmp(sBuf, soc_pathKeys[i].cmd, soc_pathKeys[i].len))
            {

            valptr = sBuf + strlen(soc_pathKeys[i].cmd) + 1;

            switch(soc_pathKeys[i].key)
               {

               case SOC_ID: {

                     valptr = sBuf + strlen(soc_pathKeys[i].cmd) + 1;
                     if(valptr >= (strlen(sBuf) + sBuf))
                        {
                        logSocialError("Invalid id for vnum %d in a path\n", vnum);
                        return;
                        }

                     if(id != -1)
                        {
                        logSocialError("Duplicate id field found in a path for vnum %d\n", vnum);
                        return;
                        }

                     id = atoi(valptr);
                     watchForStop = FALSE;
                     break;
                  }

               case SOC_TYPE: {

                     valptr = sBuf + strlen(soc_pathKeys[i].cmd) + 1;
                     if(valptr >= (strlen(sBuf) + sBuf))
                        {
                        logSocialError("Invalid type for vnum %d in a path\n", vnum);
                        return;
                        }

                     if(type != -1)
                        {
                        logSocialError("Duplicate type field found in a path for vnum %d\n", vnum);
                        return;
                        }

                     type = atoi(valptr);
                     watchForStop = FALSE;
                     break;
                  }

               case SOC_DELAY: {

                     valptr = sBuf + strlen(soc_pathKeys[i].cmd) + 1;
                     if(valptr >= (strlen(sBuf) + sBuf))
                        {
                        logSocialError("Invalid delay for vnum %d in a path\n", vnum);
                        return;
                        }

                     if(delay != -1)
                        {
                        logSocialError("Duplicate delay field found in a path for vnum %d\n", vnum);
                        return;
                        }

                     delay = atoi(valptr);
                     watchForStop = FALSE;
                     break;
                  }

               case SOC_ROOMS: {
                     offset = 0;
                     valptr = sBuf + strlen(soc_pathKeys[i].cmd) + 1;
                     if(valptr >= (strlen(sBuf) + sBuf))
                        {
                        logSocialError("Invalid delay for vnum %d in a path\n", vnum);
                        return;
                        }

                     if(rooms[0] != -1)
                        {
                        logSocialError("Duplicate room field found in a path for vnum %d\n", vnum);
                        return;
                        }

                     // Let the fun begin - gobble up the rooms listed
                     for(j = 0; j < MAX_PATH_ROOMS; j++)
                        {
                        test = sscanf(valptr + offset, "%s", tmp);

                        if(!test)
                           break;

                        rooms[j] = atoi(tmp);
                        offset += (strlen(tmp) + 1);
                        if(((strlen(soc_pathKeys[i].cmd) + 2) + offset) >= (strlen(sBuf) - 1))
                           break;

                        test = 0;
                        }

                     watchForStop = FALSE;
                     break;
                  }

               case SOC_DIRS: {
                     offset = 0;
                     valptr = sBuf + strlen(soc_pathKeys[i].cmd) + 1;
                     if(valptr >= (strlen(sBuf) + sBuf))
                        {
                        logSocialError("Invalid delay for vnum %d in a path\n", vnum);
                        return;
                        }

                     if(dirs[0] != -1)
                        {
                        logSocialError("Duplicate room field found in a path for vnum %d\n", vnum);
                        return;
                        }

                     // Let the fun begin - gobble up the rooms listed
                     for(j = 0; j < MAX_PATH_ROOMS; j++)
                        {
                        test = sscanf(valptr + offset, "%s", tmp);

                        if(!test)
                           break;

                        dirs[j] = atoi(tmp);
                        //offset += (strlen(tmp) + 1);
                        offset += 2;

                        if(((strlen(soc_pathKeys[i].cmd) + 1) + offset) >= (strlen(sBuf)))
                           break;
                        test = 0;

                        }

                     watchForStop = TRUE;
                     break;
                  }

               case SOC_DONE: {
                     if(!watchForStop)
                        {
                        logSocialError("DONE encountered out of sync in periodic action for vnum %d\n", vnum);
                        return;
                        }


                     total_paths++;
                     path_id = total_paths;

                     path_info[path_id].num = (ush_int) path_id;
                     path_info[path_id].type = (ush_int) type;
                     path_info[path_id].delay = (ush_int) delay;
                     path_info[path_id].id = (ush_int) id;

                     for(j = 0; j < MAX_PATH_ROOMS; j++)
                        {
                        path_info[path_id].rooms[j] = rooms[j];
                        path_info[path_id].dirs[j] = ((sh_int) dirs[j]);
                        }

                     path_lookup[path_id].mob_vnum = vnum;
                     path_lookup[path_id].path_num = (ush_int) path_id;
                     path_lookup[path_id].id = (ush_int) id;

                     AddProcMob(vnum, path_handler, "path");
                     return;
                  }

               default:
                  dump_core();
               }
            break;
            }
         }
      }

}

void loadPeriodic(void)
{

   struct socials_head *socHead;
   struct socData_periodic *periodicHead = NULL;
   struct socData_periodic *origPeriodicHead = NULL;
   struct socData_periodic *socNext;

   char *valptr, *tmpptr;

   int   flag, chance, delay, action, i;
   int   watchForStop = FALSE;

   flag = delay = chance = action = -1;

   for(;;)
      {

      fget_line(sf, sBuf, MAX_STRING_LENGTH);

      for(i = 0;;i++)
         {
         if(soc_periodicKeys[i].key == -1)
            {
            logSocialError("Invalid periodic command for mobile %d, cannot find a match: %s\n", vnum, sBuf);
            freePeriodic(origPeriodicHead);
            break;
            }

         if(!strncmp(sBuf, soc_periodicKeys[i].cmd, soc_periodicKeys[i].len))
            {

            valptr = sBuf + strlen(soc_periodicKeys[i].cmd) + 1;

            switch(soc_periodicKeys[i].key)
               {

               case SOC_FLAG: {

                     valptr = sBuf + strlen(soc_periodicKeys[i].cmd) + 1;
                     if(valptr >= (strlen(sBuf) + sBuf))
                        {
                        logSocialError("Invalid flag for vnum %d in a periodic action\n", vnum);
                        freePeriodic(origPeriodicHead);
                        return;
                        }

                     if(flag != -1)
                        {
                        logSocialError("Duplicate flag field found in a periodic action for vnum %d\n", vnum);
                        freePeriodic(origPeriodicHead);
                        return;
                        }

                     flag = atoi(valptr);
                     watchForStop = FALSE;
                     break;
                  }

               case SOC_DELAY: {

                     valptr = sBuf + strlen(soc_periodicKeys[i].cmd) + 1;
                     if(valptr >= (strlen(sBuf) + sBuf))
                        {
                        logSocialError("Invalid delay for vnum %d in a periodic action\n", vnum);
                        freePeriodic(origPeriodicHead);
                        return;
                        }

                     if(delay != -1)
                        {
                        logSocialError("Duplicate delay field found in a periodic action for vnum %d\n", vnum);
                        freePeriodic(origPeriodicHead);
                        return;
                        }

                     delay = atoi(valptr);
                     watchForStop = FALSE;
                     break;
                  }

               case SOC_CHANCE: {

                     valptr = sBuf + strlen(soc_periodicKeys[i].cmd) + 1;
                     if(valptr >= (strlen(sBuf) + sBuf))
                        {
                        logSocialError("Invalid chance for vnum %d in a periodic action\n", vnum);
                        freePeriodic(origPeriodicHead);
                        return;
                        }
                     if(delay != -1)
                        {
                        logSocialError("Duplicate chance field found in a periodic action for vnum %d\n", vnum);
                        freePeriodic(origPeriodicHead);
                        return;
                        }
                     chance = atoi(valptr);
                     watchForStop = FALSE;
                     break;
                  }

                  /* Ok what happens here is a mess but its semi efficient..
                   * - Get the command number associated with the ACTION field
                   * - ACTION should be followed by an argument string terminated by ~
                   * - the for(;;) loop reads multiple arg lines dumping em into
                   *   sBuf and replacing the terminating ~ with a NULL..
                   * - Next we find a place to store all of the crap we've just read..
                   *   Its being pointed to by periodicHead (created as needed)..
                   * - Assign all the read values and wait for either DONE or next set
                   *   of commands that will chain after this one..
                   */
               case SOC_ACTION: {
                     /* get the command number for this action */
                     valptr = sBuf + strlen(soc_periodicKeys[i].cmd) + 1;
                     if(valptr >= (strlen(sBuf) + sBuf))
                        {
                        logSocialError("Invalid action for vnum %d in a periodic action\n", vnum);
                        freePeriodic(origPeriodicHead);
                        return;
                        }
                     if(action != -1)
                        {
                        logSocialError("Duplicate action field found in a periodic action for vnum %d\n", vnum);
                        freePeriodic(origPeriodicHead);
                        return;
                        }

                     action = atoi(valptr);

                     /* get the arguments for this action if any.. terminated by ~ */
                     tmpptr = sBuf;
                     for(;;)
                        {
                        fget_line(sf, tmpptr, MAX_STRING_LENGTH);
                        if(*tmpptr == '~')
                           {
                           *tmpptr = 0;
                           if((flag == -1) || (delay == -1) || (chance == -1))
                              {
                              logSocialError("Either flag, delay or chance is missing in a periodic action for vnum %d\n", vnum);
                              freePeriodic(origPeriodicHead);
                              return;
                              }

                           periodicHead = origPeriodicHead;

                           if(!periodicHead)
                              {
                              CREATE(periodicHead, struct socData_periodic, 1);
                              origPeriodicHead = periodicHead;
                              }
                           else
                              {
                              /* Skip to the end of the chain of commands defined for this
                               * periodic action */
                              while(periodicHead->chainNext)
                                 periodicHead = periodicHead->chainNext;

                              CREATE(periodicHead->chainNext, struct socData_periodic, 1);
                              periodicHead = periodicHead->chainNext;

                              }

                           periodicHead->flag = flag;
                           periodicHead->delay = delay;
                           periodicHead->chance = chance;
                           periodicHead->action = action;
                           if(*sBuf)
                              periodicHead->arg = str_dup(sBuf);
                           flag = delay = chance = action = -1;
                           watchForStop = TRUE;
                           break;

                           }
                        else
                           tmpptr += strlen(tmpptr);
                        }
                     break;
                  }

               case SOC_DONE: {
                     if(!watchForStop)
                        {
                        logSocialError("DONE encountered out of sync in periodic action for vnum %d\n", vnum);
                        freePeriodic(origPeriodicHead);
                        return;
                        }

                     socHead = socials_index[rnum];
                     if(socHead)
                        {
                        if(socHead->periodic)
                           {
                           socNext = socHead->periodic;   /* ptr to the list of trigger actions for this mobile */
                           while(socNext->next)           /* skip past other trigger commands already defined for this one */
                              socNext = socNext->next;
                           socNext->next = origPeriodicHead;
                           }
                        else
                           socHead->periodic = origPeriodicHead;
                        }
                     else
                        {
                        CREATE(socHead, struct socials_head, 1);
                        socHead->periodic = origPeriodicHead;
                        socials_index[rnum] = socHead;
                        }

                     SET_BIT(set_command_index[rnum], SOC_PERIODIC);
                     return;
                  }

               default:
                  dump_core();
               }
            break;
            }
         }
      }
}


void freePeriodic(struct socData_periodic *phead)
{

   struct socData_periodic *current, *prev;

   current = phead;

   while(current)
      {
      if(current->arg)
         free (current->arg);
      prev = current;
      current = current->next;
      free (prev);
      }

   return;

}

/****************************************************************************/
/**********************  T  R  I  G  G  E  R  S  ****************************/
/****************************************************************************/

void loadTriggers(void)
{

   struct socials_head *socHead;
   struct socData_trigger *triggerHead = NULL;
   struct socData_trigger *origTriggerHead = NULL;
   struct socData_trigger *socNext;

   char *valptr, *tmpptr;

   int   flag, chance, delay, action, trigger;
   int   watchForStop = FALSE, i;

   flag = delay = chance = action = trigger = -1;


   for(;;)
      {

      fget_line(sf, sBuf, MAX_STRING_LENGTH);

      for(i = 0;;i++)
         {
         if(soc_triggerKeys[i].key == -1)
            {
            logSocialError("Invalid trigger command for mobile %d, cannot find a match: %s\n", vnum, sBuf);
            freeTriggers(origTriggerHead);
            break;
            }

         if(!strncmp(sBuf, soc_triggerKeys[i].cmd, soc_triggerKeys[i].len))
            {

            valptr = sBuf + strlen(soc_triggerKeys[i].cmd) + 1;

            switch(soc_triggerKeys[i].key)
               {

               case SOC_FLAG: {

                     valptr = sBuf + strlen(soc_triggerKeys[i].cmd) + 1;
                     if(valptr >= (strlen(sBuf) + sBuf))
                        {
                        logSocialError("Invalid flag for vnum %d in a trigger action\n", vnum);
                        freeTriggers(origTriggerHead);
                        return;
                        }

                     if(flag != -1)
                        {
                        logSocialError("Duplicate flag field found in a trigger action for vnum %d\n", vnum);
                        freeTriggers(origTriggerHead);
                        return;
                        }

                     flag = atoi(valptr);
                     watchForStop = FALSE;
                     break;
                  }

               case SOC_DELAY: {

                     valptr = sBuf + strlen(soc_triggerKeys[i].cmd) + 1;
                     if(valptr >= (strlen(sBuf) + sBuf))
                        {
                        logSocialError("Invalid delay for vnum %d in a trigger action\n", vnum);
                        freeTriggers(origTriggerHead);
                        return;
                        }

                     if(delay != -1)
                        {
                        logSocialError("Duplicate delay field found in a trigger action for vnum %d\n", vnum);
                        freeTriggers(origTriggerHead);
                        return;
                        }

                     delay = atoi(valptr);
                     watchForStop = FALSE;
                     break;
                  }

               case SOC_CHANCE: {

                     valptr = sBuf + strlen(soc_triggerKeys[i].cmd) + 1;
                     if(valptr >= (strlen(sBuf) + sBuf))
                        {
                        logSocialError("Invalid chance for vnum %d in a trigger action\n", vnum);
                        freeTriggers(origTriggerHead);
                        return;
                        }
                     if(delay != -1)
                        {
                        logSocialError("Duplicate chance field found in a trigger action for vnum %d\n", vnum);
                        freeTriggers(origTriggerHead);
                        return;
                        }
                     chance = atoi(valptr);
                     watchForStop = FALSE;
                     break;
                  }

               case SOC_TRIGGER: {
                     /* if they define multiple trigger fields within the _chain_
                      * it will be ignored by the proc anyways.. so dont bother
                      * to filter it.. Still want to catch redefinitions withing
                      * the currently processed command tho.. */
                     valptr = sBuf + strlen(soc_triggerKeys[i].cmd) + 1;
                     if(valptr >= (strlen(sBuf) + sBuf))
                        {
                        logSocialError("Invalid chance for vnum %d in a trigger action\n", vnum);
                        freeTriggers(origTriggerHead);
                        return;
                        }
                     if(trigger != -1)
                        {
                        logSocialError("Duplicate trigger field found in a trigger action for vnum %d\n", vnum);
                        freeTriggers(origTriggerHead);
                        return;
                        }
                     trigger = atoi(valptr);
                     watchForStop = FALSE;
                     break;
                  }

                  /* Ok what happens here is a mess but its semi efficient..
                   * - Get the command number associated with the ACTION field
                   * - ACTION should be followed by an argument string terminated by ~
                   * - the for(;;) loop reads multiple arg lines dumping em into
                   *   sBuf and replacing the terminating ~ with a NULL..
                   * - Next we find a place to store all of the crap we've just read..
                   *   Its being pointed to by triggerHead (created as needed)..
                   * - Assign all the read values and wait for either DONE or next set
                   *   of commands that will chain after this one..
                   */
               case SOC_ACTION: {
                     /* get the command number for this action */
                     valptr = sBuf + strlen(soc_triggerKeys[i].cmd) + 1;
                     if(valptr >= (strlen(sBuf) + sBuf))
                        {
                        logSocialError("Invalid action for vnum %d in a trigger action\n", vnum);
                        freeTriggers(origTriggerHead);
                        return;
                        }
                     if(action != -1)
                        {
                        logSocialError("Duplicate action field found in a trigger action for vnum %d\n", vnum);
                        freeTriggers(origTriggerHead);
                        return;
                        }

                     action = atoi(valptr);

                     /* get the arguments for this action if any.. terminated by ~ */
                     tmpptr = sBuf;
                     for(;;)
                        {
                        fget_line(sf, tmpptr, MAX_STRING_LENGTH);
                        if(*tmpptr == '~')
                           {
                           *tmpptr = 0;
                           if((flag == -1) || (delay == -1) || (chance == -1))
                              {
                              logSocialError("Either flag, delay or chance is missing in a trigger action for vnum %d\n", vnum);
                              freeTriggers(origTriggerHead);
                              return;
                              }

                           triggerHead = origTriggerHead;

                           if(!triggerHead)
                              {
                              CREATE(triggerHead, struct socData_trigger, 1);
                              origTriggerHead = triggerHead;
                              }
                           else
                              {
                              /* Skip to the end of the chain of commands defined for this
                               * trigger action */
                              while(triggerHead->chainNext)
                                 triggerHead = triggerHead->chainNext;

                              CREATE(triggerHead->chainNext, struct socData_trigger, 1);
                              triggerHead = triggerHead->chainNext;

                              }

                           triggerHead->flag = flag;
                           triggerHead->delay = delay;
                           triggerHead->chance = chance;
                           triggerHead->trigger_action = trigger;
                           triggerHead->response_action = action;
                           if(*sBuf)
                              triggerHead->arg = str_dup(sBuf);
                           flag = delay = chance = action = -1;
                           watchForStop = TRUE;
                           break;

                           }
                        else
                           tmpptr += strlen(tmpptr);
                        }
                     break;
                  }

               case SOC_DONE: {
                     if(!watchForStop)
                        {
                        logSocialError("DONE encountered out of sync in trigger action for vnum %d\n", vnum);
                        freeTriggers(origTriggerHead);
                        return;
                        }

                     socHead = socials_index[rnum];
                     if(socHead)
                        {
                        if(socHead->trigger)
                           {
                           socNext = socHead->trigger;   /* ptr to the list of periodic actions for this mobile */
                           while(socNext->next)           /* skip past other periodic commands already defined for this one */
                              socNext = socNext->next;
                           socNext->next = origTriggerHead;
                           }
                        else
                           socHead->trigger = origTriggerHead;
                        }
                     else
                        {
                        CREATE(socHead, struct socials_head, 1);
                        socHead->trigger = origTriggerHead;
                        socials_index[rnum] = socHead;
                        }

                     SET_CBIT(socials_index[rnum]->cmd_list, trigger);

                     SET_BIT(set_command_index[rnum], SOC_TRIGGER);
                     return;
                  }

               default:
                  dump_core();
               }
            break;
            }
         }
      }
}


void freeTriggers(struct socData_trigger *phead)
{

   struct socData_trigger *current, *prev;

   current = phead;

   while(current)
      {
      if(current->arg)
         free (current->arg);
      prev = current;
      current = current->next;
      free (prev);
      }

   return;

}


void freeTimed(struct socData_timed *phead)
{

   struct socData_timed *current, *prev;

   current = phead;

   while(current)
      {
      if(current->arg)
         free (current->arg);
      prev = current;
      current = current->next;
      free (prev);
      }

   return;

}

/****************************************************************************/
/*************************  L  I  S  T  S  **********************************/
/****************************************************************************/

void loadPeriodicList(void)
{

   struct socials_head *socHead;
   struct socList *listHead = NULL;
   struct socList *origListHead = NULL;

   struct socList *listArray;
   struct socList *listArrayTmp;
   struct socList  socTmp;

   char   *valptr, *tmpptr;

   int    flag, chance, delay, action, i;
   int    watchForStop = FALSE;
   int    numElements;


   /* qwe */

   numElements = countPeriodicListElements();
   CREATE(listArray, struct socList, numElements);
   listArrayTmp = listArray;
   listHead = &socTmp;

   flag = delay = chance = action = -1;

   for(;;)
      {

      fget_line(sf, sBuf, MAX_STRING_LENGTH);

      for(i = 0;;i++)
         {
         if(soc_listKeys[i].key == -1)
            {
            logSocialError("Invalid periodic command for mobile %d, cannot find a match: %s\n", vnum, sBuf);
            freePeriodicList(origListHead, numElements);
            break;
            }

         if(!strncmp(sBuf, soc_listKeys[i].cmd, soc_listKeys[i].len))
            {

            valptr = sBuf + strlen(soc_listKeys[i].cmd) + 1;

            switch(soc_listKeys[i].key)
               {

               case SOC_FLAG: {

                     valptr = sBuf + strlen(soc_listKeys[i].cmd) + 1;
                     if(valptr >= (strlen(sBuf) + sBuf))
                        {
                        logSocialError("Invalid flag for vnum %d in a periodic action\n", vnum);
                        freePeriodicList(origListHead, numElements);
                        return;
                        }

                     if(flag != -1)
                        {
                        logSocialError("Duplicate flag field found in a periodic action for vnum %d\n", vnum);
                        freePeriodicList(origListHead, numElements);
                        return;
                        }

                     flag = atoi(valptr);
                     watchForStop = FALSE;
                     break;
                  }

               case SOC_DELAY: {

                     valptr = sBuf + strlen(soc_listKeys[i].cmd) + 1;
                     if(valptr >= (strlen(sBuf) + sBuf))
                        {
                        logSocialError("Invalid delay for vnum %d in a periodic action\n", vnum);
                        freePeriodicList(origListHead, numElements);
                        return;
                        }

                     if(delay != -1)
                        {
                        logSocialError("Duplicate delay field found in a periodic action for vnum %d\n", vnum);
                        freePeriodicList(origListHead, numElements);
                        return;
                        }

                     delay = atoi(valptr);
                     watchForStop = FALSE;
                     break;
                  }

               case SOC_CHANCE: {

                     valptr = sBuf + strlen(soc_listKeys[i].cmd) + 1;
                     if(valptr >= (strlen(sBuf) + sBuf))
                        {
                        logSocialError("Invalid chance for vnum %d in a periodic action\n", vnum);
                        freePeriodicList(origListHead, numElements);
                        return;
                        }
                     if(delay != -1)
                        {
                        logSocialError("Duplicate chance field found in a periodic action for vnum %d\n", vnum);
                        freePeriodicList(origListHead, numElements);
                        return;
                        }
                     chance = atoi(valptr);
                     watchForStop = FALSE;
                     break;
                  }

                  /* Ok what happens here is a mess but its semi efficient..
                   * - Get the command number associated with the ACTION field
                   * - ACTION should be followed by an argument string terminated by ~
                   * - the for(;;) loop reads multiple arg lines dumping em into
                   *   sBuf and replacing the terminating ~ with a NULL..
                   * - Next we find a place to store all of the crap we've just read..
                   *   Its being pointed to by origListHead (created as needed)..
                   * - Assign all the read values and wait for either DONE or next set
                   *   of commands that will chain after this one..
                   */
               case SOC_ACTION: {
                     /* get the command number for this action */
                     valptr = sBuf + strlen(soc_listKeys[i].cmd) + 1;
                     if(valptr >= (strlen(sBuf) + sBuf))
                        {
                        logSocialError("Invalid action for vnum %d in a periodic action\n", vnum);
                        freePeriodicList(origListHead, numElements);
                        return;
                        }
                     if(action != -1)
                        {
                        logSocialError("Duplicate action field found in a periodic action for vnum %d\n", vnum);
                        freePeriodicList(origListHead, numElements);
                        return;
                        }

                     action = atoi(valptr);

                     /* get the arguments for this action if any.. terminated by ~ */
                     tmpptr = sBuf;
                     for(;;)
                        {
                        fget_line(sf, tmpptr, MAX_STRING_LENGTH);
                        if(*tmpptr == '~')
                           {
                           *tmpptr = 0;
                           if((flag == -1) || (delay == -1) || (chance == -1))
                              {
                              logSocialError("Either flag, delay or chance is missing in a periodic action for vnum %d\n", vnum);
                              freePeriodicList(origListHead, numElements);
                              return;
                              }

                           listHead = origListHead;

                           if(!listHead)
                              {
                              CREATE(listHead, struct socList, 1);
                              origListHead = listHead;
                              }
                           else
                              {
                              while(listHead->next)
                                 listHead = listHead->next;

                              CREATE(listHead->next, struct socList, 1);
                              listHead = listHead->next;

                              }

                           listHead->flag = flag;
                           listHead->delay = delay;
                           listHead->chance = chance;
                           listHead->action = action;
                           if(*sBuf)
                              listHead->arg = str_dup(sBuf);
                           flag = delay = chance = action = -1;
                           watchForStop = TRUE;
                           break;

                           }
                        else
                           tmpptr += strlen(tmpptr);
                        }
                     break;
                  }

               case SOC_DONE: {
                     if(!watchForStop)
                        {
                        logSocialError("DONE encountered out of sync in list action for vnum %d\n", vnum);
                        freePeriodicList(origListHead, numElements);
                        return;
                        }
                     bcopy(origListHead, listArrayTmp, sizeof(struct socList));
                     listArrayTmp++;
                     watchForStop = FALSE;

                     /* Don't free origListHead here - it may be needed for SOC_DONELIST
                      * The list will be freed at SOC_DONELIST or when an error occurs */
                     break;
                  }

               case SOC_DONELIST: {
                     if(!watchForStop)
                        {
                        logSocialError("DONELIST encountered out of sync in list action for vnum %d\n", vnum);
                        freePeriodicList(origListHead, numElements);
                        return;
                        }

                     bcopy(origListHead, listArrayTmp, sizeof(struct socList));

                     socHead = socials_index[rnum];
                     if(socHead)
                        {
                        if(socHead->list)
                           {
                           socHead->list->listArray = listArray;
                           socHead->list->numActions = numElements;
                           }
                        else
                           {
                           CREATE(socHead->list, struct socData_list, 1);
                           socHead->list->listArray = listArray;
                           socHead->list->numActions = numElements;
                           }
                        }
                     else
                        {
                        CREATE(socHead, struct socials_head, 1);
                        CREATE(socHead->list, struct socData_list, 1);
                        socHead->list->listArray = listArray;
                        socHead->list->numActions = numElements;
                        socials_index[rnum] = socHead;
                        }

                     SET_BIT(set_command_index[rnum], SOC_LIST);
                     
                     /* Free the temporary list after copying to array */
                     freePeriodicList(origListHead, numElements);
                     
                     return;
                  }

               default:
                  dump_core();
               }
            break;
            }
         }
      }
}


int countPeriodicListElements()
{

   int i, num = 0, bail = FALSE, loopBail = FALSE;
   long oldPos = 0;
   char *valptr;

   oldPos = ftell(sf);

   for(;;)
      {

      fget_line(sf, sBuf, MAX_STRING_LENGTH);
      loopBail = FALSE;

      for(i = 0;;i++)
         {

         if(!strncmp(sBuf, soc_listKeys[i].cmd, soc_listKeys[i].len))
            {
            valptr = sBuf + strlen(soc_listKeys[i].cmd) + 1;
            switch(soc_listKeys[i].key)
               {

               case SOC_DONE: {
                     num++;
                     loopBail = TRUE;
                     break;
                  }

               case SOC_DONELIST: {
                     num++;
                     bail = TRUE;
                     loopBail = TRUE;
                     break;
                  }

               default:
                  loopBail = TRUE;
                  break;
               }
            loopBail = TRUE;
            }
         if(loopBail)
            break;
         }

      if(bail)
         break;
      }


   if(fseek (sf, oldPos, SEEK_SET) == -1)
      {
      logit(LOG_DEBUG, "Error in world.soc: Cannot reset the file position indicator. Errno: %s\n", errno);
      dump_core();
      }

   return num;

}


void freePeriodicList(struct socList *phead, int num)
{
   struct socList *current, *next;

   /* Handle NULL pointer */
   if (!phead)
      return;

   /* If this is being called after the list was already freed and copied to array,
    * then we should not free it again. The function is being called with a linked
    * list, not an array, so we traverse it as a linked list */
   current = phead;
   
   while(current)
      {
      next = current->next;
      if(current->arg)
         free (current->arg);
      free (current);
      current = next;
      }

   return;

}

void loadTimed(void)
{
   struct socials_head *socHead;
   struct socData_timed *timedHead = NULL;
   struct socData_timed *origTimedHead = NULL;
   struct socData_timed *socNext;

   char *valptr, *tmpptr;

   int   flag, chance, delay, action, hour;
   int   watchForStop = FALSE, i;

   flag = delay = chance = action = hour = -1;


   for(;;)
      {

      fget_line(sf, sBuf, MAX_STRING_LENGTH);

      for(i = 0;;i++)
         {
         if(soc_timedKeys[i].key == -1)
            {
            logSocialError("Invalid timed command for mobile %d, cannot find a match: %s\n", vnum, sBuf);
            freeTimed(origTimedHead);
            break;
            }

         if(!strncmp(sBuf, soc_timedKeys[i].cmd, soc_timedKeys[i].len))
            {

            valptr = sBuf + strlen(soc_timedKeys[i].cmd) + 1;

            switch(soc_timedKeys[i].key)
               {

               case SOC_FLAG: {

                     valptr = sBuf + strlen(soc_timedKeys[i].cmd) + 1;
                     if(valptr >= (strlen(sBuf) + sBuf))
                        {
                        logSocialError("Invalid flag for vnum %d in a timed action\n", vnum);
                        freeTimed(origTimedHead);
                        return;
                        }

                     if(flag != -1)
                        {
                        logSocialError("Duplicate flag field found in a timed action for vnum %d\n", vnum);
                        freeTimed(origTimedHead);
                        return;
                        }

                     flag = atoi(valptr);
                     watchForStop = FALSE;
                     break;
                  }

               case SOC_DELAY: {

                     valptr = sBuf + strlen(soc_timedKeys[i].cmd) + 1;
                     if(valptr >= (strlen(sBuf) + sBuf))
                        {
                        logSocialError("Invalid delay for vnum %d in a timed action\n", vnum);
                        freeTimed(origTimedHead);
                        return;
                        }

                     if(delay != -1)
                        {
                        logSocialError("Duplicate delay field found in a timed action for vnum %d\n", vnum);
                        freeTimed(origTimedHead);
                        return;
                        }

                     delay = atoi(valptr);
                     watchForStop = FALSE;
                     break;
                  }

               case SOC_CHANCE: {

                     valptr = sBuf + strlen(soc_timedKeys[i].cmd) + 1;
                     if(valptr >= (strlen(sBuf) + sBuf))
                        {
                        logSocialError("Invalid chance for vnum %d in a timed action\n", vnum);
                        freeTimed(origTimedHead);
                        return;
                        }
                     if(delay != -1)
                        {
                        logSocialError("Duplicate chance field found in a timed action for vnum %d\n", vnum);
                        freeTimed(origTimedHead);
                        return;
                        }
                     chance = atoi(valptr);
                     watchForStop = FALSE;
                     break;
                  }

               case SOC_HOUR: {
                     /* if they define multiple trigger fields within the _chain_
                      * it will be ignored by the proc anyways.. so dont bother
                      * to filter it.. Still want to catch redefinitions withing
                      * the currently processed command tho.. */
                     valptr = sBuf + strlen(soc_timedKeys[i].cmd) + 1;
                     if(valptr >= (strlen(sBuf) + sBuf))
                        {
                        logSocialError("Invalid hour for vnum %d in a timed action\n", vnum);
                        freeTimed(origTimedHead);
                        return;
                        }
                     if(hour != -1)
                        {
                        logSocialError("Duplicate hour field found in a timed action for vnum %d\n", vnum);
                        freeTimed(origTimedHead);
                        return;
                        }

                     hour = atoi(valptr);

                     if(hour < 0 || hour > 24)
                        {
                        logSocialError("Invalid hour field found in a timed action for vnum %d\n", vnum);
                        freeTimed(origTimedHead);
                        return;
                        }

                     watchForStop = FALSE;
                     break;
                  }

                  /* Ok what happens here is a mess but its semi efficient..
                   * - Get the command number associated with the ACTION field
                   * - ACTION should be followed by an argument string terminated by ~
                   * - the for(;;) loop reads multiple arg lines dumping em into
                   *   sBuf and replacing the terminating ~ with a NULL..
                   * - Next we find a place to store all of the crap we've just read..
                   *   Its being pointed to by triggerHead (created as needed)..
                   * - Assign all the read values and wait for either DONE or next set
                   *   of commands that will chain after this one..
                   */
               case SOC_ACTION: {
                     /* get the command number for this action */
                     valptr = sBuf + strlen(soc_timedKeys[i].cmd) + 1;
                     if(valptr >= (strlen(sBuf) + sBuf))
                        {
                        logSocialError("Invalid action for vnum %d in a timed action\n", vnum);
                        freeTimed(origTimedHead);
                        return;
                        }
                     if(action != -1)
                        {
                        logSocialError("Duplicate action field found in a timed action for vnum %d\n", vnum);
                        freeTimed(origTimedHead);
                        return;
                        }

                     action = atoi(valptr);

                     /* get the arguments for this action if any.. terminated by ~ */
                     tmpptr = sBuf;
                     for(;;)
                        {
                        fget_line(sf, tmpptr, MAX_STRING_LENGTH);
                        if(*tmpptr == '~')
                           {
                           *tmpptr = 0;
                           if((flag == -1) || (delay == -1) || (chance == -1))
                              {
                              logSocialError("Either flag, delay or chance is missing in a timed action for vnum %d\n", vnum);
                              freeTimed(origTimedHead);
                              return;
                              }

                           timedHead = origTimedHead;

                           if(!timedHead)
                              {
                              CREATE(timedHead, struct socData_timed, 1);
                              origTimedHead = timedHead;
                              }
                           else
                              {
                              /* Skip to the end of the chain of commands defined for this
                               * trigger action */
                              while(timedHead->chainNext)
                                 timedHead = timedHead->chainNext;

                              CREATE(timedHead->chainNext, struct socData_timed, 1);
                              timedHead = timedHead->chainNext;

                              }

                           timedHead->flag = flag;
                           timedHead->delay = delay;
                           timedHead->chance = chance;
                           timedHead->hour = hour;
                           timedHead->response_action = action;
                           if(*sBuf)
                              timedHead->arg = str_dup(sBuf);
                           flag = delay = chance = action = -1;
                           watchForStop = TRUE;
                           break;

                           }
                        else
                           tmpptr += strlen(tmpptr);
                        }
                     break;
                  }

               case SOC_DONE: {
                     if(!watchForStop)
                        {
                        logSocialError("DONE encountered out of sync in timed action for vnum %d\n", vnum);
                        freeTimed(origTimedHead);
                        return;
                        }

                     socHead = socials_index[rnum];
                     if(socHead)
                        {
                        if(socHead->timed)
                           {
                           socNext = socHead->timed;   /* ptr to the list of timed actions for this mobile */
                           while(socNext->next)           /* skip past other timed commands already defined for this one */
                              socNext = socNext->next;
                           socNext->next = origTimedHead;
                           }
                        else
                           socHead->timed = origTimedHead;
                        }
                     else
                        {
                        CREATE(socHead, struct socials_head, 1);
                        socHead->timed = origTimedHead;
                        socials_index[rnum] = socHead;
                        }

                     //SET_CBIT(socials_index[rnum]->cmd_list, hour);

                     SET_BIT(set_command_index[rnum], SOC_TIMED);
                     return;
                  }

               default:
                  dump_core();
               }
            break;
            }
         }
      }
}

/* return TRUE if any arguments have been read.. */

char *read_social_args(void)
{

   unsigned int strsize;
   char         *arg;

   if(fget_line(sf, sBuf, MAX_STRING_LENGTH) == -1)
      {
      logit(LOG_DEBUG, "Error reading world.soc past line %lu.\n", line);
      dump_core();
      }
   line++;


   if(sBuf[0] != '~')
      return NULL;

   if(fget_line(sf, sBuf, MAX_STRING_LENGTH) == -1)
      {
      logit(LOG_DEBUG, "Error reading world.soc past line %lu.\n", line);
      dump_core();
      }
   line++;

   /* if we hit a tilde we've got no args to read.. so we ignore it
    and go to the next line                                        */

   if(sBuf[0] != '~')
      {
      strsize = strlen(sBuf);
      if(strsize != 0)
         {
         /* Alloc mem for the arg string only if we know we got a valid mobile */
         if(save)
            {
            CREATE(arg, char, strsize + 1);
            strcpy(arg, sBuf);
            }
         }
      else
         {
         logit(LOG_DEBUG, "Error while reading world.soc: argument field empty on line %lu.\n", line);
         dump_core();
         }
      /* now read the remaining tilde char */
      if(fget_line(sf, sBuf, MAX_STRING_LENGTH) == -1)
         {
         logit(LOG_DEBUG, "Error reading world.soc: Argument Closing tilde not found as expected on line %lu.\n", line);
         dump_core();
         }

      /* and the next thing that follows it to keep it all sync'd */
      if(fget_line(sf, sBuf, MAX_STRING_LENGTH) == -1)
         {
         logit(LOG_DEBUG, "Error reading world.soc: Argument Closing tilde not found as expected on line %lu.\n", line);
         dump_core();
         }
      line++;
      }
   else
      return NULL;

   return arg;

}


void assign_the_socials()
{
   int i;

   for(i = 0; i < top_of_mobt; i++)
      if(socials_index[i])
         {
         if(IS_SET(set_command_index[i], SOC_TRIGGER))
            AddProcMob(mob_index[i].virtual, reply_proc, "reply_proc");
         if(IS_SET((set_command_index[i]), SOC_PERIODIC))
            AddProcMob(mob_index[i].virtual, periodic_proc, "periodic_proc");
         if(IS_SET((set_command_index[i]), SOC_LIST))
            AddProcMob(mob_index[i].virtual, list_proc, "list_proc");
         if(IS_SET((set_command_index[i]), SOC_TIMED))
            AddProcMob(mob_index[i].virtual, timed_proc, "timed_proc");

         }
}



int reply_proc(P_char mob, P_char ch, int cmd, char *arg)
{
   int calltype, taction = 0;
   struct socData_trigger *data, *orgData;
   char Gbuf1[MAX_STRING_LENGTH], name[MAX_INPUT_LENGTH];
   bool pathChain = FALSE;


   PARSE_ARG(cmd, calltype, cmd);

   if(calltype == PROC_INITIALIZE)
      return IDX_COMMAND;

   /* Want this as close to the start of the fn as possible */
   if(cmd && (!socials_index[mob->nr] || !IS_CSET(socials_index[mob->nr]->cmd_list, cmd)))
      return FALSE;

   if((calltype == 0) && AWAKE(mob) && (mob->in_room != NOWHERE))
      {

      /* get the first damn element from the head.. */
      data = socials_index[mob->nr]->trigger;

      if(!data)
         {
         logit(LOG_DEBUG, "[socials_proc]: No socials data exists for mob vnum: %d\n", mob_index[mob->nr].virtual);
         dump_core();
         }

      /* bail out if the FROM_MOB flag is set and the dude that triggered us is
       * a PC or a Pet */
      if(IS_SET(data->flag, FROM_MOB))
         {
         if(IS_PC(ch))
            return FALSE;
         if(ch->following)
            if(IS_PC(ch->following))
               return FALSE;
         }

      /* bail out if the FROM_PC flag is set.. */
      if(IS_SET(data->flag, FROM_PC))
         {
         if(IS_NPC(ch))
            return FALSE;
         }

      /* This one loops thru all the different commands */
      do
         {
         if(cmd == data->trigger_action &&
            (data->chance ? (!number(0, data->chance)) : TRUE))
            {

            taction = data->trigger_action;

            if(IS_SET(data->flag, DIRECTED) && arg)
               {
               half_chop(arg, name, Gbuf1);
               if(mob != get_char_room_vis(ch, name))
                  break;
               }

            orgData = data;
            do
               {
               if((data->response_action) == CMD_SOC_PATH)
                  pathChain = TRUE;
               if(IS_FIGHTING(mob))
                  {
                  if(!IS_SET(data->flag, NON_COMBAT_ONLY))
                     socials_doAction(mob, ch, data->arg, data->delay, data->response_action, data->arg, data->flag, taction);
                  }
               else
                  {
                  if(!IS_SET(data->flag, COMBAT_ONLY))
                     socials_doAction(mob, ch, data->arg, data->delay, data->response_action, data->arg, data->flag, taction);
                  }
               // Bail out if we hit a path init - will be re-activated when it's finished
               if(pathChain)
                  return FALSE;
               data = data->chainNext;  /* chain to next action in reply to this trigger */
               } while(data);
            data = orgData;
            }
         data = data->next;
         } while(data);

      }

   return FALSE;

}


int periodic_proc(P_char mob, P_char ch, int cmd, char *arg)
{
   int calltype, bailout = FALSE;
   struct socData_periodic *data, *orgData;


   PARSE_ARG(cmd, calltype, cmd);

   if(calltype == PROC_INITIALIZE)
      return IDX_PERIODIC;


   if((calltype == PROC_EVENT) && AWAKE(mob) && (mob->in_room != NOWHERE))
      {

      /* get the first element from the head.. */
      data = socials_index[mob->nr]->periodic;
      if(!data)
         dump_core();

      do
         {
         if(!number(0, data->chance))
            {
            orgData = data;
            do
               {
               if(IS_FIGHTING(mob))
                  {
                  if(!IS_SET(data->flag, NON_COMBAT_ONLY))
                     socials_doAction(mob, ch, data->arg, data->delay, data->action, data->arg, data->flag, 0);
                  }
               else
                  {
                  if(!IS_SET(data->flag, COMBAT_ONLY))
                     socials_doAction(mob, ch, data->arg, data->delay, data->action, data->arg, data->flag, 0);
                  }
               /* want to finish doing the current chain then exit on next command */
               if(!IS_SET(data->flag, BLOCK_OTHER))
                  bailout = TRUE;
               data = data->chainNext;  /* chain to next action in reply to this trigger */
               } while(data);
            data = orgData;
            }

         if(bailout)
            break;
         data = data->next;
         } while(data);

      }

   return FALSE;

}



/* In this case the idea is to avoid all the IDX_PERIODIC calls in order to
 * simply find out if the time is right to process the timed commands..
 * So we just calculate the time period untill it should be triggered and
 * schedule an event to call performTimedProc..                           */
int timed_proc(P_char mob, P_char ch, int cmd, char *arg)
{
   int calltype;
   struct socData_timed *data;
   unsigned long timeDelay;
   int initHour = 0, initMinute = 0;
   int targHour = 0;

   PARSE_ARG(cmd, calltype, cmd);

   if(calltype == PROC_INITIALIZE)
      {

      // Get the timed data for this mob
      data = socials_index[mob->nr]->timed;

      if(!data)
         {
         return FALSE;
         }

      // Init current time vars
      initHour   = time_info.hour;
      initMinute = time_info.minute;

      // Loop through and add events for every timed command this mob has
      do
         {
         targHour = data->hour;

         if(targHour > initHour)
            {
            timeDelay = ((targHour - initHour) * SECS_PER_MUD_HOUR * WAIT_SEC) -
                        (initMinute * WAIT_SEC);
            }
         else
            {
            timeDelay = (24 * SECS_PER_MUD_HOUR * WAIT_SEC) -
                        (initHour * SECS_PER_MUD_HOUR * WAIT_SEC) - (initMinute * WAIT_SEC)
                        + (targHour * SECS_PER_MUD_HOUR * WAIT_SEC);
            }

         AddEvent(EVENT_CHAR_EXECUTE, timeDelay, TRUE, mob, performTimedProc);

         data = data->next;
         } while(data);

      return FALSE;
      }

   /* should never get here, otherwise something's fucked.. */
   dump_core();

   return FALSE;
}


void path_timed_socials(P_char mob, int path_id, int hour)
{
   struct socData_timed *data, *orgData;
   bool pathChain = FALSE, found = FALSE;
   int i = 0;

   data = socials_index[mob->nr]->timed;

   if(!data)
      {
      logit(LOG_DEBUG, "[socials_proc]: No socials data exists for mob vnum: %d\n", mob_index[mob->nr].virtual);
      dump_core();
      }

   if((mob->in_room == NOWHERE))
      return;

   // Loop through all timed commands, looking for the one that matches curHour
   // When found, execute the chain of actions

   do
      {
      if(data->hour == hour && (data->chance ? (!number(0, data->chance)) : TRUE))
         {

         orgData = data;

         // Loop through this chain
         do
            {

            if(data->arg)
               i = atoi(data->arg);

            // Find where we left off
            if(((data->response_action) == CMD_SOC_PATH) && (i == path_id))
               {
               found = TRUE;
               data = data->chainNext;
               continue;
               }

            if(found)
               {
               if((data->response_action) == CMD_SOC_PATH)
                  pathChain = TRUE;

               if(IS_FIGHTING(mob))
                  {
                  if(!IS_SET(data->flag, NON_COMBAT_ONLY))
                     socials_doAction(mob, NULL, data->arg, data->delay, data->response_action, data->arg, data->flag, (hour + TIMED_FACTOR));
                  }
               else
                  {
                  if(!IS_SET(data->flag, COMBAT_ONLY))
                     socials_doAction(mob, NULL, data->arg, data->delay, data->response_action, data->arg, data->flag, (hour + TIMED_FACTOR));
                  }

               // Bail out if we hit a path init - will be re-activated when it's finished
               if(pathChain)
                  {
                  return;
                  }
               }
            data = data->chainNext;
            } while(data);
         data = orgData;
         }
      data = data->next;
      } while(data);

   return;
}

int performTimedProc(P_char mob)
{

   int curHour = 0, curMinute = 0, timeDelay = 0;
   struct socData_timed *data, *orgData;
   bool pathChain = FALSE;

   curHour   = time_info.hour;
   curMinute = time_info.minute;
   timeDelay = (24 * SECS_PER_MUD_HOUR * WAIT_SEC);

   data = socials_index[mob->nr]->timed;

   if(!data)
      {
      logit(LOG_DEBUG, "[socials_proc]: No socials data exists for mob vnum: %d\n", mob_index[mob->nr].virtual);
      dump_core();
      }

   if((mob->in_room == NOWHERE))
      return FALSE;

   // Loop through all timed commands, looking for the one that matches curHour
   // When found, execute the chain of actions

   do
      {
      if(data->hour == curHour && (data->chance ? (!number(0, data->chance)) : TRUE))
         {

         orgData = data;

         // Loop through this chain
         do
            {
            if((data->response_action) == CMD_SOC_PATH)
               pathChain = TRUE;

            if(IS_FIGHTING(mob))
               {
               if(!IS_SET(data->flag, NON_COMBAT_ONLY))
                  socials_doAction(mob, NULL, data->arg, data->delay, data->response_action, data->arg, data->flag, (curHour + TIMED_FACTOR));
               }
            else
               {
               if(!IS_SET(data->flag, COMBAT_ONLY))
                  socials_doAction(mob, NULL, data->arg, data->delay, data->response_action, data->arg, data->flag, (curHour + TIMED_FACTOR));
               }

            // Bail out if we hit a path init - will be re-activated when it's finished
            if(pathChain)
               {
               AddEvent(EVENT_CHAR_EXECUTE, timeDelay, TRUE, mob, performTimedProc);
               return FALSE;
               }

            data = data->chainNext;
            } while(data);
         data = orgData;
         }
      data = data->next;
      } while(data);

   // Re-add the event
   AddEvent(EVENT_CHAR_EXECUTE, timeDelay, TRUE, mob, performTimedProc);
   return FALSE;
}

int list_proc(P_char mob, P_char ch, int cmd, char *arg)
{
   int calltype, bailout = FALSE, i;
   struct socData_list *head;
   struct socList *data;


   PARSE_ARG(cmd, calltype, cmd);

   if(calltype == PROC_INITIALIZE)
      return IDX_PERIODIC;


   if((calltype == PROC_EVENT) && AWAKE(mob) && (mob->in_room != NOWHERE))
      {
      
      /* Validate mob->nr is within bounds */
      if(mob->nr < 0 || !socials_index[mob->nr])
         return FALSE;

      head = socials_index[mob->nr]->list;
      if(!head)
         return FALSE;

      /* Check if listArray is valid and has actions */
      if(!head->listArray || head->numActions == 0)
         return FALSE;

      do
         {
         i = number(0, head->numActions - 1);
         if(i < 0 || i >= head->numActions)
            dump_core();

         data = &(head->listArray[i]);

         /* Validate data pointer and chance field */
         if(!data)
            {
            logit(LOG_DEBUG, "NULL data pointer in list_proc for mob %d", mob->nr);
            return FALSE;
            }
         
         /* Validate chance field is within reasonable bounds */
         if(data->chance < 0 || data->chance > 100)
            {
            logit(LOG_DEBUG, "Invalid chance value %d in list_proc for mob %d, index %d", 
                    data->chance, mob->nr, i);
            return FALSE;
            }

         /* Process only the single action from the array - no chaining through next pointers
          * The listArray is a flat array, not a linked list */
         if(!number(0, data->chance))
            {

            if(IS_FIGHTING(mob))
               {
               if(!IS_SET(data->flag, NON_COMBAT_ONLY))
                  socials_doAction(mob, ch, data->arg, data->delay, data->action, data->arg, data->flag, 0);
               }
            else
               {
               if(!IS_SET(data->flag, COMBAT_ONLY))
                  socials_doAction(mob, ch, data->arg, data->delay, data->action, data->arg, data->flag, 0);
               }

            /* want to finish doing the current chain then exit on next command */
            if(!IS_SET(data->flag, BLOCK_OTHER))
               bailout = TRUE;

            }


         if(bailout)
            break;
         head = head->nextList;
         } while(head);

      }

   return FALSE;
}


typedef struct socials_zone_echo_s
   {
   char          *text;
   unsigned long target;
   } socials_zone_echo_t;


void socials_zone_indoor_echo(void *arg)
{
   socials_zone_echo_t *sze = (socials_zone_echo_t *) arg;

   send_to_zone_indoor(sze->target, sze->text);

   return;
}

void socials_zone_outdoor_echo(void *arg)
{
   socials_zone_echo_t *sze = (socials_zone_echo_t *) arg;

   send_to_zone_outdoor(sze->target, sze->text);

   return;
}

void socials_zone_echo(void *arg)
{
   socials_zone_echo_t *sze = (socials_zone_echo_t *) arg;

   send_to_zone(sze->target, sze->text);

   return;
}

void socials_room_echo(void *arg)
{
   socials_zone_echo_t *sze = (socials_zone_echo_t *) arg;

   send_to_room(sze->text, sze->target);

   return;
}
/*
void
socials_char_echo(void *arg, P_char ch)
{
  socials_zone_echo_t *sze = (socials_zone_echo_t *) arg;

  send_to_char(sze->text, ch);

  return;
}
*/
/* Performs an action stored in the socials_data structure..
 * If the data->arg is specified the targets stored in the data->flag
 * (USE_INITIATOR, USE_SELF, USE_TARGET) will be ignored..
 */

void socials_doAction(P_char mob, P_char ch, char *cmdarg, int delay, int raction, char *arg, int flag, int taction)
{
   char gbuf[MAX_STRING_LENGTH];
   socials_zone_echo_t *sarg;

   switch(raction)
      {
      case CMD_SOC_ECHOZ_INDOOR: {
            if(!delay)
               send_to_zone_indoor(world[mob->in_room].zone, arg);
            else
               {
               CREATE(sarg, socials_zone_echo_t, 1);
               sarg->text = arg;
               sarg->target = world[mob->in_room].zone;
               AddEvent(EVENT_VOID_FN_EXECUTE, delay, TRUE, socials_zone_indoor_echo, (void *) sarg);
               }
            return;
         }
      case CMD_SOC_ECHOZ_OUTDOOR: {
            if(!delay)
               {
               send_to_zone_outdoor(world[mob->in_room].zone, arg);
               }
            else
               {
               CREATE(sarg, socials_zone_echo_t, 1);
               sarg->text = arg;
               sarg->target = world[mob->in_room].zone;
               AddEvent(EVENT_VOID_FN_EXECUTE, delay, TRUE, socials_zone_outdoor_echo, (void *) sarg);
               }
            return;
         }
      case CMD_SOC_ECHOZ_ALL: {
            if(!delay)
               {
               send_to_zone(world[mob->in_room].zone, arg);
               }
            else
               {
               CREATE(sarg, socials_zone_echo_t, 1);
               sarg->text = arg;
               sarg->target = world[mob->in_room].zone;
               AddEvent(EVENT_VOID_FN_EXECUTE, delay, TRUE, socials_zone_echo, (void *) sarg);
               }
            return;
         }
      case CMD_SOC_PATH: {
            sprintf(gbuf, "%s %d", arg, taction);
            if(!delay)
               {
               socials_init_path(mob, gbuf);
               }
            else
               {
               AddEvent(EVENT_PATH_INIT, delay, TRUE, mob, gbuf);
               }
            return;
         }
      case CMD_SOC_ECHO: {
            if(!delay)
               {
               send_to_room(arg, mob->in_room);
               }
            else
               {
               CREATE(sarg, socials_zone_echo_t, 1);
               sarg->text = arg;
               sarg->target = mob->in_room;
               AddEvent(EVENT_VOID_FN_EXECUTE, delay, TRUE, socials_room_echo, (void *) sarg);
               }
            return;
         }/*
         case CMD_SOC_ECHOT: {
     debuglog(51, DS_EILISTRAEE, "We're in command 1005, ch is %d", ch);
          if (!delay) {
             send_to_char(arg, ch);
           } else {
             CREATE(sarg, socials_zone_echo_t, 1);
             sarg->text = arg;
             AddEvent(EVENT_VOID_FN_EXECUTE, delay, TRUE, socials_char_echo, (void *) sarg);
           }
          return;
         }*/
      }

   snprintf(gbuf, sizeof(gbuf), "%s ", command[raction - 1]);

   /* hmm, odd little thing.  seems to be set up that if the soc takes an arg,
      it ignores USE_* flags.  and by the same token, USE_* flags assume there
      will be no args.  plus, no call to this function differentiates between
      arg and cmdarg - they both pass the same string.  gonna nuke this
      conditional here and incorp it for each USE_* flag below.  1/4/01 --D2 */
#if 0
   if(cmdarg)
      {
      strcat(gbuf, cmdarg);
      AddEvent(EVENT_DELAYED_COMMAND, delay, TRUE, mob, gbuf);
      }
   else
      {
#endif
      if(IS_SET(flag, USE_INITIATOR))
         {
         if(!ch)
            return;
         if(cmdarg && *cmdarg)
            {
            size_t gbuf_len = strlen(gbuf);
            size_t remaining = sizeof(gbuf) - gbuf_len - 1;
            if(remaining > 0)
               {
               strncat(gbuf, cmdarg, remaining);
               gbuf[sizeof(gbuf) - 1] = '\0';
               }
            }
         size_t gbuf_len = strlen(gbuf);
         snprintf(gbuf + gbuf_len, sizeof(gbuf) - gbuf_len, "%s", ch->player.name);
         AddEvent(EVENT_DELAYED_COMMAND, delay, TRUE, mob, gbuf);
         }
      else
         {
         if(IS_SET(flag, USE_SELF))
            {
            if(cmdarg && *cmdarg)
               {
               size_t gbuf_len = strlen(gbuf);
               size_t remaining = sizeof(gbuf) - gbuf_len - 1;
               if(remaining > 0)
                  {
                  strncat(gbuf, cmdarg, remaining);
                  gbuf[sizeof(gbuf) - 1] = '\0';
                  }
               }
            size_t gbuf_len = strlen(gbuf);
            snprintf(gbuf + gbuf_len, sizeof(gbuf) - gbuf_len, "me");
            AddEvent(EVENT_DELAYED_COMMAND, delay, TRUE, mob, gbuf);
            }
         else
            {
            if(IS_SET(flag, USE_TARGET) && IS_FIGHTING(mob))
               {
               if(cmdarg && *cmdarg)
                  {
                  size_t gbuf_len = strlen(gbuf);
                  size_t remaining = sizeof(gbuf) - gbuf_len - 1;
                  if(remaining > 0)
                     {
                     strncat(gbuf, cmdarg, remaining);
                     gbuf[sizeof(gbuf) - 1] = '\0';
                     }
                  }
               size_t gbuf_len = strlen(gbuf);
               snprintf(gbuf + gbuf_len, sizeof(gbuf) - gbuf_len, "%s", mob->specials.fighting->player.name);
               AddEvent(EVENT_DELAYED_COMMAND, delay, TRUE, mob, gbuf);
               }
            else
               {
               if(cmdarg && *cmdarg)
                  {
                  size_t gbuf_len = strlen(gbuf);
                  size_t remaining = sizeof(gbuf) - gbuf_len - 1;
                  if(remaining > 0)
                     {
                     strncat(gbuf, cmdarg, remaining);
                     gbuf[sizeof(gbuf) - 1] = '\0';
                     }
                  }
               AddEvent(EVENT_DELAYED_COMMAND, delay, TRUE, mob, gbuf);
               }
            }
         }
      //  }

      return;
      }




/* Free all memory allocated for socials */
void free_socials(void) {
   int i;
   
   for (i = 0; i < SOCIALS_INDEX_SIZE; i++) {
      if (socials_index[i]) {
         /* Free list array if it exists */
         if (socials_index[i]->list) {
            if (socials_index[i]->list->listArray) {
               free(socials_index[i]->list->listArray);
            }
            free(socials_index[i]->list);
         }
         
         /* Free periodic data if it exists - it's a linked list */
         if (socials_index[i]->periodic) {
            freePeriodic(socials_index[i]->periodic);
         }
         
         /* Free trigger data if it exists - it's a linked list */
         if (socials_index[i]->trigger) {
            freeTriggers(socials_index[i]->trigger);
         }
         
         /* Free timed data if it exists - it's a linked list */
         if (socials_index[i]->timed) {
            freeTimed(socials_index[i]->timed);
         }
         
         /* Free path data if it exists */
         if (socials_index[i]->path) {
            free(socials_index[i]->path);
         }
         
         /* Free error string if it exists */
         if (socials_index[i]->error) {
            free(socials_index[i]->error);
         }
         
         /* Free the head structure itself */
         free(socials_index[i]);
         socials_index[i] = NULL;
      }
   }
}
