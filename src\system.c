/* ***************************************************************************
 *  File: system.c                                           Part of Outcast *
 *  Usage: misc. system related functionality                                *
 *  Copyright  1990, 1991 - see 'license.doc' for complete information.      *
 *  Copyright  1999 - Outcast Systems Ltd by Altherog                        *
 *************************************************************************** */

#include <ctype.h>
#include <stdarg.h>
#include <stdio.h>
#include <string.h>
#include <sys/vfs.h>

#include "prototypes.h"
#include "events.h"


#define    TORIL_MOUNTPOINT             "."
#define    PFILE_MOUNTPOINT             "./Players"
#define    TORIL_WARNING_TRESHOLD       150000
#define    PLAYERS_WARNING_TRESHOLD     10000


extern int port;

struct statfs *getFreeSpaceOn(char *path)
{
  static struct statfs buf;

  if (!path)
    return NULL;

  if (statfs(path, &buf) != 0) {
    wizlog(57, "checkDiscSpace() out of date. Cannot statfs() filesystem mounted on [%s]. Someone go look, eh?", path);
    return NULL;
  }

  return &buf;
}

/* Event to check and notify about low disc space on main and pfile partitions
 *                                                        -- Altherog Jul 99 */

void checkDiscSpace(void)
{
  struct statfs *buf;

  /* dont check if not running on main */
  if (port != DFLT_MAIN_PORT)
    return;

  if ((buf = getFreeSpaceOn(TORIL_MOUNTPOINT)) != NULL) {
    if (buf->f_bavail <= TORIL_WARNING_TRESHOLD)
      wizlog(51, "&+rWARNING&N&+R:&N&+L Disc space on [%s] below %lu [%lu]. Someone go look eh?",
             TORIL_MOUNTPOINT, TORIL_WARNING_TRESHOLD, buf->f_bavail);
  }

  if ((buf = getFreeSpaceOn(PFILE_MOUNTPOINT)) != NULL) {
    if (buf->f_bavail <= PLAYERS_WARNING_TRESHOLD)
      wizlog(51, "&+rWARNING&N&+R:&N&+L Disc space on [%s] below %lu [%lu]. Someone go look eh?",
             PFILE_MOUNTPOINT, PLAYERS_WARNING_TRESHOLD, buf->f_bavail);
  }

  AddEvent(EVENT_SPECIAL, 240, TRUE, checkDiscSpace, 0);

  return;
}

