/* ***************************************************************************
 *  File: board.c                                              Part of Outcast *
 *  Usage: handle look/read/write to all the bulletin boards.                *
 *  Copyright  1990, 1991 - see 'license.doc' for complete information.      *
 *  Copyright  1994-1997 - John <PERSON> & Outcast Systems Ltd.                *
 *************************************************************************** */

#include "board.h"

/* local function prototypes */

static void board_delete_message(P_obj, P_char, char *, int);
static void board_list_messages(P_obj, P_char, int);
static void board_load_board(int);
static void board_show_message(P_obj, P_char, char *, int);
static void board_start_writing(P_obj, P_char, char *, int);

static time_t ct;
static char *tmstr;

struct hsearch_data Board_hash[1] = {{NULL, 0, 0}};
ENTRY hash_entries[BOARD_COUNT];

/* to add a new board, increment BOARD_COUNT in config.h, and add a line with the filename and Vnum of the board
 * object to the list under 'Board_init'.  Order does not matter (but inserting them in Vnum-order will keep me
 * happy).  Much easier than before.  JAB */

struct board_data Boards[BOARD_COUNT];
const struct board_init Board_init[BOARD_COUNT] = {
   {"lib/boards/www.messages",               73},
   {"lib/boards/clangboard.messages",        74},
   {"lib/boards/zoneboard.messages",         75},
   {"lib/boards/planning.messages",          76},
   {"lib/boards/personal.messages",          77},
   {"lib/boards/a-f.messages",               78},
   {"lib/boards/g-l.messages",               79},
   {"lib/boards/m-r.messages",               80},
   {"lib/boards/s-z.messages",               81},
   {"lib/boards/flea.messages",              82},
   {"lib/boards/story.messages",             83},
   {"lib/boards/bugboard.messages",          84},
   {"lib/boards/troubleboard.messages",      85},
   {"lib/boards/fixedbugsboard.messages",    86},
   {"lib/boards/forger.messages",            87},
   {"lib/boards/reimb.messages",             88},
   {"lib/boards/god.messages",               89},
   {"lib/boards/board.messages",             90},
   {"lib/boards/coder.messages",             91},
   {"lib/boards/quest.messages",             92},
   //  {"lib/boards/guilds.messages",            93},
   {"lib/boards/alignment.messages",         96},
   {"lib/boards/adminproject.messages",      97},
   {"lib/boards/questsession.messages",      98},
   {"lib/boards/questcampaign.messages",     99},
   {"lib/boards/barterboardWD.messages",   1236},
   {"lib/boards/barterboardEM.messages",   1237},
   {"lib/boards/barterboardMH.messages",   1238},
   {"lib/boards/barterboardAS.messages",   1239},
   {"lib/boards/barterboardLU.messages",   1240},
   {"lib/boards/barterboardCA.messages",   1241},
   {"lib/boards/barterboardBS.messages",   1242},
   {"lib/boards/barterboardBG.messages",   1243},
   {"lib/boards/barterboardZK.messages",   1244},
   {"lib/boards/barterboardGL.messages",   1245},
   {"lib/boards/barterboardFA.messages",   1246},
   {"lib/boards/barterboardGH.messages",   1247},
   {"lib/boards/barterboardIX.messages",   1248},
   {"lib/boards/barterboardVT.messages",   1249},
   {"lib/boards/barterboardHY.messages",   1250},
   {"lib/boards/barterboardGN.messages",   1251},
   //  {"lib/boards/sister.messages",         16600},
   {"lib/boards/blip.messages",           41911},
   //  {"lib/boards/saber.messages",          75000},
   //  {"lib/boards/swamp.messages",          75103},
   //  {"lib/boards/lyre.messages",           75201},
   //  {"lib/boards/wyrm.messages",           75300},
   //  {"lib/boards/oath.messages",           75401},
   //  {"lib/boards/errant.messages",         75502},
   //  {"lib/boards/vs.messages",             75707},
   //  {"lib/boards/flame.messages",          75802},
};

/* This function initializes everything necessary to use the board, including a Vnum to array index hash table. */

void initialize_boards(void)
{
   ENTRY *ret_hash;
   char tbuf1[12], tbuf2[12];
   int i;

   bzero(Boards, sizeof(struct board_data) * BOARD_COUNT);
   if(!hcreate_r(BOARD_COUNT, Board_hash))
      dump_core();

   for(i = 0; i < BOARD_COUNT; i++)
      {
      AddProcObj((int)Board_init[i].Vnum, board, "board");

      sprintf(tbuf1, "%d", Board_init[i].Vnum);
      sprintf(tbuf2, "%d", i);
      hash_entries[i].key = str_dup(tbuf1);
      hash_entries[i].data = str_dup(tbuf2);
      if(!hsearch_r(hash_entries[i], ENTER, &ret_hash, Board_hash))
         dump_core();  /* out of memory */

      board_load_board(i);
      }
}

void board_save_board(int board_idx)
{
   FILE *the_file;
   int i, len;

   if((board_idx < 0) || (board_idx >= BOARD_COUNT))
      dump_core();

   if(!(the_file = fopen(Board_init[board_idx].filename, "w")))
      {
      logit(LOG_BOARD, "Unable to update %s.", Board_init[board_idx].filename);
      return;
      }

   fwrite((void *)&Boards[board_idx].num_msgs, sizeof(int), 1, the_file);

   for(i = 0; i < Boards[board_idx].num_msgs; i++)
      {
      len = strlen(Boards[board_idx].msg[i].title) + 1;
      fwrite(&len, sizeof(int), 1, the_file);
      fwrite(Boards[board_idx].msg[i].title, sizeof(char), (unsigned) len, the_file);

      if(Boards[board_idx].msg[i].body)
         {
         Boards[board_idx].msg[i].not_saved = 0;
         len = strlen(Boards[board_idx].msg[i].body) + 1;
         fwrite(&len, sizeof(int), 1, the_file);
         fwrite(Boards[board_idx].msg[i].body, sizeof(char), (unsigned) len, the_file);
         }
      else
         {
         len = 0;
         fwrite(&len, sizeof(int), 1, the_file);
         }
      }

   fclose(the_file);
   return;
}

static void board_load_board(int board_idx)
{
   FILE *the_file;
   char buf[MAX_STRING_LENGTH];
   int i, len = 0;

   if((board_idx < 0) || (board_idx >= BOARD_COUNT))
      {
      logit(LOG_EXIT, "Illegal board count (%d) in board_load_board().", board_idx);
      dump_core();
      }

   if(!(the_file = fopen(Board_init[board_idx].filename, "r")))
      {
      logit(LOG_BOARD, "Can't open message file (%s).  Board will be empty.", Board_init[board_idx].filename);
      return;
      }

   fread(&len, sizeof(int), 1, the_file);

   if((len < 0) || (len > MAX_MSGS) || feof(the_file))
      {
      logit(LOG_BOARD, "Board-message file (%s) corrupt or nonexistent.  Board will be empty.",
            Board_init[board_idx].filename);
      fclose(the_file);
      return;
      }

   Boards[board_idx].num_msgs = len;

   for(i = 0; i < Boards[board_idx].num_msgs; i++)
      {
      fread(&len, sizeof(int), 1, the_file);

      if(len >= MAX_STRING_LENGTH)
         {
         logit(LOG_BOARD, "Board file (%s) corrupt.  Truncating at %d messages.", Board_init[board_idx].filename, i);
         Boards[board_idx].num_msgs = i;
         break;
         }

      if(len != 0)
         {
         fread(buf, sizeof(char), (unsigned) len, the_file);
         Boards[board_idx].msg[i].title = str_dup(buf);
         }

      fread(&len, sizeof(int), 1, the_file);
      if(len >= MAX_STRING_LENGTH)
         {
         logit(LOG_BOARD, "Board file (%s) corrupt.  Truncating at %d messages.", Board_init[board_idx].filename, i);
         free_string(Boards[board_idx].msg[i].title);
         Boards[board_idx].num_msgs = i;
         break;
         }

      if(len != 0)
         {
         fread(buf, sizeof(char), (unsigned) len, the_file);
         Boards[board_idx].msg[i].body = str_dup(buf);
         }
      }

   fclose(the_file);
   return;
}

/* ok, the original board() was an absolutely horrible hack by diku's Quinn, it did wonderful things like
 * changing the board's long description into a list of the current messages and sending that to the player when
 * the board was looked at.  It also hardcoded in the command numbers, set pointers to the addresses of constant
 * structures, and several other unsightly and highly dangerous things.  This completely rewritten version is
 * hack-free and much better behaved.  Despite what it may look like, this is pretty much a standard object special
 * proc.  JAB */

int board(P_obj obj, P_char ch, int cmd, char *arg)
{
   ENTRY this_entry, *ret_hash;
   P_obj t_obj = NULL;
   char tbuf[12], buf[MAX_INPUT_LENGTH];
   int calltype = cmd, old_procs = TRUE, board_idx, found = FALSE;
   struct func_attachment *fn;

   PARSE_ARG(cmd, calltype, cmd);
   old_procs = FALSE;

   /* check for periodic event calls */
   if(calltype == PROC_INITIALIZE)
      return old_procs ? FALSE : IDX_COMMAND;

   if((calltype != PROC_COMMAND)
      || !obj
      || !arg
      || !*arg
      || !ch
      || !ch->desc
      || IS_NPC(ch)
      || (ch->in_room == NOWHERE)
      || !CAN_SEE_OBJ(ch, obj)
      || ((cmd != CMD_LOOK)
          && (cmd != CMD_WRITE)
          && (cmd != CMD_READ)
          && (cmd != CMD_DELETE)))
      return FALSE;

   /* this is the only really bad part, to allow the use of multiple boards in one room, we have to require a
    * command format of:  <cmd> <board> <args>
    * which of course sucks, since there is usually only one board in a room, soooo...
    * if the <board> arg is missing, the first object in the room with the 'board' proc attached is picked.
    * So far, so good, now if the <board> arg is NOT missing, we have to figure out which board object *we* are,
    * and decide if this command is meant for us or not.
    * JAB
    */

   one_argument(arg, buf);
   if(!(t_obj = get_obj_in_list_vis(ch, buf, world[ch->in_room].contents)))
      {

      /* first arg in arg is NOT a valid keyword, so we assume that the first object in room with board proc is the
       * intended target.  Of course, finding an object by it's proc is not exactly a high demand area.  JAB */

      /* except for 'look' command, if we do that, then the only things you can look at in a room with boards, is a
       * board. */

      if(cmd == CMD_LOOK)
         return FALSE;

      /* there IS an additional problem, you cannot read or write anything EXCEPT a board, in a room with boards,
       * however, unless I REQUIRE an object argument (read board #), there is not way around this, so I'm electing
       * to leave it in place.  If you want to read/write something else, leave the room (or purge the boards).
       * Delete is not a problem, it's only used for boards.  JAB */

      for(obj = world[ch->in_room].contents; obj; obj = obj->next_content)
         if(CAN_SEE_OBJ(ch, obj))
            {
            for(fn = obj_index[obj->R_num].func; !found && fn; fn = fn->next)
               if(fn->func.obj == board)
                  {
                  found = TRUE;
                  t_obj = obj;
                  }

            if(found)
               break;
            }

      if(!t_obj)
         {
         /* wanker can't see a valid board, blah */
         return FALSE;
         }
      }
   else
      {
      if(t_obj == obj)
         arg = one_argument(arg, buf);
      }

   if(t_obj != obj)
      return FALSE;

   /* ok, either we have a horrible coincidence, or this command is actually intended for us. */

   sprintf(tbuf, "%d", obj_index[obj->R_num].virtual);
   this_entry.key = tbuf;
   if(!hsearch_r(this_entry, FIND, &ret_hash, Board_hash))
      {
      /* most likely, someone added a board proc outside of initialize_boards, thus the board Vnum did not get
         added to the hash table.  So we yell and bomb. JAB */
      logit(LOG_EXIT, "Bad board proc assignment (Vnum %s) in board special!", tbuf);
      dump_core();
      }

   board_idx = atoi(ret_hash[0].data);

   /* note that we are the ONLY place these 4 functions are called from, so they assume quite a bit about the
    * reliability of their args.  All 4 are static functions too, just to make sure things stay sane.  JAB */

   switch(cmd)
      {
      case CMD_LOOK:
         board_list_messages(obj, ch, board_idx);
         break;
      case CMD_WRITE:
         board_start_writing(obj, ch, arg, board_idx);
         break;
      case CMD_READ:
         board_show_message(obj, ch, arg, board_idx);
         break;
      case CMD_DELETE:
         board_delete_message(obj, ch, arg, board_idx);
         break;
      default:
         return FALSE;

      }

   return TRUE;
}

static void board_start_writing(P_obj obj, P_char ch, char *arg, int board_idx)
{
   char buf[MAX_STRING_LENGTH];
   int msg_nr;
   struct writing_info *writing;

   /* ch, obj and board_idx are guaranteed to be valid, arg may be too long, or something, so check */

   if(obj->value[1] > GET_LEVEL(ch))
      {
      send_to_char("Sorry, you aren't awesome enough to write on this board.\n", ch);
      return;
      }

   if((msg_nr = Boards[board_idx].num_msgs) >= MAX_MSGS)
      {
      send_to_char("The board is full already.\n", ch);
      return;
      }

   if(ch->only.pc->writing)
      {
      send_to_char("Sorry, you can only write one thing at a time!\n", ch);
      return;
      }

   /* skip blanks */
   for(; isspace(*arg); arg++);
   if(!*arg)
      {
      send_to_char("Usage:\n"
                   "write [board] <title>\n"
                   "   If you exclude the [board] argument, the first board in the room is assumed.\n"
                   "   The <title> argument is required for boards.\n", ch);
      return;
      }

   if(strlen(arg) > 55)
      {  /* to leave room for number, name, date */
      send_to_char("Titles must be less than 56 characters long.\n", ch);
      return;
      }

   send_to_char("Write your message. Terminate with '@@'.\n\n", ch);
   act("$n starts to write a message.", TRUE, ch, 0, 0, TO_ROOM);

   ct = time(0);
   {
      struct tm *tm_info = localtime(&ct);
      if (!tm_info) {
         send_to_char("Error: System time unavailable. Please try again later.\n", ch);
         return;
      }
      tmstr = asctime(tm_info);
   }
   *(tmstr + strlen(tmstr) - 9) = '\0';  /* kill seconds and year */

   sprintf(buf, "[%s (%s)] %s", tmstr, GET_NAME(ch), arg);
   Boards[board_idx].msg[msg_nr].title = str_dup(buf);
   Boards[board_idx].msg[msg_nr].body = NULL;
   Boards[board_idx].msg[msg_nr].not_saved = 1;
   Boards[board_idx].msg[msg_nr].author = ch;

   CREATE(writing, struct writing_info, 1);
   writing->what = WRT_BOARD;
   writing->writer = ch;
   writing->text_start = &Boards[board_idx].msg[msg_nr].body;
   writing->max_length = MAX_MESSAGE_LENGTH;
   writing->board_idx = board_idx;
   writing->msg_nr = msg_nr;

   ch->only.pc->writing = writing;

   /* note, no EVENT_STRINGING for board messages, just the writing_info struct, EVENT_STRINGING on a char indicates
    * that the CHARACTER is being strung, not that the character is stringing. */

   Boards[board_idx].num_msgs++;
}

static void board_delete_message(P_obj obj, P_char ch, char *arg, int board_idx)
{
   int i, msg_nr;
   char buf[MAX_INPUT_LENGTH], buf2[MAX_INPUT_LENGTH], *s, *s2;

   if(obj->value[0] > GET_LEVEL(ch))
      {
      send_to_char("What messages?\n", ch);
      return;
      }

   if(!Boards[board_idx].num_msgs)
      {
      send_to_char("The board is empty!\n", ch);
      return;
      }

   one_argument(arg, buf2);

   if(!*buf2 || !isdigit(*buf2) || !(msg_nr = atoi(buf2)))
      {
      send_to_char("Usage:\n"
                   "delete [board] <message number>\n"
                   "   If you exclude the [board] argument, the first board in the room is assumed.\n", ch);
      return;
      }

   if((msg_nr < 1) || (msg_nr > Boards[board_idx].num_msgs))
      {
      sprintf(buf, "There %s only %d message%s on this board!\n",
              (Boards[board_idx].num_msgs > 1) ? "are" : "is",
              Boards[board_idx].num_msgs,
              (Boards[board_idx].num_msgs > 1) ? "s" : "");
      send_to_char(buf, ch);
      return;
      }

   msg_nr--;

   if(Boards[board_idx].msg[msg_nr].not_saved)
      {
      sprintf(buf, "At least wait until %s finishes writing it!\n",
              GET_NAME(Boards[board_idx].msg[msg_nr].author));
      send_to_char(buf, ch);
      return;
      }

   if(obj->value[2] > GET_LEVEL(ch))
      {
      send_to_char("Sorry, you aren't awesome enough to delete messages (even your own)!\n", ch);
      return;
      }

   if(obj->value[3] > GET_LEVEL(ch))
      {
      s = s2 = 0;
      s = (char *) strchr(Boards[board_idx].msg[msg_nr].title, '(');
      if(s)
         s2 = (char *) strchr(s, ')');
      if(!s || !s2 || strn_cmp(s + 1, GET_NAME(ch), (unsigned) (s2 - s - 1)))
         {
         send_to_char("You are far too feeble to delete other people's messages.\n", ch);
         return;
         }
      }

   if(Boards[board_idx].msg[msg_nr].title)
      {
      sprintf(buf, "Deleting %d] '%s'.\n", msg_nr + 1, Boards[board_idx].msg[msg_nr].title);
      send_to_char(buf, ch);
      free_string(Boards[board_idx].msg[msg_nr].title);
      Boards[board_idx].msg[msg_nr].title = NULL;
      }
   else
      {
      sprintf(buf, "Deleting %d].\n", msg_nr + 1);
      send_to_char(buf, ch);
      }

   sprintf(buf, "$n just removed message %d from %s.", msg_nr + 1, obj->description);
   act(buf, FALSE, ch, 0, 0, TO_ROOM);

   if(Boards[board_idx].msg[msg_nr].body)
      {
      free_string(Boards[board_idx].msg[msg_nr].body);
      Boards[board_idx].msg[msg_nr].body = NULL;
      }


   if(Boards[board_idx].num_msgs != (msg_nr + 1))
      for(i = msg_nr; i < (Boards[board_idx].num_msgs - 1); i++)
         {
         Boards[board_idx].msg[i].title = Boards[board_idx].msg[i + 1].title;
         Boards[board_idx].msg[i].body = Boards[board_idx].msg[i + 1].body;
         Boards[board_idx].msg[i].not_saved = Boards[board_idx].msg[i + 1].not_saved;
         Boards[board_idx].msg[i].author = Boards[board_idx].msg[i + 1].author;
         if(Boards[board_idx].msg[i].not_saved && Boards[board_idx].msg[i].author)
            {
            Boards[board_idx].msg[i].author->only.pc->writing->msg_nr--;
            Boards[board_idx].msg[i].author->only.pc->writing->text_start = &Boards[board_idx].msg[i].body;
            }
         }

   Boards[board_idx].num_msgs--;

   /* clear off the last entry so things don't get freed twice, later along. */

   Boards[board_idx].msg[Boards[board_idx].num_msgs].title = NULL;
   Boards[board_idx].msg[Boards[board_idx].num_msgs].body = NULL;
   Boards[board_idx].msg[Boards[board_idx].num_msgs].not_saved = FALSE;
   Boards[board_idx].msg[Boards[board_idx].num_msgs].author = NULL;

   board_save_board(board_idx);

   return;
}

static void board_show_message(P_obj obj, P_char ch, char *arg, int board_idx)
{
   char buf[MAX_STRING_LENGTH], buf2[MAX_INPUT_LENGTH];
   int msg_nr;

   if(!Boards[board_idx].num_msgs || (obj->value[0] > GET_LEVEL(ch)))
      {
      send_to_char("The board is empty!\n", ch);
      return;
      }

   one_argument(arg, buf2);
   if(!*buf2 || !isdigit(*buf2) || !(msg_nr = atoi(buf2)))
      {
      send_to_char("Usage:\n"
                   "read [board] <message number>\n"
                   "   If you exclude the [board] argument, the first board in the room is assumed.\n", ch);
      return;
      }

   if((msg_nr < 1) || (msg_nr > Boards[board_idx].num_msgs))
      {
      sprintf(buf, "There %s only %d message%s on this board!\n",
              (Boards[board_idx].num_msgs > 1) ? "are" : "is",
              Boards[board_idx].num_msgs,
              (Boards[board_idx].num_msgs > 1) ? "s" : "");
      send_to_char(buf, ch);
      return;
      }

   msg_nr--;
   sprintf(buf, "Message #%d : %s\n\n%s",
           msg_nr + 1, Boards[board_idx].msg[msg_nr].title, Boards[board_idx].msg[msg_nr].body);
   page_string(ch->desc, buf, 1);
   return;
}

static void board_list_messages(P_obj obj, P_char ch, int board_idx)
{
   char buf[MAX_STRING_LENGTH];
   int i;

   if(obj->value[0] > GET_LEVEL(ch))
      {
      send_to_char("Sorry, you aren't awesome enough to even look at this board.\n", ch);
      return;
      }

   act("$n studies the board.", TRUE, ch, 0, 0, TO_ROOM);

   if(!Boards[board_idx].num_msgs)
      {
      send_to_char("The board is empty.\n", ch);
      return;
      }
   else
      {
      sprintf(buf, "There %s %d message%s on this board.\n",
              (Boards[board_idx].num_msgs > 1) ? "are" : "is",
              Boards[board_idx].num_msgs,
              (Boards[board_idx].num_msgs > 1) ? "s" : "");
      for(i = Boards[board_idx].num_msgs; i > 0; i--)
         sprintf(buf + strlen(buf), "%3d] %s\n", i, Boards[board_idx].msg[i - 1].title);
      }

   page_string(ch->desc, buf, 1);
}

