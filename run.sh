#!/bin/sh
#
# Outcast MUD autorun script
# Simplified version that works with existing shutdown commands
# 
# This script will automatically restart the MUD if it crashes or reboots.
# Use "shutdown ok" in game to stop the MUD permanently.
# Use "shutdown reboot" in game to restart the MUD.
#
# You can manually control this script by creating these files:
# - touch .killscript  : stops the autorun script
# - touch pause        : pauses autorun until file is removed
#

# Enable core dumps
ulimit -c unlimited

# The port on which to run the MUD
PORT=9999

# Default flags to pass to the MUD server
FLAGS='-d .'

########
# Main #
########

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "$0")" && pwd)"
cd "$SCRIPT_DIR"

# Set core dump pattern to core.<pid> if we have permission
if [ -w /proc/sys/kernel/core_pattern ]; then
  echo "core.%p" > /proc/sys/kernel/core_pattern
fi

# Create necessary directories
mkdir -p cores

# The main loop
while ( : ) do

  # Check if ocm is already running
  run=$(ps -A | grep -v grep | grep -c "ocm")
  if [ "$run" -ge "1" ]; then
    echo "OCM is already running, waiting..."
    sleep 60
    continue
  fi

  # Check for manual killscript
  if [ -r .killscript ]; then
    DATE=`date`;
    echo "autoscript terminated by .killscript file $DATE"
    rm .killscript
    exit
  fi

  # Pause if requested
  while [ -r pause ]; do
    echo "Autorun paused... remove 'pause' file to continue"
    sleep 60 
  done

  DATE=`date`
  echo "autorun starting game $DATE"
  echo "running ./ocm $FLAGS -port $PORT"

  # Run the MUD and capture exit code
  ./ocm $FLAGS -port $PORT
  EXIT_CODE=$?

  DATE=`date`
  echo "Game exited with code $EXIT_CODE at $DATE"

  # Check for core dumps and create backtrace if found
  if [ -s core ]; then 
    echo "Core dump detected, saving..."
    mv core cores/core.$(date +%d.%m.%Y.%T)
    # If gdb is available, generate backtrace
    if command -v gdb >/dev/null 2>&1; then
      echo "bt" > gdb.tmp
      echo "quit" >> gdb.tmp
      gdb ./ocm cores/core.$(date +%d.%m.%Y.%T) -command gdb.tmp >cores/backtrace.$(date +%d.%m.%Y.%T) 2>&1
      rm -f gdb.tmp
    fi
  fi

  # Wait before restarting (shorter wait for crashes)
  if [ $EXIT_CODE -ne 0 ]; then
    echo "Crash detected, restarting in 5 seconds..."
    sleep 5
  else
    echo "Clean exit, restarting in 60 seconds..."
    echo "Create .killscript file to stop autorun, or 'pause' file to pause"
    sleep 60
  fi

done
