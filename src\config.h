/* ***************************************************************************
 *  File: config.h                                             Part of Outcast *
 *  Usage: global configuration options and settings.                        *
 *  Copyright  1995 - Outcast Systems Ltd.                                   *
 *************************************************************************** */

#ifndef _SOJ_CONFIG_H
#define _SOJ_CONFIG_H

/* couple of basic things */

#define ALPHA_MODE   0     // this is the master switch to control alpha mode setup
                           // it controls things like loading newbies to VT, and bypasses the stats roller
                           // also turns on OOC for all by force, and sets ooc to lvl 1


#ifndef FALSE
   #define FALSE (0)          /* pretty damn basic */
#endif
#ifndef TRUE
   #define TRUE (1)           /* pretty damn basic */
#endif

/* default mother socket number */
#define DFLT_MAIN_PORT 9999          /* default main port */
#define DFLT_TEST_PORT 4000          /* default test port */

/* configurable options */
#undef AZUTHSQL
#define STORAGE_LOG
#undef AREAS_DEBUG
#define  REPLY                   // Is Reply working? (not likely)
#define CONTRACTS                /* Is Contract Code working? */
#undef  CONFIG_JAIL             /* are the jails working? */
#define ERANDOM                 /* enable BSD-sytle pseudo-random nuber generator */
#define EXP_TROPHY              /* keep track of kills and limit XP from repetitions */
#undef  FIGHT_DEBUG             /* for step by step debug info on fight routines */
#undef  LANGUAGE_CRYPT          /* Scramble unknown spoken languages */
#define MEM_DEBUG               /* detailed tracking of where all the RAM is going */
#define OOC                     /* allow use of global out of context channel */
#undef  OVL                     /* anti-spam code, but adds significant cpu overhead */
#define PCS_USE_MANA            /* mana use for pcs currently disabled */
#undef  PET_STABLE              /* PET_STABLE allows stabling of pets -NOT YET FINISHED PORT! */
#undef  SPELL_DEBUG             /* for step by step debug info on spell routines */
#define ARTIFACT                 /* Code support for unique Artifact objects */
#define NEW_GROUP_CACHE          /* Group Object Caching Code */
#undef NEW_GROUP_PROC           /* Group special procs, *very* disabled :P */
#define ADV_PROC                /* Gond's new robust special proc system */
#undef SERIAL_NUMBERS           /* Unique serial numbers on all obj_data and char_data structs, currently disabled
                                   due to horrific disk requirements since common objects don't stack in pfiles and
                                   the log file of creations and destructions are HUGE.  JAB */
#undef SHARED_STRINGS           /* saves tons of bootup time, at cost of some ramuse */
#undef SPELL_DAM_DEBUG          /* For debugging the spell damage routine */
#define EVENT_SAVING            /* Saving of selected event types */
#define PROC_SAVING             /* Special Procedure saving */
#define ASSOC                   /* Associations Code */
#undef  KINGDOM                 /* Kingdoms code */
#define PCPROCS                 /* PC Procs */
#undef  OLDJUSTICE              /* Old outcast justice system */
#define NEWJUSTICE              /* New duris/outcast justice hybrid */
#define PK_BALANCE              /* PK damage online balancing system */
#define NEW_SITE_LOOKUPS        /* async dns and ident lookup code */
#define NEWCOMBAT               /* New combat toggle */
#define MMAIL_SYSTEM            /* Mail system */
#undef  SQL                     /* SQL object/PC saving etc - Alth*/
#define NEW_NECRO               /* New necro shyte - Iyachtu */
#define NEW_BARD                /* New bard shyte, omg! */
#define NEW_AWARE               /* New Awareness shite */

/* default filesystem variables */

#define DFLT_DIR "."            /* default data directory     */
#define MUDNAME "Faerun"       /* name whod shows for the mud */
#define SAVE_DIR "Players"      /* default directory for player save files */
#define STABLE_DIR "Stables"    /* default directory for pet stables save files */
#define MAIL_DIR "Players/Mail" /* default mailbox save dir */
/* balance affecting variables */

#define DAMFACTOR               1 /* scaling factor for spell damage */
#define PET_HITPOINT_MODIFIER   1
#define DRAG_COST               5 /* Additional move cost when dragging */
#define MANA_PER_CIRCLE         7 /* mobs still using mana, this much per circle for cost */
#define MAX_ACTION_DELAYS      14 /* number of commands with special delays */
#define MAX_DRAG             1.75 /* N times max_carry is how much you can drag */
#define MAX_TIMED_USAGES       25 /* max number of skills with usage timers, PFILE! */
#define MAX_SHADOW_MOVES        5 /* number of 'free' moves before someone MIGHT notice a shadower */
#define MAX_TRACK_DIST         32 /* old limit for tracking */
#define MOB_DROPPED_WEAP_DELAY  2 /* rounds til mob can recover a dropped weapon */
#define MOB_FUMBLING_DELAY      2 /* rounds for mob to recover from fumble */
#define MOB_HEADBUTT_DELAY      3 /* rounds between mob headbutts */
#define PC_BASH_DELAY          10 /* rounds between bashes */
#define PC_BERSERK_DELAY        5 /* rounds between berserks */
#define PC_DROPPED_WEAP_DELAY   2 /* rounds til pc can recover a dropped weapon */
#define PC_FUMBLING_DELAY       2 /* rounds for pc to recover from fumble */
#define PC_HEADBUTT_DELAY       3 /* rounds between pc headbutts */
#define PC_INSTANTKILL_DELAY   10 /* rounds between instantkills */
#define SHADOW_AWARE_PENALTY   20 /* penalty to shadowers skill, if target is 'aware' */

/* time factors */

#define MAX_ARENA_CORPSE_TIME  1200     /*   5 minutes */
#define MAX_NPC_CORPSE_TIME    2400     /*  10 minutes */
#define MAX_PC_UNLOOTED_CORPSE_TIME 18 * 60 * 60 * 4/* 18 hrs worth of unlooted pcorpse decay time */
#define MAX_PC_CORPSE_TIME    36000     /* 150 minutes */
#define MAX_CORPSE_LINGER        45     /* after a corpse is this many days old it'll start rotting on reboots */
#define OPT_USEC             250000     /* time delay corresponding to 4 pulses/sec */
#define OVL_PULSE                18     /* anti-spam repeated input limit period */
#define PULSE_BARD_UPDATE        60     /* 15 sec */
#define PULSE_MYCONID_UPDATE     60     /* 15 sec */
#define PULSE_MOBILE             40     /* 10 seconds */
#define PULSE_VEHICLE            10     /* for ships, 2.5 seconds */
#define PULSE_VIOLENCE           16     /* combat round, 4 seconds */
#define PULSE_MOB_HUNT            5     /* how often a HUNTER mob moves */
#define SECS_PER_MUD_HOUR        60
#define SECS_PER_MUD_DAY       1440     /*  24 * SECS_PER_MUD_HOUR  */
#define SECS_PER_MUD_MONTH    50400     /*  35 * SECS_PER_MUD_DAY   */
#define SECS_PER_MUD_YEAR    856800     /*  17 * SECS_PER_MUD_MONTH */
#define SECS_PER_REAL_MIN        60
#define SECS_PER_REAL_HOUR     3600     /*  60 * SECS_PER_REAL_MIN  */
#define SECS_PER_REAL_DAY     86400     /*  24 * SECS_PER_REAL_HOUR */
#define SECS_PER_REAL_YEAR 31536000     /* 365 * SECS_PER_REAL_DAY  */
#define SHORT_AFFECT             80     /* 20 seconds */
#define WAIT_ROUND               16     /* pulses in a combat round */
#define WAIT_SEC                  4     /* pulses in a second */

/* limiting factors, some are arbitrary, some are not, change only after thourough checking! */

#define FORGER                60    /* highest possible level */
#define MAXLVL                59    /* highest level do_advance can handle */
#define MAXLVLMORTAL          50    /* highest a mortal can go */
#define BOARD_COUNT           41    /* number of defined bboards */
#define MAX_CIRCLE            10    /* Alter at your own peril! */
#define MAX_DUPES_IN_WELL      3    /* donation well won't accept more than this of same item */
#define MAX_HOSTNAME         256    /* max length of server's hostname */
#define MAX_INPUT_LENGTH     321    /*    4 80 character lines + trailing NULL*/
#define MAX_MESSAGES         160    /* for pose command */
#define MAX_MESSAGE_LENGTH (MAX_STRING_LENGTH - 100)    /* for boards, leaves room for overhead */
#define MAX_MSGS              99    /* Max number of messages on one board. */
#define MAX_NUM_ROOMS        100    /* maximum number of rooms in a single ship */
#define MAX_NUM_SHIPS         13    /* maximum number of ships in game */
#define MAX_OBJ_AFFECT         2    /* silly, but I haven't gotten around to fixing it */
#define MAX_PLAYERS_BEFORE_LOCK 325 /* for running with hard limit on number of players */
#define MAX_PROD              20    /* shops, max number of differnt items a shop can produce */
#define MAX_QUESTS          7500    /* max allowable quests */
#define MAX_QUEUE_LENGTH    4800    /* 60 80 character lines */
#define MAX_SEASONS            4    /* maximum number of seasons in a zone */
#define MAX_SKILLS           550    /* size of skill array, PFILE! */
#define MAX_SONGS             50    /* for new bard code - Iyachtu */
#define MAX_STRING_LENGTH  65535    /* 819+ 80 character lines (64k - 1) */
#define MAX_TONGUE            14    /* number of defined tongues */
#define MAX_TRADE             34    /* shops, max number of item types they trade in  */
#define MAX_WEAR              32    /* size of equipment[] array */
#define MAX_WHO_PLAYERS      758    /* for do_who, max number of players it can handle */
#define MIN_GROUP_NAME_LENGTH 8     /* minimum length of a group name */
#define MAX_GROUP_NAME_LENGTH 80    /* maximum length of a group name */
#define MAX_PC_GROUP          12    /* groups aren't allowed any bigger than this */
#define MINLVLIMMORTAL        51    /* bottom of immortal levels */
#define SHIP_MAX_SPEED        30    /* Ah canna push her ana faster cap'n! */
#define TOTALCLASS            26    /* number of defined classes (0-24) */
#define TOTALLVLS             61    /* range of levels (0-60) */
#define PC_RACES (LAST_PC_RACE + 1) /* number of defined player races */
#define LAST_CLASS            25    /* number of defined classes (1-16) */
#define ROOM_DIMENSION_NAMES  9     /* number of names to define room sizes */
#define MAX_OFF_LEVEL         20    /* Maximum number of offensive spell 'levels' */
#define CODE_CONTROL_BYTES    10    /* max number of code disable/enable flags - altherog Dec 98 */
#define AUCTION_COUNT          2    /* number of defined auctions */
#define MAX_POSTS             999   /* Max number of posts on one board. */
#define MAX_PC_MR             10    /* Max Magic resistance for PC's */
#define TIMED_FACTOR          5000  /* Timed Socials hack --CRM */

/* misc thingies */
#ifdef ALPHA_MODE
   #define NEWBIE_LOAD_ROOM        3502     /* this dumps newbies into viperstounge */
#else
   #define NEWBIE_LOAD_ROOM        3502     /* for newbie code */
#endif

#define DEFAULT_MODE (SHOW_NAME | SHOW_ON)      /* information that whod reveals */

#define NOWHERE           -1    /* nil reference for room-database    */
#define OVL_DWEEB_LIMIT    1    /* legal repetitions within OVL_PULSE for dweebs */
#define OVL_NORMAL_LIMIT   5    /* legal repetitions within OVL_PULSE */

#ifdef MEM_DEBUG
   #define MEM_STRINGS        1
   #define MEM_SOCIALS        2
   #define MEM_BAN            3
   #define MEM_PC             4
   #define MEM_NPC            5
   #define MEM_SPL_CAST       6
   #define MEM_QUEUES         7
   #define MEM_GMEMBER        8
   #define MEM_DESC           9
   #define MEM_E_DSCR        10
   #define MEM_IDX           11
   #define MEM_ROOM          12
   #define MEM_ZONE          13
   #define MEM_EXITS         14
   #define MEM_OBJ           15
   #define MEM_AFF           16
   #define MEM_MEM           17
   #define MEM_FOLLOW        18
   #define MEM_TRACK         19

// Following two items for is_condensed ease of use - Iyachtu
   #define OTHER              0
   #define SELF               1

   #ifndef ARTIFACT
      #define MEM_LASTMEM       20    /* This should always be one greater than last
                                   memory element */
   #else  /* ARTIFACT */
      #define MEM_ARTIFACT      20
      #define MEM_LASTMEM       21    /* This should always be one greater than last
                                   memory element */
   #endif /* !ARTIFACT */

extern int mem_use[MEM_LASTMEM];
#endif

#endif  /* _SOJ_CONFIG_H */

