# Luminari MUD - Complete Artifact Equipment System Reference

## Overview

The artifact equipment system in Luminari MUD is a comprehensive feature that manages unique, powerful items with special ownership tracking and abilities. The system is implemented as a compile-time optional feature controlled by the `#ifdef ARTIFACT` preprocessor directive.

## Key System Features

- **Unique Ownership Tracking**: Each artifact can only be owned by one character at a time
- **Persistent Storage**: Artifact ownership is saved to the `areas/world.artifact` file
- **Transfer Logging**: All artifact ownership changes are logged
- **Nested Artifact Handling**: Support for artifacts contained within other objects
- **Database Integration**: Full integration with the MUD's object system via `IDX_ARTIFACT` flag
- **Special Abilities**: Individual artifacts have unique commands and abilities

## File Structure and Organization

### Core Implementation Files

#### 1. **Primary System Files**
- **`src/specs.artifacts.c`**
  - Main artifact specifications and implementations
  - Contains specific artifact behaviors and special procedures
  - Implements individual artifacts like:
    - <PERSON><PERSON><PERSON><PERSON><PERSON> (Staff of Ancient Oaks)
    - <PERSON><PERSON><PERSON><PERSON> (Rod of Light)
  - Defines artifact-specific commands and abilities

- **`src/handler.c`**
  - Core artifact handling functions:
    - `artifactTo<PERSON>har()` - Assigns artifact to character
    - `artifactFromChar()` - Removes artifact from character
    - `simpleArtifactFromChar()` - Simplified artifact removal
    - `tagNestedArtifacts()` - Handles artifacts in containers
    - `getNestedArtifacts()` - Retrieves nested artifacts
    - `dropNestedArtifacts()` - Drops nested artifacts
    - `artifactIsOwned()` - Checks artifact ownership
    - `searchForArtifact()` - Searches for specific artifacts
    - `tagBogusArtifact()` - Marks invalid artifacts

- **`src/db.c`**
  - Artifact database initialization and management
  - Contains global variables:
    - `art_f` - File pointer for artifact data
    - `totalArtifacts` - Counter for total artifacts
    - `art_index` - Array indexing artifacts
  - Key functions:
    - `initializeArtifacts()` - Initializes artifact system at boot
    - `uniqueArtifact()` - Ensures artifact uniqueness
    - `destroyArtifactList()` - Cleanup function

#### 2. **Header and Definition Files**
- **`src/structs.h`**
  - Contains `struct artifact_data` definition (lines 805-810)
  - Defines artifact-related structure declarations

- **`src/prototypes.h`**
  - Function prototypes for all artifact-related functions (lines 1283-1293)
  - Declares artifact helper functions

- **`src/config.h`**
  - Defines `ARTIFACT` feature flag
  - Memory indexing configuration for artifacts

- **`src/specs.include.h`**
  - Defines `IDX_ARTIFACT` flag and related macros

- **`src/specs.prototypes.h`**
  - Contains prototypes for artifact-related special procedures
  - References to `specs.artifact.c`

- **`src/db.h`**
  - Declares `ARTIFACT_FILE` macro pointing to "areas/world.artifact"
  - Database-related artifact definitions

### Command and Interface Files

- **`src/actwiz.c`**
  - Wizard/admin commands for artifact management
  - Artifact inspection and debugging commands
  - Owner checks and bogus artifact tagging

- **`src/specs.assign.c`**
  - Contains artifact assignments
  - Special procedure hooks (e.g., Doombringer)
  - Links artifacts to their special behaviors

### Support and Integration Files

- **`src/files.c`**
  - Save/load paths for artifact persistence
  - Ensures artifacts aren't removed during temporary saves

- **`src/olcdb.c`**
  - Online Creation (OLC) system integration
  - External declarations and database hooks
  - References to artifact arrays

- **`src/debug.c`**
  - Memory accounting includes `MEM_ARTIFACT` when enabled
  - Debugging support for artifact system

### Legacy and Reference Files

- **`src/specs.retired.c`**
  - Contains historical/retired artifact implementations
  - Comments about old/removed artifacts

- **`src/specs.undermountain.c`**
  - Contains minor artifact-related dialogue/content
  - Contextual references to artifacts

### Data Files

- **`areas/world.artifact`**
  - Primary artifact data file
  - Contains:
    - Artifact ownership tracking
    - List of all artifacts with VNUMs and current owners
    - Documentation for each artifact (Kelrarin's Hammer, Tiamat's Stinger, etc.)

### Build System

- **`src/Makefile`**
  - References artifact-related object files in build process
  - Manages compilation of artifact system components

### Documentation

- **`docs/developer-guide.md`**
  - References artifact system in codebase overview
  - Developer documentation for working with artifacts

## Critical Files for Understanding the System

The three most important files for understanding the artifact system are:

1. **`src/specs.artifacts.c`** - Contains the actual artifact implementations
2. **`src/handler.c`** - Core functionality for artifact management
3. **`src/structs.h`** - Data structures that define how artifacts work

## System Architecture

The artifact system integrates deeply with Luminari's object and character systems:

1. **Initialization**: System loads at boot time via `initializeArtifacts()`
2. **Ownership Management**: Tracks who owns each artifact through the artifact file
3. **Transfer Handling**: Monitors all equipment changes to update ownership
4. **Persistence**: Saves state to disk to maintain ownership across reboots
5. **Special Abilities**: Each artifact can have unique commands and behaviors

## Development Notes

- The system uses conditional compilation (`#ifdef ARTIFACT`) allowing it to be enabled/disabled at compile time
- All artifact transfers are logged for administrative oversight
- The system prevents duplication of unique artifacts
- Nested artifact support allows artifacts to be stored in containers while maintaining ownership tracking