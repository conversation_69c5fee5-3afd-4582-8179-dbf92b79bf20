/* ***************************************************************************
 *  File: db.c                                                 Part of Outcast *
 *  Usage: Loading/Saving chars, booting world, resetting etc.               *
 *  Copyright  1990, 1991 - see 'license.doc' for complete information.      *
 *  Copyright  1994, 1995 - Outcast Systems Ltd.                             *
 *************************************************************************** */

#include <ctype.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>

#include "comm.h"
#include "db.h"
#include "email_reg.h"
#include "events.h"
#include "interp.h"
#ifdef NEWJUSTICE
#include "newjustice.h"
#endif
#ifdef OLDJUSTICE
#include "justice.h"
#endif
#include "mm.h"
#include "prototypes.h"
#include "race_class.h"
#include "specs.include.h"
#include "specs.prototypes.h"
#include "spells.h"
#include "utils.h"
#include "weather.h"
#include "olcdb.h"
#include "olc.h"

// Change this to YES if you want a HUGE amount of boot-time spam from db.c
// while olc scans the world files as they are read in, and inserts OLC free
// rooms whenever makers skipped room numbers.
static int OLC_BOOT_DEBUG = NO;

/* external variables */
extern int port;
extern byte create_locked;
extern P_desc descriptor_list;
extern P_event current_event;
extern P_event event_type_list[];
extern const char *equipment_types[];
extern const char *where[];
extern const int min_stats_for_class[TOTALCLASS][8];
extern const struct race_lookup race_lookup_table[];
extern const struct stat_data stat_factor[];
extern const struct trait_data racial_traits[];
extern int boot_time;
extern int hometown[];
extern int no_specials;
extern int pulse;
extern int restore_wear[];
extern int shutdownflag;
extern int spl_table[TOTALLVLS][MAX_CIRCLE];
extern int Top_Of_OLC_Database;
extern int scan_player_files;
extern int sql_scan_objects;
extern struct con_app_type con_app[];
extern struct mm_ds *dead_pc_pool;
extern struct str_app_type str_app[];
extern struct time_info_data time_info;
extern u_int event_counter[];
extern int racial_mods[][2];
extern bool pclist_debug;

static int NumberOfZones = 0;
static unsigned int TotalBlankRooms = 0;

void verify_mobile(P_char mob);

/**************************************************************************
 *  declarations of most of the 'global' variables                         *
 ************************************************************************ */

#if 0
// Azuth this seems to never be used heh
struct reset_q_type reset_q;
#endif

P_olc olc_db = NULL; /* Allocation for the OLC database */
P_room world = NULL; /* dyn alloc'ed array of rooms     */
P_obj object_list = NULL; /* the global linked list of obj's */
P_char NPC_list = NULL; /* global l-list of NPCs          */
P_char PC_list = NULL; /* global l-list of PCs          */
P_group group_list = NULL; /* global l-list of all groups */
P_index mob_index = NULL; /* index table for mobile file     */
P_index obj_index = NULL; /* index table for object file     */
struct zone_data *zone_table = NULL; /* table of reset data             */

int PC_count = 0; /* count of PC bodies in game, only used by PC_list_debug at present */
int top_of_world = 0; /* ref to the top element of world */
int top_of_zone_table = 0; /* top of the zone table           */
int top_of_mobt = 0; /* top of mobile index table       */
int top_of_objt = 0; /* top of object index table       */
int top_of_helpt = 0; /* top of help index table         */
int top_of_whelpt = 0; /* top of help index table         */
int top_of_infot = 0; /* top of info index table         */

FILE *mob_f = NULL; /* file containing mob prototypes  */
FILE *obj_f = NULL; /* obj prototypes                  */
FILE *help_fl = NULL; /* file for help texts (HELP <kwd>) */
FILE *whelp_fl = NULL; /* file for help texts (HELP <kwd>) */
FILE *info_fl = NULL; /* file for info texts (INFO <kwd>) */

#ifdef ARTIFACT
FILE *art_f = NULL; /* file containing artifact data */
int totalArtifacts = 0; /* total number of artifacts */
struct artifact_data *art_index = NULL; /* array of artifact data */
#endif

struct message_list fight_messages[MAX_MESSAGES]; /* fighting messages   */

char *alignment_table = NULL;
char *bonus = NULL;
char *changelog = NULL;
char *classtable = NULL; /* class selection tables          */
char *credits = NULL; /* the Credits List                */
char *faq = NULL; /* frequently asked questions      */
char *generaltable = NULL; /* race/class comparison charts    */
char *genlog = NULL;
char *greetinga = NULL; /* greeting for our ansi viewers   */
char *help = NULL; /* the main help page              */
char *helpa = NULL;
char *hometown_table = NULL;
char *info = NULL; /* the info text                   */
char *infoa = NULL;
char *keepchar = NULL;
char *motd = NULL; /* the messages of today           */
char *namechart = NULL;
char *news = NULL; /* the news                        */
char *racetable = NULL; /* race selection tables           */
char *racewars = NULL; /* good/evil race explanation      */
char *reroll = NULL;
char *rules = NULL;
char *wizlist = NULL; /* the wizlist                     */
char *wizlista = NULL; /* wizlist for ansi listeners      */
char *wizmotd = NULL;
char *wiznews = NULL;
char *olc_menu = NULL; /* on-line creation main menu      */
char *olc_new_user = NULL; /* on-line creation new user info  */
char *olc_db_menu = NULL; /* on-line creation database menu  */
char *olc_db_new_user = NULL; /* on-line creation db new user    */
char *olc_room_exit_menu = NULL; /* on-line creation room exits     */
char *olc_room_expert_long = NULL; /* on-line creation command line   */
char *olc_room_expert_short = NULL; /* on-line creation command line   */

struct help_index_element *help_index = NULL;
struct whelp_index_element *whelp_index = NULL;
struct info_index_element *info_index = NULL;

struct time_info_data time_info; /* the information about the time  */

struct mm_ds *dead_char_pool = NULL;
struct mm_ds *dead_npc_pool = NULL;
struct mm_ds *dead_obj_pool = NULL;
struct mm_ds *dead_grp_pool = NULL;

ubyte sets_code_control[CODE_CONTROL_BYTES]; /* array holding code enable disable flags
                                                * its stuck in here for lack of better place
                                                *               -- Altherog Dec 98 */

#ifdef PK_BALANCE
float sets_skill_adjustments[MAX_SKILLS]; /* each element holds a factor by which the
                                                * corresponding skill will be adjusted
                                                *               -- Altherog Jun 99 */
#endif

#ifdef ARTIFACT
void initializeArtifacts(void);
int uniqueArtifact(P_obj, P_char, int, char *);
void destroyArtifactList(void);
#endif

void report_world(void); // this is temp for debugging only -Azuth 10/25/04

P_index generate_indices(FILE *, int *);

void release_mob_mem(P_char ch);
void release_obj_mem(P_obj obj);

void release_group_mem(struct group_data *grp) {
  mm_release(dead_grp_pool, grp);
}

void release_mob_mem(P_char ch) {
  RemoveEvent();
  mm_release(dead_char_pool, ch);
}

void release_obj_mem(P_obj obj) {
  mm_release(dead_obj_pool, obj);
}

/*************************************
 *  routines for booting the system  *
 *************************************/

/* body of the booting system */
void boot_db(void) {
  int status = 0;
  int coin;

  logit(LOG_STATUS, "Boot db -- BEGIN.");
  fprintf(stderr, "Boot db -- BEGIN.\n");
  fprintf(stderr, "The game takes approximately 12 minutes to boot up.\n");
  boot_time = time(0);

  logit(LOG_STATUS, "Resetting the game time:");
  fprintf(stderr, "Resetting the game time:\n");
  reset_time();

  logit(LOG_STATUS, "Reading Code Control file.");
  fprintf(stderr, "Loading Code Control file.\n");
  if (restoreCodeControlBits() != 0) {
    fprintf(stderr, "ERROR! Trouble opening Code Control configuration file! Exiting.\n");
    dump_core();
  }

#ifdef SQL
  sql_initialize();
#endif
#ifdef AZUTHSQL
  if (sql_initialize())
    fprintf(stderr, "Failed to init SQL\n");
  else {
    if (sql_create_logpwords())
      fprintf(stderr, "Failed to create logging password tables\n");
  }
#endif

  fprintf(stderr, "Reading files from lib directory. (motd, wizlist, etc)\n");
  logit(LOG_STATUS, "Reading changelog file.");
  changelog = file_to_string(CHANGELOG_FILE);
  logit(LOG_STATUS, "Reading genname file.");
  genlog = file_to_string(LOG_GENNAME);
  logit(LOG_STATUS, "Reading wiznewsfile.");
  wiznews = file_to_string(WIZNEWS_FILE);
  logit(LOG_STATUS, "Reading newsfile.");
  news = file_to_string(NEWS_FILE);
  logit(LOG_STATUS, "Reading faqfile.");
  faq = file_to_string(FAQ_FILE);
  logit(LOG_STATUS, "Reading credits.");
  credits = file_to_string(CREDITS_FILE);
  logit(LOG_STATUS, "Reading motd.");
  motd = file_to_string(MOTD_FILE);
  logit(LOG_STATUS, "Reading Ansi motd.");
  wizmotd = file_to_string(WIZMOTD_FILE);
  logit(LOG_STATUS, "Reading Ansi wizmotd.");
  help = file_to_string(HELP_PAGE_FILE);
  logit(LOG_STATUS, "Reading ANSI help.");
  helpa = file_to_string(HELPA_PAGE_FILE);
  logit(LOG_STATUS, "Reading info.");
  info = file_to_string(INFO_PAGE_FILE);
  logit(LOG_STATUS, "Reading ANSI info.");
  infoa = file_to_string(INFOA_PAGE_FILE);
  logit(LOG_STATUS, "Reading wizlist.");
  wizlist = file_to_string(WIZLIST_FILE);
  logit(LOG_STATUS, "Reading rules.");
  rules = file_to_string(RULES_FILE);
  logit(LOG_STATUS, "Reading Ansi login screen.");
  greetinga = file_to_string(GREETINGA_FILE);
  logit(LOG_STATUS, "Reading Ansi wizlist.");
  wizlista = file_to_string(WIZLISTA_FILE);
  logit(LOG_STATUS, "Reading race/class comparison table.");
  generaltable = file_to_string(GENERALTABLE_FILE);
  logit(LOG_STATUS, "Reading Race table.");
  racetable = file_to_string(RACETABLE_FILE);
  logit(LOG_STATUS, "Reading Class table.");
  classtable = file_to_string(CLASSTABLE_FILE);
  logit(LOG_STATUS, "Reading Racewars explanation.");
  racewars = file_to_string(RACEWARS_FILE);
  logit(LOG_STATUS, "Reading Namechart message.");
  namechart = file_to_string(NAMECHART_FILE);
  logit(LOG_STATUS, "Reading Reroll message.");
  reroll = file_to_string(REROLL_FILE);
  logit(LOG_STATUS, "Reading Bonus message.");
  bonus = file_to_string(BONUS_FILE);
  logit(LOG_STATUS, "Reading Keepchar message.");
  keepchar = file_to_string(KEEPCHAR_FILE);
  logit(LOG_STATUS, "Reading Hometown_table message.");
  hometown_table = file_to_string(HOMETOWN_FILE);
  logit(LOG_STATUS, "Reading Alignment_table message.");
  alignment_table = file_to_string(ALIGNMENT_FILE);
  logit(LOG_STATUS, "Reading olc main menu.");
  olc_menu = file_to_string(OLC_MENU_FILE);
  logit(LOG_STATUS, "Reading olc database menu.");
  olc_db_menu = file_to_string(OLC_DB_MENU_FILE);
  logit(LOG_STATUS, "Reading olc db new user info.");
  olc_new_user = file_to_string(OLC_NEW_USER_FILE);
  logit(LOG_STATUS, "Reading olc db new user info.");
  olc_db_new_user = file_to_string(OLC_DB_NEW_USER_FILE);
  logit(LOG_STATUS, "Reading olc room exit menu.");
  olc_room_exit_menu = file_to_string(OLC_ROOM_EXIT_MENU);
  logit(LOG_STATUS, "Reading olc room expert mode help file - long version.");
  olc_room_expert_long = file_to_string(OLC_ROOM_EXPERT_LONG);
  logit(LOG_STATUS, "Reading olc room expert mode help file - short version.");
  olc_room_expert_short = file_to_string(OLC_ROOM_EXPERT_SHORT);

  // New function to build areas.
  // This routine compiles all the small *.wld, *.mob, etc into big world.wld, world.mob, etc
  status = build_areas();
  if (status == FAILED) {
    fprintf(stderr, "Failed to compile all areas successfully.\n");
    logit(LOG_STATUS, "Failed to compile all areas successfully.\n");
  }

  fprintf(stderr, "Opening mobile, object, help and info files.\n");
  logit(LOG_STATUS, "Opening mobile, object, help and info files.");

  mob_f = fopen(MOB_FILE, "r");
  if (!mob_f) {
    perror("boot");
    fprintf(stderr, "ERROR! Trouble opening mobile file world.mob! Exiting.\n");
    dump_core();
  }

  obj_f = fopen(OBJ_FILE, "r");
  if (!obj_f) {
    perror("boot");
    fprintf(stderr, "ERROR! Trouble opening object file world.obj! Exiting.\n");
    dump_core();
  }

  help_fl = fopen(HELP_KWRD_FILE, "r");
  if (!help_fl) {
    logit(LOG_FILE, "   Could not open help file.");
    fprintf(stderr, "ERROR! Could not open help file! Exiting.");
  } else
    help_index = build_help_index(help_fl, &top_of_helpt);

  whelp_fl = fopen(WHELP_KWRD_FILE, "r");
  if (!whelp_fl) {
    logit(LOG_FILE, "   Could not open wizhelp file.");
    fprintf(stderr, "ERROR! Could not open wizhelp file! Exiting.");
  } else {
    fprintf(stderr, "Opened wizhelp file, building index...");
    whelp_index = build_whelp_index(whelp_fl, &top_of_whelpt);
  }

  info_fl = fopen(INFO_KWRD_FILE, "r");
  if (!info_fl) {
    logit(LOG_FILE, "   Could not open info file.");
    fprintf(stderr, "ERROR! Could not open info file! Exiting.");
  } else
    info_index = build_info_index(info_fl, &top_of_infot);

  fprintf(stderr, "Loading olc database.\n");
  logit(LOG_STATUS, "Loading olc database.");
  Read_Write_OLC_Database(READ_DB);

  fprintf(stderr, "Loading zone table.\n");
  logit(LOG_STATUS, "Loading zone table.");
  boot_zones(); // load zones into memory from world.zon

  logit(LOG_STATUS, "After boot_zones()");
  report_world();

  // Read in control knobs.
  Read_Control_Database();
  //   Read_Write_OLC_Database(READ_DB); no idea why this was read in twice, but assuming for now it should not be

  //  fprintf(stderr, "Listing of zones: \n");
  //  for(count = 0; count <= NumberOfZones; count++)
  //    fprintf(stderr, "Zone #%d: -> %s <-\n", count, zone_table[count].name);
  //  fprintf(stderr, "All done..\n");

  fprintf(stderr, "\nLoading rooms - this process can take approximately 2 minutes.\n");
  logit(LOG_STATUS, "Loading rooms - this process can take approximately 2 minutes.");
  boot_world(); // load the world into memory from world.wld file

  logit(LOG_STATUS, "After boot_world()");
  report_world();

  fprintf(stderr, "Renumbering rooms.\n");
  logit(LOG_STATUS, "Renumbering rooms.");
  renumber_exits();

  fprintf(stderr, "Generating index table for mobiles.\n");
  logit(LOG_STATUS, "Generating index table for mobiles.");
  mob_index = generate_indices(mob_f, &top_of_mobt);

  fprintf(stderr, "Generating index table for objects.\n");
  logit(LOG_STATUS, "Generating index table for objects.");
  obj_index = generate_indices(obj_f, &top_of_objt);

  fprintf(stderr, "Loading mobile behaviour definitions.\n");
  logit(LOG_STATUS, "Loading mobile behaviour definitions.");
  boot_the_socials();
  fprintf(stderr, "Done.\n");

  /*  load_obj_limits(); */

  fprintf(stderr, "Renumbering zone table.\n");
  logit(LOG_STATUS, "Renumbering zone table.");
  renum_zone_table();

  fprintf(stderr, "Loading fight messages.\n");
  logit(LOG_STATUS, "Loading fight messages.");
  load_messages();

  fprintf(stderr, "Loading social messages.\n");
  logit(LOG_STATUS, "Loading social messages.");
  boot_social_messages();

  fprintf(stderr, "Initializing boards.\n");
  logit(LOG_STATUS, "Initializing boards..");
  initialize_boards();

  fprintf(stderr, "Initializing auction.\n");
  logit(LOG_STATUS, "Initializing auction..");
  boot_auctions();
  //  initialize_auctions(); old version

  fprintf(stderr, "Loading pose messages.\n");
  logit(LOG_STATUS, "Loading pose messages.");
  boot_pose_messages();

  fprintf(stderr, "Loading justice definitions.\n");
  logit(LOG_STATUS, "Loading justice definitions.");
  //boot_justice_libs();

  fprintf(stderr, "Loading PK Skill and Spell balance data.\n");
  logit(LOG_STATUS, "Loading PK Skill and Spell balance data.");
  if (restoreSkillAdjustments() != 0) {
    fprintf(stderr, "ERROR! Trouble opening PK Skill and Spell balance data file! Exiting.\n");
    dump_core();
  }

  /* The mmail system should always be initialized even if mmail is disabled by using Code Control - Alth */
  fprintf(stderr, "Initializing mailbox system.\n");
  logit(LOG_STATUS, "Initializing mailbox system.");
  mmail_mbox_system_init();

  /* before loading any mobs, initialize the memory management for structs that will be used for mobiles */

  /* the fancy formula at as the last parameter just says "allocate enough memory for n mobs at a time.
     Here, n is half the number of different types of mobs in the game */

  dead_char_pool =
          mm_create("CHARs", sizeof (struct char_data), offsetof(struct char_data, next),
          mm_find_best_chunk(sizeof (struct char_data), 60, 150));

  dead_pc_pool =
          mm_create("PCs", sizeof (struct pc_only_data), offsetof(struct pc_only_data, ignored),
          mm_find_best_chunk(sizeof (struct pc_only_data), 20, 50));

  dead_npc_pool =
          mm_create("NPCs", sizeof (struct npc_only_data), offsetof(struct npc_only_data, memory),
          mm_find_best_chunk(sizeof (struct npc_only_data), 50, 100));

  dead_obj_pool =
          mm_create("OBJs", sizeof (struct obj_data), offsetof(struct obj_data, next),
          mm_find_best_chunk(sizeof (struct obj_data), 100, 250));

  dead_grp_pool =
          mm_create("GRPs", sizeof (struct group_data), offsetof(struct group_data, next),
          mm_find_best_chunk(sizeof (struct group_data), 10, 40));

#ifdef SQL
  if (sql_scan_objects) {
    sql_perform_obj_scans();
    fprintf(stdout, "Object Scan finished.\n");
    exit(1);
  }
#endif

  if (port != DFLT_MAIN_PORT) { /* Wizlock create on boot - CRM 10/98 */
    fprintf(stderr, "Locking new character creation...\n");
    logit(LOG_STATUS, "Locking new character creation...");
    game_locked = game_locked | LOCK_CREATE;
  }

  if (!no_specials) {
    fprintf(stderr, "Assigning function pointers (spec procs):\n");
    logit(LOG_STATUS, "Assigning function pointers:");

    logit(LOG_STATUS, "   Mobiles.");
    fprintf(stderr, "-- Mobile special procedures.\n");
    assign_mobiles();

    logit(LOG_STATUS, "   Objects.");
    fprintf(stderr, "-- Object special procedures.\n");
    assign_objects();

#ifdef NEW_GROUP_PROC
    logit(LOG_STATUS, "   Groups.");
    fprintf(stderr, "-- Group special procedures.\n");
    assign_groups();
#endif

    logit(LOG_STATUS, "   Room.");
    fprintf(stderr, "-- Room special procedures.\n");
    assign_rooms();

    logit(LOG_STATUS, "   Diseases.");

    fprintf(stderr, "-- Disease definitions.\n");
    AddDiseaseDefinitions();

    logit(LOG_STATUS, "   Cures.");
    fprintf(stderr, "-- Cure definitions.\n");
    AddCures();

#ifdef ARTIFACT
    logit(LOG_STATUS, "   Initializing Artifacts.");
    fprintf(stderr, "-- Initializing Artifacts.\n");
    initializeArtifacts();
#endif
  }

  fprintf(stderr, "Assigning command pointers from interpreter.\n");
  logit(LOG_STATUS, "   Commands.");
  assign_command_pointers();

  fprintf(stderr, "Assigning spell pointers from sparser.\n");
  logit(LOG_STATUS, "   Spells.");
  assign_spell_pointers();

  fprintf(stderr, "Assigning grants from interpreter.\n");
  logit(LOG_STATUS, "Init Grants");
  assign_grant_commands();

  fprintf(stderr, "Initializing weather.\n");
  logit(LOG_STATUS, "Setting up weather");
  weather_setup();

  fprintf(stderr, "Initializing bans.\n");
  logit(LOG_STATUS, "Reading ban sites.");
  read_ban_file();

  fprintf(stderr, "Initializing event driver.\n");
  logit(LOG_STATUS, "Initializing event driver.");
  init_events();

  /* have to do ships after events, since zone resets (loading ships) are done in init_events now. */

  fprintf(stderr, "Initializing ships.\n");
  logit(LOG_STATUS, "Initializing ships.");
  initialize_ships();

  fprintf(stderr, "Reloading Player corpses.\n");
  logit(LOG_STATUS, "Reloading Player corpses.");
  restoreCorpses();

  fprintf(stderr, "Processing saved pet files.\n");
  npcsave_MoveIngameToClaim();

  fprintf(stderr, "Initializing trade.\n");
  logit(LOG_STATUS, "Initializing trade.");
  if (!(initialize_trade()))
    fprintf(stderr, "ERROR! Trouble initializing trade.\n");

  fprintf(stderr, "Pre-loading EMS accept data.\n");
  EMS_Bootup();
#ifdef NEWJUSTICE
  logit(LOG_STATUS, "Reloading Town Justice.");
  //   restore_town_justice();
#endif

  logit(LOG_STATUS, "Loading associations.");
  boot_assocs();

#ifdef KINGDOM
  logit(LOG_STATUS, "Loading house.");
  restore_house();

  logit(LOG_STATUS, " ... loading house construction Q");
  loadConstructionQ();
#endif

#ifdef SQL
  if (scan_player_files) {
    sql_scan_players_blocking();
    exit(1);
  }
#endif

  /* load the ipshare database */
  boot_ipshare();

  /* read the jackpot info from file */
  for (coin = COIN_COPPER; coin < NUM_COIN_TYPES; coin++)
    read_jackpot(coin);

  logit(LOG_STATUS, "Boot db -- DONE.");

  /*  MemReport(); Calcs how much ram we are hogging and where */
}

/* reset the time in the game from file */
void reset_time(void) {
  int beginning_of_time = 650336715;

  mud_time_passed(&time_info, time(0), beginning_of_time);

  logit(LOG_STATUS, "   Current Gametime:  %d/%d/%d  %d%s",
          time_info.month, time_info.day, time_info.year,
          (time_info.hour % 12) ? time_info.hour % 12 : 12,
          (time_info.hour == 12) ? " noon." :
          (time_info.hour == 0) ? " midnight." :
          (time_info.hour > 11) ? "pm." : "am.");

#if 0
  FILE *f1;
  int current_time;
  int last_time;
  int diff_time;
  int diff_hours;

  if (!(f1 = fopen(TIME_FILE, "r"))) {
    perror("reset time");
    dump_core();
  }

  fscanf(f1, "#\n");

  fscanf(f1, "%D\n", &last_time);
  fscanf(f1, "%d\n", &last_time_info.hours);
  fscanf(f1, "%d\n", &last_time_info.day);
  fscanf(f1, "%d\n", &last_time_info.month);
  fscanf(f1, "%d\n", &last_time_info.year);

  fclose(f1);

  logit(LOG_STATUS, "   Last Gametime: %dH %dD %dM %dY.",
          last_time_info.hours, last_time_info.day,
          last_time_info.month, last_time_info.year);

  current_time = time(0);
  diff_time = current_time - last_time;

  logit(LOG_STATUS, "   Time since last shutdown: %d.", diff_time);

  time_info.hours = last_time_info.hours;
  time_info.day = last_time_info.day;
  time_info.month = last_time_info.month;
  time_info.year = last_time_info.year;

  diff_hours = diff_time / SECS_PER_MUD_HOUR;
  diff_time = diff_time % SEC_PR_HOUR;

  sprintf(buf,);
  logit(LOG_STATUS, "   Real time lack : %d sec.", diff_time);

  switch (time_info.hours) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
      weather_info.sunlight = SUN_DARK;
      break;
    case 5:
      weather_info.sunlight = SUN_RISE;
      break;
    case 6:
    case 7:
    case 8:
    case 9:
    case 10:
    case 11:
    case 12:
    case 13:
    case 14:
    case 15:
    case 16:
    case 17:
    case 18:
    case 19:
    case 20:
      weather_info.sunlight = SUN_LIGHT;
      break;
    case 21:
      weather_info.sunlight = SUN_SET;
      break;
    case 22:
    case 23:
    default:
      weather_info.sunlight = SUN_DARK;
      break;
  }

  weather_info.pressure = 960;
  if ((time_info.month >= 7) && (time_info.month <= 12))
    weather_info.pressure += dice(1, 50);
  else
    weather_info.pressure += dice(1, 80);

  weather_info.change = 0;

  if (weather_info.pressure <= 980)
    weather_info.sky = SKY_LIGHTNING;
  else if (weather_info.pressure <= 1000)
    weather_info.sky = SKY_RAINING;
  else if (weather_info.pressure <= 1020)
    weather_info.sky = SKY_CLOUDY;
  else
    weather_info.sky = SKY_CLOUDLESS;
#endif
}

/* SAM, changed to get rid of all stupid reallocs :P */

/* load the zone table and command tables */
void boot_zones(void) {
  FILE *fl;
  int num_zones, num_commands, zon = 0, cmd_no = 0, tmp, t_idx;
  int tmp1, tmp2, tmp3, tmp4, tmp5, tmp6, tmp7, count;
  int *command_array;
  char *check, buf[8192], tmp_buf[MAX_STRING_LENGTH], c;
  char buf1[8192], buf2[8192], buf3[8192], data[8192], *hold;
  char buf4[8192], buf5[8192], buf6[8192];

#ifdef AREAS_DEBUG
  fprintf(stderr, "<<START:  Booting Zones>>\n");
#endif

  fl = fopen(ZONE_FILE, "r");
  if (!fl) {
    perror("boot_zones");
    dump_core();
  }

  /* SAM, Count the number of zones  */
  logit(LOG_STATUS, "Counting zones...");
  num_zones = 0;

#ifdef AREAS_DEBUG
  fprintf(stderr, "  <<START:  Counting Zones>>\n");
#endif

  for (;;) {
    fgets(tmp_buf, MAX_STRING_LENGTH, fl);
    if (tmp_buf[0] == '$')
      break;
    if (tmp_buf[0] == '#') {
#ifdef AREAS_DEBUG
      tmp_buf[strlen(tmp_buf) - 1] = 0; /* nuke the \n for formatting reasons */
      fprintf(stderr, "    <<Zone [%3d] %s>>\n", num_zones, tmp_buf);
#endif
      num_zones++;
    }
  }

  logit(LOG_STATUS, "\t\t%d zones allocated", num_zones);
  rewind(fl);

#ifdef AREAS_DEBUG
  fprintf(stderr, "  <<DONE:  Counting Zones>>\n");
#endif

  /* Count the number of commands in each zone */
  logit(LOG_STATUS, "Counting commands for each zone...");
  CREATE(command_array, int, (unsigned) num_zones);

  t_idx = 0;
  num_commands = 0;

#ifdef AREAS_DEBUG
  fprintf(stderr, "  <<START:  Counting Zone Commands>>\n");
#endif

  for (;;) {
    fgets(tmp_buf, MAX_STRING_LENGTH, fl);

    if (tmp_buf[0] == '$')
      break;

    if (tmp_buf[0] == '#') { /* skip over zone name */
      fgets(tmp_buf, MAX_STRING_LENGTH, fl);
      fgets(tmp_buf, MAX_STRING_LENGTH, fl);

#ifdef AREAS_DEBUG
      tmp_buf[strlen(tmp_buf) - 1] = 0; /* nuke the \n for formatting reasons */
      fprintf(stderr, "    <<New Zone: %s>>\n", tmp_buf);
#endif
    } else if (tmp_buf[0] == 'S') {
      /* this was increased slightly to prevent an obscure array bounds
       * write I had been noticing...if its 1, it crashes the mud on bootup
       * so the extra few bytes are well worth it...sometime someone needs
       * go over all of this with a fine tooth comb and figure out why
       * it is writing out of bounds at all...
       */
      command_array[t_idx] = num_commands + 2;
      t_idx++;
      num_commands = 0;

#ifdef AREAS_DEBUG
      fprintf(stderr, "    <<Zone Commands: [%d]>>\n", command_array[t_idx - 1]);
#endif
    } else {
      /* Skip leading whitespace to match the reading phase behavior */
      char *p = tmp_buf;
      while (*p == ' ' || *p == '\t') p++;
      
      if (*p == 'M' || *p == 'O' || *p == 'E' ||
          *p == 'P' || *p == 'D' || *p == 'G' ||
          *p == 'R' || *p == 'F' || *p == 'L' ||
          *p == 'Z' || *p == 'X' || *p == 'T') {
        num_commands++;
      }
    }
  }

  rewind(fl);

#ifdef AREAS_DEBUG
  fprintf(stderr, "  <<DONE:  Counting Zone Commands>>\n");
#endif

  /* Allocate array of zone structures */
#ifdef MEM_DEBUG
  mem_use[MEM_ZONE] += (sizeof (struct zone_data) * num_zones);
#endif

  CREATE(zone_table, struct zone_data, (unsigned) num_zones);

  if (OLC_BOOT_DEBUG == YES)
    fprintf(stderr, "DEBUG: Main zone read loop.\n");

  for (;;) {
    fscanf(fl, " #%d\n", &tmp);

    // Added hook for OLC - We read in the filename tag at the top of the zone file.
    // --MIAX 10-10-00 for Outcast III. o_o
    hold = fread_string(fl);
    if (*hold == '$') {
      free_string(hold);
      break; /* end of file */
    }
    else {
      sscanf(hold, "%s %s %s \n", buf1, buf2, buf3);
      if (!strcmp(buf1, ">")) {
        *data = '\0';
        for (count = 0; count <= strlen(buf3) - 5; count++) {
          sprintf(buf1, "%c", buf3[count]);
          strcat(data, buf1);
        }

        zone_table[zon].filename = str_dup(data);
        zone_table[zon].active_olc = NO;

        if (OLC_BOOT_DEBUG == YES)
          fprintf(stderr, "DEBUG: -> Zone defined, filename is: (%s)\n", zone_table[zon].filename);
      } else {
        logit(LOG_EXIT, "No filename tag on zone: %s, dumping.\n", hold);
        dump_core();
      }
    }

    // Now we check the OLC user database to see if this zone is actively
    // being worked on. If so, then make all the un-used room numbers in
    // the zone olc-free-rooms. This ensures the area maker can build the
    // entire zone in one sitting, or several sittings during a single
    // boot cycle without ever running out of available rooms on-line.
    // We set the olc_active flag here. 10/14/00 --MIAX
    if (OLC_BOOT_DEBUG == YES)
      fprintf(stderr, "DEBUG: -> Check area filename against OLC database:\n");
    for (count = 1; count <= Top_Of_OLC_Database; count++) {
      if (olc_db[count].CurrentArea) {
        if (OLC_BOOT_DEBUG == YES) {
          fprintf(stderr, "DEBUG: -> -> User %d is working on zone #%d, comparing to current zone #%d.\n",
                  count, olc_db[count].CurrentArea, zon);
        }
      } else {
        if (OLC_BOOT_DEBUG == YES)
          fprintf(stderr, "DEBUG: -> -> User %d is not working on an area.\n", count);

        continue;
      }

      if (olc_db[count].CurrentArea == zon) {
        if (OLC_BOOT_DEBUG == YES)
          fprintf(stderr, "DEBUG: -> -> MATCH: This is an Active Area! (%s). Allocating all free rooms.\n",
                zone_table[zon].filename);

        // olc_db[count].AreaFileName = str_dup(zone_table[zon].filename);
        zone_table[zon].active_olc = YES;
        break;
      }
    }
    
    free_string(hold);  /* Free hold after we're done with it */

    // The new stuff for reading in maker/email its all option...
    check = fread_string(fl);

    if (*check == '$') {
      free_string(check);
      break; /* end of file */
    }

    sscanf(check, "%s %s %s \n", buf4, buf5, buf6);
    if (!strcmp(buf4, "<")) {
      *data = '\0';
      for (count = 0; count <= strlen(buf6) - 5; count++) {
        sprintf(buf4, "%c", buf6[count]);
        strcat(data, buf1);
      }

      zone_table[zon].maker = str_dup(data);

      check = fread_string(fl);

      // fprintf(stderr, "DEBUG: Zone #%d has zone named defined as: (%s)\n", zon, check);

      if (*check == '$')
        break; /* end of file */
    }

    zone_table[zon].hometown = 0; /* default hometown is none */
    zone_table[zon].name = str_dup(check);
    zone_table[zon].mzr_pct = 0;
    zone_table[zon].hrs_since_req = MZR_UPTIME_HOURS;
    zone_table[zon].zone_empty_cnt_req = 0;

#ifdef AREAS_DEBUG
    fprintf(stderr, "  <<START:  Reading Zone Weather [%d]>>\n", zon);
#endif

    if (fscanf(fl, " %d %d %d %d %d %d %d \n", &tmp1, &tmp2, &tmp3, &tmp4, &tmp5, &tmp6, &tmp7) == 7) { /* new major zone resets must get scanned first */
      zone_table[zon].top = tmp1;
      zone_table[zon].lifespan_min = tmp2;
      zone_table[zon].lifespan_max = tmp2;
      zone_table[zon].reset_mode = tmp3;
      zone_table[zon].flags = tmp4;
      zone_table[zon].mzr_pct = tmp5;
      zone_table[zon].hrs_since_req = tmp6;
      zone_table[zon].zone_empty_cnt_req = tmp7;
    } else if (fscanf(fl, " %d %d %d %d \n", &tmp1, &tmp2, &tmp3, &tmp4) == 4) { /* old style */
      zone_table[zon].top = tmp1;
      zone_table[zon].lifespan_min = tmp2;
      zone_table[zon].lifespan_max = tmp2;
      zone_table[zon].reset_mode = tmp3;
      zone_table[zon].flags = tmp4;
    } else { /* error */
      logit(LOG_EXIT, "Zone %s has erroneous data format", check);
      dump_core();
    }

#ifdef AREAS_DEBUG
    fprintf(stderr, "  <<DONE:  Reading Zone Weather [%d]>>\n", zon);
#endif

    fscanf(fl, " %d %d %d ", &tmp1, &tmp2, &tmp3);
    zone_table[zon].climate.season_pattern = tmp1;
    zone_table[zon].climate.flags = tmp2;
    zone_table[zon].climate.energy_add = tmp3;

    fscanf(fl, " %d %d %d %d ", &tmp1, &tmp2, &tmp3, &tmp4);
    zone_table[zon].climate.season_wind[0] = BOUNDED(0, tmp1, 5);
    zone_table[zon].climate.season_wind[1] = BOUNDED(0, tmp2, 5);
    zone_table[zon].climate.season_wind[2] = BOUNDED(0, tmp3, 5);
    zone_table[zon].climate.season_wind[3] = BOUNDED(0, tmp4, 5);

    fscanf(fl, " %d %d %d %d ", &tmp1, &tmp2, &tmp3, &tmp4);
    zone_table[zon].climate.season_wind_dir[0] = BOUNDED(0, tmp1, 5);
    zone_table[zon].climate.season_wind_dir[1] = BOUNDED(0, tmp2, 5);
    zone_table[zon].climate.season_wind_dir[2] = BOUNDED(0, tmp3, 5);
    zone_table[zon].climate.season_wind_dir[3] = BOUNDED(0, tmp4, 5);

    fscanf(fl, " %d %d %d %d ", &tmp1, &tmp2, &tmp3, &tmp4);
    zone_table[zon].climate.season_wind_variance[0] = BOUNDED(0, tmp1, 1);
    zone_table[zon].climate.season_wind_variance[1] = BOUNDED(0, tmp2, 1);
    zone_table[zon].climate.season_wind_variance[2] = BOUNDED(0, tmp3, 1);
    zone_table[zon].climate.season_wind_variance[3] = BOUNDED(0, tmp4, 1);

    fscanf(fl, " %d %d %d %d ", &tmp1, &tmp2, &tmp3, &tmp4);
    zone_table[zon].climate.season_precip[0] = BOUNDED(0, tmp1, 8);
    zone_table[zon].climate.season_precip[1] = BOUNDED(0, tmp2, 8);
    zone_table[zon].climate.season_precip[2] = BOUNDED(0, tmp3, 8);
    zone_table[zon].climate.season_precip[3] = BOUNDED(0, tmp4, 8);

    fscanf(fl, " %d %d %d %d ", &tmp1, &tmp2, &tmp3, &tmp4);
    zone_table[zon].climate.season_temp[0] = BOUNDED(0, tmp1, 10);
    zone_table[zon].climate.season_temp[1] = BOUNDED(0, tmp2, 10);
    zone_table[zon].climate.season_temp[2] = BOUNDED(0, tmp3, 10);
    zone_table[zon].climate.season_temp[3] = BOUNDED(0, tmp4, 10);

    /* Set beginning values for zone real range */
    zone_table[zon].real_bottom = zone_table[zon].real_top = -1;

    /* allocate the command table */
    if (command_array[zon]) {
#ifdef MEM_DEBUG
      mem_use[MEM_ZONE] += (sizeof (struct reset_com) * command_array[zon]);
#endif
      CREATE(zone_table[zon].cmd, struct reset_com, (unsigned) command_array[zon]);
      zone_table[zon].cmd_count = command_array[zon];  /* Store the number of commands */
    } else {
      fprintf(stderr, "zone %d has NO commands!\n", zon);
      dump_core();
    }

    /* read the command table */
    cmd_no = 0;

#ifdef AREAS_DEBUG
    fprintf(stderr, "  <<START:  Reading Zone Commands [%d]>>\n", zon);
#endif

    for (;;) {
      fscanf(fl, " "); /* skip blanks */
      fscanf(fl, "%c", &c);

      if (c == '*') {
        fgets(buf, 8190, fl); /* skip command */
#ifdef AREAS_DEBUG
        buf[strlen(buf) - 1] = 0; /* nuke the \n for formatting reasons */
        fprintf(stderr, "    <<SKIPPING: *%s>>\n", buf);
#endif
        continue;
      }

      if (c == 'S') {
#ifdef AREAS_DEBUG
        fprintf(stderr, "  <<DONE:  Reading Zone Commands [%d]  Count: %d>>\n", zon, cmd_no);
#endif
        /* Don't store the 'S' command - it's just a terminator */
        break;
      }
      
      zone_table[zon].cmd[cmd_no].command = c;

      fscanf(fl, " %d %d %d", &tmp, &zone_table[zon].cmd[cmd_no].arg1, &zone_table[zon].cmd[cmd_no].arg2);
      zone_table[zon].cmd[cmd_no].if_flag = tmp;

#ifdef AREAS_DEBUG
      fprintf(stderr, "    <<[%6d] %c %d %5d %3d", cmd_no, c, tmp,
              zone_table[zon].cmd[cmd_no].arg1, zone_table[zon].cmd[cmd_no].arg2);
#endif

      if (c == 'G' || c == 'R') {
        fgets(buf, sizeof (buf) - 1, fl);

        if (sscanf(buf, "%d", &zone_table[zon].cmd[cmd_no].arg4) != 1) {
          zone_table[zon].cmd[cmd_no].arg4 = 100;
#ifdef AREAS_DEBUG
          fprintf(stderr, "        %3da>>\n", zone_table[zon].cmd[cmd_no].arg4);
#endif
        }
#ifdef AREAS_DEBUG
        else
          fprintf(stderr, "        %3d >>\n", zone_table[zon].cmd[cmd_no].arg4);
#endif
      } else if (c == 'M' || c == 'O' || c == 'E' || c == 'P' || c == 'F' || c == 'D' || c == 'X' || c == 'T') {
        int tt = fscanf(fl, " %d", &zone_table[zon].cmd[cmd_no].arg3);

#ifdef AREAS_DEBUG
        fprintf(stderr, " %5d ", zone_table[zon].cmd[cmd_no].arg3);
#endif

        if (tt != 0) { // only read buf and next int if there was a previous successful int read
          fgets(buf, sizeof (buf) - 1, fl);

          if (sscanf(buf, " %d", &zone_table[zon].cmd[cmd_no].arg4) != 1) {
            zone_table[zon].cmd[cmd_no].arg4 = 100;
#ifdef AREAS_DEBUG
            fprintf(stderr, " %3da>>\n", zone_table[zon].cmd[cmd_no].arg4);
#endif
          }
        }
#ifdef AREAS_DEBUG
        else
          fprintf(stderr, " %3d >>\n", zone_table[zon].cmd[cmd_no].arg4);
#endif
      } else {
        fgets(buf, sizeof (buf) - 1, fl); /* read comment */
#ifdef AREAS_DEBUG
        fprintf(stderr, "           >>");
#endif
      }

      cmd_no++;
      
      /* Bounds check to prevent buffer overflow */
      if (cmd_no >= command_array[zon]) {
        /* Try to derive the zone number from room range - zones typically use roomnum/100 */
        int estimated_zone_num = zone_table[zon].top / 100;
        fprintf(stderr, "WARNING: Zone #%d [index %d] (%s) has more commands than allocated space (%d).\n", 
                estimated_zone_num, zon, zone_table[zon].name, command_array[zon]);
        fprintf(stderr, "Stopping at command %d to prevent buffer overflow.\n", cmd_no);
        fprintf(stderr, "Last command was: '%c' %d %d %d\n", c, 
                zone_table[zon].cmd[cmd_no-1].if_flag,
                zone_table[zon].cmd[cmd_no-1].arg1,
                zone_table[zon].cmd[cmd_no-1].arg2);
        fprintf(stderr, "Skipping remaining commands until 'S' is found.\n");
        
        /* Skip remaining commands in the file until we find 'S' */
        while (c != 'S') {
          fscanf(fl, " "); /* skip blanks */
          fscanf(fl, "%c", &c);
          if (c == '*') {
            fgets(buf, 8190, fl); /* skip comment line */
          } else if (c != 'S') {
            fgets(buf, 8190, fl); /* skip rest of command line */
          }
        }
        /* Found 'S', now skip any remaining text on that line */
        fgets(buf, 8190, fl);
        break;
      }
    }

    zon++;
  }

  top_of_zone_table = --zon;

  NumberOfZones = top_of_zone_table;
  //  fprintf(stderr, "DEBUG: NumberOfZones: (%d), top_of_zone_table: (%d)\n",
  //    NumberOfZones, top_of_zone_table);

  free_string(check);
  free((char *) command_array);

  fclose(fl);
#ifdef AREAS_DEBUG
  fprintf(stderr, "<<DONE:  Booting Zones>>\n");
#endif

  //  for(count = 0; count <= top_of_zone_table; count++)
  //    fprintf(stderr, "Zone: %s has activeo_olc flag set to: (%d)\n",
  //      zone_table[count].name, zone_table[count].active_olc);
}

/* Free zone table memory to prevent leaks */
void free_zones(void) {
  int i;
  
  if (!zone_table)
    return;
    
  for (i = 0; i <= top_of_zone_table; i++) {
    /* Free allocated strings */
    if (zone_table[i].name)
      free_string(zone_table[i].name);
    if (zone_table[i].filename)
      free_string(zone_table[i].filename);
    if (zone_table[i].maker)
      free_string(zone_table[i].maker);
    
    /* Free command table */
    if (zone_table[i].cmd) {
      free(zone_table[i].cmd);
#ifdef MEM_DEBUG
      mem_use[MEM_ZONE] -= (sizeof(struct reset_com) * zone_table[i].cmd_count);
#endif
    }
  }
  
  /* Free the zone table itself */
  free(zone_table);
  zone_table = NULL;
#ifdef MEM_DEBUG
  mem_use[MEM_ZONE] -= (sizeof(struct zone_data) * (top_of_zone_table + 1));
#endif
}

/* Changelog of modifications to this routine. --MIAX o_o

Fri Oct 20 1:25:00 EST 2000 Miax

 * db.c (boot_world): Debugged the OLC additions, and added two new values
    to the top of db.c: NORMAL_FREE_OLC_ROOMS sets the number of rooms that
    olc will reserve per zone for creation of new rooms in that zone. I set
    the initial value to 250, meaning that if the area maker left more than
    250 room numbers un-used, OLC will create these rooms for OLC that all
    have the FREE_OLC_ROOM flag set (meaning they will be invisible and
    not usable on the mud). For zones that are defined as being actively
    worked on by an area maker (defined using the olcdb command), then ALL
    available free room numbers in that zone are created for use with OLC
    as described below, so that the maker can use them all during a single
    OLC session of creation if they so choose. Because of the bastardized
    way that boot_world reads in and stores rooms, it was much easier to
    simple define a set number of Free OLC Rooms for memory allocation,
    which is usually about 10,000 more rooms than is normally setup by
    OLC. This increases memory usage some, but always by a consistant
    amount, and made the coding alot easier. I will likely automate this
    better later on. As of now the new boot_world works perfectly, and I
    now have ready-made blank rooms in every zone automatically defined
    each reboot that OLC can easily grab and use. This is a breakthrough
    in the goal of getting room OLC up and running.


Wed Oct 18 1:10:00 EST 2000 Miax

 * db.c (boot_world): Re-wrote most of the routine, cleaned up the syntax
     and added many more commentary lines. Added some complex functionality
     to the routine to add invisible "Free OLC Rooms" in each zone that has
     un-used virtual room numbers. These empty rooms have a new room flag,
     FREE_OLC_ROOM, which will be used to exclude them from all room lookups,
     zone/room statting, and the like. They will for all intents and purposes
     Not exist until OLC needs a free room in that particular zone for creating
     a room when the area creater builds a new room. In that case, OLC just
     removes this flag, puts the maker in the room, adds any vectors to the
     room if they were pre-specified by the maker, and away the maker goes.
     The beauty of it is that we don't have to re-allocate the world struct,
     and the new rooms are already numbered in the real_room range, meaning
     they are viable and usable immediately. ;) Its big drawback is that it
     will consume more memory with all these empty rooms, but it's the only
     solution that will allow makers to forge actively on-line and not have
     to re-number the entire mud (which would be much harsher). As the rooms
     have nullified titles and room descripts, and no exits, they will in
     reality take up very little memory, so this "buldging" of the mud for
     integrated OLC room creation is accepted as the pay-off is high. If the
     zone in question has no skipped room numbers (e.g. the maker incremented
     their room numbers by 1 instead of by multiples of 5 or 10), and also
     if the maker used all the free rooms in that zone, then there are
     literally no free virtual room numbers in the zone's room range that
     are free for newly created OLC rooms, and the code will spit out error
     messages to the area maker telling them this, and that they will have
     to either start a new zone and connect them, or re-number the old
     zone into a bigger room range. Normal zones will never have more than
     250 "free olc rooms" reserved per boot, even if there are 10,000 free
     virtual room numbers in the zone (such as in the trackless sea). If the
     zone is flagged by the OLC database as being actively worked on, then
     it will make every free room number in the zone available as "free olc
     rooms" for creation, so that the area maker can work right up to the end
     of their room block if they wanted to. --MIAX 10/18/00
 */

void boot_world(void) {
  FILE *fl = NULL;
  char *temp, chk[MAX_STRING_LENGTH], tmp_buf[MAX_STRING_LENGTH];

  char OLCName[50] = "Free OLC Room (Reserved)";
  char OLCDescription[50] = "None - Edit Me!\n";

  unsigned int room_nr = 0; // this is the real room number incremented in the main loop
  unsigned int virtual_nr; // this is the vnum scanned in from the file

  unsigned int room_size, size;
  int math = 0, tmp, tmp2, i, gulf, whichzone, zone;
  int new_zone = 0, found_zone = 0, omgwtf;
  unsigned int lastzone_virtual_nr = 0, last_virtual_nr = 0, last_zone_nr = -1;
  unsigned int bottom_of_zone, top_of_zone, zonenum = 0;
  struct extra_descr_data *new_descr;
  unsigned int rf;
  unsigned int count, num_rooms;
  bool flag = TRUE;

  // strangely this is NOT defined normally.
  // determine if it works some day -Azuth
#ifdef SHARED_STRINGS
  int dlist = 0, dsaved = 0L;

  struct pc {
    char *p;
  } *d_list = 0;
#endif

#ifdef AREAS_DEBUG
  fprintf(stderr, "<<START:  Booting World>>\n");
#endif

  fl = fopen(WORLD_FILE, "r");
  if (!fl) {
    perror("fopen");
    logit(LOG_EXIT, "boot_world: could not open world file.");
    dump_core();
  }

#ifdef AREAS_DEBUG
  fprintf(stderr, "  <<START:  Counting Rooms>>\n");
#endif

  /* Count the number of rooms, to make allocation more efficient!! */
  num_rooms = 0;
  for (;;) {
    fgets(tmp_buf, MAX_STRING_LENGTH, fl);
    if (tmp_buf[0] == '$')
      break; // only occurs once in pos 0 at end of world.wld file

    if (tmp_buf[0] == '#')
      num_rooms++;
  }

  rewind(fl); // back to top of file

  logit(LOG_STATUS, "\t\t%d regular rooms", num_rooms);

#ifdef AREAS_DEBUG
  fprintf(stderr, "  <<DONE:  Counting Rooms (%d)>>\n", num_rooms);
#endif

  // Add on the maximum number of OLC rooms we will allow this boot,
  // for memory allocation.
  num_rooms += ADDITIONAL_ROOMS_FOR_OLC; // This is a huge chunk of memory, is 100000 really needed?


  // Allocate array of room structures in memory.
#ifdef MEM_DEBUG
  mem_use[MEM_ROOM] += (sizeof (struct room_data) * num_rooms);
#endif
  CREATE(world, struct room_data, (unsigned) num_rooms);

  logit(LOG_STATUS, "\t\t%d rooms allocated after %d OLC rooms added", num_rooms, ADDITIONAL_ROOMS_FOR_OLC);

#ifdef SHARED_STRINGS
  /* allocate array of pointers to pointers for list of unique room descs,
     this list will speed searching.
     It is freed after all rooms are read into memory.
     The point?  Duplicate room descs are only allocated
     once.  All rooms with identical descs will all use the s
     same desc (mainly oceans, but there are other
     duplications as well). JAB */
  CREATE(d_list, struct pc, (unsigned) num_rooms);
#endif

#ifdef AREAS_DEBUG
  fprintf(stderr, "  <<START:  Allocating Rooms>>\n");
#endif

  // Here we read in all the rooms for the zone, this is the master read loop
  // for rooms. It's rather in-efficient, but I'll leave it be for now. --MIAX
  room_nr = 0;
  for (;;) {
    // are we past the allocated number of rooms?
    if (room_nr >= num_rooms) {
      logit(LOG_STATUS, "WTF? 0-based room_nr (%d) is higher than we allocated? (%d)", room_nr, num_rooms);
      dump_core();
    }

    // Store the last room number we worked on for OLC calculations.
    if (room_nr == 0) {
      lastzone_virtual_nr = 0;
      last_virtual_nr = 0;
    } else {
      lastzone_virtual_nr = last_virtual_nr;
      last_virtual_nr = virtual_nr;
    }

    // Read the new room number.
    virtual_nr = -1;
    fscanf(fl, " #%d\n", &virtual_nr); // I guess for now we just hope and pray this worked :() FIXME later -Azuth

#ifdef AREAS_DEBUG
    fprintf(stderr, "    <<Room: [%5d] (%5d)>> \n", room_nr, virtual_nr);
#endif

    // Make sure the room number we just read is valid and in order.
    if (room_nr) { // no need to check if room is 0
      if (world[room_nr - 1].number == virtual_nr) {
        logit(LOG_DEBUG, "Duplicate room entry: (%d)", virtual_nr);
        fprintf(stderr, "Duplicate room entry: (%d)", virtual_nr);
        dump_core();
      }

      if (world[room_nr - 1].number > virtual_nr) {
        logit(LOG_DEBUG, "Room (%d, %d) out of order.", world[room_nr - 1].number, virtual_nr);
        fprintf(stderr, "Room (%d, %d) out of order.", world[room_nr - 1].number, virtual_nr);
        dump_core();
      }
    }

    // Read in next line, which is always a room title, or end-of-file marker: $~
    temp = fread_string(fl);
    if (*temp == '$')
      break; // Don't do OLC processing if were at the end of the file.

    // NOTE NOTE NOTE NOTE
    // In this spot in the boot_world process, we know the room number and name.
    // If there is any extra processing to be done on this room for other code,
    // and you don't need to know room flags or such, put that code here. --MIAX


    /* Determine the room number, check against the last virtual_nr value to see
    if the maker skipped rooms. If so, then allocate the skipped rooms as
    free OLC rooms (up to 250 max per zone per boot). If we have already
    found and defined 250 rooms in this zone, discard the rest. If we have
    less than 250 free OLC rooms defined, then determine how many room
    numberes were skipped between the last room and this room, and allocate
    them as OLC free rooms. We do this by setting the FREE_OLC_ROOM flag,
    which will make the room invisible when the mud is up. --MIAX 10/18/00 */
    if (OLC_BOOT_DEBUG == YES)
      fprintf(stderr, "DEBUG: Looking up zone number for room (R/V): %d/%d\n", room_nr, virtual_nr);

    // omgwtf
    new_zone = NO;
    found_zone = NO;
    for (whichzone = 0; whichzone <= (top_of_zone_table - 1); whichzone++) {
      if (whichzone == 0)
        top_of_zone = 0;
      else
        top_of_zone = zone_table[(whichzone - 1)].top;

      if ((OLC_BOOT_DEBUG == YES) && (virtual_nr > 89) && (virtual_nr < 102))
        fprintf(stderr, "XDEBUG: virtual_nr: %d, last-top: %d, top: %d\n",
              virtual_nr, top_of_zone, zone_table[whichzone].top);

      if ((virtual_nr > top_of_zone) && (virtual_nr <= zone_table[whichzone].top)) {
        // Found a match, we know what zone this room belongs to. Now check to
        // see if its the first room in this zone. If it is, we step back and
        // make sure we used up all the free rooms at the end of the last zone.
        // Then we loop again to take care of any empty rooms between the very
        // beginning of the zone's room range, and the first room number in the
        // next zone. --MIAX

        found_zone = YES;
        if (whichzone == 0) {
          top_of_zone = 0;
          last_zone_nr = 0;
        } else if (last_zone_nr != whichzone) {
          if (OLC_BOOT_DEBUG == YES) {
            fprintf(stderr, "\nDEBUG: -> We Crossed a Zone boundry.\n");
            fprintf(stderr, "DEBUG: -> Zone boundary found between zone #%d (%s) and #%d (%s)\n\n",
                    last_zone_nr, zone_table[last_zone_nr].name, whichzone, zone_table[whichzone].name);
          }

          new_zone = YES;
        }
        break;
      }
    }


    // Now we know what zone file we are in, process the room.
    // Execute main activity loop for OLC.
    // xxxx - Not sure if we really want new_zone == YES here.. ? o_O
    if (OLC_BOOT_DEBUG == YES)
      fprintf(stderr, "DEBUG: Master while loop for OLC room processing.\n");

    while ((found_zone == YES) || (new_zone == YES)) {
      // First handle the case where we are on a zone boundary.
      if (new_zone == YES) {
        //   if(zone_table[whichzone].real_bottom < 0)
        //    logit(LOG_DEBUG, "boot_world(): whichzone %d zone_table[whichzone].real_bottom is < 0!!", whichzone);
        // ok this is bad, that log was called for every whichzone except 0
        // meaning it passes a negative number to the world[] array, omgwtf is right!
        //if(world[zone_table[whichzone].real_bottom].number <= 1)
        //  bottom_of_zone = 0;
        //else
        bottom_of_zone = world[zone_table[last_zone_nr].real_bottom].number;
        zone = last_zone_nr;
      } else {
        if (world[zone_table[whichzone].real_bottom].number < 1)
          bottom_of_zone = 0;
        else
          bottom_of_zone = world[zone_table[whichzone].real_bottom].number;
        zone = whichzone;
      }

      // Some debugging info.
      if (OLC_BOOT_DEBUG == YES) {
        fprintf(stderr, "DEBUG: -> Working on: [%d -> %d <- %d], lastroom: %d, zone: #%d (%s)\n",
                bottom_of_zone, virtual_nr,
                //world[zone_table[zone].real_bottom].number, virtual_nr,
                zone_table[zone].top, last_virtual_nr,
                zone, zone_table[zone].name);
      }

      // Determine how many rooms are already setup for OLC in this zone.
      zone_table[zone].avail_to_olc = 0;
      for (omgwtf = zone_table[zone].real_bottom; omgwtf < zone_table[zone + 1].real_bottom; omgwtf++) {
        if (IS_CSET(world[omgwtf].room_flags, RESERVED_OLC))
          zone_table[zone].avail_to_olc++;
      }

      if (OLC_BOOT_DEBUG == YES)
        fprintf(stderr, "DEBUG: -> Zone #%d, Zone Bottom: %d, OLC rooms so far: %d\n",
              zone, bottom_of_zone, zone_table[zone].avail_to_olc);

      // If there are already enough OLC rooms made, don't make more.
      if (((zone_table[zone].avail_to_olc >= NORMAL_FREE_OLC_ROOMS) && (zone_table[zone].active_olc == NO)) ||
              (zone_table[zone].avail_to_olc >= MAXIMUM_FREE_OLC_ROOMS)) {
        if (new_zone == YES) {
          bottom_of_zone = virtual_nr;
          last_zone_nr = whichzone;
          new_zone = NO;
        }

        if (OLC_BOOT_DEBUG == YES)
          fprintf(stderr, "DEBUG: -> -> Already at limit of allowed OLC rooms (%d) for this zone.\n",
                zone_table[zone].avail_to_olc);
        continue;
      }

      if (new_zone == YES) {
        gulf = (zone_table[zone].top - last_virtual_nr);
        if (OLC_BOOT_DEBUG == YES)
          fprintf(stderr, "DEBUG: -> -> Gulf for new zone between rooms found: %d unused rooms.\n", gulf);
      } else {
        gulf = ((virtual_nr - last_virtual_nr) - 1);
        if (OLC_BOOT_DEBUG == YES)
          fprintf(stderr, "DEBUG: -> -> Gulf for current zone between rooms found: %d unused rooms.\n", gulf);
      }

      // If there is a gap in room numbering, add free olc rooms if necessary.
      if (gulf > 0) {
        if (OLC_BOOT_DEBUG == YES) {
          fprintf(stderr, "DEBUG: Found %d free rooms between %d and %d, Free: %d/%d\n",
                  gulf, last_virtual_nr, virtual_nr,
                  zone_table[zone].free, zone_table[zone].avail_to_olc);
          fprintf(stderr, "     Zone: %s -> RawGulf = %d, Active_olc = %d, Avail to olc = %d\n",
                  zone_table[zone].name, gulf,
                  zone_table[zone].active_olc, zone_table[zone].avail_to_olc);
        }

        if ((gulf >= NORMAL_FREE_OLC_ROOMS) && (zone_table[zone].active_olc == NO))
          gulf = NORMAL_FREE_OLC_ROOMS;
        else if ((gulf >= MAXIMUM_FREE_OLC_ROOMS) && (zone_table[zone].active_olc == YES))
          gulf = MAXIMUM_FREE_OLC_ROOMS;
        else if (gulf < 1)
          gulf = 0;

        if (OLC_BOOT_DEBUG == YES)
          fprintf(stderr, "DEBUG: -> -> Looping through %d unused rooms.\n", gulf);

        for (count = 1; count <= gulf; count++) {
          if (new_zone == YES)
            world[room_nr].number = (last_virtual_nr + count);
          else
            world[room_nr].number = (last_virtual_nr + count);

          if (world[room_nr].name)
            FREE(world[room_nr].name);

          world[room_nr].name = str_dup(OLCName);

          if (world[room_nr].description)
            FREE(world[room_nr].description);

          world[room_nr].description = str_dup(OLCDescription);
          world[room_nr].zone = zone;
          if (zone_table[zone].real_bottom == -1)
            zone_table[zone].real_bottom = room_nr;

          zone_table[zone].real_top = room_nr;
          world[room_nr].sector_type = 0;
          world[room_nr].length = 1;
          world[room_nr].width = 1;
          world[room_nr].height = 1;
          world[room_nr].funct = 0;
          world[room_nr].contents = 0;
          world[room_nr].people = 0;
          world[room_nr].light = 0;
#if TRAPS
          world[room_nr].trapper = 0;
#endif
          world[room_nr].track_visited = 0;
          for (tmp = 0; tmp <= 5; tmp++)
            world[room_nr].dir_option[tmp] = 0;

          world[room_nr].ex_description = 0;
          world[room_nr].chance_fall = 0;
          world[room_nr].minlvl = -1;
          world[room_nr].maxlvl = -1;
          world[room_nr].mana_alignment = -1;
          world[room_nr].mana = 0;

          CLEAR_CBITS(world[room_nr].room_flags, ROOM_FLAG_BYTES);
          SET_CBIT(world[room_nr].room_flags, RESERVED_OLC);

          if (OLC_BOOT_DEBUG == YES)
            fprintf(stderr, "-> Created Free OLC room: %d (real: %d) in zone %s, Avail To OLC: %d\n",
                  world[room_nr].number, room_nr,
                  zone_table[zone].name, zone_table[zone].avail_to_olc);

          room_nr++;
          TotalBlankRooms++;
        }
      }

      if (new_zone == YES) {
        //last_virtual_nr = virtual_nr;
        last_zone_nr = whichzone;
        new_zone = NO;
        bottom_of_zone = virtual_nr;
      }
      //        } else {
      found_zone = NO;
      break;
      //        }
    } // End of Free OLC room generator. o_o

    // Done with OLC, Back to normal processing of the new room.
    world[room_nr].number = virtual_nr;
    world[room_nr].name = temp;

    temp = fread_string(fl); /* read room desc */
#ifndef SHARED_STRINGS
    world[room_nr].description = temp;
#else
    /* the new bit, and yes, it will slow down bootup, but it will save RAM and right now, we have to save
       all we can. */

    /* reverse order will speed things through zones with lots of identical descs */
    for (tmp = dlist - 1; (tmp > -1) && str_cmp((d_list + tmp)->p, temp); tmp--)
      ;

    if (tmp > -1) {
      /* got a match */
      world[room_nr].description = (d_list + tmp)->p;
#ifdef AREAS_DEBUG
      fprintf(stderr, " DupD");
#endif
      if (temp) {
        dsaved += strlen(temp);
        free(temp);
        temp = NULL;
      }
    } else {
      /* new desc is unique, add it to d_list and off we go */
      world[room_nr].description = temp;
#ifdef AREAS_DEBUG
      fprintf(stderr, " NewD");
#endif
      (d_list + dlist++)->p = temp;
    }
#endif  /* ifndef SHARED_STRINGS */

    if (top_of_zone_table >= 0) {
      fscanf(fl, " %d ", &tmp);
      if (world[room_nr].number <= (zonenum ? zone_table[zonenum - 1].top : -1)) {
        logit(LOG_DEBUG, "Room nr %d (%d) is below zone %d.\n", room_nr, world[room_nr].number, zonenum);
        dump_core();
      }

      while (world[room_nr].number > zone_table[zonenum].top) {
        if (++zonenum > top_of_zone_table) {
          logit(LOG_DEBUG, "Room %d is outside of any zone.\n", virtual_nr);
          dump_core();
        }
      }

      world[room_nr].zone = zonenum;

      if (zone_table[zonenum].real_bottom == -1)
        zone_table[zonenum].real_bottom = room_nr;

      zone_table[zonenum].real_top = room_nr;
    }

    /* get the first set of room flags */
    CLEAR_CBITS(world[room_nr].room_flags, ROOM_FLAG_BYTES);
    fscanf(fl, " %u ", &rf);
    for (i = 0; i < 32; i++) {
      if (IS_SET(rf, 1U << i))
        SET_CBIT(world[room_nr].room_flags, i + 1);
    }

    if (IS_CSET(world[room_nr].room_flags, DARK))
      REMOVE_CBIT(world[room_nr].room_flags, DARK);

    if (IS_CSET(world[room_nr].room_flags, NO_MAGIC) && !IS_CSET(world[room_nr].room_flags, NO_SUMMON))
      SET_CBIT(world[room_nr].room_flags, NO_SUMMON);

#ifdef AREAS_DEBUG
    fprintf(stderr, " F%10u", (unsigned int) tmp);
#endif

    fscanf(fl, " %d ", &tmp);
    world[room_nr].sector_type = tmp;
#ifdef AREAS_DEBUG
    fprintf(stderr, " T%2d", tmp);
#endif

    /* New reads and a calculation for room dimensions. --MIAX 080896 */

    fscanf(fl, " %d ", &tmp);
    world[room_nr].length = tmp;

    fscanf(fl, " %d ", &tmp);
    world[room_nr].width = tmp;

    fscanf(fl, " %d ", &tmp);
    world[room_nr].height = tmp;

    /* need to have more space to accomodate the cbit conversion.. */
    /* oh man, this smells but it just wouldnt be sane to do it properly <g>  -- Alth */
    rf = 0;
    if (fscanf(fl, " %u \n", &rf) == 1) {
      for (i = 0; i < 32; i++) {
        if (IS_SET(rf, 1U << i))
          SET_CBIT(world[room_nr].room_flags, i + 33);
      }
    }

#ifdef AREAS_DEBUG
    fprintf(stderr, " Sz%3dx%3dx%3d", world[room_nr].length, world[room_nr].width, world[room_nr].height);
#endif
    /* room dimension message codes:
       0 - Non-Exsistant
       1 - Tiny
       2 - Small
       3 - Mid-sized
       4 - Large
       5 - Very Large
       6 - Huge
       7 - Enormous
       8 - Boundless

    Calculate basic room size in square feet, judge room size
    -- MIAX 10/18/00 */

    /* Compensate for outside rooms with an infinite ceiling by taking an
       average of the lenght and width. This way, the room size calculation
       won't be screwed up by the 500 ft outside ceiling. --MIAX         */

    if (world[room_nr].height == 500)
      math = ((world[room_nr].length + world[room_nr].width) / 2);
    else
      math = world[room_nr].height;

    size = ((int) world[room_nr].length * (int) world[room_nr].width * math);

    if (size <= 27) /* 3 x 3 x 3 = 27 square feet = Non-Exsistant*/
      room_size = 0;
    else if (size <= 125) /* 5 x 5 x 5 = 125 sq. feet = Tiny */
      room_size = 1;
    else if (size <= 1000) /* 10 x 10 x 10 = 1000 sq. feet = Small */
      room_size = 2;
    else if (size <= 15625) /* 25 x 25 x 25 = 15625 sq. feet = Mid-sized */
      room_size = 3;
    else if (size <= 125000) /* 50 x 50 x 50 = 125000 sq. feet = Large */
      room_size = 4;
    else if (size <= 421875) /* 75 x 75 x 75 = 421875 sq. ft. = Very Large */
      room_size = 5;
    else if (size <= 1000000) /* 100 x 100 x 100 = 1 Million sq. ft. = Huge */
      room_size = 6;
    else if (size <= 15625000) /* 250 x 250 x 250 = 15 Million sf = Enormous */
      room_size = 7;
    else /* Anything bigger = Boundless */
      room_size = 8;

    world[room_nr].size = room_size;
#ifdef AREAS_DEBUG
    fprintf(stderr, " (%d)>>\n", room_size);
#endif

    /* If the room is narrow, automatically set the room to single file.  If there are too many exits,
       the single file code will automatically strip off the single file flag, and no one will ever know
       it was there to begin with. */
    if ((world[room_nr].length < 6) || (world[room_nr].width < 6))
      SET_CBIT(world[room_nr].room_flags, SINGLE_FILE);

    /* End of room dimension add-on */
    world[room_nr].funct = 0;
    world[room_nr].contents = 0;
    world[room_nr].people = 0;
    world[room_nr].light = 0; /* Zero light sources */
#if TRAPS
    world[room_nr].trapper = 0;
#endif
    /* Set the visited field to 0.  This is needed as a marker for visited node for thief skill TRACK */
    world[room_nr].track_visited = 0;

    for (tmp = 0; tmp <= 5; tmp++)
      world[room_nr].dir_option[tmp] = 0;

    world[room_nr].ex_description = 0;
    world[room_nr].chance_fall = 0;
    world[room_nr].minlvl = -1;
    world[room_nr].maxlvl = -1;
    world[room_nr].mana_alignment = -1;
    world[room_nr].mana = 0;


    for (;;) {
      fscanf(fl, " %s \n", chk);

      if (*chk == 'D') { /* direction field */
#ifdef AREAS_DEBUG
        fprintf(stderr, "      <<Dir: %d", atoi(chk + 1));
#endif
        setup_dir(fl, room_nr, atoi(chk + 1));
      } else if (*chk == 'E') { /* extra description field */
#ifdef MEM_DEBUG
        mem_use[MEM_E_DSCR] += sizeof (struct extra_descr_data);
#endif
        CREATE(new_descr, struct extra_descr_data, 1);

#ifdef AREAS_DEBUG
        fprintf(stderr, "      <<Xtra:");
#endif
        new_descr->keyword = fread_string(fl);
        new_descr->description = fread_string(fl);
        new_descr->next = world[room_nr].ex_description;
        world[room_nr].ex_description = new_descr;
#ifdef AREAS_DEBUG
        fprintf(stderr, " %s>>\n", new_descr->keyword);
#endif
      } else if (*chk == 'R') {
        fscanf(fl, " %d ", &tmp);
#ifdef AREAS_DEBUG
        fprintf(stderr, "      <<LvlL: %2d", tmp);
#endif
        /* added for debugging purposes -- DTS 2/3/95 */
        if (tmp >= -1)
          world[room_nr].minlvl = tmp;
        else {
          logit(LOG_DEBUG, "Room [%d] has minlvl < -1.", world[room_nr].number);
          world[room_nr].minlvl = -1;
        }

        fscanf(fl, " %d ", &tmp);
        /* ditto here -- DTS */
#ifdef AREAS_DEBUG
        fprintf(stderr, "(%2d) %2d", world[room_nr].minlvl, tmp);
#endif
        if (tmp <= (MAXLVL + 1))
          world[room_nr].maxlvl = tmp;
        else if (tmp < world[room_nr].minlvl) {
          logit(LOG_DEBUG, "Room [%d] has maxlvl < minlvl.", world[room_nr].number);
          world[room_nr].maxlvl = world[room_nr].minlvl;
        } else {
          logit(LOG_DEBUG, "Room [%d] has maxlvl > MAXLVL + 1 (60).", world[room_nr].number);
          world[room_nr].maxlvl = 60;
        }
#ifdef AREAS_DEBUG
        fprintf(stderr, "(%2d)>>\n", world[room_nr].maxlvl);
#endif
      } else if (*chk == 'F') {
        fscanf(fl, "%d ", &tmp);
        world[room_nr].chance_fall = tmp;
#ifdef AREAS_DEBUG
        fprintf(stderr, "      <<%%F: %3d>>\n", tmp);
#endif
      } else if (*chk == 'M') {
        fscanf(fl, " %d %d ", &tmp, &tmp2);
        world[room_nr].mana_alignment = tmp;
        world[room_nr].mana = tmp2;
#ifdef AREAS_DEBUG
        fprintf(stderr, "      <<Mana (obsolete): %5d %5d>>\n", tmp, tmp2);
#endif
      } else if (*chk == 'S') { /* end of current room */
        break;
      }
    }

    if (TERRAIN_NOGROUND(room_nr) &&
            (!world[room_nr].dir_option[5] || (world[room_nr].dir_option[5]->to_room == room_nr))) {
      logit(LOG_DEBUG, "Room %d, is NO_GROUND but has no valid 'down' exit", world[room_nr].number);
      world[room_nr].sector_type = SECT_INSIDE;
    }

    if ((world[room_nr].chance_fall > 0) &&
            (!world[room_nr].dir_option[5] || (world[room_nr].dir_option[5]->to_room == room_nr))) {
      logit(LOG_DEBUG, "Room %d, has %d%% chance fall, but has no valid 'down' exit",
              world[room_nr].chance_fall, world[room_nr].number);
      world[room_nr].chance_fall = 0;
    }

    room_nr++;
  } // main for(;;) loop reading each room

#ifdef AREAS_DEBUG
  fprintf(stderr, ">>\n  <<DONE:  Allocating Rooms>>\n");
#endif

  free(temp); /* cleanup the area containing the terminal $ */

#ifdef SHARED_STRINGS
  logit(LOG_STATUS, "%d duplicate room descs, %ld bytes saved", (num_rooms - dlist + 1), dsaved);

  /* free up our unique desc list */
  free((char *) d_list);
#endif

  fclose(fl);
  top_of_world = --room_nr;

  for (count = 0; count <= (top_of_zone_table - 1); count++) {
    zone_table[count].free = 0;
    zone_table[count].avail_to_olc = 0;
    zone_table[count].actual_rooms = 0;

    // fprintf(stderr, "Working on zone #%d (%s), Top: %d/%d\nRooms: ",
    // count, zone_table[count].name, zone_table[count].real_top,
    // zone_table[count].top);


    for (omgwtf = zone_table[count].real_bottom; omgwtf <= zone_table[count].real_top; omgwtf++) {
      // fprintf(stderr, "room = #%d (top: %d/%d):", world[omgwtf].number,
      // zone_table[count].real_top, world[zone_table[count].real_top].number);

      if ((!world[omgwtf].name) && (!IS_CSET(world[omgwtf].room_flags, RESERVED_OLC))) {
        // fprintf(stderr, " %dF", omgwtf);
        zone_table[count].free++;
      } else if (IS_CSET(world[omgwtf].room_flags, RESERVED_OLC)) {
        // fprintf(stderr, " %dO", omgwtf);
        zone_table[count].avail_to_olc++;
      } else {
        // fprintf(stderr, " %dR", omgwtf);
        zone_table[count].actual_rooms++;
      }
    }

    // Now tally up the rooms that are left blank at the end.
    if (world[zone_table[count].real_top].number < zone_table[count].top) {
      for (omgwtf = world[zone_table[count].real_top].number; omgwtf <= zone_table[count].top; omgwtf++) {
        // fprintf(stderr, " %dF\n", omgwtf);
        zone_table[count].free++;
      }
    }

    /*
        fprintf(stderr, "\nDEBUG New Zone #%d: free: %d, olc: %d, actual: %d\n",
          count, zone_table[count].free,
          zone_table[count].avail_to_olc,
          zone_table[count].actual_rooms);
        fprintf(stderr, "        Name: %s, Real Range %d-%d (%d), Virtual Range: %d-%d (%d)\n",
          zone_table[count].name, zone_table[count].real_bottom, zone_table[count].real_top,
          (zone_table[count].real_top - zone_table[count].real_bottom),
          world[zone_table[count].real_bottom].number, zone_table[count].top,
          (zone_table[count].top - world[zone_table[count].real_bottom].number));
     */
  }

  fprintf(stderr, "There are %d rooms allocated to OLC across all zones.\n", TotalBlankRooms);

#ifdef AREAS_DEBUG
  fprintf(stderr, "<<DONE:  Booting World>>\n");
#endif
}

/* read direction data */
void setup_dir(FILE * fl, int room, int dir) {
  int tmp;

#ifdef MEM_DEBUG
  mem_use[MEM_EXITS] += sizeof (struct room_direction_data);
#endif
  CREATE(world[room].dir_option[dir], struct room_direction_data, 1);

  world[room].dir_option[dir]->general_description = fread_string(fl);
  world[room].dir_option[dir]->keyword = fread_string(fl);

  fscanf(fl, " %d ", &tmp);
#ifdef AREAS_DEBUG
  fprintf(stderr, " F%2d", tmp);
#endif
  if (tmp) {
    if (tmp >= 16) {
      SET_BIT(world[room].dir_option[dir]->exit_info, EX_TRAPPED);
      tmp -= 16;
    }

    if (tmp >= 8) {
      SET_BIT(world[room].dir_option[dir]->exit_info, EX_BLOCKED);
      tmp -= 8;
    }

    if (tmp >= 4) {
      SET_BIT(world[room].dir_option[dir]->exit_info, EX_SECRET);
      tmp -= 4;
    }

    if (tmp)
      SET_BIT(world[room].dir_option[dir]->exit_info, EX_ISDOOR);

    if (tmp == 2)
      SET_BIT(world[room].dir_option[dir]->exit_info, EX_PICKABLE);

    if (tmp == 3)
      SET_BIT(world[room].dir_option[dir]->exit_info, EX_PICKPROOF);
  } else
    world[room].dir_option[dir]->exit_info = 0;
#ifdef AREAS_DEBUG
  fprintf(stderr, " /%2d", world[room].dir_option[dir]->exit_info);
#endif

  fscanf(fl, " %d ", &tmp);
  world[room].dir_option[dir]->key = tmp;
#ifdef AREAS_DEBUG
  fprintf(stderr, " K%5d", world[room].dir_option[dir]->key);
#endif

  fscanf(fl, " %d ", &tmp);
  world[room].dir_option[dir]->to_room = tmp;

#ifdef AREAS_DEBUG
  fprintf(stderr, " To %5d>>\n", world[room].dir_option[dir]->to_room);
#endif

  /* Okie this is how it works! If you want trap a door add 16 to the */
  /* exit flag and then the following fields after the to_room number */
  /* <trap_state> = 0 disabled, 1 enabled (use always 1 in wld files( */
  /* <trap_type> = 1 blade, 2 poison, 3 rock, 4 fireball, 5 lightning */
  /* <trap_min_damage> = nuff' said me think                          */
  /* <trap_max_damage> = as above                                     */
  /* <trap_effect> = 0 trap hits only the player who mess with the    */
  /* door, 1 trap hits all the players in the room                    */
  /* <trap_hardness> = how much hard is to detect and remove the trap */
  /* hardness is percentual so stay into a -40 +40 range AT MAX       */
  /* <trap_active%> = how often a trap is active when world boots     */
  /* example:                                                         */
  /* 24 0 10000 1 2 10 50 0 10 50                                     */
  /* which means: door unpickable secret and trapped, no key, to room */
  /* 10000, trap enabled, trap poison, damage from 10 to 50 hps, only */
  /* to the player which tries to open the door, penalty of 10 in     */
  /* detecting and removing the trap, and there is a 50% of           */
  /* possibilities than the trap is active at boot.                   */
  /* Ilsie 072898                                                     */

  if (IS_SET(world[room].dir_option[dir]->exit_info, EX_TRAPPED)) {
    fscanf(fl, " %d ", &tmp);
    world[room].dir_option[dir]->trap_state = tmp;

    fscanf(fl, " %d ", &tmp);
    world[room].dir_option[dir]->trap_type = tmp;

    fscanf(fl, " %d ", &tmp);
    world[room].dir_option[dir]->trap_min_damage = tmp;

    fscanf(fl, " %d ", &tmp);
    world[room].dir_option[dir]->trap_max_damage = tmp;

    fscanf(fl, " %d ", &tmp);
    world[room].dir_option[dir]->trap_effect = tmp;

    fscanf(fl, " %d ", &tmp);
    world[room].dir_option[dir]->trap_hardness = BOUNDED(-100, tmp, +100);

    fscanf(fl, " %d ", &tmp);
    world[room].dir_option[dir]->trap_load_percent = BOUNDED(0, tmp, +100);

    if (number(1, 100) > world[room].dir_option[dir]->trap_load_percent)
      world[room].dir_option[dir]->trap_state = 0;
  }
}

/* this used to be misnamed renum_world(), it replaces all the virtual room numbers in room exits with their
   real numbers.  JAB */

void renumber_exits(void) {
  int room, door;

  // Added sanity check to allow for null (empty) rooms. --MIAX 10/17/00
  for (room = 0; room <= top_of_world; room++) {
    if (!IS_CSET(world[room].room_flags, RESERVED_OLC)) {
      for (door = 0; door < 6; door++) {
        if (world[room].dir_option[door] && (world[room].dir_option[door]->to_room > NOWHERE)) {
          world[room].dir_option[door]->to_room = real_room(world[room].dir_option[door]->to_room);
        }
      }
    }
  }
}

/* generate index table for object or monster file */
P_index generate_indices(FILE *fl, int *top) {
  P_index t_idx;
  char buf[MAX_STRING_LENGTH + 1];
  int i = 0, num;

  rewind(fl);

  /* first time just count */
  rewind(fl);
  num = 0;
  buf[0] = 0;
  for (;;) {
    fgets(buf, MAX_STRING_LENGTH, fl);
    if (buf[0] == '$')
      break;

    if (buf[0] == '#')
      num++;
  }

  logit(LOG_STATUS, "\t\t%d entries allocated", num);

  /* allocate array of index_data */

#ifdef MEM_DEBUG
  mem_use[MEM_IDX] += (sizeof (struct index_data) * num);
#endif
  CREATE(t_idx, struct index_data, (unsigned) num);

  rewind(fl);

  for (;;) {
    if (fgets(buf, MAX_STRING_LENGTH, fl)) {
      if (buf[0] == '#') {
        sscanf(buf, "#%d", &t_idx[i].virtual);
        t_idx[i].pos = ftell(fl);
        t_idx[i].number = 0;
        t_idx[i].func = NULL;
        t_idx[i].keys = NULL;
        t_idx[i].desc1 = NULL;
        t_idx[i].desc2 = NULL;
        t_idx[i].desc3 = NULL;

        if (i && (t_idx[i - 1].virtual >= t_idx[i].virtual)) {
          logit(LOG_EXIT, "%s index (%d, %d) out of order.", (mob_index) ? "Obj" : "Mob", t_idx[i - 1].virtual, t_idx[i].virtual);
          dump_core();
        }
        i++;
      } else if (buf[0] == '$') /* EOF */
        break;
    } else {
      logit(LOG_EXIT, "Unexpected EOF in %s file.", (mob_index) ? "obj" : "mob");
      dump_core();
    }
  }

  *top = i - 2;
  return (t_idx);
}

/* this is a real bear, basically, all references to mob/obj/room have to be converted from virtual number
   to the corresponding real number.  I added error checking to disable any zone command that comes out
   silly (IE, room/mob/obj doesn't exist in current world.)  Also, AREA_DEBUG reporting.  JAB */

/* This is where stat z cmds gets it's info -Azuth */
void renum_zone_table(void) {
  int zone, comm;

#ifdef AREAS_DEBUG
  char hold;

  fprintf(stderr, "<<START:  Renumbering Zone Commands>>\n");
#endif

  /* logit(LOG_DEBUG, "DEBUG: renum_zone_table() - Starting, top_of_zone_table = %d", top_of_zone_table);
  fprintf(stderr, "DEBUG: renum_zone_table() - Starting, top_of_zone_table = %d\n", top_of_zone_table); */

  for (zone = 0; zone <= top_of_zone_table; zone++) {
    if (zone % 10 == 0) {
      /* logit(LOG_DEBUG, "DEBUG: renum_zone_table() - Processing zone %d of %d", zone, top_of_zone_table);
      fprintf(stderr, "DEBUG: renum_zone_table() - Processing zone %d of %d\n", zone, top_of_zone_table); */
    }
    
    if (!zone_table[zone].cmd) {
      /* logit(LOG_DEBUG, "DEBUG: renum_zone_table() - WARNING: Zone %d has NULL cmd array!", zone);
      fprintf(stderr, "DEBUG: renum_zone_table() - WARNING: Zone %d has NULL cmd array!\n", zone); */
      continue;
    }
    
    /* Extra debug for zones around the crash point */
    if (zone >= 65 && zone <= 80) {
      /* logit(LOG_DEBUG, "DEBUG: renum_zone_table() - About to process zone %d (name: %s)", 
            zone, zone_table[zone].name ? zone_table[zone].name : "NULL");
      fprintf(stderr, "DEBUG: renum_zone_table() - About to process zone %d (name: %s)\n", 
              zone, zone_table[zone].name ? zone_table[zone].name : "NULL"); */
      
      /* Extra debug for zone 70 specifically */
      if (zone == 70) {
        /* logit(LOG_DEBUG, "DEBUG: renum_zone_table() - Zone 70 details: lifespan=%d, top=%d, reset_mode=%d", 
              zone_table[zone].lifespan, zone_table[zone].top, zone_table[zone].reset_mode);
        fprintf(stderr, "DEBUG: renum_zone_table() - Zone 70 details: lifespan=%d, top=%d, reset_mode=%d\n", 
                zone_table[zone].lifespan, zone_table[zone].top, zone_table[zone].reset_mode); */
      }
    }
    
    /* Safety check for NULL cmd array */
    if (!zone_table[zone].cmd) {
      /* logit(LOG_DEBUG, "DEBUG: renum_zone_table() - ERROR: Zone %d has NULL cmd array in loop!", zone);
      fprintf(stderr, "DEBUG: renum_zone_table() - ERROR: Zone %d has NULL cmd array in loop!\n", zone); */
      continue;
    }
    
    for (comm = 0; comm < zone_table[zone].cmd_count && zone_table[zone].cmd[comm].command != 'S'; comm++) {
      /* Extra debug for zones 70-80 */
      if (zone >= 70 && zone <= 80) {
        if (comm % 50 == 0 || comm < 5) {
          /* logit(LOG_DEBUG, "DEBUG: renum_zone_table() - Zone %d, cmd[%d]: command='%c', arg1=%d, arg2=%d, arg3=%d", 
                zone, comm, zone_table[zone].cmd[comm].command, 
                zone_table[zone].cmd[comm].arg1,
                zone_table[zone].cmd[comm].arg2,
                zone_table[zone].cmd[comm].arg3);
          fprintf(stderr, "DEBUG: renum_zone_table() - Zone %d, cmd[%d]: command='%c', arg1=%d, arg2=%d, arg3=%d\n", 
                  zone, comm, zone_table[zone].cmd[comm].command, 
                  zone_table[zone].cmd[comm].arg1,
                  zone_table[zone].cmd[comm].arg2,
                  zone_table[zone].cmd[comm].arg3); */
        }
      }
      /* Safety check for very large command lists */
      if (comm > 10000) {
        /* logit(LOG_DEBUG, "DEBUG: renum_zone_table() - ERROR: Zone %d has more than 10000 commands!", zone);
        fprintf(stderr, "DEBUG: renum_zone_table() - ERROR: Zone %d has more than 10000 commands!\n", zone); */
        break;
      }
      
      /* Extra debug for zones around the crash point */
      if (zone >= 65 && zone <= 80 && comm < 5) {
        /* logit(LOG_DEBUG, "DEBUG: renum_zone_table() - Zone %d, cmd[%d].command = '%c'", 
              zone, comm, zone_table[zone].cmd[comm].command); */
      }
      
#ifdef AREAS_DEBUG
      hold = zone_table[zone].cmd[comm].command;
#endif
      // store up info for stat z cmds -Azuth
      zone_table[zone].cmd[comm].arg1v = zone_table[zone].cmd[comm].arg1;
      zone_table[zone].cmd[comm].arg2v = zone_table[zone].cmd[comm].arg2;
      zone_table[zone].cmd[comm].arg3v = zone_table[zone].cmd[comm].arg3;
      zone_table[zone].cmd[comm].arg4v = zone_table[zone].cmd[comm].arg4;
      zone_table[zone].cmd[comm].old_command = zone_table[zone].cmd[comm].command;

      if (comm % 100 == 0 && comm > 0) {
        /* logit(LOG_DEBUG, "DEBUG: renum_zone_table() - Zone %d, processing command %d", zone, comm); */
      }
      
      /* Extra debug before switch for zones 70-80 */
      if (zone >= 70 && zone <= 80 && (comm % 50 == 0 || comm < 5)) {
        /* logit(LOG_DEBUG, "DEBUG: renum_zone_table() - Zone %d, about to process command '%c' at index %d", 
              zone, zone_table[zone].cmd[comm].command, comm);
        fprintf(stderr, "DEBUG: renum_zone_table() - Zone %d, about to process command '%c' at index %d\n", 
                zone, zone_table[zone].cmd[comm].command, comm); */
      }
      
      switch (zone_table[zone].cmd[comm].command) {
        case 'D':
          zone_table[zone].cmd[comm].arg1 = real_room(zone_table[zone].cmd[comm].arg1);

          if (zone_table[zone].cmd[comm].arg1 == NOWHERE)
            zone_table[zone].cmd[comm].command = '!';
          break;

        case 'E':
          zone_table[zone].cmd[comm].arg1 = real_object(zone_table[zone].cmd[comm].arg1);

          if (zone_table[zone].cmd[comm].arg1 == NOWHERE)
            zone_table[zone].cmd[comm].command = '!';
          break;

        case 'F': /* added 5-20-96 -- DDW */
          zone_table[zone].cmd[comm].arg1 = real_room(zone_table[zone].cmd[comm].arg1);
          zone_table[zone].cmd[comm].arg2 = real_mobile(zone_table[zone].cmd[comm].arg2);
          zone_table[zone].cmd[comm].arg3 = real_mobile(zone_table[zone].cmd[comm].arg3);

          if ((zone_table[zone].cmd[comm].arg1 == NOWHERE) || (zone_table[zone].cmd[comm].arg2 == NOWHERE) ||
                  (zone_table[zone].cmd[comm].arg3 == NOWHERE))
            zone_table[zone].cmd[comm].command = '!';
          break;

        case 'G':
          zone_table[zone].cmd[comm].arg1 = real_object(zone_table[zone].cmd[comm].arg1);

          if (zone_table[zone].cmd[comm].arg1 == NOWHERE)
            zone_table[zone].cmd[comm].command = '!';
          break;

        case 'M': /* mobile to room */
          zone_table[zone].cmd[comm].arg1 = real_mobile(zone_table[zone].cmd[comm].arg1);
          zone_table[zone].cmd[comm].arg3 = real_room(zone_table[zone].cmd[comm].arg3);

          if ((zone_table[zone].cmd[comm].arg1 == NOWHERE) || (zone_table[zone].cmd[comm].arg3 == NOWHERE))
            zone_table[zone].cmd[comm].command = '!';
          break;

        case 'O':
          zone_table[zone].cmd[comm].arg1 = real_object(zone_table[zone].cmd[comm].arg1);
          zone_table[zone].cmd[comm].arg3 = real_room(zone_table[zone].cmd[comm].arg3);

          if ((zone_table[zone].cmd[comm].arg1 == NOWHERE) || (zone_table[zone].cmd[comm].arg3 == NOWHERE))
            zone_table[zone].cmd[comm].command = '!';
          break;

        case 'P':
          zone_table[zone].cmd[comm].arg1 = real_object(zone_table[zone].cmd[comm].arg1);
          zone_table[zone].cmd[comm].arg3 = real_object(zone_table[zone].cmd[comm].arg3);

          if ((zone_table[zone].cmd[comm].arg1 == NOWHERE) || (zone_table[zone].cmd[comm].arg3 == NOWHERE))
            zone_table[zone].cmd[comm].command = '!';
          break;

        case 'R':
          zone_table[zone].cmd[comm].arg1 = real_room(zone_table[zone].cmd[comm].arg1);
          zone_table[zone].cmd[comm].arg2 = real_object(zone_table[zone].cmd[comm].arg2);

          if ((zone_table[zone].cmd[comm].arg1 == NOWHERE) || (zone_table[zone].cmd[comm].arg2 == NOWHERE))
            zone_table[zone].cmd[comm].command = '!';
          break;

        case 'T':
          /* Reset to midnight if an invalid hour is specified...*/
          if (zone_table[zone].cmd[comm].arg1 < -1 || zone_table[zone].cmd[comm].arg1 > 23)
            zone_table[zone].cmd[comm].arg1 = 0;

          /* Reset month/day to 0 if invalid */
          if (zone_table[zone].cmd[comm].arg2 < 0 || zone_table[zone].cmd[comm].arg2 > 35)
            zone_table[zone].cmd[comm].arg2 = 0;
          if (zone_table[zone].cmd[comm].arg3 < 0 || zone_table[zone].cmd[comm].arg3 > 7)
            zone_table[zone].cmd[comm].arg3 = 0;
          if (zone_table[zone].cmd[comm].arg4 < 0 || zone_table[zone].cmd[comm].arg4 > 17)
            zone_table[zone].cmd[comm].arg4 = 0;
          break;

        case 'X':
          if (zone_table[zone].cmd[comm].arg1 != -1)
            zone_table[zone].cmd[comm].arg1 = real_room(zone_table[zone].cmd[comm].arg1);

          zone_table[zone].cmd[comm].arg2 = real_mobile(zone_table[zone].cmd[comm].arg2);

          if (((zone_table[zone].cmd[comm].arg1 == NOWHERE) && (zone_table[zone].cmd[comm].arg1 != -1)))
            zone_table[zone].cmd[comm].command = '!';
          break;
      }

      if (zone_table[zone].cmd[comm].command == '!') {
        zone_table[zone].cmd[comm].if_flag = 0;
#ifdef AREAS_DEBUG
        fprintf(stderr, "  <<Zone [%3d]  %c Command [%5d]  Disabled>>\n", zone, hold, comm);
#endif
      }
    }
    /* logit(LOG_DEBUG, "DEBUG: renum_zone_table() - Zone %d completed with %d commands", zone, comm); */
  }

#ifdef AREAS_DEBUG
  fprintf(stderr, "<<DONE:  Renumbering Zone Commands>>\n");
#endif
  
  /* logit(LOG_DEBUG, "DEBUG: renum_zone_table() - Function completed successfully");
  fprintf(stderr, "DEBUG: renum_zone_table() - Function completed successfully\n"); */
}

// function currently unused

void MemReport(void) {
  int memused, memused2;
  int i;
  struct extra_descr_data *edd;
  int exits = 0, j, r;
  int mob_s, obj_s, room_s, exit_s, idx_s;
  int mob_c = 0, obj_c = 0;
  int db_mem = 0;
  P_obj o;

  idx_s = sizeof (struct index_data);

  for (memused = (top_of_mobt * idx_s), i = 0; i < top_of_mobt; i++) {
    mob_c += mob_index[i].number;
    if (mob_index[i].keys)
      memused += strlen(mob_index[i].keys);

    if (mob_index[i].desc1)
      memused += strlen(mob_index[i].desc1);

    if (mob_index[i].desc2)
      memused += strlen(mob_index[i].desc2);

    if (mob_index[i].desc3)
      memused += strlen(mob_index[i].desc3);
  }

  mob_s = sizeof (struct char_data) + sizeof (struct npc_only_data);

  logit(LOG_FILE, "Mem  (mobs): %-6d using %d (%d structs (%d each) + %d text) ",
          mob_c, memused + mob_s * mob_c, mob_s * mob_c, mob_s, memused);

  logit(LOG_FILE, "Average  (mobs): %d", (memused + mob_s * mob_c) / mob_c);

  db_mem += memused + mob_s * mob_c;
  obj_s = sizeof (struct obj_data);

  for (memused = (top_of_objt * idx_s), i = 0; i < top_of_objt; i++) {
    obj_c += obj_index[i].number;
    if (obj_index[i].keys)
      memused += strlen(obj_index[i].keys);

    if (obj_index[i].desc1)
      memused += strlen(obj_index[i].desc1);

    if (obj_index[i].desc2)
      memused += strlen(obj_index[i].desc2);

    if (obj_index[i].desc3)
      memused += strlen(obj_index[i].desc3);
  }

  for (o = object_list; o; o = o->next) {
    for (edd = o->ex_description; edd; edd = edd->next) {
      memused += sizeof (struct extra_descr_data);

      if (edd->description)
        memused += strlen(edd->description);

      if (edd->keyword)
        memused += strlen(edd->keyword);
    }
  }

  logit(LOG_FILE, "Mem  (objs): %-6d using %d (%d structs (%d each) + %d text)",
          obj_c, memused + obj_s * obj_c, obj_s * obj_c, obj_s, memused);

  logit(LOG_FILE, "Average  (objs): %d", (memused + obj_s * obj_c) / obj_c);

  db_mem += memused + obj_s * obj_c;
  room_s = sizeof (struct room_data);
  exit_s = sizeof (struct room_direction_data);

  // Added additional sanity check to allow for null (empty) rooms. --MIAX 10/17/00
  memused = 0;
  memused2 = 0;
  for (r = 0; r < top_of_world; r++) {
    if (!IS_CSET(world[r].room_flags, RESERVED_OLC)) {
      if (world[r].name)
        memused += strlen(world[r].name);

      if (world[r].description)
        memused += strlen(world[r].description);

      for (edd = world[r].ex_description; edd; edd = edd->next) {
        memused += sizeof (struct extra_descr_data);

        if (edd->description)
          memused += strlen(edd->description);

        if (edd->keyword)
          memused += strlen(edd->keyword);
      }

      for (j = 0; j < 6; j++) {
        if (world[r].dir_option[j]) {
          exits++;
          if (world[r].dir_option[j]->general_description)
            memused2 += strlen(world[r].dir_option[j]->general_description);

          if (world[r].dir_option[j]->keyword)
            memused2 += strlen(world[r].dir_option[j]->keyword);
        }
      }
    }
  }

  logit(LOG_FILE, "Mem (rooms): %-5d using %d (%d structs (%d each) + %d text)",
          top_of_world, memused + room_s * top_of_world, room_s * top_of_world, room_s, memused);

  logit(LOG_FILE, "Average (rooms): %d", (memused + room_s * top_of_world) / top_of_world);

  db_mem += memused + room_s * top_of_world;

  logit(LOG_FILE, "Mem (exits): %-5d using %d (%d structs (%d each) + %d text)",
          exits, memused2 + exits * exit_s, exits * exit_s, exit_s, memused2);

  logit(LOG_FILE, "Average (exits): %d", (memused2 + exit_s * exits) / exits);

  logit(LOG_FILE, "Room Total: %d", memused + room_s * top_of_world + memused2 + exit_s * exits);

  db_mem += room_s * top_of_world + memused2 + exit_s * exits;

  logit(LOG_FILE, "DB total ram: %d", db_mem);

  logit(LOG_FILE, "Events: %d (%d active), using %d (%d each)",
          event_counter[LAST_EVENT] + event_counter[LAST_EVENT + 1], event_counter[LAST_EVENT],
          (event_counter[LAST_EVENT] + event_counter[LAST_EVENT + 1]) * sizeof (struct event_data),
          sizeof (struct event_data));
}

/* Smandoggi's new weather setup */
void weather_setup(void) {
  int zon, s;

  /* default conditions for season values */
  const char winds[6] = {2, 12, 30, 40, 50, 80};
  const char precip[9] = {0, 0, 0, 1, 3, 8, 15, 23, 45};
  const char humid[9] = {4, 10, 20, 30, 40, 50, 60, 75, 100};
  const char temps[11] = {-20, -10, 0, 2, 7, 17, 23, 29, 37, 55, 95};

  for (zon = 0; zon <= top_of_zone_table; zon++) {
    /* get the season */
    s = get_season(zon);

    /* These are pretty standard start values */
    zone_table[zon].conditions.pressure = 980;
    zone_table[zon].conditions.free_energy = 10000;
    zone_table[zon].conditions.precip_depth = 0;

    /* These use the default conditions above */
    zone_table[zon].conditions.windspeed =
            winds[(int) zone_table[zon].climate.season_wind[s]];

    zone_table[zon].conditions.wind_dir =
            zone_table[zon].climate.season_wind_dir[s];

    zone_table[zon].conditions.precip_rate =
            precip[(int) zone_table[zon].climate.season_precip[s]];

    zone_table[zon].conditions.temp =
            temps[(int) zone_table[zon].climate.season_temp[s]];

    zone_table[zon].conditions.humidity =
            humid[(int) zone_table[zon].climate.season_precip[s]];

    /* Set ambient light */
    calc_light_zone(zon);
  }
}







/*************************************************************************
 *  procedures for resetting, both play-time and boot-time         *
 *********************************************************************** */

/*
 ** read a mobile from MOB_FILE
 **
 ** Improvements:
 **
 ** 1) If mobile has memory, create memory module as well
 */

P_char read_mobile(int nr, int type) {
  P_char mob = NULL;
  char Gbuf1[MAX_STRING_LENGTH], Gbuf2[MAX_STRING_LENGTH];
  char buf[MAX_INPUT_LENGTH], letter = 0, *tptr;
  int hasProc = FALSE, i, j, tmp = 0, tmp1, tmp2, tmp3, tmp4, tmp5, tmp6, r = 0, res = 0, mana = 0;
  uint utmp1, utmp2, utmp3, utmp4, utmp5;
  struct func_attachment *fn;

  i = nr;
  if (type == VIRTUAL) {
    nr = real_mobile(nr);
    if (nr < 0) {
      logit(LOG_DEBUG, "read_mobile: Mob %d not in database", i);
      dump_stack("Missing Mob");
      return NULL;
    }
  }

  if (nr < 0 || nr > top_of_mobt) {
    logit(LOG_DEBUG, "read_mobile: invalid rnum (%d). args %d, %s (max=%d)", 
          nr, i, type ? "VIRTUAL" : "REAL", top_of_mobt);
    dump_stack("Mob Invalid Rnum");
    return 0;
  }

  fseek(mob_f, mob_index[nr].pos, 0);

  mob = GetNewChar(NEW_NPC);
  
  if (!mob) {
    logit(LOG_DEBUG, "read_mobile: GetNewChar failed for mob %d", nr);
    return NULL;
  }

  mob->nr = nr;
  mob->desc = NULL;

  mob_index[nr].number++;

  /* *** String data *** */

  /* added pointers to the index struct, so that all mobs of the same type will
     now share all text.  This should save us a huge amount of RAM. -JAB */

  if (!mob_index[nr].keys) {
    mob->player.name = fread_string(mob_f);
    if (!mob->player.name) {
      logit(LOG_DEBUG, "read_mobile: Failed to read name for mob %d", nr);
      mob->player.name = str_dup("undefined");
    }
    for (j = 0; mob->player.name && *(mob->player.name + j); j++) /* make sure all keywords are lowercased */
      *(mob->player.name + j) = LOWER(*(mob->player.name + j));

    mob_index[nr].keys = mob->player.name;
  } else {
    skip_fread(mob_f);
    mob->player.name = mob_index[nr].keys;
  }

  if (!mob_index[nr].desc2) {
    mob->player.short_descr = fread_string(mob_f);
    mob_index[nr].desc2 = mob->player.short_descr;
  } else {
    skip_fread(mob_f);
    mob->player.short_descr = mob_index[nr].desc2;
  }

  if (!mob_index[nr].desc1) {
    mob->player.long_descr = fread_string(mob_f);
    mob_index[nr].desc1 = mob->player.long_descr;
  } else {
    skip_fread(mob_f);
    mob->player.long_descr = mob_index[nr].desc1;
  }

  if (!mob_index[nr].desc3) {
    mob->player.description = fread_string(mob_f);
    mob_index[nr].desc3 = mob->player.description;
  } else {
    skip_fread(mob_f);
    mob->player.description = mob_index[nr].desc3;
  }

  /* *** Numeric data *** */

  fgets(buf, sizeof (buf) - 1, mob_f);

  utmp1 = utmp5 = 0;
  tmp1 = sscanf(buf, " %u %u %u %d %c %u \n", &utmp1, &utmp2, &utmp3, &tmp4, &letter, &utmp5);
  if (tmp1 == 5 || tmp == 6) {
    /* convert the npcact's to cbits -- Alth */
    for (i = 0; i < 32; i++) {
      if (IS_SET(utmp1, 1U << i))
        SET_CBIT(mob->only.npc->npcact, i + 1);
    }

    for (i = 0; i < 32; i++) {
      if (IS_SET(utmp5, 1U << i))
        SET_CBIT(mob->only.npc->npcact, i + 1);
    }

    /* fun fun, until mob converter can switch all the mob files to new format,
       we have to convert old bitflags to new cbit flags.
       Not too tough, but cpu intensive. JAB */

    for (i = 0; i < 32; i++) {
      if (IS_SET(utmp2, 1U << i))
        SET_CBIT(mob->specials.affects, i + 1);
    }

    for (i = 0; i < 32; i++) {
      if (IS_SET(utmp3, 1U << i))
        SET_CBIT(mob->specials.affects, i + 33);
    }

    mob->specials.alignment = tmp4;
  } else {
    logit(LOG_DEBUG, "Mob %d has messed up format.", mob_index[nr].virtual);
    RemoveFromCharList(mob);
    free_char(mob);
    return NULL;
  }

  if (letter == 'S') {
    fgets(Gbuf1, MAX_STRING_LENGTH, mob_f);
    Gbuf2[0] = utmp1 = utmp2 = 0;
    sscanf(Gbuf1, "%s %u %u %s\n", buf, &utmp1, &utmp2, Gbuf2);

    mob->points.base_height = utmp1;
    mob->points.base_weight = utmp2;

    /* defaults to RACE_NONE */
    mob->player.race = RACE_NONE;
    for (i = 0; (i < DEFINED_RACES) && !mob->player.race; i++) {
      if (!str_cmp(race_lookup_table[i].code, buf))
        mob->player.race = race_lookup_table[i].race;
    }

    /* Awright, read the aggro mask..  -- Diirinka */
    if (*Gbuf2) {
      tptr = strtok(Gbuf2, ".");
      do {
        for (i = 0; i < DEFINED_RACES; i++) {
          if (!str_cmp(race_lookup_table[i].code, tptr)) {
            SET_CBIT(mob->only.npc->aggressive, (int) race_lookup_table[i].race);
            break;
          }
        }
      } while ((tptr = strtok(NULL, ".")));
    }

    fscanf(mob_f, " %u ", &utmp1);
    if (utmp1 > MAXLVL) /* Bzzzzzt */
      utmp1 = MAXLVL;

    GET_LEVEL(mob) = utmp1;

    /*
     * The following initialises the # of spells useable for NPCs in a given
     * spell circle based on the spl_table[level][spell_circle] in memorize.c.
     * Element 0 of this tracking array serves as an accumulator used in
     * repleneshing used slots. - SKB 31 Mar 1995
     */
    mob->only.npc->spells_in_circle[0] = 0;

    for (j = 1; j <= MAX_CIRCLE; j++)
      mob->only.npc->spells_in_circle[j] = spl_table[GET_LEVEL(mob)][j - 1];

    fscanf(mob_f, " %d ", &tmp);
    mob->points.base_hitroll = tmp;

    mob->points.hitroll = mob->points.base_hitroll;

    fscanf(mob_f, " %d ", &tmp);
    mob->points.base_armor = BOUNDED(-100, tmp, 100);

    tmp = 0;
    tmp2 = 0;
    tmp3 = 0;
    fscanf(mob_f, " %dd%d+%d ", &tmp1, &tmp2, &tmp3);

    if ((tmp1 <= 0) || (tmp2 <= 0))
      mob->points.base_hit = tmp3;
    else
      mob->points.base_hit = dice(tmp1, tmp2) + tmp3;

    mob->points.hit = mob->points.max_hit = mob->points.base_hit;
    if (mob->points.hit <= 0) {
      logit(LOG_MOB, "Warning: MOB #%d has negative (%d) hp.\n", mob_index[nr].virtual, mob->points.hit);
      dump_stack("negative or 0 hits");
    }

    fscanf(mob_f, " %dd%d+%d \n", &tmp, &tmp2, &tmp3);
    mob->points.base_damroll = mob->points.damroll = tmp3;
    mob->points.damnodice = tmp;
    mob->points.damsizedice = tmp2;

    fgets(buf, sizeof (buf) - 1, mob_f);
    if (sscanf(buf, " %u.%u.%u.%u %u", &utmp1, &utmp2, &utmp3, &utmp4, &utmp5) == 5) {
      GET_COPPER(mob) = utmp1;
      GET_SILVER(mob) = utmp2;
      GET_GOLD(mob) = utmp3;
      GET_PLATINUM(mob) = utmp4;
      GET_EXP(mob) = utmp5;
    } else {
      tmp1 = 0;
      tmp = 0;
      utmp3 = 0;
      if (sscanf(buf, " %u %u", &utmp1, &utmp2) == 2) {
        ADD_MONEY(mob, (signed) utmp1);
        GET_EXP(mob) = utmp2;
      } else {
        logit(LOG_DEBUG, "Bogus cash and/or exp for mob %d", mob_index[nr].virtual);
        dump_core();
      }
    }
  }

  tmp5 = 0;
  tmp6 = 0;
  fscanf(mob_f, " %d %d %d %d %d %d\n", &tmp, &tmp2, &tmp3, &tmp4, &tmp5, &tmp6);
  mob->specials.position = tmp;
  mob->only.npc->default_pos = tmp2;
  mob->player.sex = tmp3;
  if (tmp4) {
    if (((int) tmp4) > 0 && ((int) tmp4 <= LAST_CLASS))
      mob->player.class = tmp4;
  }

  GET_PRESTIGE_BONUS(mob) = tmp6;

  /* Default Magic resistances for mobs */
  for (res = 0; racial_mods[res][0] != 0; res++) {
    if (GET_RACE(mob) == racial_mods[res][0])
      r = racial_mods[res][1];
  }

  res = GET_LEVEL(mob) * r / 59;

  /* If there's a value set in the mob file, overwrite the default. */
  if (tmp5 > 0 && tmp5 <= 100) {
    mob->points.base_magic_resistance = tmp5;
    GET_MAGIC_RESISTANCE(mob) = tmp5;
  } else if (mob_index[mob->nr].virtual == 19700) { // Customize Tia's MR
    mob->points.base_magic_resistance = 20;
    GET_MAGIC_RESISTANCE(mob) = 20;
  } else {
    mob->points.base_magic_resistance = res;
    GET_MAGIC_RESISTANCE(mob) = res;
  }

  /* 100% MR only available for 54+ mobs */
  if (GET_LEVEL(mob) < 54 && tmp5 > 99) {
    GET_MAGIC_RESISTANCE(mob) = res;
    mob->points.base_magic_resistance = res;
  }

  if (letter == 'S') {
    mob->player.time.birth = time(0);
    mob->player.time.played = 0;
    mob->player.time.logon = time(0);

    for (i = 0; i < 3; i++)
      GET_COND(mob, i) = -1;

    for (i = 0; i < 5; i++)
      mob->specials.apply_saving_throw[i] = 0;

    if ((GET_LEVEL(mob) > 50) ||
            (GET_RACE(mob) == RACE_DRAGON) ||
            (GET_RACE(mob) == RACE_DRAGONKIN) ||
            (GET_RACE(mob) == RACE_DEMON) ||
            (GET_RACE(mob) == RACE_DEVIL) ||
            (GET_RACE(mob) == RACE_ANGEL) ||
            (GET_RACE(mob) == RACE_KIRIN) ||
            (GET_RACE(mob) == RACE_VAMPIRE) ||
            (GET_RACE(mob) == RACE_HIGH_UNDEAD))
      roll_basic_abilities(mob, 3);
    else if (GET_LEVEL(mob) > 35)
      roll_basic_abilities(mob, 2);
    else if (GET_LEVEL(mob) > 10)
      roll_basic_abilities(mob, 1);
    else
      roll_basic_abilities(mob, 0);

    if (IS_UNDEAD(mob) || IS_ANIMAL(mob))
      SET_CBIT(mob->specials.affects, AFF_INFRAVISION);

    mob->base_stats.Str = BOUNDED(1, mob->base_stats.Str, 100);
    mob->base_stats.Dex = BOUNDED(1, mob->base_stats.Dex, 100);
    mob->base_stats.Agi = BOUNDED(1, mob->base_stats.Dex, 100);
    mob->base_stats.Con = BOUNDED(1, mob->base_stats.Con, 100);
    mob->base_stats.Pow = BOUNDED(1, mob->base_stats.Con, 100);
    mob->base_stats.Int = BOUNDED(1, mob->base_stats.Int, 100);
    mob->base_stats.Wis = BOUNDED(1, mob->base_stats.Wis, 100);
    mob->base_stats.Cha = BOUNDED(1, mob->base_stats.Wis, 100);

    mob->points.move = mob->points.base_move = mob->points.max_move =
            MAX(50, mob->base_stats.Agi) + (mob->base_stats.Str + mob->base_stats.Con) / ((IS_ANIMAL(mob)) ? 1 : 2);
  }

  /* Result of these adjustments: mob damage = max average of 120 for
     nondemon/dragons, 200 for dragons/demons. */
  mob->curr_stats = mob->base_stats;

  /* mobs now load with max cash for their level, so we vary it a bit */
  if (GET_PLATINUM(mob) > 6)
    GET_PLATINUM(mob) = dice(3, (GET_PLATINUM(mob) / 3));
  else
    GET_PLATINUM(mob) = number(0, GET_PLATINUM(mob));

  if (GET_GOLD(mob) > 6)
    GET_GOLD(mob) = dice(3, (GET_GOLD(mob) / 3));
  else
    GET_GOLD(mob) = number(0, GET_GOLD(mob));

  if (GET_SILVER(mob) > 6)
    GET_SILVER(mob) = dice(3, (GET_SILVER(mob) / 3));
  else
    GET_SILVER(mob) = number(0, GET_SILVER(mob));

  if (GET_COPPER(mob) > 6)
    GET_COPPER(mob) = dice(3, (GET_COPPER(mob) / 3));
  else
    GET_COPPER(mob) = number(0, GET_COPPER(mob));

  /* if either height or weight is zero, set both, based on race */
  if (!GET_HEIGHT(mob) || !GET_WEIGHT(mob))
    set_char_size(mob);

  /* add memory to just about all mobs */
  if (!IS_CSET(mob->only.npc->npcact, ACT_MEMORY) && !IS_ANIMAL(mob))
    SET_CBIT(mob->only.npc->npcact, ACT_MEMORY);

  /* nuke the experience of NPCs with NO powers */
  if ((GET_RACE(mob) != RACE_DRAGON) && GET_CLASS(mob) == 0 &&
          !IS_CSET(mob->only.npc->npcact, ACT_HAS_CL) &&
          !IS_CSET(mob->only.npc->npcact, ACT_HAS_MU) &&
          !IS_CSET(mob->only.npc->npcact, ACT_HAS_TH) &&
          !IS_CSET(mob->only.npc->npcact, ACT_HAS_WA) &&
          !IS_CSET(mob->only.npc->npcact, ACT_HAS_PS)) {
    if (GET_LEVEL(mob) > 20)
      GET_EXP(mob) /= 5;
    else
      GET_EXP(mob) /= 2;
  }

  /* Create memory module if needed */
  if (IS_CSET(mob->only.npc->npcact, ACT_MEMORY)) {
    if (!(mob->only.npc->memory = mem_create(20))) {
      mob->only.npc->memory = NULL;
      REMOVE_CBIT(mob->only.npc->npcact, ACT_MEMORY);
    }
  } else
    mob->only.npc->memory = NULL;

  /* initial con adjustment, so they don't load hurt */
  update_con_bonus(mob);
  mob->points.hit += (IS_WARRIOR(mob) ?
          con_app[STAT_INDEX(GET_C_CON(mob))].hitp :
          MIN(2, con_app[STAT_INDEX(GET_C_CON(mob))].hitp)) * GET_LEVEL(mob);

  /* these have needed this for a long time */
  /* Add NULL check for mob name */
  if (GET_NAME(mob) && !strcmp(GET_NAME(mob), "elite"))
    SET_CBIT(mob->specials.affects, AFF_DETECT_INVISIBLE);

  /* to prevent people from crying "he stole my weapon judge!
     that's why I had to kill him! */
  if (IS_CSET(mob->only.npc->npcact, ACT_WITNESS)) {
    if (IS_CSET(mob->only.npc->npcact, ACT_SCAVENGER))
      REMOVE_CBIT(mob->only.npc->npcact, ACT_SCAVENGER);
  }

  /* fudge to give mobs with HAS_PS appropriate mana. */
  if (IS_PSIONICIST(mob)) {
    mana = (number(85, 100) + (GET_LEVEL(mob) * number(12, 15)));
    mob->points.base_mana = mana;
    mob->points.mana = mob->points.max_mana = mob->points.base_mana;
  }

  /* make it load with full hp     -- Altherog */
  GET_HIT(mob) = GET_MAX_HIT(mob);

  // Init last_direction to -1, this will kill the problem with mbos not moving north --CRM
  mob->only.npc->last_direction = -1;

  /* hook for demons and devils. Yes this is ugly.  Got a better way? :P */
  if (mob_index[nr].func && (GET_RACE(mob) == RACE_DEMON)) {
    fn = mob_index[nr].func;
    if (fn->type && (fn->type == FUNC_MOB)) {
      for (; fn; fn = fn->next) {
        if (!str_cmp(fn->name, "standardDemon")) {
          hasProc = TRUE;
          continue;
        }
      }
    }

    if (!hasProc)
      AddProcMob(mob_index[nr].virtual, standardDemon, "standardDemon");
  } else if (GET_RACE(mob) == RACE_DEMON)
    AddProcMob(mob_index[nr].virtual, standardDemon, "standardDemon");

  if (mob_index[nr].func && (GET_RACE(mob) == RACE_DEVIL)) {
    fn = mob_index[nr].func;
    if (fn->type && (fn->type == FUNC_MOB)) {
      for (; fn; fn = fn->next) {
        if (!str_cmp(fn->name, "standardDevil")) {
          hasProc = TRUE;
          continue;
        }
      }
    }

    if (!hasProc)
      AddProcMob(mob_index[nr].virtual, standardDevil, "standardDevil");
  } else if (GET_RACE(mob) == RACE_DEVIL)
    AddProcMob(mob_index[nr].virtual, standardDevil, "standardDevil");

  /* New stuff for umberhulks */
  /* hook for umberhulks...Yes this is ugly.  Got a better way? :P */
  if (mob_index[nr].func && (GET_RACE(mob) == RACE_UMBERHULK)) {
    fn = mob_index[nr].func;
    if (fn->type && (fn->type == FUNC_MOB)) {
      for (; fn; fn = fn->next) {
        if (!str_cmp(fn->name, "standardUmberhulk")) {
          hasProc = TRUE;
          continue;
        }
      }
    }

    if (!hasProc)
      AddProcMob(mob_index[nr].virtual, standardUmberhulk, "standardUmberhulk");
  } else if (GET_RACE(mob) == RACE_UMBERHULK)
    AddProcMob(mob_index[nr].virtual, standardUmberhulk, "standardUmberhulk");

  /* to cut LOG_SYS spam, check, correct and log this here. JAB */
  if (IS_CSET(mob->only.npc->npcact, ACT_SPEC) && !mob_index[nr].func) {
    REMOVE_CBIT(mob->only.npc->npcact, ACT_SPEC);
    if (mob_index[nr].number == 1) /* only first, not every */
      logit(LOG_MOB, "ACT_SPEC, but no function: %d %s", mob_index[nr].virtual, GET_NAME(mob));
  }

  // Haha, FEAR the UberKludge (tm) --CRM
  AddEvent(EVENT_CHAR_EXECUTE, 2, TRUE, mob, initial_hit_adjust);

  AddEvent(EVENT_MOB_MUNDANE, number(2, PULSE_MOBILE + 4), TRUE, mob, 0);

  /* init a periodic event for each mob */
  fn = mob_index[nr].func;
  while (fn) {
    fn->proc_flag = (*fn->func.ch) (mob, 0, PROC_INITIALIZE, 0);
    mob_index[nr].spec_flag |= fn->proc_flag;
    fn = fn->next;
  }

  if (IS_SET(mob_index[nr].spec_flag, IDX_PERIODIC))
    AddEvent(EVENT_MOB_SPECIAL, number(2, PULSE_MOBILE + 4), TRUE, mob, 0);

  if (IS_SET(mob_index[nr].spec_flag, IDX_NPC_PATH))
    AddEvent(EVENT_PATH, PULSE_MOBILE, TRUE, mob, 0);

  if (IS_CSET(mob->only.npc->npcact, ACT_SCAVENGER)) {
    fn = mob_index[nr].func;
    while (fn) {
      if (fn->func.ch == shop_keeper)
        break;
      fn = fn->next;
    }

    if (fn)
      REMOVE_CBIT(mob->only.npc->npcact, ACT_SCAVENGER);
  }


#if 0
  /* guildmasters and shopkeepers messing up
     If the are sentinels and do NOT have a spec proc, give them HUNTER
     Idea is, avoid getting major sentinels like tiamat to possibility
     follow someone and leave treasure unprotected, but to get most
     generic sentinels to track briefly -- this operates on the assumption
     that most major treasure protectors like tiamat have spec procs. */

  if (IS_CSET(mob->only.npc->npcact, ACT_SENTINEL) && IS_CSET(mob->only.npc->npcact, ACT_MEMORY) &&
          !mob_index[nr].func && !IS_CSET(mob->only.npc->npcact, ACT_HUNTER))
    SET_CBIT(mob->only.npc->npcact, ACT_HUNTER);
#endif

  affect_total(mob);

  //logit(TEST_MOB_LOOKUP, "#%d\n%s~\n%s~", nr, GET_NAME(mob), mob->player.short_descr);
#if 0
  verify_mobile(mob);
#endif
  return (mob);
}

/* Moradin's do_reboot, finished by Azuth */

/* after thinking about this, this function may be quite useless */
int file_to_string_alloc(const char *name, char **buf) {
  char *temp;

  debuglog(51, DS_AZUTH, "file_to_string_alloc: name [%s]", name);

  temp = file_to_string(name);
  if (!temp || !*temp) {
    debuglog(51, DS_AZUTH, "file_to_string_alloc: failed to load");
    return -1;
  }

  if (*buf) {
    debuglog(51, DS_AZUTH, "file_to_string_alloc: freeing orig");
    free_string(*buf);
    *buf = NULL;
  }

  *buf = str_dup(temp); /* temp is local, so must re-allocate it */
  free(temp); /* and don't forget to free temp itself */

  return 0;
}

void do_text_reload(P_char ch, char *argument, int cmd) {
  int i;
  char arg[MAX_STRING_LENGTH], debug[MAX_STRING_LENGTH];
  char Gbuf1[MAX_STRING_LENGTH];

  struct reboot_data reboot_info[] ={
    {"changelog", CHANGELOG_FILE, &changelog},
    {"genlog", LOG_GENNAME, &genlog},
    {"wiznews", WIZNEWS_FILE, &wiznews},
    {"news", NEWS_FILE, &news},
    {"faq", FAQ_FILE, &faq},
    {"credits", CREDITS_FILE, &credits},
    {"motd", MOTD_FILE, &motd},
    {"wizmotd", WIZMOTD_FILE, &wizmotd},
    {"pagehelp", HELP_PAGE_FILE, &help},
    {"pagehelpa", HELPA_PAGE_FILE, &helpa},
    {"pageinfo", INFO_PAGE_FILE, &info},
    {"pageinfoa", INFOA_PAGE_FILE, &infoa},
    {"wizlist", WIZLIST_FILE, &wizlist},
    {"rules", RULES_FILE, &rules},
    {"greetinga", GREETINGA_FILE, &greetinga},
    {"wizlista", WIZLISTA_FILE, &wizlista},
    {"generaltable", GENERALTABLE_FILE, &generaltable},
    {"racetable", RACETABLE_FILE, &racetable},
    {"classtable", CLASSTABLE_FILE, &classtable},
    {"racewars", RACEWARS_FILE, &racewars},
    {"namechart", NAMECHART_FILE, &namechart},
    {"reroll", REROLL_FILE, &reroll},
    {"bonus", BONUS_FILE, &bonus},
    {"keepchar", KEEPCHAR_FILE, &keepchar},
    {"hometowntable", HOMETOWN_FILE, &hometown_table},
    {"alignmenttable", ALIGNMENT_FILE, &alignment_table},
    {"olc_menu", OLC_MENU_FILE, &olc_menu},
    {"olc_new_user", OLC_NEW_USER_FILE, &olc_new_user},
    {"olc_db_menu", OLC_DB_MENU_FILE, &olc_db_menu},
    {"olc_db_new_user", OLC_DB_NEW_USER_FILE, &olc_db_new_user},
    {"olc_room_exit_menu", OLC_ROOM_EXIT_MENU, &olc_room_exit_menu},
    {"olc_room_expert_long", OLC_ROOM_EXPERT_LONG, &olc_room_expert_long},
    {"olc_room_expert_short", OLC_ROOM_EXPERT_SHORT, &olc_room_expert_short},
    {"kwdhelp", NULL_FILE, NULL}, /* this is item #33 if it changes, change the switch below */
    {"kwdinfo", NULL_FILE, NULL},
    {"kwdwizhelp", NULL_FILE, NULL}, /* this is item #35 if it changes, change the switch below */
    {"\n", NULL_FILE, NULL}
  };

  one_argument(argument, arg);

  debuglog(51, DS_AZUTH, "reload file: arg [%s]", arg);

  if (!*arg) { /* no argument */
    send_to_char("Reload what?\n", ch);
    return;
  }

  if (is_abbrev(arg, "list") || *arg == '?') { /* arg = list or ? */
    send_to_char("The following reload options are available:\n", ch);
    for (i = 0; *RB_OP(reboot_info[i]) != TERM_ARRAY_CHAR; i++) {
      sprintf(Gbuf1, "%s\n", RB_OP(reboot_info[i]));
      send_to_char(Gbuf1, ch);
    }

    return;
  }

  for (i = 0; *RB_OP(reboot_info[i]) != TERM_ARRAY_CHAR; i++) {
    if (!is_abbrev(arg, RB_OP(reboot_info[i])))
      continue;

    if (!str_cmp(RB_FILE(reboot_info[i]), NULL_FILE))
      break;

    sprintf(debug, "Reloading_file: %s\n", RB_FILE(reboot_info[i]));
    send_to_char(debug, ch);
    if (REREAD_FILE(reboot_info[i]) < 0)
      send_to_char("Error! That file could not be reloaded!\n", ch);

    OK(ch);
    return;
  }

  /* if we get here it's either a help file or not in list */
  if (str_cmp(RB_FILE(reboot_info[i]), NULL_FILE)) { /* wasn't the help files, must be not in list */
    send_to_char("Unknown reload option.\n", ch);
    send_to_char("Correct format: reload list | all | ? | * | <option>", ch);
    return;
  }

  /* must be help or info file */
  switch (i) {
    case 33: /* This is for the keyword help file */
      send_to_char("Loading the help file again.\n", ch);

      if (help_fl)
        fclose(help_fl);

      if (!(help_fl = fopen(HELP_KWRD_FILE, "r")))
        return;
      else {
        for (i = 0; i < top_of_helpt; i++)
          free(help_index[i].keyword);
        free(help_index);
        help_index = build_help_index(help_fl, &top_of_helpt);
      }

      OK(ch);
      break;

    case 34: /* This is for the keyword info file */
      send_to_char("Loading the info file again\n", ch);

      if (info_fl)
        fclose(info_fl);

      if (!(info_fl = fopen(INFO_KWRD_FILE, "r")))
        return;
      else {
        for (i = 0; i < top_of_infot; i++)
          free(info_index[i].keyword);

        free(info_index);
        info_index = build_info_index(info_fl, &top_of_infot);
      }

      OK(ch);
      break;

    case 35: /* This is for the keyword wizhelp file */
      send_to_char("Loading the wizhelp file again.\n", ch);

      if (whelp_fl)
        fclose(whelp_fl);

      if (!(whelp_fl = fopen(WHELP_KWRD_FILE, "r")))
        return;
      else {
        for (i = 0; i < top_of_whelpt; i++)
          free(whelp_index[i].keyword);

        free(whelp_index);
        whelp_index = build_whelp_index(whelp_fl, &top_of_whelpt);
      }

      OK(ch);
      break;

    default:
      send_to_char("ERROR in do_reload_text, add new switch case", ch);
      break;
  }
}

/* read an object from OBJ_FILE */

P_obj read_object(int nr, int type) {
  P_obj obj;
  char Gbuf1[MAX_STRING_LENGTH];
  char chk[MAX_STRING_LENGTH];
  char tmp_valuebuf[256]; /* used for reading and "parsing" the expanded value fields ie: missile code */
  int tmp, tmp2, tmp3, i, j;
  int tmp4 = 0;
  uint utmp;
  struct extra_descr_data *new_descr;
  struct func_attachment *fn;

  i = nr;
  if (type == VIRTUAL) {
    if ((nr = real_object(nr)) < 0) {
      logit(LOG_DEBUG, "read_object: Obj %d not in database", i);
      dump_stack("Missing Object");
      return (0);
    }
  }

  if (nr < 0) {
    logit(LOG_DEBUG, "read_object: negative rnum (%d) args %d, %s", nr, i, type ? "VIRTUAL" : "REAL");
    dump_stack("Object Negative Rnum");
    return 0;
  }

  fseek(obj_f, obj_index[nr].pos, 0);

  obj = GetNewObj(NEW_OBJ);

  obj->R_num = nr;
  obj_index[nr].number++;

  /* *** string data *** */

  /* added pointers to the index struct, so that all objs of the same type will
     now share all text.  This should save us a huge amount of RAM. -JAB */

  if (!obj_index[nr].keys) {
    obj->name = fread_string(obj_f);
    for (j = 0; *(obj->name + j); j++) /* make sure all keywords are lowercased */
      *(obj->name + j) = LOWER(*(obj->name + j));

    obj_index[nr].keys = obj->name;
  } else {
    skip_fread(obj_f);
    obj->name = obj_index[nr].keys;
  }

  if (!obj_index[nr].desc2) {
    obj->short_description = fread_string(obj_f);
    if (!obj->short_description) /* prevent null string crashes on this -Azuth */
      obj->short_description = str_dup("No description");

    obj_index[nr].desc2 = obj->short_description;
  } else {
    skip_fread(obj_f);
    obj->short_description = obj_index[nr].desc2;
  }

  if (!obj_index[nr].desc1) {
    obj->description = fread_string(obj_f);
    obj_index[nr].desc1 = obj->description;
  } else {
    skip_fread(obj_f);
    obj->description = obj_index[nr].desc1;
  }

  if (!obj_index[nr].desc3) {
    obj->action_description = fread_string(obj_f);
    obj_index[nr].desc3 = obj->action_description;
  } else {
    skip_fread(obj_f);
    obj->action_description = obj_index[nr].desc3;
  }

  /* *** numeric data *** */

  fgets(Gbuf1, MAX_STRING_LENGTH, obj_f);
  sscanf(Gbuf1, "%u %u %u %u\n", &tmp, &tmp2, &tmp3, &tmp4);

  obj->type = tmp;

  /* Added hooks to automagically assign ITEM_SWITCH and ITEM_TELEPORT procs */
  /* Note that this CAN be extended to other command activated-type items or
     other objects that use special procs */
  /* Skip it if object functions already assigned -- allows for special
     override situations... */
  if (!obj_index[nr].func) {
    switch (obj->type) {
      case ITEM_TELEPORT:
        AddProcObj(obj_index[nr].virtual, item_teleport, "item_teleport");
        break;
      case ITEM_SWITCH:
        AddProcObj(obj_index[nr].virtual, item_switch, "item_switch");
        break;
      case ITEM_SUMMON:
        AddProcObj(obj_index[nr].virtual, item_summon, "item_summon");
        break;
      case ITEM_DISTRIBUTION:
        AddProcObj(obj_index[nr].virtual, mob_distro_system, "mob_distro_system");
        break;
      default:
        break;
    }
  }

  obj->extra_flags = tmp2;

  // Auto light all Ships --CRM
  if (obj->type == ITEM_SHIP)
    SET_BIT(obj->extra_flags, ITEM_LIT);

  obj->wear_flags = tmp3;

  /* Read the anti flags, if the field exists.. - Shev 9/98*/
  obj->anti_flags = tmp4;

  /* handles reading of objects that have a variable length of value fields..
   * ie missile weapons so far..                     Altherog March 7 97   */

  fgets(tmp_valuebuf, 256, obj_f);
  sscanf(tmp_valuebuf, "%d %d %d %d %d %d %d %d",
          &obj->value[0],
          &obj->value[1],
          &obj->value[2],
          &obj->value[3],
          &obj->value[4],
          &obj->value[5],
          &obj->value[6],
          &obj->value[7]);

  /* keeping track of weight in 1/4 lb increments, I changed all the drink
     containers, but other objects are still in 1 lb chunks.  JAB */
  fscanf(obj_f, " %d ", &tmp);
  if (obj->type == ITEM_DRINKCON)
    tmp /= 4;

  obj->weight = tmp;
  fscanf(obj_f, " %d \n", &tmp);
  obj->cost = tmp;

  fscanf(obj_f, " %d \n", &tmp); /* cost_per_day, no longer used.  JAB */
  /* too bad I found a use for it.  DMB */
  obj->durability = tmp;

  /* read in affect flag -DCL */
  CLEAR_CBITS(obj->sets_affs, AFF_BYTES);
  if (fscanf(obj_f, " %u \n", &utmp) == 1) {
    for (i = 0; i < 32; i++) {
      if (IS_SET(utmp, 1U << i))
        SET_CBIT(obj->sets_affs, i + 1);
    }

    if (fscanf(obj_f, " %u \n", &utmp) == 1) {
      for (i = 0; i < 32; i++) {
        if (IS_SET(utmp, 1U << i))
          SET_CBIT(obj->sets_affs, i + 33);
      }
    }
  }

  // No hide items. :P  --CRM
  if (IS_CSET(obj->sets_affs, AFF_HIDE))
    REMOVE_CBIT(obj->sets_affs, AFF_HIDE);

  /* *** extra descriptions *** */

  while (fscanf(obj_f, " %s \n", chk), *chk == 'E') {
#ifdef MEM_DEBUG
    mem_use[MEM_E_DSCR] += sizeof (struct extra_descr_data);
#endif
    CREATE(new_descr, struct extra_descr_data, 1);

    new_descr->keyword = fread_string(obj_f);
    new_descr->description = fread_string(obj_f);

    new_descr->next = obj->ex_description;
    obj->ex_description = new_descr;
  }

  for (i = 0; (i < MAX_OBJ_AFFECT) && (*chk == 'A'); i++) {
    fscanf(obj_f, " %d ", &tmp);
    obj->affected[i].location = tmp;
    fscanf(obj_f, " %d \n", &tmp);
    /* hack hack, until I convert objects JAB */
    if (((obj->affected[i].location >= APPLY_STR) && (obj->affected[i].location <= APPLY_CON)) ||
            ((obj->affected[i].location >= APPLY_AGI) && (obj->affected[i].location <= APPLY_LUCK)))
      tmp = (tmp * 45) / 10;

    obj->affected[i].modifier = tmp;
    fscanf(obj_f, " %s \n", chk);
  }

  /* Trapped item data */
  obj->trap_eff = obj->trap_dam = obj->trap_charge = obj->trap_dnum = obj->trap_dsize = 0;
  if (*chk == 'T') {
    fscanf(obj_f, " %d ", &tmp);
    obj->trap_eff = tmp;
    fscanf(obj_f, " %d ", &tmp);
    obj->trap_dam = tmp;
    fscanf(obj_f, " %d ", &tmp);
    obj->trap_charge = tmp;
    fscanf(obj_f, " %d \n", &tmp);
    obj->trap_level = tmp;
    fscanf(obj_f, " %d \n", &tmp);
    obj->trap_dnum = tmp;
    fscanf(obj_f, " %d \n", &tmp);
    obj->trap_dsize = tmp;
  }

#ifdef ARTIFACT /* set the owner to NULL */
  if (art_index) {
    if (IS_SET(obj_index[obj->R_num].spec_flag, IDX_ARTIFACT))
      art_index[searchForArtifact(obj_index[obj->R_num].virtual)].ch = NULL;
  }
#endif

  if (obj_index[nr].func) {
    if (nr == real_object(2998)) { /* Clock tower */
      if (obj_index[nr].number == 1)
        AddEvent(EVENT_OBJ_SPECIAL, ((3 - (time_info.hour % 3)) * 240) - pulse + 1, FALSE, obj, 0);
    } else {
      fn = obj_index[nr].func;
      while (fn) {
        fn->proc_flag = (*fn->func.obj) (obj, 0, PROC_INITIALIZE, 0);
        obj_index[nr].spec_flag |= fn->proc_flag;
        fn = fn->next;
      }

      if (IS_SET(obj_index[nr].spec_flag, IDX_PERIODIC))
        AddEvent(EVENT_OBJ_SPECIAL, number(2, PULSE_MOBILE + 4), TRUE, obj, 0);
    }
  }

  return (obj);
}

#define ZCMD zone_table[zone].cmd[cmd_no]

/* execute the reset command table of a given zone */

/*
 ** Addition:
 **
 ** 1) If mobiles are dual, load in a transient long sword for
 **    his/her secondary weapon
 ** 2) No longer is there a restriction on the number of objects that
 **    can be loaded into a room.  That is, if max_exist=3 of A and
 **    there are 2 As in the room's content list, A will be loaded a
 **    third time.  Previously, it was that 1 was max_exist no matter. --TAM 7-12-94
 */
/* addition of two new zone commands which control group process      */
/* all old commands work as is, except follow, which no longer groups */
/* Command: ( note: grouping is not dependant on follow ) */

/* L <if-flag> <group_vnum>   * this assigns a group leader to last mobile
   Z <if-flag>                * this terminates a group */

void reset_zone(int zone) {
  P_char mob = NULL, leader, follower, tmpMob, nextMob, holdmob;
  P_event e1 = NULL;
  P_obj obj, obj_to, next_obj;
  int cmd_no, last_cmd = 1, tmp, followerFound, i, tmp_wkday;
  int last_mob_load = 0;
  int rare_pct = 0;
  int zone_empty = 0;
  bool grouping = FALSE; /* boolean to determine if grouping */
  P_group group = NULL;
  
  /* Debug for zone 148 crash */
  /*
  if (zone == 148) {
    fprintf(stderr, "DEBUG: reset_zone() - Starting reset for zone 148\n");
    fprintf(stderr, "DEBUG: reset_zone() - Zone 148 has %d commands\n", zone_table[zone].cmd_count);
    fprintf(stderr, "DEBUG: reset_zone() - top_of_objt = %d, top_of_world = %d\n", top_of_objt, top_of_world);
  }
  */

  zone_empty = is_empty(zone);
  if ((zone_table[zone].reset_mode == 1 || zone_table[zone].reset_mode == 3) && !zone_empty && current_event && (current_event->type == EVENT_RESET_ZONE)) {
    /* reschedule for 1 minute from now */
    current_event->timer++;
    debuglog(51, DS_ZONERESET, "Zone &+R%d&n %s&n reset held because not empty and is required.", zone, zone_table[zone].name);
    return;
  }
#ifdef AREAS_DEBUG
  fprintf(stderr, "<<START:  Boot Reset of Zone [%3d]>>\n", zone);
#endif

  debuglog(51, DS_ZONERESET, "Zone &+Y%d&n %s&n resetting.", zone, zone_table[zone].name);
  if (major_reset_zone(zone)) {
    if (purge_zone(zone)) {
      wizlog(51, "Major zone reset of zone &+G%d&n %s", zone, zone_table[zone].name);
      zone_table[zone].time_last_boot = time(0);
      zone_table[zone].zone_empty_cnt = 0;
    }
  }

  if (zone_empty)
    zone_table[zone].zone_empty_cnt++;

  for (cmd_no = 0; cmd_no < zone_table[zone].cmd_count; cmd_no++) {
    if (ZCMD.command == 'S') {
#ifdef AREAS_DEBUG
      fprintf(stderr, "<<DONE:  Boot Reset of Zone [%3d]>>\n", zone);
#endif
      break;
    }

#ifdef AREAS_DEBUG
    fprintf(stderr, "  <<[%5d] %c %d", cmd_no, ZCMD.command, ZCMD.if_flag);
    switch (ZCMD.command) {
      case 'D':
        fprintf(stderr, " (%4d) %5d %5d %5d %3d%%",
                ZCMD.arg1, world[ZCMD.arg1].number, ZCMD.arg2, ZCMD.arg3, ZCMD.arg4);
        break;
      case 'E':
        fprintf(stderr, " (%4d) %5d %5d %5d %3d%%",
                ZCMD.arg1, obj_index[ZCMD.arg1].virtual, ZCMD.arg2, ZCMD.arg3, ZCMD.arg4);
        break;
      case 'F':
        fprintf(stderr, " (%4d) %5d (%4d) %5d (%4d) %5d %3d%%",
                ZCMD.arg1, world[ZCMD.arg1].number,
                ZCMD.arg2, mob_index[ZCMD.arg2].virtual,
                ZCMD.arg3, mob_index[ZCMD.arg3].virtual, ZCMD.arg4);
        break;
      case 'G':
        fprintf(stderr, " (%4d) %5d %5d       %3d%%",
                ZCMD.arg1, obj_index[ZCMD.arg1].virtual, ZCMD.arg2, ZCMD.arg4);
        break;
      case 'M': /* mobile to room */
        fprintf(stderr, " (%4d) %5d %5d (%4d) %5d %3d%%",
                ZCMD.arg1, mob_index[ZCMD.arg1].virtual, ZCMD.arg2,
                ZCMD.arg3, world[ZCMD.arg3].number, ZCMD.arg4);
        break;
      case 'O':
        fprintf(stderr, " (%4d) %5d %5d (%4d) %5d %3d%%",
                ZCMD.arg1, obj_index[ZCMD.arg1].virtual, ZCMD.arg2,
                ZCMD.arg3, world[ZCMD.arg3].number, ZCMD.arg4);
        break;
      case 'P':
        fprintf(stderr, " (%4d) %5d %5d (%4d) %5d %3d%%",
                ZCMD.arg1, obj_index[ZCMD.arg1].virtual, ZCMD.arg2,
                ZCMD.arg3, obj_index[ZCMD.arg3].virtual, ZCMD.arg4);
        break;
      case 'R':
        fprintf(stderr, " (%4d) %5d (%4d) %5d",
                ZCMD.arg1, obj_index[ZCMD.arg1].virtual,
                ZCMD.arg2, world[ZCMD.arg2].number);
        break;
      case 'X':
        fprintf(stderr, " (%4d) %5d (%4d) %5d",
                ZCMD.arg1, obj_index[ZCMD.arg1].virtual,
                ZCMD.arg2, obj_index[ZCMD.arg2].virtual);
        break;
      case 'T':
        fprintf(stderr, " %d", ZCMD.arg1);
        break;
    }
#endif

    /* last_mob_loaded added to prevent falling out of an if-flag chain in the */
    /* event of a failed rareload, etc in E/G                     - CRM 9/98  */

    /* Since the follow command doesn't care about the other commands... DDW */
    //      if(!((ZCMD.command == 'F' || ZCMD.command == 'E' || ZCMD.command == 'G') || last_cmd || !ZCMD.if_flag))
    if (ZCMD.command != 'F' && ZCMD.command != 'E' && ZCMD.command != 'G') { // we only check if_flag on all other cmds, not on F, E, or G
      if (ZCMD.if_flag && !last_cmd) {
#ifdef AREAS_DEBUG
        fprintf(stderr, " * Skipped (if_flag failure)>>\n");
#endif
        last_cmd = 0;
        last_mob_load = 0;
        ZCMD.status = CMD_STAT_SKIP_IF;
        continue;
      }
    }

    if ((ZCMD.command == 'G' || ZCMD.command == 'E') && !last_mob_load) {
      last_cmd = 0;
      ZCMD.status = CMD_STAT_NO_MOB;
      continue;
    }

    /* This should be useless... */
#if 0
    /* Reset last_mob_load when it gets to the end of a chain.. */
    if (ZCMD.command != 'G' && ZCMD.command != 'E' && ZCMD.command != 'M')
      last_mob_load = 0;

#endif
    rare_pct = number(0, 99); // snag a rare load percent in one spot
    
    /* Debug for zone 148 crash */
    /*
    if (zone == 148) {
      fprintf(stderr, "DEBUG: reset_zone() - Zone 148, cmd %d: command='%c', arg1=%d, arg2=%d, arg3=%d, arg4=%d\n",
              cmd_no, ZCMD.command, ZCMD.arg1, ZCMD.arg2, ZCMD.arg3, ZCMD.arg4);
      if (ZCMD.command == 'M' && ZCMD.arg1 >= 0 && ZCMD.arg1 <= top_of_mobt) {
        fprintf(stderr, "DEBUG: reset_zone() - Mob virtual=%d, real=%d\n",
                mob_index[ZCMD.arg1].virtual, ZCMD.arg1);
      }
    }
    */
    
    switch (ZCMD.command) {
      case 'M': /* read a mobile */
        last_cmd = last_mob_load = 0; // assume fail to start with
        /* Add bounds checking for mob index */
        if (ZCMD.arg1 < 0 || ZCMD.arg1 > top_of_mobt) {
          logit(LOG_BOOT, "WARNING: Zone %d, cmd %d: Invalid mob index %d (max=%d)", 
                zone, cmd_no, ZCMD.arg1, top_of_mobt);
          ZCMD.status = CMD_STAT_ERROR;
          ZCMD.old_command = ZCMD.command;
          ZCMD.command = '!';
          break;
        }
        if (mob_index[ZCMD.arg1].number < ZCMD.arg2) {
          if (ZCMD.arg4 > rare_pct) {
            mob = read_mobile(ZCMD.arg1, REAL);
            if (!mob) {
              ZCMD.status = CMD_STAT_MOB_FAILED_LOAD;
              ZCMD.old_command = ZCMD.command;
              ZCMD.command = '!';
              logit(LOG_DEBUG, "reset_zone(): (zone %d) mob %d [%d] not loadable",
                      zone, ZCMD.arg1, mob_index[ZCMD.arg1].virtual);
            }
          } else {
            mob = 0;
            ZCMD.status = CMD_STAT_RARE_PCT_FAILED;
            logit(LOG_MOB, "M cmd not executed %d %d %d %d", ZCMD.arg1, ZCMD.arg2, ZCMD.arg3, ZCMD.arg4);
          }

          if (!mob)
            break;

          /* Add bounds checking for room index */
          if (ZCMD.arg3 < 0 || ZCMD.arg3 > top_of_world) {
            logit(LOG_BOOT, "WARNING: Zone %d, cmd %d: Invalid room index %d for mob %d (max=%d)", 
                  zone, cmd_no, ZCMD.arg3, ZCMD.arg1, top_of_world);
            extract_char(mob);
            ZCMD.status = CMD_STAT_ERROR;
            ZCMD.old_command = ZCMD.command;
            ZCMD.command = '!';
            break;
          }

          GET_HOME(mob) = world[ZCMD.arg3].number;
          GET_BIRTHPLACE(mob) = GET_HOME(mob);
          char_to_room(mob, ZCMD.arg3, -2);

          if (grouping)
            newGroupMember(group, mob);

          if (zone_table[world[mob->in_room].zone].hometown) {
            if (IS_NPC_GUARD(mob) && !IS_CSET(mob->only.npc->npcact, ACT_SENTINEL))
              justice_register_guard(mob);
          }

          ZCMD.status = CMD_STAT_SUCCESS;
          last_cmd = last_mob_load = 1;
        } else
          ZCMD.status = CMD_STAT_MAX_LIMIT_HIT;
        break;

      case 'O': /* load an object to room */
        last_cmd = 0; // assume fail to start with
        /* Add bounds checking to prevent segfault */
        if (ZCMD.arg1 < 0 || ZCMD.arg1 > top_of_objt) {
          logit(LOG_BOOT, "WARNING: Zone %d, cmd %d: Invalid object index %d (max=%d)", 
                zone, cmd_no, ZCMD.arg1, top_of_objt);
          ZCMD.status = CMD_STAT_ERROR;
          ZCMD.old_command = ZCMD.command;
          ZCMD.command = '!';
          break;
        }
        if (ZCMD.arg3 < 0 || ZCMD.arg3 >= top_of_world) {
          logit(LOG_BOOT, "WARNING: Zone %d, cmd %d: Invalid room index %d (max=%d)", 
                zone, cmd_no, ZCMD.arg3, top_of_world - 1);
          ZCMD.status = CMD_STAT_ERROR;
          ZCMD.old_command = ZCMD.command;
          ZCMD.command = '!';
          break;
        }
        if ((ZCMD.arg1 >= 0) && (ZCMD.arg1 <= top_of_objt) && 
            (ZCMD.arg3 >= 0) && (ZCMD.arg3 < top_of_world) && 
            (obj_index[ZCMD.arg1].number < ZCMD.arg2)) {
          obj = get_obj_in_list_num(ZCMD.arg1, world[ZCMD.arg3].contents);
          if (!obj || IS_SET(obj->wear_flags, ITEM_TAKE)) {
            if (ZCMD.arg4 > rare_pct) {
              obj = read_object(ZCMD.arg1, REAL);
              if (!obj) {
                ZCMD.status = CMD_STAT_OBJ_FAILED_LOAD;
                ZCMD.old_command = ZCMD.command;
                ZCMD.command = '!';
                logit(LOG_DEBUG, "reset_zone(): (zone %d) obj %d [%d] not loadable",
                        zone, ZCMD.arg1, obj_index[ZCMD.arg1].virtual);
              }

              if (obj) {
                obj_to_room(obj, ZCMD.arg3);
#ifdef ARTIFACT
                if (art_index)
                  if (IS_SET(obj_index[ZCMD.arg1].spec_flag, IDX_ARTIFACT))
                    if (artifactIsOwned(obj_index[ZCMD.arg1].virtual) ||
                            (obj_index[ZCMD.arg1].number > 1)) {
                      extract_obj(obj);
                      break;
                    }
#endif
                last_cmd = 1;
                ZCMD.status = CMD_STAT_SUCCESS;
                break;
              }
            }
          }
        } else if ((ZCMD.arg1 >= 0) && (ZCMD.arg1 <= top_of_objt) && (obj_index[ZCMD.arg1].number < ZCMD.arg2)) {
          logit(LOG_OBJ, "O cmd: obj: %d to_room: %d, chance: %d, limit %d(%d)",
                  obj_index[ZCMD.arg1].virtual, (ZCMD.arg3 >= 0) ? world[ZCMD.arg3].number : -2,
                  ZCMD.arg4, ZCMD.arg2, obj_index[ZCMD.arg1].number);
          ZCMD.status = CMD_STAT_ERROR;
          ZCMD.old_command = ZCMD.command;
          ZCMD.command = '!'; /* disable */
        } else
          ZCMD.status = CMD_STAT_MAX_LIMIT_HIT;
        break;

      case 'P': /* object to object */
        last_cmd = 0; // assume fail to start with
        /* Add bounds checking to prevent segfault */
        if ((ZCMD.arg1 >= 0) && (ZCMD.arg1 <= top_of_objt) && 
            (ZCMD.arg3 >= 0) && (ZCMD.arg3 <= top_of_objt) && 
            (obj_index[ZCMD.arg1].number < ZCMD.arg2)) {
          if (ZCMD.arg4 > rare_pct) {
            obj = read_object(ZCMD.arg1, REAL);
            if (!obj) {
              ZCMD.old_command = ZCMD.command;
              ZCMD.command = '!';
              logit(LOG_DEBUG, "reset_zone(): (zone %d) obj %d [%d] not loadable",
                      zone, ZCMD.arg1, obj_index[ZCMD.arg1].virtual);
            }

            if (obj) {
              obj_to = get_obj_num(ZCMD.arg3);
              if (obj_to) {
#ifdef ARTIFACT
                if (art_index)
                  if (IS_SET(obj_index[ZCMD.arg1].spec_flag, IDX_ARTIFACT))
                    if (artifactIsOwned(obj_index[ZCMD.arg1].virtual) ||
                            (obj_index[ZCMD.arg1].number > 1)) {
                      extract_obj(obj);
                      break;
                    }
#endif
                obj_to_obj(obj, obj_to);
                ZCMD.status = CMD_STAT_SUCCESS;
                last_cmd = 1;
                break;
              } else
                ZCMD.status = CMD_STAT_OBJ_TO_OBJ_FAIL;
            }
          }
        } else if ((ZCMD.arg1 >= 0) && (ZCMD.arg1 <= top_of_objt) && (obj_index[ZCMD.arg1].number < ZCMD.arg2)) {
          ZCMD.status = CMD_STAT_ERROR;
          logit(LOG_OBJ, "P cmd: obj: %d to_obj: %d, chance: %d, limit %d(%d)",
                  obj_index[ZCMD.arg1].virtual,
                  (ZCMD.arg3 >= 0) ? obj_index[ZCMD.arg3].virtual : -2, ZCMD.arg4,
                  ZCMD.arg2, obj_index[ZCMD.arg1].number);
        } else
          ZCMD.status = CMD_STAT_MAX_LIMIT_HIT;
        break;

      case 'G': /* obj_to_char */
        last_cmd = 0; // assume fail to start with
        /* Add bounds checking to prevent segfault */
        if ((ZCMD.arg1 >= 0) && (ZCMD.arg1 <= top_of_objt) && 
            (obj_index[ZCMD.arg1].number < ZCMD.arg2)) {
          if (ZCMD.arg4 > rare_pct) {
            obj = read_object(ZCMD.arg1, REAL);
            if (!obj) {
              ZCMD.old_command = ZCMD.command;
              ZCMD.command = '!';
              logit(LOG_DEBUG, "reset_zone(): (zone %d) obj %d [%d] not loadable",
                      zone, ZCMD.arg1, obj_index[ZCMD.arg1].virtual);
            }

            if (obj) {
              if (mob) {
#if 0  /* Removed until libs updates --MIAX */
                if (!IMMATERIAL(mob))
                  obj_to_char(obj, mob);
                else {
                  extract_obj(obj);
                  break;
                }
#else
                obj_to_char(obj, mob);
#endif
#ifdef ARTIFACT
                if (art_index)
                  if (IS_SET(obj_index[ZCMD.arg1].spec_flag, IDX_ARTIFACT))
                    if (artifactIsOwned(obj_index[ZCMD.arg1].virtual) ||
                            (obj_index[ZCMD.arg1].number > 1)) {
                      extract_obj(obj);
                      break;
                    }
#endif
                ZCMD.status = CMD_STAT_SUCCESS;
                last_cmd = 1;
                break;
              } else {
                logit(LOG_MOB, "ERROR: (Z: %d) \"G %d %5d %3d %2d\" (no char)", zone,
                        ZCMD.if_flag, obj_index[ZCMD.arg1].virtual, ZCMD.arg2, ZCMD.arg3);
                break;
              }
            }
          }
        } else if ((ZCMD.arg1 >= 0) && (ZCMD.arg1 <= top_of_objt) && (obj_index[ZCMD.arg1].number < ZCMD.arg2)) {
          ZCMD.status = CMD_STAT_ERROR;
          logit(LOG_OBJ, "G cmd: obj: %d to_char: %d, chance: %d, limit %d(%d)",
                  obj_index[ZCMD.arg1].virtual, (ZCMD.arg3 >= 0) ? mob_index[ZCMD.arg3].virtual : -2,
                  ZCMD.arg4, ZCMD.arg2, obj_index[ZCMD.arg1].number);
        } else
          ZCMD.status = CMD_STAT_MAX_LIMIT_HIT;
        break;

      case 'E': /* object to equipment list */
        last_cmd = 0; // assume fail to start with
        /* Add bounds checking to prevent segfault */
        if ((ZCMD.arg1 >= 0) && (ZCMD.arg1 <= top_of_objt) && 
            (obj_index[ZCMD.arg1].number < ZCMD.arg2)) {
          if (ZCMD.arg4 > rare_pct) {
            obj = read_object(ZCMD.arg1, REAL);
            if (!obj) {
              ZCMD.old_command = ZCMD.command;
              ZCMD.command = '!';
              logit(LOG_DEBUG, "reset_zone(): (zone %d) obj %d [%d] not loadable",
                      zone, ZCMD.arg1, obj_index[ZCMD.arg1].virtual);
            }

            if (obj) {
              last_cmd = 1;
              if (mob && (ZCMD.arg3 > 0) && (ZCMD.arg3 <= CUR_MAX_WEAR)) {
#ifdef ARTIFACT
                if (art_index)
                  if (IS_SET(obj_index[ZCMD.arg1].spec_flag, IDX_ARTIFACT))
                    if (artifactIsOwned(obj_index[ZCMD.arg1].virtual) ||
                            (obj_index[ZCMD.arg1].number > 1)) {
                      extract_obj(obj);
                      last_cmd = 0;
                      break;
                    }
#endif
#if 0  /* Removed until libs updates --MIAX */
                if (!IMMATERIAL(mob))
                  obj_to_char(obj, mob);
                else {
                  last_cmd = 0;
                  extract_obj(obj);
                  break;
                }
#else
                obj_to_char(obj, mob);
#endif

                tmp = wear(mob, obj, restore_wear[ZCMD.arg3], 0);

                /* failed to put it in the specified slot, but errors happen, try all slots. JAB */
                if (!tmp)
                  tmp = try_wear(mob, obj);

                if (!tmp && (restore_wear[ZCMD.arg3] == 12)) {
                  /* it's a weapon slot thing, let's see if we need str */
                  int max_wield_weight;

                  if (GET_OBJ_WEIGHT(obj) > (CAN_WIELD_W(mob) / ((mob->equipment[WIELD]) ? 5 : 1))) {
                    max_wield_weight = stat_factor[(int) GET_RACE(mob)].Str;
                    max_wield_weight *= MIN(max_wield_weight, (int) GET_WEIGHT(mob));
                    max_wield_weight = max_wield_weight / 338 / ((mob->equipment[WIELD]) ? 5 : 1);

                    if (GET_OBJ_WEIGHT(obj) <= max_wield_weight) {
                      max_wield_weight = GET_OBJ_WEIGHT(obj) * ((mob->equipment[WIELD]) ? 5 : 1);
                      while ((mob->base_stats.Str < 100) && (CAN_WIELD_W(mob) < max_wield_weight)) {
                        mob->base_stats.Str++;
                        GET_C_STR(mob) = mob->base_stats.Str * stat_factor[(int) GET_RACE(mob)].Str / 100;
                      }

                      wear(mob, obj, restore_wear[ZCMD.arg3], 0);
                    }
                  }
                }

                ZCMD.status = CMD_STAT_SUCCESS;
                break;
              } else {
                logit(LOG_OBJ, "E cmd: obj: %d pos: %d(%s) chance: %d, limit %d(%d)",
                        obj_index[ZCMD.arg1].virtual, ZCMD.arg3,
                        ((ZCMD.arg3 > 0) && (ZCMD.arg3 <= CUR_MAX_WEAR)) ? equipment_types[ZCMD.arg3] : "ERR",
                        ZCMD.arg4, ZCMD.arg2, obj_index[ZCMD.arg1].number);
                ZCMD.status = CMD_STAT_EQUIP_MOB_FAIL;
                break;
              }
            }
          }
        } else if ((ZCMD.arg1 >= 0) && (ZCMD.arg1 <= top_of_objt) && (obj_index[ZCMD.arg1].number < ZCMD.arg2)) {
          logit(LOG_OBJ, "E cmd: obj: %d pos: %d(%s) chance: %d, limit %d(%d)",
                  obj_index[ZCMD.arg1].virtual, ZCMD.arg3,
                  ((ZCMD.arg3 > 0) && (ZCMD.arg3 <= CUR_MAX_WEAR)) ? equipment_types[ZCMD.arg3] : "ERR",
                  ZCMD.arg4, ZCMD.arg2, obj_index[ZCMD.arg1].number);
          ZCMD.status = CMD_STAT_ERROR;
          ZCMD.old_command = ZCMD.command;
          ZCMD.command = '!'; /* disable */
        } else
          ZCMD.status = CMD_STAT_MAX_LIMIT_HIT;
        break;

      case 'D': /* set state of door */
        last_cmd = 0; // assume fail to start with
        if ((ZCMD.arg1 < 0) || !world[ZCMD.arg1].dir_option[ZCMD.arg2]) {
          logit(LOG_DEBUG, "D cmd: room: %d dir: %d state: %d' has error.",
                  (ZCMD.arg1 > 0) ? world[ZCMD.arg1].number : ZCMD.arg1, ZCMD.arg2, ZCMD.arg3);
          ZCMD.status = CMD_STAT_BAD_DOOR;
          ZCMD.old_command = ZCMD.command;
          ZCMD.command = '!'; /* disable */
          break;
        }

        if (ZCMD.arg4 < rare_pct) /* Support rare % for door loads - Shev 9/98*/
          break;

        switch (ZCMD.arg3 & 0x03) {
          case 0:
            REMOVE_BIT(world[ZCMD.arg1].dir_option[ZCMD.arg2]->exit_info, EX_LOCKED);
            REMOVE_BIT(world[ZCMD.arg1].dir_option[ZCMD.arg2]->exit_info, EX_CLOSED);
            break;
          case 1:
            SET_BIT(world[ZCMD.arg1].dir_option[ZCMD.arg2]->exit_info, EX_CLOSED);
            REMOVE_BIT(world[ZCMD.arg1].dir_option[ZCMD.arg2]->exit_info, EX_LOCKED);
            break;
          case 2:
          case 3:
            SET_BIT(world[ZCMD.arg1].dir_option[ZCMD.arg2]->exit_info, EX_LOCKED);
            SET_BIT(world[ZCMD.arg1].dir_option[ZCMD.arg2]->exit_info, EX_CLOSED);
            break;
        }

        if (ZCMD.arg3 & 0x04)
          SET_BIT(world[ZCMD.arg1].dir_option[ZCMD.arg2]->exit_info, EX_SECRET);

        if (ZCMD.arg3 & 0x08)
          SET_BIT(world[ZCMD.arg1].dir_option[ZCMD.arg2]->exit_info, EX_BLOCKED);

        if (ZCMD.arg3 & 0x16) {
          if (number(1, 100) < world[ZCMD.arg1].dir_option[ZCMD.arg2]->trap_load_percent) {
            SET_BIT(world[ZCMD.arg1].dir_option[ZCMD.arg2]->exit_info, EX_TRAPPED);
            world[ZCMD.arg1].dir_option[ZCMD.arg2]->trap_state = 1;
          }
        }

        ZCMD.status = CMD_STAT_SUCCESS;
        last_cmd = 1;
        break;

        /* Follower Code Format:                                       */
        /*    F <if> <room> <leader> <follower>                        */
        /*                                                             */
        /* The If flag is used to tell which type of follower          */
        /* we are creating as follows:                                 */
        /*  0  -- follower follows last loaded leader                  */
        /*  1  -- one follower pairs up with leader in room.           */
        /*        (avoids leaders that are already grouped and must    */
        /*         be used in pairs of mob/leader loads. For example:  */
        /*         suppose you wanted every goblin you load to come    */
        /*         with a warg pet, but several goblins might be in    */
        /*         the same room. Use this then. This is rarely done.) */
        /*  2  -- all followers in room group with leader as leader,   */
        /*        (if leader is same as follower then first one is     */
        /*         leader. If leader is not present in room, then we   */
        /*         simply do not follow anyone...)                     */
        /* More can be added if needed...                       -- DDW */

        /* potential problems include: lack of debugging...due to the  */
        /* incredible amount of spam this code would produce in the    */
        /* debug file, I've disabled all debugging...(imagine that     */
        /* undermountain has 100 follow commands, and a zone reset     */
        /* occurs, but noone has even set foot in Undermountain...this */
        /* means 100 failed follow commands logged in debug...which    */
        /* is ludicrous, so the area maker just needs to be smarter    */
        /* also, this command can't be disabled, because a follow might*/
        /* need to occur even if no other commands were executed...    */
        /* this means that it just might be a memory hog, especially   */
        /* with all the looping needed.                         -- DDW */

      case 'F': /* Mob follows another mob */
        leader = NULL;
        follower = NULL;
        followerFound = 0;
        if ((ZCMD.arg1 >= 0) && (ZCMD.arg2 >= 0) && (ZCMD.arg3 >= 0)) {
          /* okay, which type of follow is this? */
          switch (ZCMD.if_flag) {
              /* first case: follower will follow last loaded leader */
            case 0:
              for (tmpMob = world[ZCMD.arg1].people; tmpMob; tmpMob = nextMob) {
                nextMob = tmpMob->next_in_room;
                /* we have found the leader */
                if (!leader && (tmpMob->nr == ZCMD.arg2))
                  leader = tmpMob;

                /* we have found a follower that is not grouped */
                if (!follower && (tmpMob->nr == ZCMD.arg3) && (leader != tmpMob) && !tmpMob->following)
                  follower = tmpMob;
              }

              if (leader && follower) {
                if (follower->following && (follower->following != leader))
                  stop_follower(follower);

                if (!follower->following)
                  add_follower(follower, leader, TRUE);
              }
              break;

              /* second case: follower will pair with leader (avoids   */
              /* leaders that are already grouped in all cases. If we  */
              /* need to group with a leader already grouped, then use */
              /* if flag 0!) This should RARELY be used.               */
            case 1:
            case 3: /* Mount follower */
              /* we want to verify that the leader is in the room */
              for (tmpMob = world[ZCMD.arg1].people; tmpMob; tmpMob = nextMob) {
                nextMob = tmpMob->next_in_room;

                /* we have found the leader */
                if (!leader && (tmpMob->nr == ZCMD.arg2) &&
                        (!tmpMob->followers && !tmpMob->following))
                  leader = tmpMob;

                /* we have found the follower */
                if (!follower && (tmpMob->nr == ZCMD.arg3) && (tmpMob != leader) &&
                        !tmpMob->following && !tmpMob->followers)
                  follower = tmpMob;
              }

              if (leader && follower) {
                if (follower->following && (follower->following != leader))
                  stop_follower(follower);

                if (!follower->following)
                  add_follower(follower, leader, TRUE);

                if (ZCMD.if_flag == 3) /* mount command */
                  do_mount(leader, GET_NAME(follower), 0);
              }
              break;

              /* third case: all mobs in room will group with leader */
              /* special case of this occurs when the leader already */
              /* exists, is the same as the follower and must be re- */
              /* grouped.                                            */
            case 2:
              for (tmpMob = world[ZCMD.arg1].people; tmpMob; tmpMob = nextMob) {
                nextMob = tmpMob->next_in_room;

                /* first we find the first leader */
                if (!leader && (tmpMob->nr == ZCMD.arg2))
                  leader = tmpMob;

                /* this just makes sure the leader we want is the original */
                /* leader from the first zone reset...if possible          */
                if (leader && !leader->followers && (tmpMob->nr == ZCMD.arg2))
                  if (tmpMob->followers)
                    leader = tmpMob;
              }

              if (leader) { /* no need to waste our time if no leader */
                for (follower = world[leader->in_room].people; follower; follower = follower->next_in_room) {
                  if ((follower != leader) && (follower->nr == ZCMD.arg3) && !follower->following) {
                    if (follower->following && (follower->following != leader))
                      stop_follower(follower);

                    if (!follower->following)
                      add_follower(follower, leader, TRUE);

                    followerFound = 1;
                  }
                }
              }
              break;

              /* command fails */
            default:
              logit(LOG_DEBUG,
                      "reset_zone():[zone %d] Follow in %d with unknown if flag.\n"
                      "                          Failed: F %d %d %d %d",
                      zone, world[ZCMD.arg1].number, ZCMD.if_flag, world[ZCMD.arg1].number,
                      mob_index[ZCMD.arg2].virtual, mob_index[ZCMD.arg3].virtual);
              break;
          }
#if 0
          /* what exactly went wrong?? *//*disabled*/
          if ((!follower && ZCMD.if_flag < 2) || (ZCMD.if_flag == 2 && !followerFound))
            logit(LOG_DEBUG,
                  "reset_zone():[zone %d] Follower[%d] not in %d.\n"
                  "                          Failed: F %d %d %d %d",
                  zone, mob_index[ZCMD.arg3].virtual, world[ZCMD.arg1].number,
                  ZCMD.if_flag, world[ZCMD.arg1].number,
                  mob_index[ZCMD.arg2].virtual, mob_index[ZCMD.arg3].virtual);

          if (!leader)
            logit(LOG_DEBUG,
                  "reset_zone():[zone %d] leader[%d] does not in %d.\n"
                  "                          Failed: F %d %d %d %d",
                  zone, mob_index[ZCMD.arg2].virtual, world[ZCMD.arg1].number,
                  ZCMD.if_flag, world[ZCMD.arg1].number,
                  mob_index[ZCMD.arg2].virtual, mob_index[ZCMD.arg3].virtual);
#endif
        } else
          logit(LOG_DEBUG,
                "reset_zone():[zone %d] Follow in %d missing something.\n"
                "                            Failed: F %d %d %d %d",
                zone, world[ZCMD.arg1].number,
                ZCMD.if_flag, world[ZCMD.arg1].number,
                mob_index[ZCMD.arg2].virtual, mob_index[ZCMD.arg3].virtual);
        break;

      case 'R': /* remove an object from a room */
        last_cmd = 0; // assume fail to start with
        if ((ZCMD.arg1 >= 0) && (ZCMD.arg2 >= 0)) {
          if (ZCMD.arg4 && (ZCMD.arg4 < rare_pct)) /* Support rare % for Remove Obj - Shev 9/98 */
            break;

          obj = get_obj_in_list_num(ZCMD.arg2, world[ZCMD.arg1].contents);
          if (obj) {
            obj_from_room(obj);
            extract_obj(obj);
            ZCMD.status = CMD_STAT_SUCCESS;
            last_cmd = 1;
          }
        } else {
          logit(LOG_OBJ, "ERROR: R cmd: obj: %d from_room: %d",
                  (ZCMD.arg2 >= 0) ? obj_index[ZCMD.arg2].virtual : -1,
                  (ZCMD.arg1 >= 0) ? world[ZCMD.arg1].number : -1);
          ZCMD.status = CMD_STAT_REMOVE_OBJ_FAIL;
          ZCMD.old_command = ZCMD.command;
          ZCMD.command = '!'; /* disable */
        }
        break;

        /* remove a mob from a room - or ALL instances of specified mob - CRM */
      case 'X':
        last_cmd = 0; // assume fail to start with
        if (((ZCMD.arg1 >= 0) || (ZCMD.arg1 == -1)) && (ZCMD.arg2 >= 0)) {
          if (ZCMD.arg4 > 0 && ZCMD.arg4 < rare_pct) /* Support rare % for Remove Obj - Shev 9/98 */
            break;

          /* Remove ALL instances of specified mob.. */
          if (ZCMD.arg1 == -1) {
            for (mob = NPC_list; mob; mob = holdmob) {
              holdmob = mob->next;

              if (mob->nr == ZCMD.arg2) {
                if (mob->specials.fighting && ZCMD.arg3)
                  continue;

                act("&+L$n&N&+L sneaks away into the shadows...&N ", TRUE, mob, 0, NULL, TO_ROOM);

                if (mob->specials.fighting)
                  stop_fighting(mob);

                if (IS_CASTING(mob))
                  StopCasting(mob);

                StopAllAttackers(mob);
                stop_riding(mob);
                mob = ForceReturn(mob);

                for (i = 0; i < MAX_WEAR; i++) {
                  if (mob->equipment[i])
                    obj_to_char(unequip_char(mob, i, FALSE), mob);
                }

                for (obj = mob->carrying; obj != NULL; obj = next_obj) {
                  next_obj = obj->next_content;
                  obj_from_char(obj);
                  extract_obj(obj);
                }

                extract_char(mob);
                mob = NULL;
              }
            }

            ZCMD.status = CMD_STAT_SUCCESS;
            last_cmd = 1;
            break;
          }

          /* Remove single incidence of specified mob.. */
          mob = get_char_room_num(ZCMD.arg2, ZCMD.arg1);
          if (mob) {
            if (mob->specials.fighting && ZCMD.arg3)
              break;

            if (mob->specials.fighting)
              stop_fighting(mob);

            if (IS_CASTING(mob))
              StopCasting(mob);

            StopAllAttackers(mob);
            stop_riding(mob);
            mob = ForceReturn(mob);

            for (i = 0; i < MAX_WEAR; i++) {
              if (mob->equipment[i])
                obj_to_char(unequip_char(mob, i, FALSE), mob);
            }

            for (obj = mob->carrying; obj != NULL; obj = next_obj) {
              next_obj = obj->next_content;
              obj_from_char(obj);
              extract_obj(obj);
            }

            extract_char(mob);
            mob = NULL;
            ZCMD.status = CMD_STAT_SUCCESS;
            last_cmd = 1;
          }
        } else {
          logit(LOG_MOB, "ERROR: X cmd: mob: %d from_room: %d",
                  (ZCMD.arg2 >= 0) ? mob_index[ZCMD.arg2].virtual : -1,
                  (ZCMD.arg1 >= 0) ? world[ZCMD.arg1].number : -1);
          ZCMD.status = CMD_STAT_REMOVE_MOB_FAIL;
          ZCMD.old_command = ZCMD.command;
          ZCMD.command = '!'; /* disable */
        }
        break;

        /* Check any hour/day/weekday/month time against the current variables, */
        /* If all specified parameters match the current time, set last_cmd to  */
        /* true.  Useful for initiating a chain of zon cmds at a specific date. */
        /* - 10/98 CRM                                                          */

      case 'T':
        last_cmd = 1; // assume success to start with
        tmp_wkday = ((35 * time_info.month) + time_info.day + 1) % 7;

        /* Ugly, but it works.  Make last command true then check each */
        /* time parameter IF they exist.  If any fail the time check,  */
        /* we set last_cmd to false...like I said, ugly.  oh well. :P  */
        if (ZCMD.arg1 > -1 && time_info.hour != ZCMD.arg1)
          last_cmd = 0;

        if (ZCMD.arg2 && (time_info.day + 1) != ZCMD.arg2)
          last_cmd = 0;

        if (ZCMD.arg3 && (tmp_wkday + 1) != ZCMD.arg3)
          last_cmd = 0;

        if (ZCMD.arg4 && (time_info.month + 1) != ZCMD.arg4)
          last_cmd = 0;

        if (last_cmd)
          ZCMD.status = CMD_STAT_SUCCESS;
        else
          ZCMD.status = CMD_STAT_TIME_FAIL;
        break;

        /* dead code -Azuth */
#if 0
        /* L group command: L <if-flag> <group_vnum> */
      case 'L': /* these do not affect last_cmd */
        last_cmd = 0; // assume fail to start with
        if (ZCMD.arg1 >= 0) {
          if (group || grouping)
            logit(LOG_DEBUG, "WARNING: L cmd: grp: %d called with out previous Z!", ZCMD.arg1);

          if (!mob)
            logit(LOG_DEBUG, "ERROR: L cmd: grp: %d, leader was not loaded!", ZCMD.arg1);
          else {
            group = read_group(ZCMD.arg1, REAL);
            if (!group) {
              logit(LOG_DEBUG, "ERROR: L cmd: grp: %d does not exist", ZCMD.arg1);
              ZCMD.old_command = ZCMD.command;
              ZCMD.command = '!'; /* disable */
            } else {
              newGroupMember(group, mob);
              grouping = TRUE;
            }
          }
        } else
          logit(LOG_DEBUG, "ERROR: L cmd: grp: %d does not exist", ZCMD.arg1);
        break;
#endif

        /* Z group command: Z <if-flag> */
      case 'Z': /* these do not affect last_cmd */
        if (!grouping && !group)
          logit(LOG_DEBUG, "ERROR: Z cmd: not grouping when called");
        else {
          grouping = FALSE;
          /* check to make sure the group is more than 1 member */
          if (group->members && !group->members->next) {
            debuglog(51, DS_GROUP, "reset_zone: DeleteGroup for %s", C_NAME(group->leader));
            deleteGroup(group->leader, group);
          }

          group = NULL;
        }
        break;

      case '!':
        /* command previously disabled because of error */
        ZCMD.status = CMD_STAT_DISABLED;
        last_cmd = 0;
        break;

      default:
        logit(LOG_FILE, "Undefd cmd in reset table; zone %d cmd %d.", zone, cmd_no);
        logit(LOG_DEBUG, "Undefd cmd in reset table; zone %d cmd %d.", zone, cmd_no);
        ZCMD.status = CMD_STAT_UNKNOWN_CMD;
        ZCMD.old_command = ZCMD.command;
        ZCMD.command = '!';
        last_cmd = 0;
        break;
    }

#ifdef AREAS_DEBUG
    fprintf(stderr, " * Done>>\n");
#endif
  }
  
  /* Check if we exited without finding 'S' terminator */
  if (cmd_no >= zone_table[zone].cmd_count) {
    logit(LOG_BOOT, "WARNING: Zone %d (%s) commands not properly terminated with 'S' (processed %d commands)",
          zone, zone_table[zone].name ? zone_table[zone].name : "unknown", cmd_no);
  }

  /* new variable reset time zones (this appears to be unused as yet -Azuth) */
  if (zone_table[zone].lifespan_min != zone_table[zone].lifespan_max)
    zone_table[zone].lifespan = number(zone_table[zone].lifespan_min, zone_table[zone].lifespan_max);
  else
    zone_table[zone].lifespan = zone_table[zone].lifespan_min;

  // prep zone timer for it's next reset
  if (current_event)
    current_event->timer = zone_table[zone].lifespan;
  else {
    for (e1 = event_type_list[EVENT_RESET_ZONE]; e1; e1 = e1->next_type)
      if (e1->type == zone)
        break;
    if (e1)
      e1->timer = zone_table[zone].lifespan;
  }

  zone_table[zone].age = 0;
}

#undef ZCMD

/* for use in reset_zone; return TRUE if zone 'nr' is free of PC's  */
int is_empty(int zone_nr) {
  P_char i;

  for (i = PC_list; i; i = i->next) {
    if (i->desc && i->desc->connected)
      continue; /* not in game */

    // dear god, how did this happen, we weren't skipping gods in zones! -Azuth
    if (IS_TRUSTED(i))
      continue;

    if ((i->in_room != NOWHERE) && (world[i->in_room].zone == zone_nr))
      return (0);
  }

  return (1);
}

/************************************************************************
 *  procs of a (more or less) general utility nature              *
 ********************************************************************** */

/* read and allocate space for a '~'-terminated string from a given file.
   Added &n to end of strings with ansi, to prevent 'bleeding'  JAB */
char *fread_string(FILE * fl) {
  char buf[MAX_STRING_LENGTH + 1], tmp[MAX_STRING_LENGTH + 1], *rslt;
  int flag, t_len = 0, b_len = 0;
  register char *point;

  buf[0] = 0;
  tmp[0] = 0;

  if (!fl) {
    fprintf(stderr, "fread_str: null file pointer!\n");
    return NULL;
  }

  do {
    if (!fgets(tmp, MAX_STRING_LENGTH, fl)) {
      perror("fread_str");
      logit(LOG_DEBUG, "%s", tmp);
      dump_core();
    }

    t_len = strlen(tmp);
    b_len += t_len;

    if (b_len < MAX_STRING_LENGTH)
      strcat(buf, tmp);
    else {
      perror("fread_string: string too large (db.c)");
      logit(LOG_DEBUG, "%s", tmp);
      dump_core();
    }

    for (point = buf + b_len - 1; (point > buf) && isspace(*point); point--)
      ;

    if ((flag = (*point == '~')))
      *point = '\0';
    else
      *(buf + b_len) = '\0';

    b_len = strlen(buf);
  } while (!flag);

  /* do the allocate boogie  */
  if (b_len > 0) {
    /* ansi reset if they have color codes */
    if (strstr(buf, "&")) {
      switch (*(buf + b_len - 1)) {
        case 'n':
          if ((b_len > 1) && (*(buf + b_len - 2) == '&'))
            break;

        default:
          strcat(buf, "&n");
          b_len += 2;
      }
    }

    rslt = str_dup(buf);
  } else
    rslt = 0;

  return (rslt);
}

/* dupe of fread_string, needed so we can ignore it during bootup under insight.  JAB */
char *boot_fread_string(FILE * fl) {
  char buf[MAX_STRING_LENGTH], tmp[MAX_STRING_LENGTH], *rslt;
  int flag, t_len = 0, b_len = 0;
  register char *point;

  buf[0] = 0;
  tmp[0] = 0;

  if (!fl) {
    fprintf(stderr, "fread_str: null file pointer!\n");
    return NULL;
  }

  do {
    if (!fgets(tmp, MAX_STRING_LENGTH - 1, fl)) {
      perror("fread_str");
      logit(LOG_DEBUG, "%s", tmp);
      dump_core();
    }

    t_len = strlen(tmp);
    b_len += t_len;
    if (b_len > MAX_STRING_LENGTH) {
      perror("boot_fread_string: string too large (db.c)");
      logit(LOG_DEBUG, "%s", tmp);
      dump_core();
    } else
      strcat(buf, tmp);

    for (point = buf + b_len - 1; (point > buf) && isspace(*point); point--)
      ;

    if ((flag = (*point == '~')))
      *point = '\0';
    else
      *(buf + b_len) = '\0';

    b_len = strlen(buf);
  } while (!flag);

  /* do the allocate boogie  */
  if (b_len > 0) {
    /* ansi reset if they have color codes */
    if (strstr(buf, "&") && (*(buf + b_len - 1) != 'n')) {
      strcat(buf, "&n");
      b_len += 2;
    }

#ifdef MEM_DEBUG
    mem_use[MEM_STRINGS] += b_len + 1;
#endif
    CREATE(rslt, char, (unsigned) (b_len + 1));

    strcpy(rslt, buf);
  } else
    rslt = 0;

  return (rslt);
}

/* advance file pointer past next '~' terminated string, this saves us an alloc and free when we already
   HAVE that string in common storage.  read_object() and read_mobile() use this after the first call for
   each obj/mob.  JAB */

void skip_fread(FILE *fl) {
  char tmp[MAX_STRING_LENGTH];
  register char *point;

  if (!fl) {
    fprintf(stderr, "skip_fread: null file pointer!\n");
    return;
  }

  for (;;) {
    if (!fgets(tmp, MAX_STRING_LENGTH - 1, fl)) {
      perror("skip_fread");
      logit(LOG_DEBUG, "%s", tmp);
      dump_core();
    }

    for (point = tmp + strlen(tmp) - 1; (point >= tmp) && isspace(*point); point--)
      ;

    if (*point == '~')
      return;
  }
}

/* release memory allocated for a char struct */
void free_char(P_char ch) {
  struct Trophy_data *troph1, *troph2;
  struct affected_type *af, *tmp;

  if (!ch) {
    logit(LOG_DEBUG, "free_char called with no char!");
    return;
  }

  for (af = ch->affected; af; af = tmp) {
    tmp = af->next;
    affect_remove(ch, af);
  }

  if (IS_PC(ch))
    delete_memorize_list(ch, ch->only.pc->memorize_list); /* memorize spells -DCL */

  /* remove all events associated with this ch */
  ClearCharEvents(ch);

  if (IS_NPC(ch)) {
    /* MOST mob strings should not be freed, as they are shared among all
       mobs with the same Vnum.  Only if a string has been altered inside the
       game, should it be freed here. */
    if ((ch->only.npc->str_mask & STRUNG_KEYS) && ch->player.name) {
      free_string(ch->player.name);
      ch->player.name = NULL;
    }

    if ((ch->only.npc->str_mask & STRUNG_DESC1) && ch->player.long_descr) {
      free_string(ch->player.long_descr);
      ch->player.long_descr = NULL;
    }

    if ((ch->only.npc->str_mask & STRUNG_DESC2) && ch->player.short_descr) {
      free_string(ch->player.short_descr);
      ch->player.short_descr = NULL;
    }

    if ((ch->only.npc->str_mask & STRUNG_DESC3) && ch->player.description) {
      free_string(ch->player.description);
      ch->player.description = NULL;
    }
  } else { /* nuke the list of trophies */
    for (troph1 = ch->only.pc->Trophies; troph1; troph1 = troph2) {
      troph2 = troph1->next;
      free((char *) troph1);
    }

    /* unlike for mobs, all player strings are unique, so they get freed always (if they exist of course) */
    if (ch->only.pc->poofIn) {
      free_string(ch->only.pc->poofIn);
      ch->only.pc->poofIn = NULL;
    }

    if (ch->only.pc->poofOut) {
      free_string(ch->only.pc->poofOut);
      ch->only.pc->poofOut = NULL;
    }

    if (ch->only.pc->title) {
      free_string(ch->only.pc->title);
      ch->only.pc->title = NULL;
    }

    if (ch->only.pc->last_login) {
      free_string(ch->only.pc->last_login);
      ch->only.pc->last_login = NULL;
    }

    if (ch->player.description) {
      free_string(ch->player.description);
      ch->player.description = NULL;
    }

    if (ch->player.name) {
      free_string(ch->player.name);
      ch->player.name = NULL;
    }
  }

  if (IS_NPC(ch)) {
#ifdef SERIAL_NUMBERS
    logit(LOG_LIFE, "%9u destroyed (NPC)", ch->OSN);
#endif
    mm_release(dead_npc_pool, ch->only.npc);
    ch->only.npc = NULL;
  } else {
#ifdef SERIAL_NUMBERS
    logit(LOG_LIFE, "%9u destroyed (PC)", ch->OSN);
#endif
    mm_release(dead_pc_pool, ch->only.pc);
    ch->only.pc = NULL;
  }

  ch->in_room = NOWHERE;
  ch->specials.was_in_room = NOWHERE;
  ch->next = NULL;
  ch->next_in_room = NULL;

  SET_POS(ch, GET_POS(ch) + STAT_DEAD); /* set stat_dead in case the char was purged instead of killed */

  AddEvent(EVENT_CHAR_EXECUTE, 480, TRUE, ch, release_mob_mem);
  return;
}

/* release memory allocated for an obj struct */

void free_obj(P_obj obj) {
  struct extra_descr_data *edd, *next_one;

  if (!obj) {
    logit(LOG_DEBUG, "free_obj called with no obj!");
    return;
  }

  ClearObjEvents(obj);

  /* MOST obj strings should not be freed, as they are shared among all objects
     with the same Vnum.  Only if a string has been altered inside the game,
     should it be freed here. */

  if ((obj->str_mask & STRUNG_KEYS) && obj->name) {
    free_string(obj->name);
    obj->name = NULL;
  }

  if ((obj->str_mask & STRUNG_DESC1) && obj->description) {
    free_string(obj->description);
    obj->description = NULL;
  }

  if ((obj->str_mask & STRUNG_DESC2) && obj->short_description) {
    free_string(obj->short_description);
    obj->short_description = NULL;
  }

  if ((obj->str_mask & STRUNG_DESC3) && obj->action_description) {
    free_string(obj->action_description);
    obj->action_description = NULL;
  }

  for (edd = obj->ex_description; edd; edd = next_one) {
    next_one = edd->next;
    if (edd->keyword) {
      free_string(edd->keyword);
      edd->keyword = NULL;
    }

    if (edd->description) {
      free_string(edd->description);
      edd->description = NULL;
    }

#ifdef MEM_DEBUG
    mem_use[MEM_E_DSCR] -= sizeof (struct extra_descr_data);
#endif
    free((char *) edd);
  }

#ifdef SERIAL_NUMBERS
  logit(LOG_LIFE, "%9u destroyed (OBJ)", obj->OSN);
#endif

  release_obj_mem(obj);
}

/* read contents of a text file, and place in buf */

/* Removed global array, this function now mallocs what it needs SAM 7-94 */

char *file_to_string(const char *name) {
  FILE *fl;
  char tmp[256], *ptr;
  char Gbuf1[MAX_STRING_LENGTH];

  Gbuf1[0] = 0;

  if (!(fl = fopen(name, "r"))) {
    sprintf(tmp, "file-to-string (%s)", name);
    perror(tmp);
    return (NULL);
  }

  do {
    fgets(tmp, 255, fl);

    if (!feof(fl)) {
      if (strlen(Gbuf1) + strlen(tmp) + 2 > MAX_STRING_LENGTH) {
        logit(LOG_FILE, "file_to_string(): file (%s) too long.", name);
        return (NULL);
      }

      strcat(Gbuf1, tmp);
      *(Gbuf1 + strlen(Gbuf1)) = '\0';
    }
  } while (!feof(fl));

  fclose(fl);
#ifdef MEM_DEBUG
  mem_use[MEM_STRINGS] += strlen(Gbuf1) + 1;
#endif
  CREATE(ptr, char, strlen(Gbuf1) + 1);

  strcpy(ptr, Gbuf1);

  return (ptr);
}

/* every character and object created now has a unique serial number assigned to it when it is created.
   Currently the only thing this number is used for is to generate creation/destruction log entries in
   LOG_LIFE.  Future usage is planned however.

   JAB */

uint GetNewOSN(int type) {
  FILE *OSN_file = NULL;
  static uint next_OSN = 0;
  uint tmp;

  /* functionality:  we grab 100 numbers at once from the serial number file, update the serial number file
     then use those numbers as needed.  If the mud crashes the serial number file will already be updated, so
     there is no danger of duplicate serial numbers.  Worst case, we might have 100 unassigned serial numbers. */

  if (!next_OSN || !(next_OSN % 100)) {
    /* bootup or time to fetch more */
    if (!(OSN_file = fopen("lib/misc/OSN_file", "r+")))
      dump_core();

    if (fscanf(OSN_file, "%u\n", &tmp) != 1)
      dump_core();

    if (tmp != next_OSN) {
      if (next_OSN)
        dump_core();
      else
        next_OSN = tmp;
    }

    tmp += 100;

    rewind(OSN_file);
    if (fprintf(OSN_file, "%u\n", tmp) < 4)
      dump_core();

    fclose(OSN_file);
    OSN_file = NULL;
  }

  /* ok, due to extremely paranoid section above, we know we can assign our next OSN with a clear conscience */

  switch (type) {
    case NEW_PC:
      logit(LOG_LIFE, "%9u created (PC)", next_OSN);
      break;
    case NEW_NPC:
      logit(LOG_LIFE, "%9u created (NPC)", next_OSN);
      break;
    case NEW_OBJ:
      logit(LOG_LIFE, "%9u created (OBJ)", next_OSN);
      break;
    default:
      dump_core();
  }

  next_OSN++;

  return (next_OSN - 1);
}

/* this was clear_char, but I incorporated all the functionality of creating a new character struct, so that
   one call will give you a shiny new (and zeroed out) character struct.  Already inserted in the various
   lists.

   type is either NEW_PC or NEW_NPC, since they have different attached structures.

   Returns a pointer to the new char struct, if it CAN'T create the new struct, it dumps core, since something
   serious is obviously wrong.

   called from: read_mobile(), nanny() and extract_char().

   JAB */

P_char GetNewChar(int type) {
  P_char t_ch = NULL;


  if (!(t_ch = (P_char) mm_get(dead_char_pool)))
    dump_core();

  bzero(t_ch, sizeof (struct char_data)); /* belt and suspenders, unused mmds memory should be zeroed */

  if (type == NEW_PC) {
    if (!(t_ch->only.pc = (struct pc_only_data *) mm_get(dead_pc_pool)))
      dump_core();

    bzero(t_ch->only.pc, sizeof (struct pc_only_data));

    t_ch->next = PC_list;
    PC_list = t_ch;
    if (pclist_debug)
      debuglog(51, DS_PC, "GetNewChar(NEW_PC): post add [%s]", C_NAME(t_ch) ? C_NAME(t_ch) : "NULL");

    /* grumble, have to set this here, or people with idiot terms can't roll new chars clearly */
    SET_CBIT(t_ch->only.pc->pcact, PLR_DUMB_TERM);

  } else if (type == NEW_NPC) {
    if (!(t_ch->only.npc = (struct npc_only_data *) mm_get(dead_npc_pool)))
      dump_core();

    bzero(t_ch->only.npc, sizeof (struct npc_only_data));

    t_ch->next = NPC_list;
    NPC_list = t_ch;
  } else
    dump_core(); /* assume nothing */

  /* set defaults */
#ifdef SERIAL_NUMBERS
  t_ch->OSN = GetNewOSN(type);
#endif
  t_ch->in_room = t_ch->nr = t_ch->specials.was_in_room = NOWHERE;

  SET_POS(t_ch, POS_STANDING + STAT_NORMAL);
  t_ch->points.base_armor = 100;

  return t_ch;
}

/* this used to be clear_object(), but in keeping with new interface, if now handles all the drudgery of new
   object creation.

   type is currently ignored (there is only one), but I added it to prevent having to change prototypes later.

   Returns either a pointer to a new object, or dumps core.
   called from: read_object()

   JAB */

P_obj GetNewObj(int type) {
  P_obj t_obj = NULL;


  if (type != NEW_OBJ)
    dump_core();

  if (!(t_obj = (P_obj) mm_get(dead_obj_pool)))
    dump_core();

  bzero(t_obj, sizeof (struct obj_data));

  /* update the object list (which is doubly linked) */
  if (object_list)
    object_list->prev = t_obj;

  t_obj->next = object_list;
  object_list = t_obj;

  /* set defaults */

#ifdef SERIAL_NUMBERS
  t_obj->OSN = GetNewOSN(type);
#endif
  t_obj->R_num = -1;
  t_obj->loc_p = LOC_NOWHERE;
  t_obj->loc.room = NOWHERE;

  return t_obj;
}

/* returns the real number of the room with given virtual number */
int real_room(int virtual) {
  int bot, top, mid;

  /* Check for invalid virtual number first */
  if (virtual < 0) {
    return (-1);
  }

  bot = 0;
  top = top_of_world;

  /* perform binary search on world-table */
  for (;;) {
    mid = (bot + top) >> 1;

    if ((world + mid)->number == virtual)
      return (mid);

    if (bot >= top)
      return (-1);

    if ((world + mid)->number > virtual)
      top = mid - 1;
    else
      bot = mid + 1;
  }
}

/*
 * the reason to have real_room0(), real_mobile0(), and real_object0()
 * that mirrors the original functions is very simple.  These new
 * functions returns 0 instead of -1 for items not found in database. So
 * in spec_ass.c when it calls these functions it won't be assigning to a
 * -1 array index entry.  This causes some problem when zones are removed
 * or ids are reassigned. -DCL
 * returns the real number of the room with given virtual number */
int real_room0(int virtual) {
  int bot, top, mid;

  bot = 0;
  top = top_of_world;

  /* perform binary search on world-table */
  for (;;) {
    mid = (bot + top) >> 1;

    if ((world + mid)->number == virtual)
      return (mid);

    if (bot >= top)
      return (0);

    if ((world + mid)->number > virtual)
      top = mid - 1;
    else
      bot = mid + 1;
  }
}

/* returns the real number of the monster with given virtual number */
int real_mobile(int virtual) {
  int bot, top, mid;

  /* Check for invalid virtual number first */
  if (virtual < 0) {
    return (-1);
  }

  bot = 0;
  top = top_of_mobt;

  /* perform binary search on mob-table */
  for (;;) {
    mid = (bot + top) >> 1;
    
    /* Bounds check to prevent invalid reads */
    if (mid < 0 || mid > top_of_mobt) {
      return (-1);
    }

    if ((mob_index + mid)->virtual == virtual)
      return (mid);

    if (bot >= top)
      return (-1);

    if ((mob_index + mid)->virtual > virtual)
      top = mid - 1;
    else
      bot = mid + 1;
  }
}

/* returns the real number of the object with given virtual number */
int real_object(int virtual) {
  int bot, top, mid;

  /* Check for invalid virtual number first */
  if (virtual < 0) {
    return (-1);
  }

  bot = 0;
  top = top_of_objt;

  /* perform binary search on obj-table */
  for (;;) {
    mid = (bot + top) >> 1;

    if ((obj_index + mid)->virtual == virtual)
      return (mid);

    if (bot >= top)
      return (-1);

    if ((obj_index + mid)->virtual > virtual)
      top = mid - 1;
    else
      bot = mid + 1;
  }
}

void verify_mobile(P_char mob) {
  int hold = 0, plat, gold, silver, copper;
  int p_p, p_g, p_s, coins, orig_coins;
  int current_plat, current_gold, current_silver, current_copper;
  int current_exp, current_coins;

  /* Sanity Check */
  if (!mob) {
    logit(LOG_EXIT, "verify_mobile(mob == NULL)");
    dump_core();
  }

  current_exp = GET_EXP(mob);
  current_plat = GET_PLATINUM(mob);
  current_gold = GET_GOLD(mob);
  current_silver = GET_SILVER(mob);
  current_copper = GET_COPPER(mob);

  /*** STAGE 1: Experience and Money ***/
  if (GET_LEVEL(mob) < 11) {
    hold = (GET_LEVEL(mob) * 100);
    coins = (GET_LEVEL(mob)) * 52.;
  } else if ((GET_LEVEL(mob) > 10) && (GET_LEVEL(mob) < 21)) {
    hold = (GET_LEVEL(mob) * 400);
    coins = (GET_LEVEL(mob)) * 78.;
  } else if ((GET_LEVEL(mob) > 20) && (GET_LEVEL(mob) < 31)) {
    hold = (GET_LEVEL(mob) * 750);
    coins = (GET_LEVEL(mob)) * 117.;
  } else if ((GET_LEVEL(mob) > 30) && (GET_LEVEL(mob) < 41)) {
    hold = (GET_LEVEL(mob) * 1000);
    coins = (GET_LEVEL(mob)) * 175.;
  } else if ((GET_LEVEL(mob) > 40) && (GET_LEVEL(mob) < 51)) {
    hold = (GET_LEVEL(mob) * 2500);
    coins = (GET_LEVEL(mob)) * 263.;
  } else if (GET_LEVEL(mob) > 50) {
    hold = (GET_LEVEL(mob) * 5000);
    coins = (GET_LEVEL(mob)) * 394.;
  }


  p_p = GET_LEVEL(mob) * 16;
  p_g = GET_LEVEL(mob) * 11;
  p_s = GET_LEVEL(mob) * 6;

  /* ok, got %'s, now we figure the real coins */
  orig_coins = coins;
  plat = orig_coins * p_p / 1000000;
  if (plat < 0)
    plat = 0;

  coins -= plat * 1000;
  gold = orig_coins * p_g / 100000;
  if (gold < 0)
    gold = 0;

  coins -= gold * 100;
  while ((coins < 0) && (gold > 0)) {
    gold--;
    coins += 100;
  }

  silver = orig_coins * p_s / 10000;
  if (silver < 0)
    silver = 0;

  coins -= silver * 10;
  while ((coins < 0) && (silver > 0)) {
    silver--;
    coins += 10;
  }

  copper = coins;
  if (copper < 0)
    copper = 0;

  /* ok, now to prevent final silliness */
  while (copper > number(40, 2 * (80 - GET_LEVEL(mob)))) {
    copper -= 14;
    silver++;
  }

  while (silver > number(40, 2 * (80 - GET_LEVEL(mob)))) {
    silver -= 14;
    gold++;
  }

  while (gold > number(40, 2 * (80 - GET_LEVEL(mob)))) {
    gold -= 14;
    plat++;
  }

  /* No cash for certain mob types likes animals, birds, etc. */
  switch (GET_RACE(mob)) {
    case RACE_ANIMAL:
    case RACE_ARACHNID:
    case RACE_A_ELEMENTAL:
    case RACE_BIRD:
    case RACE_CANINE:
    case RACE_CARNIVORE:
    case RACE_E_ELEMENTAL:
    case RACE_FELINE:
    case RACE_FISH:
    case RACE_F_ELEMENTAL:
    case RACE_GOLEM:
    case RACE_HERBIVORE:
    case RACE_HORSE:
    case RACE_INSECT:
    case RACE_NONE:
    case RACE_POSSESSED:
    case RACE_PRIMATE:
    case RACE_RAPTOR:
    case RACE_REPTILE:
    case RACE_SLIME:
    case RACE_SNAKE:
    case RACE_TREE:
    case RACE_UNDEAD:
    case RACE_W_ELEMENTAL:
      copper = 0;
      silver = 0;
      gold = 0;
      plat = 0;
      break;
    case RACE_DEMON:
    case RACE_DEVIL:
      gold = (gold * 2);
      break;
    case RACE_DRAGON:
      copper = (copper * 8);
      silver = (silver * 6);
      gold = (gold * 4);
      plat = (plat * 2);
      break;
    case RACE_DRAGONKIN:
      copper = (copper * 4);
      silver = (silver * 3);
      gold = (gold * 2);
      break;
  }

  if (current_exp < (hold + hold * .2) || current_exp > (hold + hold * .2))
    logit(LOG_MOB, "verify_mobile: exp %d out of nominal range on mob %d", current_exp, mob_index[mob->nr].virtual);

  coins = plat + 10 * gold + 100 * silver + 1000 * copper;
  current_coins = current_plat + 10 * current_gold + 100 * current_silver + 1000 * current_copper;
  if (current_coins > (coins + coins * .2))
    logit(LOG_MOB, "verify_mobile: coins %d out of nominal range on mob %d", current_coins, mob_index[mob->nr].virtual);
}

/* check to see if this zone has credentials to be major reset
   returns TRUE if so FALSE if not
   check last visit by a PC to zone, major reset percent, etc
   Azuth 10/28/03 */
int major_reset_zone(int zone) {
  struct time_info_data realtime;

  real_time_passed(&realtime, time(0), zone_table[zone].time_last_boot);

  if (realtime.hour < zone_table[zone].hrs_since_req)
    return FALSE;

  if (zone_table[zone].zone_empty_cnt < zone_table[zone].zone_empty_cnt_req)
    return FALSE;

  if (number(1, 100) <= zone_table[zone].mzr_pct)
    return TRUE;

  return FALSE;
}

int purge_all_mobs(int zone);
int purge_all_objs(int zone);

/* attempt to purge things from this zone in preparations for a major reset
   returns TRUE if purge is successful, FALSE if not
   what all gets purged and how is TBD
   Azuth 10/28/03 */
int purge_zone(int zone) {
  switch (zone_table[zone].top) {
    case 96296: // Jot
      if (!purge_all_mobs(zone)) // do mobs first so that eq they have falls to ground, then do eq
        return FALSE;

      if (!purge_all_objs(zone))
        return FALSE;
      break;

    default:
      return FALSE;
  }

  return TRUE;
}

// PCs might be in the zone and maybe in the room fighting the mob!

int purge_all_mobs(int zone) {
  P_char ch = NULL, chhold = NULL;
  int room = 0;

  while ((world[room].zone != zone) && (room < top_of_world))
    room++;

  while ((world[room].zone == zone) && (room < top_of_world)) {
    for (ch = world[room].people; ch; ch = chhold) {
      chhold = ch->next_in_room;
      if (IS_PC(GET_PLYR(ch))) // probably shouldn't purge the players, althoguh...
        continue;

      if (ch->specials.fighting)
        continue; // make it simple for now, skip fighting mobs

      if ((mob_index[ch->nr].virtual >= world[zone_table[zone].real_bottom].number) &&
              (mob_index[ch->nr].virtual <= world[zone_table[zone].real_top].number))
        extract_char(ch);
    }

    room++;
  }

  return TRUE;
}

int purge_all_objs(int zone) {
  P_obj obj = NULL, objhold = NULL;
  int room = 0;

  while ((world[room].zone != zone) && (room < top_of_world))
    room++;

  while ((world[room].zone == zone) && (room < top_of_world)) {
    for (obj = world[room].contents; obj; obj = objhold) {
      objhold = obj->next_content;
      if ((obj->type == ITEM_CORPSE) && (obj->value[1] == PC_CORPSE))
        continue; // probably shouldn't purge the player's corpses either

      if ((obj_index[obj->R_num].virtual >= world[zone_table[zone].real_bottom].number) &&
              (obj_index[obj->R_num].virtual <= world[zone_table[zone].real_top].number))
        extract_obj(obj);
    }

    room++;
  }

  return TRUE;
}

// this is temp for debugging only -Azuth 10/25/04

void report_world(void) {
  int z, w;

  if (!zone_table)
    logit(LOG_STATUS, "Zone_table variable does not exist!");
  else {
    logit(LOG_STATUS, "Listing of zones:");
    for (z = 0; z <= top_of_zone_table; z++) {
      //         logit(LOG_STATUS, "zone %4d: (%5d-%5d) %s", z, z ? (zone_table[z - 1].top + 1) : 0, zone_table[z].top, zone_table[z].name);
      logit(LOG_STATUS, "zone %4d: top: %6d real_bottom: %6d real_top: %6d %s", z, zone_table[z].top, zone_table[z].real_bottom, zone_table[z].real_top, zone_table[z].name ? zone_table[z].name : "blank named zone?");
    }
  }

  if (!world)
    logit(LOG_STATUS, "World variable does not exist yet!");
  else {
    logit(LOG_STATUS, "Listing of 1st 2000 rooms:");
    for (w = 0; (w <= top_of_world) && (w <= 2000); w++) // world exist
      logit(LOG_STATUS, "World[%6d](%6d) = %s", w, world[w].number, world[w].name ? world[w].name : "blank named room?");
  }

  logit(LOG_STATUS, "All done..");
}

