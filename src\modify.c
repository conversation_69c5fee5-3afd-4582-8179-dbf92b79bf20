/* ***************************************************************************
 *  File: modify.c                                             Part of Outcast *
 *  Usage: Run-time modification (by users) of game variables                *
 *  Copyright  1990, 1991 - see 'license.doc' for complete information.      *
 *  Copyright  1994, 1995 - Outcast Systems Ltd.                             *
 *  Copyright  1997 - <PERSON>.                                           *
 *************************************************************************** */

#include <ctype.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <errno.h>

#include "assocs.h"
#include "board.h"
#include "comm.h"
#include "db.h"
#include "events.h"
#include "interp.h"
#include "prototypes.h"
#include "structs.h"
#include "utils.h"
#ifdef KINGD<PERSON>
   #include "kingdom.h"
#endif
/* external variables */

extern P_desc descriptor_list;
extern P_event current_event;
extern P_room world;
extern int reboot;
extern int shutdownflag;
extern int slow_death;
extern int tics;
extern struct board_data Boards[BOARD_COUNT];
extern const int upgrade_costs[];
#ifdef KINGDOM
extern P_house_upgrade house_upgrade_list;
#endif
#define REBOOT_AT   6           /* 0-23, time of optional reboot if -e lib/reboot */

#define TP_MOB      0
#define TP_OBJ      1
#define TP_ERROR    2

int help_array[27][2];
int whelp_array[27][2];
int info_array[27][2];

const char *string_fields[] =
{
   "name",
   "short",
   "long",
   "description",
   "title",
   "delete-description",
   "\n"
};

/* maximum length for text field x+1 */
const int length[] =
{
   32,
   64,
   256,
   240,
   64
};

const char *skill_fields[] =
{
   "learned",
   "affected",
   "duration",
   "recognize",
   "\n"
};

const int max_value[] =
{
   255,
   255,
   10000,
   1
};

/* ************************************************************************
   *  modification of malloc'ed strings                                      *
   ************************************************************************ */

/* Add user input to the 'current' string (as defined by d->str) */

void string_add(struct writing_info *writing, char *str)
{
   P_char ch;
   char *scan, *ch_ptr, buf[MAX_STRING_LENGTH + 1];
   int terminator = 0;

   if(!writing || !(ch = writing->writer))
      dump_core();

   /* Check for ansi characters, mortals not allowed to use them
      to put color in says, shouts, gossips, titles, etc. SAM 6-94 */
   if(GET_LEVEL(ch) < MINLVLIMMORTAL)
      {
      for(ch_ptr = str; *ch_ptr; ch_ptr++)
         {
         if(*ch_ptr == '&')
            switch(*(ch_ptr + 1))
               {
               case '+':
               case '-':
               case '=':
               case 'n':
               case 'N':
                  if(ch->desc)
                     SEND_TO_Q("No ansi chars allowed as input.  Discarding line.\n", ch->desc);
                  return;
                  break;
               }
         }
      }
   /* determine if this is the terminal string, and truncate if so */
   for(scan = str; *scan; scan++)
      if((terminator = (*scan == '@' && *(scan + 1) == '@')))
         {
         *scan = '\0';
         break;
         }
   if(!writing->text)
      {
      if((strlen(str) > writing->max_length) && (GET_LEVEL(ch) < 54))
         {
         send_to_char("String too long - Truncated.\n", ch);
         *(str + writing->max_length) = '\0';
         terminator = 1;
         }

      strcpy(buf, str);
      if(!terminator)
         strcat(buf, "\n");
      writing->text = str_dup(buf);
      }
   else
      {
      if(((strlen(str) + strlen(writing->text) + (terminator) ? 1 : 2) > writing->max_length) &&
         (GET_LEVEL(ch) < 54))
         {
         send_to_char("String too long. Last line skipped.\n", ch);
         terminator = 1;
         }
      else
         {
         /* note, these contortions are neccessary, or the malloced blocks (via str_dup) will just be concated, and
          * any trailing strings will be left hanging when freed.  This is basically a form of RECREATE.  JAB */
         strcpy(buf, writing->text);
         strcat(buf, str);
         if(!terminator)
            strcat(buf, "\n");
         free_string(writing->text);
         writing->text = str_dup(buf);
         }
      }

   if(terminator)
      ApplyWriting(writing);

}

#undef MAX_STR

/* interpret an argument for do_string */
void quad_arg(char *arg, int *type, char *name, int *field, char *string)
{
   char buf[MAX_STRING_LENGTH];

   /* determine type */
   arg = one_argument(arg, buf);
   if(is_abbrev(buf, "char"))
      *type = TP_MOB;
   else if(is_abbrev(buf, "obj"))
      *type = TP_OBJ;
   else
      {
      *type = TP_ERROR;
      return;
      }

   /* find name */
   arg = one_argument(arg, name);

   /* field name and number */
   arg = one_argument(arg, buf);
   if(!(*field = old_search_block(buf, 0, strlen(buf), string_fields, 0)))
      return;

   /* string */
   for(; isspace(*arg); arg++);
   for(; (*string = *arg); arg++, string++);

   return;
}

/* modification of malloc'ed strings in chars/objects */

void do_string(P_char ch, char *arg, int cmd)
{
   P_char mob;
   P_obj obj;
   char name[MAX_STRING_LENGTH], string[MAX_STRING_LENGTH];
   int field, type, i;
   struct extra_descr_data *ed, *tmp;
   struct writing_info *writing;

   if(IS_NPC(GET_PLYR(ch)))
      return;

   if(cmd && *arg)
      {
      wizcmd(GET_LEVEL(GET_PLYR(ch)), "%s: string %s", GET_NAME(GET_PLYR(ch)), arg);
      logit(LOG_WIZ, "%s: string %s", GET_NAME(GET_PLYR(ch)), arg);
      }
   quad_arg(arg, &type, name, &field, string);

   if(type == TP_ERROR)
      {
      send_to_char("Syntax:\nstring ('obj'|'char')<name><field>[<string>].", ch);
      return;
      }
   if(!field)
      {
      send_to_char("No field by that name. Try 'help string'.\n", ch);
      return;
      }

   CREATE(writing, struct writing_info, 1);
   writing->max_length = MAX_STRING_LENGTH;
   writing->writer = ch;

   if(type == TP_MOB)
      {
      /* locate the beast */
      if(!(mob = get_char_in_game_vis(ch, name, TRUE)))
         {
         send_to_char("I don't know anyone by that name...\n", ch);
         free((char *) writing);
         return;
         }

      writing->what = WRT_STRING_CH;
      writing->targ_ch = mob;

      switch(field)
         {
         case 1:
            if(IS_PC(mob) && (GET_LEVEL(ch) < MAXLVL - 2))
               {
               send_to_char("You can't change that field for players.", ch);
               free((char *) writing);
               return;
               }

            writing->text_start = &mob->player.name;
            writing->str_mask = STRUNG_KEYS;

            break;

         case 2:
            if(IS_PC(mob))
               {
               send_to_char("That field is for NPCs only.\n", ch);
               free((char *) writing);
               return;
               }

            writing->text_start = &mob->player.short_descr;
            writing->str_mask = STRUNG_DESC2;

            break;

         case 3:
            if(IS_PC(mob))
               {
               send_to_char("That field is for NPCs only.\n", ch);
               free((char *) writing);
               return;
               }

            writing->text_start = &mob->player.long_descr;
            writing->str_mask = STRUNG_DESC1;

            break;

         case 4:
            writing->text_start = &mob->player.description;
            writing->str_mask = STRUNG_DESC3;

            break;

         case 5:
            if(IS_NPC(mob))
               {
               send_to_char("NPCs don't have titles.\n", ch);
               free((char *) writing);
               return;
               }

            writing->text_start = &mob->only.pc->title;

            if(IS_PC(mob) && (GET_LEVEL(ch) < GET_LEVEL(mob)))
               {
               send_to_char("You DARE try to deface the name of your elders!?!? DIE WRETCH!\n", ch);
               statuslog(51, "%s was brutally slain for trying to change %s's title. Muhahaha!.",
                         GET_NAME(ch), GET_NAME(mob));
               raw_kill(ch);
               free((char *) writing);
               return;
               }
            else
               {
               if(mob->only.pc->title)
                  {
                  free_string(mob->only.pc->title);
                  mob->only.pc->title = NULL;
                  }
               }
            break;

         default:
            send_to_char("That field is undefined for characters.\n", ch);
            free((char *) writing);
            return;
            break;
         }
      }
   else
      {                      /* type == TP_OBJ */
      /* locate the object */

      if(!(obj = get_obj_in_hand(ch, name, &i)) || !CAN_SEE_OBJ(ch, obj))
         if(!(obj = get_obj_in_list_vis(ch, name, ch->carrying)))
            if(!(obj = get_object_in_equip_vis(ch, name, &i)))
               if((obj = get_obj_in_list_vis(ch, name, world[ch->in_room].contents)) == NULL)
                  {
                  send_to_char("No object by that name here.\n", ch);
                  free((char *) writing);
                  return;
                  }

      writing->what = WRT_STRING_OBJ;
      writing->targ_obj = obj;

      switch(field)
         {
         case 1:
            writing->text_start = &obj->name;
            writing->str_mask = STRUNG_KEYS;

            break;

         case 2:
            writing->text_start = &obj->short_description;
            writing->str_mask = STRUNG_DESC2;

            break;

         case 3:
            writing->text_start = &obj->description;
            writing->str_mask = STRUNG_DESC1;

            break;

         case 4:
            if(!*string)
               {
               send_to_char("You have to supply a keyword.\n", ch);
               free((char *) writing);
               return;
               }

            writing->str_mask = STRUNG_EDESC;

            /* try to locate extra description */
            for(ed = obj->ex_description;; ed = ed->next)
               if(!ed)
                  {              /* the field was not found. create a new one. */
#ifdef MEM_DEBUG
                  mem_use[MEM_E_DSCR] += sizeof(struct extra_descr_data);
#endif
                  CREATE(ed, struct extra_descr_data, 1);

                  ed->next = obj->ex_description;
                  obj->ex_description = ed;

                  ed->keyword = str_dup(string);
                  writing->text_start = &ed->description;
                  *string = '\0';
                  send_to_char("New field.\n", ch);
                  break;
                  }
               else if(!str_cmp(ed->keyword, string))
                  {     /* the field exists */
                  writing->text_start = &ed->description;
                  *string = '\0';
                  send_to_char("Modifying description.\n", ch);
                  break;
                  }
               //      return;                 /* the stndrd (see below) procedure does not apply here */
            break;
         case 6:
            if(!*string)
               {
               send_to_char("You must supply a field name.\n", ch);
               free((char *) writing);
               return;
               }

            /* try to locate field */
            for(ed = obj->ex_description;; ed = ed->next)
               if(!ed)
                  {
                  send_to_char("No field with that keyword.\n", ch);
                  free((char *) writing);
                  return;
                  }
               else if(!str_cmp(ed->keyword, string))
                  {
                  free_string(ed->keyword);
                  if(ed->description)
                     free_string(ed->description);
                  /* delete the entry in the desr list */
                  if(ed == obj->ex_description)
                     obj->ex_description = ed->next;
                  else
                     {
                     for(tmp = obj->ex_description; tmp->next != ed; tmp = tmp->next);
                     tmp->next = ed->next;
                     }
                  free((char *) ed);
                  free((char *) writing);
                  ed = NULL;
                  send_to_char("Field deleted.\n", ch);
                  return;
                  }
            break;

         default:
            send_to_char("That field is undefined for objects.\n", ch);
            free((char *) writing);
            return;
            break;
         }
      }

   writing->max_length = length[field - 1];
   ch->only.pc->writing = writing;

   AddEvent(EVENT_STRINGING, MAX_EVENT_TIME, FALSE, ch, writing);

   if(*string)
      {         /* there was a string in the argument array */

      if((strlen(string) > length[field - 1]) && (GET_LEVEL(ch) < 54))
         {
         send_to_char("String too long - truncated.\n", ch);
         *(string + length[field - 1]) = '\0';
         }
      writing->text = str_dup(string);
      ApplyWriting(writing);
      send_to_char("Ok.\n", ch);
      }
   else
      {                      /* there was no string. enter string mode */
      send_to_char("Enter new string.  Terminate with '@@'.\n", ch);
      }
}

#if 0                           /* not up to date, so being excluded til we need it. -JAB */

/* **********************************************************************
   *  Modification of character skills                                     *
   ********************************************************************** */

void
do_setskill(P_char ch, char *arg, int cmd)
{
   P_char vict;
   char name[MAX_INPUT_LENGTH], num[MAX_INPUT_LENGTH];
   char buf[MAX_INPUT_LENGTH], buf2[MAX_STRING_LENGTH];
   int skill, field, value, i;

   static const char *skills[] =
   {
      "search", "frighten", "telepath", "detect-evil",
      "sense-life", "cure", "bless", "remove",
      "poison", "blind", "neutralize", "purify",
      "hide", "cover", "backstab", "detect-invisible",
      "detect-magic", "enchant", "teleport", "create",
      "sanctuary", "resist", "drain", "turn",
      "protect", "light", "charm", "floating",
      "lightning-bolt", "sleep", "wake", "paralysis",
      "recharge", "shield", "fireball", "knock",
      "ventriquolism", "double", "invisible", "death-ray",
      "bash", "dodge", "kick", "uppercut",
      "defend", "dirk", "listen", "missile", "detect", "\n"
   };

   if(IS_NPC(ch))
      {
      return;
      }
   send_to_char("You cannot use setskill to set spells learned by player\n", ch);
   send_to_char("If you want to set player's attributes.\n", ch);
   send_to_char("Please use the setattr command\n", ch);

   return;

   /* Original code below */

   arg = one_argument(arg, name);

   if(!*name)
      {                 /* no arguments. print an informative text */
      send_to_char("Syntax:\nsetskill <name> <skill> <field> <value>\n", ch);
      strcpy(buf2, "Skill being one of the following:\n\n");

      for(i = 1; *skills[i] != '\n'; i++)
         {
         sprintf(buf2 + strlen(buf2), "%18s", skills[i]);

         if(!(i % 4))
            {
            strcat(buf2, "\n");
            send_to_char(buf2, ch);
            *buf2 = '\0';
            }
         }

      if(*buf2)
         send_to_char(buf2, ch);
      return;
      }
   if(!(vict = get_char_in_game_vis(ch, name, FALSE)))
      {
      send_to_char("No living thing by that name.\n", ch);
      return;
      }
   if(GET_LEVEL(vict) > GET_LEVEL(ch))
      {
      send_to_char("You cannot set skills of your peer or superior\n", ch);
      return;
      }
   arg = one_argument(arg, buf);

   if(!*buf)
      {
      send_to_char("Skill name expected.\n", ch);
      return;
      }
   if((skill = old_search_block(buf, 0, strlen(buf), skills, 1)) < 0)
      {
      send_to_char("No such skill is known. Try 'setskill' for list.\n", ch);
      return;
      }
   argument_interpreter(arg, buf, num);
   if(!*num || !*buf)
      {
      send_to_char("Field name or value undefined.\n", ch);
      return;
      }
   if((field = old_search_block(buf, 0, strlen(buf), skill_fields, 0)) < 0)
      {
      send_to_char("Unrecognized field.\n", ch);
      return;
      }
   value = atoi(num);

   if(field == 3)
      {
      if(value < -1)
         {
         send_to_char("Minimum value for that is -1.\n", ch);
         return;
         }
      }
   else if(value < 0)
      {
      send_to_char("Minimum value for that is 0.\n", ch);
      return;
      }
   if(value > max_value[field - 1])
      {
      sprintf(buf, "Max value for that is %d.\n", max_value[field - 1]);
      send_to_char(buf, ch);
      return;
      }
   switch(field)
      {
      case 1:
         if(IS_PC(vict))
            vict->only.pc->skills[skill].learned = value;
         else
            send_to_char("Mobs don't have skils.\n", ch);
         break;
         /* case 2: vict->only.pc->skills[skill].affected_by = value; break; */
         /* case 3: vict->only.pc->skills[skill].duration = value; break;    */
         /* case 4: vict->only.pc->skills[skill].recognise = value; break; */
      }

   send_to_char("Ok.\n", ch);
}

#endif

/* db stuff *********************************************** */

/* One_Word is like one_argument, execpt that words in quotes "" are */
/* regarded as ONE word                                              */

char *one_word(char *argument, char *first_arg)
{
   int found, begin, look_at;

   found = begin = 0;

   do
      {
      for(; isspace(*(argument + begin)); begin++);

      if(*(argument + begin) == '\"')
         {  /* is it a quote */
         begin++;

         for(look_at = 0; (*(argument + begin + look_at) >= ' ') &&
            (*(argument + begin + look_at) != '\"'); look_at++)
            *(first_arg + look_at) = LOWER(*(argument + begin + look_at));
         if(*(argument + begin + look_at) == '\"')
            begin++;
         }
      else
         {
         for(look_at = 0; *(argument + begin + look_at) > ' '; look_at++)
            *(first_arg + look_at) = LOWER(*(argument + begin + look_at));
         }
      *(first_arg + look_at) = '\0';
      begin += look_at;
      }
   while(fill_word(first_arg));

   return(argument + begin);
}

struct help_index_element *build_help_index(FILE * fl, int *num)
   {
   int nr = -1, issorted, i, j;
   struct help_index_element *list = 0, mem;
   char buf[82], tmp[82], *scan;
   int pos;

   for(;;)
      {
      pos = ftell(fl);
      fgets(buf, 81, fl);
      *(buf + strlen(buf) - 1) = '\0';
      scan = buf;
      for(;;)
         {
         /* extract the keywords */
         scan = one_word(scan, tmp);

         if(!*tmp)
            break;
         if(!list)
            {
            CREATE(list, struct help_index_element, 1);

            nr = 0;
            }
         else
            RECREATE(list, struct help_index_element, ++nr + 1);

         list[nr].pos = pos;
         CREATE(list[nr].keyword, char, strlen(tmp) + 1);

         strcpy(list[nr].keyword, tmp);
         }
      /* skip the text */
      do
         fgets(buf, 81, fl);
      while(*buf != '#');
      if(*(buf + 1) == '~')
         break;
      }
   /* we might as well sort the stuff */
   do
      {
      issorted = 1;
      for(i = 0; i < nr; i++)
         if(str_cmp(list[i].keyword, list[i + 1].keyword) == 1)
            {
            mem = list[i];
            list[i] = list[i + 1];
            list[i + 1] = mem;
            issorted = 0;
            }
      }
   while(!issorted);

   /* ok, new step, once list is sorted, scan down it, and make note of the list location of the first element that
      starts with each letter.  Save this in a global array.  Then do_help will reference that array, and use the
      indicated element for the start of a linear search.  Doing it this way so that partial matches will always
      match the first element they fit, not the one it happens to land on (as previous binary method did, all too
      often).  JAB */

   /* init the lookup array */
   for(i = 0; i < 27; i++)
      {
      help_array[i][0] = -1;
      help_array[i][1] = nr + 1;
      }

   /* ok, help_array works like this:
      0 - non-alpha entries
      1 - [Aa]
      .
      .
      26 - [Zz]
      Help is case_insensitive, and things are lowercased as SOP, so basically all non-alpha entries will sort out
      lower in the list[] than the alpha entries (shouldn't be many non-alpha anyway). */

   /* help_array[0] will either be 0 or -1, always, depending on whether or not there are any non-alpha entries, so
      set that up outside the loop, if no entires start with a given letter, then it's entry will be -1, and do_help
      will automatically report 'no help avail'. */

   if(LOWER(list[0].keyword[0]) < 'a')
      help_array[0][0] = 0;

   i = 0;
   j = 0;
   while(LOWER(list[i].keyword[0]) < 'a')
      i++;

   for(; i <= nr; i++)
      {
      if(LOWER(list[i].keyword[0]) == ('a' + j - 1))
         continue;
      if(LOWER(list[i].keyword[0]) > 'z')
         break;                    /* not gonna deal with non-alpha above 'z', meaningless */

      help_array[(int) j][1] = (int) i;   /* end of previous entry */

      j = (LOWER(list[i].keyword[0]) - 'a' + 1);

      help_array[(int) j][0] = (int) i;   /* start of new entry */
      }

   *num = nr;
   return(list);
   }

// Straight copy of build_help_index, for wizhelp files -- CRM

struct whelp_index_element *build_whelp_index(FILE * fl, int *num)
   {
   int nr = -1, issorted, i, j;
   struct whelp_index_element *list = 0, mem;
   char buf[82], tmp[82], *scan;
   int pos;

   for(;;)
      {
      pos = ftell(fl);
      fgets(buf, 81, fl);
      *(buf + strlen(buf) - 1) = '\0';
      scan = buf;
      for(;;)
         {
         /* extract the keywords */
         scan = one_word(scan, tmp);

         if(!*tmp)
            break;
         if(!list)
            {
            CREATE(list, struct whelp_index_element, 1);

            nr = 0;
            }
         else
            RECREATE(list, struct whelp_index_element, ++nr + 1);

         list[nr].pos = pos;
         CREATE(list[nr].keyword, char, strlen(tmp) + 1);

         strcpy(list[nr].keyword, tmp);
         }
      /* skip the text */
      do
         fgets(buf, 81, fl);
      while(*buf != '#');
      if(*(buf + 1) == '~')
         break;
      }
   /* we might as well sort the stuff */
   do
      {
      issorted = 1;
      for(i = 0; i < nr; i++)
         if(str_cmp(list[i].keyword, list[i + 1].keyword) == 1)
            {
            mem = list[i];
            list[i] = list[i + 1];
            list[i + 1] = mem;
            issorted = 0;
            }
      }
   while(!issorted);

   /* ok, new step, once list is sorted, scan down it, and make note of the list location of the first element that
      starts with each letter.  Save this in a global array.  Then do_help will reference that array, and use the
      indicated element for the start of a linear search.  Doing it this way so that partial matches will always
      match the first element they fit, not the one it happens to land on (as previous binary method did, all too
      often).  JAB */

   /* init the lookup array */
   for(i = 0; i < 27; i++)
      {
      whelp_array[i][0] = -1;
      whelp_array[i][1] = nr + 1;
      }

   /* ok, help_array works like this:
      0 - non-alpha entries
      1 - [Aa]
      .
      .
      26 - [Zz]
      Help is case_insensitive, and things are lowercased as SOP, so basically all non-alpha entries will sort out
      lower in the list[] than the alpha entries (shouldn't be many non-alpha anyway). */

   /* help_array[0] will either be 0 or -1, always, depending on whether or not there are any non-alpha entries, so
      set that up outside the loop, if no entires start with a given letter, then it's entry will be -1, and do_help
      will automatically report 'no help avail'. */

   if(LOWER(list[0].keyword[0]) < 'a')
      whelp_array[0][0] = 0;

   i = 0;
   j = 0;
   while(LOWER(list[i].keyword[0]) < 'a')
      i++;

   for(; i <= nr; i++)
      {
      if(LOWER(list[i].keyword[0]) == ('a' + j - 1))
         continue;
      if(LOWER(list[i].keyword[0]) > 'z')
         break;                    /* not gonna deal with non-alpha above 'z', meaningless */

      whelp_array[(int) j][1] = (int) i;  /* end of previous entry */

      j = (LOWER(list[i].keyword[0]) - 'a' + 1);

      whelp_array[(int) j][0] = (int) i;  /* start of new entry */
      }

   *num = nr;
   return(list);
   }

/* Shamelessly ripped off from help.  Although it would probably make sense to just reuse all the help stuff, it
 * also seems like a good idea to keep them separate, in case of later changes to file format.
 * DTS 2/5/95
 */
struct info_index_element *build_info_index(FILE * fl, int *num)
   {
   int nr = -1, issorted, i, j;
   struct info_index_element *list = 0, mem;
   char buf[82], tmp[82], *scan;
   int pos;

   for(;;)
      {
      pos = ftell(fl);
      fgets(buf, 81, fl);
      *(buf + strlen(buf) - 1) = '\0';
      scan = buf;
      for(;;)
         {
         /* extract the keywords */
         scan = one_word(scan, tmp);

         if(!*tmp)
            break;
         if(!list)
            {
            CREATE(list, struct info_index_element, 1);

            nr = 0;
            }
         else
            RECREATE(list, struct info_index_element, ++nr + 1);

         list[nr].pos = pos;
         CREATE(list[nr].keyword, char, strlen(tmp) + 1);

         strcpy(list[nr].keyword, tmp);
         }
      /* skip the text */
      do
         fgets(buf, 81, fl);
      while(*buf != '#');
      if(*(buf + 1) == '~')
         break;
      }
   /* we might as well sort the stuff */
   do
      {
      issorted = 1;
      for(i = 0; i < nr; i++)
         if(str_cmp(list[i].keyword, list[i + 1].keyword) > 0)
            {
            mem = list[i];
            list[i] = list[i + 1];
            list[i + 1] = mem;
            issorted = 0;
            }
      }
   while(!issorted);

   /* ok, new step, once list is sorted, scan down it, and make note of the list location of the first element that
      starts with each letter.  Save this in a global array.  Then do_help will reference that array, and use the
      indicated element for the start of a linear search.  Doing it this way so that partial matches will always
      match the first element they fit, not the one it happens to land on (as previous binary method did, all too
      often).  JAB */

   /* init the lookup array */
   for(i = 0; i < 27; i++)
      {
      info_array[i][0] = -1;
      info_array[i][1] = nr + 1;
      }

   /* ok, info_array works like this:
      0 - non-alpha entries
      1 - [Aa]
      .
      .
      26 - [Zz]
      Info is case_insensitive, and things are lowercased as SOP, so basically all non-lapha entries will sort out
      lower in the list[] than the alpha entries (shouldn't be many non-alpha anyway). */

   /* info_array[0] will either be 0 or -1, always, depending on whether or not there are any non-alpha entries, so
      set that up outside the loop, if no entires start with a given letter, then it's entry will be -1, and do_info
      will automatically report 'no info avail'. */

   if(LOWER(list[0].keyword[0]) < 'a')
      info_array[0][0] = 0;

   i = 0;
   j = 0;
   while(LOWER(list[i].keyword[0]) < 'a')
      i++;

   for(; i <= nr; i++)
      {
      if(LOWER(list[i].keyword[0]) == ('a' + j - 1))
         continue;
      if(LOWER(list[i].keyword[0]) > 'z')
         break;                    /* not gonna deal with non-alpha above 'z', meaningless */

      info_array[(int) j][1] = (int) i;   /* end of previous entry */

      j = (LOWER(list[i].keyword[0]) - 'a' + 1);

      info_array[(int) j][0] = (int) i;   /* start of new entry */
      }

   *num = nr;
   return(list);
   }

void page_string(P_desc d, char *str, int keep_internal)
{
   if(!d)
      return;

   if(!str)
      {
      wizlog(51, "page_string: NULL str for [%s]! Go bug Shev to fix it!", GET_NAME(d->character));
      return;
      }

   if(!IS_CSET(d->character->only.pc->pcact, PLR_PAGING_ON) || (d->connected == CON_SLCT))
      {
      /* added CON_SLCT to disable paging when doing 'who' from menu. -JAB */
      SEND_TO_Q(str, d);
      return;
      }

   if(keep_internal)
      {
      d->showstr_head = str_dup(str);
      d->showstr_point = d->showstr_head;
      }
   else
      d->showstr_point = str;

   show_string(d, 0);
}

void show_string(P_desc d, char *input)
{
   char buffer[MAX_STRING_LENGTH], buf[MAX_INPUT_LENGTH];
   register char *scan, *chk;
   int lines = 0, s_lines = 22;

   one_argument(input, buf);

   if(*buf)
      {
      if(d->showstr_head)
         {
         free(d->showstr_head);
         d->showstr_head = 0;
         }
      d->showstr_point = 0;
      return;
      }

   if(d->character && IS_PC(d->character))
      s_lines = MAX(2, d->character->only.pc->screen_length - 2);

   /* show a chunk */
   for(scan = buffer; ; scan++, d->showstr_point++)
      {
      if((*scan = *d->showstr_point) == '\n')
         lines++;

      if(!*scan || (lines >= s_lines))
         {
         *(scan + 1) = '\0';
         SEND_TO_Q(buffer, d);

         /* see if this is the end (or near the end) of the string */
         for(chk = d->showstr_point; isspace(*chk); chk++);
         d->showstr_point++;
         if(!*chk)
            {
            if(d->showstr_head)
               {
               free(d->showstr_head);
               d->showstr_head = 0;
               }
            d->showstr_point = 0;
            }
         return;
         }
      }
}

void night_watchman(void)
{
   int tc;
   struct tm *t_info;
   char buf[20];

   tc = time(0);
   t_info = localtime((long *)&tc);
   strcpy(buf, "maxplayers");

   if((t_info->tm_hour >= 9) && (t_info->tm_hour <= 17) && (t_info->tm_wday != 6) && (t_info->tm_wday != 0))
      {
      if(!(game_locked & LOCK_MAX_PLAYERS))
         do_wizlock(0, buf, -4);
      }
   else if(game_locked & LOCK_MAX_PLAYERS)
      do_wizlock(0, buf, -4);
}

void check_reboot(void)
{
   time_t tc;
   struct tm *t_info;
   char dummy;
   FILE *boot;

   tc = time(0);
   t_info = localtime(&tc);

   if(t_info && (t_info->tm_hour + 1) == REBOOT_AT && t_info->tm_min > 30)
      if((boot = fopen("./reboot", "r")))
         {
         if(t_info->tm_min > 50)
            {
            logit(LOG_STATUS, "Reboot exists.");
            fread(&dummy, sizeof(dummy), 1, boot);
            if(!feof(boot))
               {      /* the file is nonepty */
               logit(LOG_STATUS, "Reboot is nonempty.");
               if(system("./reboot"))
                  {
                  logit(LOG_STATUS, "Reboot script terminated abnormally");
                  send_to_all("The reboot was cancelled.\n");
                  system("mv ./reboot reboot.FAILED");
                  fclose(boot);
                  return;
                  }
               else
                  system("mv ./reboot reboot.SUCCEEDED");
               }
            send_to_all("Automatic reboot. Come back in a little while.\n");
            shutdownflag = reboot = 1;
            }
         else if(t_info->tm_min > 40)
            send_to_all("ATTENTION: DikuMUD will reboot in 10 minutes.\n");
         else if(t_info->tm_min > 30)
            send_to_all("Warning: The game will close and reboot in 20 minutes.\n");

         fclose(boot);
         }
}

/* this is the function that actually puts the text that has been entered into a writing_info struct, where it
 * belongs.  The only tricky part is, we have to check all our information to be certain that text_start is still
 * a valid address.  JAB */

void ApplyWriting(struct writing_info *writing)
{
   P_char ch = NULL;
   P_event e1 = NULL, e2 = NULL;
   P_obj obj = NULL;
#ifdef KINGDOM
   struct house_upgrade_rec *con_rec;
#endif
   P_assoc assoc = NULL;

   if(!writing)
      dump_core();

   ch = writing->writer;
   if(ch && IS_PC(ch) && ch->only.pc)
      {
      ch->only.pc->writing = NULL;
      if((writing->what == WRT_STRING_CH)
         && (ch == writing->targ_ch)
         && ch->desc
         && (STATE(ch->desc) == CON_EXDSCR))
         {
         SEND_TO_Q("Description changed.  you must enter the game to make this change permanent.\n", ch->desc);
         STATE(ch->desc) = CON_SLCT;
         SEND_TO_Q(MENU, ch->desc);
         }
      }

   switch(writing->what)
      {
      case WRT_BOARD:
         if((writing->board_idx < 0)
            || (writing->board_idx >= BOARD_COUNT)
            || (writing->msg_nr < 0)
            || (writing->msg_nr >= MAX_MSGS)
            || (Boards[writing->board_idx].msg[writing->msg_nr].body != NULL))
            dump_core();

         if(!writing->text)
            writing->text = str_dup("");

         Boards[writing->board_idx].msg[writing->msg_nr].body = writing->text;
         Boards[writing->board_idx].msg[writing->msg_nr].author = NULL;
         writing->text = NULL;
         board_save_board(writing->board_idx);

         free((char *)writing);
         return;

      case WRT_STRING_OBJ:
         obj = writing->targ_obj;
         if(!obj || !writing->event || !writing->str_mask)
            dump_core();

         /* clear the old stuff out first */
         if(*writing->text_start && IS_SET(obj->str_mask, writing->str_mask))
            free_string(*writing->text_start);
         *writing->text_start = NULL;

         SET_BIT(obj->str_mask, writing->str_mask);
         *writing->text_start = writing->text;
         writing->text = NULL;

         e2 = obj->events;
         break;

      case WRT_STRING_CH:
         ch = writing->targ_ch;
         if(!ch || !writing->event || (IS_NPC(ch) && !writing->str_mask))
            dump_core();

         /* clear the old stuff out first */
         if(*writing->text_start && (IS_PC(ch) || IS_SET(ch->only.npc->str_mask, writing->str_mask)))
            free_string(*writing->text_start);
         *writing->text_start = NULL;

         if(IS_NPC(ch))
            SET_BIT(ch->only.npc->str_mask, writing->str_mask);
         *writing->text_start = writing->text;
         writing->text = NULL;

         e2 = ch->events;
         break;

#ifdef KINGDOM
      case WRT_HOUSE_DESCRIPTION:
         ch = writing->targ_ch;
         if(!ch || (IS_NPC(ch) && !writing->str_mask) || !writing->targ_house)
            dump_core();

         if(!charge_char(ch, upgrade_costs[HCONTROL_DESC_ROOM]))
            return;

         con_rec = get_con_rec();
         //    con_rec->door_keyword = strdup("\n");
         //    *con_rec->door_keyword = 0;
         //    ch->desc->str = &(writing->text);
         con_rec->door_keyword = writing->text;
         con_rec->vnum = writing->targ_house->room_vnums[0];
         con_rec->time = time(0);
         con_rec->type = HCONTROL_DESC_ROOM;
         con_rec->location = world[ch->in_room].number;
         con_rec->next = house_upgrade_list;
         house_upgrade_list = con_rec;

         writeConstructionQ();
         send_to_char("Room description has begun.\r\n", ch);
         return;
#endif

      case WRT_ASSOC_DESC:
         ch = writing->writer;
         if(!ch || !GET_ASC_NUM(ch))
            dump_core();

         assoc = find_assoc_num(GET_ASC_NUM(ch));

         if(!assoc)
            dump_core();

         assoc->description = writing->text;
         writeAssoc(assoc);


         free((char *)writing);
         return;
         break;

      case WRT_ASSOC_MOTD:
         ch = writing->writer;
         if(!ch || !GET_ASC_NUM(ch))
            dump_core();

         assoc = find_assoc_num(GET_ASC_NUM(ch));

         if(!assoc)
            dump_core();

         assoc->message = writing->text;
         writeAssoc(assoc);


         free((char *)writing);
         return;
         break;

      default:
         dump_core();
      }

   LOOP_EVENTS(e1, e2)
   if(e1->type == EVENT_STRINGING)
      break;
   if(!e1)
      dump_core();

   if(current_event != e1)
      {
      e2 = current_event;
      current_event = e1;
      RemoveEvent();
      current_event = e2;
      }
   else
      RemoveEvent();

   free((char *)writing);
}


void do_rename(P_char ch, char *arg, int cmd)
{
   char name[MAX_STRING_LENGTH], new_name[MAX_STRING_LENGTH];
   char buf[256], *buff, Gbuf1[MAX_STRING_LENGTH];
   FILE *f;
   P_char doofus, finger_foo = NULL;

   finger_foo = GetNewChar(NEW_PC);
   finger_foo->only.pc->aggressive = -1;

   arg = one_argument(arg, name);
   if(!*name || !name)
      {
      send_to_char("rename <oldname> <newname>.\r\n", ch);
      if(finger_foo)
         {
         RemoveFromCharList (finger_foo);
         free_char(finger_foo);
         }

      return;
      }

   doofus = get_char_vis(ch, name, TRUE);
   if(!doofus)
      {
      send_to_char("I don't know anyone by that name...\r\n", ch);
      if(finger_foo)
         {
         RemoveFromCharList (finger_foo);
         free_char(finger_foo);
         }

      return;
      }

   if(IS_NPC(doofus))
      {
      send_to_char("You can't rename an NPC.\r\n", ch);
      if(finger_foo)
         {
         RemoveFromCharList (finger_foo);
         free_char(finger_foo);
         }

      return;
      }

   /* check existence of oldname */
   if(restoreCharOnly(finger_foo, name) >= 0 && finger_foo)
      {
      if(GET_LEVEL(finger_foo) > GET_LEVEL(ch))
         {
         send_to_char("You can't rename someone a god's name over your level.\r\n", ch);
         if(finger_foo)
            free_char(finger_foo);

         return;
         }
      }

   arg = one_argument(arg, new_name);
   if(!*new_name || !new_name)
      {
      send_to_char("rename <oldname> <newname>.\r\n", ch);
      if(finger_foo)
         {
         RemoveFromCharList (finger_foo);
         free_char(finger_foo);
         }

      return;
      }

   /* New name must not be a valid player */
   if(restoreCharOnly(finger_foo, new_name) < 0 || !finger_foo)
      {
      deny_name(GET_NAME(doofus));
      deleteCharacter(doofus);
      CAP(new_name);
      GET_NAME(doofus) = strdup(new_name);

      /* be sure new name isn't in declined list */
      strcpy(buf, new_name);
      buff = buf;

      for(; *buff; buff++)
         *buff = LOWER(*buff);

      sprintf(Gbuf1, "Players/Declined/%c/%s", buf[0], buf);

      f = fopen(Gbuf1, "w");
      if(f)
         {
         fclose(f);
         unlink(Gbuf1);
         send_to_char("Name was in the declined list, but has been removed.\r\n", ch);
         }
      else
         {
         logit(LOG_FILE, "Cannot create a decline file in do_rename() errno: %d", errno);
         wizlog(GET_LEVEL(ch), "PANIC! Cannot create a decline file in do_rename() errno: %d", errno);
         RemoveFromCharList (finger_foo);
         free_char(finger_foo);

         return;
         }

      wizlog(GET_LEVEL(ch), "%s renamed %s.  New name: %s", GET_NAME(ch), name, new_name);
      logit(LOG_FILE, "%s renamed %s.  New name: %s", GET_NAME(ch), name, new_name);
      send_to_char("Name changed, old one deleted. Good job!\r\n", ch);
      deny_name(name);
      if(writeCharacter(doofus, 1, doofus->in_room))
         {
         auction_rename(name, new_name);
         send_to_char("Autosaved!\r\n", ch);
         }
      }
   else
      send_to_char("Can't use that name, as it belongs to another.\r\n", ch);

   if(finger_foo)
      {
      RemoveFromCharList (finger_foo);
      free_char(finger_foo);
      }
}

