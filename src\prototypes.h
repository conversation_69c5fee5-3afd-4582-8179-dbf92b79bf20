/* ***************************************************************************
 *  File: prototypes.h                                       Part of Outcast *
 *  Usage: Prototypes for all functions.                                     *
 *  Copyright  1998 - Outcast Systems Ltd.                                   *
 *************************************************************************** */

#include <stdio.h>

#ifndef _SOJ_PROTOTYPES_H_
#define _SOJ_PROTOTYPES_H_

#ifndef _SOJ_CONFIG_H_
#include "config.h"
#endif

#ifndef _SOJ_STRUCTS_H_
#include "structs.h"
#endif

#ifndef _SOJ_TRADE_H_
#include "trade.h"
#endif

/* global strings, these should only be used sparingly, by functions that
   build and return a string pointer.  I'm adding first to support where_obj(),
   add others if you need to, and you need more than one.  JAB */

extern char GS_buf1[MAX_STRING_LENGTH];

/* keeps compiler quiet */

extern char *crypt(const char *, const char *);
extern int chdir(const char *);
extern int close(int);
extern int getdtablesize(void);
extern int gethostname(char *, size_t);
extern int unlink(const char *);
extern pid_t fork(void);
extern pid_t getppid(void);
#ifdef GLIBC6
extern size_t read(int, char *, size_t);
extern size_t write(int, const char *, size_t);
#endif //GLIBC6
extern unsigned int alarm(unsigned int);
extern unsigned int sleep(unsigned int);

/* actcomm.c */

#ifdef OVL
void update_ovl(void);
#endif //OVL
bool can_communicate(P_char);
bool can_talk(P_char);
bool can_project(P_char);
bool is_overload(P_char);
bool is_silent(P_char, int);
char *fread_action(FILE *);
int action(int);
int find_action(int);
void boot_social_messages(void);
void check_magic_doors(P_char, char *);
void do_action(P_char, char *, int);
void do_ask(P_char, char *, int);
void do_project_ask(P_char, char *, int);
void do_channel(P_char, char *, int);
void do_gossip(P_char, char *, int);
void do_gsay(P_char, char *, int);
void do_insult(P_char, char *, int);
void do_ooc(P_char, char *, int);
void do_acheron_channel(P_char, char *, int);
void do_qwiz(P_char, char *, int);
void do_ncc(P_char, char *, int);
void do_acc(P_char, char *, int);
void do_order(P_char, char *, int);
void do_page(P_char, char *, int);
void do_petition(P_char, char *, int);
void do_pose(P_char, char *, int);
void do_say(P_char, char *, int);
void do_project(P_char, char *, int);
void do_shout(P_char, char *, int);
void do_tell(P_char, char *, int);
void do_reply(P_char, char *, int);
void do_whisper(P_char, char *, int);
void do_write(P_char, char *, int);
void do_yell(P_char, char *, int);
void do_mass_project(P_char, char *, int);
void mobsay(P_char, const char *);
void send_to_gods(char *);
bool can_sign(P_char);
void do_sign(P_char, char *, int);
void acheron_channel_broadcast(P_char, char *);
void force_attach_order(P_char ch, char *argument);
void attach_order(P_char ch, char *argument);

/* actgroup.c */

void do_group(P_char, char *, int);
void do_disband(P_char, char *, int);
void do_gget(P_char, char *, int);
void do_gadd(P_char, char *, int);
void do_gdel(P_char, char *, int);
void do_ggive(P_char, char *, int);
void do_ungroup(P_char, char *, int);
void do_gsplit(P_char, char *, int);
void do_gtell(P_char, char *, int);
void do_glist(P_char, char *, int);
void do_gtoggle(P_char, char *, int);
void do_gduel(P_char, char *, int);
void do_greset(P_char, char *, int);
void do_gcombat(P_char, char *, int);
void do_greport(P_char, char *, int);
void do_appoint(P_char, char *, int);

/* actinf.c */

P_obj get_object_in_equip(P_char, char *, int *);
P_obj get_object_in_equip_vis(P_char, char *, int *);
char *find_ex_description(char *, struct extra_descr_data *);
char *show_obj_to_char(P_obj, P_char, int, int);
const char *ac_to_string(int);
const char *align_to_string(int);
const char *class_to_string(P_char);
const char *exp_to_string(P_char);
const char *hitdam_roll_to_string(int);
const char *load_to_string(P_char);
const char *race_to_string(P_char);
const char *save_to_string(int);
const char *stat_to_string1(int);
const char *stat_to_string2(int);
const char *stat_to_string3(int);
int DISPLAY_STAT_INDEX(int);
void ShowCharSpellBookSpells(P_char, P_obj);
void argument_split_2(char *, char *, char *);
void do_attributes(P_char, char *, int);
void do_changelog(P_char, char *, int);
void do_commands(P_char, char *, int);
void do_consider(P_char, char *, int);
void do_credits(P_char, char *, int);
void do_display(P_char, char *, int);
void do_do(P_char, char *, int);
void do_equipment(P_char, char *, int);
void do_examine(P_char, char *, int);
void do_exits(P_char, char *, int);
void do_faq(P_char, char *, int);
void do_genlog(P_char, char *, int);
void do_glance(P_char, char *, int);
void do_help(P_char, char *, int);
void do_whelp(P_char, char *, int);
void do_ignore(P_char, char *, int);
void do_info(P_char, char *, int);
void do_inventory(P_char, char *, int);
void do_levels(P_char, char *, int);
void do_look(P_char, char *, int);
void do_motd(P_char, char *, int);
void do_news(P_char, char *, int);
void do_read(P_char, char *, int);
void do_report(P_char, char *, int);
void do_rules(P_char, char *, int);
void do_scan(P_char, char *, int);
void do_score(P_char, char *, int);
void do_skills(P_char, char *, int);
void do_spells(P_char, char *, int);
void do_multi_list(P_char ch, int);
void do_time(P_char, char *, int);
void do_title(P_char, char *arg, int);
void do_trophy(P_char, char *, int);
void do_command_stub(P_char, char *, int);
void do_users(P_char, char *, int);
void do_weather(P_char, char *, int);
void do_where(P_char, char *, int);
void do_who(P_char, char *, int);
void do_eqrate(P_char, char *, int);
void do_wizhelp(P_char, char *, int);
void do_wizlist(P_char, char *, int);
void do_world(P_char, char *, int);
void list_char_to_char(P_char, int, int, int);
void list_obj_to_char(P_obj, P_char, int, int);
void new_look(P_char, char *, int, int);
void show_char_to_char(P_char, P_char, int);
void show_exits_to_char(P_char, int, int);
int is_condensed(P_char, uint, int);
void set_condensed_melee(P_char, int, int, int, int);
void set_condensed_spell(P_char, int, int, int, int);
int process_char_viewing_char(P_char, P_char);
int process_char_viewing_group(P_char, P_char, int);
void show_group_to_char(P_group, P_char, int);
void do_ipshare(P_char, char *arg, int);
void boot_ipshare(void);
void shutdown_ipshare(void);
void do_stop_pet_follow(P_char, char *, int);
void do_stop_pc_follow(P_char, char *, int);

/* actmove.c */

P_obj has_key(P_char, int);
int can_enter_room(P_char, int, int);
int do_simple_move(P_char, int, int);
int find_door(P_char, char *, char *);
int leave_by_exit(P_char, int);
int load_modifier(P_char);
void SwapCharsInList(P_char, P_char);
void do_alert(P_char, char *, int);
void do_close(P_char, char *, int);
void do_drag(P_char, char *, int);
void new_do_drag(P_char, char *, int);
void do_enter(P_char, char *, int);
void do_climb(P_char ch, char *argument, int cmd);
void do_follow(P_char, char *, int);
void do_kneel(P_char, char *, int);
void do_lock(P_char, char *, int);
void do_move(P_char, char *, int);
void do_open(P_char, char *, int);
void do_pick(P_char, char *, int);
void do_recline(P_char, char *, int);
void do_rest(P_char, char *, int);
void do_sit(P_char, char *, int);
void do_sleep(P_char, char *, int);
void do_stand(P_char, char *, int);
void do_unlock(P_char, char *, int);
void do_wake(P_char, char *, int);

/* actobj.c */

P_obj merge_objects(P_obj, P_obj);
P_obj split_object(P_obj, int);
bool put(P_char, P_obj, P_obj, int);
int objs_mergeable(P_obj, P_obj);
int try_wear(P_char, P_obj);
int wear(P_char, P_obj, int, int);
void add_loading(P_obj);
void do_drink(P_char, char *, int);
void perform_drop(P_char, P_obj);
void do_drop(P_char, char *, int);
int do_eat(P_char, char *, int);
void do_fill(P_char, char *, int);
void do_get(P_char, char *, int);
int do_give(P_char, char *, int);
void do_grab(P_char, char *, int);
void do_junk(P_char, char *, int);
void do_pour(P_char, char *, int);
void do_put(P_char, char *, int);
void do_putalldot(P_char, char *, char *, int);
void do_putallinv(P_char, char *, int);
void do_remove(P_char, char *, int);
void do_search(P_char, char *, int);
void do_sip(P_char, char *, int);
void do_taste(P_char, char *, int);
void do_wear(P_char, char *, int);
void do_wield(P_char, char *, int);
int get(P_char, P_obj, P_obj, int, int);
void name_from_drinkcon(P_obj);
void name_to_drinkcon(P_obj, int);
void perform_wear(P_char, P_obj, int);
void remove_loading(P_obj);
void weight_change_object(P_obj, int);
void newGet(P_char, char *, int, int);
int newGive(P_char, char *, int, int);
int repair_shopkeeper(P_char, P_char, int, char *);
struct obj_data *Break_Object(P_char, P_obj, int);
struct obj_data *Do_Object_Decay(P_char, P_obj, int, int);
void do_empty(P_char, char *, int);

/* actoff.c */

int CanDoFightMove(P_char, P_char, int);
int CanPsiDoFightMove(P_char, P_char);
P_char ParseTarget(P_char, char *);
bool instantkill(P_char, P_char);
bool is_in_safe(P_char ch);
bool nokill(P_char, P_char);
bool should_not_kill(P_char, P_char);
bool are_duelling(P_char, P_char);
void Circle(P_char, P_char);
void Outflank(P_char, P_char);
void attack(P_char, P_char);
void backstab(P_char, P_char);
int bash(P_char, P_char);
void weapon_bash(P_obj obj, P_char, P_char);
void do_assist(P_char, char *, int);
void do_backstab(P_char, char *, int);
void do_bash(P_char, char *, int);
int do_bodyslam(P_char, char *, int);
void do_circle(P_char, char *, int);
void do_outflank(P_char, char *, int);
void do_howl(P_char, char *, int);
void do_strafe(P_char, char *, int);
void do_disarm(P_char, char *, int);
void do_disengage(P_char, char *, int);
void do_dodge(P_char, char *, int);
void do_escape(P_char, char *, int);
void do_flee(P_char, char *, int);
int do_hamstring(P_char, char *, int);
void do_hitall(P_char, char *, int);
void do_headbutt(P_char, char *, int);
void do_hit(P_char, char *, int);
int do_kick(P_char, char *, int);
void do_kill(P_char, char *, int);
void do_murde(P_char, char *, int);
void do_murder(P_char, char *, int);
void do_parry(P_char, char *, int);
void do_rescue(P_char, char *, int);
void do_retreat(P_char, char *, int);
int do_shieldpunch(P_char, char *, int);
int do_mountedCharge(P_char, char *, int);
void do_snakebite(P_char, char *, int);
int do_tailsweep(P_char, char *, int);
void flee_lose_exp(P_char, P_char);
void rescue(P_char, P_char);
int do_trip(P_char, char *, int);
void do_assassinate(P_char, char *, int);
void do_lifetap(P_char, char *, int);
void do_garrote(P_char, char *, int);
void garrote_event(P_char);

/* actunused.c */

int MonkAcBonus(P_char);
int MonkDamage(P_char);
int MonkNumberOfAttacks(P_char);
int wornweight(P_char);
void MonkSetSpecialDie(P_char);
void chant_soul_strike(P_char, char *, int);
void chant_calm(P_char, char *, int);
void chant_heroism(P_char, char *, int);
void chant_death_grip(P_char, char *, int);
void chant_regeneration(P_char, char *, int);
void do_chant(P_char, char *, int);
void do_dragon_punch(P_char, char *, int);
void do_feign_death(P_char, char *, int);
void do_self_preservation(P_char, char *, int);
void do_springleap(P_char, char *, int);
void do_berserk(P_char, char *, int);
void do_subterfuge(P_char, char *, int);
void do_trap(P_char, char *, int);

/* actnoff.c */

int handle_timed_usage(P_char, int, int);
void do_camp(P_char, char *, int);
void do_qui(P_char, char *, int);
void do_quit(P_char, char *, int);
void do_save_silent(P_char, int);
void do_save(P_char, char *, int);
void do_not_here(P_char, char *, int);
void do_no_buy(P_char, char *, int);
int test_atm_present(P_char);
void do_balance(P_char, char *, int);
void do_deposit(P_char, char *, int);
void do_withdraw(P_char, char *, int);
void do_sneak(P_char, char *, int);
void do_hide(P_char, char *, int);
void do_listen(P_char, char *, int);
void do_charm_reptile(P_char, char *, int);
int charm_reptile_single(P_char, P_char, int);
void do_scaleskin(P_char);
void do_vipermind(P_char);
void do_forage(P_char, char *, int);
bool do_tame_mount(int, P_char, P_char);
void do_woodcarving(int, P_char, char *);
void racial_strength(P_char);
void do_interference_shield(P_char);
void do_inn_danger_sense(P_char);
void do_innate(P_char, char *, int);
void do_doorbash(P_char, char *, int);
void halfling_stealaction(P_char, char *, int);
void do_steal(P_char, char *, int);
void do_practice(P_char, char *, int);
void do_explist(P_char);
void do_idea(P_char, char *, int);
void do_typo(P_char, char *, int);
void do_bug(P_char, char *, int);
void do_brief(P_char, char *, int);
void do_anonymous(P_char, char *, int);
void do_compact(P_char, char *, int);
void do_quaff(P_char, char *, int);
void do_recite(P_char, char *, int);
void do_use(P_char, char *, int);
#if 0
void do_wimpy(P_char ch, char *, int);
void do_description(P_char ch, char *, int);
void do_password(P_char ch, char *, int);
#endif // 0
void show_toggles(P_char);
void do_toggle(P_char, char *, int);
void do_rub(P_char, char *, int);
void do_split(P_char, char *, int);
#if 0
void do_reboot(P_char ch, char *, int);
#endif // 0
void do_bury(P_char, char *, int);
int try_to_bury(P_char, P_obj);
void reward_for_bury(P_char, int);
void do_donate(P_char, char *, int);
void try_to_donate(P_char, P_obj);
void do_getcart(P_char, char *, int);
void do_leavecart(P_char, char *, int);
void do_disguise(P_char, char *, int);
void do_claim(P_char, char *, int);
void do_aggr(P_char, char *, int);
void do_consent(P_char, char *, int);
void do_nokill(P_char, char *, int);
void do_meditate(P_char, char *, int);
void do_apply(P_char, char *, int);
void do_layhand(P_char, char *, int);
void FreeShadowedData(P_char, P_char);
void StopShadowers(P_char);
int CountNumFollowers(P_char);
int CountNumShadowers(P_char);
void MoveShadower(P_char, int);
void do_shadow(P_char, char *, int);
int check_aware(P_char);
void do_awareness(P_char, char *, int);
void do_bandage(P_char, char *, int);
void mount_summoning_thing(P_char);
void do_summon_mount(P_char, char *, int);
void horde_summoning_thing(P_char);
void aggro_summon_horde(P_char);
void do_summon_horde(P_char, char *, int);
void do_dice(P_char, char *, int);
void do_drow_globe_of_darkness(P_char, char *, int);
void do_duel(P_char, char *, int);
#ifdef NEWJUSTICE
void do_tie_up(P_char, char *, int);
void do_untie(P_char, char *, int);
void do_unbind(P_char, char *, int);
#endif //NEWJUSTICE
void do_track(P_char, char *, int);
void track_move(P_char);
int MaxTrackDist(P_char);
void do_recall(P_char, char *, int);
void do_lore(P_char, char *, int);
void do_reorder(P_char ch, char *arg, int cmd);
P_obj reverse_obj_list(P_obj list, bool recurse);

/* actset.c */

char *setbit_parseArgument(char *, char *);
void do_setbit(P_char, char *, int);
int ac_strcasecmp(const char *, const char *);

/* actwiz.c */

void pachinko_roller(P_char ch);
char *comma_string(int);
char *where_obj(P_obj, int);
int ban_insert(struct ban_t **, struct ban_t *);
int gr_idiotproof(P_char, P_char, char *, int);
int check_switched(P_char ch);
struct obj_data *clone_obj(P_obj);
void NewbySkillSet(P_char);
void clone_container_obj(P_obj, P_obj);
void do_accept(P_char, char *, int);
void do_addprestige(P_char, char *, int);
void do_advance(P_char, char *, int);
void do_allow(P_char, char *, int);
void do_allowgroup(P_char, char *, int);
void do_at(P_char, char *, int);
void do_ban(P_char, char *, int);
void do_clone(P_char, char *, int);
void do_code(P_char, char *, int);
void do_createmob(P_char, char *, int);
void do_deaggro(P_char, char *, int);
void do_deny(P_char, char *, int);
void do_undecline(P_char, char *, int);
void do_decline(P_char, char *, int);
void do_demote(P_char, char *, int);
void do_echo(P_char, char *, int);
void do_echoa(P_char, char *, int);
void do_echot(P_char, char *, int);
void do_echoz(P_char, char *, int);
void do_emote(P_char, char *, int);
void do_finger(P_char, char *, int);
void do_force(P_char, char *, int);
void do_freeze(P_char, char *, int);
void do_function(P_char, char *, int);
void do_goto(P_char, char *, int);
void do_grant(P_char, char *, int);
void do_inroom(P_char, char *, int);
void do_knock(P_char, char *, int);
void do_lag(P_char, char *, int);
void do_law_flags(P_char, char *, int);
void do_list_witness(P_char, char *, int);
void do_load(P_char, char *, int);
void do_loadchar(P_char, char *, int);
void do_loadmob(P_char, char *, int);
void do_lookup(P_char, char *, int);
void do_lookup_bans(P_char, char *);
void do_notes(P_char, char *, int);
void do_outcast(P_char, char *, int);
void do_poofIn(P_char, char *, int);
void do_poofOut(P_char, char *, int);
void do_prep(P_char, char *, int);
void do_proc(P_char, char *, int);
void do_ptell(P_char, char *, int);
void do_purge(P_char, char *, int);
void do_reaggro(P_char, char *, int);
void do_reinitphys(P_char, char *, int);
void do_release(P_char, char *, int);
void do_reroll(P_char, char *, int);
void do_restore(P_char, char *, int);
void do_return(P_char, char *, int);
void do_revoke(P_char, char *, int);
void do_revoketitle(P_char, char *, int);
void do_rptoggle(P_char, char *, int);
void do_secret(P_char, char *, int);
void do_setattr(P_char, char *, int);
void do_sethelper(P_char, char *, int);
void do_sethome(P_char, char *, int);
void do_setpk(P_char, char *, int);
void do_shutdow(P_char, char *, int);
void do_shutdown(P_char, char *, int);
void do_silence(P_char, char *, int);
void do_snoop(P_char, char *, int);
void do_start(P_char);
void do_stat(P_char, char *, int);
void do_switch(P_char, char *, int);
void do_teleport(P_char, char *, int);
void do_trans(P_char, char *, int);
void do_vis(P_char, char *, int);
void do_which(P_char ch, char *args, int cmd);
void do_wizlock(P_char, char *, int);
void do_wizmsg(P_char, char *, int);
void do_zreset(P_char, char *, int);
void read_ban_file(void);
void roll_basic_abilities(P_char, int);
void sa_ageCopy(P_char, uint, int);
void sa_byteCopy(P_char, uint, int);
void sa_intCopy(P_char, uint, int);
void sa_shortCopy(P_char, uint, int);
void save_ban_file(void);
void statSocList(char*, int);
void statSocPath(char*, int);
void statSocPeriodic(char*, int);
void statSocReaction(char*, int);
void statSocTimed(char*, int);
void statSocErrors(char*, int);
void displayCodeControlSyntax(P_char ch);
void createCodeControlList(char *);
void pclistdebug(P_char ch, char *args);
void sqldump(P_char ch, char *args);
void showPCcash(P_char ch, char *args);

/* contracts.c */

void boot_jobs(void);
int writeJob(P_job);
void readJob(P_job, int);

P_job GetNewJob(void);
void DeleteJob(int);
void UnloadJob(int);
void LoadJob(P_job);
P_job CreateJob(int, int, int, int, int);

P_job find_job_num(int);
int sector_distance(int, int);
void create_job_desc(P_job, int);
int get_job_element(int, int, int, int, int, int);
void load_job_group(int, int);
void release_job_mem(P_job job);
void convert_mobile(P_char, int);
int intercept_contract(P_char, P_char, int, char);

void do_contract(P_char, char *, int);
void contract_request(P_char, char *, int);
void contract_accept(P_char);
void contract_quit(P_char);
void contract_collect(P_char);
void contract_info(P_char);
void contract_inquire(P_char);

void do_jcontrol(P_char, char *, int);
void contract_create(P_char, char *, int, int);
void contract_delete(P_char, int);
void contract_list(P_char, char *, int);
void contract_offer(P_char, int, char *);
void contract_reload(P_char, int);
void contract_set(P_char, int, char *, char *);

#ifdef ASSOC
/* assocs.c */

int assoc_activate(P_char, ush_int);
int assoc_list_queue(P_char);
int assoc_message(P_char, ush_int, char *);
int assoc_create(P_char, char *, char *);
int assoc_modify(P_char, int, char *);
int assoc_list(P_char);
int assoc_enter_description(P_char);
int assoc_info(P_char, ush_int);
int assoc_report(P_char);

/* assocs.c */

P_assoc GetNewAssoc(void);
void readAssoc(P_assoc, ush_int);
void strip_blanks(char *msg);
int writeAssoc(P_assoc);
void boot_assocs(void);
int writeAssocControl(void);
P_assoc find_assoc_num(ush_int);
int assoc_found(P_char, P_char, char *, char *);
int assoc_delete(P_char, ush_int);
int assoc_level_check(P_char);
void str_to_money(char *, int *, int *, int *, int *);
int assoc_check_ban(P_char);
int assoc_secede(P_char);
int assoc_apply(P_char, P_char);
int assoc_enroll(P_char, P_char);
void assoc_member_list(P_char);
int assoc_kickout(P_char, char *);
int assoc_change_rank(P_char, char *, char *);
int assoc_deposit(P_char, int, int, int, int);
int assoc_withdraw(P_char, int, int, int, int);
int assoc_change_title(P_char, P_char, char *, bool);
int assoc_rank_name(P_char, char *, char *);
int assoc_rank_title(P_char, char *, char *);
int assoc_fine_member(P_char, char *, int, int, int, int);
char *assoc_title(P_char, char *);
int assoc_title_len(char *);
int god_title_len(char *);
void assoc_update_member(P_char);
void do_acontrol(P_char, char *, int);
int assoc_rename(P_char, ush_int, char *);
void assoc_reset_title(P_char);
int assoc_change_type(P_char, ush_int, char *);
int assoc_toggle_bit(P_char, ush_int, char *);
int assoc_supervise(P_char, ush_int);
int assoc_ban_player(P_char, char *);
int assoc_add_member(P_char, ush_int, char *);
int assoc_remove_member(P_char, ush_int, char *);
void do_assoc(P_char, char *, int);
int assoc_count_rank(P_assoc, int);
int assoc_decline(P_char, ush_int, char *);
int assoc_queue(P_char);
int assoc_disband(P_char);
int assoc_enter_motd(P_char);
void assoc_access_chat(P_char, char *);

#endif //ASSOC

/* newauction.c */
void boot_auctions(void);
void shutdown_auctions(void);
void auction_rename(char *old_name, char *new_name);

/* auction.c */
// replaced with newauction.c
#if 0
int auction(P_obj, P_char, int, char *);
void auction_save_auction(int);
void initialize_auctions(void);
void do_auction(P_obj, P_char, int, char *, int);
#endif

/* bard.c */

P_obj has_instrument(P_char);
int SINGING(P_char);
int bard_calc_chance(P_char);
int bard_get_type(int);
int bard_in_room(P_char, struct affected_type *);
int bard_saves(P_char, P_char);
int bard_song_level(P_char);
void BardEffect(P_char);
void BardSing(P_char);
void bard_aggro(P_char, P_char);
void bard_calm(int, P_char, P_char);
void bard_charm(int, P_char, P_char);
void bard_cowardice(int, P_char, P_char);
void bard_flight(int, P_char, P_char);
void bard_forgetfulness(int, P_char, P_char);
void bard_harming(int, P_char, P_char);
void bard_healing(int, P_char, P_char);
void bard_heroism(int, P_char, P_char);
void bard_peace(int, P_char, P_char);
void bard_protection(int, P_char, P_char);
void bard_revelation(int, P_char, P_char);
void bard_sleep(int, P_char, P_char);
void bard_dance(int, P_char, P_char);
void bard_enthrall(int, P_char, P_char);
void bard_luck(int, P_char, P_char);
void bard_warchant_fortitude(int, P_char, P_char);
void bard_warchant_battlehymn(int, P_char, P_char);
void do_bardcheck_action(P_char, char *, int);
void do_bardsing(P_char, char *);
void do_play(P_char, char *, int);
void enable_bard_sing(P_char);
void do_virtuoso(P_char, char *, int);
void instrument_check_strain(P_char);
void sing_affect_in_room(P_char);
void sing_verses(P_char);
void stop_singing(P_char);
void do_warchant(P_char, char *, int);

/* board.c */

int board(P_obj, P_char, int, char *);
void board_save_board(int);
void initialize_boards(void);

/* build_areas.c */

int build_areas(void);
void punt(char *msg);
int build_zon_file(void);
int build_wld_file(void);
int build_mob_file(void);
int build_obj_file(void);
int build_shp_file(void);
int build_soc_file(void);
int build_qst_file(void);

/* comm.c */

int ban_str(const char *, const char *);
int bannedsite(P_desc, int);
int find_color_entry(int);
int get_from_q(struct txt_q *, char *);
int init_socket(int);
int new_connection(int s);
int new_descriptor(int s);
int process_input(P_desc);
int process_output(P_desc);
int write_to_descriptor_raw(int, const char *);
int write_to_descriptor(P_desc, const char *);
int descriptor_lock(P_desc, char *, int);
void lock_descriptor(P_desc, char *);
void combined_act(const char *, const char *, const char *, int, P_char, P_obj, void *);
void act(const char *, int, P_char, P_obj, void *, int);
void c_act(const char *, int, P_char, P_obj, void *, int, uint);
void cm_act(const char *, int, P_char, P_obj, void *, int, uint);
void close_socket(P_desc);
void close_sockets(int);
void coma(int);
void flush_queues(P_desc);
void nonblock(int);
void perform_complex(P_char, P_char, P_obj, P_obj, char *, int, int);
void perform_to_all(const char *, P_char);
void send_to_all(const char *);
void send_to_char(const char *, P_char);
void send_to_char_f(P_char ch, const char *format, ...);
void send_to_except(const char *, P_char);
void send_to_outdoor(const char *);
void send_to_room(const char *, int);
void send_to_room_except(const char *, int, P_char);
void send_to_room_except_two(const char *, int, P_char, P_char);
void send_to_zone(int, const char *);
void send_to_zone_func(int, int, const char *);
void send_to_zone_indoor(int, const char *);
void send_to_zone_outdoor(int, const char *);
void write_to_q(const char *, struct txt_q *, int);
void replicator(char *, int);
void link_room(int, int);
void unlink_room(int, int);
bool is_linked(int, int);
unsigned int GetTimeStamp(void);

/* db.c */

P_char GetNewChar(int);
P_char read_mobile(int, int);
P_obj GetNewObj(int);
P_obj read_object(int, int);
char *boot_fread_string(FILE *);
char *file_to_string(const char *);
char *fread_string(FILE *);
int is_empty(int);
int real_mobile(int);
int real_object(int);
int real_room(int);
int real_room0(int);
uint GetNewOSN(int);
void MemReport(void);
void boot_db(void);
void boot_pose_messages(void);
void boot_world(void);
void boot_zones(void);
void free_zones(void);
void free_char(P_char);
void free_obj(P_obj);
void init_char(P_char);
void renum_zone_table(void);
void renumber_exits(void);
void reset_time(void);
void reset_zone(int);
void setup_dir(FILE *, int, int);
void skip_fread(FILE *);
void weather_setup(void);
void release_group_mem(P_group);
void do_text_reload(P_char ch, char *arg, int cmd);
int file_to_string_alloc(const char *name, char **buf);
int major_reset_zone(int zone);
int purge_zone(int zone);

/* debug.c */

void PC_list_debug(void);
void cmdlog(P_char, char *);
void do_debug(P_char, char *, int);
void do_mreport(P_char, char *, int);
void dump_stack(char *);
void hour_debug(void);
void init_cmdlog(void);
void loop_debug(void);
void toggleSystemDebugMode(P_char, char *);

/* disease.handler.c */

void AddDiseaseDefinitions(void);
void AddCures(void);
void AddDiseaseDef(int, int, char *, char *, int (*) (P_char, P_char, int, char *), int (*) (P_obj, P_char, int, char *), int (*) (P_char, P_char, int, char *), int (*) (P_char, void *));
int InfectWithDisease(P_char, int);
int RemoveAllDiseases(P_char, bool);
int RemoveDisease(P_char, int, struct disease *);
struct disease *findDiseaseType(int);
struct disease *findDiseaseName(char *);
struct disease *cloneDiseaseFromTemplate(int);
struct disease *isPCInfectedWithDisease(P_char, int);
int countDiseases(P_char);
void AddCureComponent(struct cure *, int, int, bool);
void AddDiseaseToCure(struct cure *, int, int);
struct cure *findCure(char *);
struct cure *getCure(int);
struct cure *CreateCure(int, char *, char *, int);
bool regressDisease(P_char, struct disease *, int);
void applyDiseaseCure(P_char, int, bool);
void do_mix(P_char, char *, int);
void manageDiseases(P_char, char *);

/* editor.c */

void edit_free(struct edit_data *);
void edit_string_add(struct edit_data *, char *);
void edit_start(P_desc, char *, int, void (*callback)(P_desc, int, char *), int);

/* events.c */

bool RemoveEvent(void);
bool Schedule(int, int, int, void *, void *);
int event_time(P_event, int);
void AggAttack(void);
void Berserk(P_char, int, int);
void CharWait(P_char, int);
void ClearCharEvents(P_char);
void ClearObjEvents(P_obj);
void Events(void);
void ReSchedule(void);
void StartRegen(P_char, int);
int CheckStun(P_char);
void Stun(P_char, int);
void disguiseEvent(P_char, int);
void init_events(void);
void showEventLoad(P_char, char *);

#ifdef EVENT_SAVING
/* events.files.c */

void dumpEV(struct restored_event_data *);
char *getFunctionName(void *);
void *getFunctionPointer(char *);
int destroyRestoredEvent(struct restored_event_data *);
int destroyRestoredCharEvents(P_char);
int scheduleRestoredCharEvents(P_char);
int scheduleRestoredEvent(void *, struct restored_event_data *);
int queueRestoredEvent(void *, struct restored_event_data *);
int writeCharacterEvents(char *, P_char);
int restoreCharacterEvents(char *, P_char);
int restoreCharacterEventsOnly(P_char);

#endif //EVENT_SAVING

#ifdef NEWCOMBAT

/* combat.c */

int attemptParry(P_char, P_char, int);
int attemptShieldBlock(P_char, P_char, int);
int attemptDodge(P_char, P_char, int);
int attemptMountBlock(P_char, P_char, int);
int DefenseMod(P_char, P_char, int, int, int);
void DefenseProcess(P_char, P_char, int, int, int);
bool DefenseProcCheck(P_char ch, P_char attacker, P_obj obj, int idx_type);

void NukePoison(P_char);
void StartCombat(P_char, P_char);
void StopCombat(P_char);
void CombatEngine(void);
void CombatMeleeAttack(P_char, P_char, int);
int CalcMeleeHit(P_char, P_char, int);
int CalcMeleeDam(P_char, P_char, P_obj, int, int);
int is_missile_spell(int attackType);
int CombatDamage(P_char, P_char, int, int);
bool InitialCombatCheck(P_char, P_char);
bool CombatCheck(P_char, P_char);
int CheckCombatProcs(P_char, P_char, P_obj, int, int, int);
int VitalStrike(P_char, P_char, int);
P_char ForceReturn(P_char);
bool AdjacentInRoom(P_char, P_char);
bool DisarmCheck(P_char);
bool DisarmRecovery(P_char);
bool PhasedAttack(P_char, int);
bool PickupDisarmedWeapon(P_char);
bool damage(P_char, P_char, int, int);
bool Damage(P_char, P_char, int, int);
char *replace_string(const char *, const char *);
int CalcToHit(P_char, int);
int PartySizeMod(int, int, int, int);
int TryRiposte(P_char, P_char, int);
int WeaponSkill(P_char, int);
int WeaponSkill_num(P_char, int);
int align_mod(P_char, P_char);
int dodgeSucceed(P_char, P_char, int, int);
int blockSucceed(P_char, P_char, int, int);
int mountblockSucceed(P_char, P_char, int, int);
int exp_mod(P_char, P_char);
int parrySucceed(P_char, P_char, int, int);
void HandleDroppedWeapon(P_char, int, int);
void HandleFumblingWeapon(P_char, int, int);
void NukeSingleStone(P_char);
void NukeSingleScale(P_char);
void NukeDisplacement(P_char);
int NukeMirrorImage(P_char, P_char, int);
int DopplegangerAttack(P_char, P_char, int);
void ReTarget(P_char);
void StopAllAttackers(P_char);
void StopMercifulAttackers(P_char);
void SuddenDeath(P_char, P_char, const char *);
void appear(P_char);
void attack_back(P_char, P_char);
void change_alignment(P_char, P_char);
void check_killer(P_char, P_char);
void dam_message(int, P_char, P_char, int);
void dam_message_multi(P_char, P_char);
void fire_missiles(P_char, P_char, int, char *);
void death_cry(P_char);
void die(P_char);
void die_deathtrap(P_char);
void group_gain(P_char, P_char);
void hit(P_char, P_char, int);
void load_messages(void);
void make_corpse(P_char);
void perform_violence(void);
void raw_kill(P_char);
void set_fighting(P_char, P_char);
void stop_fighting(P_char);
void update_pos(P_char);
bool poisonedWeapon(P_char, P_char, P_obj);
void DuelKnockOut(P_char, P_char);
void stop_duelling(P_char, P_char, int);
void stop_duelling_group(P_group, P_group);
void ApplyPoisonEffect(P_char, P_char, int, int);
void make_bloodstain(P_char);
int DexHitBonus(int);
int StrDamBonus(int);
long corpseGetDecayTime(P_obj corpse);
int corpseSetDecayTime(P_obj corpse, unsigned long decayTime);
P_event corpseGetDecayEvent(P_obj corpse);
void area_message(P_char, int, int, char *);
void breath_message(P_char, int, char *);
int would_die(P_char, int);
int CHECK_BLINDFIGHTING(P_char);

#else //NEWCOMBAT

// Fight.c
P_char ForceReturn(P_char);
bool AdjacentInRoom(P_char, P_char);
bool DisarmCheck(P_char);
bool DisarmRecovery(P_char);
bool PhasedAttack(P_char, int);
bool PickupDisarmedWeapon(P_char);
bool damage(P_char, P_char, int, int);
char *replace_string(const char *, const char *);
int CalcToHit(P_char, int);
int PartySizeMod(int, int, int, int);
int TryRiposte(P_char, P_char, int);
int WeaponSkill(P_char, int);
int WeaponSkill_num(P_char, int);
int align_mod(P_char, P_char);
int dodgeSucceed(P_char, P_char, int);
int blockSucceed(P_char, P_char, int);
int exp_mod(P_char, P_char);
int parrySucceed(P_char, P_char, int);
void HandleDroppedWeapon(P_char, int, int);
void HandleFumblingWeapon(P_char, int, int);
void NukeSingleStone(P_char);
void NukeSingleScale(P_char);
void NukeDisplacement(P_char);
void NukePoisonAff(P_char);
void ReTarget(P_char);
void StopAllAttackers(P_char);
void StopMercifulAttackers(P_char);
void SuddenDeath(P_char, P_char, const char *);
void appear(P_char);
void attack_back(P_char, P_char);
void change_alignment(P_char, P_char);
void check_killer(P_char, P_char);
void dam_message(int, P_char, P_char, int);
void death_cry(P_char);
void die(P_char);
void die_deathtrap(P_char);
void group_gain(P_char, P_char);
void hit(P_char, P_char, int);
void load_messages(void);
void make_corpse(P_char);
void perform_violence(void);
void raw_kill(P_char);
void set_fighting(P_char, P_char);
void stop_fighting(P_char);
void update_pos(P_char);
bool poisonedWeapon(P_char, P_char, P_obj);
void DuelKnockOut(P_char, P_char);
void stop_duelling(P_char, P_char, int);
void stop_duelling_group(P_group, P_group);
void ApplyPoisonEffect(P_char, P_char, int, int);

#endif //NEWCOMBAT

/* files.c */

void convertGrantFlags(P_char);
unsigned long getLong(char **);
int writeCharacter(P_char, int, int);
int deleteCharacter(P_char);
int restoreCharOnly(P_char, char *);
int restoreItemsOnly(P_char, int);
int restorePasswdOnly(P_char, char *);
int restoreWitness(char *, P_char);
int restoreWitnessed(char *, P_char);
int writeWitnessed(char *, P_char);
void restoreCorpses(void);
void writeCorpse(P_obj);
P_char restoreMobileOnly(P_char, char *, int, int);
int listRentedMobiles(P_char, char *, int);
int writeMobile(P_char, int, int);
int deleteRentFile(P_char);
int deleteMobile(P_char, int, int);
int stopPetSave(P_char, P_char);
int restoreCrashedMobiles(P_char);
int writeCart(P_char);
bool cartfile_exists(P_char);
int get_line_from_string(char *, char *, int);
void restoreCart(P_char);
int saveCodeControlBits();
int restoreCodeControlBits();
int npcsave_MoveIngameToClaim();
int writeWitness(char *, wtns_rec *);
#ifdef NEWJUSTICE
int writeTownJustice(int);
int deleteTownJustice(int);
int restoreTownJustice(int);
void restore_town_justice(void);
int writeJailItems(P_char);
int deleteJailItems(P_char);
int restoreJailItems(P_char);
#endif //NEWJUSTICE
#ifdef KINGDOM
int deleteHouseObj(int);
int writeHouse(P_house);
int deleteHouse(char *);
int restoreHouse(char *);
void restore_house(void);
void writeHouseObj(P_obj, int);
void restoreHouseObj(void);
int writeConstructionQ(void);
int loadConstructionQ(void);
void boot_kingdoms(void);
#endif //KINGDOM
#ifdef PK_BALANCE
int restoreSkillAdjustments();
int saveSkillAdjustments();
#endif //PK_BALANCE
void restoreItemCaches(void);
void writeItemCache(P_obj);
void recalcRogueHits(P_char);
void writeAuctionObj(P_char, P_obj, int);
void restoreAuctionObj(void);
int moveIngameMobilesToClaim(P_char);
int restoreCrashedMobilesOnBoot(void);
int npcsave_MoveIngameToClaim(void);
int saveCodeControlBits(void);
int restoreCodeControlBits(void);
int saveSkillAdjustments(void);
int restoreSkillAdjustments(void);
int deleteAuctionObj(int);

/* control.c */

void do_control_panel(P_char, char *, int);
void do_cstat(P_char, char *, int);
void Show_CP_Help(P_char);
void Show_Control_Panel(P_char, int);
void Read_Control_Database(void);
void Write_Control_Database(void);
void Process_MRTG_Stats(void);
void Build_MRTG_Configs(char *Statname, char *Pagename, char *Peak, char *Xlegend, char *Ylegend);
void Build_MRTG_HTML(char *Title);
void ProcessCalcMeleeHit(P_char, P_char, int, int, int, int);
void ProcessCalcMeleeDamage(P_char, P_char, int);
void ProcessSpellDamage(P_char, P_char, int, int);
void ProcessCombatDamage(P_char, P_char, int, int);
void Display_Thac0_Stats(P_char, P_char, int);
void Display_Damage_Stats(P_char, P_char, int);
void Display_Spell_Stats(P_char, P_char, int);
void Reset_Combat_Stats(P_char ch);
void Clear_Thac0_Stores(void);
void Clear_Damage_Stores(void);
void Clear_Spell_Stores(void);
void Clear_Archery_Stores(void);
void Reset_Damage_Stats(P_char);
void Clear_Thac0_Stores();
void Clear_Damage_Stores();
void Clear_Spell_Stores();
void Clear_Archery_Stores();
void Read_Control_Database();
void Write_Control_Database();
unsigned int GET_STAT_RECORD(int, int, int, int);

/* follow.c */

void add_follower(P_char, P_char, bool);
bool circle_follow(P_char, P_char);
void die_follower(P_char);
void stop_all_followers(P_char);
void stop_follower(P_char);
void stop_follower_silent(P_char);
void stop_pet_follow(P_char);
void stop_pc_follow(P_char, char *);

/* guild.c */

bool CharHasSpec(P_char);
char *how_good(int);
int CharMaxSkill(P_char, int);
int FindHomeTown(P_char);
int GetClassType(P_char);
int GetPrimeStat(P_char, int);
int GetSkillType(int);
int IsTeachedHere(int, int);
int RobCash(P_char, int);
int SkillRaiseCost(P_char, int);
int SpellCopyCost(P_char, int);
int guild(int, P_char, int, char *);
int guild_special(int, P_char, int, char *, int, int (*)(int, int, P_char, int, char *, char *));
int guild_classtype_thief(int, P_char, int, char *);
int guild_classtype_warrior(int, P_char, int, char *);
int guild_classtype_cleric(int, P_char, int, char *);
int guild_classtype_mage(int, P_char, int, char *);
int guild_warrior(int, P_char, int, char *);
int guild_ranger(int, P_char, int, char *);
int guild_berserker(int, P_char, int, char *);
int guild_paladin(int, P_char, int, char *);
int guild_antipaladin(int, P_char, int, char *);
int guild_cleric(int, P_char, int, char *);
int guild_monk(int, P_char, int, char *);
int guild_druid(int, P_char, int, char *);
int guild_shaman(int, P_char, int, char *);
int guild_sorcerer(int, P_char, int, char *);
int guild_necromancer(int, P_char, int, char *);
int guild_conjurer(int, P_char, int, char *);
int guild_elementalist(int, P_char, int, char *);
int guild_thief(int, P_char, int, char *);
int guild_assassin(int, P_char, int, char *);
int guild_mercenary(int, P_char, int, char *);
int guild_bard(int, P_char, int, char *);
int guild_psionicist(int, P_char, int, char *);
int guild_lich(int, P_char, int, char *);
int guild_illusionist(int, P_char, int, char *);
int guild_battlechanter(int, P_char, int, char *);

int waterdeep_guild_eight_special(int, int, P_char, int, char *, char *);
int waterdeep_guild_eleven_special(int, int, P_char, int, char *, char *);
int waterdeep_guild_five_special(int, int, P_char, int, char *, char *);
int waterdeep_guild_four_special(int, int, P_char, int, char *, char *);
int waterdeep_guild_nine_special(int, int, P_char, int, char *, char *);
int waterdeep_guild_one_special(int, int, P_char, int, char *, char *);
int waterdeep_guild_seven_special(int, int, P_char, int, char *, char *);
int waterdeep_guild_six_special(int, int, P_char, int, char *, char *);
int waterdeep_guild_ten_special(int, int, P_char, int, char *, char *);
int waterdeep_guild_three_special(int, int, P_char, int, char *, char *);
int waterdeep_guild_twelve_special(int, int, P_char, int, char *, char *);
int waterdeep_guild_two_special(int, int, P_char, int, char *, char *);
void CharSkillGainChance(P_char, int, int);
void SetGuildSpellLvl(void);

/* group.c */

void clear_group(P_group);
P_group find_group(char *, P_char);
P_group read_group(int, int);
P_group get_group_vis(P_char, const char *, int);
int real_group(int);
int real_group0(int);
int newGroup(P_char, char *, int);
int groupAllFollowers(P_group, P_char);
int addToMobileGroup(P_char, P_char, int);
int countGroupMembers(P_group);
int countPCGroupMembers(P_group group);
int containsGroupCachedItem(P_obj);
void free_group(P_group);
void deleteGroupMember(P_gmember);
void extract_group(P_group);
void newGroupMember(P_group, P_char);
void deleteGroup(P_char, P_group);
void lookAtGroup(P_char, int, char *);
void show_gmembers_to_char(P_group, P_char, int);
void addCashToGroup(P_char, int, int, int, int);
void removeCashFromGroup(P_char, int, int, int, int);
void addGroupCacheItem(P_obj, P_char);
void removeGroupCacheItem(P_obj, P_char);
void findNewGroupLeader(P_group);
void removeCharFromGroup(P_char);
P_char findMostQualifiedCandidate(P_group, int);
int canGroupWithGroup(P_group, P_char);
int canGroupWithChar(P_char, P_char);
int countItemsInCache(P_group);
bool IS_GROUP_LEADER(P_char);
int getMaxGroupNumber(P_char);
P_gmember group_get_member(P_char ch);

/* handler.c */

P_char get_PC(char *);
P_char get_char(char *);
P_char get_char_in_game(char *);
P_char get_char_in_game_vis(P_char, const char *, int);
P_char get_char_num(int);
P_char get_char_room(const char *, int);
P_char get_char_room_num(int, int);
P_char get_char_room_vis(P_char, const char *);
P_char get_char_vis(P_char, const char *, int);
P_obj create_money(int, int, int, int);
P_obj get_obj(char *);
P_obj get_obj_in_hand(P_char, char *, int *);
P_obj get_obj_in_list(char *, P_obj);
P_obj get_obj_in_list_num(int num, P_obj);
P_obj get_obj_in_list_vis(P_char, char *, P_obj);
P_obj get_obj_num(int);
P_obj get_obj_vis(P_char, char *);
P_obj unequip_char(P_char, int, bool);
bool affected_by_spell(P_char, int);
bool isname(const char *, const char *);
char *FirstWord(char *);
int apply_ac(P_char, int);
int can_char_use_item(P_char, P_obj);
int char_light(P_char);
int generic_find(char *, int, P_char, P_char *, P_obj *);
int get_number(char **);
int room_light(int, int);
void Decay(P_obj);
void RemoveFromCharList(P_char);
void ac_stopAllFromConsenting(P_char);
void ac_stopAllFromDuelling(P_char);
void ac_stopAllFromIgnoring(P_char);
void add_coins(P_obj, int, int, int, int);
void affect_from_char(P_char, int);
void affect_join(P_char, struct affected_type *, int, int);
void affect_modify(int, int, ubyte *);
void affect_remove(P_char, struct affected_type *);
void affect_to_char(P_char, struct affected_type *);
int affect_alter_ego(int);
void initial_hit_adjust(P_char);
void affect_total(P_char);
void all_affects(P_char, int);
void apply_affs(P_char, int);
void char_from_room(P_char);
void char_to_room(P_char, long, int);
void equip_char(P_char, P_obj, int, int);
void extract_char(P_char);
void extract_obj(P_obj);
void nuke_character(P_char, P_char);
void obj_from_char(P_obj);
void obj_from_obj(P_obj);
void obj_from_room(P_obj);
void obj_to_char(P_obj, P_char);
void obj_to_obj(P_obj, P_obj);
void obj_to_room(P_obj, int);
void object_list_new_owner(P_obj, P_char);
void update_char_objects(P_char);
void update_con_bonus(P_char);
void update_object(P_obj, int);
#ifdef ARTIFACT
void tagNestedArtifacts(P_obj, P_char);
void getNestedArtifacts(P_obj, P_char);
void dropNestedArtifacts(P_obj);
int artifactFromChar(P_obj, P_char);
int simpleArtifactFromChar(P_obj, P_char);
int artifactToChar(P_obj, P_char);
int artifactIsOwned(int);
int searchForArtifact(int);
void tagBogusArtifact(P_obj);
#endif //ARTIFACT
void extract_cart(P_obj);
void add_weight_coins(P_obj, int, int, int, int);
P_char affect_find_caster(P_char, int);
void affect_remove_linked(P_char, P_char, int);
int find_all_dots(char *arg);
#define FIND_INDIV      0
#define FIND_ALL        1
#define FIND_ALLDOT     2

/* illusionist.c */

void spell_phantasmal_killer(int, P_char, P_char, P_obj);
void spell_shadow_bolt(int, P_char, P_char, P_obj);
void spell_phantasmal_blades(int, P_char, P_char, P_obj);
void spell_shadow_magic(int, P_char, P_char, P_obj);
void spell_shadow_burst(int, P_char, P_char, P_obj);
void spell_phantom_armor(int, P_char, P_char, P_obj);
void spell_mislead(int, P_char, P_char, P_obj);
void spell_sequester(int, P_char, P_char, P_obj);
void spell_displacement(int, P_char, P_char, P_obj);
void spell_nondetection(int, P_char, P_char, P_obj);
void spell_spook(int, P_char, P_char, P_obj);
void spell_shade(int, P_char, P_char, P_obj);
void spell_mirror_image(int, P_char, P_char, P_obj);
void spell_shadow_walk(int, P_char, P_char, P_obj);
void spell_dimension_shift(int, P_char, P_char, P_obj);
void dimension_shift_decay(void);
void dimension_shift_destruction(void);
void spell_phantom_steed(int, P_char, P_char, P_obj);
void spell_simulacrum(int, P_char, P_char, P_obj);
void spell_rainbow_pattern(int, P_char, P_char, P_obj);
void spell_shadow_flux(int, P_char, P_char, P_obj);
void spell_dimensional_fold(int, P_char, P_char, P_obj);
void spell_beautify(int, P_char, P_char, P_obj);
void spell_corpse_glamor(int, P_char, P_char, P_obj);
void spell_wraithform(int, P_char, P_char, char *);
void spell_true_sight(int, P_char, P_char, P_obj);
void spell_blackthorns(int, P_char, P_char, P_obj);
void spell_doppleganger(int, P_char, P_char, P_obj);
void spell_scarlet_outline(int, P_char, P_char, P_obj);
void spell_tranquility(int, P_char, P_char, P_obj);
void spell_camoflauge(int, P_char, P_char, P_obj);
void spell_feign_death(int, P_char, P_char, P_obj);
void spell_shadechill(int, P_char, P_char, P_obj);
void phantom_heal_event(void);
void spell_phantom_heal(int, P_char, P_char, P_obj);
void spell_nightmare(int, P_char, P_char, P_obj);
void spell_massmorph(int, P_char, P_char, P_obj);
void spell_phantasmal_tendril(int, P_char, P_char, P_obj);
void phantasmal_tendril_event(void);
void spell_sun_shadow(int, P_char, P_char, P_obj);
void sun_shadow_dissipate_event(P_char, P_room);
void cast_corpse_glamor(int, P_char, char *, int, P_char, P_obj);
void cast_massmorph(int, P_char, char *, int, P_char, P_obj);
void cast_phantom_steed(int, P_char, char *, int, P_char, P_obj);
void cast_simulacrum(int, P_char, char *, int, P_char, P_obj);
void cast_change_self(int, P_char, char *, int, P_char, P_obj);
void cast_shadow_walk(int, P_char, char *, int, P_char, P_obj);
void cast_dimensional_fold(int, P_char, char *, int, P_char, P_obj);
void cast_phantasmal_blades(int, P_char, char *, int, P_char, P_obj);
void cast_rainbow_pattern(int, P_char, char *, int, P_char, P_obj);
void cast_shadow_burst(int, P_char, char *, int, P_char, P_obj);
void cast_shadow_bolt(int, P_char, char *, int, P_char, P_obj);
void cast_shadow_flux(int, P_char, char *, int, P_char, P_obj);
void cast_blackthorns(int, P_char, char *, int, P_char, P_obj);
void cast_nightmare(int, P_char, char *, int, P_char, P_obj);
void cast_true_sight(int, P_char, char *, int, P_char, P_obj);
void cast_mislead(int, P_char, char *, int, P_char, P_obj);
void cast_phantom_armor(int, P_char, char *, int, P_char, P_obj);
void cast_sequester(int, P_char, char *, int, P_char, P_obj);
void cast_nondetection(int, P_char, char *, int, P_char, P_obj);
void cast_displacement(int, P_char, char *, int, P_char, P_obj);
void cast_spook(int, P_char, char *, int, P_char, P_obj);
void cast_phantasmal_killer(int, P_char, char *, int, P_char, P_obj);
void cast_shadow_magic(int, P_char, char *, int, P_char, P_obj);
void cast_mirror_image(int, P_char, char *, int, P_char, P_obj);
void cast_doppleganger(int, P_char, char *, int, P_char, P_obj);
void cast_tranquility(int, P_char, char *, int, P_char, P_obj);
void cast_scarlet_outline(int, P_char, char *, int, P_char, P_obj);
void cast_shade(int, P_char, char *, int, P_char, P_obj);
void cast_shadechill(int, P_char, char *, int, P_char, P_obj);
void cast_beautify(int, P_char, char *, int, P_char, P_obj);
void cast_camoflauge(int, P_char, char *, int, P_char, P_obj);
void cast_feign_death(int, P_char, char *, int, P_char, P_obj);
void cast_phantom_heal(int, P_char, char *, int, P_char, P_obj);
void cast_phantasmal_tendril(int, P_char, char *, int, P_char, P_obj);
void multi_round_spell_char_event(P_char);
void cast_sun_shadow(int, P_char, char *, int, P_char, P_obj);

/* interp.c */

bool special(P_char, int, char *);
char *one_argument(char *, char *);
char lower(char);
int fill_word(char *);
char *any_one_arg(char *argument, char *first_arg);
char *two_arguments(char *argument, char *first_arg, char *second_arg);
int is_abbrev(const char *, const char *);
int is_number(char *);
int old_search_block(const char *, uint, uint, const char **, int);
int search_block(char *, int, const char **, int);
void GRANTSET(int, int, int, int, int, int);
void argument_interpreter(char *, char *, char *);
void assign_command_pointers(void);
void assign_grant_commands(void);
void command_interpreter(P_char, char *);
void do_confirm(P_char, int);
void half_chop(char *, char *, char *);

/* justice.c */

#ifdef OLDJUSTICE
P_char justice_make_guard(int);
bool JusticeMobAct(P_char);
int CheckForWitness(P_char);
int justice_get_guards(P_char, P_char *, int);
int witness_remove(P_char, wtns_rec **);
void PC_SET_TOWN_JUSTICE_FLAGS(P_char, int, int);
void boot_justice_libs(void);
void do_report_crime(P_char, char *, int);
void justice_action_invader(P_char);
void justice_action_outcast(P_char);
void justice_action_wanted(P_char);
void justice_guard_remove(P_char);
void justice_hunt_cancel(P_char);
void justice_register_guard(P_char);
void justice_scan(P_char ch);
void justice_victim_remove(P_char);
void justice_witness(P_char, P_char, int);
void witness_add(P_char, char *, char *, int);
void witness_destroy(P_char);
wtns_rec *witness_find(P_char, char *, char *, int, int, wtns_rec *);
#endif //OLDJUSTICE

/* lookup.c */

void m_close(int);
P_lookup lookup_getNewStruct(void);
int lookup_establishDaemonConnection(char *, int);
void lookup_cleanUp(struct descriptor_data *, int);
void lookup_finishIdentLookup(P_desc);
void lookup_finishHostnameLookup(P_desc);
bool lookup_sendIdentQuery(struct conn_lookup_struct *);
void lookup_recvIdentReply(struct conn_lookup_struct *);
bool lookup_sendHostnameQuery(struct conn_lookup_struct *);
void lookup_recvHostnameReply(struct conn_lookup_struct *);
void lookup_startHostnameLookup(struct conn_lookup_struct *);
void lookup_startIdentLookup(struct conn_lookup_struct *);
struct conn_lookup_struct *lookup_newConnection(struct descriptor_data *);
void lookup_initConnection(struct descriptor_data *);
void lookupFSM(struct descriptor_data *, struct conn_lookup_struct *);

/* mobgen.c */

P_char GenerateMobile(int, int, int, int, int, int);
void do_mobgen(P_char, char *, int);
P_char RandomMobile(void);

/* mmail.c */

void mmail_mbox_system_init(void);
void mmail_mbox_scan_for_new_messages(P_char ch);
void do_mlist(P_char ch, char *argument, int cmd);
void do_mwrite(P_char ch, char *argument, int cmd);
void do_mread(P_char ch, char *argument, int cmd);
void do_mreply(P_char ch, char *argument, int cmd);
void do_mdelete(P_char ch, char *argument, int cmd);
void mmailAdmin(P_char ch, char *arg);
char *mmail_get_date_string(time_t *);

/* mmail.files.c */

mmesg *mmail_mbox_parse_file_message(char **);

/* mysql.c */

int sql_initialize(void);
int sql_create_db(void);
int sql_create_tables(int);
int sql_connect_db(char *dbname);
int sql_connect(void);
int sql_drop_obj_table(void);
void sql_escape_text(char *src, char *to);
int sql_obj_add(P_obj obj);
void sql_perform_obj_scans(void);
int sql_create_logpwords(void);

/* mysql.obj.c */

int sql_create_obj_table(void);
int sql_create_objextradesc_table(void);
int sql_drop_obj_tables(void);
void sql_escape_obj_text(P_obj obj, char *src, char *to);
int sql_obj_add(P_obj obj);
void sql_perform_obj_scans(void);

/* mysql.pc.c */

int sql_create_pc_table(void);
int sql_drop_pc_tables(void);
void sql_escape_pc_text(P_char ch, char *src, char *to);
int sql_pc_add(P_char ch);
P_char sql_read_pc(char *name);
int sql_insert_pc(char *name);
int sql_scan_players_blocking(void);

/* namegen.c */

P_desc nameEngine(P_desc);
char *generateName(int, int, int);
char *generateHumanName(int);
char *generateDwarfName(int);
char *generateElfName(int);
char *generateGnomeName(int);
char *generateHalflingName(int);
char *generateBarbName(int);
char *generateDuergarName(int);
char *generateDrowName(int);
char *generateTrollName(int);
char *generateOgreName(int);
char *generateSnakeName(int);
char *generateSquidName(void);
char *generateOrcName(int);

/* newjustice.c */

#ifdef NEWJUSTICE
int justice_get_guards(P_char, P_char *, int);
void do_justice(P_char, char *, int);
void justice_dispatch_guard(int, char *, char *, int);
void JusticeGuardMove(P_char, char *, int);
void JusticeGuardHunt(P_char);
int JusticeGuardAct(P_char);
void justice_hunt_cancel(P_char);
void justice_set_outcast(P_char, int);
void justice_delete_guard(P_char);
void justice_guard_remove(P_char);
void justice_victim_remove(P_char);
void PC_SET_TOWN_JUSTICE_FLAGS(P_char, int, int);
void justice_scan(P_char);
void justice_witness(P_char, P_char, int);
int justice_is_criminal(P_char);
void justice_action_invader(P_char);
void crime_add(int, char *, const char *, int, int, time_t, int, int);
int crime_remove(int, crm_rec *);
int CheckForWitness(P_char);
void witness_destroy(P_char);
int witness_remove(P_char, wtns_rec *);
void witness_add(P_char, P_char, P_char, int, int);
wtns_rec * witness_find(wtns_rec *, char *, char *, int, int, wtns_rec *);
crm_rec * crime_find(crm_rec *, char *, char *, int, int, int, crm_rec *);
void justice_send_witness(P_char, P_char, P_char, int, int);
void witness_scan(P_char, P_char, int, int, int);
int justice_send_guards(int, P_char, int, int);
P_char justice_make_guard(int);
void justice_register_guard(P_char);
void justice_hometown_echo(int, const char *);
void justice_judge(P_char, int);
void justice_sentenceDebt(P_char, char *, int);
void justice_sentenceJail(P_char, char *, int);
void justice_sentenceDeath(P_char, char *, int);
void justice_sentenceDeath2(P_char, char *, int);
void justice_sentenceDeath3(P_char, char *, int);
void justice_sentenceDeath4(P_char, char *, int);
void justice_scheduleDebt(P_char, int);
void justice_scheduleJailTime(P_char, int);
void justice_scheduleExecution(P_char);
void justice_scheduleExecution2(P_char);
void justice_scheduleExecution3(P_char);
void justice_scheduleExecution4(P_char);
void boot_justice_libs(void);
void set_town_flag_justice(P_char, int);
void clean_town_justice(void);
void justice_engine(int);
void justice_engine1(void);
void justice_engine2(void);
void justice_engine3(void);
void justice_engine4(void);
void justice_engine5(void);
void justice_engine6(void);
void justice_engine7(void);
void justice_engine8(void);
void justice_engine9(void);
void justice_engine10(void);
void justice_engine11(void);
void justice_engine12(void);
void justice_engine13(void);
void justice_engine14(void);
void justice_engine15(void);
void justice_engine16(void);
void justice_engine17(void);
void justice_engine18(void);
#endif //NEWJUSTICE

/* kingdom.c */

#ifdef KINGDOM
P_house find_house_by_owner(char *);
int charge_char(P_char, int);
P_house_upgrade get_con_rec(void);
void update_kingdoms(void);
void check_for_kingdom_trespassing(P_char);
void do_name_room(P_house, P_char, char *);
void do_describe_room(P_house, P_char, char *);
void hcontrol_parser(int, char *, int *, char *, int *, int *, char *);
bool valid_build_location(int, P_char, int);
int house_cost(P_char);
int guild_cost(P_char);
P_house get_house_from_room(int);
void hcontrol_detail_house(P_char, char *);
void hcontrol_clear_house(P_char, char *);
void hcontrol_restore_house(P_char, char *);
void hcontrol_change_house(P_char, char *);
void do_construct_room(P_house, P_char, char *);
void do_construct_house(P_char, char *);
void do_construct_guild(P_char, char *);
void do_show_q(P_char);
void do_build_golem(P_house, P_char, int);
void do_build_inn(P_house, P_char);
void do_build_fountain(P_house, P_char);
void do_build_chest(P_house, P_char);
void do_build_shop(P_house, P_char);
void stock_guild_shop(int);
void do_build_teleporter(P_house, P_char, char *);
void do_build_mouth(P_house, P_char);
void do_build_holy(P_house, P_char);
void do_build_heal(P_house, P_char);
void do_build_unholy(P_house, P_char);
void do_build_secret(P_house, P_char);
P_char get_char_online(char *);
void sack_house(P_house, P_char);
void check_sacks(P_house house);
void nuke_portal(int rnum);
void nuke_doorways(int, char *);
P_house find_house(int);
int countHousesInRoom(int);
int room_guild_no(int);
void House_boot(void);
void hcontrol_list_houses(P_char, char *);
int hcontrol_build_house(P_char, char *);
void hcontrol_destroy_house(P_char, char *);
void hcontrol_pay_house(P_char, char *);
void do_hcontrol(P_char, char *, int);
void do_house(P_char, char *, int);
void House_save_all(void);
int House_can_enter(P_char, int, int);
void house_crash_save(void);
int hcontrol_setup_room(P_house, int, int);
void do_construct(P_char, char *, int);
void do_sack(P_char, char *, int);
void clear_sacks(P_char);
P_house house_ch_is_in(P_char);
void construct_castle(P_house);
void do_stathouse(P_char, char *, int);
void destroy_castle(P_house);
void connect_rooms(int, int, int);
void read_guild_room(int, int);
void write_guild_room(int, int);
void process_construction_q(void);
void do_housekeeping(void);
void open_market_house(void);
int delete_guild_room(int);
#endif
/* lanuages.c */

char *language_crypt(P_char, P_char, char *);
char *language_known(P_char, P_char);
char *language_singlepass(P_char, int, char *);
char casecorrect(int);
char low_case(int);
int npc_get_pseudo_language_skill(P_char, int);
int npc_get_pseudo_spoken_language(P_char);
int on_aakkonen(int);
int on_vokaali(int);
void do_speak(P_char, char *, int);
void init_defaultlanguages(P_char);
void language_gain(P_char, P_char);
void language_show(P_char);

/* limits.c */

int Trophy_Mod(P_char, P_char, int);
int graf(P_char, int, int, int, int, int, int, int, int);
int hit_limit(P_char);
int hit_regen(P_char);
int mana_limit(P_char);
int mana_regen(P_char);
int move_limit(P_char);
int move_regen(P_char);
void advance_level(P_char);
void check_idling(P_char);
void gain_condition(P_char, int, int);
void gain_exp(P_char, int);
void gain_exp_regardless(P_char, int);
void gain_practices(P_char);
void lose_level(P_char);
void lose_practices(P_char);
void point_update(void);
void set_title(P_char);

/* magic.c */

bool AreaAffectCheck(P_char, P_char);
bool check_item_teleport(P_char, char *, int);
int KludgeDuration(P_char, int, int);
int Summonable(P_char);
int get_room_in_zone(int, P_char);
int modify_by_specialization(P_char, int, int);
int plane_of_room(int);
void AgeChar(P_char, int);
void BackToUsualForm(P_char);
void creeping_kludge(void);
void darkness_dissipate_event(void);
void heat_blind(P_char);
void silence_dissipate_event(void);
void spell_acid_blast(int, P_char, P_char, P_obj);
void spell_acid_breath(int, P_char, P_char, P_obj);
void spell_age(int, P_char, P_char, P_obj);
void spell_agility(int, P_char, P_char, P_obj); /*Moradin*/
void spell_animate_skeleton(int, P_char, P_char, P_obj);
void spell_animate_zombie(int, P_char, P_char, P_obj);
void spell_animate_spectre(int, P_char, P_char, P_obj);
void spell_animate_wraith(int, P_char, P_char, P_obj);
void spell_animate_ghoul(int, P_char, P_char, P_obj);
void spell_animate_ghast(int, P_char, P_char, P_obj);
void spell_animate_ghost(int, P_char, P_char, P_obj);
void spell_animate_shadow(int level, P_char ch, P_char victim, P_obj obj);
void spell_animate_wight(int level, P_char ch, P_char victim, P_obj obj);
void spell_armor(int, P_char, P_char, P_obj);
void spell_awareness(int, P_char, P_char, P_obj);
void spell_barkskin(int, P_char, P_char, P_obj);
void spell_bigbys_clenched_fist(int, P_char, P_char, P_obj);
void spell_bless(int, P_char, P_char, P_obj);
void spell_natures_blessing(int, P_char, P_char, P_obj);
void spell_blindness(int, P_char, P_char, P_obj);
void spell_burning_hands(int, P_char, P_char, P_obj);
void spell_call_lightning(int, P_char, P_char, P_obj);
void spell_cause_critical(int, P_char, P_char, P_obj);
void spell_cause_light(int, P_char, P_char, P_obj);
void spell_cause_serious(int, P_char, P_char, P_obj);
void spell_chain_lightning(int, P_char, P_char, P_obj);
void spell_charm_person(int, P_char, P_char, P_obj);
void spell_chill_touch(int, P_char, P_char, P_obj);
void spell_clairvoyance(int, P_char, P_char, P_obj);
void spell_coldshield(int, P_char, P_char, P_obj);
void spell_color_spray(int, P_char, P_char, P_obj);
void spell_command_horde(int, P_char, P_char, char *);
void spell_command_undead(int, P_char, P_char, char *);
void spell_comprehend_languages(int, P_char, P_char, P_obj);
void spell_cone_of_cold(int, P_char, P_char, P_obj);
void spell_conjure_elemental(int, P_char, P_char, P_obj, char *);
void spell_conjure_snake(int, P_char, P_char, P_obj, char *);
void spell_continual_light(int, P_char, P_char, P_obj);
void spell_control_weather(int, P_char, P_char, P_obj);
void spell_create_food(int, P_char, P_char, P_obj);
void spell_create_spring(int, P_char, P_char, P_obj);
void spell_create_water(int, P_char, P_char, P_obj);
void spell_creeping(int, P_char, P_char, P_obj);
void spell_cure_blind(int, P_char, P_char, P_obj);
void spell_cure_critic(int, P_char, P_char, P_obj);
void spell_cure_light(int, P_char, P_char, P_obj);
void spell_cure_serious(int, P_char, P_char, P_obj);
void spell_curse(int, P_char, P_char, P_obj);
void spell_curse_obj(int, P_char, P_char, P_obj);
void spell_cyclone(int, P_char, P_char, P_obj);
void spell_darkness(int, P_char, P_char, P_obj);
void spell_darkness_breath(int, P_char, P_char, P_obj);
void spell_detect_evil(int, P_char, P_char, P_obj);
void spell_detect_good(int, P_char, P_char, P_obj);
void spell_detect_invisibility(int, P_char, P_char, P_obj);
void spell_detect_magic(int, P_char, P_char, P_obj);
void spell_detect_poison(int, P_char, P_char, P_obj);
void spell_dexterity(int, P_char, P_char, P_obj);
void spell_dimension_door(int, P_char, P_char, P_obj);
void spell_disintegrate(int, P_char, P_char, P_obj);
void spell_dispel_evil(int, P_char, P_char, P_obj);
void spell_dispel_good(int, P_char, P_char, P_obj);
void spell_dispel_invisible(int, P_char, P_char, P_obj);
void spell_dispel_magic(int, P_char, P_char, P_obj);
void spell_earthquake(int, P_char, P_char, P_obj);
void spell_enchant_weapon(int, P_char, P_char, P_obj);
void spell_energy_drain(int, P_char, P_char, P_obj);
void spell_faerie_fire(int, P_char, P_char, P_obj);
void spell_faerie_fog(int, P_char, P_char, P_obj);
void spell_farsee(int, P_char, P_char, P_obj);
void spell_fear(int, P_char, P_char, P_obj);
void spell_feeblemind(int, P_char, P_char, P_obj);
void spell_fire_breath(int, P_char, P_char, P_obj);
void spell_fireball(int, P_char, P_char, P_obj);
void spell_fireshield(int, P_char, P_char, P_obj);
void spell_firestorm(int, P_char, P_char, P_obj);
void spell_flamestrike(int, P_char, P_char, P_obj);
void spell_fly(int, P_char, P_char, P_obj);
void spell_frost_breath(int, P_char, P_char, P_obj);
void spell_full_harm(int, P_char, P_char, P_obj);
void spell_full_heal(int, P_char, P_char, P_obj);
void spell_gas_breath(int, P_char, P_char, P_obj);
void spell_gate(int, P_char, P_char, P_obj);
void spell_globe(int, P_char, P_char, P_obj);
void spell_harm(int, P_char, P_char, P_obj);
void spell_haste(int, P_char, P_char, P_obj);
void spell_heal(int, P_char, P_char, P_obj);
void spell_heal_condense(int, P_char, P_char, P_obj);
void spell_heal_undead(int, P_char, P_char, P_obj);
void spell_holy_word(int, P_char, P_char, P_obj);
void spell_ice_storm(int, P_char, P_char, P_obj);
void spell_identify(int, P_char, P_char, P_obj);
void spell_incendiary_cloud(int, P_char, P_char, P_obj);
void spell_infravision(int, P_char, P_char, P_obj);
void spell_invisibility(int, P_char, P_char, P_obj);
void spell_levitate(int, P_char, P_char, P_obj);
void spell_lightning_bolt(int, P_char, P_char, P_obj);
void spell_lightning_breath(int, P_char, P_char, P_obj);
void spell_locate_object(int, P_char, char *);
void spell_mage_flame(int, P_char, P_char, P_obj);
void spell_magic_missile(int, P_char, P_char, P_obj);
void spell_major_magical_resistance(int, P_char, P_char, P_obj);
void spell_major_paralysis(int, P_char, P_char, P_obj);
void spell_major_physical_resistance(int, P_char, P_char, P_obj);
void spell_mass_charm(int, P_char, P_char, P_obj);
void spell_mass_invisibility(int, P_char, P_char, P_obj);
void spell_meteorswarm(int, P_char, P_char, P_obj);
void spell_minor_creation(int, P_char, P_char, P_obj);
void spell_minor_globe(int, P_char, P_char, P_obj);
void spell_minor_magical_resistance(int, P_char, P_char, P_obj);
void spell_minor_paralysis(int, P_char, P_char, P_obj);
void spell_minor_physical_resistance(int, P_char, P_char, P_obj);
void spell_moonwell(int, P_char, P_char, P_obj);
void spell_plane_shift(int, P_char, P_char, P_obj);
void spell_poison(int, P_char, P_char, P_obj);
void spell_preserve(int, P_char, P_char, P_obj);
void spell_prismatic_spray(int, P_char, P_char, P_obj);
void spell_prismatic_spray_kala(P_char, P_char, const char *);
void spell_prot_from_undead(int, P_char, P_char, P_obj);
void spell_prot_undead(int, P_char, P_char, P_obj);
void spell_protection_from_acid(int, P_char, P_char, P_obj);
void spell_protection_from_cold(int, P_char, P_char, P_obj);
void spell_protection_from_evil(int, P_char, P_char, P_obj);
void spell_protection_from_fire(int, P_char, P_char, P_obj);
void spell_protection_from_gas(int, P_char, P_char, P_obj);
void spell_protection_from_good(int, P_char, P_char, P_obj);
void spell_protection_from_lightning(int, P_char, P_char, P_obj);
void spell_pword_blind(int, P_char, P_char, P_obj);
void spell_pword_kill(int, P_char, P_char, P_obj);
void spell_pword_stun(int, P_char, P_char, P_obj);
void spell_ray_of_enfeeblement(int, P_char, P_char, P_obj);
void spell_recharger(int, P_char, P_char, P_obj);
void spell_rejuvenate_major(int, P_char, P_char, P_obj);
void spell_rejuvenate_minor(int, P_char, P_char, P_obj);
void spell_relocate(int, P_char, P_char, P_obj);
void spell_remove_curse(int, P_char, P_char, P_obj);
void spell_remove_poison(int, P_char, P_char, P_obj);
void spell_resurrect(int, P_char, P_char, P_obj, int);
void spell_sense_life(int, P_char, P_char, P_obj);
void spell_shocking_grasp(int, P_char, P_char, P_obj);
void spell_silence(int, P_char, P_char, P_obj);
void spell_sleep(int, P_char, P_char, P_obj);
void spell_slow(int, P_char, P_char, P_obj);
void spell_slow_poison(int, P_char, P_char, P_obj);
void spell_stone_skin(int, P_char, P_char, P_obj, int);
void spell_strength(int, P_char, P_char, P_obj);
void spell_summon(int, P_char, P_char, P_obj);
void spell_sunray(int, P_char, P_char, P_obj);
void spell_teleport(int, P_char, P_char, P_obj);
void spell_tentacles(int, P_char, P_char, P_obj);
void spell_turn_undead(int, P_char, P_char, P_obj);
void spell_unholy_word(int, P_char, P_char, P_obj);
void spell_vampiric_touch(int, P_char, P_char, P_obj);
void spell_ventriloquate(int, P_char, P_char, P_obj);
void spell_vigorize_critic(int, P_char, P_char, P_obj);
void spell_vigorize_light(int, P_char, P_char, P_obj);
void spell_vigorize_serious(int, P_char, P_char, P_obj);
void spell_vitality(int, P_char, P_char, P_obj);
void spell_vitalize_mana(int, P_char, P_char, P_obj);
void spell_waterbreath(int, P_char, P_char, P_obj);
void spell_wither(int, P_char, P_char, P_obj);
void spell_wizard_eye(int, P_char, P_char, P_obj);
void spell_word_of_recall(int, P_char, P_char, P_obj);
void spell_word_proc(int level, P_char, int, int);
void teleport_to(P_char, int);
void unequip_char_dale(P_obj);
void zone_spellmessage(int, const char *);
void animate_corpse(int, P_char, P_obj, int, int);
void spell_venom(int, P_char, P_char, P_obj);
void spell_totem_darts(int level, P_char ch, P_char victim, P_obj obj);
void spell_spiritknife(int level, P_char ch, P_char victim, P_obj obj);
void spell_jar_the_soul(int level, P_char ch, P_char victim, P_obj obj);
void spell_unleash_fetish(int level, P_char ch, P_char victim, P_obj obj);
void spell_puppet(int level, P_char ch, P_char victim, P_obj obj);
void spell_spirit_wrack(int level, P_char ch, P_char victim, P_obj obj);
void spell_soul_tempest(int level, P_char ch, P_char victim, P_obj obj);
void spell_ancestral_fury(int level, P_char ch, P_char victim, P_obj obj);
void spell_spirit_walk(int level, P_char ch, P_char victim, P_obj obj);
void spell_hex(int level, P_char ch, P_char victim, P_obj obj);
void spell_ancestral_shield(int level, P_char ch, P_char victim, P_obj obj);
void spell_sticks_to_snakes(int level, P_char ch, P_char victim, P_obj obj);
void spell_fire_seeds(int level, P_char ch, P_char victim, P_obj obj);
void spell_shillelagh(int level, P_char ch, P_char victim, P_obj obj);
void spell_summon_insects(int level, P_char ch, P_char victim, P_obj obj);
void spell_flame_blade(int level, P_char ch, P_char victim, P_obj obj);
void spell_silence_person(int level, P_char ch, P_char victim, P_obj obj);
void spell_suffocate(int level, P_char ch, P_char victim, P_obj obj);
void spell_hailstorm(int level, P_char ch, P_char victim, P_obj obj);
void spell_insect_plague(int level, P_char ch, P_char victim, P_obj obj);
void spell_dessicate(int level, P_char ch, P_char victim, P_obj obj);
void spell_entangle(int level, P_char ch, P_char victim, P_obj obj);
void spell_pass_without_trace(int level, P_char ch, P_char victim, P_obj obj);
void spell_dust_devil(int level, P_char ch, P_char victim, P_obj obj);
void rock_to_mud_dissipate_event(void);
void spell_rock_to_mud(int level, P_char ch, P_char victim, P_obj obj);
void mud_to_rock_dissipate_event(void);
void spell_mud_to_rock(int level, P_char ch, P_char victim, P_obj obj);
void spell_goodberry(int level, P_char ch, P_char victim, P_obj obj);
void spell_protection_from_animals(int level, P_char ch, P_char victim, P_obj obj);
void spell_transport_via_plants(int level, P_char ch, P_char victim, P_obj obj);
void spell_ward_undead(int level, P_char ch, P_char victim, P_obj obj);
void spell_destroy_undead(int level, P_char ch, P_char victim, P_obj obj);
void spell_eradicate_undead(int level, P_char ch, P_char victim, P_obj obj);
void spell_annihilate_undead(int level, P_char ch, P_char victim, P_obj obj);
void spell_revive(int level, P_char ch, P_char victim, P_obj obj);
void spell_scry_remains(int level, P_char ch, P_char victim, P_obj obj);
void spell_fumble(int, P_char, P_char, P_obj);
void spell_stumble(int, P_char, P_char, P_obj);
void spell_enervate(int, P_char, P_char, P_obj);
void spell_acid_bolt(int, P_char, P_char, P_obj);
void spell_group_barkskin(int level, P_char ch, P_char victim, P_obj obj);
void spell_greater_mind(int, P_char, P_char, P_obj);
void spell_griffon_aura(int, P_char, P_char, P_obj);

/* memorize.c */

P_obj FindSpellBookWithSpell(P_char, int, int);
P_obj Find_process_entry(P_char, P_obj, int);
P_obj SpellBookAtHand(P_char);
bool can_memorize_spell(P_char, int);
bool meming_class(int);
bool praying_class(int);
int AddSpellToSpellBook(P_char, P_obj, int);
int CountCharEvents(P_char, int);
int GetMaxCircle_char(P_char);
int GetMaxCircle_level(int);
int GetPagesInBook(P_obj);
int GetSpellCircle(P_char, int);
int GetSpellPages(int, int);
int GetTotalPagesInBook(P_obj);
int IS_SEMI_CASTER(int);
int ScribeJobs(void);
int ScribeJobsFree(void);
int ScriberSillyChecks(P_char, int);
int SpellInSpellBook(P_char, int, int);
int SpellInThisSpellBook(struct extra_descr_data *, int);
int SpellInThisSpellBook_p(struct extra_descr_data *, int);
int forget_random_spell(P_char, int);
int has_spells_in_memory(P_char);
int max_spells_in_circle(P_char, int);
int overmem_nuke(P_char, int, int);
int spell_circle_memmage(P_char, int circle);
int spells_memmed_in_circle(P_char, int);
int spells_to_be_memorized_in_circle(P_char, int);
struct extra_descr_data *find_spell_description(P_obj);
struct memorize_data *add_new_memorize_spell(P_char, int);
struct memorize_data *prioritize_spell(P_char, int);
struct scribing_data_type *find_scribe_job_char(P_char);
struct scribing_data_type *find_scribe_job_obj(P_obj);
void AddScribingAffect(P_char);
void NukeCharEventType(P_char, int);
void SetBookNewbySpells(P_char);
void SetSpellCircles(void);
void add_scribe_data(int, P_char, P_obj, int, P_obj, P_char);
void add_scribing(P_char, int, P_obj, int, P_obj, P_char);
void change_spells_in_circle(P_char, int, int);
void check_for_scribe_nukage_object(P_obj);
void delete_memorize_list(P_char, struct memorize_data *);
void delete_memorizing_spell(P_char, struct memorize_data *);
void do_forget(P_char, char *, int);
void do_memorize(P_char, char *, int);
void do_prioritize(P_char, char *, int);
void do_scribe(P_char, char *, int);
void do_teach(P_char, char *, int);
void handle_scribe(P_char, struct scribing_data_type *);
void handle_spell_mem(P_char);
void recount_spells_in_use(P_char);
void scribe_job_done(P_char);
void scribe_job_nuke(P_char);
void scribe_job_nuke_subfunc(struct scribing_data_type *);
void slay_scribe_data(struct scribing_data_type *);
void stop_memorizing(P_char);
void stop_memorizing_silent(P_char);
/* missile.c */

P_char get_char_distance_vis(P_char, const char *, int);
bool IS_GROUPED(P_char, P_char);
bool MISSILE_CAN_GO(int, int);
int Get_Fireweapon_Class(P_obj);
int Get_Fireweapon_Type(P_obj);
int Get_Missile_Type(P_obj);
int AMOUNT_OF_AMMO(P_obj);
int Mob_Archer_Search(P_char);
int Calculate_Hitroll(P_char, P_char, P_obj, P_obj, int, int, int);
int Calculate_Target_AC(P_char);
int Check_Fireweapon_Durability(P_char, P_obj);
int Find_Fire_Direction(char *);
int GetWeaponSkillNumber(P_char, int);
int ITEM_HAS_AFFECT(P_obj, int);
int Identity_ofNext_Ammo(P_char, P_obj, int);
int Missile_Damage(P_char, P_char, P_obj, P_obj, int);
int Missile_Name_Query(P_char, P_obj);
int Missile_Skill(P_char);
int Missile_Weight_Modifier(P_char, P_obj, int);
int Reload_Ammo(P_char, P_obj);
int Snare_Missile(P_char, P_char, P_obj, int);
struct char_data *Missile_In_Flight(P_char, P_char, P_obj, int, int, int, int, int, int, int);
struct obj_data *Break_Missile(P_char, P_obj, int);
struct obj_data *Do_Missile_Decay(P_char, P_obj, int, int);
struct obj_data *Stats_ofNext_Ammo(P_char, P_obj);
struct obj_data *get_missile_in_list_vis(P_char, char *, P_obj);
struct obj_data *stats_ofNext_Ammo(P_char);
void Check_Archer(P_char, int, int);
void Collect_Ammo(P_char, char *, int);
void Cycle_Throwing_Ammo(P_char);
void Directional_Shot(P_char, int, int, int);
void Fire_Missile_Weapon(P_char, char *, char *, char *, int, int, int);
void Initiate_Missile_Combat(P_char, char *, int cmd);
void Melee_Missile_Combat(P_char, P_char);
void Name_Missile(P_char, P_obj);
void OLD_Name_Missile(P_char, P_obj);
void cast_missile_shield(int, P_char, char *, int, P_char, P_obj);
void set_firing(P_char);
void spell_missile_shield(int, P_char, P_char, P_obj);
void stop_firing(P_char);
void Check_MissileShield(P_char, P_char, char *, P_obj);
int Mob_Start_Ranged_Fight(P_char, P_char);

/* mobact.c */

#if TRAPS
int mobact_trapHandle(P_char);
#endif //TRAPS
int char_deserves_helping(const P_char, const P_char);
int no_chars_in_room_deserve_helping(const P_char);
Memory *mem_create(int);
P_char FindDispelTarget(P_char, int);
P_char Juiciest(P_char);
P_char RoomJuiciest(P_char);
P_char PickTarget(P_char);
bool ClassSpecificSpells(P_char ch);
bool CallPsiSkill(P_char, P_char, int, int);
bool UsePsiSkill(P_char, P_char, int);
bool CastClericSpell(P_char, P_char, int);
bool CastShamanSpell(P_char, P_char, int);
bool CastMageSpell(P_char, P_char, int);
bool CastNecroSpell(P_char, P_char, int);
bool CastInvokerSpell(P_char, P_char, int);
bool CastEnchanterSpell(P_char, P_char, int);
bool CastIllusionistSpell(P_char, P_char, int);
bool CastElementalistSpell(P_char, P_char, int);
bool CastLichSpell(P_char, P_char, int);
bool CastDruidSpell(P_char, P_char, int);
bool DemonCombat(P_char);
bool DragonCombat(P_char, int);
bool In_Adjacent_Room(P_char, P_char);
bool InitNewMobHunt(P_char ch);
bool MobCastSpell(P_char, P_char, P_obj, int, int);
bool MobKnowsSpell(P_char, int);
bool MobThief(P_char);
bool MobWarrior(P_char);
bool MobPaladin(P_char, P_char, int);
bool MobAntiPaladin(P_char, P_char, int);
bool MobRanger(P_char, P_char, int);
bool NewMobSpecials(P_char);
bool NewMobAct(P_char);
bool NewMobHunt(void);
bool TryToGetHome(P_char);
bool npc_has_spell_slot(P_char, int);
int AlignRestriction(P_char, P_obj);
int CheckForRemember(P_char);
int CountToughness(P_char, P_char);
int DoesPsiResist(P_char, P_char);
int DoesResist(P_char, P_char);
int FreshCorpse(int);
int GetLowestSpellCircle(int);
int GetLowestSpellCircle_p(int);
int GetMobMagicResistance(P_char);
int IS_PSIONICIST(P_char);
int IS_CLERIC(P_char);
int IS_MAGE(P_char);
int IS_THIEF(P_char);
int IS_WARRIOR(P_char);
int IsBetterObject(P_char, P_obj, int);
int ItemsIn(P_obj);
int MobCanGo(P_char, int);
int PowerCheck(P_char, P_char);
int RateObject(P_char, int, P_obj);
int UndeadCombat(P_char);
int groupLeaderHunt(P_char);
int mem_inMemory(Memory *, char *);
void CheckEqWorthUsing(P_char, P_obj);
void GhostFearEffect(P_char);
void MobCombat(P_char);
void MobHuntCheck(P_char, P_char);
void MobStartFight(P_char, P_char);
void SweepAttack(P_char);
void WingBuffet(P_char);
void memClear(P_char);
void mem_addToMemory(Memory *, char *);
void mem_destroy(Memory *);
void mem_remFromMemory(Memory *, char *);
void mobact_memoryHandle(P_char);
void mobile_activity(void);
void restore_npc_spell(P_char);
void start_npc_spell_mem(P_char, int);
int CantCastSpell(P_char);

/* mobmagic.c */
void spell_ball_of_lightning(int level, P_char ch, P_char victim, P_obj obj);
void lightningball_event(void);
void lightning_ball_explode(void);


/* modify.c */

char *nogames(void);
char *one_word(char *, char *);
int load(void);
int workhours(void);
struct whelp_index_element *build_whelp_index(FILE *, int *);
struct help_index_element *build_help_index(FILE *, int *);
struct info_index_element *build_info_index(FILE *, int *);
void ApplyWriting(struct writing_info *);
void check_reboot(void);
void do_string(P_char, char *, int);
void do_rename(P_char, char *, int);
void gr(int);
void night_watchman(void);
void page_string(P_desc, char *, int);
void quad_arg(char *, int *, char *, int *, char *);
void show_string(P_desc, char *);
void string_add(struct writing_info *, char *);

/* morph.c */

void do_shapechange(P_char, char *, int);
P_char morph(P_char, int, int);
P_char un_morph(P_char);
void switch_followers(P_char, P_char);
void handle_morph_event(P_char);

/* mount.c */

bool check_valid_ride(P_char);
void do_dismount(P_char, char *, int);
void do_mount(P_char, char *, int);
void stop_riding(P_char);

/* nanny.c */

bool _parse_name(char *, char *, P_desc);
bool has_avail_class(P_desc);
bool meets_class_min(P_desc);
bool pfile_exists(const char *, char *);
bool too_many_bads(P_desc);
bool valid_password(P_desc, char *);
int char_quals_for_class(P_char, int);
int display_avail_classes(P_desc, int);
int find_hometown(P_char);
int find_starting_alignment(int, int);
int number_of_players(void);
uint init_law_flags(P_char);
void add_stat_bonus(P_char, int, int);
void create_denied_file(const char *, char *);
void deny_name(char *);
void display_characteristics(P_desc);
void display_stats(P_desc);
void echo_off(P_desc);
void echo_on(P_desc);
void enter_game(P_desc);
void find_starting_location(P_char, int);
void load_obj_to_newbies(P_char);
void nanny(P_desc, char *);
void new_description(P_desc, int, char *);
void newby_announce(P_desc);
void print_recommended_action(P_desc);
void select_alignment(P_desc, char *);
void select_bonus(P_desc, char *);
void select_class(P_desc, char *);
void select_class_info(P_desc, char *);
void select_hometown(P_desc, char *);
void select_keepchar(P_desc, char *);
void select_main_menu(P_desc, char *);
void select_name(P_desc, char *, int);
void select_pwd(P_desc, char *);
void select_race(P_desc, char *);
void select_reroll(P_desc, char *);
void select_sex(P_desc, char *);
void select_terminal(P_desc, char *);
void set_char_size(P_char);
void show_avail_classes(P_desc);
void show_avail_hometowns(P_desc);
void wimps_in_accept_queue(void);
void NPC_enter_game_prep(P_char);
int AddSpellsToSpellBook(P_char ch, P_obj obj, int spl);

/* necro.c */

void spell_lich_curse(int, P_char, P_char, P_obj);
void spell_reconstruction(int, P_char, P_char, P_obj);
void spell_embalm(int, P_char, P_char, P_obj);
void spell_life_drain(int, P_char, P_char, P_obj);
void spell_living_rot(int, P_char, P_char, P_obj);
void spell_lich_touch(int, P_char, P_char, P_obj);
void spell_ice_tomb(int, P_char, P_char, P_obj);
void spell_locate_remains(int, P_char, char *);
void spell_heal_lich(int, P_char, P_char, P_obj);
int iceTombShatter(P_char, P_char);
void livingRot(P_char, P_room);
void do_lich_chill_touch(P_char, char *, int);
void convertNecroToLich(P_char);
int lichConverter(P_char, P_char, int, char *);
void spell_banshee_wail(int, P_char, P_char, P_obj);
void mess_death_pact(P_char, int, int);
void spell_soul_bind(int, P_char, P_char, P_obj);
void spell_death_pact(int, P_char, P_char, P_obj);

/* newmagic.c */

void break_charm_event(P_char);
void spell_fell_frost(int, P_char, P_char, P_obj);
void evards_tentacles_event(void);
void tentacle_event(P_char);
void fiend_break_charm(P_char);
void burn_blood_char_event(P_char);
void burn_char_event(P_char);
void acid_burn_char_event(P_char);
void cold_burn_char_event(P_char);
void airy_dissipate_event(P_char, P_room);
void fog_dissipate_event(P_char, P_room);
void efog_dissipate_event(P_char, P_room);
void efog_dissipate_travel(P_char, P_room);
void ffog_dissipate_event(P_char, P_room);
void ffog_dissipate_travel(P_char, P_room);
void spell_acidstorm(int, P_char, P_char, P_obj);
void spell_sandstorm(int, P_char, P_char, P_obj);
void spell_blacklight_burst(int, P_char, P_char, P_obj);
void spell_blazing_beam(int, P_char, P_char, P_obj);
void spell_thunderblast(int, P_char, P_char, P_obj);
void spell_minute_meteors(int, P_char, P_char, P_obj);
void spell_mordenkains_sword(int, P_char, P_char, P_obj);
void spell_constriction(int, P_char, P_char, P_obj);
void spell_force_missiles(int, P_char, P_char, P_obj);
void spell_inferno(int, P_char, P_char, P_obj);
void spell_blur(int, P_char, P_char, P_obj);
void spell_blink(int, P_char, P_char, P_obj);
void spell_enlarge(int, P_char, P_char, P_obj);
void spell_reduce(int, P_char, P_char, P_obj);
void spell_airy_water(int, P_char, P_char, P_obj);
void spell_repulsion(int, P_char, P_char, P_obj);
void spell_mind_blank(int, P_char, P_char, P_obj);
void spell_solid_fog(int, P_char, P_char, P_obj);
void spell_dragonscales(int, P_char, P_char, P_obj);
void spell_time_stop(int, P_char, P_char, P_obj);
void spell_shield(int, P_char, P_char, P_obj);
void spell_find_familiar(int, P_char, P_char, P_obj);
void spell_monster_summoning(int, P_char, P_char, P_obj);
void spell_unseen_servant(int, P_char, P_char, P_obj);
void spell_summon_mount(int, P_char, P_char, P_obj);
void spell_call_lycanthrope(int, P_char, P_char, P_obj);
void spell_control_fiend(int, P_char, P_char, P_obj);
void spell_minor_horde(int, P_char, P_char, P_obj);
void spell_evards_tentacles(int, P_char, P_char, P_obj);
void spell_pain_touch(int, P_char, P_char, P_obj);
void spell_spectral_hand(int, P_char, P_char, P_obj);
void spell_nerve_dance(int, P_char, P_char, P_obj);
void spell_rain_of_blood(int, P_char, P_char, P_obj);
void spell_grease(int, P_char, P_char, P_obj);
void spell_glitterdust(int, P_char, P_char, P_obj);
void spell_thunder_lance(int, P_char, P_char, P_obj);
void spell_needle_swarm(int, P_char, P_char, P_obj);
void spell_snapping_teeth(int, P_char, P_char, P_obj);
void spell_monste_summoning(int, P_char, P_char, P_obj);
void spell_tazriks_frenzied_hound(int, P_char, P_char, P_obj);
void spell_poltergeist(int, P_char, P_char, P_obj);
void tazrik_event(void);
void spell_dark_wrath(int, P_char, P_char, P_obj);
void spell_unholy_aura(int, P_char, P_char, P_obj);
void spell_holy_shroud(int, P_char, P_char, P_obj);
void spell_beltyns_burning_blood(int, P_char, P_char, P_obj);
void spell_abi_dalzims_horrid_wilting(int, P_char, P_char, P_obj);
void spell_firewave(int, P_char, P_char, P_obj);
void spell_icewave(int, P_char, P_char, P_obj);
void spell_blackmantle(int, P_char, P_char, P_obj);
void spell_vampiric_curse(int, P_char, P_char, P_obj);
void spell_changestaff(int level, P_char ch, P_char victim, P_obj obj);
int check_merge_spells(P_char);
int check_merge_caster(P_char, int);
void junk_memorized_spell(P_char);
int merge_spells(int, int, P_char, P_char, int);
void spell_conflagration(int, P_char, P_char, P_obj);
void merge_spells_messages(P_char, P_char);
void spell_feedback(P_char, P_char);
/* added by Gargauth */
void spell_sandblast(int, P_char, P_char, P_obj);
void spell_miracle(int level, P_char ch, P_char victim, P_obj obj);
void miracle_full_heal(void);
void spell_divine_blessing(int level, P_char ch, P_char victim, P_obj obj);
void spell_faerie_reduce(int level, P_char ch, P_char victim, P_obj obj);
void nuke_time_stop(P_char ch);
void spell_divine_purification(int level, P_char ch, P_char victim, P_obj obj);
int use_spell_component(P_char, int);

/* objtrap.c */

void do_trapset(P_char ch, char *argument, int cmd);
bool checkmovetrap(P_char ch, int dir);
bool checkgetput(P_char ch, P_obj obj);
bool checkopen(P_char ch, P_obj obj);
bool checksearch(P_char ch, P_obj obj);
bool checkpick(P_char ch, P_obj obj);
void trapdamage(P_char ch, P_obj obj);

/* olc.c */

void do_olc(P_char, char *, int);
void direct_olc_return_input(struct olc_data *, char *);
char *olc_prompt(char *, struct olc_data *);
void olc_end(struct olc_data *);
void olc_expert_create(P_char ch, char *);
void olc_expert_end(struct olc_data *);
int olc_save_wld(int zone, P_char);
void olc_expert_del_exit_menu(P_char, int);
void olc_del_exit_menu(struct olc_data *);
void olc_show_menu(struct olc_data *);
void olc_zone_control(P_char, char *, int);
void olc_build_bitflag_menu32(char *str, ubyte *value, const
        char *names[]);
void olc_build_flag_menu8(char *str, ubyte value, const char
        *names[]);

/* olc_room.c */

void olc_room_menu(struct olc_data *data);
void process_olc_room_return_input(struct olc_data *data, char *str);
void olc_room_callback(P_desc, int, char *);
void GetExitInfoText(char *str, unsigned int ExitInfo);
void olc_doorflag_display(struct olc_data *data);
void olc_vector_detail(struct olc_data *data, int);
void olc_room_expert_mode(P_char ch, char *, int);
void do_olc_move(struct olc_data *data, char *);
char *GetOppositeDirectionText(int, char *);
int olc_account_lookup(char *);
int olc_room_name(P_char, int, char *);
int olc_room_delete(P_char, int);
int olc_room_undelete(P_char, int);
int olc_room_desc(P_char, int);
int olc_room_flags(P_char, int);
int olc_room_sectorType(P_char, int, int);
int olc_room_dimensions(P_char, int, int, int);
int olc_room_xtra_descriptions(P_char, int);
int olc_room_exit_vectors(P_char, int, char *);
int olc_room_exit_create(struct olc_data *data, int);
int olc_room_exit_change(struct olc_data *data, char *);
int olc_room_exit_replicate(struct olc_data *data, char *);
int GetDirection(char *);
int GetOppositeDirection(char *);
int olc_mode_room_menu(struct olc_data *data, char *);
int olc_mode_room_name(struct olc_data *data, char *);
int olc_mode_room_flags(struct olc_data *data, char *);
int olc_mode_room_sect(struct olc_data *data, char *);
int olc_mode_room_xtra_menu(struct olc_data *data, char *);
int olc_mode_room_xtra_keywords(struct olc_data *data, char *);
int olc_mode_room_xtra_description(struct olc_data *data, char *);
int olc_mode_room_xtra_del(struct olc_data *data, int);
int olc_mode_room_create(struct olc_data *data, char *);
int olc_mode_room_delete(struct olc_data *data, char *);
int olc_mode_room_undelete(struct olc_data *data, char *);
int olc_mode_room_dimensions(struct olc_data *data, char *);
int olc_mode_room_exit(struct olc_data *data, char *);
int olc_mode_exit(struct olc_data *data, char *);
int olc_mode_exit_delete(struct olc_data *data, char *);
int olc_mode_exit_create(struct olc_data *data, char *);
int olc_mode_exit_zap(struct olc_data *data, char *);
int olc_mode_exit_dimensions(struct olc_data *data, char *);
int olc_mode_exit_description(struct olc_data *data, char *);
int olc_mode_exit_flags(struct olc_data *data, char *);
int olc_mode_exit_keywords(struct olc_data *data, char *);
int olc_mode_exit_keynumber(struct olc_data *data, char *);
int olc_mode_exit_toroom(struct olc_data *data, char *);
int olc_mode_exit_confirmation(struct olc_data *data, char *);
int olc_mode_exit_edit(struct olc_data *data, char *);
int olc_mode_exit_menu(struct olc_data *data, char *);

/* olc_zone.c */

void olc_zone_flags_display(struct olc_data *data);
void olc_zone_menu(struct olc_data *data);
void process_olc_zone_return_input(struct olc_data *data, char *str);

/* olcdb.c */

int Check_OLC_Access(P_char, int, int);
void Read_Write_OLC_Database(int);
void Manage_OLC_Database(P_char, char *, int);
void OLC_Main(P_char, char *, int);
void do_olc_create(P_char, char *, int);
void do_olc_delete(P_char, char *, int);
void do_olc_edit(P_char, char *, int);
void do_olc_makearea(P_char, char *, int);
void do_olc_newarea(P_char, char *, int);
void do_olc_create(P_char, char *, int);
void Show_OLC_Account(int, P_char);
char *ChopOutArgumentParameters(int, char *, char *);
void Update_OLC_Stats(P_char, char *);

/* processlogin.c */

bool ConnectDone(void);
bool GetNewDesc(void);
bool ProcessNewConnect(int); /* (int socket) */
int InitConnectManager(int); /* (int port) */
int newConnection(int);
int newDescriptor(int);

/* quest.c */

int binary_search(int, int, int);
int find_quester_id(int);
int partition(int, int);
int quester(P_char, P_char, int, char *);
void assign_the_questers(void);
void boot_the_quests(void);
void quick_sort_quest_index(int, int);
void tell_quest(int, P_char);
void do_findquest(P_char, char *arg, int);
void do_quest_exit(P_char, char *, int);

/* random.c */

char *initRNGstate(unsigned int, char *, int);
char *setRNGstate(char *);
int erandom(void);
int irand(int);
int lrand(int);
void esrand(unsigned int);
void setrandom(void);

/* ship.c */

bool is_valid_ship(int);
int control_panel(P_obj, P_char, int, char *);
int in_which_ship(P_char);
int navagation(P_char, int, int);
int ship(P_obj, P_char, int, char *);
int ship_exit_room(int, P_char, int, char *);
int ship_look_out_room(int, P_char, int, char *);
void initialize_ships(void);
void ship_activity(void);
void sink_ship(int);

/* signals.c */

void checkpointing(void);
void fork_request(void);
void hupsig(void);
void logsig(void);
void reaper(void);
void shutdown_notice(void);
void shutdown_request(void);
void signal_setup(void);

/* outcast.c */

void AddDeadChar(P_char);
void AddDeadObj(P_obj);
void game_loop(int);
void game_up_message(int);
void run_the_game(int);
void timediff(struct timeval *, struct timeval *, struct timeval *);

/* socials.c */

void boot_the_socials(void);
void free_socials(void);
char* read_social_args(void);
void assign_the_socials(void);
void socials_doAction(P_char, P_char, char *, int, int, char *, int, int);
int performTimedProc(P_char);
int reply_proc(P_char, P_char, int, char *);
int periodic_proc(P_char, P_char, int, char *);
int list_proc(P_char, P_char, int, char *);
int timed_proc(P_char, P_char, int, char *);
int path_proc(P_char, P_char, int, char *);
int countPeriodicListElements(void);
void loadTriggers(void);
void loadPeriodic(void);
void loadPeriodicList(void);
void loadTimed(void);
void loadPath(void);
int findNextMobHeader(void);
void logSocialError(const char *, ...);
void freePeriodic(struct socData_periodic *);
void freeTriggers(struct socData_trigger *);
void freePeriodicList(struct socList *, int);
void socials_zone_indoor_echo(void *);
void socials_zone_outdoor_echo(void *);
void socials_zone_echo(void *);
void socials_room_echo(void *);
//void  socials_char_echo(void *, P_char ch);
void path_trigger_socials(P_char, int, int);
void path_timed_socials(P_char, int, int);

/* sparser.c */

int evadeSucceed(P_char, int);
int FindSpellDamage(P_char, P_char, int, int);
bool NewSaves(P_char, int, int);
bool cast_common(P_char, char *);
bool powercast_common(P_char, char *);
bool cast_common_generic(P_char, int);
bool falling_char(P_char);
bool falling_obj(P_obj);
bool saves_spell(P_char, int);
void skip_spaces(char **string);
int SpareCastStack(void);
int SpellCastChance(P_char, int);
int SpellCastStack(void);
int SpellCastTime(P_char, int);
int find_save(P_char, int);
int kala_spell(int);
int spectype_of_spell(int);
int skilltype_of_spell(int);
int spectype_of_spell(int);
void *ResolveSpellTarget(P_char, int, char **, int *);
void KnockOut(P_char, int);
void NukeRedunantSpellcast(P_char);
void SONG_CREATE(const char *, int, int);
void WARCHANT_CREATE(const char *, int, int);
void SKILL_ADD(int, int, int, int, int);
void SKILL_CREATE(const char *, int, int);
void SPELL_ADD(int, int, int);
void SPELL_CREATE(const char *, int, int, int, int, int, int, int, void (*)(int, P_char, char *, int, P_char, P_obj));
void SpellCastProcess(P_char, struct spellcast_datatype *);
void SpellCastShow(P_char, int);
void StopCasting(P_char);
void affect_update(void);
void assign_spell_pointers(void);
void do_abort(P_char, char *, int);
int do_cast(P_char, char *, int);
void do_powercast(P_char, char *, int);
void nuke_spellcast(struct spellcast_datatype *);
void say_spell(P_char, int);
void short_affect_update(void);
int area_valid_targets(P_char ch);
int AreaSave(P_char, P_char, int *, int);
#define AVOID_AREA 2
void merge_spells_results(P_char, P_char);
int fight_in_room(P_char ch);

/* specs.acheron.c */

void assignAcheronObjects(void);
int acheronEntrancePortal(P_obj, P_char, int, char *);
int acheronPlatformPortal(P_obj, P_char, int, char *);
int acheronRoamingPortal(P_obj, P_char, int, char *);
void do_acheron_exit(P_char, char *, int);
int pkill_portal_evil(P_obj, P_char, int, char *);
int pkill_portal_goods(P_obj, P_char, int, char *);
/* specs.files.c */

int restoreCharacterProcsOnly(P_char);
int writeCharacterProcs(char *, P_char);
int restoreCharacterProcs(char *, P_char);

/* specs.PC.c */

void whoopAssEvent(P_char);
void whoopAss(P_char, P_char);

/* specs.mobile.c */

int assoc_golem(P_char, P_char, int, char *);

/* specs.realm.c */

void break_root_event(P_char);

/* specs.zhentilkeep.c */

void AssignZKMobiles(void);
void AssignZKObjects(void);
void AssignZKRooms(void);

/* specials.c */

void checkHostileSectors(P_char);
void sinkingObject(P_obj);
int calcSinkingSpeed(P_obj);

/* spells.c */

int plane_of_room(int);
void cast_acid_blast(int, P_char, char *, int, P_char, P_obj);
void cast_acid_breath(int, P_char, char *, int, P_char, P_obj);
void cast_acidstorm(int, P_char, char *, int, P_char, P_obj);
void cast_airy_water(int, P_char, char *, int, P_char, P_obj);
void cast_age(int, P_char, char *, int, P_char, P_obj);
void cast_agility(int, P_char, char *, int, P_char, P_obj); /*Moradin*/
void cast_animate_skeleton(int, P_char, char *, int, P_char, P_obj);
void cast_animate_zombie(int, P_char, char *, int, P_char, P_obj);
void cast_animate_spectre(int, P_char, char *, int, P_char, P_obj);
void cast_animate_wraith(int, P_char, char *, int, P_char, P_obj);
void cast_animate_ghoul(int, P_char, char *, int, P_char, P_obj);
void cast_animate_ghast(int, P_char, char *, int, P_char, P_obj);
void cast_animate_ghost(int, P_char, char *, int, P_char, P_obj);
void cast_animate_shadow(int, P_char, char *, int, P_char, P_obj);
void cast_animate_wight(int, P_char, char *, int, P_char, P_obj);
void cast_armor(int, P_char, char *, int, P_char, P_obj);
void cast_awareness(int, P_char, char *, int, P_char, P_obj);
void cast_banshee_wail(int, P_char, char *, int, P_char, P_obj);
void cast_barkskin(int, P_char, char *, int, P_char, P_obj);
void cast_bigbys_clenched_fist(int, P_char, char *, int, P_char, P_obj);
void cast_blackmantle(int, P_char, char *, int, P_char, P_obj);
void cast_blacklight_burst(int, P_char, char *, int, P_char, P_obj);
void cast_blazing_beam(int, P_char, char *, int, P_char, P_obj);
void cast_bless(int, P_char, char *, int, P_char, P_obj);
void cast_natures_blessing(int, P_char, char *, int, P_char, P_obj);
void cast_blindness(int, P_char, char *, int, P_char, P_obj);
void cast_blink(int, P_char, char *, int, P_char, P_obj);
void cast_blur(int, P_char, char *, int, P_char, P_obj);
void cast_burning_hands(int, P_char, char *, int, P_char, P_obj);
void cast_call_lightning(int, P_char, char *, int, P_char, P_obj);
void cast_cause_critical(int, P_char, char *, int, P_char, P_obj);
void cast_cause_light(int, P_char, char *, int, P_char, P_obj);
void cast_cause_serious(int, P_char, char *, int, P_char, P_obj);
void cast_chain_lightning(int, P_char, char *, int, P_char, P_obj);
void cast_charm_person(int, P_char, char *, int, P_char, P_obj);
void cast_chill_touch(int, P_char, char *, int, P_char, P_obj);
void cast_clairvoyance(int, P_char, char *, int, P_char, P_obj);
void cast_coldshield(int, P_char, char *, int, P_char, P_obj);
void cast_color_spray(int, P_char, char *, int, P_char, P_obj);
void cast_command_horde(int, P_char, char *, int, P_char, P_obj);
void cast_command_undead(int, P_char, char *, int, P_char, P_obj);
void cast_comprehend_languages(int, P_char, char *, int, P_char, P_obj);
void cast_cone_of_cold(int, P_char, char *, int, P_char, P_obj);
void cast_conjure_elemental(int, P_char, char *, int, P_char, P_obj);
void cast_continual_light(int, P_char, char *, int, P_char, P_obj);
void cast_control_weather(int, P_char, char *, int, P_char, P_obj);
void cast_create_dracolich(int, P_char, char *, int, P_char, P_obj);
void cast_create_food(int, P_char, char *, int, P_char, P_obj);
void cast_create_spring(int, P_char, char *, int, P_char, P_obj);
void cast_create_water(int, P_char, char *, int, P_char, P_obj);
void cast_creeping(int, P_char, char *, int, P_char, P_obj);
void cast_cure_blind(int, P_char, char *, int, P_char, P_obj);
void cast_cure_critic(int, P_char, char *, int, P_char, P_obj);
void cast_cure_light(int, P_char, char *, int, P_char, P_obj);
void cast_cure_serious(int, P_char, char *, int, P_char, P_obj);
void cast_curse(int, P_char, char *, int, P_char, P_obj);
void cast_curse_obj(int, P_char, char *, int, P_char, P_obj);
void cast_cyclone(int, P_char, char *, int, P_char, P_obj);
void cast_darkness(int, P_char, char *, int, P_char, P_obj);
void cast_darkness_breath(int, P_char, char *, int, P_char, P_obj);
void cast_death_pact(int level, P_char ch, char * arg, int type, P_char victim, P_obj tar_obj);
void cast_detect_evil(int, P_char, char *, int, P_char, P_obj);
void cast_detect_good(int, P_char, char *, int, P_char, P_obj);
void cast_detect_invisibility(int, P_char, char *, int, P_char, P_obj);
void cast_detect_magic(int, P_char, char *, int, P_char, P_obj);
void cast_detect_poison(int, P_char, char *, int, P_char, P_obj);
void cast_dexterity(int, P_char, char *, int, P_char, P_obj);
void cast_dimension_door(int, P_char, char *, int, P_char, P_obj);
void cast_disintegrate(int, P_char, char *, int, P_char, P_obj);
void cast_dispel_evil(int, P_char, char *, int, P_char, P_obj);
void cast_dispel_good(int, P_char, char *, int, P_char, P_obj);
void cast_dispel_invisible(int, P_char, char *, int, P_char, P_obj);
void cast_dragonscales(int, P_char, char *, int, P_char, P_obj);
void cast_time_stop(int, P_char, char *, int, P_char, P_obj);
void cast_dispel_magic(int, P_char, char *, int, P_char, P_obj);
void cast_earthquake(int, P_char, char *, int, P_char, P_obj);
void cast_enchant_weapon(int, P_char, char *, int, P_char, P_obj);
void cast_energy_drain(int, P_char, char *, int, P_char, P_obj);
void cast_enlarge(int, P_char, char *, int, P_char, P_obj);
void cast_faerie_fire(int, P_char, char *, int, P_char, P_obj);
void cast_faerie_fog(int, P_char, char *, int, P_char, P_obj);
void cast_farsee(int, P_char, char *, int, P_char, P_obj);
void cast_fear(int, P_char, char *, int, P_char, P_obj);
void cast_feeblemind(int, P_char, char *, int, P_char, P_obj);
void cast_find_familiar(int, P_char, char *, int, P_char, P_obj);
void cast_fire_breath(int, P_char, char *, int, P_char, P_obj);
void cast_fireball(int, P_char, char *, int, P_char, P_obj);
void cast_fireshield(int, P_char, char *, int, P_char, P_obj);
void cast_firestorm(int, P_char, char *, int, P_char, P_obj);
void cast_flamestrike(int, P_char, char *, int, P_char, P_obj);
void cast_fly(int, P_char, char *, int, P_char, P_obj);
void cast_frost_breath(int, P_char, char *, int, P_char, P_obj);
void cast_full_harm(int, P_char, char *, int, P_char, P_obj);
void cast_full_heal(int, P_char, char *, int, P_char, P_obj);
void cast_gas_breath(int, P_char, char *, int, P_char, P_obj);
void cast_gate(int, P_char, char *, int, P_char, P_obj);
void cast_globe(int, P_char, char *, int, P_char, P_obj);
void cast_group_full_heal(int, P_char, char *, int, P_char, P_obj);
void cast_group_heal(int, P_char, char *, int, P_char, P_obj);
void cast_group_barkskin(int, P_char, char *, int, P_char, P_obj);
void cast_harm(int, P_char, char *, int, P_char, P_obj);
void cast_haste(int, P_char, char *, int, P_char, P_obj);
void cast_heal(int, P_char, char *, int, P_char, P_obj);
void cast_heal_undead(int, P_char, char *, int, P_char, P_obj);
void cast_holy_word(int, P_char, char *, int, P_char, P_obj);
void cast_ice_storm(int, P_char, char *, int, P_char, P_obj);
void cast_identify(int, P_char, char *, int, P_char, P_obj);
void cast_incendiary_cloud(int, P_char, char *, int, P_char, P_obj);
void cast_inferno(int, P_char, char *, int, P_char, P_obj);
void cast_infravision(int, P_char, char *, int, P_char, P_obj);
void cast_invisibility(int, P_char, char *, int, P_char, P_obj);
void cast_levitate(int, P_char, char *, int, P_char, P_obj);
void cast_lightning_bolt(int, P_char, char *, int, P_char, P_obj);
void cast_lightning_breath(int, P_char, char *, int, P_char, P_obj);
void cast_locate_object(int, P_char, char *, int, P_char, P_obj);
void cast_locate_remains(int, P_char, char *, int, P_char, P_obj);
void cast_mage_flame(int, P_char, char *, int, P_char, P_obj);
void cast_magic_missile(int, P_char, char *, int, P_char, P_obj);
void cast_major_magical_resistance(int, P_char, char *, int, P_char, P_obj);
void cast_major_paralysis(int, P_char, char *, int, P_char, P_obj);
void cast_major_physical_resistance(int, P_char, char *, int, P_char, P_obj);
void cast_mass_charm(int, P_char, char *, int, P_char, P_obj);
void cast_mass_invisibility(int, P_char, char *, int, P_char, P_obj);
void cast_meteorswarm(int, P_char, char *, int, P_char, P_obj);
void cast_mind_blank(int, P_char, char *, int, P_char, P_obj);
void cast_minor_creation(int, P_char, char *, int, P_char, P_obj);
void cast_minor_globe(int, P_char, char *, int, P_char, P_obj);
void cast_minor_magical_resistance(int, P_char, char *, int, P_char, P_obj);
void cast_minor_paralysis(int, P_char, char *, int, P_char, P_obj);
void cast_minor_physical_resistance(int, P_char, char *, int, P_char, P_obj);
void cast_minute_meteors(int, P_char, char *, int, P_char, P_obj);
void cast_moonwell(int, P_char, char *, int, P_char, P_obj);
void cast_dimension_shift(int, P_char, char *, int, P_char, P_obj);
void cast_plane_shift(int, P_char, char *, int, P_char, P_obj);
void cast_phantasmal_blades(int, P_char, char *, int, P_char, P_obj);
void cast_poison(int, P_char, char *, int, P_char, P_obj);
void cast_preserve(int, P_char, char *, int, P_char, P_obj);
void cast_prismatic_spray(int, P_char, char *, int, P_char, P_obj);
void cast_prot_from_undead(int, P_char, char *, int, P_char, P_obj);
void cast_prot_undead(int, P_char, char *, int, P_char, P_obj);
void cast_protection_from_acid(int, P_char, char *, int, P_char, P_obj);
void cast_protection_from_cold(int, P_char, char *, int, P_char, P_obj);
void cast_protection_from_evil(int, P_char, char *, int, P_char, P_obj);
void cast_protection_from_fire(int, P_char, char *, int, P_char, P_obj);
void cast_protection_from_gas(int, P_char, char *, int, P_char, P_obj);
void cast_protection_from_good(int, P_char, char *, int, P_char, P_obj);
void cast_protection_from_lightning(int, P_char, char *, int, P_char, P_obj);
void cast_pword_blind(int, P_char, char *, int, P_char, P_obj);
void cast_pword_kill(int, P_char, char *, int, P_char, P_obj);
void cast_pword_stun(int, P_char, char *, int, P_char, P_obj);
void cast_ray_of_enfeeblement(int, P_char, char *, int, P_char, P_obj);
void cast_recharger(int, P_char, char *, int, P_char, P_obj);
void cast_reduce(int, P_char, char *, int, P_char, P_obj);
void cast_rejuvenate_major(int, P_char, char *, int, P_char, P_obj);
void cast_rejuvenate_minor(int, P_char, char *, int, P_char, P_obj);
void cast_relocate(int, P_char, char *, int, P_char, P_obj);
void cast_remove_curse(int, P_char, char *, int, P_char, P_obj);
void cast_remove_poison(int, P_char, char *, int, P_char, P_obj);
void cast_repulsion(int, P_char, char *, int, P_char, P_obj);
void cast_resurrect(int, P_char, char *, int, P_char, P_obj);
void cast_sandstorm(int, P_char, char *, int, P_char, P_obj);
void cast_sense_life(int, P_char, char *, int, P_char, P_obj);
void cast_shield(int, P_char, char *, int, P_char, P_obj);
void cast_shocking_grasp(int, P_char, char *, int, P_char, P_obj);
void cast_silence(int, P_char, char *, int, P_char, P_obj);
void cast_sleep(int, P_char, char *, int, P_char, P_obj);
void cast_slow(int, P_char, char *, int, P_char, P_obj);
void cast_slow_poison(int, P_char, char *, int, P_char, P_obj);
void cast_solid_fog(int, P_char, char *, int, P_char, P_obj);
void cast_soul_bind(int, P_char, char *, int, P_char, P_obj);
void cast_stone_skin(int, P_char, char *, int, P_char, P_obj);
void cast_strength(int, P_char, char *, int, P_char, P_obj);
void cast_summon(int, P_char, char *, int, P_char, P_obj);
void cast_sunray(int, P_char, char *, int, P_char, P_obj);
void cast_teleport(int, P_char, char *, int, P_char, P_obj);
void cast_tentacles(int, P_char, char *, int, P_char, P_obj);
void cast_thunderblast(int, P_char, char *, int, P_char, P_obj);
void cast_turn_undead(int, P_char, char *, int, P_char, P_obj);
void cast_unholy_word(int, P_char, char *, int, P_char, P_obj);
void cast_vampiric_curse(int, P_char, char *, int, P_char, P_obj);
void cast_vampiric_touch(int, P_char, char *, int, P_char, P_obj);
void cast_ventriloquate(int, P_char, char *, int, P_char, P_obj);
void cast_vigorize_critic(int, P_char, char *, int, P_char, P_obj);
void cast_vigorize_light(int, P_char, char *, int, P_char, P_obj);
void cast_vigorize_serious(int, P_char, char *, int, P_char, P_obj);
void cast_vitality(int, P_char, char *, int, P_char, P_obj);
void cast_vitalize_mana(int, P_char, char *, int, P_char, P_obj);
void cast_waterbreath(int, P_char, char *, int, P_char, P_obj);
void cast_wither(int, P_char, char *, int, P_char, P_obj);
void cast_wizard_eye(int, P_char, char *, int, P_char, P_obj);
void cast_word_of_recall(int, P_char, char *, int, P_char, P_obj);
void cast_wraithform(int, P_char, char *, int, P_char, P_obj);
void cast_embalm(int, P_char, char *, int, P_char, P_obj);
void cast_rot(int, P_char, char *, int, P_char, P_obj);
void cast_lich_touch(int, P_char, char *, int, P_char, P_obj);
void cast_life_drain(int, P_char, char *, int, P_char, P_obj);
void cast_ice_tomb(int, P_char, char *, int, P_char, P_obj);
void cast_venom(int, P_char, char *, int, P_char, P_obj);
void cast_beltyns_burning_blood(int, P_char, char *, int, P_char, P_obj);
void cast_abi_dalzims_horrid_wilting(int, P_char, char *, int, P_char, P_obj);
void cast_unseen_servant(int, P_char, char *, int, P_char, P_obj);
void cast_summon_mount(int, P_char, char *, int, P_char, P_obj);
void cast_call_lycanthrope(int, P_char, char *, int, P_char, P_obj);
void cast_control_fiend(int, P_char, char *, int, P_char, P_obj);
void cast_minor_horde(int, P_char, char *, int, P_char, P_obj);
void cast_evards_tentacles(int, P_char, char *, int, P_char, P_obj);
void cast_sandblast(int, P_char, char *, int, P_char, P_obj);
void cast_miracle(int level, P_char ch, char *arg, int type, P_char tar_ch, P_obj tar_obj);
void cast_mordenkains_sword(int, P_char, char *, int, P_char, P_obj);
void cast_force_missiles(int, P_char, char *, int, P_char, P_obj);
void cast_constriction(int, P_char, char *, int, P_char, P_obj);
void cast_pain_touch(int, P_char, char *, int, P_char, P_obj);
void cast_spectral_hand(int, P_char, char *, int, P_char, P_obj);
void cast_nerve_dance(int, P_char, char *, int, P_char, P_obj);
void cast_rain_of_blood(int, P_char, char *, int, P_char, P_obj);
void cast_grease(int, P_char, char *, int, P_char, P_obj);
void cast_glitterdust(int, P_char, char *, int, P_char, P_obj);
void cast_thunder_lance(int, P_char, char *, int, P_char, P_obj);
void cast_needle_swarm(int, P_char, char *, int, P_char, P_obj);
void cast_snapping_teeth(int, P_char, char *, int, P_char, P_obj);
void cast_monster_summoning(int, P_char, char *, int, P_char, P_obj);
//void cast_poltergeist(int, P_char, char *, int, P_char, P_obj);
void cast_poltergeist(int level, P_char ch, char *arg, int type, P_char tar_ch, P_obj tar_obj);
void cast_tazriks_frenzied_hound(int, P_char, char *, int, P_char, P_obj);
void cast_dark_wrath(int, P_char, char *, int, P_char, P_obj);
void cast_unholy_aura(int, P_char, char *, int, P_char, P_obj);
void cast_holy_shroud(int, P_char, char *, int, P_char, P_obj);
void cast_divine_blessing(int, P_char, char *, int, P_char, P_obj);
void cast_fell_frost(int, P_char, char *, int, P_char, P_obj);

void cast_totem_darts(int level, P_char ch, char *arg, int type, P_char victim, P_obj tar_obj);
void cast_spiritknife(int level, P_char ch, char *arg, int type, P_char victim, P_obj tar_obj);
void cast_jar_the_soul(int level, P_char ch, char *arg, int type, P_char victim, P_obj tar_obj);
void cast_unleash_fetish(int level, P_char ch, char *arg, int type, P_char victim, P_obj tar_obj);
void cast_puppet(int level, P_char ch, char *arg, int type, P_char victim, P_obj tar_obj);
void cast_hex(int level, P_char ch, char *arg, int type, P_char victim, P_obj tar_obj);
void cast_soul_tempest(int level, P_char ch, char *arg, int type, P_char victim, P_obj tar_obj);
void cast_spirit_wrack(int level, P_char ch, char *arg, int type, P_char victim, P_obj tar_obj);
void cast_spirit_walk(int level, P_char ch, char *arg, int type, P_char victim, P_obj tar_obj);
void cast_ancestral_shield(int level, P_char ch, char *arg, int type, P_char victim, P_obj tar_obj);
void cast_ancestral_fury(int level, P_char ch, char *arg, int type, P_char victim, P_obj tar_obj);
void cast_goodberry(int level, P_char ch, char *arg, int type, P_char victim, P_obj tar_obj);
void cast_shillelagh(int level, P_char ch, char *arg, int type, P_char victim, P_obj tar_obj);
void cast_protection_from_animals(int level, P_char ch, char *arg, int type, P_char victim, P_obj tar_obj);
void cast_sticks_to_snakes(int level, P_char ch, char *arg, int type, P_char victim, P_obj tar_obj);
void cast_summon_insects(int level, P_char ch, char *arg, int type, P_char victim, P_obj tar_obj);
void cast_dust_devil(int level, P_char ch, char *arg, int type, P_char victim, P_obj tar_obj);
void cast_transport_via_plants(int level, P_char ch, char *arg, int type, P_char victim, P_obj tar_obj);
void cast_suffocate(int level, P_char ch, char *arg, int type, P_char victim, P_obj tar_obj);
void cast_insect_plague(int level, P_char ch, char *arg, int type, P_char victim, P_obj tar_obj);
void cast_changestaff(int level, P_char ch, char *arg, int type, P_char victim, P_obj tar_obj);
void cast_pass_without_trace(int level, P_char ch, char *arg, int type, P_char victim, P_obj tar_obj);
void cast_flame_blade(int level, P_char ch, char *arg, int type, P_char victim, P_obj tar_obj);
void cast_rock_to_mud(int level, P_char ch, char *arg, int type, P_char victim, P_obj tar_obj);
void cast_mud_to_rock(int level, P_char ch, char *arg, int type, P_char victim, P_obj tar_obj);
void cast_fire_seeds(int level, P_char ch, char *arg, int type, P_char victim, P_obj tar_obj);
void cast_hailstorm(int level, P_char ch, char *arg, int type, P_char victim, P_obj tar_obj);
void cast_entangle(int level, P_char ch, char *arg, int type, P_char victim, P_obj tar_obj);
void cast_dessicate(int level, P_char ch, char *arg, int type, P_char victim, P_obj tar_obj);
void cast_revive(int level, P_char ch, char *arg, int type, P_char victim, P_obj tar_obj);
void cast_greater_realm_of_protection(int level, P_char ch, char *arg, int type, P_char victim, P_obj tar_obj);
void cast_ward_undead(int level, P_char ch, char *arg, int type, P_char victim, P_obj tar_obj);
void cast_destroy_undead(int level, P_char ch, char *arg, int type, P_char victim, P_obj tar_obj);
void cast_eradicate_undead(int level, P_char ch, char *arg, int type, P_char victim, P_obj tar_obj);
void cast_annihilate_undead(int level, P_char ch, char *arg, int type, P_char victim, P_obj tar_obj);
void cast_silence_person(int level, P_char ch, char *arg, int type, P_char victim, P_obj tar_obj);
void cast_scry_remains(int level, P_char ch, char *arg, int type, P_char victim, P_obj tar_obj);

void cast_firewave(int, P_char, char *, int, P_char, P_obj);
void cast_icewave(int, P_char, char *, int, P_char, P_obj);
void cast_conflagration(int, P_char, char *, int, P_char, P_obj);

void cast_fumble(int, P_char, char *, int, P_char, P_obj);
void cast_stumble(int, P_char, char *, int, P_char, P_obj);
void cast_enervate(int, P_char, char *, int, P_char, P_obj);
void cast_acid_bolt(int, P_char, char *, int, P_char, P_obj);

void cast_spell_stub(int, P_char, char *, int, P_char, P_obj);

void cast_faerie_reduce(int level, P_char ch, char *arg, int type, P_char tar_ch, P_obj tar_obj);
void area_gheal(P_char, int);
void cast_ball_of_lightning(int level, P_char ch, char *arg, int type, P_char tar_ch, P_obj tar_obj);

void cast_greater_mind(int, P_char, char *, int, P_char, P_obj);
void cast_divine_purification(int, P_char, char *, int, P_char, P_obj);
void cast_griffon_aura(int, P_char, char *, int, P_char, P_obj);
/* utility.c */

void timer_start(void);
void timer_stop(void);
double timer_lap(void);
double timer_elapsed(void);
void drop_commodities(P_char);
int IS_PET(P_char);
int IS_MORPH(P_char);
P_char char_in_room(int);
bool FightingCheck(P_char, P_char, const char *);
bool SanityCheck(P_char, const char *);
bool StatSave(P_char, int, int);
bool are_together(P_char ch1, P_char ch2);
bool has_help(P_char);
bool is_aggr_to(P_char, P_char);
bool is_aggr_to_remote(P_char, P_char);
char *PERS(P_char, P_char);
char *coin_stringv(int);
char *coin_stringv_short(int);
char *full_address(P_desc, int, int);
char *full_hostname(P_desc, int, int);
char *full_username(P_desc, int, int);
char *str_dup(const char *);
const char *MovementWords(P_char, int, int, int);
int BOUNDED(int, int, int);
int MAX(int, int);
int MIN(int, int);
int NumAttackers(P_char);
int STAT_INDEX(int);
int SUB_MONEY(P_char, int, int);
int ac_can_see(P_char, P_char);
int ac_can_see_obj(P_char, P_obj);
int ansi_comp(char *, int);
int coin_type(char *);
int dice(int, int);
int exist_in_equipment(P_char, int);
int fget_line(FILE *, char *, int);
int is_granted(P_char, int);
int move_cost(P_char, int);
int number(int, int);
int same_group(P_char, P_char);
int str_cmp(const char *, const char *);
int str_n_cmp(const char *, const char *);
int strn_cmp(const char *, const char *, uint);
void ADD_MONEY(P_char, int);
void CAP(char *);
void age(struct time_info_data *, P_char);
void banlog(int, const char *, ...);
void connectlog(int, const char *, ...);
void dump_core(void);
void free_string(char *);
void logit(const char *, const char *, ...);
void mud_time_passed(struct time_info_data *, time_t, time_t);
void real_time_passed(struct time_info_data *, time_t, time_t);
void sprint64bit(uint *, const char **, char *);
void sprint_cbit(ubyte *, uint, const char **, char *);
void sprintbit(uint, const char **, char *);
void sprinttype(int, const char **, char *);
void statuslog(int, const char *, ...);
void emslog(int, const char *, ...);
void givelog(int, const char *, ...);
void sql_givelog(int viewlevel, char *giver, int obj_vnum, char *obj_name, char *receiver, int where);
void strToLower(char *);
void strip_tilde(char *);
void update_last_login(P_desc);
void assoclog(int, const char *, ...);
void wizlog(int, const char *, ...);
void wizcmd(int, const char *, ...);
void debuglog(int, int, const char *, ...);
int get_cart_weight(P_obj);
bool has_mount(P_char);
int getNumberOfPets(P_char);
bool is_char_pet(P_char ch, P_char pet);
bool hasAutoSaveEvent(P_char);
bool removeAutoSaveEvent(P_char ch);
unsigned long getMoneyContainedInObj(P_obj);
unsigned long getCarriedMoney(P_char);
struct moneyStruct* getCarriedMoneyStruct(P_char);
void str_free(char *);
int same_race_side(P_char, P_char);

/* weather.c */

void astral_clock(void);
char get_season(int);
void another_hour(void);
void blow_out_torches(void);
void calc_light_zone(int);
void weather_change(void);
void weather_report(int, const char *);
void extreme_weather(int, int);

/* whod.c */

void close_whod(void);
void do_whod(P_char, char *, int);
void init_whod(int);
void whod_loop(void);

/* pkill.c */

int CAN_PKILL(P_char attacker, P_char vict);
int convertPkillHitDamage(int, int);
int adjustSkillDamage(int, int);
void do_loot(P_char, char *, int);
void adjustPKBalance(P_char, char *);
void setEngagedInPk(P_char);
void removeEngagedInPk(P_char);

/* psionic.c */

void do_drain(P_char, char *, int);
void illithid_feeding(P_char, P_char);
int canUsePsionicSkill(P_char, int);
void incSkillSubPsp(P_char, int, int, int);
void bootInitializePSPTable(void);
void psiSkillUsageLogging(P_char, P_char, char *, int);
bool canDrain(P_char, P_char);
int psiDamage(P_char, P_char, int, int);


void do_sense_danger(P_char, char *, int);
void do_vision(P_char, char *, int);
void do_mindblast(P_char, char *, int);
void do_adrenalize(P_char, char *, int);
void do_enhance(P_char, char *, int);
void do_sustain(P_char, char *, int);
void do_disperse(P_char, char *, int);
void do_combatmind(P_char, char *, int);
void do_bodyshift(P_char, char *, int);
void do_amplify(P_char, char *, int);
void do_aurasight(P_char, char *, int);
void do_empathy(P_char, char *, int);
void do_equalibrium(P_char, char *, int);
void do_levitation(P_char, char *, int);
void do_telekinate(P_char, char *, int);
void do_battle_trance(P_char, char *, int);
void do_dominate(P_char, char *, int);
void do_stasis(P_char, char *, int);
void do_rift(P_char, char *, int);
void do_ultrablast(P_char, char *, int);
void do_detonate(P_char, char *, int);
int do_project_force(P_char, char *, int);
void do_death_field(P_char, char *, int);
void do_body_control(P_char, char *, int);
void do_catfall(P_char, char *, int);
void do_flesharmor(P_char, char *, int);
void do_reduction(P_char, char *, int);
void do_expansion(P_char, char *, int);
void do_shift(P_char, char *, int);
void do_mass_domination(P_char, char *, int);
void do_tower_of_iron_will(P_char, char *, int);
void do_attraction(P_char, char *, int);
void do_synaptic_static(P_char, char *, int);
void do_alter_aura(P_char, char *, int);
void do_canibalize(P_char, char *, int);
void do_enhance_skill(P_char, char *, int);
void do_stasis_field(P_char, char *, int);
void do_metaglobe(P_char, char *, int);
void do_globe_of_darkness(P_char, char *, int);
void do_charge(P_char, char *, int);

void nukeChargeEvent(P_char);
void listPSPCrystalCharges(P_char, char *);
void chargePSPCrystal(P_char);
P_obj findPSPCrystal(P_char);
void stopChargingPSPCrystal(P_char, P_obj);
void nukeAllPSPCrystalsExcept(P_char, P_obj, bool);
void eventGlobeOfDarkness(P_char, P_room);
void removeMetaGlobe(P_char ch);
void createStasisSphear(P_char);
int dominate_single(P_char, P_char, int);
int sense_aggros_in_room(P_char, int);
void psionic_enhance_str(P_char);
void psionic_enhance_dex(P_char);
void psionic_enhance_agi(P_char);
void psionic_enhance_con(P_char);
void psionic_enhance_vision(P_char);

/* stats.c */

void mud_info(void);
void mud_info_web(void);
void mud_info_log(void);

/* system.c */

struct statfs *getFreeSpaceOn(char *);
void checkDiscSpace(void);

/* traps.c */

void trigger_trap(P_char, int);
void do_detect_traps(P_char, char *, int);
void do_remove_traps(P_char, char *, int);

/* trade.c */

int get_commodity_value(P_obj, int, struct trade_info *, int, int);
int trade_center(int, P_char, int, char *);
void load_trade_center(struct trade_center*);
void update_trade_center(struct trade_center*);
int initialize_trade(void);
void kill_the_trader(P_char);
int cart(P_obj, P_char, int, char *);
void trade_center_random(struct trade_center*);
int bandit(P_char, P_char, int, char *);
int get_cartvalue(P_char);

/* email_reg.c */

FILE *EMS_Open_Database(FILE *);
FILE *Open_Email_Database(FILE *);
char *GET_EMS_STATUS(char *, int);
char *EMS_File_To_Struct(char *, char *);
char *EMS_NAME(char *);
char *EMS_Lookup_Account(char *, char *);
char *GetTime(char *);
char *GetFileTime(char *);
char *EMS_Create_Output_Text(char *, int, char *, int, char *, int);
char *GetTimeForFileNames(char *);
char *GetTimeFromFileNameTime(char *);
int EMS_Account_To_Struct(char *, int, char *, int);
int EMS_Lookup_Account_Status(char *);
int EMS_Multi_Player(P_desc, int);
int Check_EMS_Bans(char *);
int EMS_Return_Account_Pointer(char *);
int EMS_Restore_Account(int, P_desc, char *);
int EMS_Lookup_Player_Level(char *);
int new_system(const char *);
unsigned int EMS_Increment_Counter(unsigned int);
void Email_Database_Lookup(P_char ch, char *, int);
void EMS_Sojparse_Log(P_char, char *, int);
void EMS_Account_From_Struct(char *);
void EMS_Send_Application(P_desc, char *, int);
void EMS_Process_Mail(void);
void EMS_Return_Notice(char *, char *, char *, int, char *);
void EMS_Bootup(void);
void EMS_Command(P_char, char *, int);
void EMS_Database_Modify(int, char *, int, char *, int);
void EMS_Usage(P_char);
void EMS_Player_File_Modify(char *, int, char *, int);
void EMS_Write_Message(P_char, char *, int);
void EMS_Auto_Disable(void);
void EMS_Setup_Newbie(P_desc, int);
void do_ems(P_char, char *, int);
void do_ems_alias(P_char, char *, int);
void do_ems_list(P_char, char *, char *, char *);
void do_ems_lookup(P_char, char *, char *, char *);
void do_ems_status(P_char, char *);
void do_ems_header(P_char, char *, char *, char *);
void do_ems_records(P_char, char *, char *, char *, char *, char *, char *);
void do_reject(P_char, char *, int);
void do_auto_reset(P_char, char *);
void EMS_Setup_Newbie(P_desc, int);

/* myconid.c */

int EMITTING(P_char);
int myconid_calc_chance(P_char);
int myconid_in_room(P_char, struct affected_type *);
int myconid_saves(P_char, P_char);
int myconid_emit_level(P_char);
void MyconidEffect(P_char);
void MyconidEmit(P_char);
void myconid_soothe(int, P_char, P_char);
void myconid_melding(int, P_char, P_char);
void myconid_hypnotize(int, P_char, P_char);
void myconid_pacify(int, P_char, P_char);
void myconid_pain(int, P_char, P_char);
void myconid_vigor(int, P_char, P_char);
void myconid_distress(int, P_char, P_char);
void do_emit(P_char, char *, int);
void emit_affect_in_room(P_char);
void emit_messages(P_char);
void stop_emitting(P_char);


// Elementalist.c

void convert_conjurer_to_elementalist(P_char);

void spell_air_blast(int, P_char, P_char, P_obj);
void spell_blizzard_sphere(int, P_char, P_char, P_obj);
void spell_earth_darts(int, P_char, P_char, P_obj);
void spell_ice_spear(int, P_char, P_char, P_obj);
void spell_lava_burst(int, P_char, P_char, P_obj);
void spell_slippery_ice(int, P_char, P_char, P_obj);
void spell_whirlwind(int, P_char, P_char, P_obj);
void spell_elemental_water(int, P_char, P_char, P_obj);
void spell_elemental_fire(int, P_char, P_char, P_obj);
void spell_elemental_air(int, P_char, P_char, P_obj);
void spell_elemental_earth(int, P_char, P_char, P_obj);
void spell_elemental_kin(int, P_char, P_char, P_obj);
void spell_elemental_ward(int, P_char, P_char, P_obj);
void spell_ice_tongue(int, P_char, P_char, P_obj);
void spell_earthblood(int, P_char, P_char, P_obj);
void spell_earth_fog(int, P_char, char *);
void spell_fire_fog(int, P_char, char *);

void cast_air_blast(int, P_char, char *, int, P_char, P_obj);
void cast_blizzard_sphere(int, P_char, char *, int, P_char, P_obj);
void cast_earth_darts(int, P_char, char *, int, P_char, P_obj);
void cast_ice_spear(int, P_char, char *, int, P_char, P_obj);
void cast_lava_burst(int, P_char, char *, int, P_char, P_obj);
void cast_slippery_ice(int, P_char, char *, int, P_char, P_obj);
void cast_whirlwind(int, P_char, char *, int, P_char, P_obj);
void cast_elemental_water(int, P_char, char *, int, P_char, P_obj);
void cast_elemental_air(int, P_char, char *, int, P_char, P_obj);
void cast_elemental_fire(int, P_char, char *, int, P_char, P_obj);
void cast_elemental_earth(int, P_char, char *, int, P_char, P_obj);
void cast_elemental_kin(int, P_char, char *, int, P_char, P_obj);
void cast_elemental_ward(int, P_char, char *, int, P_char, P_obj);
void cast_ice_tongue(int, P_char, char *, int, P_char, P_obj);
void cast_earthblood(int, P_char, char *, int, P_char, P_obj);
void cast_earth_fog(int, P_char, char *, int, P_char, P_obj);
void cast_fire_fog(int, P_char, char *, int, P_char, P_obj);

#ifdef NEW_BARD
/* newbard.c */
void do_song(P_char ch, char *arg, int cmd, int mode);
void create_song(P_char ch);
bool refresh_bard_list(P_char ch);
void remove_from_song(P_char ch, P_char target);
void clear_list_member(P_song_element member);
void add_list_member(P_song_element member, P_char ch, P_char target);
void kill_song_list(P_char ch);
void kill_song(P_char ch);
int check_bard_stutter(P_char ch);
P_char check_accompany(P_char ch);
void do_accompany(P_char ch, char *arg, int cmd);
int song_quality(P_char ch);
void check_bard_virtuoso(P_char ch);
void song_of_healing(P_char ch);
void song_of_offensive_harmony(P_char ch);
bool affected_by_song(P_char ch, int songtype);
void song_of_offensive_disruption(P_char ch);
void song_of_defensive_harmony(P_char ch);
void song_of_defensive_disruption(P_char ch);
void song_of_miscast_magic(P_char ch);
void song_of_sorcery(P_char ch);
void song_of_revelation(P_char ch);
void song_of_protection(P_char ch);
void song_of_harming(P_char ch);
void song_of_travel(P_char ch);
void song_of_renewal(P_char ch);
void song_of_the_elements(P_char ch);
void song_of_recovery(P_char ch);
void refresh_bard_spells(P_char ch, char *arg, int argSize);
void kill_song_message(P_char ch);
void bard_stop_singing(P_char ch);
void cast_resurrection_recovery(int, P_char, char *, int, P_char, P_obj);
#endif

/* These following ones will end up in songs.c */
void song_of_regeneration(P_char ch);

// storage.c
void do_storage(P_char, char *, int);
P_obj get_cache_in_room(P_char);
P_obj get_cache_remote(P_char, P_char);
void storage_create(P_char);
void storage_list(P_char, char *);
bool VerifyStorage(P_char);
bool VerifyStorageRemote(P_char, P_char);
void restoreStorageCache(P_char, P_char);
void writeStorageCache(P_obj);
void storage_store(P_char, char *);
void storage_access(P_char, char *);
void storage_retrieve(P_char, char *);
P_obj storage_close(P_char);

#endif /* _SOJ_PROTOTYPES_H_ */
