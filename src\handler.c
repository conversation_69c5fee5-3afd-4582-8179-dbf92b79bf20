/* ***************************************************************************
 *  file: handler.c                                            part of Outcast *
 *  usage: various routines for moving about objects/players.                *
 *  copyright  1990, 1991 - see 'license.doc' for complete information.      *
 *  copyright  1994, 1995 - outcast systems ltd.                             *
 *************************************************************************** */

#include <ctype.h>
#include <stdio.h>
#include <string.h>
#include <time.h>

#include "comm.h"
#include "db.h"
#include "events.h"
#ifdef NEWJUSTICE
   #include "newjustice.h"
#endif
#ifdef OLDJUSTICE
   #include "justice.h"
#endif
#include "mm.h"
#include "prototypes.h"
#include "race_class.h"
#include "skillrec.h"
#include "specs.include.h"
#include "specs.prototypes.h"
#include "spells.h"
#include "utils.h"
#include "weather.h"
#include "mmail.h"

/* external variables */

extern P_char NPC_list;
extern P_char PC_list;
extern bool pclist_debug;
extern const char *connected_types[];
extern P_char combat_list;
extern P_char dead_guys;
extern P_desc descriptor_list;
extern P_event current_event;
extern P_event event_type_list[];
extern P_index mob_index;
extern P_index obj_index;
extern P_obj object_list;
extern P_group group_list;
extern P_room world;
#ifdef ARTIFACT
extern struct artifact_data *art_index;
#endif
extern char *coin_names[];
extern const struct stat_data stat_factor[];
extern const struct trait_data racial_traits[];
extern int PC_count;
extern int innate_abilities[];
extern int rev_dir[];
extern int top_of_world;
extern struct con_app_type con_app[];
extern struct dex_app_type dex_app[];
extern struct mm_ds *dead_char_pool;
extern struct mm_ds *dead_pc_pool;
extern struct zone_data *zone_table;
extern int racial_mods[][2];
#ifdef ARTIFACT
extern totalArtifacts;
FILE *art_f;
#endif

/* static function protoypes */
static void PurgeCorpseFile(P_obj);

struct mm_ds *dead_affect_pool = NULL;

static void PurgeCorpseFile(P_obj corpse)
{
   char *tmp, Gbuf1[MAX_STRING_LENGTH], Gbuf2[MAX_STRING_LENGTH];
   int t_stamp;
   char test[MAX_STRING_LENGTH];

   if(!corpse || (corpse->type != ITEM_CORPSE) || (corpse->value[1] != PC_CORPSE))
      dump_core();

   if(sscanf(corpse->action_description, " %d %s ", &t_stamp, Gbuf1) != 2)
      {
      strcpy(Gbuf1, corpse->action_description);
      }
   else
      {
      strcpy(test, corpse->action_description);
      if(test[10] == ' ')
         strcpy(Gbuf1, (corpse->action_description + 11));
      else
         strcpy(Gbuf1, (corpse->action_description + 10));
      }

   sprintf(Gbuf2, "%s%d", Gbuf1, corpse->value[3]);
   for(tmp = Gbuf2; *tmp; tmp++)
      *tmp = LOWER(*tmp);

   sprintf(Gbuf1, "%s/Corpses/%s", SAVE_DIR, Gbuf2);
   strcpy(Gbuf2, Gbuf1);
   strcat(Gbuf2, ".bak");

   unlink(Gbuf1);
   unlink(Gbuf2);

   return;
}

/* ok, currently, rooms are of fixed size, and a single torch is enough to illuminate
   any room.  This, needless to say, sucks.  However, doing anything about it will
   be hard as hell, so we live with it for now.  Light will work like this:
   1- if there is a light source in the room, room is lit.
   2- if there is a 'dark' source in the room, room is pitch black.
   3- this is partially overruled by darkness not being permanent, ever.

   You can cast darkness on an object, which will last until the item is rented,
   or destroyed, or a god casts continual light on it, or it wears off (darkness
   on an object starts an event).  You can cast it on a char, and it sets an affect
   that will wear off, or be dispelled like any other magic affect.
   You can cast it in a room, and it will last til it wears off, or a continual
   light negates it.  The DARK flag on objects is NEVER saved, which is why it
   wears off when rented.
 */

/* recalculate (and return) the amount of light ch is emitting */

int char_light(P_char ch)
{
   P_obj t_obj = NULL;
   int i, amt = 0, dark = 0, mf_l;
   struct affected_type *af;

   if(!ch)
      return -1;

   if(IS_AFFECTED(ch, AFF_MAGE_FLAME))
      {
      mf_l = 0;

      /* first, check if spell has been cast, and base level of light on
         that.  if not, assume artifact or whatnot and use vict level */

      for(af = ch->affected; af; af = af->next)
         {
         if((af->type == SPELL_MAGE_FLAME) &&
            (af->modifier > 0))
            {
            mf_l = af->modifier / 10;
            }
         }

      if(!mf_l)
         mf_l = GET_LEVEL(ch) / 10;

      amt += (mf_l + 3);
      }

   for(i = 0; i < MAX_WEAR; i++)
      if(ch->equipment[i])
         {
         if((ch->equipment[i]->type == ITEM_LIGHT) &&
            (((i >= WIELD) && (i <= HOLD) && ch->equipment[i]->value[2]) ||
             (ch->equipment[i]->value[2] < 0)))
            amt += 2;

         if(IS_SET(ch->equipment[i]->extra_flags, ITEM_LIT))
            amt += 8;
         }
      /* yup inven (surface layer anyway) counts */
   for(t_obj = ch->carrying; !dark && t_obj; t_obj = t_obj->next_content)
      {
      if(IS_SET(t_obj->extra_flags, ITEM_LIT))
         amt += 4;
      if((t_obj->type == ITEM_LIGHT) && (t_obj->value[2] == -1))
         amt += 1;
      }

   if(GET_RACE(ch) == RACE_F_ELEMENTAL)
      amt += 4;

   if(dark)
      amt = -1;
   if(amt > 127)
      amt = 127;

   i = ch->light;
   ch->light = BOUNDED(-1, amt, 127);

   if(ch->light != i)
      room_light(ch->in_room, REAL);

   return ch->light;
}

/* recalculate (and return) the amount of light in a room */

int room_light(int room_nr, int flag)
{
   P_char t_ch = NULL, victim, tmp2;
   P_obj t_obj = NULL;
   int amt = 0, dark = 0, rroom = -1, i;

   if(room_nr < 0)
      return -1;

   if(flag == REAL)
      rroom = room_nr;
   else if(room_nr < top_of_world)
      rroom = real_room(room_nr);
   else
      return -1;

   if(rroom == NOWHERE)
      return -1;

   if(IS_CSET(world[rroom].room_flags, MAGIC_DARK))
      {
      world[rroom].light = -1;
      return -1;
      }
   if(IS_CSET(world[rroom].room_flags, MAGIC_LIGHT))
      amt += 4;

   if(world[rroom].sector_type == SECT_FIREPLANE)
      amt += 4;

   if((world[rroom].sector_type == SECT_ASTRAL) ||
      (world[rroom].sector_type == SECT_ETHEREAL))
      amt += 4;

   for(t_ch = world[rroom].people; t_ch && !dark; t_ch = t_ch->next_in_room)
      if(t_ch->light == -1)
         dark = 1;
      else
         amt += t_ch->light;

   /* lit items in room count */
   for(t_obj = world[rroom].contents; !dark && t_obj; t_obj = t_obj->next_content)
      {
      if(IS_SET(t_obj->extra_flags, ITEM_LIT))
         amt += 4;
      if((t_obj->type == ITEM_LIGHT) && (t_obj->value[2] == -1))
         amt += 1;
      }

   /* have to do something about ambient (sun) light, not sure what yet */

   if(dark)
      amt = -1;

   i = world[rroom].light;
   world[rroom].light = BOUNDED(-1, amt, 127);

   if(world[rroom].people && (i < 1) && (amt > 0) && !ALONE(world[rroom].people))
      {
      /* room just lit up, let's give the mobs a chance to jump things, this will mostly negate the
         gra torch/look/rem torch strategy, as mobs will tend to jump you when you light things up.
         MuHAHA!  JAB */

      for(t_ch = world[rroom].people; t_ch; t_ch = tmp2)
         {
         tmp2 = t_ch->next_in_room;
         if(IS_NPC(t_ch) && AWAKE(t_ch) && !IS_FIGHTING(t_ch) && MIN_POS(t_ch, POS_STANDING + STAT_NORMAL) &&
            (victim = PickTarget(t_ch)) && is_aggr_to(t_ch, victim))
            AddEvent(EVENT_AGG_ATTACK, number(1, 5), TRUE, t_ch, victim);
         }
      }
   return world[rroom].light;
}

char *FirstWord(char *namelist)
{
   register char *point;
   static char holder[1024];

   /* Added by DTS 7/9/95 */
   if(!namelist)
      return(NULL);

   for(point = holder; isalpha(*namelist); namelist++, point++)
      {
      *point = *namelist;
      /* make sure we wont overflow */
      if(point - holder >= 1024)
         dump_core();
      }

   *point = '\0';

   return(holder);
}

bool isname(const char *str, const char *namelist)
{
   char tstr[MAX_STRING_LENGTH];
   int i = 0, j = 0, k = 0, k2 = 0;

   if(!str || !namelist)
      return FALSE;

   /* eat leading spaces in str */
   while(*(str + k) == ' ')
      k++;

   if(!*(str + k))
      return FALSE;

   /* lowercase the search string now, so we don't have to do it over and over again when we look. JAB */

   for(k2 = 0; *(str + k); k++)
      tstr[k2++] = LOWER(*(str + k));
   tstr[k2] = 0;

   for(;;)
      {
      for(i = 0;; i++, j++)
         {
         if(!tstr[i])
            {
            if(!*(namelist + j) || (*(namelist + j) == ' '))
               return TRUE;
            break;
            }
         else
            {
            if(!*(namelist + j))
               return FALSE;

            if(tstr[i] != LOWER(*(namelist + j)))
               break;
            }
         }

      /* skip to next name */

      for(; *(namelist + j) && (*(namelist + j) != ' '); j++);
      if(!*(namelist + j))
         return FALSE;
      j++;                        /* first char of new name */
      }
   return FALSE;
}

/* complete rewriting of the affect handler routines.  Reason?  Several smallish problems with how affects
   are applied.  New method:  All changes are made to dummy variables, then the summation of all changes
   is applied to the actual character.  Thus if 10 items/spells change strength up/down, only the net effect
   gets applied.  This also allows the new STAT_MAX and RACIAL_STAT affects to work properly.  JAB */

struct hold_data
   {
   int c_Str, c_Dex, c_Agi, c_Con, c_Pow, c_Int, c_Wis, c_Cha, c_Kar, c_Luc;
   int m_Str, m_Dex, m_Agi, m_Con, m_Pow, m_Int, m_Wis, m_Cha, m_Kar, m_Luc;
   int r_Str, r_Dex, r_Agi, r_Con, r_Pow, r_Int, r_Wis, r_Cha, r_Kar, r_Luc;
   int AC, Age, Dam, Hit, Hits, CurHits, Fprot, Move, Mana, Height, Weight, MR;
   int S_spell, S_para, S_petri, S_rod, S_breath;
   ubyte BitVecs[AFF_BYTES];
   ubyte BitVecs2[AFF_BYTES];
   } TmpAffs;

/* this routine actually applies the summarized affects to the character.  All sanity checking is done here.
   By breaking it out we can exercise very discrete control over what is an isn't legal.
   mode:  TRUE  - apply TmpAffs.
   FALSE - reset the values.

   JAB */

void apply_affs(P_char ch, int mode)
{
   int t1, t2, t3, t4 = 100;
   struct affected_type *af;

   /* for stats, it just flat out recalcs them, no +/- about it, safer that way */

   if(IS_CSET(TmpAffs.BitVecs, AFF_RES_PENALTY))
      for(af = ch->affected; af; af = af->next)
         if((af->type == SKILL_RES_PENALTY) && (af->duration > 0))
            {
            t4 -= (MIN(12, af->duration) * 8);
            break;
            }

   t1 = (!mode || !TmpAffs.r_Str) ? (int) GET_RACE(ch) : TmpAffs.r_Str;
   t3 = (mode) ? (t4 + TmpAffs.m_Str) : t4;
   if(t4 == 100)
      t2 = BOUNDED(1, (ch->base_stats.Str + ((mode) ? TmpAffs.c_Str : 0)), t3);
   else
      t2 = BOUNDED(1, (ch->base_stats.Str * t4 / 100 + ((mode) ? TmpAffs.c_Str : 0)), t3);
   GET_C_STR(ch) = BOUNDED(0, (int) (stat_factor[t1].Str * t2 / 100. + .55), 511);

   t1 = (!mode || !TmpAffs.r_Dex) ? (int) GET_RACE(ch) : TmpAffs.r_Dex;
   t3 = (mode) ? (t4 + TmpAffs.m_Dex) : t4;
   if(t4 == 100)
      t2 = BOUNDED(1, (ch->base_stats.Dex + ((mode) ? TmpAffs.c_Dex : 0)), t3);
   else
      t2 = BOUNDED(1, (ch->base_stats.Dex * t4 / 100 + ((mode) ? TmpAffs.c_Dex : 0)), t3);
   GET_C_DEX(ch) = BOUNDED(0, (int) (stat_factor[t1].Dex * t2 / 100. + .55), 511);

   t1 = (!mode || !TmpAffs.r_Agi) ? (int) GET_RACE(ch) : TmpAffs.r_Agi;
   t3 = (mode) ? (t4 + TmpAffs.m_Agi) : t4;
   if(t4 == 100)
      t2 = BOUNDED(1, (ch->base_stats.Agi + ((mode) ? TmpAffs.c_Agi : 0)), t3);
   else
      t2 = BOUNDED(1, (ch->base_stats.Agi * t4 / 100 + ((mode) ? TmpAffs.c_Agi : 0)), t3);
   GET_C_AGI(ch) = BOUNDED(0, (int) (stat_factor[t1].Agi * t2 / 100. + .55), 511);

   t1 = (!mode || !TmpAffs.r_Con) ? (int) GET_RACE(ch) : TmpAffs.r_Con;
   t3 = (mode) ? (t4 + TmpAffs.m_Con) : t4;
   if(t4 == 100)
      t2 = BOUNDED(1, (ch->base_stats.Con + ((mode) ? TmpAffs.c_Con : 0)), t3);
   else
      t2 = BOUNDED(1, (ch->base_stats.Con * t4 / 100 + ((mode) ? TmpAffs.c_Con : 0)), t3);
   GET_C_CON(ch) = BOUNDED(0, (int) (stat_factor[t1].Con * t2 / 100. + .55), 511);

   t1 = (!mode || !TmpAffs.r_Pow) ? (int) GET_RACE(ch) : TmpAffs.r_Pow;
   t3 = (mode) ? (t4 + TmpAffs.m_Pow) : t4;
   if(t4 == 100)
      t2 = BOUNDED(1, (ch->base_stats.Pow + ((mode) ? TmpAffs.c_Pow : 0)), t3);
   else
      t2 = BOUNDED(1, (ch->base_stats.Pow * t4 / 100 + ((mode) ? TmpAffs.c_Pow : 0)), t3);
   GET_C_POW(ch) = BOUNDED(0, (int) (stat_factor[t1].Pow * t2 / 100. + .55), 511);

   t1 = (!mode || !TmpAffs.r_Int) ? (int) GET_RACE(ch) : TmpAffs.r_Int;
   t3 = (mode) ? (t4 + TmpAffs.m_Int) : t4;
   if(t4 == 100)
      t2 = BOUNDED(1, (ch->base_stats.Int + ((mode) ? TmpAffs.c_Int : 0)), t3);
   else
      t2 = BOUNDED(1, (ch->base_stats.Int * t4 / 100 + ((mode) ? TmpAffs.c_Int : 0)), t3);
   GET_C_INT(ch) = BOUNDED(0, (int) (stat_factor[t1].Int * t2 / 100. + .55), 511);

   t1 = (!mode || !TmpAffs.r_Wis) ? (int) GET_RACE(ch) : TmpAffs.r_Wis;
   t3 = (mode) ? (t4 + TmpAffs.m_Wis) : t4;
   if(t4 == 100)
      t2 = BOUNDED(1, (ch->base_stats.Wis + ((mode) ? TmpAffs.c_Wis : 0)), t3);
   else
      t2 = BOUNDED(1, (ch->base_stats.Wis * t4 / 100 + ((mode) ? TmpAffs.c_Wis : 0)), t3);
   GET_C_WIS(ch) = BOUNDED(0, (int) (stat_factor[t1].Wis * t2 / 100. + .55), 511);

   t1 = (!mode || !TmpAffs.r_Cha) ? (int) GET_RACE(ch) : TmpAffs.r_Cha;
   t3 = (mode) ? (t4 + TmpAffs.m_Cha) : t4;
   if(t4 == 100)
      t2 = BOUNDED(1, (ch->base_stats.Cha + ((mode) ? TmpAffs.c_Cha : 0)), t3);
   else
      t2 = BOUNDED(1, (ch->base_stats.Cha * t4 / 100 + ((mode) ? TmpAffs.c_Cha : 0)), t3);
   GET_C_CHA(ch) = BOUNDED(0, (int) (stat_factor[t1].Cha * t2 / 100. + .55), 511);

   t1 = (!mode || !TmpAffs.r_Kar) ? (int) GET_RACE(ch) : TmpAffs.r_Kar;
   t3 = (mode) ? (t4 + TmpAffs.m_Kar) : t4;
   if(t4 == 100)
      t2 = BOUNDED(1, (ch->base_stats.Karma + ((mode) ? TmpAffs.c_Kar : 0)), t3);
   else
      t2 = BOUNDED(1, (ch->base_stats.Karma * t4 / 100 + ((mode) ? TmpAffs.c_Kar : 0)), t3);
   GET_C_KARMA(ch) = BOUNDED(0, (int) (stat_factor[t1].Karma * t2 / 100. + .55), 511);

   t1 = (!mode || !TmpAffs.r_Luc) ? (int) GET_RACE(ch) : TmpAffs.r_Luc;
   t3 = (mode) ? (t4 + TmpAffs.m_Luc) : t4;
   if(t4 == 100)
      t2 = BOUNDED(1, (ch->base_stats.Luck + ((mode) ? TmpAffs.c_Luc : 0)), t3);
   else
      t2 = BOUNDED(1, (ch->base_stats.Luck * t4 / 100 + ((mode) ? TmpAffs.c_Luc : 0)), t3);
   GET_C_LUCK(ch) = BOUNDED(0, (int) (stat_factor[t1].Luck * t2 / 100. + .55), 511);

   /* note that the current stats now show the ACTUAL stat, including racial factors, this is much better
      than the old method, fewer recalcs.  Only practical difference, the stat section of do_attributes()
      needs to change.  JAB */

   ch->specials.apply_saving_throw[SAVING_PARA] = (mode) ? TmpAffs.S_para : 0;
   ch->specials.apply_saving_throw[SAVING_ROD] = (mode) ? TmpAffs.S_rod : 0;
   ch->specials.apply_saving_throw[SAVING_PETRI] = (mode) ? TmpAffs.S_petri : 0;
   ch->specials.apply_saving_throw[SAVING_SPELL] = (mode) ? TmpAffs.S_spell : 0;
   ch->specials.apply_saving_throw[SAVING_BREATH] = (mode) ? TmpAffs.S_breath : 0;

   GET_AC(ch) = ch->points.base_armor + ((mode) ? TmpAffs.AC : 0);
   ch->points.damroll = ch->points.base_damroll + ((mode) ? TmpAffs.Dam : 0);
   ch->points.hitroll = ch->points.base_hitroll + ((mode) ? TmpAffs.Hit : 0);

   ch->points.height = ch->points.base_height + ((mode) ? TmpAffs.Height : 0);
   ch->points.weight = ch->points.base_weight + ((mode) ? TmpAffs.Weight : 0);

   ch->points.magic_resistance = ch->points.base_magic_resistance + ((mode) ? TmpAffs.MR : 0);

   t1 = GET_MAX_HIT(ch) - GET_HIT(ch);
   GET_MAX_HIT(ch) = hit_limit(ch) + ((mode) ? TmpAffs.Hits : 0);

   GET_HIT(ch) += ((mode) ? TmpAffs.CurHits : 0);

   t1 = GET_MAX_MOVE(ch) - GET_MOVE(ch);
   GET_MAX_MOVE(ch) = move_limit(ch) + ((mode) ? TmpAffs.Move : 0);

   t1 = GET_MAX_MANA(ch) - GET_MANA(ch);
   GET_MAX_MANA(ch) = mana_limit(ch) + ((mode) ? TmpAffs.Mana : 0);


   ch->player.time.age_mod = (mode) ? TmpAffs.Age : 0;

   if(mode)
      {
      ADD_CBITS(ch->specials.affects, TmpAffs.BitVecs, AFF_BYTES);

      if(TmpAffs.Fprot)
         SET_CBIT(ch->specials.affects, AFF_PROT_FIRE);
      }
   else
      {
      if(TmpAffs.Fprot)
         REMOVE_CBIT(ch->specials.affects, AFF_PROT_FIRE);

      REMOVE_CBITS(ch->specials.affects, TmpAffs.BitVecs, AFF_BYTES);
      }

   /* ok, some innate powers just set bits, so we need to reset those */

   if(racial_traits[GET_RACE(ch)].infravision)
      SET_CBIT(ch->specials.affects, AFF_INFRAVISION);
   if((racial_traits[GET_RACE(ch)].ultravision) && (GET_CLASS(ch) != CLASS_LICH))
      SET_CBIT(ch->specials.affects, AFF_ULTRAVISION);
   if(HAS_INNATE(ch, INNATE_INFRAVISION))
      SET_CBIT(ch->specials.affects, AFF_INFRAVISION);
   if(HAS_INNATE(ch, INNATE_ANTI_GOOD))
      {
      SET_CBIT(ch->specials.affects, AFF_PROTECT_GOOD);
      SET_CBIT(ch->specials.affects, AFF_DETECT_GOOD);
      }
   if(HAS_INNATE(ch, INNATE_ANTI_EVIL))
      {
      SET_CBIT(ch->specials.affects, AFF_PROTECT_EVIL);
      SET_CBIT(ch->specials.affects, AFF_DETECT_EVIL);
      }
}

/* basically just a huge switch() to alter lots of variables in hold_data, called only from all_affects().
   JAB */

void affect_modify(int loc, int mod, ubyte *bitv)
{
   if(bitv)
      ADD_CBITS(TmpAffs.BitVecs, bitv, AFF_BYTES);

   switch(loc)
      {

      case APPLY_NONE:
         break;

      case APPLY_AGI_MAX:
         TmpAffs.m_Agi += mod;
         /* fall through, _MAX also affects current */
      case APPLY_AGI:
         TmpAffs.c_Agi += mod;
         break;

      case APPLY_CHA_MAX:
         TmpAffs.m_Cha += mod;
         /* fall through, _MAX also affects current */
      case APPLY_CHA:
         TmpAffs.c_Cha += mod;
         break;

      case APPLY_CON_MAX:
         TmpAffs.m_Con += mod;
         /* fall through, _MAX also affects current */
      case APPLY_CON:
         TmpAffs.c_Con += mod;
         break;

      case APPLY_DEX_MAX:
         TmpAffs.m_Dex += mod;
         /* fall through, _MAX also affects current */
      case APPLY_DEX:
         TmpAffs.c_Dex += mod;
         break;

      case APPLY_INT_MAX:
         TmpAffs.m_Int += mod;
         /* fall through, _MAX also affects current */
      case APPLY_INT:
         TmpAffs.c_Int += mod;
         break;

      case APPLY_KARMA_MAX:
         TmpAffs.m_Kar += mod;
         /* fall through, _MAX also affects current */
      case APPLY_KARMA:
         TmpAffs.c_Kar += mod;
         break;

      case APPLY_LUCK_MAX:
         TmpAffs.m_Luc += mod;
         /* fall through, _MAX also affects current */
      case APPLY_LUCK:
         TmpAffs.c_Luc += mod;
         break;

      case APPLY_POW_MAX:
         TmpAffs.m_Pow += mod;
         /* fall through, _MAX also affects current */
      case APPLY_POW:
         TmpAffs.c_Pow += mod;
         break;

      case APPLY_STR_MAX:
         TmpAffs.m_Str += mod;
         /* fall through, _MAX also affects current */
      case APPLY_STR:
         TmpAffs.c_Str += mod;
         break;

      case APPLY_WIS_MAX:
         TmpAffs.m_Wis += mod;
         /* fall through, _MAX also affects current */
      case APPLY_WIS:
         TmpAffs.c_Wis += mod;
         break;

         /* tricky, only the WORST 'magic' modifier applies, so we have to check.  JAB */

      case APPLY_STR_RACE:
         if((mod <= RACE_NONE) || (mod > LAST_RACE))
            {
            logit(LOG_DEBUG, "affect_modify(): unknown race (%d) for APPLY_STR_RACE.", loc);
            break;
            }
         if(!TmpAffs.r_Str || (stat_factor[mod].Str > stat_factor[TmpAffs.r_Str].Str))
            TmpAffs.r_Str = mod;
         break;
      case APPLY_DEX_RACE:
         if((mod <= RACE_NONE) || (mod > LAST_RACE))
            {
            logit(LOG_DEBUG, "affect_modify(): unknown race (%d) for APPLY_DEX_RACE.", loc);
            break;
            }
         if(!TmpAffs.r_Dex || (stat_factor[mod].Dex > stat_factor[TmpAffs.r_Dex].Dex))
            TmpAffs.r_Dex = mod;
         break;
      case APPLY_AGI_RACE:
         if((mod <= RACE_NONE) || (mod > LAST_RACE))
            {
            logit(LOG_DEBUG, "affect_modify(): unknown race (%d) for APPLY_AGI_RACE.", loc);
            break;
            }
         if(!TmpAffs.r_Agi || (stat_factor[mod].Agi > stat_factor[TmpAffs.r_Agi].Agi))
            TmpAffs.r_Agi = mod;
         break;
      case APPLY_CON_RACE:
         if((mod <= RACE_NONE) || (mod > LAST_RACE))
            {
            logit(LOG_DEBUG, "affect_modify(): unknown race (%d) for APPLY_CON_RACE.", loc);
            break;
            }
         if(!TmpAffs.r_Con || (stat_factor[mod].Con > stat_factor[TmpAffs.r_Con].Con))
            TmpAffs.r_Con = mod;
         break;
      case APPLY_POW_RACE:
         if((mod <= RACE_NONE) || (mod > LAST_RACE))
            {
            logit(LOG_DEBUG, "affect_modify(): unknown race (%d) for APPLY_POW_RACE.", loc);
            break;
            }
         if(!TmpAffs.r_Pow || (stat_factor[mod].Pow > stat_factor[TmpAffs.r_Pow].Pow))
            TmpAffs.r_Pow = mod;
         break;
      case APPLY_INT_RACE:
         if((mod <= RACE_NONE) || (mod > LAST_RACE))
            {
            logit(LOG_DEBUG, "affect_modify(): unknown race (%d) for APPLY_INT_RACE.", loc);
            break;
            }
         if(!TmpAffs.r_Int || (stat_factor[mod].Int > stat_factor[TmpAffs.r_Int].Int))
            TmpAffs.r_Int = mod;
         break;
      case APPLY_WIS_RACE:
         if((mod <= RACE_NONE) || (mod > LAST_RACE))
            {
            logit(LOG_DEBUG, "affect_modify(): unknown race (%d) for APPLY_WIS_RACE.", loc);
            break;
            }
         if(!TmpAffs.r_Wis || (stat_factor[mod].Wis > stat_factor[TmpAffs.r_Wis].Wis))
            TmpAffs.r_Wis = mod;
         break;
      case APPLY_CHA_RACE:
         if((mod <= RACE_NONE) || (mod > LAST_RACE))
            {
            logit(LOG_DEBUG, "affect_modify(): unknown race (%d) for APPLY_CHA_RACE.", loc);
            break;
            }
         if(!TmpAffs.r_Cha || (stat_factor[mod].Cha > stat_factor[TmpAffs.r_Cha].Cha))
            TmpAffs.r_Cha = mod;
         break;
      case APPLY_KARMA_RACE:
         if((mod <= RACE_NONE) || (mod > LAST_RACE))
            {
            logit(LOG_DEBUG, "affect_modify(): unknown race (%d) for APPLY_KARMA_RACE.", loc);
            break;
            }
         if(!TmpAffs.r_Kar || (stat_factor[mod].Karma > stat_factor[TmpAffs.r_Kar].Karma))
            TmpAffs.r_Kar = mod;
         break;
      case APPLY_LUCK_RACE:
         if((mod <= RACE_NONE) || (mod > LAST_RACE))
            {
            logit(LOG_DEBUG, "affect_modify(): unknown race (%d) for APPLY_LUCK_RACE.", loc);
            break;
            }
         if(!TmpAffs.r_Luc || (stat_factor[mod].Luck > stat_factor[TmpAffs.r_Luc].Luck))
            TmpAffs.r_Luc = mod;
         break;

      case APPLY_FIRE_PROT:
         SET_CBIT(TmpAffs.BitVecs, AFF_PROT_FIRE);
         break;

      case APPLY_ARMOR:
         TmpAffs.AC += mod;
         break;
      case APPLY_AGE:
         TmpAffs.Age += mod;
         break;

      case APPLY_DAMROLL:
         TmpAffs.Dam += mod;
         break;
      case APPLY_HITROLL:
         TmpAffs.Hit += mod;
         break;

      case APPLY_HIT:
         TmpAffs.Hits += mod;
         break;
      case APPLY_CUR_HIT:
         TmpAffs.CurHits += mod;
         break;
      case APPLY_MOVE:
         TmpAffs.Move += mod;
         break;
      case APPLY_MANA:
         TmpAffs.Mana += mod;
         break;
      case APPLY_CHAR_HEIGHT:
         TmpAffs.Height += mod;
         break;
      case APPLY_CHAR_WEIGHT:
         TmpAffs.Weight += mod;
         break;
      case APPLY_SAVING_BREATH:
         TmpAffs.S_breath += mod;
         break;
      case APPLY_SAVING_PARA:
         TmpAffs.S_para += mod;
         break;
      case APPLY_SAVING_PETRI:
         TmpAffs.S_petri += mod;
         break;
      case APPLY_SAVING_ROD:
         TmpAffs.S_rod += mod;
         break;
      case APPLY_SAVING_SPELL:
         TmpAffs.S_spell += mod;
         break;

      case APPLY_MAGIC_RESIST:
         TmpAffs.MR += mod;
         break;

         /* these 5 are all horribly bad ideas, possible we can imp things to do these, but they sure as hell
            won't be APPLYs!  JAB */

      case APPLY_CLASS:
      case APPLY_EXP:
      case APPLY_GOLD:
      case APPLY_LEVEL:
      case APPLY_SEX:

         /* and these 2 are pretty silly, so I'm not imping them. */

      default:
         logit(LOG_DEBUG, "affect_modify(): unknown apply (%d).", loc);
         break;
      }
}

/* Scan all equipment and affect structures attached to a character, summing all the alterations, then
   either subtract them all, or add them all.

   mode FALSE - remove all effects.
   mode TRUE  - apply all affects.

   JAB */

void all_affects(P_char ch, int mode)
{
   int i, j;
   struct affected_type *af;

   if(ch == NULL)               /* replaced call to SanityCheck with this */
      return;

   bzero(&TmpAffs, sizeof(struct hold_data));

   for(i = 0; i < MAX_WEAR; i++)
      {

      /* held items only have effect, if they can only be held */
      if(ch->equipment[i] && ((i != HOLD) || (ch->equipment[i]->wear_flags == (ITEM_TAKE | ITEM_HOLD))))
         {

         /* nuke affects from objects */

         for(j = 0; j < MAX_OBJ_AFFECT; j++)
            affect_modify(ch->equipment[i]->affected[j].location, ch->equipment[i]->affected[j].modifier,
                          ch->equipment[i]->sets_affs);

         /* AND ac_apply */
         affect_modify(APPLY_AC, -(apply_ac(ch, i)), NULL);
         }
      }

   for(af = ch->affected; af; af = af->next)
      affect_modify(af->location, af->modifier, af->sets_affs);

   apply_affs(ch, mode);
}

// UberKludge 2003 - Stop mobs from loading hurt - event called from read_mobile.
void initial_hit_adjust(P_char ch)
{

   if(!ch)
      return;

   if(IS_PC(ch))
      return;

   if(GET_STAT(ch) == STAT_DEAD)
      return;

   // All clear (hahahahahaha) set the hp
   GET_HIT(ch) = GET_MAX_HIT(ch);

   return;
}

/* This updates a character by resetting affectable values to base states, then reaffecting everything */

void affect_total(P_char ch)
{
   if(!ch)
      return;

   bzero(&TmpAffs, sizeof(struct hold_data));

   all_affects(ch, FALSE);       /* effectively resets character to a state with NO affects */

   /* be very very careful what you put in here, if, for example, someone is close to death, and then
      you do something to force an update in here, and they happen to be wearing some +hitpoints items,
      you just killed them mysteriously.  */

   all_affects(ch, TRUE);        /* now add them all back */

   /* now recalc con bonus, since we could have just changed something dealing with Con */

   update_con_bonus(ch);

   /* only if actually in game. JAB */
   if(ch->desc && !ch->desc->connected)
      {
      if(GET_HIT(ch) != GET_MAX_HIT(ch))
         StartRegen(ch, EVENT_HIT_REGEN);
      if(GET_MANA(ch) != GET_MAX_MANA(ch))
         StartRegen(ch, EVENT_MANA_REGEN);
      if(GET_MOVE(ch) != GET_MAX_MOVE(ch))
         StartRegen(ch, EVENT_MOVE_REGEN);
      }
}

/* hackish, sorta, the bonus hits from constitution boil down to basically APPLY_HIT (+/-), so to keep
   things working right, we handle it by using an affect from a psuedo-skill (SKILL_CON_BONUS), which
   players never see, and which never wears off.  As con changes, we remove the old affect and add back
   in the new affect.  It's not too bad, cause we can use the same affect structure (just change the
   modifier), just have to make sure we update it always, as con and/or level changes.  JAB */

void update_con_bonus(P_char ch)
{
   int c_bonus;
   struct affected_type *af_p, *af_p2, *af_p3, af;

   /* first, make sure ch has an affect for SKILL_CON_BONUS, if not, this is a call from read_mobile()
      (new mob), or from init_char (new player).  So, create a 0 mod affect, attach it, and then continue on.
    */

   /* scan affected for our affect. */

   if(!ch || (GET_STAT(ch) == STAT_DEAD) || IS_MORPH(ch))
      return;

   for(af_p = ch->affected; af_p; af_p = af_p->next)
      if(af_p->type == SKILL_CON_BONUS)
         break;

   if(af_p)
      {
      /* only 1 counts, nuke others. JAB */
      for(af_p2 = af_p->next; af_p2; af_p2 = af_p3)
         {
         af_p3 = af_p2->next;
         if(af_p2->type == SKILL_CON_BONUS)
            affect_remove(ch, af_p2);
         }
      }
   if(!af_p)
      {
      bzero(&af, sizeof(af));
      af.type = SKILL_CON_BONUS;
      af.duration = 1;
      af.location = APPLY_HIT;
      affect_to_char(ch, &af);
      af_p = &af;
      }
   /* ok, af_p now points to ch's CON_BONUS affect (for sure), we grab it and check to see if it needs
      changing, change it, and add it back. */

   c_bonus = con_app[STAT_INDEX(GET_C_CON(ch))].hitp;

   if((c_bonus > 2) && !IS_WARRIOR(ch))
      c_bonus = 2;

   c_bonus *= GET_LEVEL(ch);
   if((c_bonus + ch->points.base_hit) < GET_LEVEL(ch))
      c_bonus = GET_LEVEL(ch) - ch->points.base_hit;

   //   if(!strcmp(GET_NAME(ch), "earth elemental"))
   //      debuglog(51, DS_AZUTH, "ch is %s, HP = %d, MXHP = %d, c_bonus = %d, af_p->modifier = %d, conAP = %d",
   //            GET_NAME(ch), GET_HIT(ch), GET_MAX_HIT(ch), c_bonus, af_p->modifier, con_app[STAT_INDEX(GET_C_CON(ch))].hitp);
   if(ch->nr == 1044)
      debuglog(51, DS_AZUTH, "ch is %s, HP = %d, MXHP = %d, c_bonus = %d, af_p->modifier = %d, conAP = %d",
               GET_NAME(ch), GET_HIT(ch), GET_MAX_HIT(ch), c_bonus, af_p->modifier, con_app[STAT_INDEX(GET_C_CON(ch))].hitp);
   if(c_bonus != af_p->modifier)
      {
      GET_MAX_HIT(ch) += (c_bonus - af_p->modifier);

      af_p->modifier = c_bonus;

      AddEvent(EVENT_BALANCE_AFFECTS, 1, TRUE, ch, 0);
      }
}

/* insert an affect_type in a char_data structure automatically sets apropriate bits and apply's */

void affect_to_char(P_char ch, struct affected_type *af)
{
   struct affected_type *affected_alloc;

   if(!dead_affect_pool)
      dead_affect_pool =
      mm_create("AFFECTS", sizeof(struct affected_type), offsetof(struct affected_type, next), 10);
#if 0
#ifdef MEM_DEBUG
   mem_use[MEM_AFF] += sizeof(struct affected_type);
#endif
   CREATE(affected_alloc, struct affected_type, 1);
#endif

   affected_alloc = mm_get(dead_affect_pool);

   *affected_alloc = *af;
   affected_alloc->next = ch->affected;
   ch->affected = affected_alloc;

   AddEvent(EVENT_BALANCE_AFFECTS, 1, TRUE, ch, 0);
}


/* this function returns the type for a linked affect */
/* any new linked affs need to be in here - Iyachtu   */
int affect_alter_ego(int type)
{
   switch(type)
      {
      case SPELL_CASTER_STONE:
         return SPELL_STONE_SKIN;
         break;
      case SPELL_STONE_SKIN:
         return SPELL_CASTER_STONE;
         break;
      case SPELL_CASTER_WATER_EMBODIMENT:
         return SPELL_ELEMENTAL_WATER;
         break;
      case SPELL_ELEMENTAL_WATER:
         return SPELL_CASTER_WATER_EMBODIMENT;
         break;
      case SPELL_CASTER_FIRE_EMBODIMENT:
         return SPELL_ELEMENTAL_FIRE;
         break;
      case SPELL_ELEMENTAL_FIRE:
         return SPELL_CASTER_FIRE_EMBODIMENT;
         break;
      case SPELL_CASTER_EARTH_EMBODIMENT:
         return SPELL_ELEMENTAL_EARTH;
         break;
      case SPELL_ELEMENTAL_EARTH:
         return SPELL_CASTER_EARTH_EMBODIMENT;
         break;
      case SPELL_CASTER_AIR_EMBODIMENT:
         return SPELL_ELEMENTAL_AIR;
         break;
      case SPELL_ELEMENTAL_AIR:
         return SPELL_CASTER_AIR_EMBODIMENT;
         break;
      case SPELL_CASTER_SCALE:
         return SPELL_DRAGONSCALES;
         break;
      case SPELL_DRAGONSCALES:
         return SPELL_CASTER_SCALE;
         break;
      default:
         return FALSE;
      }
}

P_char affect_find_caster(P_char ch, int type)
{
   struct affected_type *af, *af_next;
   P_char caster = NULL;

   for(af = ch->affected; af; af = af_next)
      {
      af_next = af->next;
      if((af->type == type) && (af->target != NULL))
         break;
      }

   /* didn't find the affect with a caster flag on the ch */
   if(!af)
      return NULL;

   caster = af->target;

   /* this next section can be removed after debugging, it merely verifies */
   /* that the caster *also* has this affect on him.  Don't really need to */
   /* know that, but will help track down any ambiguities - Iyachtu        */

   /* now switch type to matching one on caster */
   type = affect_alter_ego(type);

   for(af = caster->affected; af; af = af_next)
      {
      af_next = af->next;
      if((af->type == type) && (af->target == ch))
         break;
      }

   /* didn't find matching affect on caster */
   if(!af)
      {
      debuglog(51, DS_SPELLCAST, "affect_find_caster couldn't find affect on caster");
      return NULL;
      }

   return caster;
}



/* removes a linked affect, when the target affect is removed */

void affect_remove_linked(P_char ch, P_char victim, int type)
{
   struct affected_type *af, *af_next;
   int remove_type = 0, kill = 0;

   if(!ch || !victim)
      {
      debuglog(51, DS_SPELLCAST, "affect_remove_linked called with invalid ch or victim");
      return;
      }

   /* any new linked affects MUST be put in here    */
   /* if not, they won't work, we'll get spammed    */
   /* and we'll probably crash elsewhere - Iyachtu  */

   /* kill is used to determine whether to remove the */
   /* affect or to just take away the linked pointer  */
   /* you want it to kill CASTER type affects         */

   remove_type = (affect_alter_ego(type));

   switch(remove_type)
      {
      case SPELL_STONE_SKIN:
         kill = FALSE;
         break;
      case SPELL_CASTER_STONE:
         kill = TRUE;
         break;
      case SPELL_CASTER_WATER_EMBODIMENT:
         kill = TRUE;
         break;
      case SPELL_ELEMENTAL_WATER:
         kill = TRUE;
         break;
      case SPELL_CASTER_FIRE_EMBODIMENT:
         kill = TRUE;
         break;
      case SPELL_ELEMENTAL_FIRE:
         kill = TRUE;
         break;
      case SPELL_CASTER_AIR_EMBODIMENT:
         kill = TRUE;
         break;
      case SPELL_ELEMENTAL_AIR:
         kill = TRUE;
         break;
      case SPELL_CASTER_EARTH_EMBODIMENT:
         kill = TRUE;
         break;
      case SPELL_ELEMENTAL_EARTH:
         kill = TRUE;
         break;
      case SPELL_CASTER_SCALE:
         kill = TRUE;
         break;
      case SPELL_DRAGONSCALES:
         kill = FALSE;
         break;
      default:
         debuglog(51, DS_SPELLCAST, "affect_remove_linked called with invalid aff type: %d", type);
         return;
      }

   for(af = ch->affected; af; af = af_next)
      {
      af_next = af->next;
      if((af->type == remove_type) && (af->target == victim))
         {
         /* OK, there is a matching affect on the other target */

         /* remove the pointer, to prevent invalid pointers */
         if(af->target)
            af->target = NULL;

         if(kill)
            debuglog(51, DS_SPELLCAST, "removing affect from %s", GET_NAME(ch));

         /* if this is the caster version, we set it to zero duration */
         /* Changed so we don't loop, but still kill the affect */
         if(kill)
            af->duration = 0;
         }
      }
}


/* remove an affected_type structure from a char (called when duration reaches zero). pointer *af must
   never be NULL!  Frees mem and calls affect_location_apply */

void affect_remove(P_char ch, struct affected_type *af)
{
   struct affected_type *hjp;

   if(!(ch && ch->affected))
      {
      logit(LOG_EXIT, "affect_remove() bogus parms");
      dump_core();
      }
   /* remove structure *af from linked list */

   all_affects(ch, FALSE);
   if(ch->affected == af)
      {
      /* remove head of list */
      ch->affected = af->next;
      }
   else
      {

      for(hjp = ch->affected; (hjp->next) && (hjp->next != af); hjp = hjp->next);

      if(hjp->next != af)
         {
         logit(LOG_EXIT, "could not locate affected_type in ch->affected for %s. affect_remove()",
               GET_NAME(ch));
         debuglog(51, DS_SPELLCAST, "Couldn't find affect to remove in affect_remove");
         return;
         // Changed this so it just does a return.  If we start getting
         // odd behavior and we're getting this debuglog, re-instate dump_core - Iyachtu
#if 0
         dump_core();
#endif
         }
      hjp->next = af->next;       /* skip the af element */
      }

   if((af->type == SPELL_WATERBREATH) || (af->type == SKILL_BODY_CONTROL))
      checkHostileSectors(ch);

#if 0
#ifdef MEM_DEBUG
   mem_use[MEM_AFF] -= sizeof(struct affected_type);

#endif

   free((char *) af);
#endif
   /* adding for linked affects */

   if(af->target)
      {
      affect_remove_linked (af->target, ch, af->type);
      }

   if(af->instruction)
      free((char *) af->instruction);
   mm_release(dead_affect_pool, af);

   af = NULL;
   all_affects(ch, TRUE);

   char_light(ch);
   room_light(ch->in_room, REAL);
   AddEvent(EVENT_BALANCE_AFFECTS, 1, TRUE, ch, 0);
}

/* call affect_remove with every spell of spelltype "skill" */

void affect_from_char(P_char ch, int skill)
{
   struct affected_type *hjp, *tmp;

   for(hjp = ch->affected; hjp; hjp = tmp)
      {
      tmp = hjp->next;
      if(hjp->type == skill)
         affect_remove(ch, hjp);
      }
}

/* Return TRUE if a char is affected by a spell (SPELL_XXX) */

bool affected_by_spell(P_char ch, int skill)
{
   struct affected_type *hjp;

   if(!ch)
      {
      wizlog(51, "holy cheezewiz, batman... no ch to affected_by_spell!");
      return FALSE;
      }

   for(hjp = ch->affected; hjp; hjp = hjp->next)
      if((hjp->type == skill) ||
         ((hjp->type == SPELL_ELEMENTAL_FIRE) &&
          (skill == SPELL_FIRESHIELD)))
         return(TRUE);

   return(FALSE);
}

void affect_join(P_char ch, struct affected_type *af, int avg_dur, int avg_mod)
{
   bool found = FALSE;
   struct affected_type *hjp;

   for(hjp = ch->affected; !found && hjp; hjp = hjp->next)
      {
      if(hjp->type == af->type)
         {

         af->duration += hjp->duration;
         if(avg_dur)
            af->duration /= 2;

         af->modifier += hjp->modifier;
         if(avg_mod)
            af->modifier /= 2;

         affect_remove(ch, hjp);
         affect_to_char(ch, af);
         found = TRUE;
         }
      }

   if(!found)
      affect_to_char(ch, af);

   AddEvent(EVENT_BALANCE_AFFECTS, 1, TRUE, ch, 0);
}

/* move a player out of a room */

void char_from_room(P_char ch)
{
   P_char i;
   int was_in;
   char buf[8192];

   if(ch->in_room == NOWHERE)
      {
      logit(LOG_EXIT, "call to char_from_room() when already NOWHERE (%s)", GET_NAME(ch));
      dump_core();
      }

   if(IS_AFFECTED(ch, AFF_SCRIBING))
      scribe_job_nuke(ch);

   if(IS_AFFECTED(ch, AFF_CASTING))
      StopCasting(ch);

   char_light(ch);
   was_in = ch->in_room;
   room_light(was_in, REAL);

   // Remove Earth Fog on room if ch is affected
   if(IS_AFFECTED(ch, AFF_EARTH_FOG) && IS_CSET(world[ch->in_room].room_flags, ROOM_EARTH_FOG_TRAVEL))
      {
      REMOVE_CBIT(world[ch->in_room].room_flags, ROOM_EARTH_FOG_TRAVEL);
      }

   // Remove Fire Fog on room if ch is affected
   if(IS_AFFECTED(ch, AFF_FIRE_FOG) && IS_CSET(world[ch->in_room].room_flags, ROOM_FIRE_FOG_TRAVEL))
      {
      REMOVE_CBIT(world[ch->in_room].room_flags, ROOM_FIRE_FOG_TRAVEL);
      }

#if 0
   /* shadow skill --TAM */
   if(IS_BEING_SHADOWED(ch))
      ch->specials.shadow.room_last_in = ch->in_room;
#endif
   /* once a player leaves a room, they shouldn't be getting hit anymore by ANY type of special attack..
      as well, they shouldn't be hitting anyone */
   stop_fighting(ch);
   StopAllAttackers(ch);

   if(ch == world[ch->in_room].people)  /* head of list */
      world[ch->in_room].people = ch->next_in_room;
   else
      {
      /* locate the previous element */
      for(i = world[ch->in_room].people; i && (i->next_in_room != ch); i = i->next_in_room);
      if(!i)
         {
         logit(LOG_EXIT, "called char_from_room, char not in room list");
         sprintf(buf, "DEBUG: char_from_room bad call hit, detail:\n");
         statuslog(51, buf);
         sprintf(buf, "-> Char: %s, InRoom: %d/%d, State: %d\n",
                 GET_NAME(ch), ch->in_room, world[ch->in_room].number, STATE(ch->desc));
         statuslog(51, buf);

         ch->specials.was_in_room = NOWHERE;
         ch->in_room = NOWHERE;
         ch->next_in_room = 0;
         room_light(was_in, REAL);
         /* return; */
         dump_core();
         }
      i->next_in_room = ch->next_in_room;
      }

   ch->specials.was_in_room = ch->in_room;
   ch->in_room = NOWHERE;
   ch->next_in_room = 0;
   room_light(was_in, REAL);
}

/* place a character in a room.  */

void char_to_room(P_char ch, long room, int dir)
{
   P_char t_ch, k;
   char j, exit1 = -1, exit2 = -1, exit3 = -1;
#if 0
   bool pet_found = FALSE;
#endif

   // Wack massmorph if set -- Moradin
   if(IS_MASSMORPH(ch))
      {
      act("&+mThe illusion over $n&+m's group is shattered!",
          TRUE, GET_GROUP_LEADER(GET_GROUP(ch)), 0, 0, TO_ROOM);
      send_to_char("&+mThe illusion covering your group is shattered!&N\n",
                   (GET_GROUP_LEADER(GET_GROUP(ch))));
      REMOVE_BIT((GET_GROUP(ch))->flags, GAFF_MASSMORPH);
      }  // end tag on Moradin

   if((ch->in_room != NOWHERE) || ch->next_in_room)
      {
      logit(LOG_EXIT, "%s has bogus data in char_to_room()", GET_NAME(ch));
      dump_core();
      }
   if(room == -1)
      {
      /* this is a real bad idea, most likely caused by real_room() not existing in database */
      room = 0;
      logit(LOG_DEBUG, "char_to_room: trying to move %s to room -1.", GET_NAME(ch));
      }

   /* shadow skill --tam */
   if(IS_BEING_SHADOWED(ch) && (dir > -1))
      MoveShadower(ch, room);

   if(!IS_CSET(world[room].room_flags, SINGLE_FILE))
      {
      ch->next_in_room = world[room].people;
      world[room].people = ch;
      }
   else
      {
      for(j = 0; j < 6; j++)
         if(world[room].dir_option[(int) j])
            {
            if(exit1 == -1)
               exit1 = j;
            else if(exit2 == -1)
               exit2 = j;
            else
               exit3 = j;
            }

      if((exit1 == -1) || (exit2 == -1))
         {
         REMOVE_CBIT(world[room].room_flags, SINGLE_FILE);
         //         logit(LOG_DEBUG, "Room %d set SINGLE_FILE with < 2 exits", world[room].number);
         exit1 = -1;               /* will cause normal behavior */
         }
      if(exit3 != -1)
         {
         REMOVE_CBIT(world[room].room_flags, SINGLE_FILE);
         //         logit(LOG_DEBUG, "Room %d set SINGLE_FILE with > 2 exits", world[room].number);
         exit1 = -1;               /* will cause normal behavior */
         }
      if((exit1 == -1) || (exit1 == rev_dir[(dir < 0) ? 0 : dir]))
         {
         ch->next_in_room = world[room].people;
         world[room].people = ch;
         }
      else
         {
         if(!(k = world[room].people))
            world[room].people = ch;
         else
            {
            while(k->next_in_room)
               k = k->next_in_room;
            k->next_in_room = ch;
            ch->next_in_room = 0;
            }
         }
      }

   ch->in_room = room;
#if 0
   /* shadow skill --tam */
   if(IS_BEING_SHADOWED(ch) && (dir > -1))
      MoveShadower(ch, room);
#endif
   /* ok, since running battles aren't allowed, if they get here and are still fighting, they either got
      yanked out of combat or this is a do_at() call.  we check for the do_at() call, and stop them from
      fighting if it's not a do_at() call (farsee spell uses do_at()) */

   if(ch->specials.fighting && (dir >= 0))
      stop_fighting(ch);

   char_light(ch);
   room_light(ch->in_room, REAL);

   /* see if the ch is affected by globe of darkness and handle it
    *                                          -- Altherog May 1, 1999 */
   if(IS_AFFECTED(ch, AFF_GLOBE_OF_DARKNESS))
      {
      if(IS_SUNLIT(ch->in_room))
         {
         act("&+LThe light fades to gray...\n", TRUE, ch, 0, 0, TO_ROOM);
         SET_CBIT(world[ch->in_room].room_flags, ROOM_GLOBE_OF_DARKNESS);
         /* schedule a brief event, the chance is they are just passing by quickly */
         AddEvent(EVENT_ROOM_EXECUTE, 4, TRUE, ch, eventGlobeOfDarkness);
         }
      }

   if(dir == -3)
      return;


   // sprintf(Gbuf1, "&+WDEBUG -> do_look called from char_to_room. dir: (%d), room: (%d)\n",
   //  dir, room);
   // send_to_char(Gbuf1, ch);

   if(dir > -3)
      do_look(ch, 0, -4);


   /* checks whether the char has just entered a sector hostile
      to its physique                          -- Altherog Apr 17, 1997 */
   checkHostileSectors(ch);

   if(dir < 0)                  /* flag value, skip aggro checks */
      return;

   /* new, room specials get checked when chars enter, ignores return, but does some checking before it
      continues. JAB */

   if(world[ch->in_room].funct)
      {
      (*world[ch->in_room].funct) (ch->in_room, ch, (PROC_ENTER_ROOM + dir), NULL);
      if(!ch || (room != ch->in_room) || (GET_STAT(ch) == STAT_DEAD))
         return;
      }

   if(CHAR_FALLING(ch))
      {
      /* gonna skip agg checks if they are just passing through */
      if(falling_char(ch))
         return;
      }
   if((room == NOWHERE) || IS_CSET(world[room].room_flags, SAFE_ZONE))
      return;
#if 0
   /* removed for newjustice.  --D2 8/5/99 */
   /* new and nasty, if an undead enters a 'good' hometown zone, the guards treat it like an invading
      PC, and hunt it down. */

   /* Revised - only hunt it down if it's an undead PC PET.  This has been driving makers crazy. :P */
   /* Shev CRM 9/98 */

   if(IS_NPC(ch) && ch->following)
      {
      if(IS_PC(ch->following))
         pet_found = TRUE;
      }

   if(IS_UNDEAD(ch) && pet_found &&
      IS_SET(hometowns[zone_table[world[ch->in_room].zone].hometown].flags, JUSTICE_GOODHOME))
      {
      justice_action_invader(ch);
      if(ch->following &&
         IS_SET(hometowns[zone_table[world[ch->following->in_room].zone].hometown].flags, JUSTICE_GOODHOME))
         justice_action_invader(ch->following);
      }
#endif

   // Set Earth Fog on room and add the event if ch is affected
   if(IS_AFFECTED(ch, AFF_EARTH_FOG) && !IS_CSET(world[ch->in_room].room_flags, ROOM_EARTH_FOG) &&
      !IS_CSET(world[ch->in_room].room_flags, ROOM_EARTH_FOG_TRAVEL) &&\
      !IS_CSET(world[ch->in_room].room_flags, ROOM_SOLID_FOG) &&
      !IS_CSET(world[ch->in_room].room_flags, ROOM_FIRE_FOG_TRAVEL) &&
      !IS_CSET(world[ch->in_room].room_flags, ROOM_FIRE_FOG))
      {
      SET_CBIT(world[ch->in_room].room_flags, ROOM_EARTH_FOG_TRAVEL);
      AddEvent(EVENT_ROOM_EXECUTE, 120, TRUE, ch, efog_dissipate_travel);
      }

   // Set Fire Fog on room and add the event if ch is affected
   if(IS_AFFECTED(ch, AFF_FIRE_FOG) && !IS_CSET(world[ch->in_room].room_flags, ROOM_EARTH_FOG) &&
      !IS_CSET(world[ch->in_room].room_flags, ROOM_EARTH_FOG_TRAVEL) &&\
      !IS_CSET(world[ch->in_room].room_flags, ROOM_SOLID_FOG) &&
      !IS_CSET(world[ch->in_room].room_flags, ROOM_FIRE_FOG_TRAVEL) &&
      !IS_CSET(world[ch->in_room].room_flags, ROOM_FIRE_FOG))
      {
      SET_CBIT(world[ch->in_room].room_flags, ROOM_FIRE_FOG_TRAVEL);
      AddEvent(EVENT_ROOM_EXECUTE, 120, TRUE, ch, ffog_dissipate_travel);
      }


   if(ALONE(ch))
      return;

   /* check char entering room for an agg (auto) attack on occupants. */

   t_ch = PickTarget(ch);

   /* delay is 0 to 7, 0 to 5 for avg. dex, 0 to 4 for 18, 0 to 1 for 23+, that's pulses, so at most,
      delay < 2 seconds.  delays for people in the room are slightly longer.  instant attacks are now a
      thing of the past. */

   if(t_ch && is_aggr_to(ch, t_ch))
      AddEvent(EVENT_AGG_ATTACK, number(0, MAX(0, (11 - dex_app[STAT_INDEX(GET_C_DEX(ch))].reaction) / 2)),
               TRUE, ch, t_ch);

   /* checks for room occupants are made in do_simple_move() */
}

/* give an object to a char   */

void obj_to_char(P_obj object, P_char ch)
{
   P_obj o;
#ifdef ARTIFACT
   int aIndex;
#endif

   if(!ch)
      {
      logit(LOG_MOB, "call to obj_to_char with no ch");
      return;
      }
   if(!object)
      {
      logit(LOG_OBJ, "call to obj_to_char with no obj");
      return;
      }
   if(!OBJ_NOWHERE(object))
      {
      logit(LOG_EXIT, "wonders never cease, obj not in NOWHERE");
      dump_core();
      }

   if((object->type == ITEM_COMMODITY) && (IS_NPC(ch)) && (IS_AFFECTED(ch, AFF_CHARM)))
      {
      act("You cannot get commodities.", FALSE, ch, object, 0, TO_CHAR);
      act("$n cannot get commodities.", FALSE, ch, object, 0, TO_ROOM);
      obj_to_room(object, ch->in_room);
      return;
      }

   if(ch->carrying && (ch->carrying->R_num == object->R_num))
      {
      object->next_content = ch->carrying;
      ch->carrying = object;
      }
   else
      {
      o = ch->carrying;
      while(o)
         {
         if(o->next_content && (o->next_content->R_num == object->R_num))
            {
            object->next_content = o->next_content;
            o->next_content = object;
            break;
            }
         else
            o = o->next_content;
         }
      if(!o)
         {
         object->next_content = ch->carrying;
         ch->carrying = object;
         }
      }

   if(IS_SET(object->extra_flags, ITEM_LIT) || IS_SET(object->extra_flags, ITEM_DARK) ||
      ((object->type == ITEM_LIGHT) && object->value[2]))
      {
      char_light(ch);
      room_light(ch->in_room, REAL);
      }
   object->loc_p = LOC_CARRIED;
   object->loc.carrying = ch;
   GET_CARRYING_W(ch) += GET_OBJ_WEIGHT(object);
   IS_CARRYING_N(ch)++;

#ifdef ARTIFACT /* Unique artifact code hook */
   /* check for an artifact object */
   if(art_index)
      {
      if(IS_SET(obj_index[object->R_num].spec_flag, IDX_ARTIFACT))
         {
         aIndex = searchForArtifact(obj_index[object->R_num].virtual);
         if(IS_PC(ch) && (art_index[aIndex].ch != ch) &&
            (art_index[aIndex].ch >= 0))
            {
            if(art_index[aIndex].ch && strcmp(art_index[aIndex].owner, "noone"))
               artifactFromChar(object, ch);
            artifactToChar(object, ch);
            }
         art_index[aIndex].ch = NULL;
         }
      else
         {
         /* container objects require extra effort (could be deeply nested) */
         if(OBJ_IS_CONTAINER(object))
            getNestedArtifacts (object, ch);
         }
      }
#endif
}

/* take an object from a char */

void obj_from_char(P_obj object)
{
   P_obj tmp;

   if(!OBJ_CARRIED(object) || !object->loc.carrying)
      {
      logit(LOG_EXIT, "obj not carried in obj_from_char");
      dump_core();
      }
#ifdef ARTIFACT
   if(art_index)
      tagNestedArtifacts (object, getObjectOwner(object));
#endif

   if(IS_CSET(object->sets_affs, AFF_GROUP_CACHED))
      {
      debuglog(51, DS_GROUP_CACHE_DEBUG, "obj_from_char(): removing [%s] from %s", object->name, GET_NAME(object->loc.carrying));
      removeGroupCacheItem(object, object->loc.carrying);
      }

   /* nuke all accumulated PSPs if they move they remove the obj from inventory */
   if(object->type == ITEM_PSP_CRYSTAL)
      {
      object->value[1] = 0;
      if(IS_CSET(object->sets_affs, AFF_CHARGING))
         {
         REMOVE_CBIT(object->sets_affs, AFF_CHARGING);
         REMOVE_CBIT(object->loc.carrying->specials.affects, AFF_CHARGING);
         }
      }

   if(object->loc.carrying->carrying == object)         /* head of list */
      object->loc.carrying->carrying = object->next_content;
   else
      {
      for(tmp = object->loc.carrying->carrying;
         tmp && (tmp->next_content != object);
         tmp = tmp->next_content);      /* locate previous */

      tmp->next_content = object->next_content;
      }

   if(IS_SET(object->extra_flags, ITEM_LIT) || IS_SET(object->extra_flags, ITEM_DARK) ||
      ((object->type == ITEM_LIGHT) && object->value[2]))
      {
      char_light(object->loc.carrying);
      room_light((object->loc.carrying)->in_room, REAL);
      }

   GET_CARRYING_W(object->loc.carrying) -= GET_OBJ_WEIGHT(object);
   IS_CARRYING_N(object->loc.carrying)--;
   object->loc_p = LOC_NOWHERE;
   object->loc.room = 0;
   object->next_content = NULL;
}

/* return the effect of a piece of armor in position eq_pos */

int apply_ac(P_char ch, int eq_pos)
{
   if(!(ch && (eq_pos >= 0) && (eq_pos < MAX_WEAR) && ch->equipment[eq_pos]))
      {
      logit(LOG_EXIT, "assert: apply_ac called for dead character");
      dump_core();
      }
   if(!(GET_ITEM_TYPE(ch->equipment[eq_pos]) == ITEM_ARMOR))
      return 0;

   switch(eq_pos)
      {

      case WEAR_BODY:
         return(ch->equipment[eq_pos]->value[0]);
      case WEAR_HEAD:
         return(ch->equipment[eq_pos]->value[0]);
      case WEAR_LEGS:
         return(ch->equipment[eq_pos]->value[0]);
      case WEAR_FEET:
         return(ch->equipment[eq_pos]->value[0]);
      case WEAR_TAIL:
         return(ch->equipment[eq_pos]->value[0]);
      case WEAR_HANDS:
         return(ch->equipment[eq_pos]->value[0]);
      case WEAR_ARMS:
         return(ch->equipment[eq_pos]->value[0]);
      case WEAR_SHIELD:
         return(ch->equipment[eq_pos]->value[0]);
      case WEAR_WAIST:
         return(ch->equipment[eq_pos]->value[0]);
      case WEAR_ABOUT:
         return(ch->equipment[eq_pos]->value[0]);
      case WEAR_WRIST_L:
         return(ch->equipment[eq_pos]->value[0]);
      case WEAR_WRIST_R:
         return(ch->equipment[eq_pos]->value[0]);
      case WEAR_NECK_1:
         return(ch->equipment[eq_pos]->value[0]);
      case WEAR_NECK_2:
         return(ch->equipment[eq_pos]->value[0]);
      case WEAR_EYES:
         return(ch->equipment[eq_pos]->value[0]);
      case WEAR_FACE:
         return(ch->equipment[eq_pos]->value[0]);
      default:                      /* should all positions give ac? ... NO.  JAB */
         return 0;
      }
}

void equip_char(P_char ch, P_obj obj, int pos, int nodrop)
{
#ifdef ARTIFACT
   int artif_index;
#endif
   struct func_attachment *fn;

   if(!(ch && obj && (pos >= 0) && (pos < MAX_WEAR) && !ch->equipment[pos]))
      {
      logit(LOG_EXIT, "assert: bogus args in equip_char");
      dump_core();
      }
   if(!OBJ_NOWHERE(obj))
      {
      logit(LOG_EXIT, "and now for something completely different, obj not in NOWHERE");
      dump_core();
      }

   /* changed so gods aren't zapped -- DTS 1/21/95 */
   if(!IS_TRUSTED(ch) && IS_PC(ch) &&
      ((IS_OBJ_STAT(obj, ITEM_ANTI_EVIL) && IS_EVIL(ch)) ||
       (IS_OBJ_STAT(obj, ITEM_ANTI_GOOD) && IS_GOOD(ch)) ||
       (IS_OBJ_STAT(obj, ITEM_ANTI_NEUTRAL) && IS_NEUTRAL(ch))  ||
       (IS_OBJ_STAT(obj, ITEM_ANTI_GOODRACE) && RACE_GOOD(ch)) ||
       (IS_OBJ_STAT(obj, ITEM_ANTI_EVILRACE) && RACE_EVIL(ch))))
      {
      if(nodrop)
         {
         /* quietly fail, this happens in initial wearing from files.c */
         obj_to_char(obj, ch);
         return;
         }
      else
         {
         if(ch->in_room != NOWHERE)
            {
            act("You are zapped by $p and instantly drop it.", FALSE, ch, obj, 0, TO_CHAR);
            act("$n is zapped by $p and instantly drops it.", FALSE, ch, obj, 0, TO_ROOM);
            obj_to_room(obj, ch->in_room);
            }
         else
            {
            extract_obj(obj);
            logit(LOG_DEBUG, "ch->in_room == NOWHERE when equipping char.");
            }
         return;
         }
      }
   ch->equipment[pos] = obj;
   obj->loc.wearing = ch;
   obj->loc_p = LOC_WORN;

   AddEvent(EVENT_BALANCE_AFFECTS, 1, TRUE, ch, 0);

   /* light works though */
   if(IS_SET(obj->extra_flags, ITEM_LIT) || IS_SET(obj->extra_flags, ITEM_DARK) ||
      ((obj->type == ITEM_LIGHT) && obj->value[2]))
      {
      char_light(ch);
      room_light(ch->in_room, REAL);
      }
   GET_CARRYING_W(ch) += (GET_OBJ_WEIGHT(obj) / 2);

   // Inserted PROC_OBJ_WEAR test and call - Urdlen
   if(!nodrop && // Don't need it procing when saving or loading player
      (obj->R_num >= 0) &&
      (obj_index[obj->R_num].spec_flag & IDX_OBJ_WEAR))
      {
      fn = obj_index[obj->R_num].func;
      while(fn && obj && (obj->R_num >= 0))
         if(!(fn->proc_flag & IDX_OBJ_WEAR) ||
            !(*fn->func.obj) (obj, ch, PROC_OBJ_WEAR, NULL))
            fn = fn->next;
      }

#ifdef ARTIFACT /* Unique artifact code hook */
   /* check for an artifact object */
   if(art_index)
      {
      if(IS_SET(obj_index[obj->R_num].spec_flag, IDX_ARTIFACT))
         {
         artif_index = searchForArtifact(obj_index[obj->R_num].virtual);
         if(IS_PC(ch) && (art_index[artif_index].ch != ch) &&
            (art_index[artif_index].ch > 0))
            artifactToChar(obj, ch);
         art_index[artif_index].ch = NULL;
         }
      else
         {
         /* container objects require extra effort (could be deeply nested) */
         if(OBJ_IS_CONTAINER(obj))
            getNestedArtifacts (obj, ch);
         }
      }
#endif
}

P_obj unequip_char(P_char ch, int pos, bool doProc)
{
   P_obj obj;
   struct func_attachment *fn;

   if(!(ch && (pos >= 0) && (pos < MAX_WEAR) && ch->equipment[pos]))
      {
      logit(LOG_EXIT, "assert: unequip_char char called with bad args");
      dump_core();
      }
   obj = ch->equipment[pos];

#ifdef ARTIFACT
   if(art_index)
      tagNestedArtifacts (obj, ch);
#endif

   if(!OBJ_WORN(obj))
      {
      logit(LOG_EXIT, "equip: obj is not flagged equipped when in equip.");
      dump_core();
      }
   all_affects(ch, FALSE);
   ch->equipment[pos] = NULL;
   obj->loc_p = LOC_NOWHERE;
   all_affects(ch, TRUE);

   AddEvent(EVENT_BALANCE_AFFECTS, 1, TRUE, ch, 0);

   if(IS_SET(obj->extra_flags, ITEM_LIT) || IS_SET(obj->extra_flags, ITEM_DARK) ||
      ((obj->type == ITEM_LIGHT) && obj->value[2]))
      {
      char_light(ch);
      room_light(ch->in_room, REAL);
      }
   GET_CARRYING_W(ch) -= (GET_OBJ_WEIGHT(obj) / 2);

   /*
    * Inserted PROC_OBJ_REMOVE test and call - Urdlen
    * This will only happen in cases where the obj is not destroyed in its use,
    * i.e. stolen, disarmed, removed.  Broken keys, consumed potions and scrolls,
    * used disguise kits, etc are not subject to a proc here.
    */
   if(doProc && // Don't need it procing when saving or loading player
      (obj->R_num >= 0) &&
      (obj_index[obj->R_num].spec_flag & IDX_OBJ_REMOVE))
      {
      fn = obj_index[obj->R_num].func;
      while(fn && obj && (obj->R_num >= 0))
         if(!(fn->proc_flag & IDX_OBJ_REMOVE) ||
            !(*fn->func.obj) (obj, ch, PROC_OBJ_REMOVE, NULL))
            fn = fn->next;
      }

   return(obj);
}

int get_number(char **name)
{
   char *ppos, buf[MAX_STRING_LENGTH];
   int i;

   buf[0] = 0;

   if((ppos = index(*name, '.')))
      {
      *ppos++ = '\0';
      strcpy(buf, *name);
      strcpy(*name, ppos);

      for(i = 0; *(buf + i); i++)
         if(!isdigit(*(buf + i)))
            return(0);

      return(atoi(buf));
      }
   return(1);
}


/* search a given list for an object, and return a pointer to that object */

P_obj get_obj_in_list(char *name, P_obj list)
{
   P_obj i;
   char *tmp, tmpname[MAX_STRING_LENGTH];
   int j, k;

   if(name)
      strcpy(tmpname, name);
   tmp = tmpname;
   if(!(k = get_number(&tmp)))
      return(0);

   for(i = list, j = 1; i && (j <= k); i = i->next_content)
      if(isname(tmp, i->name))
         {
         if(j == k)
            return(i);
         j++;
         }
   return(0);
}

/* search a given list for an object number, and return a ptr to that obj */

P_obj get_obj_in_list_num(int num, P_obj list)
{
   P_obj i;

   if((num < 0) || !list)
      return NULL;

   for(i = list; i; i = i->next_content)
      if(i->R_num == num)
         return i;

   return NULL;
}

/*search the entire world for an object, and return a pointer  */

P_obj get_obj(char *name)
{
   P_obj i;
   char *tmp, tmpname[MAX_STRING_LENGTH];
   int j, k;

   if(name)
      strcpy(tmpname, name);
   tmp = tmpname;
   if(!(k = get_number(&tmp)))
      return(0);

   for(i = object_list, j = 1; i && (j <= k); i = i->next)
      if(isname(tmp, i->name))
         {
         if(j == k)
            return(i);
         j++;
         }
   return(0);
}

/*search the entire world for an object number, and return a pointer  */

P_obj get_obj_num(int nr)
{
   P_obj i;

   for(i = object_list; i; i = i->next)
      if(i->R_num == nr)
         return(i);

   return(0);
}

/* search a room for a char, and return a pointer if found..  */

P_char get_char_room(const char *name, int room)
{
   P_char i;
   char *tmp, tmpname[MAX_STRING_LENGTH];
   int j, k;

   if(name)
      strcpy(tmpname, name);
   tmp = tmpname;
   if(!(k = get_number(&tmp)))
      return(0);

   for(i = world[room].people, j = 1; i && (j <= k); i = i->next_in_room)
      if(isname(tmp, GET_NAME(i)))
         {
         if((j == k) && !IS_AFFECTED(i, AFF_HIDE))
            return(i);
         j++;
         }
   return(0);
}

/* search a room for a char, and return a pointer if found..  */

P_char get_char_room_num(int num, int room)
{
   P_char i;

   if(!num || !room)
      return NULL;

   for(i = world[room].people; i ; i = i->next_in_room)
      {
      if(IS_NPC(i) && (i->nr == num))
         return(i);
      }

   return(0);
}

/* search all over the world for a char, and return a pointer if found */

P_char get_char(char *name)
{
   P_char i;
   char *tmp, tmpname[MAX_STRING_LENGTH];
   int j, k;

   if(name)
      strcpy(tmpname, name);
   else
      return NULL;

   tmp = tmpname;
   if(!(k = get_number(&tmp)))
      return NULL;

   for(i = NPC_list, j = 1; i && (j <= k); i = i->next)
      if(isname(tmp, GET_NAME(i)))
         {
         if(j == k)
            return(i);
         j++;
         }

   for(i = PC_list; i && (j <= k); i = i->next)
      {
      if(isname(tmp, GET_NAME(i)))
         {
         if(j == k)
            return(i);
         j++;
         }
      }

   return NULL;
}

/* modified get_char, for PCs only */
P_char get_PC(char *name)
{
   P_char i;
   char *tmp, tmpname[MAX_STRING_LENGTH];
   int j = 1, k;

   if(name)
      strcpy(tmpname, name);
   else
      return NULL;

   tmp = tmpname;
   k = get_number(&tmp);
   if(!k)
      return NULL;

   for(i = PC_list; i && (j <= k); i = i->next)
      {
      if(isname(tmp, GET_NAME(i)))
         {
         if(j == k)
            return(i);
         j++;
         }
      }

   return NULL;
}

/* search all over the world for a char, and return a pointer if found, exclude chars not in the game */

P_char get_char_in_game(char *name)
{
   P_char i;
   char *tmp, tmpname[MAX_STRING_LENGTH];
   int j, k;

   if(name)
      strcpy(tmpname, name);
   else
      return NULL;

   tmp = tmpname;
   if(!(k = get_number(&tmp)))
      return NULL;

   for(i = NPC_list, j = 1; i && (j <= k); i = i->next)
      if(isname(tmp, GET_NAME(i)) && (i->in_room != NOWHERE))
         {
         if(j == k)
            return(i);
         j++;
         }

   for(i = PC_list; i && (j <= k); i = i->next)
      {
      if(isname(tmp, GET_NAME(i)) && (!i->desc || !i->desc->connected))
         {
         if(j == k)
            return(i);
         j++;
         }
      }

   return NULL;
}

/* search all over the world for a char num, and return a pointer if found */

P_char get_char_num(int nr)
{
   P_char i;

   for(i = NPC_list; i; i = i->next)
      if(i->nr == nr)
         return(i);

   return(0);
}

/* put an object in a room */

void obj_to_room(P_obj obj, int room)
{
   P_char i;
   P_event e1;
   P_obj o;
   int found = FALSE;
#ifdef ARTIFACT
   int aIndex;
#endif

   if(!OBJ_NOWHERE(obj))
      {      /* ROFL - Pooka */
      logit(LOG_EXIT, "here's a switch, obj NOT in NOWHERE");
      dump_core();
      }
   if((room < 0) || (room > top_of_world))
      {
      logit(LOG_EXIT, "bogus room in obj_to_room");
      dump_core();
      }

   if(TERRAIN_WATER(room)
      && !TERRAIN_NOFALL(room)
      && !IS_SET(obj->extra_flags, ITEM_FLOAT)
      && (obj->type != ITEM_MISSILE_INFLIGHT)
      && (obj->type != ITEM_BOAT)
      && (obj->type != ITEM_SHIP))
      {
      for(i = world[room].people; i; i = i->next_in_room)
         act("$p sinks into the water.", TRUE, i, obj, 0, TO_CHAR);
      extract_obj(obj);
      return;
      }

   if(obj->type == ITEM_MISSILE_INFLIGHT)
      obj->loc_p = LOC_FLOATING;
   else
      obj->loc_p = LOC_ROOM;
   obj->loc.room = room;

   if(IS_SET(obj->extra_flags, ITEM_TRANSIENT) && (obj->type != ITEM_MISSILE_INFLIGHT))
      {
      /* Transient objs needed to be destroyed when dropped */

      AddEvent(EVENT_DECAY, 0, TRUE, obj, 0);
      }

   if(!world[room].contents || OBJ_FLOATING(obj))
      {
      obj->next_content = world[room].contents;
      world[room].contents = obj;
      }
   else if(world[room].contents->R_num == obj->R_num)
      {
      if(obj_index[obj->R_num].virtual == 3)
         {
         /* generic 'pile of coins' obj, merge them */
         add_coins(world[room].contents, obj->value[0], obj->value[1], obj->value[2], obj->value[3]);
         obj->loc_p = LOC_NOWHERE;
         extract_obj(obj);
         return;
         }
      else
         {
         obj->next_content = world[room].contents;
         world[room].contents = obj;
         }
      }
   else
      {
      o = world[room].contents;
      while(o)
         {
         if(o->next_content && (o->next_content->R_num == obj->R_num))
            {
            if(obj_index[obj->R_num].virtual == 3)
               {
               /* generic 'pile of coins' obj, merge them */
               add_coins(o->next_content, obj->value[0], obj->value[1], obj->value[2], obj->value[3]);
               obj->loc_p = LOC_NOWHERE;
               extract_obj(obj);
               return;
               }
            obj->next_content = o->next_content;
            o->next_content = obj;
            break;
            }
         else
            o = o->next_content;
         }
      if(!o)
         {
         obj->next_content = world[room].contents;
         world[obj->loc.room].contents = obj;
         }
      }

   if(IS_SET(obj->extra_flags, ITEM_LIT) || IS_SET(obj->extra_flags, ITEM_DARK) ||
      ((obj->type == ITEM_LIGHT) && obj->value[2]))
      room_light(room, REAL);

   if(obj && (obj->type == ITEM_CORPSE) && (obj->value[1] == PC_CORPSE))
      writeCorpse(obj); // call it directly so we quickly as possible get it saved
                        // the event version left a big hole for a lost corpse 7/25/200 -Azuth
   //      AddEvent(EVENT_CORPSE_SAVE, 2, TRUE, obj, 0);

   if(obj && GET_ITEM_TYPE(obj) != ITEM_CORPSE)
      {
      if(OBJ_FALLING(obj))
         {
         LOOP_EVENTS(e1, obj->events)
         if(e1->type == EVENT_FALLING_OBJ)
            return;
         falling_obj(obj);
         }
      }

   /* if the was transfered to underwater or underwater_gr attach an event
    * that will simulate sinking                 -- Diirinka May 12, 97 */
   if(!OBJ_FLOATING(obj) && TERRAIN_UNDERWATER(room))
      {
      if(obj->events)
         {
         LOOP_EVENTS(e1, obj->events)
         if((e1->type == EVENT_OBJ_EXECUTE) && (e1->target.t_func == (void*) sinkingObject))
            found = TRUE;
         }
      if(!found)
         AddEvent(EVENT_OBJ_EXECUTE, 10, TRUE, obj, sinkingObject);
      }


#ifdef ARTIFACT
   /* check for an artifact object */
   if(art_index)
      {
      if(IS_SET(obj_index[obj->R_num].spec_flag, IDX_ARTIFACT))
         {
         aIndex = searchForArtifact(obj_index[obj->R_num].virtual);
         if((art_index[aIndex].ch) &&
            artifactIsOwned(obj_index[obj->R_num].virtual))
            artifactFromChar(obj, art_index[aIndex].ch);

         }
      else
         {
         /* container objects require extra effort (could be deeply nested) */
         if(OBJ_IS_CONTAINER(obj))
            dropNestedArtifacts (obj);
         }
      }
#endif
}

/* Take an object from a room */

void obj_from_room(P_obj object)
{
   P_obj i;

   if(!OBJ_ROOM(object))
      {
      logit(LOG_EXIT, "obj not in room in obj_from_room");
      dump_core();
      }

#ifdef ARTIFACT
   if(art_index)
      tagNestedArtifacts (object, NULL);
#endif

   /* remove object from room */

   if(object == world[object->loc.room].contents)       /* head of list */
      world[object->loc.room].contents = object->next_content;
   else
      {
      /* locate previous element in list */
      for(i = world[object->loc.room].contents; i && (i->next_content != object); i = i->next_content);

      if(!i)
         {
         logit(LOG_EXIT, "obj_from_room: futzed up room object list");
         dump_core();
         }
      i->next_content = object->next_content;
      }

   if(IS_SET(object->extra_flags, ITEM_LIT) || IS_SET(object->extra_flags, ITEM_DARK) ||
      ((object->type == ITEM_LIGHT) && object->value[2]))
      room_light(object->loc.room, REAL);


   object->loc_p = LOC_NOWHERE;
   object->loc.room = NOWHERE;
   object->next_content = NULL;

   /* nuke player corpse file */

   if(object && (object->type == ITEM_CORPSE) && (object->value[1] == PC_CORPSE))
      PurgeCorpseFile(object);
}

/* put an object in an object (quaint)  */

void obj_to_obj(P_obj obj, P_obj obj_to)
{
   P_obj tmp_obj, o;
   int wgt = 0, t_wgt = 0;
#ifdef ARTIFACT
   P_char ch;
#endif

   if(!obj || !obj_to ||
      ((obj_to->type != ITEM_CONTAINER) && (obj_to->type != ITEM_CART) &&  /* @ TRADE @ */
       (obj_to->type != ITEM_QUIVER) && (obj_to->type != ITEM_CORPSE) &&
       (obj_to->type != ITEM_CRUCIBLE)))
      {
      dump_core();
      }
   obj->loc_p = LOC_INSIDE;
   obj->loc.inside = obj_to;

   if(obj_to->contains && (obj_to->contains->R_num == obj->R_num))
      {
      obj->next_content = obj_to->contains;
      obj_to->contains = obj;
      }
   else
      {
      o = obj_to->contains;
      while(o)
         {
         if(o->next_content && (o->next_content->R_num == obj->R_num))
            {
            obj->next_content = o->next_content;
            o->next_content = obj;
            break;
            }
         else
            o = o->next_content;
         }
      if(!o)
         {
         obj->next_content = obj_to->contains;
         obj_to->contains = obj;
         }
      }

   /* Modified routine to reduce Arrow weight to 1/10th of 1 pound, making
      quiver weight realistic to what it should be.    9-7-96 --MIAX        */
   if(((obj->type == ITEM_MISSILE) || (obj->type == ITEM_FIREWEAPON)) &&
      (obj_to->type == ITEM_QUIVER))
      {
      wgt = (AMOUNT_OF_AMMO(obj_to) / 5);
      obj_to->weight = ((wgt * GET_OBJ_WEIGHT(obj)) + 1);
      }
   else
      {
      wgt = GET_OBJ_WEIGHT(obj);
      for(tmp_obj = obj->loc.inside; wgt && tmp_obj;
         tmp_obj = OBJ_INSIDE(tmp_obj) ? tmp_obj->loc.inside : NULL)
         {
         t_wgt = GET_OBJ_WEIGHT(tmp_obj);
         tmp_obj->weight += wgt;
         if(t_wgt == GET_OBJ_WEIGHT(tmp_obj))
            break;
         wgt = GET_OBJ_WEIGHT(tmp_obj) - t_wgt;
         }
#ifdef ARTIFACT
      /* check for an artifact object */
      if(art_index)
         {
         ch = art_index[searchForArtifact(obj_index[obj->R_num].virtual)].ch;
         if(IS_SET(obj_index[obj->R_num].spec_flag, IDX_ARTIFACT))
            {
            if(ch && (getObjectOwner(obj) != ch))
               if(IS_PC(ch))
                  artifactFromChar(obj, ch);
            }
         else
            {
            /* container objects require extra effort (could be deeply nested) */
            if(OBJ_IS_CONTAINER(obj) &&
               (getObjectOwner(obj) != getObjectOwner(obj_to)))
               dropNestedArtifacts (obj);
            }
         }
#endif
      }
}

/* remove an object from an object */

void obj_from_obj(P_obj obj)
{
   P_obj tmp, obj_from;
   int wgt;

   if(OBJ_INSIDE(obj))
      {
#ifdef ARTIFACT
      if(art_index)
         tagNestedArtifacts (obj, getObjectOwner(obj));
#endif
      obj_from = obj->loc.inside;
      if(obj == obj_from->contains)      /* head of list */
         obj_from->contains = obj->next_content;
      else
         {
         /* locate previous */
         for(tmp = obj_from->contains; tmp && (tmp->next_content != obj); tmp = tmp->next_content);

         if(!tmp)
            {
            logit(LOG_EXIT, "obj_from_obj(): Fatal error in object structures");
            dump_core();
            }
         tmp->next_content = obj->next_content;
         }

      /* Subtract weight from containers container */
      /* Same modification as to obj_to_obj, changed for quivers to reduce
         arrow weights to what they should be. */
      if(((obj->type == ITEM_MISSILE) || (obj->type == ITEM_FIREWEAPON)) &&
         (obj_from->type == ITEM_QUIVER))
         {
         wgt = (AMOUNT_OF_AMMO(obj_from) / 5);
         obj_from->weight = ((wgt * GET_OBJ_WEIGHT(obj)) + 1);
         }
      else
         {
         wgt = GET_OBJ_WEIGHT(obj);
         for(tmp = obj->loc.inside; wgt && tmp;
            tmp = OBJ_INSIDE(tmp) ? tmp->loc.inside : NULL)
            {
            tmp->weight -= GET_OBJ_WEIGHT(obj);
            wgt = GET_OBJ_WEIGHT(tmp);
            }
         }

      obj->loc_p = LOC_NOWHERE;
      obj->loc.inside = NULL;
      obj->next_content = NULL;
      }
   else
      {
      logit(LOG_EXIT, "obj_from_obj(): call with no object");
      dump_core();
      }
}

/* Set all loc.carrying to point to new owner */

void object_list_new_owner(P_obj list, P_char ch)
{
   if(list)
      {
      object_list_new_owner(list->contains, ch);
      object_list_new_owner(list->next_content, ch);
      list->loc.carrying = ch;
      }
}

/* Extract an object from the world */

void extract_obj(P_obj obj)
{
   P_group group = NULL;
   P_gmember member = NULL;
   struct cache_list *cache = NULL;
   int found = FALSE, i;
   P_obj temp1 = NULL;
   struct func_attachment *fn;
#ifdef ARTIFACT
   P_char ch = NULL;
#endif

   if(!obj)
      {
      logit(LOG_DEBUG, "NULL obj in call to extract_obj");
      dump_core();
      return;
      }
#ifdef ARTIFACT /* the cost is used to flag a bogus object */
   /* this IS a hack...and should be someday fixed...might require a
      new construct to the object struct to do this, however -- DDW */
   if(art_index && (obj->cost > -1))
      {
      if(IS_SET(obj_index[obj->R_num].spec_flag, IDX_ARTIFACT))
         {
         if(artifactIsOwned(obj_index[obj->R_num].virtual))
            {
            ch = get_char(art_index[searchForArtifact(obj_index[obj->R_num].virtual)].owner);
            simpleArtifactFromChar (obj, ch);
            }
         }
      }
#endif

   if(IS_CSET(obj->sets_affs, AFF_GROUP_CACHED))
      { /* go find the item in someones glist via pointer search -Azuth */
      group = group_list;
      if(!group)
         debuglog(51, DS_GROUP_CACHE_DEBUG, "extract_obj: item still in grp cache '%s' location unknown",
                  obj->short_description);

      while(group && !found)
         {
         for(member = group->members; member && !found; member = member->next)
            {
            if(!member->g_objs)
               continue;

            /* member has objects in the cache */
            for(cache = member->g_objs; cache && !found; cache = cache->next)
               {
               if(obj == cache->this) // if pointers are equal, this is the one we want axed
                  {
                  found = TRUE;
                  debuglog(51, DS_GROUP_CACHE_DEBUG, "extract_obj: removing %s from %s", obj->short_description, GET_NAME(member->this));
                  removeGroupCacheItem(obj, member->this);
                  }
               }
            }

         group = group->next;
         }
      }

   check_for_scribe_nukage_object(obj);
   if(OBJ_ROOM(obj))
      obj_from_room(obj);
   else if(OBJ_CARRIED(obj))
      obj_from_char(obj);
   else if(OBJ_WORN(obj))
      {
      for(i = 0; i < MAX_WEAR; i++)
         if(obj->loc.wearing->equipment[i] == obj)
            {
            temp1 = obj;
            break;
            }
      if(!temp1)
         {
         logit(LOG_EXIT, "obj loc.wearing, but not in equipment list");
         dump_core();
         }
      unequip_char(obj->loc.wearing, i, FALSE);
      }
   else if(OBJ_INSIDE(obj))
      {
      obj_from_obj(obj);
      }

   for(; obj->contains; obj->contains = temp1)
      {
      temp1 = obj->contains->next_content;
      extract_obj(obj->contains);
      }

   obj->contains = NULL;

   /* leaves nothing ! */

   /* yank it from the object_list, very fast now */

   // Inserted PROC_OBJ_EXTRACT case - Urdlen
   if((obj->R_num >= 0) && (obj_index[obj->R_num].spec_flag & IDX_OBJ_EXTRACT))
      {
      fn = obj_index[obj->R_num].func;
      while(fn && obj && (obj->R_num >= 0))
         if(!(fn->proc_flag & IDX_OBJ_EXTRACT) ||
            !(*fn->func.obj) (obj, NULL, PROC_OBJ_EXTRACT, NULL))
            fn = fn->next;
      }

   if(object_list == obj)
      {     /* head of list */
      object_list = obj->next;
      if(object_list)
         object_list->prev = NULL;
      }
   else
      {
      if(obj->prev)
         obj->prev->next = obj->next;
      if(obj->next)
         obj->next->prev = obj->prev;
      }

   obj->prev = NULL;
   obj->next = NULL;

   if(obj->R_num >= 0)
      (obj_index[obj->R_num].number)--;

   free_obj(obj);
}

/* replaces major part of point_update, called by Events() to make an object
   decay and be extracted.  Mainly for use on corpses of course, but other
   objects may be handled in the same way. */

void Decay(P_obj obj)
{
   P_char holder = NULL;
   P_obj t_obj = NULL, t_obj2 = NULL;
   bool corpselog = FALSE;
   int pos, dest = 0, old_load;

   if(!obj)
      {
      logit(LOG_DEBUG, "Decay():  NULL obj");
      return;
      }

   /* @@ */
   if(GET_ITEM_TYPE(obj)==ITEM_CART)
      {
      if(obj->owner)
         return;

      if(world[obj->loc.room].people)
         {
         act("A couple of darkdressed men come out from the shadow...\nand in a second $p is gone.",
             0, world[obj->loc.room].people, obj, 0, TO_ROOM);
         act("A couple of darkdressed men come out from the shadow...\nand in a second $p is gone.",
             0, world[obj->loc.room].people, obj, 0, TO_CHAR);
         }
      extract_cart(obj);
      return;
      }
   /* @@ */

   if(OBJ_ROOM(obj))
      {
      dest = 1;

      if(obj->R_num == real_object(752))
         {
         /* astral gates */
         if(world[obj->loc.room].people)
            {
            act("$p dissolves in a swirl of colors and is gone.",
                0, world[obj->loc.room].people, obj, 0, TO_ROOM);
            act("$p dissolves in a swirl of colors and is gone.",
                0, world[obj->loc.room].people, obj, 0, TO_CHAR);
            }
         }
      else if(obj->R_num == real_object(2))
         {
         /* corpses */
         if(world[obj->loc.room].people)
            {
            act("$p melts into the ground, leaving no trace of its passing.",
                0, world[obj->loc.room].people, obj, 0, TO_ROOM);
            act("$p melts into the ground, leaving no trace of its passing.",
                0, world[obj->loc.room].people, obj, 0, TO_CHAR);
            }
         /* added logging to prevent player bitching -- DTS 2/1/95 */
         if(obj->value[1] == PC_CORPSE)
            {
            logit(LOG_CORPSE, "%s decayed in room %d.", obj->short_description, world[obj->loc.room].number);
            corpselog = TRUE;
            }
         }
      else if(obj->R_num == real_object(4))
         {
         /*
          * Blood stains (no message)
          */
         extract_obj(obj);
         return;
         }
      else
         {
         /* everything else */
         if(world[obj->loc.room].people)
            {
            act("$p crumbles to dust and blows away.", TRUE, world[obj->loc.room].people, obj, 0, TO_ROOM);
            act("$p crumbles to dust and blows away.", TRUE, world[obj->loc.room].people, obj, 0, TO_CHAR);
            }
         }

      }
   else if(OBJ_INSIDE(obj))
      {
      dest = 2;

      extract_obj(obj);
      return;

      /* no messages if they decay while inside something else, except if it
         is being carried, and changes the load.  So find carrier and load. */

      holder = getObjectOwner(obj);
      old_load = IS_CARRYING_W(holder);

      /*  Removed because this is way old and strange 8-20-96 --MIAX

          for (t_obj = obj->loc.inside; OBJ_INSIDE(t_obj); t_obj = obj->loc.inside);
          if (OBJ_CARRIED(t_obj)) {
            carrier = t_obj->loc.carrying;
            old_load = IS_CARRYING_W(carrier);

          } else if (OBJ_WORN(t_obj)) {
            carrier = t_obj->loc.wearing;
            old_load = IS_CARRYING_W(carrier);
          }
      */

      }
   else
      {
      /* handle the other locations */

      if(OBJ_WORN(obj))
         {
         for(pos = 0; pos < MAX_WEAR; pos++)
            if(obj->loc.wearing->equipment[pos] && (obj->loc.wearing->equipment[pos] == obj))
               break;

         if(obj->loc.wearing->equipment[pos] != obj)
            {
            logit(LOG_DEBUG, "Decay():  equipped obj %d (%s) not in equip (%s)",
                  obj->R_num, obj->name, GET_NAME(obj->loc.wearing));
            AddEvent(EVENT_BALANCE_AFFECTS, 1, TRUE, obj->loc.wearing, 0);
            extract_obj(obj);
            return;
            }
         obj_to_char(unequip_char(obj->loc.wearing, pos, TRUE), obj->loc.wearing);
         /* now carried, drop through */
         }
      if(OBJ_CARRIED(obj))
         {
         if(obj->R_num == real_object(2))
            {
            /* corpses */
            if(obj->contains)
               act("$p decays in your hands, dumping its contents on the ground.",
                   FALSE, obj->loc.carrying, obj, 0, TO_CHAR);
            else
               act("$p decays in your hands, leaving no trace.", FALSE, obj->loc.carrying, obj, 0, TO_CHAR);

            /* added logging to prevent player bitching -- DTS 2/1/95 */
            if(obj->value[1] == PC_CORPSE)
               {
               logit(LOG_CORPSE, "%s decayed in possession of %s in room %d.", obj->short_description,
                     (IS_PC(obj->loc.carrying) ? GET_NAME(obj->loc.carrying) :
                      obj->loc.carrying->player.short_descr),
                     world[obj->loc.carrying->in_room].number);
               corpselog = TRUE;
               }
            }
         else
            {
            /* everything else */
            if(obj->contains)
               act("$p crumbles in your hands, dumping its contents on the ground.",
                   FALSE, obj->loc.carrying, obj, 0, TO_CHAR);
            else
               act("$p crumbles in your hands, leaving no trace.", FALSE, obj->loc.carrying, obj, 0, TO_CHAR);
            }

         if((pos = obj->loc.carrying->in_room) != NOWHERE)
            {
            obj_from_char(obj);
            obj_to_room(obj, pos);
            dest = 1;
            }
         else
            {
            extract_obj(obj);
            return;
            }
         }
      else
         {
         /* ugly */
         logit(LOG_DEBUG, "Decay():  obj %d (%s) has no location.", obj->R_num, obj->name);
         extract_obj(obj);
         return;
         }
      }

   if(obj->contains)
      {
      for(t_obj = obj->contains; t_obj; t_obj = t_obj2)
         {
         t_obj2 = t_obj->next_content;
         obj_from_obj(t_obj);
         if(corpselog && !IS_SET(t_obj->extra_flags, ITEM_TRANSIENT))
            logit(LOG_CORPSE, "%s Decay drop: [%d] %s", obj->short_description,
                  obj_index[t_obj->R_num].virtual, t_obj->name);
         if(dest == 1)
            {
            obj_to_room(t_obj, obj->loc.room);
            }
         else
            {
            obj_to_obj(t_obj, obj->loc.inside);
            }
         }
      }
   extract_obj(obj);
   /*  Kris broke this. JAB  6/4/97
   if (holder) {
     if (old_load > IS_CARRYING_W(holder))
       send_to_char("Your load suddenly feels lighter!\n", holder);
     if (old_load < IS_CARRYING_W(holder))
       send_to_char("Your load suddenly feels heavier!\n", holder);
   }
   */
}

/*
 ** Improved update_char_objects tells holder of light source that
 ** his/her light source has just gone out.
 */

void update_char_objects(P_char ch)
{
   int i, change;

   for(change = 0, i = PRIMARY_WEAPON; i < WEAR_EYES; i++)
      if(ch->equipment[i] && (ch->equipment[i]->type == ITEM_LIGHT) && (ch->equipment[i]->value[2] > 0))
         {

         (ch->equipment[i]->value[2])--;

         /* Check if light source has just gone out .. if so, inform */
         /* the HOLDer of light source. */

         if(ch->equipment[i]->value[2] <= 0)
            {
            act("Your $q just went out.", FALSE, ch, ch->equipment[i], 0, TO_CHAR);
            act("$n's $q just went out.", FALSE, ch, ch->equipment[i], 0, TO_ROOM);
            change = 1;
            }
         else if(ch->equipment[i]->value[2] <= 2)
            act("Your $q glows dimly, barely illuminating the room.", FALSE, ch, ch->equipment[i], 0, TO_CHAR);
         else if(ch->equipment[i]->value[2] <= 6)
            act("Your $q flickers as it slowly burns down.", FALSE, ch, ch->equipment[i], 0, TO_CHAR);
         }
   if(change)
      {
      char_light(ch);
      room_light(ch->in_room, REAL);
      }
}

void RemoveFromCharList(P_char ch)
{
   P_char t_ch = NULL;

   /* pull the char from the list */

   if(IS_NPC(ch))
      {
      if(ch == NPC_list)
         NPC_list = ch->next;
      else
         {
         for(t_ch = NPC_list; t_ch && (t_ch->next != ch); t_ch = t_ch->next)
            ;

         if(t_ch)
            t_ch->next = ch->next;
         else
            {
            logit(LOG_EXIT, "RemoveFromCharList(), Char not in NPC_list. (%s)", GET_NAME(ch));
            dump_core();
            }
         }
      }
   else
      {
      if(ch == PC_list)
         PC_list = ch->next;
      else
         {
         for(t_ch = PC_list; t_ch && (t_ch->next != ch); t_ch = t_ch->next)
            ;

         if(t_ch)
            t_ch->next = ch->next;
         else
            {
            logit(LOG_EXIT, "RemoveFromCharList(), Char not in PC_list. (%s)", GET_NAME(ch));
            dump_core();
            }
         }

      if(pclist_debug)
         debuglog(51, DS_PC, "RemoveFromCharList: post remove [%s]", C_NAME(ch) ? C_NAME(ch) : "NULL");
      }

   ch->next = NULL;
}

/* Extract a ch completely from the world, also, free the char struct, old way left stray PC char_data
   structs laying about to cause miscief.  JAB */

void extract_char(P_char ch)
{
   P_desc t_desc;
   P_event ev, ev_save;
   P_obj next_obj, obj;
   int l;
   struct hunt_data *data;
   P_group group = NULL;
   P_gmember member = NULL;
   char Gbuf[MAX_STRING_LENGTH], buf2[MAX_STRING_LENGTH];
   P_char tch = NULL;

   if(!ch)
      {
      logit(LOG_EXIT, "No ch in extract_char");
      dump_core();
      }

   /* moved the is_morph and is switched stuff up above the name checks -azuth */
   /* Don't wanna be extractingthat morphed char.. */
   if(IS_MORPH(ch))
      {
      tch = un_morph(ch);
      if(pclist_debug)
         debuglog(51, DS_PC, "extract_char: [%s] was morphed, orig was [%s], leaving func premature?", C_NAME(ch) ? C_NAME(ch) : "NULL",
                  C_NAME(tch) ? C_NAME(tch) : "NULL");

      return;
      }

   if(IS_PC(ch) && !ch->desc)
      {
      for(t_desc = descriptor_list; t_desc; t_desc = t_desc->next)
         if(t_desc->original == ch)
            do_return(t_desc->character, 0, -4);
      }

   if(!(ch->player.name))
      {
      debuglog(51, DS_BADPARAMS, "hit extract char with that odd !name bug");
      if(pclist_debug)
         {
         if(tch->desc)
            sprinttype(ch->desc->connected, connected_types, buf2);
         else
            sprintf(buf2, "NO_DESCRIP");

         debuglog(51, DS_PC, "extract_char: [%s] has no name, connected status: %s leaving func premature?", C_NAME(ch) ? C_NAME(ch) : "NULL", buf2);
         }

      return;
      }

   if(!(*ch->player.name))
      {
      logit(LOG_EXIT, "No name in extract_char");
      return;
      // dump_core();
      }

   if(ch->in_room == NOWHERE)
      {
      logit(LOG_EXIT, "NOWHERE extracting char. (handler.c, extract_char)");
      dump_core();
      }
   if(IS_PC(ch))
      {
      if(IS_AFFECTED(ch, AFF_SCRIBING))
         scribe_job_nuke(ch);
      if(IS_AFFECTED(ch, AFF_CASTING))
         StopCasting(ch);
      /* Removed in Durian code, hopefully because it is no longer necessary to have this
         extra check here which prevents the mud from crashing if someone is in the editor.
         If mud crashes alot, we will add a modified version of the below call to work with
         the new editor.                                               7/26/98 --MIAX */
      /*
          if (ch->only.pc->writing)
            string_add(ch->only.pc->writing, "\n@@\n");
          ch->only.pc->writing = NULL;
      */
#ifdef NEW_BARD
      if(IS_SINGING(ch))
         bard_stop_singing(ch);
      if(ch->bard_singing)
         remove_from_song(ch->bard_singing, ch);
#endif
      }
   if(ch->followers)
      stop_all_followers(ch);

   if(ch->following)
      stop_follower(ch);

   group = GET_GROUP(ch);
   if(group)
      {
      /* if PC leader: nuke the group, otherwise find a NEW leader */
      if(GET_GROUP_LEADER(group) == ch)
         {
         if((countPCGroupMembers(group) < 2))
            {
            debuglog(51, DS_GROUP, "extract_char: (1) DeleteGroup for %s", C_NAME(ch));
            deleteGroup(ch, group);
            group = NULL;
            }
         else
            findNewGroupLeader(group);
         }

      if(group)
         {

         sprintf (Gbuf, "%s has left the group.\n", C_NAME(ch));
         LOOP_THRU_GROUP (member, group) {
            if((ch != member->this) && CAN_SEE(member->this, ch))
               send_to_char (Gbuf, member->this);
         }

         /* remove member from group */
         debuglog(51, DS_GROUP, "extract_char: removeCharFromGroup %s", C_NAME(ch));
         removeCharFromGroup(ch);

         /* verify that the group is larger than 1 member */
         if(group->members && !group->members->next)
            {
            debuglog(51, DS_GROUP, "extract_char: (2) DeleteGroup for %s", C_NAME(group->members->this));
            deleteGroup(group->members->this, group);
            }
         }
      }

   /* stop riding if rider dies */
   if(GET_MOUNT(ch) || (IS_NPC(ch) && ch->only.npc->rider))
      stop_riding(ch);

   if(IS_SHADOWING(ch))
      {
      act("You stop shadowing $N.", TRUE, ch, 0, GET_CHAR_SHADOWED(ch), TO_CHAR);
      FreeShadowedData(ch, GET_CHAR_SHADOWED(ch));
      }
   else
      {
      if(IS_BEING_SHADOWED(ch))
         {
         StopShadowers(ch);
         }
      }

   /* make mobs stop hunting ch.  Doing it "longhand" to make the code  a bit more readable.. */

   FIND_EVENT_TYPE(ev, EVENT_MOB_HUNT) {
      data = ev->target.t_hunt;

      if(!data)
         continue;
      if((data->hunt_type <= HUNT_LAST_VICTIM_TARGET) && (data->targ.victim == ch))
         {
         free((char *) data);
         ev->target.t_hunt = NULL;
         }
   }

   justice_victim_remove(ch);
   justice_guard_remove(ch);

   /* Code to stop others from ignoring person quitting */
   ac_stopAllFromIgnoring(ch);

   /* Code to stop others from consenting person quitting */
   ac_stopAllFromConsenting(ch);
   if(ch->specials.consent)
      send_to_char_f(ch->specials.consent, "%s has quit the game, consent lost.\n", GET_NAME(ch));

   /* Stop duelling */
   ac_stopAllFromDuelling(ch);

   if(ch->specials.fighting)
      stop_fighting(ch);

   /* Code to stop all that are attacking ch */
   StopAllAttackers(ch);

   /* Code to remove resources consumed by memory */
   if(HAS_MEMORY(ch))
      {
      mem_destroy(ch->only.npc->memory);
      ch->only.npc->memory = NULL; // ensure nothing tries to access it later -Azuth
      }

   if(current_event && (current_event->actor.a_ch == ch))
      current_event = NULL;

   ev_save = current_event;
   ClearCharEvents(ch);
   current_event = ev_save;
   NukeRedunantSpellcast(ch);

   /* OK, back to original stuff */
   /* clear equipment_list */
   for(l = 0; l < MAX_WEAR; l++)
      if(ch->equipment[l])
         {

         obj = unequip_char(ch, l, FALSE);

         if(IS_SET(obj->extra_flags, ITEM_TRANSIENT) || IS_CSET(world[ch->in_room].room_flags, DEATH))
            {
            extract_obj(obj);
            obj = NULL;
            }
         else
            obj_to_room(obj, ch->in_room);
         }

   if(ch->carrying)
      {
      for(obj = ch->carrying; obj != NULL; obj = next_obj)
         {
         next_obj = obj->next_content;
         if(IS_CSET(world[ch->in_room].room_flags, DEATH) || IS_SET(obj->extra_flags, ITEM_TRANSIENT))
            {
            extract_obj(obj);
            obj = NULL;
            }
         else
            {
            obj_from_char(obj);
            obj_to_room(obj, ch->in_room);
            }
         }
      }

   if(IS_ENABLED(CODE_MMAIL))
      {
      /* save the mailbox and remove it from global mbox list */
      if(IS_PC(ch))
         {
         if(GET_MBOX(ch))
            {
            mmail_mbox_save(GET_MBOX(ch));
            mmail_mbox_list_remove(GET_MBOX(ch));
            }
         }
      }

   witness_destroy(ch);
   char_from_room(ch);

   RemoveFromCharList(ch);

   if(ch->desc)
      {
      if(IS_NPC(ch))
         {
         logit(LOG_EXIT, "NPC has a descriptor");
         dump_core();
         }

      if(ch->desc->original)
         do_return(ch, 0, -4);

      t_desc = ch->desc;  /* snag the descriptor */
      ch->desc = 0;

      /* NOTE!  ch still exists, but now has no desc, and is completely unattached to anything!  We now treat the
         old body, just like it was an NPC (other than only.pc, of course), no more saving, no restoring, nada!
         Ignore this warning and die horribly!  JAB */

      if(t_desc->connected != CON_FLUSH)
         {
         t_desc->connected = CON_SLCT;
         SEND_TO_Q(MENU, t_desc);

         /* rather than keeping the old character struct (which caused all sorts of weird things) we reload the data
            from their character file, which was more than likely, just saved from somewhere else.
            JAB */

         t_desc->character = GetNewChar(NEW_PC);

         t_desc->character->only.pc->aggressive = -1;

         if((t_desc->rtype = restoreCharOnly(t_desc->character, GET_NAME(ch))) == -2)
            {
            /* player file exists, but it's corrupt, which means something
               serious is wrong, since we JUST saved
               that sucker, so, panic, and dump core.  JAB */
            logit(LOG_EXIT, "Reload of %s in extract_char failed (corrupt pfile)", GET_NAME(ch));
            dump_core();
            }
         if(t_desc->rtype == -1)
            {
            /* the weenie clause, either char has just been termed, or Mystra has been stringing people with illegal
               names again.  To fix, just chop their link, they'll have to login under the correct name and mud won't
               crash due to a NULL character struct. JAB */
            free_char(t_desc->character);
            t_desc->character = NULL;
            close_socket(t_desc);
            return;
            }

         t_desc->character->desc = t_desc;

         /* at this point, t_desc->character should be in exactly the same state as if he had just logged in and
            correctly entered his password.  Only difference, he's at the menu, not the MotD.  Note very carefully
            that ch and t_desc->character are completely different entities.  JAB */
         }
      }

   if(IS_NPC(ch))
      {
      if(ch->nr > -1)            /* if mobile */
         mob_index[ch->nr].number--;
      else
         {
         logit(LOG_EXIT, "NPC with nr < 0?  BOOM");
         dump_core();
         }
      }
   else
      {
      PC_count--;
      if(pclist_debug)
         debuglog(51, DS_PC, "extract_char: post extract [%s] PC_count %d", C_NAME(ch) ? C_NAME(ch) : "NULL", PC_count);

      if(PC_count < 0)
         dump_core();
      }

   free_char(ch);
}

/* erases victim completely, including any objects and pfiles, this is a much cleaner way of handling
   character deletions. JAB */

// ch is nuking victim here
void nuke_character(P_char ch, P_char victim)
{
   P_desc hold = NULL;
   P_obj t_obj1 = NULL, t_obj2 = NULL;
   int pos = 0;

   if(!ch)
      {
      logit(LOG_WIZ, "No char in call to nuke_character");
      return;
      }

   if(!victim)
      {
      logit(LOG_WIZ, "No victim in call to nuke_character");
      return;
      }

   if((ch != victim) && (!IS_TRUSTED(ch) || (GET_LEVEL(ch) < GET_LEVEL(victim))))
      {
      logit(LOG_WIZ, "%s tried to nuke %s, punishment fitted to the crime", GET_NAME(ch), GET_NAME(victim));
      /* nuke_character(ch, ch); after I know it works, maybe. JAB */
      return;
      }

   /* nuke all objects */

   for(pos = 0; pos < MAX_WEAR; pos++)
      {
      if(victim->equipment[pos])
         {
         t_obj1 = unequip_char(victim, pos, FALSE);
         extract_obj(t_obj1);
         }
      }

   for(t_obj1 = victim->carrying; t_obj1; t_obj1 = t_obj2)
      {
      t_obj2 = t_obj1->next_content;
      obj_from_char(t_obj1);
      extract_obj(t_obj1);
      }

   if(victim->desc && victim->desc->snoop.snooping && victim->desc->snoop.snooping->snoop.snoop_by)
      {
      victim->desc->snoop.snooping->snoop.snoop_by = 0;
      victim->desc->snoop.snooping = 0;
      }

   hold = victim->desc;
   if(IS_PC(victim))
      {
      if(hold && !hold->connected)
         {
         SEND_TO_Q("\n\nCharacter is deleted!\n", hold);
         hold->connected = CON_FLUSH;
         }

      statuslog(51, "%s deleted by %s.", GET_NAME(victim), GET_NAME(ch));
      logit(LOG_PLAYER, "%s deleted by %s.", GET_NAME(victim), GET_NAME(ch));
      deleteCharacter(victim);
      }

   extract_char(victim);

   if(hold)
      hold->character = NULL;
}

/* ***********************************************************************
   Here follows high-level versions of some earlier routines, ie functions
   which incorporate the actual player-data.
   *********************************************************************** */

P_char get_char_room_vis(P_char ch, const char *name)
{
   P_char i;
   char *tmp, tmpname[MAX_STRING_LENGTH];
   int j, k;

   if(!name || !*name)
      return NULL;

   strcpy(tmpname, name);
   tmp = tmpname;

   while(*tmp == ' ')
      tmp++;

   if(!*tmp || !(k = get_number(&tmp)))
      return NULL;

   if(!str_cmp(tmp, "me") || !str_cmp(tmp, "self"))
      return(ch);

   for(i = world[ch->in_room].people, j = 1; i && (j <= k); i = i->next_in_room)
      {

      if(CAN_SEE(ch, i) &&
         (isname(tmp, GET_NAME(i)) ||
          (IS_DISGUISE(i) && isname(tmp, GET_DISGUISE_NAME(i))) ||
          ((i != ch) && !IS_TRUSTED(ch) && IS_DARK(ch->in_room) &&
           IS_AFFECTED(ch, AFF_INFRAVISION) && (isname(tmp, "shape") || isname(tmp, "outline")))))
         {
         if(j == k)
            return(i);
         j++;
         }
      }

   return(0);
}

P_char get_char_vis(P_char ch, const char *name, int room_first)
{
   P_char i;
   char *tmp, tmpname[MAX_STRING_LENGTH];
   int j = 1, k;

   if(!name || !*name)
      return NULL;

   strcpy(tmpname, name);
   tmp = tmpname;
   if(!(k = get_number(&tmp)))
      return NULL;

   if(!str_cmp(name, "me") || !str_cmp(name, "self"))
      return(ch);

   if(room_first)
      {
      j = 1;
      for(i = world[ch->in_room].people; i; i = i->next_in_room)
         {
         if(isname(tmp, GET_NAME(i)) && CAN_SEE(ch, i))
            {
            if(j == k)
               return(i);
            j++;
            }
         }
      }

   for(i = NPC_list, j = 1; i && (j <= k); i = i->next)
      if(isname(tmp, GET_NAME(i)) && CAN_SEE(ch, i))
         {
         if(j == k)
            return(i);
         j++;
         }

   for(i = PC_list; i && (j <= k); i = i->next)
      if(isname(tmp, GET_NAME(i)) && CAN_SEE(ch, i))
         {
         if(j == k)
            return(i);
         j++;
         }

   return NULL;
}

P_char get_char_in_game_vis(P_char ch, const char *name, int room_first)
{
   P_char i;
   char *tmp, tmpname[MAX_STRING_LENGTH];
   int j, k;

   if(!name || !*name)
      return NULL;

   strcpy(tmpname, name);
   tmp = tmpname;
   if(!(k = get_number(&tmp)))
      return NULL;

   if(!str_cmp(name, "me") || !str_cmp(name, "self"))
      return(ch);

   if(room_first)
      {
      j = 1;
      for(i = world[ch->in_room].people; i; i = i->next_in_room)
         {
         if(isname(tmp, GET_NAME(i)) && CAN_SEE(ch, i))
            {
            if(j == k)
               return(i);
            j++;
            }
         }
      }

   for(i = NPC_list, j = 1; i && (j <= k); i = i->next)
      if(isname(tmp, GET_NAME(i)) && (i->in_room != NOWHERE) && CAN_SEE(ch, i))
         {
         if(j == k)
            return(i);
         j++;
         }

   for(i = PC_list; i && (j <= k); i = i->next)
      if(isname(tmp, GET_NAME(i)) && (!i->desc || !i->desc->connected) && CAN_SEE(ch, i))
         {
         if(j == k)
            return(i);
         j++;
         }

   return NULL;
}

/* find 'name' in one of 'ch's hands, return obj pointer if found and set 'pos' to the slot, otherwise return
 * NULL and don't touch 'pos'.
 * JAB */

P_obj get_obj_in_hand(P_char ch, char *name, int *pos)
{
   int i;

   if(!ch || !pos || !name || !*name)
      return NULL;

   for(i = 16; i < 19; i++)
      {
      /* PRIMARY_WEAPON, SECONDARY_WEAPON and HOLD (will be more possible later) */
      if(ch->equipment[i] && isname(name, ch->equipment[i]->name))
         {
         *pos = i;
         return ch->equipment[i];
         }
      }

   return NULL;
}

P_obj get_obj_in_list_vis(P_char ch, char *name, P_obj list)
{
   P_obj i;
   char tmpname[MAX_STRING_LENGTH], *tmp;
   int j, k;

   if(!name || !*name)
      return(0);

   strcpy(tmpname, name);
   tmp = tmpname;
   if(!(k = get_number(&tmp)))
      return(0);

   for(i = list, j = 1; i && (j <= k); i = i->next_content)
      if(isname(tmp, i->name))
         if(CAN_SEE_OBJ(ch, i))
            {
            if(j == k)
               return(i);
            j++;
            }
   return(0);
}

/*search the entire world for an object, and return a pointer  */

P_obj get_obj_vis(P_char ch, char *name)
{
   P_obj t_obj;
   char *tmp, tmpname[MAX_STRING_LENGTH];
   int i, j, k;

   if(!name || !*name)
      return(0);

   strcpy(tmpname, name);
   tmp = tmpname;
   if(!(k = get_number(&tmp)))
      return(0);

   /* check equipment first, this was ancient glitch */
   for(i = 0, j = 0; (i < MAX_WEAR) && (j <= k); i++)
      if((t_obj = ch->equipment[i]))
         if(isname(tmp, t_obj->name))
            if(CAN_SEE_OBJ(ch, t_obj))
               {
               if(j == k)
                  return(t_obj);
               j++;
               }

            /* scan items carried */
            /* changed tmp to name, want to scan the stacked objects instead of just finding
             * the first occurrence.. -- Alth May 25 99 */
   if((t_obj = get_obj_in_list_vis(ch, name, ch->carrying)))
      return(t_obj);

   /* scan room */
   /* changed tmp to name, want to scan the stacked objects instead of just finding
    * the first occurrence.. -- Alth May 25 99 */
   if((t_obj = get_obj_in_list_vis(ch, name, world[ch->in_room].contents)))
      return(t_obj);

   /* ok.. no luck yet. scan the entire obj list   */
   for(t_obj = object_list, j = 1; t_obj && (j <= k); t_obj = t_obj->next)
      if(isname(tmp, t_obj->name))
         if(CAN_SEE_OBJ(ch, t_obj))
            {
            if(j == k)
               return(t_obj);
            j++;
            }
   return(0);
}

void add_coins(P_obj pile, int copper, int silver, int gold, int platinum)
{
   char buf[200];
   const char *desc;
   int num, i, j, p;
   struct extra_descr_data *nd;

   if(!pile || (pile->type != ITEM_MONEY))
      return;

   if((copper < 0) || (silver < 0) || (gold < 0) || (platinum < 0))
      dump_core();

   pile->value[0] += copper;
   pile->value[1] += silver;
   pile->value[2] += gold;
   pile->value[3] += platinum;

   if((pile->value[0] < 0) || (pile->value[1] < 0) || (pile->value[2] < 0) || (pile->value[3] < 0))
      dump_core();

   num = (pile->value[0] + pile->value[1] + pile->value[2] + pile->value[3]);

   if(num < 0)
      dump_core();
   else if(num == 0)
      return;

   if(num >= 1000000)
      desc = "A mountain of coins.";
   else if(num >= 100000)
      desc = "A huge pile of coins.";
   else if(num >= 10000)
      desc = "A large pile of coins.";
   else if(num >= 1000)
      desc = "A pile of coins.";
   else if(num >= 30)
      desc = "A small pile of coins.";
   else if(num >= 10)
      desc = "A few coins.";
   else if(num > 5)
      desc = "A handful of coins.";
   else if(num > 1)
      {
      sprintf(buf, "%d coins.", num);
      desc = buf;
      }
   else
      {
      for(i = 0; i < 4; i++)
         if(pile->value[i])
            j = i;
      sprintf(buf, "A %s coin.", coin_names[j]);
      desc = buf;
      }

   if((pile->str_mask & STRUNG_DESC2) && pile->short_description)
      {
      free_string(pile->short_description);
      pile->short_description = NULL;
      }
   pile->str_mask |= STRUNG_DESC2;
   pile->short_description = (char *) str_dup(desc);
   pile->short_description[0] = tolower(pile->short_description[0]);
   pile->short_description[strlen(pile->short_description) - 1] = 0;
   if((pile->str_mask & STRUNG_DESC1) && pile->description)
      {
      free_string(pile->description);
      pile->description = NULL;
      }
   pile->str_mask |= STRUNG_DESC1;
   pile->description = (char *) str_dup(desc);
   nd = pile->ex_description;
   if(nd)
      {
      if(nd->description)
         {
         free_string(nd->description);
         nd->description = NULL;
         }
      if(num == 1)
         nd->description = (char *) str_dup(pile->description);
      else
         {
         strcpy(buf, "The pile appears to consist of: ");
         for(i = 0; i < 4; i++)
            {
            p = (pile->value[i] * 100) / num;
            if(p > 99)
               sprintf(buf + strlen(buf), "%s coins, ", coin_names[i]);
            else if(p >= 85)
               sprintf(buf + strlen(buf), "mostly %s coins, ", coin_names[i]);
            else if(p >= 65)
               sprintf(buf + strlen(buf), "3/4 %s coins, ", coin_names[i]);
            else if(p >= 40)
               sprintf(buf + strlen(buf), "half %s coins, ", coin_names[i]);
            else if(p >= 20)
               sprintf(buf + strlen(buf), "1/4 %s coins, ", coin_names[i]);
            else if(p >= 10)
               sprintf(buf + strlen(buf), "some %s coins, ", coin_names[i]);
            else if(p >= 1)
               sprintf(buf + strlen(buf), "a few %s coins, ", coin_names[i]);
            }
         buf[strlen(buf) - 2] = '.';
         buf[strlen(buf) - 1] = 0;
         nd->description = (char *) str_dup(buf);
         }
      }
}

P_obj create_money(int copper, int silver, int gold, int platinum)
{
   P_obj obj;

   obj = read_object(3, VIRTUAL);
   if(!obj)
      dump_core();

   add_coins(obj, copper, silver, gold, platinum);

   return(obj);
}


/* Generic Find, designed to find any object/character                    */
/* Calling :                                                              */
/*  *arg     is the string containing the string to be searched for.       */
/*           This string doesn't have to be a single word, the routine    */
/*           extracts the next word itself.                               */
/*  bitv..   All those bits that you want to "search through".            */
/*           Bit found will be result of the function                     */
/*  *ch      This is the person that is trying to "find"                  */
/* **tar_ch  Will be NULL if no character was found, otherwise points     */
/* **tar_obj Will be NULL if no object was found, otherwise points        */
/*                                                                        */
/* The routine returns 0 (if not found) otherwise, returns the bit in     */
/* which target was found.                                                */

int generic_find(char *arg, int sets_affs, P_char ch, P_char *tar_ch, P_obj *tar_obj)
{
   char name[MAX_INPUT_LENGTH];
   int i, found_obj = 0, found_char = 0, found = 0;
   static const char *ignore[] =
   {
      "the",
      "in",
      "on",
      "at",
      "\n"
   };

   name[0] = 0;

   /* Eliminate spaces and "ignore" words */
   while(*arg && !found)
      {
      for(; *arg == ' '; arg++);

      for(i = 0; (name[i] = *(arg + i)) && (name[i] != ' '); i++);
      name[i] = 0;
      arg += i;
      if(search_block(name, strlen(name), ignore, TRUE) > -1)
         found = TRUE;
      }

   if(!name[0])
      return(0);

   *tar_ch = 0;
   *tar_obj = 0;

   /* local people */

   if(IS_SET(sets_affs, FIND_CHAR_ROOM))
      {      /* Find person in room */
      if((*tar_ch = get_char_room_vis(ch, name)))
         found_char = FIND_CHAR_ROOM;
      }

   /* local objects */

   if(IS_SET(sets_affs, FIND_OBJ_EQUIP))
      {
      for(i = 0; i < MAX_WEAR; i++)
         if(ch->equipment[i] && isname(name, ch->equipment[i]->name))
            {
            *tar_obj = ch->equipment[i];
            found_obj = FIND_OBJ_EQUIP;
            break;
            }
      }

   if(!found_obj && IS_SET(sets_affs, FIND_OBJ_INV) && ch->carrying)
      {
      if((*tar_obj = get_obj_in_list_vis(ch, name, ch->carrying)))
         found_obj = FIND_OBJ_INV;
      }

   if(!found_obj && IS_SET(sets_affs, FIND_OBJ_ROOM) && world[ch->in_room].contents)
      {
      if((*tar_obj = get_obj_in_list_vis(ch, name, world[ch->in_room].contents)))
         found_obj = FIND_OBJ_ROOM;
      }

   /* check for both sorts of global searches */

   if(!found_char && IS_SET(sets_affs, FIND_CHAR_WORLD))
      {
      if((*tar_ch = get_char_in_game_vis(ch, name, FALSE)))
         found_char = FIND_CHAR_WORLD;
      }
   if(!found_obj && IS_SET(sets_affs, FIND_OBJ_WORLD))
      {
      if((*tar_obj = get_obj_vis(ch, name)))
         found_obj = FIND_OBJ_WORLD;
      }

   return((found_char | found_obj));
}

/* AC new additions */

/*
   ** This function is called when "ch" is quitting game.  Note that
   ** the relevant fields of "ch" must be intact before calling of this
   ** function.
   **
   ** This function stops other people from ignoring the quitting, thereby
   ** freeing them to ignore someone else ;)
 */

void ac_stopAllFromIgnoring(P_char ch)
{
   P_char c;

   for(c = PC_list; c; c = c->next)
      {
      if(c->desc && c->desc->connected)
         continue;  /* not in game */

      if(c->only.pc->ignored == ch)
         {
         send_to_char("The person you are ignoring has just quit the game.\n", c);
         c->only.pc->ignored = NULL;
         }
      }
}

void ac_stopAllFromDuelling(P_char ch)
{
   P_char c;

   for(c = PC_list; c; c = c->next)
      {
      if(c->desc && c->desc->connected)
         continue;  /* not in game */

      if(IS_DUELLING(c) && (GET_DUEL_TARGET(c) == ch))
         {
         send_to_char("The person you were duelling has just quit the game.\n", c);
         stop_duelling(c, ch, 0);
         }
      }
}

/*
   ** Basically same semantics for ac_stopAllFromIgnoring except
   ** this one is for consenting.
 */

void ac_stopAllFromConsenting(P_char ch)
{
   P_char c;

   for(c = PC_list; c; c = c->next)
      {
      if(c->desc && c->desc->connected)
         continue;  /* not in game */

      if(c->specials.consent == ch)
         {
         send_to_char("The person you consented to has just quit the game.\n", c);
         c->specials.consent = NULL;
         }
      }

   for(c = NPC_list; c; c = c->next)
      if(c->specials.consent == ch)
         c->specials.consent = NULL;
}

/* much adulterated, but hopefully working. JAB */
/* re-written almost entirely to allow for new object anti flags - 9/21/98 Shev */

int can_char_use_item(P_char ch, P_obj obj)
{
   if(!ch || !obj)
      return(FALSE);

   /*  if (IMMATERIAL(ch))     Removed until libs updated. --MIAX
       return FALSE; */

   if(HANDLESS(ch) && (CAN_WEAR(obj, ITEM_WEAR_FINGER) || CAN_WEAR(obj, ITEM_WEAR_HANDS)))
      return FALSE;  /* wield is handled elsewhere */
   if(!IS_HUMANOID(ch) &&
      (CAN_WEAR(obj, ITEM_WEAR_BODY) ||
       CAN_WEAR(obj, ITEM_WEAR_HEAD) ||
       CAN_WEAR(obj, ITEM_WEAR_LEGS) ||
       CAN_WEAR(obj, ITEM_WEAR_FEET) ||
       CAN_WEAR(obj, ITEM_WEAR_HANDS) ||
       CAN_WEAR(obj, ITEM_WEAR_ARMS) ||
       CAN_WEAR(obj, ITEM_WEAR_WRIST) ||
       CAN_WEAR(obj, ITEM_WEAR_EYES) ||
       CAN_WEAR(obj, ITEM_WEAR_FACE)))
      return FALSE;

   /* Don't impose class/race restrictions on mobs - CRM */
   if(IS_NPC(ch))
      return TRUE;

   if(IS_OBJ_STAT(obj, ITEM_ANTI_WA))
      {
      if((GET_CLASS(ch) == CLASS_MERCENARY) || IS_WARRIOR(ch))
         return FALSE;
      }

   if(IS_OBJ_STAT(obj, ITEM_ANTI_TH))
      {
      if(GET_CLASS(ch) == CLASS_BARD)
         {
         if(IS_OBJ_STAT(obj, ITEM_ANTI_MU))
            return FALSE;
         }
      else if(GET_CLASS(ch) == CLASS_BATTLECHANTER)
         {
         if(IS_OBJ_STAT(obj, ITEM_ANTI_CL))
            return FALSE;
         }
      else if((GET_CLASS(ch) == CLASS_MERCENARY) || IS_THIEF(ch))
         return FALSE;
      }

   if(IS_OBJ_STAT(obj, ITEM_ANTI_CL))
      {
      if(GET_CLASS(ch) == CLASS_BATTLECHANTER)
         {
         if(IS_OBJ_STAT(obj, ITEM_ANTI_TH))
            return FALSE;
         }
      else if(IS_CLERIC(ch))
         return FALSE;
      }

   if(IS_OBJ_STAT(obj, ITEM_ANTI_MU))
      {
      if(GET_CLASS(ch) == CLASS_BARD)
         {
         if(IS_OBJ_STAT(obj, ITEM_ANTI_TH))
            return FALSE;
         }
      else if((GET_CLASS(ch) == CLASS_PSIONICIST) || IS_MAGE(ch))
         return FALSE;
      }

   /* Removed for now - may be reinstated later. */
#if 0
   /* Illithid weight/head slot restrictions */
   if(GET_CLASS(ch) == CLASS_PSIONICIST)
      {
      if(CAN_WEAR(obj, ITEM_WEAR_HEAD)    ||
         CAN_WEAR(obj, ITEM_WEAR_FACE)    ||
         CAN_WEAR(obj, ITEM_WEAR_EARRING) ||
         CAN_WEAR(obj, ITEM_WEAR_EYES))
         return FALSE;
      if(GET_OBJ_WEIGHT(obj) > 5)
         return FALSE;
      }

#endif

   /* Now check the anti flags...fun fun fun */

   /* Sex discrimination first */
   if(IS_SET(obj->anti_flags, ITEM_ANTI_MALE) && (GET_SEX(ch) == SEX_MALE))
      return FALSE;

   if(IS_SET(obj->anti_flags, ITEM_ANTI_FEMALE) && (GET_SEX(ch) == SEX_FEMALE))
      return FALSE;

   /* Now race discrimination.. */

   switch(GET_RACE(ch))
      {
      case RACE_HUMAN:
         if(IS_SET(obj->anti_flags, ITEM_ANTI_HUMAN))
            return FALSE;
         break;
      case RACE_BARBARIAN:
         if(IS_SET(obj->anti_flags, ITEM_ANTI_BARBARIAN))
            return FALSE;
         break;
      case RACE_DROW:
         if(IS_SET(obj->anti_flags, ITEM_ANTI_DROWELF))
            return FALSE;
         break;
      case RACE_GREY:
         if(IS_SET(obj->anti_flags, ITEM_ANTI_GREYELF))
            return FALSE;
         break;
      case RACE_MOUNTAIN:
         if(IS_SET(obj->anti_flags, ITEM_ANTI_DWARF))
            return FALSE;
         break;
      case RACE_DUERGAR:
         if(IS_SET(obj->anti_flags, ITEM_ANTI_DUERGAR))
            return FALSE;
         break;
      case RACE_HALFLING:
         if(IS_SET(obj->anti_flags, ITEM_ANTI_HALFLING))
            return FALSE;
         break;
      case RACE_GNOME:
         if(IS_SET(obj->anti_flags, ITEM_ANTI_GNOME))
            return FALSE;
         break;
      case RACE_OGRE:
         if(IS_SET(obj->anti_flags, ITEM_ANTI_OGRE))
            return FALSE;
         break;
      case RACE_TROLL:
         if(IS_SET(obj->anti_flags, ITEM_ANTI_TROLL))
            return FALSE;
         break;
      case RACE_HALFELF:
         if(IS_SET(obj->anti_flags, ITEM_ANTI_HALFELF))
            return FALSE;
         break;
      case RACE_ILLITHID:
         if(IS_SET(obj->anti_flags, ITEM_ANTI_ILLITHID))
            return FALSE;
         break;
      case RACE_YUANTI:
         if(IS_SET(obj->anti_flags, ITEM_ANTI_YUANTI))
            return FALSE;
         break;
      case RACE_LICH:
         if(IS_SET(obj->anti_flags, ITEM_ANTI_LICH))
            return FALSE;
         break;
      case RACE_PORC:
         if(IS_SET(obj->anti_flags, ITEM_ANTI_ORC) || IS_OBJ_STAT(obj, ITEM_ANTI_EVILRACE))
            return FALSE;
         break;
      }

   /* You guessed it, time for class discrimination! */

   switch(GET_CLASS(ch))
      {
      case CLASS_WARRIOR:
         if(IS_SET(obj->anti_flags, ITEM_ANTI_WARRIOR))
            return FALSE;
         break;
      case CLASS_RANGER:
         if(IS_SET(obj->anti_flags, ITEM_ANTI_RANGER))
            return FALSE;
         break;
      case CLASS_PALADIN:
         if(IS_SET(obj->anti_flags, ITEM_ANTI_PALADIN))
            return FALSE;
         break;
      case CLASS_ANTIPALADIN:
         if(IS_SET(obj->anti_flags, ITEM_ANTI_ANTIPALADIN))
            return FALSE;
         break;
      case CLASS_CLERIC:
         if(IS_SET(obj->anti_flags, ITEM_ANTI_CLERIC))
            return FALSE;
         break;
      case CLASS_INVOKER:
         if(IS_SET(obj->anti_flags, ITEM_ANTI_INVOKER))
            return FALSE;
         break;
      case CLASS_DRUID:
         if(IS_SET(obj->anti_flags, ITEM_ANTI_DRUID))
            return FALSE;
         break;
      case CLASS_SHAMAN:
         if(IS_SET(obj->anti_flags, ITEM_ANTI_SHAMAN))
            return FALSE;
         break;
      case CLASS_ENCHANTER:
         if(IS_SET(obj->anti_flags, ITEM_ANTI_ENCHANTER))
            return FALSE;
         break;
      case CLASS_NECROMANCER:
         if(IS_SET(obj->anti_flags, ITEM_ANTI_NECROMANCER))
            return FALSE;
         break;
      case CLASS_CONJURER:
      case CLASS_ELEMENTALIST:
         if(IS_SET(obj->anti_flags, ITEM_ANTI_CONJURER))
            return FALSE;
         break;
      case CLASS_PSIONICIST:
         if(IS_SET(obj->anti_flags, ITEM_ANTI_PSIONICIST))
            return FALSE;
         break;
      case CLASS_THIEF:
         if(IS_SET(obj->anti_flags, ITEM_ANTI_THIEF))
            return FALSE;
         break;
         /*  case CLASS_ASSASSIN:
             if (IS_SET(obj->anti_flags, ITEM_ANTI_ASSASSIN))
               return FALSE;
             break;*/
         /* hack hack hack */
      case CLASS_ROGUE:
         if(/*IS_SET(obj->anti_flags, ITEM_ANTI_ASSASSIN) || */IS_SET(obj->anti_flags, ITEM_ANTI_THIEF))
            return FALSE;
         break;
      case CLASS_ILLUSIONIST:
         if(IS_SET(obj->anti_flags, ITEM_ANTI_ILLUSIONIST))
            return FALSE;
         break;
      case CLASS_BARD:
      case CLASS_BATTLECHANTER:
         if(IS_SET(obj->anti_flags, ITEM_ANTI_BARD))
            return FALSE;
         break;
      case CLASS_LICH:
         if(IS_SET(obj->anti_flags, ITEM_ANTI_LICH))
            return FALSE;
         break;
      }

   /* Made it this far, item is good to go */
   return TRUE;
}

#if 0  /* old version, too restrictive on NPCs, so I had to change it JAB 5/30/96 */
int
can_char_use_item(P_char ch, P_obj obj)
{
   if(!ch || !obj)
      return(FALSE);

   if(IMMATERIAL(ch))
      return FALSE;
   if(HANDLESS(ch) && (CAN_WEAR(obj, ITEM_WEAR_FINGER) || CAN_WEAR(obj, ITEM_WEAR_HANDS)))
      return FALSE;  /* wield is handled elsewhere */
   if(!IS_HUMANOID(ch) &&
      (CAN_WEAR(obj, ITEM_WEAR_BODY) ||
       CAN_WEAR(obj, ITEM_WEAR_HEAD) ||
       CAN_WEAR(obj, ITEM_WEAR_LEGS) ||
       CAN_WEAR(obj, ITEM_WEAR_FEET) ||
       CAN_WEAR(obj, ITEM_WEAR_HANDS) ||
       CAN_WEAR(obj, ITEM_WEAR_ARMS) ||
       CAN_WEAR(obj, ITEM_WEAR_WRIST) ||
       CAN_WEAR(obj, ITEM_WEAR_EYES) ||
       CAN_WEAR(obj, ITEM_WEAR_FACE)))
      return FALSE;

   /* special case for mercs */
   if((GET_CLASS(ch) == CLASS_MERCENARY) &&
      (!IS_OBJ_STAT(obj, ITEM_ANTI_WA) || !IS_OBJ_STAT(obj, ITEM_ANTI_TH)))
      return TRUE;
   if(IS_WARRIOR(ch) && IS_OBJ_STAT(obj, ITEM_ANTI_WA))
      return(FALSE);
   if(IS_THIEF(ch) && IS_OBJ_STAT(obj, ITEM_ANTI_TH))
      return(FALSE);
   if(IS_MAGE(ch) && IS_OBJ_STAT(obj, ITEM_ANTI_MU))
      return(FALSE);
   if(IS_CLERIC(ch) && IS_OBJ_STAT(obj, ITEM_ANTI_CL))
      return(FALSE);
   return TRUE;
}
#endif


#ifdef ARTIFACT
int artifactToChar(P_obj obj, P_char ch)
{
   char artifactString[82], time_buf[MAX_STRING_LENGTH];
   time_t ct;
   int i;

   if(!obj || !ch)
      {
      logit(LOG_DEBUG, "artifactToChar: no obj or char");
      dump_core();
      }
   /* there is no point in logging an item someone already owns */
   if(!strcmp(art_index[searchForArtifact(obj_index[obj->R_num].virtual)].owner,
              C_NAME(ch)))
      return TRUE;

   /* first major tracking step is to log a status message to admins */
   statuslog (GET_LEVEL(ch), "%s now has artifact: %s",
              C_NAME(ch), obj->short_description);

   /* next we access the artifact file and make a note of the owner change */

   /* we want to first build the 80 character string that goes into the */
   /* text artifact file */
   ct = time(0);
   time_buf[0] = 0;
   artifactString[0] = 0;
   {
      struct tm *tm_info = localtime(&ct);
      if (!tm_info) {
         logit(LOG_ERROR, "localtime() failed in artifactToChar");
         return FALSE;
      }
      strcpy(time_buf, asctime(tm_info));
   }
   time_buf[strlen(time_buf) - 1] = 0;
   sprintf (artifactString, "%5d %-40s %-33s\n",
            obj_index[obj->R_num].virtual, C_NAME(ch), time_buf);

   /* we first determine the artifact's location in the artifact file */
   for(i=0; i<totalArtifacts; i++)
      if(art_index[i].virtual == obj_index[obj->R_num].virtual)
         break;
   if(i >= totalArtifacts)
      {
      logit (LOG_DEBUG, "artifactToChar: array out of bounds");
      dump_core();
      }

#ifdef MEM_DEBUG
   mem_use[MEM_STRINGS] -= strlen(art_index[i].owner);
   mem_use[MEM_STRINGS] += strlen(C_NAME(ch));
#endif
   free ((char *) art_index[i].owner);
   CREATE(art_index[i].owner, char, (unsigned) strlen(C_NAME(ch)));
   strcpy (art_index[i].owner, C_NAME(ch));

   /* we own the artifact right now */
   art_index[i].ch = ch;

   fseek(art_f, art_index[i].pos, SEEK_SET);

   /* now we write to the file the updated info */
   fprintf (art_f, "%s", artifactString);
   fflush (art_f);

   /* now we check to see if there are nested artifacts inside this one */
   if(OBJ_IS_CONTAINER(obj))
      getNestedArtifacts (obj, ch);

   return TRUE;
}



int artifactFromChar(P_obj obj, P_char ch)
{
   time_t ct;
   int i;
   char time_buf[MAX_STRING_LENGTH], artifactString[82];
   const char *name = "noone";

   if(!obj || !ch)
      {
      logit(LOG_DEBUG, "artifactToChar: no obj or ch");
      dump_core();
      }
   /* first major tracking step is to log a status message to admins */
   statuslog (GET_LEVEL(ch), "%s no longer has artifact: %s",
              C_NAME(ch), obj->short_description);

   /* next we access the artifact file and make a note of the owner change */

   /* we want to first build the 80 character string that goes into the */
   /* text artifact file */
   ct = time(0);
   time_buf[0] = 0;
   artifactString[0] = 0;
   {
      struct tm *tm_info = localtime(&ct);
      if (!tm_info) {
         logit(LOG_ERROR, "localtime() failed in artifactToChar");
         return FALSE;
      }
      strcpy(time_buf, asctime(tm_info));
   }
   time_buf[strlen(time_buf) - 1] = 0;
   sprintf (artifactString, "%5d %-40s %-33s\n",
            obj_index[obj->R_num].virtual, name, time_buf);

   /* we first determine the artifact's location in the artifact file */
   for(i=0; i<totalArtifacts; i++)
      if(art_index[i].virtual == obj_index[obj->R_num].virtual)
         break;
   if(i >= totalArtifacts)
      {
      logit (LOG_DEBUG, "artifactFromChar: array out of bounds");
      dump_core();
      }

#ifdef MEM_DEBUG
   mem_use[MEM_STRINGS] -= strlen(art_index[i].owner);
   mem_use[MEM_STRINGS] += strlen(name);
#endif
   free ((char *) art_index[i].owner);
   CREATE(art_index[i].owner, char, (unsigned) strlen(name));
   strcpy (art_index[i].owner, name);

   fseek(art_f, art_index[i].pos, SEEK_SET);

   /* now we write to the file the updated info */
   fprintf (art_f, "%s", artifactString);
   fflush (art_f);

   /* now we check to see if there are nested artifacts inside this one */
   if(OBJ_IS_CONTAINER(obj))
      dropNestedArtifacts (obj);

   return TRUE;
}

int simpleArtifactFromChar(P_obj obj, P_char ch)
{
   time_t ct;
   int i;
   char time_buf[MAX_STRING_LENGTH], artifactString[82];
   const char *name = "noone";

   if(!ch)
      return FALSE;


   if(!obj)
      {
      logit(LOG_DEBUG, "artifactToChar: no obj");
      dump_core();
      }
   /* first major tracking step is to log a status message to admins */
   statuslog (GET_LEVEL(ch), "%s no longer has artifact: %s",
              C_NAME(ch), obj->short_description);

   /* next we access the artifact file and make a note of the owner change */

   /* we want to first build the 80 character string that goes into the */
   /* text artifact file */
   ct = time(0);
   time_buf[0] = 0;
   artifactString[0] = 0;
   {
      struct tm *tm_info = localtime(&ct);
      if (!tm_info) {
         logit(LOG_ERROR, "localtime() failed in artifactToChar");
         return FALSE;
      }
      strcpy(time_buf, asctime(tm_info));
   }
   time_buf[strlen(time_buf) - 1] = 0;
   sprintf (artifactString, "%5d %-40s %-33s\n",
            obj_index[obj->R_num].virtual, name, time_buf);

   /* we first determine the artifact's location in the artifact file */
   for(i=0; i<totalArtifacts; i++)
      if(art_index[i].virtual == obj_index[obj->R_num].virtual)
         break;
   if(i >= totalArtifacts)
      {
      logit (LOG_DEBUG, "artifactFromChar: array out of bounds");
      dump_core();
      }

#ifdef MEM_DEBUG
   mem_use[MEM_STRINGS] -= strlen(art_index[i].owner);
   mem_use[MEM_STRINGS] += strlen(name);
#endif
   free ((char *) art_index[i].owner);
   CREATE(art_index[i].owner, char, (unsigned) strlen(name));
   strcpy (art_index[i].owner, name);

   fseek(art_f, art_index[i].pos, SEEK_SET);

   /* now we write to the file the updated info */
   fprintf (art_f, "%s", artifactString);
   fflush (art_f);

   return TRUE;
}


/* if we have a deeply nested container containing an artifact, we want */
/* any artifacts this container might have and add them to a character */


void tagNestedArtifacts (P_obj obj, P_char ch)
{
   P_obj o;
   if(!obj)
      {
      logit (LOG_DEBUG, "tagNestedArtifacts: no obj");
      dump_core();
      }
   /* tag current object */
   if(IS_SET(obj_index[obj->R_num].spec_flag, IDX_ARTIFACT))
      art_index[searchForArtifact(obj_index[obj->R_num].virtual)].ch = ch;

   /* this might have nested artifacts... */
   if(OBJ_IS_CONTAINER(obj))
      {
      /* loop thru all of the contents of the container */
      for(o = obj->contains; o; o=o->next_content)
         tagNestedArtifacts (o, ch);
      }
}


void dropNestedArtifacts (P_obj obj)
{
   P_obj o;
   int aIndex;

   if(!obj)
      {
      logit (LOG_DEBUG, "dropNestedArtifacts: no obj");
      dump_core();
      }
   /* loop thru all of the contents of the container */
   for(o = obj->contains; o; o=o->next_content)
      {
      /* we must loop thru containers as well */
      if(IS_SET(obj_index[o->R_num].spec_flag, IDX_ARTIFACT))
         {
         aIndex = searchForArtifact(obj_index[o->R_num].virtual);
         if(art_index[aIndex].ch)
            artifactFromChar (o, art_index[aIndex].ch);
         }
      else if(OBJ_IS_CONTAINER(o))
         dropNestedArtifacts (o);
      }
}


void getNestedArtifacts (P_obj obj, P_char ch)
{
   int aIndex;
   P_obj o;

   if(!obj || !ch)
      {
      logit (LOG_DEBUG, "getNestedArtifacts: no obj or ch");
      dump_core();
      }
   /* loop thru all of the contents of the container */
   for(o = obj->contains; o; o=o->next_content)
      {
      if(IS_SET(obj_index[o->R_num].spec_flag, IDX_ARTIFACT))
         {
         aIndex = searchForArtifact(obj_index[o->R_num].virtual);
         if(art_index[aIndex].ch && (art_index[aIndex].ch != ch))
            artifactFromChar(o, art_index[aIndex].ch);
         artifactToChar(o, ch);
         }
      else
         /* we must loop thru containers as well */
         if(OBJ_IS_CONTAINER(o))
         getNestedArtifacts (o, ch);
      }
}


void tagBogusArtifact (P_obj obj)
{
   P_obj o;
   /* this is a MAJOR hack to prevent artifacts from being removed from
    * their rightful owners when a bogus copy of the artifact is extracted.
    * the cost value is set prior to a call to extract object. This
    * function recursively sets the cost for this object and its contents.
    * This should likely be fixed by creating a new data member for the
    * object struct someday. -- DDW
    */
   obj->cost = -1;
   for(o = obj->contains; o; o=o->next_content)
      tagBogusArtifact(o);
}


/* this basically tests the list of artifacts, and determines if
   an owner exists. If not, then it returns FALSE, otherwise TRUE */
int artifactIsOwned (int vnum)
{
   int artif_index;
   const char *namelist = "noone none nobody no";

   if((artif_index = searchForArtifact(vnum)) >= 0)
      /* artifact found */
      if(isname(art_index[artif_index].owner, namelist))
         {
         return FALSE;
         }
      else
         {
         return TRUE;
         }
   else
      return FALSE;
}


/* currently using binary search as the searching mechanism, assumes
   sorted artifact list (might need to sort) */
int searchForArtifact (int virtual)
{
   int bot, top, mid;

   bot = 0;
   top = totalArtifacts;
   for(;;)
      {
      mid = (bot+top) / 2;
      if(art_index[mid].virtual == virtual)
         return mid;
      if(bot >= top)
         {
         logit(LOG_DEBUG, "searchForArtifact: %d not in database", virtual);
         return(-1);
         }
      if(art_index[mid].virtual > virtual)
         top = mid - 1;
      else
         bot = mid + 1;
      }
}
#endif

void extract_cart(P_obj cart)
{
   P_obj obj,obj_save;

   if(!cart)
      return;

   obj=cart->contains;

   while(obj)
      {
      obj_save=obj->next_content;
      extract_obj(obj);
      obj=obj_save;
      }

   extract_obj(cart);
}

void add_weight_coins(P_obj obj, int copper, int silver, int gold, int platinum)
{

   int num, weight;

   num = (copper + silver + gold + platinum);

   if(num < 0)
      dump_core();
   else if(num == 0)
      return;

   if((GET_OBJ_WEIGHT(obj)) == 0)
      weight = (num / 25);
   else
      weight = ((GET_OBJ_WEIGHT(obj)) + (num / 25));


   weight_change_object(obj, weight);

}
/* a function to scan for "all" or "all.x" */
int find_all_dots(char *arg)
{
   if(!strcmp(arg, "all"))
      return(FIND_ALL);
   else if(!strncmp(arg, "all.", 4))
      {
      strcpy(arg, arg + 4);
      return(FIND_ALLDOT);
      }
   else
      return(FIND_INDIV);
}
