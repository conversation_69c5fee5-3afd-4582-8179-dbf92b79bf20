/* *************************************************************************
 *  file: Interpreter.h , Command interpreter module.        Part of Outcast *
 *  Usage: Procedures interpreting user command                            *
 ************************************************************************* */

#ifndef _SOJ_INTERP_H_
#define _SOJ_INTERP_H_

#define GRANT_FLAGS(ch) (ch)->only.pc->grant
#define GRANT_FLAGS2(ch) (ch)->only.pc->grant2

#define LOCK_CREATE             0x00000001
#define LOCK_CONNECTIONS        0x00000002
#define LOCK_MAX_PLAYERS        0x00000004

#define CMD_NUKES_HIDE(cmd)     ((cmd > 8) && \
                                ((cmd) != CMD_LOOK) && \
                                ((cmd) != CMD_GSAY) && \
                                ((cmd) != CMD_TELL) && \
                                ((cmd) != CMD_SCAN) && \
                                ((cmd) != CMD_AWARENESS))

#define IS_SONIC_CMD(cmd) ((cmd > -1) && \
                           (((cmd) == CMD_SAY) || \
                            ((cmd) == CMD_SAY2) || \
                            ((cmd) == CMD_GSHOUT) || \
                            ((cmd) == CMD_LAUGH) || \
                            ((cmd) == CMD_GIGGLE) || \
                            ((cmd) == CMD_GROWL) || \
                            ((cmd) == CMD_SCREAM) || \
                            ((cmd) == CMD_INSULT) || \
                            ((cmd) == CMD_SIGH) || \
                            ((cmd) == CMD_EMOTE) || \
                            ((cmd) == CMD_CRY) || \
                            ((cmd) == CMD_WHISPER) || \
                            ((cmd) == CMD_CACKLE) || \
                            ((cmd) == CMD_ASK) || \
                            ((cmd) == CMD_ORDER) || \
                            ((cmd) == CMD_ACCUSE) || \
                            ((cmd) == CMD_APPLAUD) || \
                            ((cmd) == CMD_BURP) || \
                            ((cmd) == CMD_CHUCKLE) || \
                            ((cmd) == CMD_CLAP) || \
                            ((cmd) == CMD_COUGH) || \
                            ((cmd) == CMD_FART) || \
                            ((cmd) == CMD_GASP) || \
                            ((cmd) == CMD_GROAN) || \
                            ((cmd) == CMD_HICCUP) || \
                            ((cmd) == CMD_MOAN) || \
                            ((cmd) == CMD_PURR) || \
                            ((cmd) == CMD_SING) || \
                            ((cmd) == CMD_SNAP) || \
                            ((cmd) == CMD_SNEEZE) || \
                            ((cmd) == CMD_SNICKER) || \
                            ((cmd) == CMD_SNORE) || \
                            ((cmd) == CMD_WHISTLE) || \
                            ((cmd) == CMD_CURSE) || \
                            ((cmd) == CMD_BEG) || \
                            ((cmd) == CMD_TAUNT) || \
                            ((cmd) == CMD_WHINE) || \
                            ((cmd) == CMD_YODEL) || \
                            ((cmd) == CMD_RECITE) || \
                            ((cmd) == CMD_EMOTE2) || \
                            ((cmd) == CMD_GSAY) || \
                            ((cmd) == CMD_SCARE) || \
                            ((cmd) == CMD_SCOLD) || \
                            ((cmd) == CMD_SHUSH) || \
                            ((cmd) == CMD_TEASE) || \
                            ((cmd) == CMD_WHIMPER) || \
                            ((cmd) == CMD_SHOUT) || \
                            ((cmd) == CMD_APOLOGIZE) || \
                            ((cmd) == CMD_ACK) || \
                            ((cmd) == CMD_CHEER) || \
                            ((cmd) == CMD_SNORT) || \
                            ((cmd) == CMD_BARK) || \
                            ((cmd) == CMD_DUH) || \
                            ((cmd) == CMD_GRUMBLE) || \
                            ((cmd) == CMD_WHEEZE) || \
                            ((cmd) == CMD_HISS) || \
                            ((cmd) == CMD_HELLO) || \
                            ((cmd) == CMD_HUM) || \
                            ((cmd) == CMD_ROAR) || \
                            ((cmd) == CMD_GRUNT) || \
                            ((cmd) == CMD_TARZAN) || \
                            ((cmd) == CMD_CHANT) || \
                            ((cmd) == CMD_INTRODUCE) || \
                            ((cmd) == CMD_MUTTER)))

extern int game_locked;

/* these defines are here to facilitate adding/deleting/reordering commands.
   Especially for things like specials looking for specific commands

   NOTE!  the number assigned to the CMD_ macros MUST match the position of the
   corresponding word in the *command[] list:

   CMD_KISS is 9, "kiss" must be the 9th string in *commands[] (interp.c)
   */

#define CMD_NORTH            1
#define CMD_EAST             2
#define CMD_SOUTH            3
#define CMD_WEST             4
#define CMD_UP               5
#define CMD_DOWN             6
#define CMD_ENTER            7
#define CMD_EXITS            8
#define CMD_KISS             9
#define CMD_GET             10
#define CMD_DRINK           11
#define CMD_EAT             12
#define CMD_WEAR            13
#define CMD_WIELD           14
#define CMD_LOOK            15
#define CMD_SCORE           16
#define CMD_SAY             17
#define CMD_GSHOUT          18
#define CMD_TELL            19
#define CMD_INVENTORY       20
#define CMD_QUI             21
#define CMD_BOUNCE          22
#define CMD_SMILE           23
#define CMD_DANCE           24
#define CMD_KILL            25
#define CMD_CAST            26
#define CMD_LAUGH           27
#define CMD_GIGGLE          28
#define CMD_SHAKE           29
#define CMD_PROJECT         30
#define CMD_GROUP           31
#define CMD_SCREAM          32
#define CMD_INSULT          33
#define CMD_COMFORT         34
#define CMD_NOD             35
#define CMD_SIGH            36
#define CMD_SULK            37
#define CMD_HELP            38
#define CMD_WHO             39
#define CMD_EMOTE           40
#define CMD_ECHO            41
#define CMD_STAND           42
#define CMD_SIT             43
#define CMD_REST            44
#define CMD_SLEEP           45
#define CMD_WAKE            46
#define CMD_FORCE           47
#define CMD_TRANSFER        48
#define CMD_HUG             49
#define CMD_SNUGGLE         50
#define CMD_CUDDLE          51
#define CMD_NUZZLE          52
#define CMD_CRY             53
#define CMD_NEWS            54
#define CMD_EQUIPMENT       55
#define CMD_BUY             56
#define CMD_SELL            57
#define CMD_VALUE           58
#define CMD_LIST            59
#define CMD_DROP            60
#define CMD_GOTO            61
#define CMD_WEATHER         62
#define CMD_READ            63
#define CMD_POUR            64
#define CMD_GRAB            65
#define CMD_REMOVE          66
#define CMD_PUT             67
#define CMD_SHUTDOW         68
#define CMD_SAVE            69
#define CMD_HIT             70
#define CMD_STRING          71
#define CMD_GIVE            72
#define CMD_QUIT            73
#define CMD_STAT            74
#define CMD_INNATE          75
#define CMD_TIME            76
#define CMD_LOAD            77
#define CMD_PURGE           78
#define CMD_SHUTDOWN        79
#define CMD_IDEA            80
#define CMD_TYPO            81
#define CMD_BUG             82
#define CMD_WHISPER         83
#define CMD_CACKLE          84
#define CMD_AT              85
#define CMD_ASK             86
#define CMD_ORDER           87
#define CMD_SIP             88
#define CMD_TASTE           89
#define CMD_SNOOP           90
#define CMD_FOLLOW          91
#define CMD_RENT            92
#define CMD_OFFER           93
#define CMD_POKE            94
#define CMD_ADVANCE         95
#define CMD_ACC             96
#define CMD_GRIN            97
#define CMD_BOW             98
#define CMD_OPEN            99
#define CMD_CLOSE          100
#define CMD_LOCK           101
#define CMD_UNLOCK         102
#define CMD_MREPORT        103
#define CMD_APPLAUD        104
#define CMD_BLUSH          105
#define CMD_BURP           106
#define CMD_CHUCKLE        107
#define CMD_CLAP           108
#define CMD_COUGH          109
#define CMD_CURTSEY        110
#define CMD_FART           111
#define CMD_FLIP           112
#define CMD_FONDLE         113
#define CMD_FROWN          114
#define CMD_GASP           115
#define CMD_GLARE          116
#define CMD_GROAN          117
#define CMD_GROPE          118
#define CMD_HICCUP         119
#define CMD_LICK           120
#define CMD_LOVE           121
#define CMD_MOAN           122
#define CMD_NIBBLE         123
#define CMD_POUT           124
#define CMD_PURR           125
#define CMD_RUFFLE         126
#define CMD_SHIVER         127
#define CMD_SHRUG          128
#define CMD_SING           129
#define CMD_SLAP           130
#define CMD_SMIRK          131
#define CMD_SNAP           132
#define CMD_SNEEZE         133
#define CMD_SNICKER        134
#define CMD_SNIFF          135
#define CMD_SNORE          136
#define CMD_SPIT           137
#define CMD_SQUEEZE        138
#define CMD_STARE          139
#define CMD_STRUT          140
#define CMD_THANK          141
#define CMD_TWIDDLE        142
#define CMD_WAVE           143
#define CMD_WHISTLE        144
#define CMD_WIGGLE         145
#define CMD_WINK           146
#define CMD_YAWN           147
#define CMD_SNOWBALL       148
#define CMD_WRITE          149
#define CMD_HOLD           150
#define CMD_FLEE           151
#define CMD_SNEAK          152
#define CMD_HIDE           153
#define CMD_BACKSTAB       154
#define CMD_PICK           155
#define CMD_STEAL          156
#define CMD_BASH           157
#define CMD_RESCUE         158
#define CMD_KICK           159
#define CMD_FRENCH         160
#define CMD_COMB           161
#define CMD_MASSAGE        162
#define CMD_TICKLE         163
#define CMD_PRACTICE       164
#define CMD_PAT            165
#define CMD_EXAMINE        166
#define CMD_TAKE           167
#define CMD_INFO           168
#define CMD_SPELLS         169
#define CMD_PRACTISE       170
#define CMD_CURSE          171
#define CMD_USE            172
#define CMD_WHERE          173
#define CMD_LEVELS         174
#define CMD_REROLL         175
#define CMD_PRAY           176
#define CMD_EMOTE2         177  /* ":" */
#define CMD_BEG            178
#define CMD_BLEED          179
#define CMD_CRINGE         180
#define CMD_DREAM          181
#define CMD_FUME           182
#define CMD_GROVEL         183
#define CMD_HOP            184
#define CMD_NUDGE          185
#define CMD_PEER           186
#define CMD_POINT          187
#define CMD_PONDER         188
#define CMD_PUNCH          189
#define CMD_SNARL          190
#define CMD_SPANK          191
#define CMD_STEAM          192
#define CMD_TACKLE         193
#define CMD_TAUNT          194
#define CMD_THINK          195
#define CMD_WHINE          196
#define CMD_WORSHIP        197
#define CMD_YODEL          198
#define CMD_TOGGLE         199
#define CMD_WIZMSG         200
#define CMD_CONSIDER       201
#define CMD_GROWL          202
#define CMD_RESTORE        203
#define CMD_RETURN         204
#define CMD_SWITCH         205
#define CMD_QUAFF          206
#define CMD_RECITE         207
#define CMD_USERS          208
#define CMD_POSE           209
#define CMD_SILENCE        210
#define CMD_WIZHELP        211
#define CMD_CREDITS        212
#define CMD_DISBAND        213
#define CMD_VIS            214
#define CMD_LFLAGS         215
#define CMD_POOFIN         216
#define CMD_WIZLIST        217
#define CMD_DISPLAY        218
#define CMD_ECHOA          219
#define CMD_DEMOTE         220
#define CMD_POOFOUT        221
#define CMD_CIRCLE         222
#define CMD_BALANCE        223
#define CMD_WIZLOCK        224
#define CMD_DEPOSIT        225
#define CMD_WITHDRAW       226
#define CMD_IGNORE         227
#define CMD_DRAIN          228
#define CMD_TITLE          229
#define CMD_AGGR           230
#define CMD_GSAY           231
#define CMD_CONSENT        232
#define CMD_SETBIT         233
#define CMD_HITALL         234
#define CMD_TRAP           235
#define CMD_MURDER         236
#define CMD_GLANCE         237
#define CMD_PUKE           238
#define CMD_MASS_PROJECT   239
#define CMD_FILL           240
#define CMD_OOC            241
#define CMD_NOKILL         242
#define CMD_PAGE           243
#define CMD_COMMANDS       244
#define CMD_ATTRIBUTES     245
#define CMD_RULES          246
#define CMD_TRACK          247
#define CMD_ANALYZE  CMD_TRACK
#define CMD_LISTEN         248
#define CMD_DISARM         249
#define CMD_PET            250
#define CMD_DELETE         251
#define CMD_BAN            252
#define CMD_ALLOW          253
#define CMD_PLAY           254
#define CMD_MOVE           255  /* first chessboard command */
#define CMD_BRIBE          256
#define CMD_BONK           257
#define CMD_CALM           258
#define CMD_RUB            259
#define CMD_CENSOR         260
#define CMD_CHOKE          261
#define CMD_DROOL          262
#define CMD_FLEX           263
#define CMD_JUMP           264
#define CMD_LEAN           265
#define CMD_MOON           266
#define CMD_OGLE           267
#define CMD_PANT           268
#define CMD_PINCH          269
#define CMD_PUSH           270
#define CMD_SCARE          271
#define CMD_SCOLD          272
#define CMD_SEDUCE         273
#define CMD_SHOVE          274
#define CMD_SHUDDER        275
#define CMD_SHUSH          276
#define CMD_SLOBBER        277
#define CMD_SMELL          278
#define CMD_SNEER          279
#define CMD_SPIN           280
#define CMD_SQUIRM         281
#define CMD_STOMP          282
#define CMD_STRANGLE       283
#define CMD_STRETCH        284
#define CMD_TAP            285
#define CMD_TEASE          286
#define CMD_TIP            287
#define CMD_TWEAK          288
#define CMD_TWIRL          289
#define CMD_UNDRESS        290
#define CMD_WHIMPER        291
#define CMD_EXCHANGE       292
#define CMD_RELEASE        293
#define CMD_SEARCH         294
#define CMD_JOIN           295
#define CMD_CAMP           296
#define CMD_SECRET         297
#define CMD_LOOKUP         298
#define CMD_REPORT         299
#define CMD_SPLIT          300
#define CMD_WORLD          301
#define CMD_JUNK           302
#define CMD_PETITION       303
#define CMD_DO             304
#define CMD_SAY2           305  /* "'" */
#define CMD_CARESS         306
#define CMD_BURY           307
#define CMD_DONATE         308
#define CMD_SHOUT          309
#define CMD_DISEMBARK      310
#define CMD_PANIC          311
#define CMD_NOG            312
#define CMD_TWIBBLE        313
#define CMD_BLEH           314
#define CMD_LIGHTNING      315
#define CMD_SWEEP          316
#define CMD_APOLOGIZE      317
#define CMD_AFK            318
#define CMD_LAG            319
#define CMD_TOUCH          320
#define CMD_SCRATCH        321
#define CMD_WINCE          322
#define CMD_TOSS           323
#define CMD_FLAME          324
#define CMD_ARCH           325
#define CMD_AMAZE          326
#define CMD_BATHE          327
#define CMD_EMBRACE        328
#define CMD_BRB            329
#define CMD_ACK            330
#define CMD_CHEER          331
#define CMD_SNORT          332
#define CMD_EYEBROW        333
#define CMD_BANG           334
#define CMD_PILLOW         335
#define CMD_NAP            336
#define CMD_NOSE           337
#define CMD_RAISE          338
#define CMD_HAND           339
#define CMD_PULL           340
#define CMD_TUG            341
#define CMD_WET            342
#define CMD_MOSH           343
#define CMD_WAIT           344
#define CMD_HI5            345
#define CMD_ENVY           346
#define CMD_FLIRT          347
#define CMD_BARK           348
#define CMD_WHAP           349
#define CMD_ROLL           350
#define CMD_BLINK          351
#define CMD_DUH            352
#define CMD_GAG            353
#define CMD_GRUMBLE        354
#define CMD_DROPKICK       355
#define CMD_WHATEVER       356
#define CMD_FOOL           357
#define CMD_NOOGIE         358
#define CMD_MEDITATE       359
#define CMD_SMOKE          360
#define CMD_WHEEZE         361
#define CMD_BIRD           362
#define CMD_BOGGLE         363
#define CMD_HISS           364
#define CMD_BITE           365
#define CMD_TELEPORT       366
#define CMD_BANDAGE        367
#define CMD_BLOW           368
#define CMD_BORED          369
#define CMD_BYE            370
#define CMD_CONGRATULATE   371
#define CMD_DUCK           372
#define CMD_FLUTTER        373
#define CMD_GOOSE          374
#define CMD_GULP           375
#define CMD_HALO           376
#define CMD_HELLO          377
#define CMD_HICKEY         378
#define CMD_HOSE           379
#define CMD_HUM            380
#define CMD_IMPALE         381
#define CMD_JAM            382
#define CMD_KNEEL          383
#define CMD_MOURN          384
#define CMD_PROTECT        385
#define CMD_PUZZLE         386
#define CMD_ROAR           387
#define CMD_ROSE           388
#define CMD_SALUTE         389
#define CMD_SKIP           390
#define CMD_SWAT           391
#define CMD_TONGUE         392
#define CMD_WOOPS          393
#define CMD_ZONE           394
#define CMD_TRIP           395
#define CMD_MELT           396
#define CMD_SHAPECHANGE    397
#define CMD_ASSIST         398
#define CMD_DOORBASH       399
#define CMD_EXP            400
#define CMD_ROFL           401
#define CMD_AGREE          402
#define CMD_HAPPY          403
#define CMD_PUCKER         404
#define CMD_SPAM           405
#define CMD_BEER           406
#define CMD_BODYSLAM       407
#define CMD_SACRIFICE      408
#define CMD_TERMINATE      409
#define CMD_CD             410
#define CMD_MEMORIZE       411
#define CMD_FORGET         412
#define CMD_HEADBUTT       413
#define CMD_SHADOW         414
#define CMD_RIDE           415
#define CMD_MOUNT          416
#define CMD_DISMOUNT       417
#define CMD_DEBUG          418
#define CMD_FREEZE         419
#define CMD_BBL            420
#define CMD_GAPE           421
#define CMD_VETO           422
#define CMD_JK             423
#define CMD_TIPTOE         424
#define CMD_GRUNT          425
#define CMD_HOLDON         426
#define CMD_IMITATE        427
#define CMD_TANGO          428
#define CMD_TARZAN         429
#define CMD_POUNCE         430
#define CMD_CHEEK          431
#define CMD_LAYHAND        432
#define CMD_AWARENESS      433
#define CMD_SELFPRESERVE   434
#define CMD_SPRINGLEAP     435
#define CMD_FEIGNDEATH     436
#define CMD_CHANT          437
#define CMD_DRAG           438
#define CMD_SPEAK          439
#define CMD_RELOAD         440
#define CMD_DRAGONPUNCH    441
#define CMD_REVOKE         442
#define CMD_GRANT          443
#define CMD_WHOD           444
#define CMD_MOTD           445
#define CMD_ZRESET         446
#define CMD_FULL           447
#define CMD_WELCOME        448
#define CMD_INTRODUCE      449
#define CMD_SWEAT          450
#define CMD_MUTTER         451
#define CMD_LUCKY          452
#define CMD_AYT            453
#define CMD_FIDGET         454
#define CMD_FUZZY          455
#define CMD_SNOOGIE        456
#define CMD_READY          457
#define CMD_PLONK          458
#define CMD_HERO           459
#define CMD_LOST           460
#define CMD_CLEAR          461  /* another chessboard command */
#define CMD_FLASH          462
#define CMD_CURIOUS        463
#define CMD_HUNGER         464
#define CMD_THIRST         465
#define CMD_ECHOZ          466
#define CMD_PTELL          467
#define CMD_SCRIBE         468
#define CMD_TEACH          469
#define CMD_REINITPHYS     470
#define CMD_FINGER         471
#define CMD_ACCEPT         472
#define CMD_DECLINE        473
#define CMD_SUMMON         474
#define CMD_CLONE          475
#define CMD_APPLY          476   /* apply poison */
#define CMD_ZAP            477
#define CMD_ALERT          478
#define CMD_RECLINE        479
#define CMD_KNOCK          480
#define CMD_SKILLS         481
#define CMD_POWERCAST      482
#define CMD_BERSERK        483
#define CMD_FAQ            484
#define CMD_DISENGAGE      485
#define CMD_RETREAT        486
#define CMD_INROOM         487
#define CMD_WHICH          488
#define CMD_REVOKETITLE    489
#define CMD_SETHOME        490
#define CMD_NOTES          491
#define CMD_WIZNEWS        492
#define CMD_ABORT          493
#define CMD_CEASEFIRE      494
#define CMD_ATHROW         495
#define CMD_AFIRE          496
#define CMD_THROW          497
#define CMD_FIRE           498
#define CMD_AMMO           499
#define CMD_MINDBLAST      500
#define CMD_ADRENALIZE     501
#define CMD_ENHANCE        502
#define CMD_SUSTAIN        503
#define CMD_DISPERSE       504
#define CMD_COMBATMIND     505
#define CMD_BODYSHIFT      506
#define CMD_AMPLIFY        507
#define CMD_AURASIGHT      508
#define CMD_EMPATHY        509
#define CMD_EQUALIBRIUM    510
#define CMD_TELEKINATE     511
#define CMD_TRANCE         512
#define CMD_DOMINATE       513
#define CMD_STASIS         514
#define CMD_RIFT           515
#define CMD_ULTRABLAST     516
#define CMD_DANGERSENSE    517
#define CMD_PROJECT_FORCE  518
#define CMD_DETONATE       519
#define CMD_DEATHFIELD     520
#define CMD_BODYCONTROL    521
#define CMD_CATFALL        522
#define CMD_FLESHARMOR     523
#define CMD_REDUCE         524
#define CMD_EXPAND         525
#define CMD_SHIFT          526
#define CMD_MASS_DOMINATE  527
#define CMD_GLOBE          528
#define CMD_TOWER          529
#define CMD_ATTRACTION     530
#define CMD_SYNAPTIC_STATIC  531
/* #define CMD_ALTER_AURA     532 */
#define CMD_CANIBALIZE     533
#define CMD_SKILL_ENHANCE  534
#define CMD_STASIS_FIELD   535
#define CMD_PROJECT_ASK    536
#define CMD_DARKNESS       537
#define CMD_COLLECT        538
#define CMD_PREP           539
#define CMD_CHILLTOUCH     540
#define CMD_SNAKEBITE      541
#define CMD_TAILSWEEP      542
#define CMD_BEFRIENDREPTILE  543
#define CMD_FORAGE         544
#define CMD_DETECTTRAPS    545
#define CMD_DISARMTRAPS    546
#define CMD_SIGN           547
#define CMD_ESCAPE         548
#define CMD_RENAME         549
#define CMD_LOADCHAR       550
#define CMD_GETCART        551
#define CMD_LEAVECART      552
#define CMD_CHANGELOG      553
#define CMD_SCAN           554
#define CMD_SHIELDPUNCH    555
#define CMD_DISGUISE       556
#define CMD_STORE          557
#define CMD_STABLE         558
#define CMD_RETRIEVE       559
#define CMD_CLAIM          560
#define CMD_CCONTROL       561
#define CMD_ACONTROL       562
#define CMD_ASSOC          563
#define CMD_ASCLIST        564
#define CMD_EMS            565
#define CMD_REJECT         566
#define CMD_OUTCAST        567
#define CMD_ESTAT          568
#define CMD_ELIST          569
#define CMD_ELOOK          570
#define CMD_EHEAD          571
#define CMD_RADD           572
#define CMD_RSHOW          573
#define CMD_RDEL           574
#define CMD_RLIST          575
#define CMD_EKILL          576
#define CMD_EFREEZE        577
#define CMD_ETHAW          578
#define CMD_ERESET         579
#define CMD_EHELP          580
#define CMD_EFORCE         581
#define CMD_ESTATUS        582
#define CMD_UNGROUP        583
#define CMD_GGET           584
#define CMD_GADD           585
#define CMD_GDEL           586
#define CMD_GGIVE          587
#define CMD_GTOGGLE        588
#define CMD_GTELL          589
#define CMD_GLIST          590
#define CMD_SOJPARSE       591
#define CMD_HCONTROL       592
#define CMD_HOUSE          593
#define CMD_CONSTRUCT      594
#define CMD_NCC            595
#define CMD_SETHELPER      596
#define CMD_PROC           597
#define CMD_DICE           598
#define CMD_JUSTICE        599
#define CMD_LWITNESS       600
#define CMD_EMIT           601
#define CMD_MOUNTED_CHARGE 602
#define CMD_FUNCTION       603
#define CMD_PAY            604
#define CMD_TURN_IN        605
#define CMD_CRIME_REPORT   606
#define CMD_PARDON         607
#define CMD_DUEL           608
#define CMD_ACCUSE         609
#define CMD_LOOT           610
#define CMD_TIE_UP         611
#define CMD_UNTIE          612
#define CMD_UNBIND         613
#define CMD_VIRTUOSO       614
#define CMD_ASSASSINATE    615
#define CMD_GARROTE        616
#define CMD_CONTRACT       617
#define CMD_TROPHY         618
#define CMD_RECALL         619
#define CMD_JCONTROL       620
#define CMD_GENLOG         621
#define CMD_OLC            622
#define CMD_OLR            623
#define CMD_OLCDB          624
#define CMD_STAT_NOPAGE    625
#define CMD_MIX            626
#define CMD_LIFETAP        627
#define CMD_MOBGEN         628
#define CMD_ACHEXIT        629
#define CMD_HORDE          630
#define CMD_AUCTION        631
#define CMD_GRESET         632
#define CMD_GCOMBAT        633
#define CMD_CSTAT          634
#define CMD_CP             635
#define CMD_UNDECLINE      636
#define CMD_DENY           637
#define CMD_TITHE          638
#define CMD_CHARGE         639
#define CMD_REPLY          640
#define CMD_GREPORT        641
#define CMD_LORE           642
#define CMD_ECHOT          643
#define CMD_WARCHANT       644
#define CMD_ARENA          645
#define CMD_QWIZ           646
#define CMD_MREAD          647
#define CMD_MWRITE         648
#define CMD_MLIST          649
#define CMD_MDELETE        650
#define CMD_NUKE           651
#define CMD_PRIORITIZE     652
#define CMD_ACCOMPANY      653
#define CMD_CLIMB          654
#define CMD_MREPLY         655
#define CMD_IPSHARE        656
#define CMD_ADDPRESTIGE    657
#define CMD_FINDQUEST      658
#define CMD_LOADMOB        659
#define CMD_ALLOWGROUP     660
#define CMD_HOWL           661
#define CMD_OUTFLANK       662
#define CMD_STRAFE         663
#define CMD_HIRE           664
#define CMD_EMBARK         665
#define CMD_DELIVER        666
#define CMD_APPOINT        667
#define CMD_STORAGE        668
#define CMD_GAMBLE         669
#define CMD_EMPTY          670
#define CMD_REAGGRO        671
#define CMD_DEAGGRO        672
#define CMD_CREATEMOB      673
#define CMD_HAMSTRING      674
#define CMD_RPTOGGLE       675
#define CMD_DEPART         676
#define CMD_REORDER        677
#define CMD_STOPPETFOLLOW  678
#define CMD_STOPPCFOLLOW   679
#define CMD_SETPK	   680
#define CMD_EQRATE         681

#define MAX_CMD_LIST       682  /* make sure this is always 1 more than highest command number. */


#define MAX_GRANT_CMDS     (GRANT_BYTES * 8) /* Max number of grantable commands */

/* Reserved for the mob social special commands  -- Altherog Sept 07 1997 */

#define CMD_SOC_FIRST             999 /* Start Marker */
#define CMD_SOC_ECHOZ_INDOOR     1000 /* Indoor Echo Zone */
#define CMD_SOC_ECHOZ_OUTDOOR    1001 /* Indoor Echo Zone */
#define CMD_SOC_ECHOZ_ALL        1002 /* Whole Zone Echo */
#define CMD_SOC_ECHO             1003 /* Room only */
#define CMD_SOC_PATH             1004  // Initiate pre-defined path
//#define CMD_SOC_ECHOT		 1005 // Send to character only
#define CMD_SOC_LAST             1005 /* End Marker - Increase it if adding more
                                       * special commands. */

#endif /* _SOJ_INTERP_H_ */
