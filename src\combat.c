/*****************************************************************************
 *  File: combat.c                                        Outcast III MUD    *
 *  Usage: Functions relating to combat                                      *
 *  Author: <PERSON> (<PERSON><PERSON><PERSON>) and Outcast coders past and present *
 *                                                                           *
 *  Copyright 2003 - Outcast III MUD                                         *                                                                                                                    *
 *************************************************************************** */

#include <stdio.h>
#include <string.h>
#include <time.h>

#include "comm.h"
#include "db.h"
#include "events.h"
#include "interp.h"
#include "newjustice.h"
#include "prototypes.h"
#include "specs.prototypes.h"
#include "specs.include.h"
#include "skillrec.h"
#include "spells.h"
#include "race_class.h"
#include "utils.h"
#include "weather.h"

#ifdef NEWCOMBAT

// External Variables

extern ubyte sets_code_control[CODE_CONTROL_BYTES];
extern P_char NPC_list;
extern P_desc descriptor_list;
extern P_event event_type_list[];
extern P_event current_event;
extern P_index mob_index;
extern P_index obj_index;
extern P_obj object_list;
extern P_room world;
extern const char *spell_wear_off_msg[];
extern const int exp_table[TOTALCLASS][52];
extern const struct stat_data stat_factor[];
extern const struct trait_data racial_traits[];
extern int phase;
extern int power_table[];
extern int pulse;
extern int top_of_world;
extern int current_spell_being_cast;
extern struct agi_app_type agi_app[];
extern struct dex_app_type dex_app[];
extern struct message_list fight_messages[];
extern struct str_app_type str_app[];
extern struct time_info_data time_info;
extern struct zone_data *zone_table;

extern int store_thac0_classMod;
extern int store_thac0_levelMod;
extern int store_thac0_weaponSkillMod;
extern int store_thac0_calcWeaponSkillNum;
extern int store_thac0_calcWeaponSkill;
extern int store_thac0_dexMod;
extern int store_thac0_hitrollMod;
extern int store_thac0_paladinBonus;
extern int store_thac0_vampTouchBonus;
extern int store_thac0_blindBonus;
extern int store_thac0_loadMod;
extern int store_thac0_blindMalus;
extern int store_thac0_protEvilMalus;
extern int store_thac0_protGoodMalus;
extern int store_thac0_protUndead;
extern int store_thac0_blurrMalus;
extern int store_thac0_ressurectMalus;
extern int store_thac0_victAgility;
extern int store_thac0_victAgilityMod;
extern int store_thac0_victimAC;
extern int store_thac0_victimACMod;
extern int store_thac0_hitChance;
extern int store_thac0_baseHitChance;
extern int store_thac0_diceRoll;
extern int store_thac0_attacks;
extern int store_dam_mobDam10Mod;
extern int store_dam_mobDam20Mod;
extern int store_dam_mobDam30Mod;
extern int store_dam_mobDam40Mod;
extern int store_dam_mobDam50Mod;
extern int store_dam_mobDam60Mod;
extern int store_dam_weaponDice;
extern int store_dam_barehandDice;
extern int store_dam_damrollBonus;
extern int store_dam_strDamBonus;
extern int store_dam_backstabBonus;
extern int store_dam_circleBonus;
extern int store_dam_postureBonus;
extern int store_dam_offenseBonus;
extern int store_dam_mountedBonus;
extern int store_dam_critHitBonus;
extern int store_dam_acAbsorptionBonus;
extern int store_dam_mobDam10Bonus;
extern int store_dam_mobDam20Bonus;
extern int store_dam_mobDam30Bonus;
extern int store_dam_mobDam40Bonus;
extern int store_dam_mobDam50Bonus;
extern int store_dam_mobDam60Bonus;
extern int store_dam_bounded;
extern int store_dam_postureBonus;
extern int store_dam_offenseBonus;
extern int store_dam_totalDamage;
extern unsigned int store_dam_totalAdjustedDamage;
extern int counter_dam_totalDamage;
extern int counter_dam_stoneProtectSize;
extern int counter_dam_scaleProtectSize;
extern int store_dam_stoneProtectSize;
extern int store_dam_scaleProtectSize;
extern int counter_dam_stoneMaxHits;
extern int counter_dam_scaleMaxHits;
extern int store_dam_stoneMaxHits;
extern int store_dam_scaleMaxHits;
extern int store_spells_highConRaceMod;

extern int cc_thac0_classMod;
extern int cc_thac0_levelMod;
extern int cc_thac0_weaponSkillMod;
extern int cc_thac0_dexMod;
extern int cc_thac0_hitrollMod;
extern int cc_thac0_paladinBonus;
extern int cc_thac0_vampTouchBonus;
extern int cc_thac0_blindBonus;
extern int cc_thac0_loadMod;
extern int cc_thac0_blindMalus;
extern int cc_thac0_protEvilMalus;
extern int cc_thac0_protGoodMalus;
extern int cc_thac0_protUndead;
extern int cc_thac0_blurrMalus;
extern int cc_thac0_ressurectMalus;
extern int cc_thac0_victAgilityMod;
extern int cc_thac0_victimACMod;
extern int cc_thac0_mobHitrollMod;
extern int cc_thac0_mobSkillMod;
extern int cc_thac0_dexSlope;
extern int cc_thac0_dexBase;
extern int cc_dam_weaponDiceMod;
extern int cc_dam_barehandDiceMod;
extern int cc_dam_damrollMod;
extern int cc_dam_strDamMod;
extern int cc_dam_backstabMod;
extern int cc_dam_circleMod;
extern int cc_dam_postureMod;
extern int cc_dam_offenseMod;
extern int cc_dam_mountedMod;
extern int cc_dam_critHitMod;
extern int cc_dam_acAbsorptionMod;
extern int cc_dam_mobDam10Mod;
extern int cc_dam_mobDam20Mod;
extern int cc_dam_mobDam30Mod;
extern int cc_dam_mobDam40Mod;
extern int cc_dam_mobDam50Mod;
extern int cc_dam_mobDam60Mod;
extern int cc_dam_totalAdjustedDamage;
extern int cc_dam_stoneProtectSize;
extern int cc_dam_stoneMaxHits;
extern int cc_dam_scaleProtectSize;
extern int cc_dam_scaleMaxHits;
extern int cc_damage_strSlope;
extern int cc_damage_strBase;
extern int cc_spells_highConRaceMod;
extern int cc_def_attFactor;
extern int cc_def_wpnFactor;
extern int cc_def_penaltySlope;
extern int cc_def_saveFactor;
extern int MasterCounter_PcDeath;
extern int MasterCounter_MobDeath;
extern int MasterCounter_MobExpGained;
extern int MasterCounter_MoneyGained;

extern int cc_prestige_fragBonus;
extern int cc_prestige_roleplayPenalty;

// Combat Specific Structures

P_char combat_list = 0;           // Linked list containing all chars in combat
P_char combat_next_ch = 0;      // Global shortcut referencing next char in combat

// Global Int - Iyachtu

int avoidCount = 0;
int spammy = 0;
int shieldHits = 0;

// These are rather..vital.

   #define ATTACK_ERROR      -1
   #define ATTACK_MISS       1
   #define ATTACK_FUMBLE     2
   #define ATTACK_HIT        3
   #define ATTACK_CRIT       4

// This is important too, really
   #define DEFENSE_FULL      1
   #define DEFENSE_PARTIAL   2

/* Weapon attack texts */
static struct attack_hit_type attack_hit_text[] = {
   {"hit", "hits"},              /* TYPE_HIT      */
   {"pound", "pounds"},          /* TYPE_BLUDGEON */
   {"pierce", "pierces"},        /* TYPE_PIERCE   */
   {"slash", "slashes"},         /* TYPE_SLASH    */
   {"whip", "whips"},            /* TYPE_WHIP     */
   {"claw", "claws"},            /* TYPE_CLAW     */
   {"bite", "bites"},            /* TYPE_BITE     */
   {"sting", "stings"},          /* TYPE_STING    */
   {"crush", "crushes"}          /* TYPE_CRUSH    */
};

/* Racial attack texts */
static struct attack_hit_type race_hit_text[] = {
   /* 0 */   {"undefined", "undefined"},   /* RACE_NONE */
   {"hit", "hits"},             /* RACE_HUMAN */
   {"hit", "hits"},             /* RACE_BARBARIAN */
   {"hit", "hits"},             /* RACE_DROW */
   {"hit", "hits"},             /* RACE_GREY */
   /* 5 */   {"hit", "hits"},             /* RACE_MOUNTAIN */
   {"hit", "hits"},             /* RACE_DUERGAR */
   {"hit", "hits"},             /* RACE_HALFLING */
   {"hit", "hits"},             /* RACE_GNOME */
   {"hit", "hits"},             /* RACE_OGRE */
   /* 10 */  {"hit", "hits"},             /* RACE_TROLL */
   {"hit", "hits"},             /* RACE_HALFELF */
   {"hit", "hits"},             /* RACE_ILLITHID */
   {"hit", "hits"},             /* RACE_YUANTI */
   {"hit", "hits"},             /* RACE_LICH */
   /* 15 */  {"hit", "hits"},             /* RACE_MYCONID */
   {"hit", "hits"},             /* RACE_PORC */
   {"undefined", "undefined"},   /* RACE_UNDEFINED */
   {"undefined", "undefined"},   /* RACE_UNDEFINED */
   {"hit", "hits"},             /* RACE_POSSESSED */
   /* 20 */  {"bite", "bites"},           /* RACE_RAPTOR */
   {"drain", "drains"},         /* RACE_HIGH_UNDEAD */
   {"claw", "claws"},           /* RACE_FELINE */
   {"bite", "bites"},           /* RACE_CANINE */
   {"pummel", "pummels"},       /* RACE_ANGEL */
   /* 25 */  {"strike", "strikes"},       /* RACE_GITHYANKI */
   {"smash", "smashes"},        /* RACE_SESSILE */
   {"drain", "drains"},         /* RACE_SPIRIT */
   {"bite", "bites"},           /* RACE_HYBRID_ANIMAL */
   {"slime", "slimes"},         /* RACE_SLIME */
   /* 30 */  {"burn", "burns"},           /* RACE_F_ELEMENTAL */
   {"choke", "chokes"},         /* RACE_A_ELEMENTAL */
   {"pound", "pounds"},         /* RACE_W_ELEMENTAL */
   {"smash", "smashes"},        /* RACE_E_ELEMENTAL */
   {"pummel", "pummels"},       /* RACE_DEMON */
   /* 35 */  {"pummel", "pummels"},       /* RACE_DEVIL */
   {"beat", "beats"},           /* RACE_UNDEAD */
   {"drain", "drains"},         /* RACE_VAMPIRE */
   {"touch", "touches"},        /* RACE_GHOST */
   {"claw", "claws"},           /* RACE_LYCANTH */
   /* 40 */  {"smash", "smashes"},        /* RACE_GIANT */
   {"hit", "hits"},             /* RACE_KOBOLD */
   {"punch", "punches"},        /* RACE_ORC */
   {"hit", "hits"},             /* RACE_GOBLIN */
   {"punch", "punches"},        /* RACE_HALFORC */
   /* 45 */  {"smash", "smashes"},        /* RACE_GOLEM */
   {"nip", "nips"},             /* RACE_FAERIE */
   {"claw", "claws"},           /* RACE_DRAGON */
   {"claw", "claws"},           /* RACE_DRAGONKIN */
   {"bite", "bites"},           /* RACE_REPTILE */
   /* 50 */  {"bite", "bites"},           /* RACE_SNAKE */
   {"sting", "stings"},         /* RACE_INSECT */
   {"sting", "stings"},         /* RACE_ARACHNID */
   {"bite", "bites"},           /* RACE_FISH */
   {"claw", "claws"},           /* RACE_BIRD */
   /* 55 */  {"charge", "charges"},       /* RACE_HORSE */
   {"beat", "beats"},           /* RACE_PRIMATE */
   {"strike", "strikes"},       /* RACE_HUMANOID */
   {"bite", "bites"},           /* RACE_ANIMAL */
   {"smash", "smashes"},        /* RACE_TREE */
   /* 60 */  {"bite", "bites"},           /* RACE_HERBIVORE */
   {"claw", "claws"},           /* RACE_CARNIVORE */
   {"sting", "stings"},         /* RACE_PARASITE */
   {"strike", "strikes"},       /* RACE_REPTOID */
   {"hit", "hits"},             /* RACE_KUO_TOA */
   /* 65 */  {"hit", "hits"},             /* RACE_HYBRID_HUMAN */
   {"charge", "charges"},       /* RACE_CENTAUR */
   {"crush", "crushes"},        /* RACE_NAGA */
   {"hit", "hits"},             /* RACE_HUMANOID_OTHER */
   {"charge", "charges"},       /* RACE_KIRIN */
   /* 70 */  {"beat", "beats"},           /* RACE_EFFREETI */
   {"hit", "hits"},             /* RACE_DJINNI */
   {"bite", "bites"},           /* RACE_BEHOLDER */
   {"claw", "claws"},           /* RACE_BEAR */
   {"sting", "stings"},         /* RACE_MANSCORPION */
   /* 75 */  {"sting", "stings"},         /* RACE_IXZAN */
   {"claw", "claws"}            /* RACE_UMBERHULK */
};

/* moved outside. JAB */
// This is one of the bonus categories that factor into ToHit (THAC0).
// Its roughly based on the class-types, matches the list of CLASS_ es
// in race_class.h, but only really amounts to about 6% of the total
// to hit value. --MIAX 03-17-01
static int class_mod[LAST_CLASS + 1] =
{0,  /* Unused - leave blank */
   12, // Warrior
   12, // Ranger
   12, // Berserker - Old
   12, // Paladin
   12, // Anti-Paladin
   8,  // Cleric
   10, // Monk - Old
   8,  // Druid
   8,  // Shaman
   4,  // Sorcerer - Old
   4,  // Necromancer
   4,  // Conjurer - Old
   8,  // Thief - Old
   8,  // Assassin - Old
   12, // Mercenary - Old
   8,  // Bard
   2,  // Psionicist
   4,  // Lich
   4,  // Enchanter
   4,  // Invoker
   4, // Illusionist
   8,  // Battlechanter
   8,  // Rogue
   4,  // Elementalist
   12  // Dire Raider
}; // rogues and elementalists not added here
   // when they should've been, also added dires


   #define FIGHT_STOPPED(CH) \
  (!(CH) || !((CH)->specials.fighting) || ((CH)->in_room != (CH)->specials.fighting->in_room))


/*******************************************************************/
/*                    MAIN COMBAT FUNCTIONS                        */
/*******************************************************************/



void StartCombat(P_char ch, P_char victim)
{
   char Gbuf[10];

   // Basic Sanity Checks
   if(!SanityCheck(ch, "BeginCombat - ch") || !SanityCheck(victim, "BeginCombat-victim"))
      return;

   // Can't fight yourself
   if(ch == victim)
      return;

   if(GET_STAT(ch) <= STAT_INCAP)
      return;

   if(IS_NPC(ch))
      if(IS_CSET(ch->only.npc->npcact, ACT_MOUNT))
         return;

      // Already fighting?
   if(IS_FIGHTING(ch) || ch->specials.next_fighting)
      {
      logit(LOG_EXIT, "StartCombat(): ch already fighting!");
      dump_core();
      }

   // AggImmune Checks for gods
   if(IS_TRUSTED(ch) && IS_CSET(ch->only.pc->pcact, PLR_AGGIMMUNE))
      return;

   // Another AggImmune check + garroting hack
   if(IS_TRUSTED(victim) && IS_CSET(victim->only.pc->pcact, PLR_AGGIMMUNE))
      {
      if(IS_FIGHTING(victim) && !IS_CSET(victim->specials.affects, AFF_GARROTING))
         StopCombat(victim);
      return;
      }

   // Single File check - retarget if ch is a mob and is not adjacent to vict
   if(IS_CSET(world[ch->in_room].room_flags, SINGLE_FILE) && !AdjacentInRoom(ch, victim))
      {
      if(IS_NPC(ch) && !IS_MORPH(ch))
         victim = PickTarget(ch);
      if(!victim)
         return;
      }

   // The following function checks all the other details, and strips affs where needed
   if((InitialCombatCheck(ch, victim)) == FALSE)
      return;

   // Paging Mode hack - nuke for victim
   if(victim->desc && victim->desc->showstr_point)
      {
      strcpy(Gbuf, "q\n");
      show_string(victim->desc, Gbuf);
      }

   // Finally - set the chars fighting
   ch->specials.fighting = victim;
   ch->specials.next_fighting = combat_list;
   combat_list = ch;

   // Call for initial "Dragon Fear" check
   if(((GET_RACE(ch) == RACE_DRAGON) || (GET_RACE(ch) == RACE_DRAGONKIN)) &&
      IS_NPC(ch) && !IS_MORPH(ch))
      DragonCombat(ch, TRUE);

   // Set mobile memory - ch
   if(IS_NPC(ch) && HAS_MEMORY(ch) && ch->only.npc->memory &&
      (GET_STAT(ch) > STAT_INCAP) && !IS_TRUSTED(victim))
      {
      // PC's
      if(IS_PC(victim))
         mem_addToMemory(ch->only.npc->memory, GET_NAME(victim));
      // PC pets
      else if(IS_AFFECTED(victim, AFF_CHARM) && victim->following && IS_PC(victim->following) &&
              (victim->following->in_room == victim->in_room) && CAN_SEE(ch, victim->following))
         mem_addToMemory(ch->only.npc->memory, GET_NAME(victim->following));
      }

   // Set mobile memory - victim
   if(IS_NPC(victim) && (GET_STAT(victim) > STAT_INCAP) &&
      HAS_MEMORY(victim) && victim->only.npc->memory && !IS_TRUSTED(ch))
      {
      // PC's
      if(IS_PC(ch) && !IS_TRUSTED(ch))
         mem_addToMemory(victim->only.npc->memory, GET_NAME(ch));
      // PC pets
      else if(IS_AFFECTED(ch, AFF_CHARM) && ch->following && IS_PC(ch->following) &&
              (ch->following->in_room == ch->in_room) && CAN_SEE(victim, ch->following))
         mem_addToMemory(victim->only.npc->memory, GET_NAME(ch->following));
      }

   // Fin

}

void StopCombat(P_char ch)
{
   P_char tmp;
   P_char vict;

   // Sanity Check
   if(!SanityCheck(ch, "StopCombat") || !IS_FIGHTING(ch))
      return;

   // Remove ch from the combat list
   if(ch == combat_next_ch)
      combat_next_ch = ch->specials.next_fighting;
   if(combat_list == ch)
      combat_list = ch->specials.next_fighting;
   else
      {
      for(tmp = combat_list; tmp && (tmp->specials.next_fighting != ch); tmp = tmp->specials.next_fighting);
      if(!tmp)
         {
         logit(LOG_EXIT, "%s not found in combat_list StopCombat()", GET_NAME(ch));
         dump_core();
         }
      tmp->specials.next_fighting = ch->specials.next_fighting;
      }

   // Does the obvious really need explaining?

   /* No, just fixing for garrote!  :)  Iyachtu */
   /* MUST remove affect from player and mob here */

   /* First, are we garroting? */
   if(IS_CSET(ch->specials.affects, AFF_GARROTING))
      {
      /* We are, remove the affect from the player */
      REMOVE_CBIT(ch->specials.affects, AFF_GARROTING);
      /* If the player is not fighting someone (aka garroting them) */
      /* or the mob they're targeting isn't currently garroted */
      vict = ch->specials.fighting;
      if((!vict) ||
         (!(IS_CSET(vict->specials.affects , AFF_GARROTE_VICT))))
         {
         /* This means we have something weird going on, but possibly not major */
         debuglog(51, DS_COMBAT_SKILLS, "StopCombat: rogue garroting, no target - semi-fubar");
         }
      /* Expected result of this, we remove the garrote affect from victim */
      /* Previously, the victim would have remained garroted indefinitely */
      /* If we ever got to StopCombat - blink is one way to do this */
      else REMOVE_CBIT(ch->specials.fighting->specials.affects, AFF_GARROTE_VICT);
      }
   ch->specials.next_fighting = NULL;
   ch->specials.fighting = NULL;

   // Put that bow down, gomer
   stop_firing(ch);

   // HE'S DEAD JIM!
   update_pos(ch);
}

void CombatEngine(void)
{
   P_char ch = NULL, victim = NULL;
   int numAttacks = 0, attack = 0;
   bool doubleAttack = FALSE, dualWield = FALSE, dualHaste = FALSE;
   bool avoided = FALSE, bashDelay = FALSE;
   bool dodged = FALSE, blocked = FALSE, parried = FALSE, secondary = FALSE;
   bool mountblocked = FALSE;

   if(IS_ENABLED(CODE_NEWDEFENSE))
      {
      // Reset Attack Avoidance Counter
      for(ch = combat_list; ch; ch = combat_next_ch)
         {
         ch->specials.avoid_counter = 0;
         combat_next_ch = ch->specials.next_fighting;
         }
      }

   // Main combat loop
   for(ch = combat_list; ch; ch = combat_next_ch)
      {

      // Non playing chars need not apply!
      // FIX Should probably remove this ch from the combat_list
      if(ch->in_room == -1)
         continue;

      debuglog(51, DS_COMBAT, "Entering combat loop for %s", GET_NAME(ch));

      // Initialize our vars for this iteration
      bashDelay = FALSE;
      avoidCount = 0;
      shieldHits = 0;
      doubleAttack = FALSE;
      dualWield = FALSE;
      dualHaste = FALSE;
      combat_next_ch = ch->specials.next_fighting;
      victim = ch->specials.fighting;
      ch->points.hitCount = 0;

      if(!victim)
         {
         logit(LOG_EXIT, "CombatEngine(): %s fighting null victim.", GET_NAME(ch));
         dump_core();
         }

      if(ch->specials.action_delays[ACT_DELAY_DISARM] > 0)
         ch->specials.action_delays[ACT_DELAY_DISARM]--;

      // Decrement bash delay
      if(ch->specials.action_delays[ACT_DELAY_BASH] > 0)
         {
         ch->specials.action_delays[ACT_DELAY_BASH]--;
         bashDelay = TRUE;
         }

      // Decrement Hitall delay
      if(ch->specials.action_delays[ACT_DELAY_HITALL] > 0)
         ch->specials.action_delays[ACT_DELAY_HITALL]--;

      // Decrement Headbutt delay
      if(ch->specials.action_delays[ACT_DELAY_HEADBUTT] > 0)
         ch->specials.action_delays[ACT_DELAY_HEADBUTT]--;

      // Decrement Dragon Specail Action delay
      if(ch->specials.action_delays[ACT_DELAY_DRAGON])
         --ch->specials.action_delays[ACT_DELAY_DRAGON];

      // This function checks the validity of ch & vict and nukes affects where needed
      if(!CombatCheck(ch, victim))
         {
         debuglog(51, DS_COMBAT, "CombatCheck failed for %s vs. %s", GET_NAME(ch), GET_NAME(victim));
         continue;
         }

      // Experimental section.  Trying to deal with Minor Para issue.
      // Individual attacks are handled by actoff.c, if we're in this
      // combat engine, and player is not vicious, then they need to NOT
      // attack if victim is minor para'd - Iyachtu
      if((IS_AFFECTED(victim, AFF_MINOR_PARALYSIS)
          || IS_AFFECTED(victim, AFF_MAJOR_PARALYSIS))
         && (IS_PC(ch))
         && (!IS_CSET(ch->only.pc->pcact, PLR_VICIOUS)))
         {
         continue;
         }

      // Missile Combat hook
      if(IS_AFFECTED(ch, AFF_AUTO_FIRING))
         {
         Melee_Missile_Combat(ch, victim);
         continue;
         }

      // Ok.  Now the fun begins: calculate how many attacks ch gets

      // We start at 1, naturally.
      numAttacks = 1;

      // Haste adds 1 attack for primary weapon
      if(IS_AFFECTED(ch, AFF_HASTE))
         numAttacks += 1;

#ifdef NEW_BARD
      else if(affected_by_song(ch, SONG_OF_OFFENSIVE_HARMONY) &&
              ch->bard_singing->song_singing->modifier >= 6)
         numAttacks += 1;
#endif

      // Check against over 100 dex for bonus attack
      // but only for halfling, elf, drow, and gnome PCs

      if((BOUNDED(0, (GET_C_DEX(ch) - 100), 50)) > (number(1, 100)))
         if((GET_RACE(ch) == RACE_GREY) ||
            (GET_RACE(ch) == RACE_GNOME) ||
            (GET_RACE(ch) == RACE_DROW) ||
            (GET_RACE(ch) == RACE_HALFLING))
            numAttacks += 1;

         // Dual wield adds another attack
      if((dualWield = PhasedAttack(ch, SKILL_DUAL_WIELD)))
         numAttacks += 1;

      // If Dualling and hasted, add an attack
      if(dualWield && IS_AFFECTED(ch, AFF_HASTE) &&
         (GET_CHAR_SKILL(ch, SKILL_DUAL_WIELD) > 50))
         {
         if((GET_CLASS(ch)!=CLASS_DIRERAIDER) || number(0,1))
            {
            numAttacks += 1;
            dualHaste = TRUE;
            }
         }
      else if(dualWield && GET_CHAR_SKILL(ch, SKILL_DUAL_WIELD) > 50 &&
              affected_by_song(ch, SONG_OF_OFFENSIVE_HARMONY) &&
              ch->bard_singing->song_singing->modifier >= 6)
         {
         if(GET_CLASS(ch) != CLASS_DIRERAIDER || number(0, 1))
            {
            numAttacks += 1;
            dualHaste = TRUE;
            }
         }

      // Double Attack adds another attack - only with piercers for Rogues
      if(GET_CLASS(ch) == CLASS_ROGUE)
         {
         if(ch->equipment[PRIMARY_WEAPON] &&
            (ch->equipment[PRIMARY_WEAPON]->value[3] == 11) &&
            (ch->equipment[SECONDARY_WEAPON] ?
             (ch->equipment[SECONDARY_WEAPON]->value[3] == 11) : 1))
            doubleAttack = PhasedAttack(ch, SKILL_DOUBLE_ATTACK);
         }
      else
         {
         doubleAttack = PhasedAttack(ch, SKILL_DOUBLE_ATTACK);
         }

      if(doubleAttack)
         numAttacks += 1;

      // Slowness halves numAttacks - if num is odd, extra attack 1/2 the time
      if(IS_AFFECTED(ch, AFF_SLOW) && (numAttacks > 1))
         {
         if((numAttacks % 2) && (phase % 2))
            numAttacks++;
         numAttacks /= 2;
         numAttacks = MAX(1, numAttacks);
         }

#ifdef NEW_BARD
      else if((affected_by_song(ch, SONG_OF_OFFENSIVE_DISRUPTION)) &&
              (ch->bard_singing->song_singing->special) && (numAttacks > 1))
         {
         if((numAttacks % 2) && (phase % 2))
            numAttacks++;
         numAttacks /= 2;
         numAttacks = MAX(1, numAttacks);
         }
#endif

      // numAttacks is anywhere from 1-5 now

      // Attack Loop
      for(attack = 0; (attack < numAttacks) && !FIGHT_STOPPED(ch); attack++)
         {

         // Init these to false for each hit
         dodged = FALSE;
         blocked = FALSE;
         parried = FALSE;
         mountblocked = FALSE;
         secondary = FALSE;
         avoided = FALSE;

         // Set weapon to secondary if this is a dual attack
         if((dualWield && (attack == 1)) || (dualHaste && (attack == 3)))
            secondary = TRUE;

         // Old Defensive Section
         if(!IS_ENABLED(CODE_NEWDEFENSE))
            {

            // First attempt a parry
            if(parrySucceed(victim, ch, (secondary ? SECONDARY_WEAPON : WIELD), avoidCount))
               {
               avoided = TRUE;
               avoidCount++;
               }

            // Now shield block
            if(!avoided && blockSucceed(victim, ch, (secondary ? SECONDARY_WEAPON : WIELD), avoidCount))
               {
               avoided = TRUE;
               avoidCount++;
               }

            // Now mountblock
            if(!avoided && mountblockSucceed(victim, ch, (secondary ? SECONDARY_WEAPON : WIELD), avoidCount))
               {
               avoided = TRUE;
               avoidCount++;
               }

            // Lastly, try to dodge the attack
            if(!avoided && dodgeSucceed(victim, ch, (secondary ? SECONDARY_WEAPON : WIELD), avoidCount))
               {
               avoided = TRUE;
               avoidCount++;
               }
            }
         // If we didn't manage to avoid the attack somehow, go forth and hit!
         if(!avoided)
            {
            spammy = TRUE;
            CombatMeleeAttack(ch, victim, (secondary ? TYPE_SECONDARY_WEAPON : TYPE_UNDEFINED));
            spammy = FALSE;
            }
         else
            // Even if the hit was avoided, do a justice check on the ch
            justice_witness(ch, victim, CRIME_ATT_MURDER);


         // Handle odd cases in which ch ends up fighting a dead victim
         if(GET_STAT(victim) == STAT_DEAD)
            {
            victim = ch->specials.fighting;
            if(!victim || (GET_STAT(victim) == STAT_DEAD))
               break;
            avoided = FALSE;
            avoidCount = 0;
            }
         }
      // End of Attack Loop

      // Display multi-hit damage messages
      if(!FIGHT_STOPPED(ch) && victim && GET_STAT(victim) != STAT_DEAD)
         dam_message_multi(ch, victim);

      // If we're not fighting, we probably shouldn't continue on to MobCombat :)
      if(FIGHT_STOPPED(ch))
         continue;

      // If this is a mob, send them into MobCombat to handle spells and specials
      if(IS_NPC(ch) && CAN_ACT(ch) && !IS_MORPH(ch) && AWAKE(ch) && !bashDelay)
         MobCombat(ch);

      }
   // End of Main Combat Engine Loop

}

void CombatMeleeAttack(P_char ch, P_char victim, int attackType)
{
   P_obj weapon = NULL;
   P_group group = NULL;
   char Gbuf1[MAX_STRING_LENGTH];
   int damage = 0, vampDam;
   int weaponType = 0, weaponSlot = 0, weaponSkill = 0, weaponSkillNum = 0;
   int origAttackType = attackType, hitType = 0, finalDamage = 0, vampsave =0;
   bool origFlag = FALSE;
   struct func_attachment *fn;

   // Debugging
   debuglog(51, DS_COMBAT, "Entering CombatMeleeAttack for %s vs. %s", GET_NAME(ch), GET_NAME(victim));

   // Basic Sanity Checks
   if(!SanityCheck(ch, "MeleeAttack") || !SanityCheck(victim, "MeleeAttack") ||
      (ch->in_room != victim->in_room))
      return;

   // Dead chars need not apply
   if(GET_STAT(victim) == STAT_DEAD)
      {
      statuslog(51, "%s attacking dead char (%s)", GET_NAME(ch), GET_NAME(victim));
      return;
      }

   // nor incap players
   if(GET_STAT(ch) <= STAT_INCAP)
      {
      if(IS_FIGHTING(ch))
         stop_fighting(ch);
      return;
      }

   // Log the attack to the group struct
   if((group = GET_GROUP(ch)))
      group->combat_log.meleeAttacks++;

   // Hook for missile combat
   if(IS_AFFECTED(victim, AFF_AUTO_FIRING))
      {
      sprintf(Gbuf1, "&+WYou hastily stop firing as %s forces you into melee combat!\n", GET_NAME(ch));
      send_to_char(Gbuf1, victim);
      stop_firing(victim);
      }
   else if(IS_AFFECTED(ch, AFF_AUTO_FIRING))
      {
      Melee_Missile_Combat(ch, victim);
      return;
      }
   // If this is a bs/circle/secondary attack, we need to flag it
   if((attackType == TYPE_SECONDARY_WEAPON) || (attackType == SKILL_CIRCLE2) ||
      (attackType == SKILL_BACKSTAB2) || (attackType == SKILL_CIRCLE) ||
      (attackType == SKILL_BACKSTAB))
      origFlag = TRUE;

   // Determine which weapon we are using
   if((attackType == TYPE_SECONDARY_WEAPON) || (attackType == SKILL_CIRCLE2) ||
      (attackType == SKILL_BACKSTAB2) || (attackType == SKILL_OUTFLANK2))
      {
      weaponSlot = SECONDARY_WEAPON;
      if(attackType == SKILL_CIRCLE2)
         attackType = SKILL_CIRCLE;
      else if(attackType == SKILL_BACKSTAB2)
         attackType = SKILL_BACKSTAB;
      else if(attackType == SKILL_OUTFLANK2)
         attackType = SKILL_OUTFLANK;
      }
   else if(attackType == TYPE_FORCE_PRIMARY)
      weaponSlot = PRIMARY_WEAPON;
   else if(attackType == TYPE_FORCE_SECONDARY)
      weaponSlot = SECONDARY_WEAPON;
   else
      {
      weaponSlot = PRIMARY_WEAPON;
      }

   // Get the weapon obj
   weapon = ch->equipment[weaponSlot];

   // If we have a weapon, determine it's type
   if(weapon && ((weapon->type == ITEM_WEAPON) || (weapon->type == ITEM_FIREWEAPON)))
      {
      switch(weapon->value[3])
         {
         case 0:
         case 1:
         case 2:
            weaponType = TYPE_WHIP;
            break;
         case 3:
            weaponType = TYPE_SLASH;
            break;
         case 4:
         case 5:
         case 6:
            weaponType = TYPE_CRUSH;
            break;
         case 7:
            weaponType = TYPE_BLUDGEON;
            break;
         case 8:
         case 9:
            weaponType = TYPE_CLAW;
            break;
         case 10:
            weaponType = TYPE_BITE;
            break;
         case 11:
            weaponType = TYPE_PIERCE;
            break;
         default:
            weaponType = TYPE_HIT;
            break;
         }
      }
   // Otherwise, set it to be a barehand hit OR the mob's attack type
   else
      {
      weapon = NULL;
      if(IS_NPC(ch) && (ch->only.npc->attack_type >= TYPE_HIT))
         weaponType = ch->only.npc->attack_type;
      else
         weaponType = TYPE_HIT;
      }

   // Debugging
   debuglog(51, DS_COMBAT, "CombatMeleeAttack: weaponType = %d    attackType = %d origAttackType = %d",
            weaponType, attackType, origAttackType);

   // Find weapon skill number
   if((attackType == TYPE_SECONDARY_WEAPON) || (attackType == SKILL_CIRCLE2) ||
      (attackType == SKILL_OUTFLANK2) || (attackType == SKILL_BACKSTAB2))
      weaponSkillNum = WeaponSkill_num(ch, SECONDARY_WEAPON);
   else
      weaponSkillNum = WeaponSkill_num(ch, WIELD);

   // Set weapon skill
   if(!weaponSkillNum)
      weaponSkill = (IS_NPC(ch) ? BOUNDED(3, (GET_LEVEL(ch) * 2), 95) : GET_CHAR_SKILL(ch, SKILL_MARTIAL_ARTS));
   else
      weaponSkill = (IS_NPC(ch) ? BOUNDED(3, (GET_LEVEL(ch) * 2), 95) : GET_CHAR_SKILL(ch, weaponSkillNum));

   /* mounted combat bonus for those PCs  --DMB */
   if(IS_RIDING(ch) && (GET_CHAR_SKILL(ch, SKILL_MOUNTED_COMBAT) > 0))
      weaponSkill += ((GET_CHAR_SKILL(ch, SKILL_MOUNTED_COMBAT) + 1) / 10);


   // Call CalcMeleeHit to see if we hit
   hitType = (CalcMeleeHit(ch, victim, origAttackType));

   // Debugging
   debuglog(51, DS_COMBAT, "CombatMeleeAttack: %d hitType for %s vs. %s",
            hitType, GET_NAME(ch), GET_NAME(victim));

   // Sanity Check!
   if(hitType == ATTACK_ERROR)
      return;

   // Justice hook
   if(hitType == ATTACK_MISS)
      justice_witness(ch, victim, CRIME_ATT_MURDER);

   if(IS_NPC(ch) && ((mob_index[ch->nr].virtual == 1259) || (mob_index[ch->nr].virtual == 1261)))
      vampsave = (GET_LEVEL(ch) / 10 - 2);

   // Vamp touch is handled specially here
   if(!weapon && (hitType >= ATTACK_HIT) &&
      (IS_AFFECTED(ch, AFF_VAMPIRIC_TOUCH) || (GET_RACE(ch) == RACE_VAMPIRE)) &&
      !(IS_AFFECTED(victim, AFF_VAMPIRIC_TOUCH) || (GET_RACE(victim) == RACE_VAMPIRE)) &&
      !IS_TRUSTED(victim) && !IS_UNDEAD(victim) && !IMMATERIAL(victim) &&
      (affected_by_spell(ch, SPELL_VAMPIRIC_TOUCH) || !NewSaves(victim, SAVING_PARA, vampsave)))
      {
      vampDam = BOUNDED(0, dice((MIN(50, GET_LEVEL(ch)) / 2), 6), GET_HIT(victim) + 11);
      if(affected_by_spell(victim, SPELL_PROT_FROM_UNDEAD))
         vampDam /= 2;
      if(vampsave)
         {
         vampDam /= 2;
         vampDam = vampDam * GET_LEVEL(victim) / 59;
         }
      CombatDamage(ch, victim, vampDam, SPELL_VAMPIRIC_TOUCH);
      if(affected_by_spell(ch, SPELL_VAMPIRIC_TOUCH))
         affect_from_char(ch, SPELL_VAMPIRIC_TOUCH);
      if(!IS_AFFECTED(ch, AFF_BLACKMANTLE))
         GET_HIT(ch) += vampDam;
      StartRegen(ch, EVENT_HIT_REGEN);
      return;
      }

   // Handle fumble (and check procs)
   if(hitType == ATTACK_FUMBLE)
      {
      if(weapon && GET_LEVEL(ch) > 1 && !IS_SET(weapon->extra_flags, ITEM_NODROP))
         {
         if(obj_index[weapon->R_num].spec_flag & IDX_WEAPON_FUMBLE)
            {
            fn = obj_index[weapon->R_num].func;
            do
               {
               if(fn->proc_flag & IDX_WEAPON_FUMBLE)
                  (*fn->func.obj) (weapon, ch, PROC_WEAPON_FUMBLE, (char *) victim);
               fn = fn->next;
               } while(fn && ch->specials.fighting && (ch->in_room == victim->in_room));
            }
         else
            {
            act("&=LYYou swing _really_ badly, sending your &n$p&=LY flying!", FALSE, ch, weapon, 0, TO_CHAR);
            act("$n stumbles with $s attack, sending $s $p flying!", TRUE, ch, weapon, 0, TO_ROOM);
            obj_to_room(unequip_char(ch, weaponSlot, TRUE), ch->in_room);
            // Do_Object_Decay(ch, (int) weaponSlot, DECAY_WEAPON_FUMBLE, ch->in_room);
            }
         char_light(ch);
         room_light(ch->in_room, REAL);
         }
      else
         {
         if(IS_NPC(ch) && (mob_index[ch->nr].spec_flag & IDX_NPC_FUMBLE))
            {
            fn = mob_index[ch->nr].func;
            do
               {
               if(fn->proc_flag & IDX_NPC_FUMBLE)
                  (*fn->func.ch) (ch, victim, PROC_NPC_FUMBLE, NULL);
               fn = fn->next;
               } while(fn && ch->specials.fighting && (ch->in_room == victim->in_room));
            }
         else
            {
            send_to_char("You stumble, but manage to avoid falling!\n", ch);
            }
         }
      return;
      }

   // Skill gain chance for weapon skill
   if(IS_PC(ch))
      {
      if(weaponSkill && (phase %2))
         CharSkillGainChance(ch, weaponSkillNum, 0);
      }

   // Calculate damage (if we hit)
   if(hitType == ATTACK_HIT || hitType == ATTACK_CRIT)
      damage = CalcMeleeDam(ch, victim, weapon, attackType, hitType);

   // Flag this as a Crit for further processing in CombatDamage
   if(hitType == ATTACK_CRIT)
      {
      if(origAttackType == TYPE_SECONDARY_WEAPON)
         weaponType = TYPE_CRIT2;
      else
         weaponType = TYPE_CRIT;
      }

   /*
       // shouldn't make blood when dragonscales stops the crit - Urdlen
       if (!IS_AFFECTED(victim, AFF_DRAGONSCALES))
         make_bloodstain(ch);
       if ((GET_CHAR_SKILL(ch, SKILL_VITAL_STRIKE) / 2) > number(1, 101))
         damage = VitalStrike(ch, victim, (int) damage);
       else
         send_to_char("&+WYou score a CRITICAL HIT!\n", ch);
     }
   */

   // Check weapon/NPC procs - if this returns TRUE, this attack is finished.
   if((CheckCombatProcs(ch, victim, weapon, hitType, (int) damage, origAttackType)) == TRUE)
      return;

   // Ice tomb shatter check
   if(IS_AFFECTED(victim, AFF_ICE_TOMB) && iceTombShatter(ch, victim))
      return;

   // Otherwise, we have an official hit!  Damage him!
   finalDamage = CombatDamage(ch, victim, (int) damage, origFlag ? origAttackType : weaponType);

   // If this dude has a doppleganger, make it attack!
   if(IS_AFFECTED(ch, AFF_DOPPLEGANGER) && victim &&
      (GET_STAT(victim) != STAT_DEAD) && (GET_HIT(victim) >= 50) &&
      ch && (GET_STAT(ch) != STAT_DEAD))
      DopplegangerAttack(ch, victim, attackType);

   // Return the final damage and we are done
   return;
}

// Function which replaces old lookup table for dex +hit bonus
// 3/27/2001 - KPZ

int DexHitBonus(int IndexedDexterity)
{
   int temp=0;

   temp = IndexedDexterity * cc_thac0_dexSlope / 50 + cc_thac0_dexBase;

   return temp;
}

// Function which replaces old lookup table for str +dam bonus
// 3/27/2001 - KPZ

int StrDamBonus(int IndexedStrength)
{
   int temp=0;

   temp = IndexedStrength * cc_damage_strSlope / 50 + cc_damage_strBase;

   return temp;
}


// Added Knob controls, look in mud_control() for details. --MIAX 03-17-01
int CalcMeleeHit(P_char ch, P_char victim, int attackType)
{
   int weaponSkill = 0, weaponSkillNum = 0;
   int diceRoll = 0, hitChance = 0, hitType = 0;
   int hold = 0, baseHitChance = 0;
   struct affected_type *af;
   int levelPenalty = 0;
   int critBonus = 0;
   int critBonusAgi = 0;
   int critBonusStr = 0;
   int testBlind = 0;

   // Basic Sanity Checks
   if(!SanityCheck(ch, "MeleeAttack") || !SanityCheck(victim, "MeleeAttack") ||
      (ch->in_room != victim->in_room))
      return ATTACK_ERROR;

   // Reset thac0 data struct for player
   Clear_Thac0_Stores();
   Clear_Damage_Stores();

   // Begin calculating hitChance

   // First grab a class_mod[] number for mobs
   // See note at top of file (look for class_mod)
   if(IS_NPC(ch))
      {
      if(IS_WARRIOR(ch) || IS_CSET(ch->only.npc->npcact, ACT_PROTECTOR))
         hitChance = class_mod[CLASS_WARRIOR];
      else if(IS_CLERIC(ch))
         hitChance = class_mod[CLASS_CLERIC];
      else if(IS_THIEF(ch))
         hitChance = class_mod[CLASS_THIEF];
      else if(IS_MAGE(ch))
         hitChance = class_mod[CLASS_SORCERER];
      else if(IS_PSIONICIST(ch))
         hitChance = class_mod[CLASS_PSIONICIST];
      }
   else
      {
      hitChance = class_mod[(int) GET_CLASS(ch)];
      }
   // Add the initial THAC0 knob here.
   hitChance = ((hitChance * cc_thac0_classMod) / 100);


   // If racial +hit modifier is more than the class mod, give them that
   hitChance = MAX(hitChance, racial_traits[GET_RACE(ch)].hitroll);
   store_thac0_classMod = hitChance;

   // Add in a level bonus
   store_thac0_levelMod = (((GET_LEVEL(ch) / 2) * cc_thac0_levelMod) / 100);
   hitChance += store_thac0_levelMod;

   // And get off that damn horse if wielding 2hander
   if(IS_RIDING(ch) && IS_PC(ch))
      if(((GET_CHAR_SKILL(ch, SKILL_MOUNTED_COMBAT)< 75) &&
          (ch->equipment[WIELD] && IS_SET(ch->equipment[WIELD]->extra_flags, ITEM_TWOHANDS))) ||
         (!GET_CHAR_SKILL(ch, SKILL_MOUNTED_COMBAT)))
         {
         send_to_char("I'm afraid you aren't quite up to two-handed mounted combat.\n", ch);
         act("$n quickly slides off $N's back.", TRUE, ch, 0, GET_MOUNT(ch), TO_NOTVICT);
         stop_riding(ch);
         }


      // Find weapon skill number
   if((attackType == TYPE_SECONDARY_WEAPON) || (attackType == SKILL_CIRCLE2) ||
      (attackType == SKILL_OUTFLANK2) || (attackType == SKILL_BACKSTAB2))
      weaponSkillNum = WeaponSkill_num(ch, SECONDARY_WEAPON);
   else
      weaponSkillNum = WeaponSkill_num(ch, WIELD);
   store_thac0_calcWeaponSkillNum = weaponSkillNum;

   // Set weapon skill
   if(!weaponSkillNum)
      weaponSkill =
      (IS_NPC(ch) ? BOUNDED(3, (GET_LEVEL(ch) * 2), 95) : GET_CHAR_SKILL(ch, SKILL_MARTIAL_ARTS));
   else
      weaponSkill =
      (IS_NPC(ch) ? BOUNDED(3, (GET_LEVEL(ch) * 2), 95) : GET_CHAR_SKILL(ch, weaponSkillNum));

   /* mounted combat bonus for those PCs. --DMB */
   if(IS_RIDING(ch) && (GET_CHAR_SKILL(ch, SKILL_MOUNTED_COMBAT) > 0))
      weaponSkill += ((GET_CHAR_SKILL(ch, SKILL_MOUNTED_COMBAT) +1) / 10);

   store_thac0_calcWeaponSkill = weaponSkill;

   // Now factor that into our hitChance.
   hold = 0;
   if(IS_NPC(ch))
      hold = BOUNDED(3, (GET_LEVEL(ch) * 2), 95);
   else if(weaponSkill && IS_PC(ch))
      hold = (weaponSkill / 2);

   // Multiply the result by the weapons skill control knob.
   store_thac0_weaponSkillMod = ((hold * cc_thac0_weaponSkillMod) / 100);

   if(!(IS_PC(ch)))
      store_thac0_weaponSkillMod =
      store_thac0_weaponSkillMod * cc_thac0_mobSkillMod / 100;

   hitChance += store_thac0_weaponSkillMod;

   // Dex +hit bonus based on new function, not lookup table -KPZ
   store_thac0_dexMod = (((DexHitBonus(STAT_INDEX(GET_C_DEX(ch))))
                          * cc_thac0_dexMod) / 100);
   hitChance += store_thac0_dexMod;

#ifdef NEW_BARD

   store_thac0_hitrollMod = (GET_HITROLL(ch));

   if(affected_by_song(ch, SONG_OF_OFFENSIVE_HARMONY))
      {
      store_thac0_hitrollMod += (ch->bard_singing->song_singing->modifier);
      debuglog(51, DS_IYACHTU, "Adding %d to hitroll for %s", ch->bard_singing->song_singing->modifier, ch->player.name);
      }
   else if(affected_by_song(ch, SONG_OF_OFFENSIVE_DISRUPTION))
      {
      store_thac0_hitrollMod -= (ch->bard_singing->song_singing->modifier);
      debuglog(51, DS_IYACHTU, "Subtracting %d from hitroll for %s", ch->bard_singing->song_singing->modifier, ch->player.name);
      }

   if(IS_PC(ch))
      store_thac0_hitrollMod = ((store_thac0_hitrollMod * cc_thac0_hitrollMod) / 100);
   else
      store_thac0_hitrollMod = store_thac0_hitrollMod * cc_thac0_mobHitrollMod / 100;

#else

   // Now we add the Player's hitroll
   store_thac0_hitrollMod = ((GET_HITROLL(ch) * cc_thac0_hitrollMod) / 100);

   if(!(IS_PC(ch)))
      store_thac0_hitrollMod = store_thac0_hitrollMod * cc_thac0_mobHitrollMod / 100;
#endif

   hitChance += store_thac0_hitrollMod;

   // Paladins and Antis get +20% with 2H weapons
   store_thac0_paladinBonus = 0;
   if(((GET_CLASS(ch) == CLASS_PALADIN) || (GET_CLASS(ch) ==
                                            CLASS_ANTIPALADIN)) && (weaponSkillNum >= SKILL_2H_BLUDGEON) &&
      (weaponSkillNum <= SKILL_2H_MISC))
      {
      store_thac0_paladinBonus = (hitChance / cc_thac0_paladinBonus);
      hitChance += store_thac0_paladinBonus;
      }

   // Debugging
   debuglog(51, DS_COMBAT, "CalcMeleeHit: %d BASE THACO for %s vs. %s",
            hitChance, GET_NAME(ch), GET_NAME(victim));

   // We now have a base hitChance - Add any applicable to hit bonuses

   // Vampiric Touch barehanded bonus
   store_thac0_vampTouchBonus = -1;
   if(!weaponSkillNum && affected_by_spell(ch, SPELL_VAMPIRIC_TOUCH))
      {
      store_thac0_vampTouchBonus = ((MIN(50, GET_LEVEL(ch)) * cc_thac0_vampTouchBonus) / 100);
      hitChance += store_thac0_vampTouchBonus;
      }

   // Blind victim bonus
   store_thac0_blindBonus = -1;
   testBlind = -1;
   if(IS_AFFECTED(victim, AFF_BLIND))
      {
      testBlind = ((testBlind * ((100 - CHECK_BLINDFIGHTING(victim)) / 100)));
      debuglog(51, DS_IYACHTU, "testBlind = %d", testBlind);
      store_thac0_blindBonus = ((cc_thac0_blindBonus *
                                 ((100 - CHECK_BLINDFIGHTING(victim)) / 100)) / 100);
      debuglog(51, DS_IYACHTU, "blindbonus = %d", store_thac0_blindBonus);
      hitChance += store_thac0_blindBonus;
      }

   // Heavily loaded victim bonuses
   store_thac0_loadMod = 0;
   if(load_modifier(victim) > 299)
      store_thac0_loadMod = (3 * cc_thac0_loadMod);
   else if(load_modifier(victim) > 199)
      store_thac0_loadMod = (2 * cc_thac0_loadMod);
   else if(load_modifier(victim) > 99)
      store_thac0_loadMod = cc_thac0_loadMod;
   hitChance += store_thac0_loadMod;

   // End of bonuses - Start whacking hitChance with maluses

   // Unseen victim malus
   if(!CAN_SEE(ch, victim))
      {
      if(IS_NPC(ch))
         {
         hold = cc_thac0_blindMalus * MIN(100, (120 - 2 * GET_LEVEL(ch))) / 100;
         }
      else
         {
         hold = cc_thac0_blindMalus * (100 - CHECK_BLINDFIGHTING(ch)) / 100;
         //CharSkillGainChance(ch, SKILL_BLINDFIGHTING, 15);
         }
      debuglog(51, DS_IYACHTU, "hold = %d", hold);
      store_thac0_blindMalus = hold;
      hitChance -= hold;
      }

   // Protection from evil malus - omgwtf
   if(IS_EVIL(ch) && !IS_EVIL(victim) && IS_AFFECTED(victim, AFF_PROTECT_EVIL) &&
      ((GET_LEVEL(ch) - IMMATERIAL(ch) ? 15 : 0) <= GET_LEVEL(victim)))
      {
      store_thac0_protEvilMalus = ((cc_thac0_protEvilMalus *
                                    (1 + (GET_LEVEL(victim) - GET_LEVEL(ch) + IMMATERIAL(ch) ? 15 : 0) / 7) * 2) / 100);
      hitChance -= store_thac0_protEvilMalus;
      }

   // Protection from good malus
   if(IS_GOOD(ch) && !IS_GOOD(victim) && IS_AFFECTED(victim, AFF_PROTECT_GOOD) &&
      ((GET_LEVEL(ch) - IMMATERIAL(ch) ? 15 : 0) <= GET_LEVEL(victim)))
      {
      store_thac0_protGoodMalus = ((cc_thac0_protEvilMalus *
                                    (1 + (GET_LEVEL(victim) - GET_LEVEL(ch) + IMMATERIAL(ch) ? 15 : 0) / 7) * 2) / 100);
      hitChance -= store_thac0_protEvilMalus;
      }

   // Protection from undead malus
   if(IS_UNDEAD(ch) && !IS_UNDEAD(victim) &&
      affected_by_spell(victim, SPELL_PROT_FROM_UNDEAD))
      {
      for(af = victim->affected; af; af = af->next)
         if(af->type == SPELL_PROT_FROM_UNDEAD)
            break;
      if(af)
         {
         store_thac0_protUndead =
         ((cc_thac0_protUndead *  (af->modifier * IMMATERIAL(ch) ? 2 : 1)) / 100);
         hitChance -= store_thac0_protUndead;
         }
      }

   // Blurred victim malus
   if(affected_by_spell(victim, SPELL_BLUR))
      {
      for(af = victim->affected; af; af = af->next)
         if(af->type == SPELL_BLUR && !af->location)
            break;
      if(af)
         {
         store_thac0_blurrMalus =
         ((cc_thac0_blurrMalus * (af->modifier * (IMMATERIAL(ch) ? 2 : 1))) / 100);
         hitChance -= store_thac0_blurrMalus;
         }
      }

   // hex has reverse blur effect now, 6/16/2001 KPZ
   if(affected_by_spell(victim, SPELL_HEX))
      {
      for(af = victim->affected; af; af = af->next)
         if(af->type == SPELL_HEX && !af->location)
            break;
      if(af)
         {
         hitChance += (af->modifier * (IMMATERIAL(ch) ? 2 : 1));
         }
      }


   // Resurrection malus
   if(IS_AFFECTED(ch, AFF_RES_PENALTY))
      for(af = ch->affected; af; af = af->next)
         if((af->type == SKILL_RES_PENALTY) && (af->duration > 0))
            {
            store_thac0_ressurectMalus = (cc_thac0_ressurectMalus *
                                          (100 - (MIN(12, af->duration) * 8)) / 100);
            store_thac0_ressurectMalus =
            (100 - store_thac0_ressurectMalus) * hitChance / 100;
            hitChance -= store_thac0_ressurectMalus;
            break;
            }

         /* unmounted attacker v. mounted defender --DMB */
   if(!IS_RIDING(ch) && IS_RIDING(victim) &&
      (GET_CHAR_SKILL(victim, SKILL_MOUNTED_COMBAT) > 0))
      {
      hitChance -= ((GET_CHAR_SKILL(victim, SKILL_MOUNTED_COMBAT) + 1) / 10);

      /* additional penalty if not using a 2h weapon due to short reach. */
      if(!ch->equipment[WIELD] || !IS_SET(ch->equipment[WIELD]->extra_flags,
                                          ITEM_TWOHANDS))
         hitChance -= (4 * (GET_CHAR_SKILL(victim, SKILL_MOUNTED_COMBAT) + 1) / 100);
      }

   // Displacement malus: this starts out at a whopping 50-150 points, and is
   // decremented by 10 for every *attempted* hit

   if(IS_AFFECTED(victim, AFF_DISPLACEMENT))
      {
      if((hitChance > 0) && (attackType < TYPE_SUFFERING))
         {
         for(af = victim->affected; af; af = af->next)
            {
            /* af2 = af->next; */
            if(af->type == SPELL_DISPLACEMENT)
               break;
            }
         if(!af || af->type != SPELL_DISPLACEMENT)
            {
            REMOVE_CBIT(victim->specials.affects, AFF_DISPLACEMENT);
            }
         else
            {
            hitChance -= af->modifier;
            //NukeDisplacement(victim);  Moved to CombatDamage()
            }
         }
      }


   // End of Maluses - begin final adjustments

   // Agility adjustment - this can be  a bonus or a malus
   if((attackType != SKILL_BACKSTAB) && (attackType != SKILL_CIRCLE) && AWAKE(victim) &&
      !IS_AFFECTED(victim, AFF_MINOR_PARALYSIS) && !IS_AFFECTED(victim, AFF_MAJOR_PARALYSIS) &&
      (CAN_SEE(victim, ch) || (CHECK_BLINDFIGHTING(victim) < number(1, 100))))
      {
      store_thac0_victAgilityMod =
      (((agi_app[STAT_INDEX(GET_C_AGI(victim))].defensive / 2) *
        cc_thac0_victAgilityMod) / 100);
      hitChance += store_thac0_victAgilityMod;
      }

   // Armor Class adjustment (1/2 of victim's AC is added to hitChance..for now)
   store_thac0_victimACMod = ((cc_thac0_victimACMod *
                               (BOUNDED(-100, (GET_AC(victim)), 100))) / 100);
   hitChance += store_thac0_victimACMod;

   // Add Level-based penalty for trying to attack something too many levels
   // above you. 3/25/2001 - KPZ
   // Penalizes by the square of the difference in levels if the ch is
   // more than 3/5 of the hitters level higher.  Acceptable range is
   // deducted from difference before squaring.

   levelPenalty =
   MAX((GET_LEVEL(victim) - GET_LEVEL(ch)) - (GET_LEVEL(ch) * 3 / 5), 0);
   levelPenalty *= levelPenalty;
   hitChance -= levelPenalty;

   // Lastly, if the victim is totally immobile, jack hitChance up to 100
   // -- Changed to a Sure hit every time. If something is not aware, a
   //     player can walk up and insert the blade wherever necessary and
   //     always hit - the first time. ;) 04/06/01 --MIAX
   if(!AWAKE(victim) || IS_AFFECTED(victim, AFF_MAJOR_PARALYSIS) ||
      (GET_STAT(victim) <= STAT_INCAP))
      hitChance = 200;


   // Gods don't miss!  (much)
   if(IS_TRUSTED(ch))
      hitChance = 200;

   // End of Final Adjustments - prepare to roll the dice

   // Debugging
   debuglog(51, DS_COMBAT, "CalcMeleeHit: %d ADJUSTED THACO for %s vs. %s",
            hitChance, GET_NAME(ch), GET_NAME(victim));

   // Bound hitChance to 1-95
   baseHitChance = hitChance;
   hitChance = BOUNDED(1, hitChance, 95);

   // ROLL THE DICE!  (finally!)
   diceRoll = number(1, 100);

   // Check against over 100 str for bonus chance to crit
   // Changed from 50 to 70 at top, making possible for 7% chance to crit. 4/12/01 --MIAX
   critBonusStr = BOUNDED(0, (GET_C_STR(ch) - 100), 70) / 10;
   critBonusAgi = BOUNDED(0, (GET_C_AGI(victim) - 100), 50) / 10;
   critBonus = BOUNDED(-1, critBonusStr - critBonusAgi, 7);

   // Debugging
   debuglog(51, DS_COMBAT, "CalcMeleeHit: %d diceRoll for %s vs. %s",
            diceRoll, GET_NAME(ch), GET_NAME(victim));

   // Determine type of hit
   if(diceRoll >= 99)                     // Fumble
      hitType = ATTACK_FUMBLE;
   else if(diceRoll <= 2 + critBonus)     // Crit
      hitType = ATTACK_CRIT;
   else if(diceRoll < hitChance)          // Normal hit
      hitType = ATTACK_HIT;
   else                                    // A big fat miss.
      hitType = ATTACK_MISS;

   // Rainbow Pattern Check - turns crits into hits
   /* following line was previously using victim... ARGH - Iyachtu */
   if((hitType == ATTACK_CRIT) && affected_by_spell(ch, SPELL_RAINBOW_PATTERN))
      hitType = ATTACK_HIT;

   /* mounted combat hook - 20% greater chance to fumble (they get a save
      below tho with the other fumble saves) --DMB */
   /*if (IS_RIDING(ch) && (diceRoll == 98) && !number(0,19))
     hitType = ATTACK_FUMBLE;  */
   // Disabled, too harsh on mounted players -KPZ

   /* mounted combat hook for 2handed weapons, causes loss of balance and falling off */
   /*if (IS_RIDING(ch) &&
      (ch->equipment[WIELD] &&
      IS_SET(ch->equipment[WIELD]->extra_flags, ITEM_TWOHANDS)) &&
      (diceRoll == 98)) {
     send_to_char("Your heavy weapon upsets your balance, down you go!\n", ch);
     stop_riding(ch);
     act("You fall to the ground.  You stop riding.", FALSE, ch, 0, 0, TO_CHAR);
     ch->specials.riding = NULL;
     SET_POS(ch, POS_SITTING + GET_STAT(ch));
     update_pos(ch);
   } */
   // Disabled, too harsh on mounted players -KPZ

   // Debugging
   debuglog(51, DS_COMBAT, "CalcMeleeHit: %d hit type for %s vs. %s",
            hitType, GET_NAME(ch), GET_NAME(victim));

   // Store the stats
   ProcessCalcMeleeHit(ch, victim, baseHitChance, hitChance, diceRoll, hitType);

   // Final checks and saves on the hit

   /* Iyachtu - Shev nothing comes out of here as a backstab, does it? */
   /* Procs will never know a backstab happened */
   // Check critical - no backstabs, and must make weapon skill check
   if(hitType == ATTACK_CRIT)
      {
      if((attackType == SKILL_BACKSTAB) || (attackType == SKILL_CIRCLE) ||
         (attackType == SKILL_OUTFLANK) || (attackType == SKILL_OUTFLANK2) ||
         (attackType == SKILL_BACKSTAB2) || (attackType == SKILL_CIRCLE2))
         hitType = ATTACK_HIT;
      if(number(1, 101) > weaponSkill)
         hitType = ATTACK_HIT;
      }

   // Check fumbles - save against dex or weapon skill will treat it as a miss
   if(hitType == ATTACK_FUMBLE)
      {
      if((number(1, 101) < weaponSkill) || StatSave(ch, APPLY_DEX, -diceRoll))
         hitType = ATTACK_MISS;
      }

   // Fin
   return(hitType);

}


// Modified by Miax on 4/12/01 to add Damage knobs, and Stat storing per Char and Class.

int CalcMeleeDam(P_char ch, P_char victim, P_obj weapon, int attackType, int hitType)
{
   int damage = 0;
   int acMod = 0;
   float math = 0;
   int agiterations = 0; /* couldn't resist - Iyachtu */
   int diemod = 0; /* how much to subtract on type of die */
   int agiroll = TRUE;

   // Basic Sanity Checks
   if(!SanityCheck(ch, "MeleeAttack") || !SanityCheck(victim, "MeleeAttack") ||
      (ch->in_room != victim->in_room))
      return -1;

   // Reset thac0 data struct for player
   Clear_Damage_Stores();

   // Begin calculating damage

   // First roll the weapon dice (if we have one)
   // Modified 4/16/01 by Miax: Added a scale for barehand damage, it makes more
   //  sense now, with humands doing 1d4 and ogres doing 1d10. Before, everyone
   //  did 1d2 barehanded. o_O
   if(weapon)
      {
      store_dam_weaponDice =
      ((dice(weapon->value[1], MAX(1, weapon->value[2])) * cc_dam_weaponDiceMod) / 100);
      damage = store_dam_weaponDice;

      // Mob barehand damage is added to weapon damage
      if(IS_NPC(ch) &&
         (!IMMATERIAL(victim) || (GET_LEVEL(ch) + 5 > GET_LEVEL(victim))))
         damage += dice(ch->points.damnodice, ch->points.damsizedice);

      }
   else
      {
      if(IS_PC(ch))
         {
         switch(GET_RACE(ch))
            {
            case RACE_NONE:
               store_dam_barehandDice = number(0, 2);
               break;
            case RACE_HALFLING:
            case RACE_GNOME:
            case RACE_ILLITHID:
            case RACE_LICH:
            case RACE_MYCONID:
            case RACE_DROW:
            case RACE_GREY:
               store_dam_barehandDice = number(0, 3);
               break;
            case RACE_HUMAN:
            case RACE_PORC:
            case RACE_HALFELF:
               store_dam_barehandDice = number(0, 4);
               break;
            case RACE_MOUNTAIN:
            case RACE_DUERGAR:
            case RACE_YUANTI:
               store_dam_barehandDice = number(0, 6);
               break;
            case RACE_BARBARIAN:
               store_dam_barehandDice = number(0, 8);
               break;
            case RACE_TROLL:
               store_dam_barehandDice = number(0, 9);
               break;
            case RACE_OGRE:
               store_dam_barehandDice = number(0, 10);
               break;
            default:
               store_dam_barehandDice = number(0, 2);
               break;
            }
         store_dam_barehandDice = ((store_dam_barehandDice * cc_dam_barehandDiceMod) / 100);
         }
      else
         {
         // If this is an NPC without a weapon, add barehand damage
         store_dam_barehandDice = dice(ch->points.damnodice, ch->points.damsizedice);
         if(IMMATERIAL(victim))
            store_dam_barehandDice /= 2;
#if 0
         if((!IMMATERIAL(victim) || (IS_NPC(ch)) || ((GET_LEVEL(ch) + 5) > GET_LEVEL(victim))))
            store_dam_barehandDice = dice(ch->points.damnodice, ch->points.damsizedice);
#endif

         store_dam_barehandDice = ((store_dam_barehandDice * cc_dam_barehandDiceMod) / 100);
         }

      damage = store_dam_barehandDice;
      }

   // Now calculate damage bonuses (order is important here)

   // Blessed weapons do 2x weapon damage to undead
   if(weapon && IS_OBJ_STAT(weapon, ITEM_BLESS) && IS_UNDEAD(victim))
      damage += (store_dam_weaponDice);

   // Immaterial weapon damage check:  Weapon damage is halved if magic, zero otherwise
   if(weapon && IMMATERIAL(victim))
      {

      if(IS_OBJ_STAT(weapon, ITEM_MAGIC))
         /* Half damage from magic weapons on immterials */
         damage -= MAX(0, (store_dam_weaponDice / 2));
      else
         damage -= store_dam_weaponDice;
      }

   // Add damroll - this is halved or negated for immaterials
   if(mob_index[victim->nr].virtual != 19700)
      {
      store_dam_damrollBonus = ((GET_DAMROLL(ch) * cc_dam_damrollMod) / 100);
#ifdef NEW_BARD
      if(affected_by_song(ch, SONG_OF_OFFENSIVE_HARMONY))
         {
         store_dam_damrollBonus += (ch->bard_singing->song_singing->modifier / 2);
         debuglog(51, DS_IYACHTU, "Adding %d to damroll for %s", ch->bard_singing->song_singing->modifier / 2, ch->player.name);
         }
      else if(affected_by_song(ch, SONG_OF_OFFENSIVE_DISRUPTION))
         {
         store_dam_damrollBonus -= (ch->bard_singing->song_singing->modifier / 2);
         debuglog(51, DS_IYACHTU, "Subtracting %d from damroll for %s", ch->bard_singing->song_singing->modifier / 2, ch->player.name);
         }
#endif
      }
   else
      store_dam_damrollBonus = 0;

   if((IMMATERIAL(victim)) && (store_dam_damrollBonus > 0))
      {
      if((weapon && IS_OBJ_STAT(weapon, ITEM_MAGIC)) ||
         (IS_NPC(ch) && (GET_LEVEL(ch) + 5 > GET_LEVEL(victim))))
         store_dam_damrollBonus /= 2;
      else
         store_dam_damrollBonus = 0;
      }

   damage += store_dam_damrollBonus;

   // Strength bonus - not applicable to wraithforms
   if(!IMMATERIAL(victim))
      {
      store_dam_strDamBonus = ((StrDamBonus(STAT_INDEX(GET_C_STR(ch))) * cc_dam_strDamMod) / 100);
      damage += store_dam_strDamBonus;
      }

   // Backstab/Circle adjustment
   if(weapon && (weapon->value[3] == 11) && !IMMATERIAL(victim))
      {
      if(attackType == SKILL_BACKSTAB || attackType == SKILL_BACKSTAB2)
         store_dam_backstabBonus =
         ((BOUNDED(2, ((GET_LEVEL(ch) / 8) + 2), 8) * cc_dam_backstabMod) / 100);
      else
         store_dam_backstabBonus = 1;
      damage *= store_dam_backstabBonus;
      if(attackType == SKILL_CIRCLE || attackType == SKILL_CIRCLE2)
         store_dam_circleBonus =
         ((BOUNDED(2, ((GET_LEVEL(ch) / 15) + 2), 5) * cc_dam_circleMod) / 100);
      else
         store_dam_circleBonus = 1;
      if(attackType == SKILL_OUTFLANK || attackType == SKILL_OUTFLANK2)
         store_dam_circleBonus =
         ((BOUNDED(2, (GET_LEVEL(ch) / 15), 3) * cc_dam_circleMod) / 100);  // Borrowed circle bonus for dire raiders - KPZ
      damage *= store_dam_circleBonus;
      }

   // Victim posture bonuses
   if(!MIN_POS(victim, POS_STANDING + STAT_NORMAL) && !IMMATERIAL(victim))
      {
      math = 1;
      if(MIN_POS(victim, POS_SITTING + STAT_RESTING))
         {
         math = 1.1;
         }
      else if(MIN_POS(victim, POS_KNEELING + STAT_DEAD))
         {
         math = 1.2;
         }
      else
         {
         math = 1.3;
         }
      store_dam_postureBonus = ((((damage * math) - damage) * cc_dam_postureMod) / 100);
      damage += store_dam_postureBonus;
      }

   // Offense skill bonus (legacy)
   if(IS_PC(ch))
      {
      if(!(phase % 2) && (GET_CHAR_SKILL(ch, SKILL_ATTACK) > number(1, 101)))
         {
         store_dam_offenseBonus =
         ((((damage * number(11, 15) / 10) - damage) * cc_dam_offenseMod) / 100);
         damage += store_dam_offenseBonus;
         CharSkillGainChance(ch, SKILL_ATTACK, 0);
         }
      }
   else
      {
      if(number(1, 101) <= BOUNDED(5, 2 * GET_LEVEL(ch), 99))
         {
         store_dam_offenseBonus =
         ((((damage * number(11, 15) / 10) - damage) * cc_dam_offenseMod) / 100);
         damage += store_dam_offenseBonus;
         }
      }

   /* Mounted attacker v. unmounted defender bonus --DMB */
   if(IS_RIDING(ch) && !IS_RIDING(victim) && !IMMATERIAL(victim))
      {
      store_dam_mountedBonus =
      (((GET_CHAR_SKILL(ch, SKILL_MOUNTED_COMBAT) / 9) * cc_dam_mountedMod) / 100);
      damage += store_dam_mountedBonus;
      }

   // Crit bonus (2 - 3.5 x damage)
   if(hitType == ATTACK_CRIT)
      {
      store_dam_critHitBonus = 0;
      for(agiroll = TRUE; ((agiterations < 5) && agiroll); agiterations++)
         {
         if(number(1, (400 - diemod)) <= MAX(50, BOUNDED(1, GET_C_AGI(victim), 150)))
            agiroll = FALSE;
         else
            store_dam_critHitBonus += damage/2;
         diemod += 50;
         }
      if(IS_PC(victim))
         debuglog(51, DS_COMBAT, "%s: Damage: %d, CritBonus: %d, Agiter: %d, Agi: %d", GET_NAME(victim),
                  damage, store_dam_critHitBonus, agiterations, GET_C_AGI(victim));
      damage += (store_dam_critHitBonus * cc_dam_critHitMod /100);

#if 0
      store_dam_critHitBonus =
      ((((damage * number(4, 7) / 2) - damage) * cc_dam_critHitMod) / 100);
      damage += store_dam_critHitBonus;
#endif
      }


   // End of bonuses - begin final adjustments

   // AC absorption adjustment
   if((damage > 0) && (ch != victim))
      {

      // For purposes of sanity, we bound this.
      acMod = BOUNDED(-100, GET_AC(victim), 100);

      // Various absorption adjustments

      if(IS_AFFECTED(victim, AFF_BLIND))
         acMod += 40 * (100 - CHECK_BLINDFIGHTING(victim)) / 100;

      if(load_modifier(victim) > 299)
         acMod += 40;
      else if(load_modifier(victim) > 199)
         acMod += 25;
      else if(load_modifier(victim) > 99)
         acMod += 10;

      if(!CAN_SEE(ch, victim))
         acMod -= 40 * (100 - CHECK_BLINDFIGHTING(ch)) / 100;

      // Agi app for absorption
      if((attackType != SKILL_BACKSTAB) && (attackType != SKILL_BACKSTAB2) &&
         (attackType != SKILL_CIRCLE) && (attackType != SKILL_CIRCLE2) &&
         (attackType != SKILL_INSTANT_KILL) && AWAKE(victim) &&
         !IS_AFFECTED(victim, AFF_MINOR_PARALYSIS) &&
         !IS_AFFECTED(victim, AFF_MAJOR_PARALYSIS) &&
         (CAN_SEE(victim, ch) || (CHECK_BLINDFIGHTING(victim) < number(1, 100))))
         acMod += agi_app[STAT_INDEX(GET_C_AGI(victim))].defensive;

      // Bound it - gotta keep this sane
      acMod = BOUNDED(-100, acMod, 100);

      // Final adjustment
      store_dam_acAbsorptionBonus =
      ((BOUNDED(0, (damage * (100 - acMod)) / 1000, (damage - 1)) * cc_dam_acAbsorptionMod) / 100);
      damage -= store_dam_acAbsorptionBonus;
      }
   // End AC absorption adjustment

   // Control knobs to increase damage mobs do
   if(IS_NPC(ch))
      {
      if(GET_LEVEL(ch) < 11)
         {
         store_dam_mobDam10Bonus = ((damage * cc_dam_mobDam10Mod) / 100);
         damage = store_dam_mobDam10Bonus;
         }
      else if(GET_LEVEL(ch) < 21)
         {
         store_dam_mobDam20Bonus = ((damage * cc_dam_mobDam20Mod) / 100);
         damage = store_dam_mobDam20Bonus;
         }
      else if(GET_LEVEL(ch) < 31)
         {
         store_dam_mobDam30Bonus = ((damage * cc_dam_mobDam30Mod) / 100);
         damage = store_dam_mobDam30Bonus;
         }
      else if(GET_LEVEL(ch) < 41)
         {
         store_dam_mobDam40Bonus = ((damage * cc_dam_mobDam40Mod) / 100);
         damage = store_dam_mobDam40Bonus;
         }
      else if(GET_LEVEL(ch) < 51)
         {
         store_dam_mobDam50Bonus = ((damage * cc_dam_mobDam50Mod) / 100);
         damage = store_dam_mobDam50Bonus;
         }
      else
         {
         store_dam_mobDam60Bonus = ((damage * cc_dam_mobDam60Mod) / 100);
         damage = store_dam_mobDam60Bonus;
         }
      }


   // Bound it, in case of "sicko setbit mobs" and "god toys"
   store_dam_bounded = BOUNDED(0, damage, 32766);
   damage = store_dam_bounded;

   // Debugging
   debuglog(51, DS_COMBAT, "CalcMeleeDam: %d damage for %s vs. %s",
            damage, GET_NAME(ch), GET_NAME(victim));

   // Store the stats
   ProcessCalcMeleeDamage(ch, victim, damage);

   // Fin!
   return(damage);

}

int is_missile_spell(int attackType)
{
   switch(attackType)
      {
      case SPELL_MAGIC_MISSILE:
         return TRUE;
      case SPELL_MINUTE_METEORS:
         return TRUE;
      case SPELL_FORCE_MISSILES:
         return TRUE;
      case SPELL_SHADOW_BOLT:
         return TRUE;
      case SPELL_TOTEM_DARTS:
         return TRUE;
      case SPELL_STICKS_TO_SNAKES:
         return TRUE;
      case SPELL_FIRE_SEEDS:
         return TRUE;
      case SPELL_EARTH_DARTS:
         return TRUE;
      default:
         return FALSE;
      }
}

int CombatDamage(P_char ch, P_char victim, int damage, int attackType)
{
   P_obj weapon = NULL;
   P_group group = NULL;
   int shieldType = 0, shieldDam = 0;
   int spellType = 0, skillLevel = 0;
   int orig_attackType = attackType;
   int condense, holddam;
   P_char caster;
   int i = 0, j = 0, nr = 0, XP = 0, diff = 0, max_hit = 0, resist = 0;
   int oldPosition = 0, newPosition = 0;
   int temp, block_flag = 0, def_type = 0;
   bool duelKO = FALSE, sitBash = FALSE, firstAttack = TRUE, secondary = FALSE;
   struct message_type *messages;
   struct affected_type *aff;
   crm_rec *crec;
   int modifier, full_damage, stone_exp;

   // Debugging
   debuglog(51, DS_COMBAT, "Entering CombatDamage for %s vs. %s for %d",
            GET_NAME(ch), GET_NAME(victim), damage);

   if(IS_DEAD(victim))
      {
      wizlog(51, "victim(%s) is dead already in call to CombatDamage(), returning TRUE", GET_NAME(victim));
      return TRUE;
      }

   // Debugging II
   if(attackType == -1)
      {
      debuglog(51, DS_COMBAT, "RESET attackType!");
      attackType = TYPE_HIT;
      }

   // Sanity Check
   if(!SanityCheck(ch, "CombatDamage") || !SanityCheck(victim, "CombatDamage"))
      return FALSE;


   // Dead chars need not apply
   if(GET_STAT(victim) == STAT_DEAD)
      {
      logit(LOG_EXIT, "CombatDamage: called with dead victim");
      dump_core();
      }

   // Determine what form of condense to check on messages - Iyachtu
   if(IS_SPELL(attackType))
      {
      if(IS_SET(skills[pindex2Skill[attackType]].targets, TAR_IGNORE) ||
         is_missile_spell(attackType))
         condense = SPELL_DMG;
      else
         condense = SPELL_MESS;
      }
   else if((attackType == SKILL_ULTRABLAST) || (attackType == SKILL_DEATH_FIELD))
      condense = SPELL_DMG;
   else if(attackType == TYPE_PSIONIC || IS_PSISKILL(attackType))
      condense = SPELL_MESS;
   else if((attackType >= TYPE_HIT) && (attackType <= TYPE_CRUSH))
      condense = MELEE_HITS;
   else
      condense = MELEE_SPEC;

   if((attackType == SPELL_TAZRIKS) || (attackType == SPELL_BALL_OF_LIGHTNING))
      condense = SPELL_MESS;

   // A few standard char vs. char checks
   if(victim != ch)
      {
      if(IS_AFFECTED(ch, AFF_WRAITHFORM) || IS_AFFECTED(victim, AFF_WRAITHFORM))
         return FALSE;
      if(CHAR_IN_SAFE_ZONE(ch))
         return FALSE;
      if(should_not_kill(ch, victim) == TRUE)
         return FALSE;
      if(nokill(ch, victim) == TRUE)
         return FALSE;
      if(IS_TRUSTED(victim) && IS_CSET(victim->only.pc->pcact, PLR_AGGIMMUNE))
         return FALSE;

      // Pkill Pet Check (includes PC pets)
      if(IS_AFFECTED(ch, AFF_CHARM) && (ch->following) && IS_PC(ch->following) &&
         (IS_PC(victim) || IS_MORPH(victim)) && (victim->specials.fighting != ch) &&
         should_not_kill(ch->following, victim))
         {
         send_to_char("&+WYou cannot harm another player, even with pets!\n", ch->following);
         return FALSE;
         }

      // Remove invis
      if(IS_AFFECTED(ch, AFF_INVISIBLE))
         appear(ch);

      // Justice Hook
      justice_witness(ch, victim, CRIME_ATT_MURDER);

      // Is victim sitting due to bash?
      if(IS_FIGHTING(victim) && (GET_POS(victim) == POS_SITTING))
         sitBash = TRUE;

      // Flag this as a first attack if not fighting, for msg handling
      if(!IS_FIGHTING(ch))
         firstAttack = TRUE;

      // If this is a first attack, set them fighting or add to memory for spells
      if((GET_STAT(victim) > STAT_INCAP) && !IS_FIGHTING(ch))
         {
         if((IS_SPELL(attackType) || IS_PSISKILL(attackType))  && HAS_MEMORY(victim) && victim->only.npc->memory)
            {
            if(IS_PC(ch))
               mem_addToMemory(victim->only.npc->memory, GET_NAME(ch));
            else if(IS_AFFECTED(ch, AFF_CHARM) && ch->following && IS_PC(ch->following) &&
                    (ch->following->in_room == ch->in_room) && CAN_SEE(victim, ch->following))
               mem_addToMemory(victim->only.npc->memory, GET_NAME(ch->following));
            }
         else
            {
            StartCombat(ch, victim);
            }
         }

      // Victim stops following if attacked
      if(victim->following == ch)
         stop_follower(victim);

      // Victim stops consenting if attacked
      if(victim->specials.consent && (victim->specials.consent == ch))
         {
         act("Fool me once, shame on you...", FALSE, ch, 0, victim, TO_VICT);
         act("$N revokes $S consent.", FALSE, ch, 0, victim, TO_CHAR);
         victim->specials.consent = NULL;
         }

      // End Standard combat checks
      }

   // Determine which weapon and type we are using
   if((attackType == TYPE_SECONDARY_WEAPON) ||
      (attackType == TYPE_CRIT) ||
      (attackType == TYPE_CRIT2) ||
      (attackType == SKILL_CIRCLE2) ||
      (attackType == SKILL_OUTFLANK2) ||
      (attackType == SKILL_BACKSTAB2))
      {

      if(attackType !=  TYPE_CRIT)
         secondary = TRUE;

      if(attackType != TYPE_CRIT)
         weapon = ch->equipment[SECONDARY_WEAPON];
      else
         weapon = ch->equipment[WIELD];

      // Handle secondary circle
      if(attackType == SKILL_CIRCLE2)
         attackType = SKILL_CIRCLE;

      // Handle secondary backstab
      else if(attackType == SKILL_BACKSTAB2)
         attackType = SKILL_BACKSTAB;

      // Handle secondary outflank
      else if(attackType == SKILL_OUTFLANK2)
         attackType = SKILL_OUTFLANK;

      // Get the hit type for secondary weapons
      else if(weapon && ((weapon->type == ITEM_WEAPON) || (weapon->type == ITEM_FIREWEAPON)))
         {
         switch(weapon->value[3])
            {
            case 0:
            case 1:
            case 2:
               attackType = TYPE_WHIP;
               break;
            case 3:
               attackType = TYPE_SLASH;
               break;
            case 4:
            case 5:
            case 6:
               attackType = TYPE_CRUSH;
               break;
            case 7:
               attackType = TYPE_BLUDGEON;
               break;
            case 8:
            case 9:
               attackType = TYPE_CLAW;
               break;
            case 10:
               attackType = TYPE_BITE;
               break;
            case 11:
               attackType = TYPE_PIERCE;
               break;
            default:
               attackType = TYPE_HIT;
               break;
            }
         }
      else
         {
         if(IS_NPC(ch) && (ch->only.npc->attack_type >= TYPE_HIT))
            attackType = ch->only.npc->attack_type;
         else
            attackType = TYPE_HIT;
         }
      }
   else
      {
      weapon = ch->equipment[PRIMARY_WEAPON];
      }

   // Classify spells into their appropriate type
   if(IS_SPELL(attackType))
      spellType = GET_SPELLTYPE(attackType);

   // Check special spell affects

#ifdef NEW_NECRO
   /* Pet mobs do 1/3 damage with spells */
   if(IS_SPELL(attackType) && IS_PET(ch))
      {
      damage = ((damage * 2) / 3);
      }
#else
   /* Pet mobs do 1/3 damage with spells */
   if(IS_SPELL(attackType) && IS_PET(ch))
      {
      damage /= 3;
      }
#endif

   // New Defensive skills
   if(IS_ENABLED(CODE_NEWDEFENSE))
      {

      // Check Defensive skills for avoidance or damage reduction
      if((damage > 0) && !IS_SPELL(attackType) && (attackType <= TYPE_CRUSH) && (attackType >= TYPE_HIT))
         {

         // This works by checking each defensive skill until block_flag == FULL_DEFENSE,
         // indicating full avoidance.  If we never get a full block, the last partial
         // block will go into affect.

         // Parry
         if(block_flag != DEFENSE_FULL)
            if((block_flag = attemptParry(victim, ch, (secondary ? SECONDARY_WEAPON : WIELD))))
               def_type = SKILL_PARRY;

            // Shieldblock
         if(block_flag != DEFENSE_FULL)
            if((block_flag = attemptShieldBlock(victim, ch, (secondary ? SECONDARY_WEAPON : WIELD))))
               def_type = SKILL_SHIELDBLOCK;

            // Flank Block
         if(block_flag != DEFENSE_FULL)
            if((block_flag = attemptMountBlock(victim, ch, (secondary ? SECONDARY_WEAPON : WIELD))))
               def_type = SKILL_MOUNTED_COMBAT;

            // Dodge
         if(block_flag != DEFENSE_FULL)
            if((block_flag = attemptDodge(victim, ch, (secondary ? SECONDARY_WEAPON : WIELD))))
               def_type = SKILL_DODGE;

            // If we get here with a def type and false block flag, means we got a partial..
         if(!block_flag && def_type)
            block_flag = DEFENSE_PARTIAL;

         // Show Msgs if we blocked, check for procs and specials
         if(block_flag)
            DefenseProcess(victim, ch, def_type, block_flag, (secondary ? SECONDARY_WEAPON : WIELD));

         // block_flag: 1 = full avoidance, 2 = half avoidance
         if(block_flag == DEFENSE_FULL)
            return FALSE;
         else if(block_flag == DEFENSE_PARTIAL)
            damage >>= 1;
         }
      }

   // If we made it past defensive checks, degrade displacement (even for misses)
   if(IS_AFFECTED(victim, AFF_DISPLACEMENT) && (attackType <= TYPE_CRUSH) && (attackType >= TYPE_HIT))
      NukeDisplacement(victim);

   // Process Crits - show msg, check VitalStrike
   if((orig_attackType == TYPE_CRIT) || (orig_attackType == TYPE_CRIT2))
      {
      make_bloodstain(ch);
      if((GET_CHAR_SKILL(ch, SKILL_VITAL_STRIKE) / 2) > number(1, 101))
         damage = VitalStrike(ch, victim, (int) damage);
      else
         send_to_char("&+WYou score a CRITICAL HIT!\n", ch);
      }

   // Poison check - yank aff from mobs about to die and set them to 10 HP
   if(((spellType == SPELL_POISON) || (attackType == SPELL_POISON) ||
       (attackType == TYPE_SUFFERING)) &&
      ((GET_HIT(victim) - damage) <= 10) && IS_NPC(victim))
      {
      NukePoison(victim);
      return FALSE;
      }

   // Mirror Image
   if(IS_AFFECTED(victim, AFF_MIRROR_IMAGE))
      if((damage > 0) && (attackType < TYPE_SUFFERING) &&
         (attackType != SKILL_BASH))
         if(NukeMirrorImage(victim, ch, attackType))
            return FALSE;

         // Modified by Uthgar, then Miax to add stat/control code. 4/21/01 --MIAX

         // Elemental Ward - blocks fire/cold shield attackback
   if((IS_AFFECTED(victim, AFF_ELEMENTAL_WARD)) && (ch != victim) && (damage > 0) &&
      ((attackType == SPELL_FIRESHIELD) || (attackType == SPELL_COLDSHIELD)))
      {
      if(attackType == SPELL_FIRESHIELD)
         c_act("&+rYour strike passes through $n&+r's fireshield in a shower of &+Rsparks&N&+r!&N",
               FALSE, ch, 0, victim, TO_VICT, SPELL_SPEC);
      else
         c_act("&+cYour strike passes through $n&+c's coldshield in a spray of &+Wfrost&N&+c!&N",
               FALSE, ch, 0, victim, TO_VICT, SPELL_SPEC);
      attack_back(ch, victim);
      return FALSE;
      }

   // Stoneskin
   if(IS_AFFECTED(victim, AFF_STONE_SKIN))
      {
      if((damage > 0) && !IS_SPELL(attackType) && (attackType < TYPE_SUFFERING))
         {
         temp = number((NewSaves(victim, SAVING_PETRI, -2) ? 60 : 24), 108);
         temp = temp * cc_dam_stoneProtectSize / 100;
         holddam = damage;
         if(IMMATERIAL(victim))
            full_damage = damage * 2;
         else
            full_damage = damage;
         damage = ((full_damage > temp) ? damage : 1);
         if((holddam > damage) && (IS_PC(victim)))
            {
            caster = affect_find_caster(victim, SPELL_STONE_SKIN);
            if((caster) && (caster->in_room == victim->in_room)  &&
               (ARE_GROUPED(caster, victim)) &&
               ((CAN_SEE(ch, caster)) || (caster->specials.fighting)))
               {
               stone_exp = (holddam - damage) * GET_LEVEL(victim) / 5;
               if(GET_CLASS(caster) != CLASS_ENCHANTER)
                  stone_exp /= 2;
               debuglog(51, DS_STONESKIN, "%s gained %d exp from stone on %s (%d damage saved)", GET_NAME(caster),
                        stone_exp, GET_NAME(victim), (holddam - damage));
               gain_exp(caster, stone_exp);
               }
            }
         store_dam_stoneProtectSize = (temp - damage);
         }
      if(damage > 0)
         NukeSingleStone(victim);
      }

   // Dragonscales
   if(IS_AFFECTED(victim, AFF_DRAGONSCALES))
      {
      if((damage > 0) && !IS_SPELL(attackType) && (attackType < TYPE_SUFFERING))
         {
         holddam = damage;
         temp = number((NewSaves(victim, SAVING_PETRI, -2) ? 75 : 30), 135);
         temp = temp * cc_dam_scaleProtectSize / 100;
         damage = ((damage > temp) ? damage : 1);
         store_dam_scaleProtectSize = (temp - damage);
         if((holddam > damage) && (IS_PC(victim)))
            {
            caster = affect_find_caster(victim, SPELL_DRAGONSCALES);
            if((caster) && (caster->in_room == victim->in_room)  &&
               (ARE_GROUPED(caster, victim)) &&
               ((CAN_SEE(ch, caster)) || (caster->specials.fighting)))
               {
               stone_exp = (holddam - damage) * GET_LEVEL(victim) / 5;
               debuglog(51, DS_STONESKIN, "%s gained %d exp from scale on %s (%d damage saved)", GET_NAME(caster),
                        stone_exp, GET_NAME(victim), (holddam - damage));
               gain_exp(caster, stone_exp);
               }
            }
         }
      if(damage > 0)
         NukeSingleScale(victim);
      }

   // Check fireshield/coldshield
   if(!IS_SPELL(attackType) && (attackType != TYPE_INSTANTKILL) &&
      (attackType != SONG_HARMING) && (attackType < TYPE_ARROW_MISSILE ||
                                       attackType == DAMAGE_SNAKEBITE || attackType == DAMAGE_TAILSWEEP) &&
      (ch != victim) && (damage > 0) && (attackType < TYPE_SUFFERING) &&
      (attackType != TYPE_PSIONIC) && !IS_PSISKILL(attackType) &&
      (attackType != 0))
      {    /* Added that last check - Iyachtu */

      // Set shield damage and shield type (fire/cold)
      if(IS_AFFECTED(victim, AFF_FIRESHIELD))
         {
         shieldDam = damage;
         if(affected_by_spell(victim, SPELL_COLDSHIELD))
            {
            shieldType = SPELL_COLDSHIELD;
            if(IS_AFFECTED(ch, AFF_PROT_COLD))
               shieldDam /= 2;
            }
         else if(affected_by_spell(victim, SPELL_UNHOLY_AURA))
            {
            if(IS_GOOD(ch))
               {
               shieldType = SPELL_UNHOLY_AURA;
               if(IS_AFFECTED(ch, AFF_PROTECT_EVIL))
                  shieldDam /= 2;
               }
            }
         else if(affected_by_spell(victim, SPELL_VAMPIRIC_CURSE))
            {
            shieldType = SPELL_VAMPIRIC_CURSE;
            shieldDam /= 3;
            }
         else
            {
            shieldType = SPELL_FIRESHIELD;
            if(IS_AFFECTED(ch, AFF_PROT_FIRE))
               shieldDam /= 2;
            }
         }
      }

   // Check Magic Resistance
   if(IS_SPELL(attackType) && (ch != victim))
      if(DoesResist(ch, victim))
         {
         attack_back(ch, victim);
         return FALSE;
         }

      // Check Psionic Resistance
   if(IS_PSISKILL(attackType) && (ch != victim))
      if((resist = DoesPsiResist(ch, victim)))
         {
         debuglog(51, DS_D2, "Psi resist: pre-resist damage = %d", damage);
         damage -= (damage * resist / 100);
         debuglog(51, DS_D2, "Psi resist: post-resist damge = %d", damage);
         attack_back(ch, victim);
         return FALSE;
         }

      // MetaGlobe
   if(IS_AFFECTED(victim, AFF_METAGLOBE) && IS_SPELL(attackType) &&
      (((attackType != SPELL_POISON) &&
        (attackType != SPELL_EARTHQUAKE) &&
        (attackType != SPELL_FIRE_BREATH) &&
        (attackType != SPELL_GAS_BREATH) &&
        (attackType != SPELL_FROST_BREATH) &&
        (attackType != SPELL_ACID_BREATH) &&
        (attackType != SPELL_FAERIE_REDUCE) &&
        (attackType != SPELL_DARKNESS_BREATH) &&
        (attackType != SPELL_LIGHTNING_BREATH)) ||
       (attackType == SPELL_FIRESHIELD) ||
       (attackType == SPELL_UNHOLY_AURA) ||
       (attackType == SPELL_COLDSHIELD)))
      {
      for(aff = ch->affected; aff; aff = aff->next)
         {
         if(aff->type == SKILL_METAGLOBE)
            {
            if(!IS_SET(aff->modifier, power_table[GetSpellCircle(ch, attackType) - 1]))
               {
               c_act("&+rThe metaglobe around your body flares as it bears the brunt "
                     "of $n's assault!", FALSE, ch, 0, victim, TO_VICT, SPELL_SPEC);
               c_act("&+rThe metaglobe around $N's body flares as it bears the brunt "
                     "of your assault!", FALSE, ch, 0, victim, TO_CHAR, SPELL_SPEC);
               return FALSE;
               }
            }
         }
      }

   // Globe (Minor and Major)
   if((IS_AFFECTED(victim, AFF_MINOR_GLOBE) || IS_AFFECTED(victim, AFF_GLOBE))  &&
      (ch != victim) && (damage > 0) &&
      ((IS_SPELL(attackType) && ((skills[pindex2Skill[attackType]].harmful &&
                                  (attackType != SPELL_POISON) &&
                                  (attackType != SPELL_EARTHQUAKE) &&
                                  (attackType != SPELL_FIRE_BREATH) &&
                                  (attackType != SPELL_GAS_BREATH) &&
                                  (attackType != SPELL_FROST_BREATH) &&
                                  (attackType != SPELL_ACID_BREATH) &&
                                  (attackType != SPELL_LIGHTNING_BREATH) &&
                                  (attackType != SPELL_FAERIE_REDUCE) &&
                                  (attackType != SPELL_DARKNESS_BREATH) &&
                                  (attackType != SPELL_PWORD_KILL) &&
                                  (attackType != SPELL_HOLY_WORD) &&
                                  (attackType != SPELL_UNHOLY_WORD) &&
                                  (attackType != SKILL_MINDBLAST) &&
                                  (attackType != SPELL_SUNRAY)) ||
                                 (attackType == SPELL_FIRESHIELD) ||
                                 (attackType == SPELL_UNHOLY_AURA) ||
                                 (attackType == SPELL_COLDSHIELD)))))
      {
      i = GetLowestSpellCircle_p(attackType);
      if((IS_AFFECTED(victim, AFF_MINOR_GLOBE) && (i < 4)) ||
         (IS_AFFECTED(victim, AFF_GLOBE) && ((i < 7) || attackType == SPELL_UNHOLY_AURA)))
         {
         c_act("&+RThe globe around your body flares as it bears the brunt of $n's assault!",
               FALSE, ch, 0, victim, TO_VICT, MELEE_HITS);
         c_act("&+RThe globe around $N's body flares as it bears the brunt of your assault!",
               FALSE, ch, 0, victim, TO_CHAR, MELEE_SPEC);
         attack_back(ch, victim);
         return FALSE;
         }
      }

   // Tower of Iron Will - reduces psi damage by a % dependent on skill
   if((attackType == TYPE_PSIONIC || IS_PSISKILL(attackType)) && IS_AFFECTED(victim, AFF_TOWER_OF_IRON_WILL) && (damage > 0))
      {
      if(IS_NPC(ch))
         skillLevel = (GET_LEVEL(ch) * 3/2);
      else
         skillLevel = GET_CHAR_SKILL(victim, SKILL_TOWER_OF_IRON_WILL);
      damage -= (damage * skillLevel / 100);
      c_act("&+BThe tower around your mind protects you from the damaging effects "
            "of &N$n&+B's psionic attack!", FALSE, ch, 0, victim, TO_VICT, SPELL_SPEC);
      }


   if(!IS_AFFECTED(victim, AFF_TOWER_OF_IRON_WILL))
      {
      if((attackType == TYPE_PSIONIC || (IS_PSISKILL(attackType) && attackType != SKILL_PROJECT_FORCE)) && (IS_AFFECTED(victim, AFF_MIND_BLANK) && damage > 0))
         {
         damage /= 2;
         c_act("&+LThe mental protections surrounding you protect you from some of the damaging effects of &N$n&+L's psionic attack!&N", FALSE, ch, 0, victim, TO_VICT, SPELL_SPEC);
         c_act("&+LThe mental barrier around &N$n&+L partially blocks your psionic attack!&N", FALSE, victim, 0, ch, TO_VICT, SPELL_SPEC);
         }
      }

   // Poor trolls get all crispy and take double dam from fire spells
   if((GET_RACE(victim) == RACE_TROLL) && (IS_FIRE_SPELL(attackType)))
      damage *= 2;

   if(((GET_RACE(victim) == RACE_F_ELEMENTAL) || (GET_RACE(victim) == RACE_EFFREETI)) && (IS_FIRE_SPELL(attackType) && attackType != SPELL_FIRE_BREATH))
      {
      act("Your magical fire is absorbed by $N.", TRUE, ch, 0, victim, TO_CHAR);
      act("$n's &+rmagical fire&n is absorbed by $N.", TRUE, ch, 0, victim, TO_NOTVICT);
      act("$n's &+rmagical fire&N mends your wounds&n.", TRUE, ch, 0, victim, TO_VICT);
      GET_HIT(victim) = MIN(GET_MAX_HIT(victim), GET_HIT(victim) + damage);
      return 0;
      }

   if(((GET_RACE(victim) == RACE_F_ELEMENTAL) || (GET_RACE(victim) == RACE_EFFREETI)) && IS_COLD_SPELL(attackType))
      {
      act("&+cYour &+Bmagical cold&N&+c makes &N$N&N&+c howl in pain.", TRUE, ch, 0, victim, TO_CHAR);
      act("&+cThe &+Bmagic cold&N&+c cast by &N$n&N&+c makes &N$N&N &+cscream in pain.&N", TRUE, ch, 0, victim, TO_NOTVICT);
      act("$n&N&+c's spell makes you howl in pain.&N", TRUE, ch, 0, victim, TO_VICT);
      damage *= 2;
      }


   // The following switch is for damage reduction due to ancestral shield
   // Victims take between 60% and 80% of normal damage from area spells
   // If number over 70% is rolled, ancestral shield is destroyed
   // 7/2002 KPZ

   modifier = number(60, 80);
   if(IS_AFFECTED(victim, AFF_ANCESTRAL_SHIELD))
      {
      switch(attackType)
         {
         case SPELL_ICE_STORM:
         case SPELL_CHAIN_LIGHTNING:
         case SPELL_INCENDIARY_CLOUD:
         case SPELL_PRISMATIC_SPRAY:
         case SPELL_THUNDERBLAST:
         case SPELL_BLACKLIGHT_BURST:
         case SPELL_METEOR_SWARM:
         case SPELL_ACIDSTORM:
         case SPELL_SANDSTORM:
         case SPELL_INFERNO:
         case SPELL_HOLY_WORD:
         case SPELL_UNHOLY_WORD:
         case SPELL_SUNRAY:
         case SPELL_FIRESTORM:
         case SPELL_CYCLONE:
         case SPELL_CALL_LIGHTNING:
         case SPELL_CREEPING:
         case SPELL_SOUL_TEMPEST:
         case SPELL_ANCESTRAL_FURY:
         case SPELL_INSECT_PLAGUE:
         case SPELL_HAILSTORM:
         case SPELL_DESSICATE:
         case SPELL_ROT:
         case SPELL_RAIN_OF_BLOOD:
         case SPELL_ICEWAVE:
         case SPELL_FIREWAVE:
         case SPELL_SHADOW_BURST:
         case SPELL_EVARDS_TENTACLES:
            // Any new area spell created needs to be added to this switch
            damage = damage * modifier / 100;
            if(modifier > 70)
               {
               send_to_char("&+CThe strain of absorbing the spell dissipates your ancestral shield!&N\n", victim);
               for(aff = victim->affected; aff; aff=aff->next)
                  if(aff->type == SPELL_ANCESTRAL_SHIELD)
                     {
                     affect_remove(victim, aff);
                     break;
                     }
               REMOVE_CBIT(victim->specials.affects, AFF_ANCESTRAL_SHIELD);
               }
            else
               send_to_char("&+CYour ancestral shield absorbs some of the spell!&N\n", victim);
            break;
         }
      }

   // End of Special afffects and modifiers - begin final adjustments


   // Can't hurt gods..
   if(IS_TRUSTED(victim))
      damage = 0;

   // Chaos Wackiness
   if(IS_ENABLED(CODE_CHAOS) && IS_PC(ch) && damage)
      {
      if(IS_SPELL(attackType))
         {
         if(!number(0, 10))
            {
            send_to_char("&+LYou feel a surge of &N&+rchaotic energy&+L as your spell goes haywire, causing massive damage!&N\n", ch);
            act("&+L$n surges with&N&+r chaotic energy&+L as $s spell goes haywire!&N", FALSE, ch, 0, 0, TO_ROOM);
            damage *= (number(2, 5));
            }
         }
      else
         {
         if(!number(0, 29))
            {
            send_to_char("&+rYou feel a surge of &N&+Lchaotic energy&N&+r as you strike with insane force!\n&N", ch);
            act("&+r$n surges with&N&+L chaotic energy&+L as $e strikes with insane force!", FALSE, ch, 0, 0, TO_ROOM);
            damage *= (number(2, 5));
            }
         }
      }

   // Just in case we ended up with a wacked number..
   damage = BOUNDED(0, damage, 32768);

   // Duel Check - if this is a killing blow, set flag and handle it below
   if(((GET_HIT(victim) - damage) <= 0)  && are_duelling(ch, victim))
      {
      duelKO = TRUE;
      damage = (GET_HIT(victim) - 1);
      }

   // Wizlog major damage - helps to track down weirdness
   if(damage > 1000 && !IS_TRUSTED(ch))
      wizlog(51, "%s did %d damage to %s.", GET_NAME(ch), damage, GET_NAME(victim));

   // Log the damage to the group struct
   if((group = GET_GROUP(ch)))
      {
      if(IS_SPELL(attackType))
         {
         group->combat_log.spellAttacks++;
         group->combat_log.spellDamage += damage;
         }
      else
         {
         group->combat_log.meleeHits++;
         group->combat_log.meleeDamage += damage;
         }
      }

   // Finally - DAMAGE the victim!
   store_dam_totalAdjustedDamage = damage * cc_dam_totalAdjustedDamage / 100;
   GET_HIT(victim) -= store_dam_totalAdjustedDamage;

   // Store the damage stats to the player and mud damage structs. 4/21/01 --MIAX
   ProcessCombatDamage(ch, victim, store_dam_totalAdjustedDamage, attackType);

   // Ok, we check to see if mob has the DELAY_HUNTER flag and setbit it if hurt
   if(IS_NPC(victim) && IS_CSET(victim->only.npc->npcact, ACT_DELAY_HUNTER))
      {
      if(GET_HIT(victim) < (GET_MAX_HIT(victim) * .9))
         {
         SET_CBIT(victim->only.npc->npcact, ACT_HUNTER);
         REMOVE_CBIT(victim->only.npc->npcact, ACT_DELAY_HUNTER);
         }
      }

   // Increment the hitCount var for multimessages - for melee attacks only
   if((attackType >= TYPE_HIT) && (attackType <= TYPE_CRUSH) && (damage >0))
      {
      ch->points.hitCount++;
      // Doppleganger Hack
      if(IS_NPC(ch) && ch->following && (mob_index[ch->nr].virtual == 204))
         ch->following->points.hitCount++;
      }

   // Ok, damage is done.  Proceed to clean up, show messages, etc..


   // Handle smartprompt
   if(IS_PC(victim) && IS_CSET(victim->only.pc->pcact, PLR_SMARTPROMPT) && victim->desc && (damage > 0))
      victim->desc->prompt_mode = 1;

   // Save old position, then update
   oldPosition = GET_POS(victim) + GET_STAT(victim);
   update_pos(victim);

   // Did we wake sleeping beauty?
   if((damage > 0) && (GET_STAT(victim) == STAT_SLEEPING))
      {
      act("$n has a RUDE awakening!", TRUE, victim, 0, 0, TO_ROOM);
      /*
       * Get them into position so there isn't another RUDE awakening message
       * in update_pos. -Urdlen
       */
      SET_POS(victim, POS_SITTING + GET_STAT(victim));
      affect_from_char(victim, SPELL_SLEEP);
      do_wake(victim, 0, -4);
      }

   // Swap old position back in, save the new
   newPosition = GET_POS(victim) + GET_STAT(victim);
   SET_POS(victim, oldPosition);

   // Gain exp!
   if((ch != victim) && !IN_ACHERON(ch))
      {
      i = (GET_LEVEL(victim) * damage * exp_mod(ch, victim));
      i = i / (align_mod(ch, victim) * 400);
      if(IS_EVIL(victim) && (GET_CLASS(GET_PLYR(ch)) == CLASS_PALADIN))
         i = (int)(i * 1.25);
      if((GET_CLASS(ch) == CLASS_INVOKER) && (i > 0))
         i = (int) (i * .50);
      if((GET_CLASS(ch) == CLASS_ENCHANTER) && (i > 0))
         i = i * 3 / 2;
      gain_exp(ch, i);
      }

   // Nuke minor para
   if(IS_AFFECTED(victim, AFF_MINOR_PARALYSIS) && (damage > 0))
      {
      affect_from_char(victim, SPELL_MINOR_PARALYSIS);
      REMOVE_CBIT(victim->specials.affects, AFF_MINOR_PARALYSIS);
      }

   // If victim is now dead, we need to show the multihit msg
   if((newPosition & STAT_DEAD) &&
      (attackType >= TYPE_HIT) && (attackType <= TYPE_CRUSH))
      {
      ch->points.hitCount--;
      dam_message_multi(ch, victim);
      ch->points.hitCount = 0;
      }

   // Handle special instance of shields - Iyachtu
   if((attackType == SPELL_COLDSHIELD) || (attackType == SPELL_UNHOLY_AURA) ||
      (attackType == SPELL_FIRESHIELD))
      {
      condense = MELEE_HITS;
      if(damage >0)
         shieldHits++;
      }

   // Handle special instance of dragon breath - Iyachtu
   if((attackType == SPELL_FIRE_BREATH) || (attackType == SPELL_FROST_BREATH) ||
      (attackType == SPELL_LIGHTNING_BREATH) || (attackType == SPELL_GAS_BREATH) ||
      (attackType == SPELL_ACID_BREATH) || (attackType == SPELL_DARKNESS_BREATH))
      condense = SPELL_SPEC;

   if((attackType==SKILL_OUTFLANK)||(attackType==SKILL_OUTFLANK2))
      attackType=TYPE_SLASH;
   // modification of attack type to make outflank show slashing message

   // Show the damage messages
   if((attackType >= TYPE_HIT) && (attackType <= TYPE_CRUSH) &&
      !(newPosition & STAT_DEAD) && !IS_TRUSTED(victim))
      {
      dam_message(damage, ch, victim, attackType);
      }
   else
      {
      for(i = 0; i < MAX_MESSAGES; i++)
         {
         if(fight_messages[i].a_type == attackType)
            {
            nr = dice(1, fight_messages[i].number_of_attacks);
            for(j = 1, messages = fight_messages[i].msg; (j < nr) && (messages); j++)
               messages = messages->next;

            if(IS_TRUSTED(victim))
               {
               if(ch != victim)
                  c_act(messages->god_msg.attacker_msg, FALSE, ch, weapon, victim, TO_CHAR, condense);
               c_act(messages->god_msg.victim_msg, FALSE, ch, weapon, victim, TO_VICT, condense);
               c_act(messages->god_msg.room_msg, FALSE, ch, weapon, victim, TO_NOTVICT, condense);
               }
            else if(damage != 0)
               {
               if(newPosition & STAT_DEAD)
                  {
                  if(ch != victim)
                     act(messages->die_msg.attacker_msg, FALSE, ch, weapon, victim, TO_CHAR);
                  act(messages->die_msg.victim_msg, FALSE, ch, weapon, victim, TO_VICT);
                  act(messages->die_msg.room_msg, FALSE, ch, weapon, victim, TO_NOTVICT);
                  }
               else
                  {
                  if(ch != victim)
                     c_act(messages->hit_msg.attacker_msg, FALSE, ch, weapon, victim, TO_CHAR, condense);
                  c_act(messages->hit_msg.victim_msg, FALSE, ch, weapon, victim, TO_VICT, condense);
                  c_act(messages->hit_msg.room_msg, FALSE, ch, weapon, victim, TO_NOTVICT, condense);
                  }
               }
            else
               {          /* damage == 0 */
               if(ch != victim)
                  c_act(messages->miss_msg.attacker_msg, FALSE, ch, weapon, victim, TO_CHAR, condense);
               c_act(messages->miss_msg.victim_msg, FALSE, ch, weapon, victim, TO_VICT, condense);
               if(attackType < TYPE_HIT)
                  c_act(messages->miss_msg.room_msg, FALSE, ch, weapon, victim, TO_NOTVICT, condense);
               }
            }
         }
      }

   // Nuke mirror images
   if(damage && IS_NPC(victim) && (victim->nr == real_mobile(203)))
      {
      c_act("&+cUpon being struck, &N$n&+c shatters into a million particles of &N&+ylight&N&+c!", TRUE,
            victim, 0, 0, TO_ROOM, SPELL_SPEC);
      extract_char(victim);
      return(damage);
      }

   // Nuke dopplegangers
   if(damage && IS_NPC(victim) && (victim->nr == real_mobile(204)))
      {
      c_act("&+LUpon being struck, &N$n&+L dissolves into a whirling mass of dark shadows.&N", TRUE,
            victim, 0, 0, TO_ROOM, SPELL_SPEC);
      extract_char(victim);
      return(damage);
      }

   // Vamp touch heal
   if((attackType == TYPE_HIT) &&
      (IS_AFFECTED(ch, AFF_VAMPIRIC_TOUCH) || (GET_RACE(ch) == RACE_VAMPIRE)) &&
      (GET_HIT(ch) < GET_MAX_HIT(ch)))
      {
      if((mob_index[ch->nr].virtual == 1259) || (mob_index[ch->nr].virtual == 1261))
         {
         GET_HIT(ch) += damage / 5;
         StartRegen(ch, EVENT_HIT_REGEN);
         }
      else
         {
         GET_HIT(ch) += damage / 2;
         StartRegen(ch, EVENT_HIT_REGEN);
         }
      }

   // Handle fireshield damage
   if(shieldType)
      {
      if(shieldType == SPELL_FIRESHIELD)
         {
         if((GET_RACE(ch) == RACE_F_ELEMENTAL) || (GET_RACE(ch) == RACE_EFFREETI))
            {
            act("$n revels in $N's fireshield.", FALSE, ch, 0, victim, TO_NOTVICT);
            act("You revel in $N's fireshield.", FALSE, ch, 0, victim, TO_CHAR);
            act("$n revels in your fireshield!", FALSE, ch, 0, victim, TO_VICT);
            GET_HIT(ch) = MIN(GET_HIT(ch) + shieldDam, GET_MAX_HIT(ch));
            }
         else if(Damage(victim, ch, shieldDam, shieldType))
            {
            update_pos(victim);
            }
         }
      else if(shieldType == SPELL_COLDSHIELD)
         {
         if((GET_RACE(ch) == RACE_F_ELEMENTAL) || (GET_RACE(ch) == RACE_EFFREETI))
            shieldDam *= 2;
         if(Damage(victim, ch, shieldDam, shieldType))
            update_pos(victim);
         }
      else if(shieldType == SPELL_UNHOLY_AURA)
         {
         if(Damage(victim, ch, shieldDam, shieldType))
            update_pos(victim);
         }
      else if(shieldType == SPELL_VAMPIRIC_CURSE && (!IS_AFFECTED(ch, AFF_BLACKMANTLE)))
         {
         act("$n's wounds seem to mend as $e hits you!", FALSE, ch, 0, victim, TO_VICT);
         act("Your wounds seem to heal slightly.", FALSE, ch, 0, victim, TO_CHAR);
         act("$n's wounds mend slightly as $e hits $N.", FALSE, ch, 0, victim, TO_NOTVICT);
         GET_HIT(ch) = MIN(GET_HIT(ch) + shieldDam, GET_MAX_HIT(ch));
         }
      }

   // Handle poisoned weapons
   if((damage != 0) && (GET_STAT(ch) != STAT_DEAD))
      {
      switch(attackType)
         {
         case TYPE_PIERCE:
         case TYPE_SLASH:
         case TYPE_CLAW:
         case TYPE_BITE:
         case TYPE_STING:
         case TYPE_SECONDARY_WEAPON:
         case TYPE_ARROW_MISSILE:
         case TYPE_WEAPON_MISSILE:
         case SKILL_CIRCLE:
         case SKILL_BACKSTAB:
         case SKILL_CIRCLE2:
         case SKILL_BACKSTAB2:
            if(poisonedWeapon(ch, victim, weapon))
               return(damage);
            break;
         default:
            break;
         }
      }

   // Duel Knockout
   if(duelKO)
      {
      DuelKnockOut(ch, victim);
      return FALSE;
      }

   // Death and dying messages
   if((newPosition & STAT_MASK) < STAT_SLEEPING)
      {
      switch(newPosition & STAT_MASK)
         {
         case STAT_DEAD:
            act("&+r$n &N&+ris dead! R.I.P.&n", TRUE, victim, 0, 0, TO_ROOM);
            act("&=rlWith a final blow, you feel yourself falling to the ground.&n",
                FALSE, victim, 0, 0, TO_CHAR);
            act("&=rlYour soul leaves your body in the cold sleep of death...&n",
                FALSE, victim, 0, 0, TO_CHAR);
            break;
         case STAT_DYING:
            act("$n is mortally wounded, and will die soon, if not aided.",
                TRUE, victim, 0, 0, TO_ROOM);
            act("&=RlYou are mortally wounded, and will die soon, if not aided.&n",
                FALSE, victim, 0, 0, TO_CHAR);
            break;
         case STAT_INCAP:
            act("$n is incapacitated and will slowly die, if not aided.",
                TRUE, victim, 0, 0, TO_ROOM);
            act("&=rlYou are incapacitated an will slowly die, if not aided.&n",
                FALSE, victim, 0, 0, TO_CHAR);
            break;
         }
      }

   update_pos(victim);

   // Fix later
#if 0
   // Certain nasty hits can stun..god this is ugly
   if(GET_STAT(victim) > STAT_INCAP)
      {
      if(GET_HIT(victim) < 2)
         {
         if(number(-1, 2) > GET_HIT(victim))
            Stun(victim, number(1, 20) - GET_HIT(victim));
         }
      else if(!STUN_PROOF(victim) && (ch != victim) && (damage < GET_HIT(victim) + 3))
         {
         if((number(0, damage * ((attackType == TYPE_HIT) ||
                                 (attackType == TYPE_CRUSH) || attackType == TYPE_BLUDGEON)) ? 3 : 1) >
            number(1, GET_HIT(victim))) &&
            !StatSave(victim, APPLY_CON, 0))
               {
               c_act("Your blow stuns $N!", FALSE, ch, 0, victim, TO_CHAR, MELEE_SPEC);
               Stun(victim, PULSE_VIOLENCE);
               }
         }
      }
#endif

   // Justice hook
   if((newPosition & STAT_MASK) == STAT_DEAD)
      justice_witness(ch, victim, CRIME_MURDER);

   // Set new position
   SET_POS(victim, newPosition);

   // Start regenerating
   if(GET_STAT(victim) != STAT_DEAD)
      StartRegen(victim, EVENT_HIT_REGEN);

   // Warning messages
   if(GET_STAT(victim) != STAT_DEAD && damage > 0)
      {
      max_hit = hit_limit(victim);
      if((damage > GET_HIT(victim)) && (ch != victim))
         c_act("&=LCYIKES!&n  Another hit like that, and you've had it!!",
               FALSE, victim, 0, 0, TO_CHAR, MELEE_SPEC);
      else if((damage > (max_hit / 5)) && (ch != victim))
         c_act("&+MOUCH!&n  That really did &+MHURT!&n",
               FALSE, victim, 0, 0, TO_CHAR, MELEE_SPEC);
      if((GET_HIT(victim) < (max_hit / 8)) && (ch != victim))
         c_act("You wish that your wounds would stop &=LRBLEEDING&N so much!\n",
               FALSE, victim, 0, 0, TO_CHAR, MELEE_SPEC);
      }

   // Handle wimpy flee
   if((GET_STAT(victim) != STAT_DEAD) && AWAKE(victim) && (ch != victim) &&
      CAN_ACT(victim) && !IS_STUNNED(victim) && (orig_attackType != SKILL_BACKSTAB))
      {
      if((IS_NPC(victim) && !IS_MORPH(victim) && IS_CSET(victim->only.npc->npcact, ACT_WIMPY) &&
          (GET_HIT(victim) < (GET_LEVEL(victim) * 4))) ||
         ((IS_PC(victim) || IS_MORPH(victim)) &&
          (( GET_WIMPY(GET_PLYR(victim))) && (GET_HIT(victim) < GET_WIMPY(GET_PLYR(victim))))) ||
         affected_by_spell(victim, SONG_COWARDICE))
         {

         do_flee(victim, 0, 1);

         // Return if victim fleed successfully
         if(!victim || (GET_STAT(victim) == STAT_DEAD) || (victim->in_room != ch->in_room))
            return(damage);
         }
      }


   //  if (IS_NPC(victim)) /* Mounts will flee when hit */
   //    if (victim && (GET_STAT(victim) != STAT_DEAD))
   //      if (IS_CSET(victim->only.npc->npcact, ACT_MOUNT))
   //        do_flee(victim, 0, 1);
   // Mounts fleeing proved unnecesary, commented out - KPZ 5/18/2001


   // If un-vicious, stop whacking on that poor victim
   if(!AWAKE(victim) || (GET_HIT(victim) < -2) ||
      IS_AFFECTED(victim, AFF_MINOR_PARALYSIS) || IS_AFFECTED(victim, AFF_MAJOR_PARALYSIS))
      {
      if(IS_FIGHTING(victim))
         StopCombat(victim);
      StopMercifulAttackers(victim);
      }

   // Handle a bunch of crap for victims that have been, well...finished.
   if(GET_STAT(victim) == STAT_DEAD)
      {

      // Return gods in now-dead switched bodies
      if(victim->desc && victim->desc->original && victim->desc->original->only.pc->switched)
         {
         do_return(victim, 0, -4);
         }
      if(IS_PC(victim) && IN_ACHERON(ch) && (!IS_DEAD(ch)))
         GET_PRESTIGE(ch) += cc_prestige_fragBonus;

      // Handle exp gain
      if((IS_NPC(victim) || victim->desc) && (ch != victim) && IS_NPC(victim))
         {
         if(GET_GROUP(ch))
            group_gain(ch, victim);
         else if(!IS_DEAD(ch))
            {
            send_to_char("You receive your share of experience.\n", ch);
            if(IS_PC(ch) || IS_MORPH(ch))
               diff = EXP_TABLE(GET_PLYR(ch), 1) - EXP_TABLE(GET_PLYR(ch), 0);
            else
               diff = GET_EXP(ch) / 25;
            XP = GET_EXP(victim) * exp_mod(ch, victim);
            XP = XP / (100 * align_mod(ch, victim));
            if(IS_EVIL(victim) && (GET_CLASS(GET_PLYR(ch)) == CLASS_PALADIN))
               XP = (int)(XP * 1.25);

            XP = Trophy_Mod(ch, victim, XP);
            if(IS_PC(ch) && IS_NPC(victim))
               GET_PRESTIGE(ch) += MAX(0, GET_PRESTIGE_BONUS(victim));
            gain_exp(ch, XP);
            change_alignment(ch, victim);
            if(XP && IS_PC(ch))
               logit(LOG_EXP, "%s: %d(%d), killed: %s", GET_NAME(ch),
                     XP, BOUNDED((diff / -4), XP, (diff / 4)), GET_NAME(victim));
            }
         }

      if(IS_PC(victim))
         {
         logit(LOG_DEATH, "%s killed by %s at %s [%d]", GET_NAME(victim),
               (IS_NPC(ch) ? ch->player.short_descr : GET_NAME(ch)),
               world[victim->in_room].name, world[victim->in_room].number);

         if(IN_ACHERON(victim) && (!IN_QUEST_PK(victim)))
            {
            char gbuf[MAX_STRING_LENGTH];
            sprintf(gbuf, "&+b[&+BAcheron&N&+b] &+L%s &N &+Rkilled by&N &+L%s.&N\n", GET_NAME(victim), GET_NAME(ch));
            acheron_channel_broadcast(victim, gbuf);
            }

         // Justice handling - remove all records for a dead pc, etc
         if(ch->specials.arrest_by && (ch->specials.arrest_by == victim))
            {
            statuslog(victim->player.level, "%s killed by %s (&+C%s justice&N) at "
                      "[%d] %s", GET_NAME(victim), C_NAME(ch),
                      town_name_list[CHAR_IN_TOWN(ch)], world[victim->in_room].number,
                      world[victim->in_room].name);

            crec = crime_find(hometowns[CHAR_IN_TOWN(victim)-1].crime_list, C_NAME(victim), NULL, CRIME_NONE, NOWHERE, J_STATUS_NONE, NULL);
            while(crec)
               {
               crime_remove(CHAR_IN_TOWN(victim), crec);
               crec = crime_find(hometowns[CHAR_IN_TOWN(victim)-1].crime_list, C_NAME(victim), NULL, CRIME_NONE, NOWHERE, J_STATUS_NONE, NULL);
               }
            }
         else if(!IS_PC(ch))
            {
            statuslog(victim->player.level, "%s killed by %s at [%d] %s",
                      GET_NAME(victim), (IS_NPC(ch) ? ch->player.short_descr : GET_NAME(ch)),
                      world[victim->in_room].number, world[victim->in_room].name);
            }
         else if(victim == ch)
            {
            statuslog(victim->player.level, "%s killed by %s at [%d] %s",
                      GET_NAME(victim), GET_NAME(ch),
                      world[victim->in_room].number, world[victim->in_room].name);
            }
         }

      // Omigod, they killed victim!  YOU BASTARDS!
      die(victim);
      victim = NULL;

      // Retarget mobs who just got lucky.
      if(IS_NPC(ch) && !IS_MORPH(ch) && CAN_ACT(ch) && MIN_POS(ch, POS_STANDING + STAT_RESTING))
         AddEvent(EVENT_CHAR_EXECUTE, PULSE_VIOLENCE - 1, TRUE, ch, ReTarget);

      return(damage);
      }

   // Akatackakataa...er..hit back!
   attack_back(ch, victim);

   // F    I     N
   return(damage);

}

/*******************************************************************/
/*                 UTILITY COMBAT FUNCTIONS                        */
/*******************************************************************/

bool InitialCombatCheck(P_char ch, P_char victim)
{
   // The calling function should verify that we have valid P_chars willing and able
   // to engage in combat - now we just need to check all the little details, and nuke
   // affects where appropriate

   // Player pets can't attack other players
   if(IS_AFFECTED(ch, AFF_CHARM) && ch->following && IS_PC(ch->following) && IS_PC(victim) &&
      (!IS_FIGHTING(victim) || (victim->specials.fighting != ch)))
      return FALSE;

   /*** Everything after here assumes that combat can happen ***/


   // Wack massmorph if set -- CRM
   if(IS_MASSMORPH(ch))
      {
      act("&+mThe illusion over $n&+m's group is shattered!",
          TRUE, GET_GROUP_LEADER(GET_GROUP(ch)), 0, 0, TO_ROOM);
      send_to_char("&+mThe illusion covering your group is shattered!&N\n",
                   GET_GROUP_LEADER(GET_GROUP(ch)));
      REMOVE_BIT((GET_GROUP(ch))->flags, GAFF_MASSMORPH);
      }

   // Wack massmorph if set on victim (just in case) -- CRM
   if(IS_MASSMORPH(victim))
      {
      act("&+mThe illusion over $n&+m's group is shattered!",
          TRUE, GET_GROUP_LEADER(GET_GROUP(victim)), 0, 0, TO_ROOM);
      send_to_char("&+mThe illusion covering your group is shattered!&N\n",
                   GET_GROUP_LEADER(GET_GROUP(victim)));
      REMOVE_BIT((GET_GROUP(victim))->flags, GAFF_MASSMORPH);
      }

   // Wake up, gomer!
   if(GET_STAT(ch) == STAT_SLEEPING)
      {
      send_to_char("You are VERY rudely awakened!\n", ch);
      act("$n has RUDE awakening!", TRUE, ch, 0, 0, TO_ROOM);
      SET_POS(ch, POS_SITTING + GET_STAT(ch));
      }


   // And get off that damn horse..
   if(IS_RIDING(ch) && IS_PC(ch))
      if(((GET_CHAR_SKILL(ch, SKILL_MOUNTED_COMBAT)< 75) &&
          (ch->equipment[WIELD] && IS_SET(ch->equipment[WIELD]->extra_flags, ITEM_TWOHANDS))) ||
         (!GET_CHAR_SKILL(ch, SKILL_MOUNTED_COMBAT)))
         {
         send_to_char("I'm afraid you aren't quite up to mounted combat.\n", ch);
         act("$n quickly slides off $N's back.", TRUE, ch, 0, GET_MOUNT(ch), TO_NOTVICT);
         stop_riding(ch);
         }

      // Nuke non-combat Song affects

   if(affected_by_spell(ch, SONG_CHARMING) &&
      ((ch->following && (ch->following == victim)) ||
       (victim->following && (victim->following == ch))))
      affect_from_char(ch, SONG_CHARMING);

#ifndef NEW_BARD
   if(affected_by_spell(ch, SONG_SLEEP))
      affect_from_char(ch, SONG_SLEEP);
#endif

   // Nuke non-combat Spell affects

   if(affected_by_spell(ch, SPELL_SLEEP))
      affect_from_char(ch, SPELL_SLEEP);

   if(IS_AFFECTED(ch, AFF_SLEEP))
      REMOVE_CBIT(ch->specials.affects, AFF_SLEEP);

   // Nuke non-combat Skill affects

   if(IS_AFFECTED(ch, AFF_SNEAK))
      {
      if(affected_by_spell(ch, SKILL_SNEAK))
         affect_from_char(ch, SKILL_SNEAK);
      if(IS_AFFECTED(ch, AFF_SNEAK))
         REMOVE_CBIT(ch->specials.affects, AFF_SNEAK);
      }

   if(IS_AFFECTED(ch, AFF_HIDE))
      {
      act("$n comes out of hiding!", TRUE, ch, 0, 0, TO_ROOM);
      REMOVE_CBIT(ch->specials.affects, AFF_HIDE);
      }

   if(IS_AFFECTED(victim, AFF_MEDITATE))
      {
      act("$n is disrupted from meditation.", TRUE, victim, 0, 0, TO_ROOM);
      REMOVE_CBIT(victim->specials.affects, AFF_MEDITATE);
      }

   if(IS_AFFECTED(victim, AFF_CHARGING))
      {
      act("$n you stop charing your psp crystal.", TRUE, victim, 0, 0, TO_ROOM);
      REMOVE_CBIT(victim->specials.affects, AFF_CHARGING);
      nukeAllPSPCrystalsExcept(victim, NULL, FALSE);
      }

   // Stop Memorizing
   stop_memorizing(ch);
   stop_memorizing(victim);

   // Disguise Check - remove disguise when ch initiates attack
   if(IS_DISGUISE_NPC(ch))
      disguiseEvent(ch, TRUE);

   // Dopplegangers can't initiate attacks
   if(IS_NPC(ch) && mob_index[ch->nr].virtual == 204)
      return FALSE;

   return(TRUE);
}

bool CombatCheck(P_char ch, P_char victim)
{
   struct affected_type *af;

   // Some basic validity checks on ch and victim first

   if(!SanityCheck(ch, "CombatCheck") || !SanityCheck(victim, "CombatCheck"))
      return FALSE;

   if(!IS_FIGHTING(ch))
      {
      logit(LOG_DEBUG, "CombatCheck(): %s not in combat with %s.", GET_NAME(ch), GET_NAME(victim));
      return FALSE;
      }

   if(ch->in_room != victim->in_room)
      {
      StopCombat(ch);
      StopCombat(victim);
      logit(LOG_DEBUG, "%s and %s not in same room while fighting.", GET_NAME(ch), GET_NAME(victim));
      return FALSE;
      }

   /*** Everything after here assumes that ch & victim are valid ***/

   // Wack massmorph if set -- CRM
   if(IS_MASSMORPH(ch))
      {
      act("&+mThe illusion over $n&+m's group is shattered!",
          TRUE, GET_GROUP_LEADER(GET_GROUP(ch)), 0, 0, TO_ROOM);
      send_to_char("&+mThe illusion covering your group is shattered!&N\n",
                   GET_GROUP_LEADER(GET_GROUP(ch)));
      REMOVE_BIT((GET_GROUP(ch))->flags, GAFF_MASSMORPH);
      }

   // Wack massmorph if set on victim (just in case) -- CRM
   if(IS_MASSMORPH(victim))
      {
      act("&+mThe illusion over $n&+m's group is shattered!",
          TRUE, GET_GROUP_LEADER(GET_GROUP(victim)), 0, 0, TO_ROOM);
      send_to_char("&+mThe illusion covering your group is shattered!&N\n",
                   GET_GROUP_LEADER(GET_GROUP(victim)));
      REMOVE_BIT((GET_GROUP(victim))->flags, GAFF_MASSMORPH);
      }

   // Garroting check
   /* Changing this to also call garrote_event */
   /* Previously an event existed that did same */
   /* once per round... unnecessary and crashing - Iyachtu */
   if(IS_CSET(ch->specials.affects, AFF_GARROTING))
      {
      garrote_event(ch);
      return FALSE;
      }

   // Paralysis check
   if(IS_AFFECTED(ch, AFF_MAJOR_PARALYSIS))
      {
      act("You remain paralyzed and can't do a thing to defend yourself...",
          FALSE, ch, 0, 0, TO_CHAR);
      act("$n strains to respond to $N's attack, but the paralysis is too overpowering.",
          FALSE, ch, 0, victim, TO_ROOM);
      return FALSE;
      }

   // Minor Paralysis check
   if(IS_AFFECTED(ch, AFF_MINOR_PARALYSIS))
      {
      act("You couldn't budge a feather in your present condition.",
          FALSE, ch, 0, 0, TO_CHAR);
      act("$n is too preoccupied with $s nervous system problem to fight.",
          FALSE, ch, 0, 0, TO_ROOM);
      return FALSE;
      }

   // Remove Minor Paralysis
   if(IS_AFFECTED(victim, AFF_MINOR_PARALYSIS))
      {
      act("$n's crushing blow frees $N from his magical paralysis!", FALSE, ch, 0, victim, TO_ROOM);
      act("$n's blow shatters the magic paralyzing you!", FALSE, ch, 0, victim, TO_VICT);
      act("Your blow disrupts the magic keeping $N frozen.", FALSE, ch, 0, victim, TO_CHAR);

      for(af = victim->affected; af; af = af->next)
         if(af->type == SPELL_MINOR_PARALYSIS)
            {
            affect_remove(victim, af);
            break;
            }
      REMOVE_CBIT(victim->specials.affects, AFF_MINOR_PARALYSIS);
      }


   // Disarm recovery check
   if(DisarmRecovery(ch) == FALSE)
      return FALSE;

   // Casting Check
   if(IS_AFFECTED(ch, AFF_CASTING))
      return FALSE;

   // If we got this far, combat can continue
   return TRUE;

}

bool elemental_proc(P_char ch, P_char victim)
{
   char Gbuf1[MAX_STRING_LENGTH], Gbuf2[MAX_STRING_LENGTH], Gbuf3[MAX_STRING_LENGTH];
   int elemental_type, dam;
   const char *damage_message[] = {
      "&+rFlames &+Rripple&N",
      "&+cFrost &+Cstreams&N",
      "&+ySparks &+Yfly&N",
      "&+mGas &+Mpours forth&N",
      "&+gAcid &+Gspatters&N"
   };
   const int elemental_protection[] = {
      AFF_PROT_FIRE,
      AFF_PROT_COLD,
      AFF_PROT_LIGHTNING,
      AFF_PROT_GAS,
      AFF_PROT_ACID
   };

   if(!ch || !victim)
      return TRUE;

   if(IS_DEAD(ch) || IS_DEAD(victim))
      return TRUE;

   if((ch->in_room == -1) || (victim->in_room == -1))
      return TRUE;

   if(!affected_by_song(ch, SONG_OF_THE_ELEMENTS))
      return TRUE;

   elemental_type = number(0,4);

   dam = (ch->bard_singing->song_singing->modifier);

   if(IS_AFFECTED(victim, elemental_protection[elemental_type]))
      dam -= (dam / 5);

   sprintf(Gbuf1, "%s as you strike %s!", damage_message[elemental_type], victim->player.short_descr);
   sprintf(Gbuf2, "%s as %s strikes %s!", damage_message[elemental_type], GET_NAME(ch), victim->player.short_descr);
   sprintf(Gbuf3, "%s as %s strikes you!", damage_message[elemental_type], GET_NAME(ch));

   act(Gbuf1, FALSE, ch, 0, victim, TO_CHAR);
   act(Gbuf2, FALSE, ch, 0, victim, TO_NOTVICT);
   act(Gbuf3, FALSE, ch, 0, victim, TO_VICT);

   debuglog(51, DS_IYACHTU, "doing %d damage", dam);

   if(CombatDamage(ch, victim, dam, 0))
      return TRUE;

   return FALSE;

}

int CheckCombatProcs(P_char ch, P_char victim, P_obj weapon, int hitType, int damage, int origAttackType)
{
   struct func_attachment *fn;

   // Check weapon procs first

   // Weapon crit
   if(weapon && (hitType == ATTACK_CRIT))
      {
      if(obj_index[weapon->R_num].spec_flag & IDX_WEAPON_CRIT)
         {
         fn = obj_index[weapon->R_num].func;
         do
            {
            if(fn->proc_flag & IDX_WEAPON_CRIT)
               if((*fn->func.obj) (weapon, ch, PROC_WEAPON_CRIT + damage, (char *) victim) ||
#if 0  /* this line prevented engaging at start of fight - Iyachtu */
                  !ch->specials.fighting ||
#endif
                  (ch->in_room != victim->in_room))
                  return TRUE;
            fn = fn->next;
            } while(fn);
         }
      }

   /* Adjusted logic here.  incomplete as preceding function cannot pass */
   /* SKILL_BACKSTAB, ever Iyachtu */
   /* Previously the logic didn't have an else if on the following line... and was */
   /* Nested with the hittype == ATTACK_HIT... so could never have it == SKILL_BACKSTAB */
   else if(weapon && hitType == ATTACK_HIT && (origAttackType == SKILL_BACKSTAB || origAttackType == SKILL_BACKSTAB2))
      {
      // Backstab hit
      if((obj_index[weapon->R_num].spec_flag & IDX_WEAPON_BS))
         {
         fn = obj_index[weapon->R_num].func;
         do
            {
            if(fn->proc_flag & IDX_WEAPON_BS)
               if((*fn->func.obj) (weapon, ch, PROC_WEAPON_BS + damage, (char *) victim) ||
#if 0  /* prevented engaging at start of fight - Iyachtu */
                  !ch->specials.fighting ||
#endif
                  (ch->in_room != victim->in_room))
                  return TRUE;
            fn = fn->next;
            } while(fn);
         }
      }
   // Weapon hit
   else if(weapon && (hitType == ATTACK_HIT))
      {
      if(affected_by_song(ch, SONG_OF_THE_ELEMENTS) && (!number(0, 15)) && elemental_proc(ch, victim))
         return TRUE;
      if(obj_index[weapon->R_num].spec_flag & IDX_WEAPON_HIT)
         {
         fn = obj_index[weapon->R_num].func;
         do
            {
            if(fn->proc_flag & IDX_WEAPON_HIT)
               if((*fn->func.obj) (weapon, ch, PROC_WEAPON_HIT + damage, (char *) victim) ||
                  (ch->in_room != victim->in_room))
                  return TRUE;
            fn = fn->next;
            } while(fn);
         }
      }

   // mob barehand crit

   else if(IS_NPC(ch) && hitType == ATTACK_CRIT)
      {
      if(mob_index[ch->nr].spec_flag & IDX_NPC_CRIT)
         {
         fn = mob_index[ch->nr].func;
         do
            {
            if(fn->proc_flag & IDX_NPC_CRIT)
               if((*fn->func.ch) (ch, victim, PROC_NPC_CRIT + damage, NULL) ||
                  !ch->specials.fighting || (ch->in_room != victim->in_room))
                  return TRUE;
            fn = fn->next;
            } while(fn);
         }
      }

   /* barehand NPC hit */

   else if(IS_NPC(ch) && hitType == ATTACK_HIT)
      {
      if(mob_index[ch->nr].spec_flag & IDX_NPC_HIT)
         {
         fn = mob_index[ch->nr].func;
         do
            {
            if(fn->proc_flag & IDX_NPC_HIT)
               if((*fn->func.ch) (ch, victim, PROC_NPC_HIT + damage, NULL) ||
                  !ch->specials.fighting || (ch->in_room != victim->in_room))
                  return TRUE;
            fn = fn->next;
            } while(fn);
         }
      }

   // NPC under attack!
   if(IS_NPC(victim) && (mob_index[victim->nr].spec_flag & IDX_NPC_WAS_HIT))
      {
      fn = mob_index[victim->nr].func;
      do
         {
         if(fn->proc_flag & IDX_NPC_WAS_HIT)
            if((*fn->func.ch) (victim, ch, PROC_NPC_WAS_HIT, NULL) ||
               /* !ch->specials.fighting || */ !victim->specials.fighting ||
               (ch->in_room != victim->in_room))
               return TRUE;
         fn = fn->next;
         } while(fn);
      }

   // If none of those checks resulted in a proc interrupting combat, return TRUE
   return FALSE;
}

int VitalStrike(P_char ch, P_char victim, int damage)
{

   int roll;

   /* This prevents dropping a dragon to its knees - Iyachtu */
   /* May want to add other races to this later */
   /* Moving this function over to the new CheckStun function */
#if 0
   if((GET_RACE(victim) == RACE_DRAGON) || (GET_RACE(victim) == RACE_DEMON) ||
      (GET_RACE(victim) == RACE_DEVIL) || (GET_RACE(victim) == RACE_ANGEL) ||
      (GET_RACE(victim) == RACE_GOLEM) || IMMATERIAL(victim))
#endif
      if(!CheckStun(victim))
         roll = number(1, 2);
      else
         roll = number(1, 3);

   switch(roll)
      {
      case 1:
      case 2:
         // Multiply normal crit damage by 50%
         damage = damage * 3 / 2;
         act("&+WBlood flies everywhere as you plunge your weapon deep "
             "into $N's side.&N", TRUE, ch, 0, victim, TO_CHAR);
         act("&+WYou gasp in agony as $n strikes at your side, severely "
             "wounding you!&N", TRUE, ch, 0, victim, TO_VICT);
         act("&+W$N gasps in agony as $n strikes at $S side, sending "
             "blood flying everywhere!&N", TRUE, ch, 0, victim, TO_NOTVICT);
         break;
      case 3:
         // Belly-wound.  Drop them to their knees for 2 rounds.
         SET_POS(victim, GET_STAT(victim) + POS_KNEELING);
         CharWait(victim, PULSE_VIOLENCE * 2);
         act("&+WYou strike at $N's belly, causing $M to double over in "
             "agony and drop to $S knees!&N", TRUE, ch, 0, victim, TO_CHAR);
         act("&+W$n strikes at your belly, wounding you severely! The pain "
             "is so great, you drop to your knees.&N", TRUE, ch, 0, victim, TO_VICT);
         act("&+W$n strikes at $N's belly, causing $M to double over in "
             "agony and drop to $S knees!&N", TRUE, ch, 0, victim, TO_NOTVICT);
         StopCasting(victim);
         break;
      }

   // Check skill gain
   CharSkillGainChance(ch, SKILL_VITAL_STRIKE, 8);

   // Fin - return damage
   return(damage);
}

void appear(P_char ch)
{
   if(!IS_AFFECTED(ch, AFF_INVISIBLE))
      return;

   if(affected_by_spell(ch, SPELL_INVISIBLE))
      affect_from_char(ch, SPELL_INVISIBLE);
   else if(IMMATERIAL(ch))
      return;  /* perm invisibility */

   REMOVE_CBIT(ch->specials.affects, AFF_INVISIBLE);

   act("$n snaps into visibility.", TRUE, ch, 0, 0, TO_ROOM);
   act("You snap into visibility.", FALSE, ch, 0, 0, TO_CHAR);

}

void load_messages(void)
{
   FILE *f1;
   char chk[MAX_STRING_LENGTH];
   int i, type;
   struct message_type *messages;

   if(!(f1 = fopen(MESS_FILE, "r")))
      {
      perror("read messages");
      dump_core();
      }
   for(i = 0; i < MAX_MESSAGES; i++)
      {
      fight_messages[i].a_type = -1;
      fight_messages[i].number_of_attacks = 0;
      fight_messages[i].msg = 0;
      }

   chk[0] = 0;
   fscanf(f1, " %s \n", chk);

   while(*chk == 'M')
      {
      fscanf(f1, " %d\n", &type);
      for(i = 0;
         (i < MAX_MESSAGES) && (fight_messages[i].a_type != type) && (fight_messages[i].a_type != -1);
         i++);
      if(i >= MAX_MESSAGES)
         {
         perror("Too many combat messages.");
         dump_core();
         }
      CREATE(messages, struct message_type, 1);

      fight_messages[i].number_of_attacks++;
      fight_messages[i].a_type = type;
      messages->next = fight_messages[i].msg;
      fight_messages[i].msg = messages;

      messages->die_msg.attacker_msg = fread_string(f1);
      messages->die_msg.victim_msg = fread_string(f1);
      messages->die_msg.room_msg = fread_string(f1);
      messages->miss_msg.attacker_msg = fread_string(f1);
      messages->miss_msg.victim_msg = fread_string(f1);
      messages->miss_msg.room_msg = fread_string(f1);
      messages->hit_msg.attacker_msg = fread_string(f1);
      messages->hit_msg.victim_msg = fread_string(f1);
      messages->hit_msg.room_msg = fread_string(f1);
      messages->god_msg.attacker_msg = fread_string(f1);
      messages->god_msg.victim_msg = fread_string(f1);
      messages->god_msg.room_msg = fread_string(f1);
      fscanf(f1, " %s \n", chk);
      }

   fclose(f1);
}

void update_pos(P_char ch)
{
   int pos, stat, tmp;

   if(!ch)
      {
      logit(LOG_EXIT, "assert: update_pos() - no char");
      dump_core();
      }

   /* if they are dead, update_pos is not going to fix the problem.  JAB */
   if(GET_STAT(ch) == STAT_DEAD)
      return;

   if((IS_FIGHTING(ch)) && (ch->specials.fighting) && (ch->specials.fighting->in_room))
      if(ch->in_room != ch->specials.fighting->in_room)
         stop_fighting(ch);

   if(IS_RIDING(ch))
      if(GET_MOUNT(ch)->in_room != ch->in_room)
         stop_riding(ch);

      /* defaults: no change.  JAB */

   stat = GET_STAT(ch);
   pos = GET_POS(ch);

   if(GET_HIT(ch) < -100)
      stat = STAT_DEAD;
   else if((GET_HIT(ch) < -10) && (!IS_AFFECTED(ch, AFF_DEATH_PACT)))
      stat = STAT_DEAD;
   else if(GET_HIT(ch) <= -6)
      stat = STAT_DYING;
   else if(GET_HIT(ch) <= -3)
      stat = STAT_INCAP;
   else if(((GET_STAT(ch) == STAT_SLEEPING) || (GET_STAT(ch) == STAT_RESTING)) &&
           (IS_FIGHTING(ch) || NumAttackers(ch)))
      stat = STAT_NORMAL;
   else if(GET_STAT(ch) < STAT_SLEEPING)
      stat = STAT_RESTING;
   else
      stat = GET_STAT(ch);        /* SLEEPING/RESTING/NORMAL */

   /* hehehe - here's a sample of the fun stuff we can do with new POS/STAT,
      if not awake, but not prone, they can fall over, with various results. */

   /* quardapeds are much more stable than bipeds, so they skip this little indignity. JAB */

   if(IS_HUMANOID(ch) && (stat < STAT_RESTING))
      {
      tmp = 0;
      switch(GET_POS(ch))
         {
         case POS_PRONE:
            /* nada */
            break;
         case POS_KNEELING:
            /* fairly stable, (except riding) but there is that chance... */
            switch(stat)
               {
               case STAT_DEAD:
                  if(IS_RIDING(ch))
                     tmp = 1;
                  else
                     {
                     if(!number(0, 9))
                        tmp = 1;
                     }
                  break;
               case STAT_DYING:
               case STAT_INCAP:
               case STAT_SLEEPING:
                  if(IS_RIDING(ch))
                     {
                     if(!number(0, 1))
                        tmp = 1;
                     }
                  else
                     {
                     if(!number(0, 9))
                        tmp = 1;
                     }
                  break;
               }
            break;
         case POS_SITTING:
            /* moderately stable, esp. if mounted, but not completely... */
            switch(stat)
               {
               case STAT_DEAD:
                  if(IS_RIDING(ch))
                     {
                     if(!number(0, 1))
                        tmp = 1;
                     }
                  else
                     {
                     if(!number(0, 9))
                        tmp = 1;
                     }
                  break;
               case STAT_DYING:
               case STAT_INCAP:
               case STAT_SLEEPING:
                  if(IS_RIDING(ch))
                     {
                     if(!number(0, 4))
                        tmp = 1;
                     }
                  else
                     {
                     if(!number(0, 7))
                        tmp = 1;
                     }
                  break;
               }
            break;
         case POS_STANDING:
            /* very unstable */
            switch(stat)
               {
               case STAT_DEAD:
                  /* they can die on their feet, but they aren't staying standing... */
                  tmp = 1;
                  break;
               case STAT_DYING:
               case STAT_INCAP:
               case STAT_SLEEPING:
                  if(IS_RIDING(ch))
                     tmp = 1;
                  else
                     {
                     if(!number(0, 5))
                        tmp = 1;
                     }
                  break;
               }
            break;
         }
      if(tmp)
         {
         tmp = 0;
         /* they involuntarily changed position, can be real bad if they were
            mounted at the time. (damage + stun).  JAB */
         if(IS_RIDING(ch))
            {
            tmp = 1;
            switch(GET_POS(GET_MOUNT(ch)))
               {
               case POS_PRONE:
               case POS_KNEELING:
               case POS_SITTING:
                  /* not far to fall, no damage, just stop riding */
                  break;
               case POS_STANDING:
                  /* ouchness */
                  Stun(ch, dice(3, 4) * 4);     /* 3 rounds max, avg, ~1.5 */
                  break;
               }
            act("$n falls off his mount!", TRUE, ch, 0, 0, TO_ROOM);
            stop_riding(ch);
            }
         else
            {
            switch(pos)
               {
               case POS_PRONE:
               case POS_KNEELING:
               case POS_SITTING:
                  /* no damage, just go prone. */
                  break;
               case POS_STANDING:
                  /* minor owie, maybe */
                  tmp = 0;
                  if(!number(0, 2))
                     {
                     if(GET_STAT(ch) > STAT_INCAP)
                        Stun(ch, dice(2, 3) * 4);         /* 1.5 rounds max, avg, ~1 */
                     tmp = 1;            /* will wake normal sleepers */
                     }
                  act("$n slumps to the ground.", TRUE, ch, 0, 0, TO_ROOM);
                  break;
               }
            }
         pos = POS_PRONE;

         /* since they can take damage in this routine, check again. JAB */
         if(GET_HIT(ch) < -100)
            stat = STAT_DEAD;
         else if((GET_HIT(ch) < -10) && (!IS_AFFECTED(ch, AFF_DEATH_PACT)))
            stat = STAT_DEAD;
         else if(GET_HIT(ch) <= -6)
            stat = STAT_DYING;
         else if(GET_HIT(ch) <= -3)
            stat = STAT_INCAP;
         else if(((GET_STAT(ch) == STAT_SLEEPING) || (GET_STAT(ch) == STAT_RESTING)) &&
                 (IS_FIGHTING(ch) || NumAttackers(ch)))
            stat = STAT_NORMAL;
         else if(GET_STAT(ch) < STAT_SLEEPING)
            stat = STAT_RESTING;
         else
            stat = GET_STAT(ch);    /* SLEEPING/RESTING/NORMAL */

         /* if they are just normally asleep, falling will wake them (mostly),
            if they were magically asleep, taking damage from falling down will
            break the spell.  If they wind up worse than sleeping, ah well... */

         if(tmp && (stat < STAT_RESTING))
            {
            if(affected_by_spell(ch, SPELL_SLEEP))
               affect_from_char(ch, SPELL_SLEEP);
            if(stat == STAT_SLEEPING)
               {
               stat = STAT_NORMAL;
               send_to_char("Huh?  What!?  You find yourself laying on the ground!\n", ch);
               }
            }
         }
      }

   if((GET_STAT(ch) == STAT_SLEEPING) && (stat > STAT_SLEEPING))
      {
      act("$n has a RUDE awakening!", TRUE, ch, 0, 0, TO_ROOM);
      affect_from_char(ch, SPELL_SLEEP);
      do_wake(ch, 0, -4);
      }

   /* finally, set new position and status */

   SET_POS(ch, pos + stat);

   if(stat == STAT_DEAD)
      {
      if(IS_FIGHTING(ch))
         stop_fighting(ch);
      StopAllAttackers(ch);
      stat = STAT_DYING;          /* reason being, killing people in update_pos, would
                                     cause horrible logistic nightmares.  If they are
                                     dead, setting them dying will handle most cases.
                                     If THIS causes problems, may have to bite the
                                     bullet and change the code in LOTS of places.
                                     Hopefully this will suffice.  JAB */
      }

   if((stat != STAT_RESTING) || ((pos != POS_SITTING) && (pos != POS_KNEELING)))
      stop_memorizing(ch);

   if(!AWAKE(ch) || IS_AFFECTED(ch, AFF_MINOR_PARALYSIS) || IS_AFFECTED(ch, AFF_MAJOR_PARALYSIS) ||
      IS_AFFECTED(ch, AFF_KNOCKED_OUT) || (GET_STAT(ch) < STAT_SLEEPING))
      {
      if(IS_FIGHTING(ch))
         stop_fighting(ch);
      StopMercifulAttackers(ch);
      }
   /* final check for mobs, if they can assume their default position */
   /* added check - DTS 7/11/95 */
   if(IS_NPC(ch) && (stat > STAT_SLEEPING) && !IS_FIGHTING(ch) && CAN_ACT(ch) && (ch->only.npc) &&
      ((ch->only.npc->default_pos & STAT_MASK) >= STAT_SLEEPING) && (!HAS_MEMORY(ch) || !GET_MEMORY(ch)))
      ch->specials.position = ch->only.npc->default_pos;
}

bool AdjacentInRoom(P_char ch, P_char ch2)
{
   P_char t_ch, t_ch2;

   if(!ch || !ch2 || (ch->in_room != ch2->in_room) || (ch->in_room == NOWHERE))
      return FALSE;

   if(IS_TRUSTED(ch) || IS_TRUSTED(ch2) || IMMATERIAL(ch) || IMMATERIAL(ch2))
      return TRUE;

   /* find first of ch/ch2 in room list */

   for(t_ch = world[ch->in_room].people; t_ch && (t_ch != ch) && (t_ch != ch2); t_ch = t_ch->next_in_room);

   if(!t_ch)
      return FALSE;

   /* find the second of ch/ch2 in room list, skipping immorts */

   for(t_ch2 = t_ch->next_in_room;
      t_ch2 && (IS_TRUSTED(t_ch2) || IMMATERIAL(t_ch2));
      t_ch2 = t_ch2->next_in_room);

   if(!t_ch2)
      return FALSE;

   if(((t_ch == ch) && (t_ch2 == ch2)) || ((t_ch2 == ch) && (t_ch == ch2)))
      return TRUE;

   return FALSE;
}

void make_corpse(P_char ch)
{
   P_obj corpse, o, money;
   char buf[MAX_STRING_LENGTH];
   int i, e_time;

   corpse = read_object(2, VIRTUAL);
   if(!corpse)
      dump_core();

   corpse->str_mask = (STRUNG_KEYS | STRUNG_DESC1 | STRUNG_DESC2 | STRUNG_DESC3);

   /* "corpse" keyword removed for PCs - DTS 7/11/95 */
   sprintf(buf, "%s %s", IS_PC(ch) ? "pcorpse" : "corpse npcorpse", GET_NAME(ch));

   /* Auto-add "undead" to undead corpses, to prevent animating them.. */
   if(IS_UNDEAD(ch))
      strcat(buf, " undead");

   /* Add "dragon" to dragon corpses, same reason */
   if(GET_RACE(ch) == RACE_DRAGON || GET_RACE(ch) == RACE_DRAGONKIN)
      strcat(buf, " dragon666");

   /* Add "SoulBind" to corpses that have been bound */
   if(IS_AFFECTED(ch, AFF_SOUL_BIND))
      strcat(buf, " SoulBind");

   corpse->name = str_dup(buf);

   if(IS_PC(ch))
      sprintf(buf, "Corpse of %s is lying here.", IS_PC(ch) ? GET_NAME(ch) : ch->player.short_descr);
   else if(IS_AFFECTED(ch, AFF_SOUL_BIND))
      sprintf(buf, "Corpse of %s is &N&+cshimmering&N here.", IS_PC(ch) ? GET_NAME(ch) : ch->player.short_descr);
   else
      sprintf(buf, "Corpse of %s is lying here.", IS_PC(ch) ? GET_NAME(ch) : ch->player.short_descr);

   corpse->description = str_dup(buf);

   sprintf(buf, "corpse of %s", IS_NPC(ch) ? ch->player.short_descr : GET_NAME(ch));
   corpse->short_description = str_dup(buf);

   /* timestamp and name stat and animate dead/resurrect */
   sprintf(buf, "%d %s", (int)time(0), IS_NPC(ch) ? ch->player.short_descr : GET_NAME(ch));
   corpse->action_description = str_dup(buf);

   /* changed this, add everything to ch's inven before xferring to corpse,
      this makes the corpse weight correct.  (also simplifies things.) */

   if(GET_MONEY(ch) > 0)
      {
      /* make a 'pile of coins' object to hold ch's cash */

      money = create_money(GET_COPPER(ch), GET_SILVER(ch), GET_GOLD(ch), GET_PLATINUM(ch));
      SUB_MONEY(ch, GET_MONEY(ch), 0);
      obj_to_char(money, ch);
      }
   for(i = 0; i < MAX_WEAR; i++)
      if(ch->equipment[i])
         obj_to_char(unequip_char(ch, i, TRUE), ch);

      /* have to change the 'loc.carrying' pointers to 'loc.inside' pointers for
         the whole object list, else ugly problems occur later. */

   corpse->contains = ch->carrying;

   for(o = corpse->contains; o; o = o->next_content)
      {
      o->loc_p = LOC_INSIDE;
      o->loc.inside = corpse;
      }

   ch->carrying = 0;

   // If this is a PC, and they lost a level, we want to store it for ress..
   if(IS_PC(ch))
      {
      if(((((GET_EXP(ch) - EXP_TABLE(ch, 0)) * 4 / 3) + EXP_TABLE(ch, 0)) < EXP_TABLE(ch, 1)) ||
         (GET_LEVEL(ch) == MAXLVLMORTAL))
         corpse->value[2] = GET_LEVEL(ch);
      else
         corpse->value[2] = (GET_LEVEL(ch) + 1);
      }
   else
      {
      corpse->value[2] = GET_LEVEL(ch);
      }


   if(IS_NPC(ch))
      {
      e_time = MAX_NPC_CORPSE_TIME;
      corpse->weight = IS_CARRYING_W(ch) * 2;
      corpse->value[0] = IS_CARRYING_W(ch);
      corpse->value[1] = NPC_CORPSE;
      corpse->value[3] = mob_index[ch->nr].virtual;
      }
   else
      {
      e_time = MAX_PC_UNLOOTED_CORPSE_TIME;
      corpse->weight = GET_WEIGHT(ch) + IS_CARRYING_W(ch);
      corpse->value[0] = IS_CARRYING_W(ch);       /* contains */
      corpse->value[1] = PC_CORPSE;
      corpse->value[4] = 1; /* Set unlooted flag for PC corpses. getting anything from
                             * the corpse or spirit walking will reset it */
      corpse->value[3] = -1;
      }
   IS_CARRYING_N(ch) = 0;
   GET_CARRYING_W(ch) = 0;

   AddEvent(EVENT_DECAY, e_time, TRUE, corpse, 0);

   if(ch->in_room == NOWHERE)
      {
      if(ch->specials.was_in_room != NOWHERE)
         obj_to_room(corpse, ch->specials.was_in_room);
      else
         {
         extract_obj(corpse);      /* no place sane to put it */
         corpse = NULL;
         }
      }
   else
      {
      obj_to_room(corpse, ch->in_room);
      if(IS_SET(corpse->wear_flags, ITEM_TAKE))
         REMOVE_BIT(corpse->wear_flags, ITEM_TAKE);
      }

   /* added by DTS 8/1/95 - ghosts and wraiths shouldn't leave corpses...
      just dump contents of corpse to room and extract corpse */
   if(GET_RACE(ch) == RACE_GHOST)
      {
      if(corpse->contains)
         {
         while(corpse->contains)
            {
            o = corpse->contains;
            obj_from_obj(o);
            if(ch->in_room != NOWHERE)
               {
               obj_to_room(o, ch->in_room);
               }
            else if(ch->specials.was_in_room != NOWHERE)
               {
               obj_to_room(o, ch->specials.was_in_room);
               }
            else
               {                /* no place to put it */
               extract_obj(o);
               }
            }
         }
      obj_from_room(corpse);
      extract_obj(corpse);
      corpse = NULL;
      }
   if(corpse && IS_PC(ch))
      writeCorpse(corpse);  /* the ONLY case where writeCorpse is called directly. JAB */
}

P_event corpseGetDecayEvent(P_obj corpse)
{
   P_event e1;

   FIND_EVENT_TYPE(e1, EVENT_DECAY)
   if(corpse == (P_obj) e1->actor.a_obj)
      break;

   /* will return NULL if no decay event found.. - Alth */

   return e1;
}

/* Returns corpse decay time. 0 is a valid timer value so a return of -1 will
 * specify no decay event found */
long corpseGetDecayTime(P_obj corpse)
{
   P_event e1;

   e1 = corpseGetDecayEvent(corpse);
   if(!e1)
      return -1;

   return e1->timer;
}

/* Set corpse decay time in _minutes_ */
int corpseSetDecayTime(P_obj corpse, unsigned long decayTime)
{
   P_event e1;

   e1 = corpseGetDecayEvent(corpse);
   if(!e1)
      return 0;  /* return if no event found. do not attempt to add one
                    since the corpse might have been perma pressed on purpose */

   e1->timer = decayTime;

   return 1;
}

/* For crit hit bloodstains...ewwww - CRM */

void make_bloodstain(P_char ch)
{
   P_obj blood, obj, next_obj;
   char buf[MAX_STRING_LENGTH];
   const char *long_desc[] =
   {
      "&+rSplattered droplets of blood cover the area.",
      "&+rA few scattered blood droplets cover the area.",
      "&+rA tiny pool of blood lays here.&N",
      "&+rA single bloody smear mars the ground.&N",
      "&+rA bloody chunk of flesh lays here.&N",
      "&+rHuge puddles of blood cover the ground.&n",
      "&+rA pool of blood covers the ground.&N",
      "&+rA thick pool of sticky blood covers the ground.&N",
      "&+rA pile of unidentifiable blood and organs rests here.&N",
      "&+rBlood is smeared all over the area.&N",
      "&+rBlood splotches cover everything in the area.&n"
   };

   if(TERRAIN_WATER(ch->in_room) || TERRAIN_NEEDFLY(ch->in_room) ||
      TERRAIN_UNDERWATER(ch->in_room))
      return;

   if(ch->specials.fighting &&
      (IS_UNDEAD(ch->specials.fighting) ||
       (!IS_ANIMAL(ch->specials.fighting) && !IS_HUMANOID(ch->specials.fighting)) ||
       IMMATERIAL(ch->specials.fighting)))
      return;

   if(world[ch->in_room].contents)
      for(obj = world[ch->in_room].contents; obj; obj = next_obj)
         {
         next_obj = obj->next_content;
         if(obj->R_num == real_object(4))
            return;
         }

   blood = read_object(4, VIRTUAL);

   if(!blood)
      return;

   blood->str_mask = (STRUNG_DESC1);

   sprintf(buf, long_desc[number(0, 10)]);
   blood->description = str_dup(buf);

   AddEvent(EVENT_DECAY, 400, TRUE, blood, 0);

   if(ch->in_room == NOWHERE)
      {
      if(real_room(ch->specials.was_in_room) != NOWHERE)
         obj_to_room(blood, real_room(ch->specials.was_in_room));
      else
         {
         extract_obj(blood);
         blood = NULL;
         }
      }
   else
      obj_to_room(blood, ch->in_room);

}

/* When ch kills victim */

void change_alignment(P_char ch, P_char victim)
{
   P_obj obj;
   int i, a_al, v_al, change = 0;

   /* Switch to real PC if shapechanged */
   ch = GET_PLYR(ch);

   if(CHAR_IN_ARENA(ch))
      return;

   a_al = GET_ALIGNMENT(ch);
   v_al = GET_ALIGNMENT(victim);

   /* global modifiers */

   if(IS_NPC(victim))
      {
      if(IS_CSET(victim->only.npc->npcact, ACT_AGGRESSIVE))
         v_al -= 100;

      if(IS_CSET(victim->only.npc->npcact, ACT_AGGRESSIVE_GOOD))
         {
         if(IS_GOOD(ch))
            v_al -= 125;
         else
            v_al -= 50;
         }

      if(IS_CSET(victim->only.npc->npcact, ACT_AGGRESSIVE_NEUTRAL))
         {
         if(IS_NEUTRAL(ch))
            v_al -= 25;
         else
            v_al -= 50;
         }

      if(IS_CSET(victim->only.npc->npcact, ACT_AGGRESSIVE_EVIL))
         {
         if(IS_EVIL(ch))
            v_al -= 75;
         else
            v_al += 50;
         }

      if((GET_RACE(ch) == RACE_DEMON) || (GET_RACE(ch) == RACE_DEVIL))
         change += 2;

      if(IS_CSET(victim->only.npc->npcact, ACT_NICE_THIEF))
         v_al += 25;

      if(IS_CSET(victim->only.npc->npcact, ACT_STAY_ZONE))
         v_al += 25;

      if(IS_CSET(victim->only.npc->npcact, ACT_WIMPY))
         v_al += 10;
      }
   else
      {
      if(victim->only.pc->aggressive > -1)
         v_al -= 100;

      if(IS_SET(victim->only.pc->law_flags, PLR_VICIOUS))
         v_al -= 25;

      if(GET_CLASS(victim) == CLASS_ASSASSIN)
         change += 2;

      if(GET_CLASS(victim) == CLASS_ANTIPALADIN)
         change += 5;

      if(GET_CLASS(victim) == CLASS_PALADIN)
         change -= 5;

      if(victim->specials.consent && (victim->specials.consent == ch))
         change -= 10;
      }

   /* at this point v_al will range from -1275 to 1135 depending on various
      flags and such, this will allow for the possibility of alignment shifts
      even when ch is -1000 or 1000, without having to pick nits. */

   /* ok, 'good' alignment is the most vulnerable to change, neutral not nearly
      so, and evil is very tough to change (except to become more evil) */

   if(a_al > 350)
      {

      /* ch is 'good' */

      if(v_al > 350)
         {

         /* victim is also 'good', this is by far the most drastic shift */

         if(v_al > a_al)
            {

            /* victim is 'more' good */
            change += ((a_al - v_al) / 40 - 9);     /* -28 to -9 */
            }
         else
            {

            /* victim is 'less' good, but still good */
            change += ((v_al - a_al) / 50 - 6);     /*  -18 to -6 */
            }

         }
      else if(v_al > -351)
         {

         /* victim is neutral, not near as bad */

         change += (v_al / -80);   /* -4 to 4 */

         }
      else
         {

         /* victim is evil, adds to align, but not alot */

         change += (v_al / -150);  /* 8 to 2 */

         }
      }
   else if(a_al > -351)
      {

      /* ch is 'neutral' */

      /* neutral chars don't change much for killing either extreme, instead,
         killing more 'moderate' alignments has the largest effect */

      if(v_al > 0)
         change -= ((1135 - v_al) / 100);  /* 0 to -11 */
      else
         change += ((1275 + v_al) / 100);  /* 12 to 0 */

      }
   else
      {

      /* ch is 'evil' */

      /* almost ANYTHING an evil kills can be regarded as an evil act.  The
         only exception is killing demons, and killing things MORE evil than
         they are currently, but not a lot in any case. */

      if(v_al < a_al)
         {

         /* victim is more evil, very small shift towards neutral */

         change += MAX(1, ((v_al - a_al) / -200));         /* 4 to 1 */
         }
      else
         {

         /* victim is 'less' evil */

         change += ((v_al - a_al) / -100);         /* -21 to 0 */
         }
      }

   /* unless I really screwed something up, change now ranges for -36 to 20, since we want to minimize
      alignment shifts, we are going to use those as a basis for 'chance of shift'.  Max actual change
      will be -4 to 2, and it's going to take concerted effort over a LONG time to change it much at all. */

   /* final mod, nothing tougher to change than 'old' evil and nothing faster than a fall from grace */

   if(IS_EVIL(ch) && (change > 1))
      change = BOUNDED(1, change - (GET_LEVEL(ch) / 15), change);

   /* Just to piss those highlevel good weenies off who kill all good mobs daily */
   if(IS_GOOD(ch) && (change < 0))
      change -= (GET_LEVEL(ch) / 3);

   if(change < 0)
      {
      while(change < 0)
         {
         a_al -= (number(0, 9) < MIN(10, -(change)));
         change += 7;
         }
      }
   else
      {
      while(change > 0)
         {
         a_al += (number(0, 9) < MIN(10, change));
         change -= 10;
         }
      }

   GET_ALIGNMENT(ch) = BOUNDED(-1000, a_al, 1000);

   for(i = 0; i < MAX_WEAR; i++)
      {
      if(!(obj = ch->equipment[i]))
         continue;
      if(((IS_OBJ_STAT(obj, ITEM_ANTI_EVIL) && IS_EVIL(ch)) ||
          (IS_OBJ_STAT(obj, ITEM_ANTI_GOOD) && IS_GOOD(ch)) ||
          (IS_OBJ_STAT(obj, ITEM_ANTI_NEUTRAL) && IS_NEUTRAL(ch))) &&
         (ch->in_room != NOWHERE) && !IS_TRUSTED(ch) && (IS_PC(ch) || IS_PET(ch)))
         {
         act("You are zapped by $p and instantly drop it.", FALSE, ch, obj, 0, TO_CHAR);
         act("$n is zapped by $p and instantly drops it.", FALSE, ch, obj, 0, TO_ROOM);
         obj = unequip_char(ch, i, TRUE);
         obj_to_room(obj, ch->in_room);
         }
      }
}

void death_cry(P_char ch)
{
   char buf[MAX_INPUT_LENGTH];
   int door, was_in, room;

   act("Your blood freezes as you hear the rattling death cry of $n.", FALSE, ch, 0, 0, TO_ROOM);
   was_in = ch->in_room;

   for(door = 0; door <= 5; door++)
      {
      if(CAN_GO(ch, door))
         {
         room = world[ch->in_room].dir_option[door]->to_room;
         sprintf(buf, "Your skin crawls as you hear a death cry nearby!\n");
         send_to_room(buf, room);
         }
      }
}

/* this routine was repeated all over, made it a function, basically handles
   switched gods and shapechangers, so that the right body dies.  Returns the
   right char, or NULL, if there is a problem.  JAB */

P_char ForceReturn(P_char ch)
{
   P_char true_id, t_ch = ch;
   sh_int in_rm;

   if(!t_ch)
      return NULL;

   if(IS_AFFECTED(ch, AFF_WRAITHFORM))
      {
      BackToUsualForm(ch);
      return ch;
      }

   /* Shapechanged players go back.. */
   if(IS_MORPH(t_ch))
      {
      true_id = MORPH_ORIG(t_ch);
      act("$n's dying body slowly changes back into $N.",
          FALSE, t_ch, 0, true_id, TO_ROOM);
      send_to_char("As the last of life leaves you, your body resumes its natural form.\r\n",
                   t_ch);
      if(t_ch->specials.fighting)
         stop_fighting(t_ch);
      un_morph(t_ch);
      return true_id;
      }

   /* switched god */

   if(t_ch->desc && t_ch->desc->original && IS_PC(t_ch->desc->original) &&
      t_ch->desc->original->only.pc->switched)
      {
      do_return(t_ch, 0, -4);
      }


   /** if shape changed, return before dying --TAM 04/09/94 **/

   if(t_ch->desc && t_ch->desc->original && !t_ch->only.pc->switched)
      {
      true_id = t_ch->desc->original;
      in_rm = t_ch->in_room;

      act("$n's dying body slowly changes back into $N.", FALSE, t_ch, 0, true_id, TO_ROOM);

      if(t_ch->specials.fighting)
         stop_fighting(t_ch);

      t_ch->desc->character = t_ch->desc->original;
      t_ch->desc->original = 0;
      t_ch->desc->character->only.pc->switched = 0;
      t_ch->desc->character->desc = t_ch->desc;
      t_ch->desc = 0;

      extract_char(t_ch);
      t_ch = NULL;

      char_from_room(true_id);
      char_to_room(true_id, in_rm, 0);

      return(true_id);
      }
   return(t_ch);
}

/* raw_kill basically does everything associated with a char's death, except
   deduct experience.  die() does that, then calls raw_kill(). */

void raw_kill(P_char ch)
{
   P_char mob;
   char abuf[10];
   struct affected_type *af, *afn;
   struct func_attachment *fn = NULL;
   struct follow_type *j, *k;
   struct func_registration_data *frd = NULL;
   char buf[MAX_INPUT_LENGTH];
   P_event e1, e2;

   ch = ForceReturn(ch);

   if(!ch)
      return;

   if(ch->specials.fighting)
      stop_fighting(ch);

   StopAllAttackers(ch);

   // Nuke mirror images
   if(IS_NPC(ch) && (ch->nr == real_mobile(203)))
      {
      act("&+cUpon being struck, &N$n&+c shatters into a million particles of &N&+ylight&N&+c!", TRUE,
          ch, 0, 0, TO_ROOM);
      extract_char(ch);
      return;
      }

   // Nuke dopplegangers
   if(IS_NPC(ch) && (ch->nr == real_mobile(204)))
      {
      act("&+LUpon being struck, &N$n&+L dissolves into a whirling mass of dark shadows.&N", TRUE,
          ch, 0, 0, TO_ROOM);
      if(ch->following && IS_PC(ch->following))
         REMOVE_CBIT(ch->following->specials.affects, AFF_DOPPLEGANGER);
      extract_char(ch);
      return;
      }

   stop_riding(ch);

   death_cry(ch);

   strcpy(buf, "bloodloss");

   if(IS_PC(ch))
      {

      /* Remove Bloodloss proc if it's attached */
      while(findAttachedChProc(ch, buf))
         {
         debuglog(51, DS_IYACHTU, "Found bloodloss on %s", ch->player.name);
         frd = findProcFunction(buf);
         RemoveProcPC(ch, frd->ptr, buf);
         }

      /* go thru all followers looking for savable pets */
      for(k = ch->followers; k; k = j)
         {
         j = k->next;
         if(IS_NPC(k->follower))
            {
            /* see if it was following a PC and saving, gotta remember who can claim it */
            if(IS_CSET(k->follower->only.npc->npcact, ACT_SAVE) &&
               hasAutoSaveEvent(k->follower) && k->follower->following)
               {
               k->follower->only.npc->lastowner = str_dup(GET_NAME(k->follower->following));
               stopPetSave(k->follower->following,  k->follower);
               }
            }
         }

      for(af = ch->affected; af; af = afn)
         {
         afn = af->next;
         if(ch->desc
            && !ch->desc->connected
            && IS_SPELL(af->type)
            && spell_wear_off_msg[af->type]
            && (*spell_wear_off_msg[af->type] != '!'))
            {
            send_to_char(spell_wear_off_msg[af->type], ch);
            send_to_char("\n", ch);
            }
         affect_from_char(ch, af->type);
         }

#ifdef NEW_BARD
      if(IS_SINGING(ch))
         bard_stop_singing(ch);
      else if(ch->bard_singing)
         remove_from_song(ch->bard_singing, ch);
#endif

      /* even things out so it doesn't barf when removing perm affect items */
      affect_total(ch);
      }

   SET_POS(ch, GET_POS(ch) + STAT_DEAD);

#ifdef PCPROCS
   if(IS_SET(GET_SPEC_FLAG(ch), IDX_NPC_DIE))
      {
      fn = GET_SPEC_FN(ch);
      while(fn)
         {
         if((fn->proc_flag & IDX_NPC_DIE) && (*fn->func.ch) (ch, 0, PROC_NPC_DIE, 0))
            break;
         fn = fn->next;
         }
      }
#else
   if(IS_NPC(ch) && (mob_index[ch->nr].spec_flag & IDX_NPC_DIE))
      {
      fn = mob_index[ch->nr].func;
      while(fn)
         {
         if((fn->proc_flag & IDX_NPC_DIE) && (*fn->func.ch) (ch, 0, PROC_NPC_DIE, 0))
            break;
         fn = fn->next;
         }
      }
#endif

   if(!fn)
      {
      if(IS_NPC(ch))
         make_corpse(ch);
      else if(!IN_ACHERON(ch))
         make_corpse(ch);
      }

   ch->specials.command_delays = FALSE;

   if(IS_PC(ch) && !IN_ACHERON(ch) && !IS_ENABLED(CODE_CHAOS))
      {
      strcpy(abuf, "all");
      if(ch->only.pc->memorize_list)
         do_forget(ch, abuf, -7);
      if(has_spells_in_memory(ch))
         do_forget(ch, abuf, -7);
#if 0
      for(i = 1; i <= numSkills; i++)
         ch->only.pc->skills[i].memorized = 0;
#endif

      }

   /* this WAS in extract_char, but only death removes you from mobs mem now.  Renting won't do it, as
      long as the game is up and neither char nor mob dies, they WILL remember! */
   if(IS_PC(ch) && (!(ch->only.pc->acheron_portal)))
      {
      mob = NPC_list;

      while(mob)
         {
         if(HAS_MEMORY(mob))
            mem_remFromMemory(mob->only.npc->memory, GET_NAME(ch));
         mob = mob->next;
         }
      }

   //RemovePCDisease(ch);
   REMOVE_CBIT(ch->specials.affects, AFF_BLIND); /* pw blind does't have aff, structure */
   if(!IN_ACHERON(ch))
      {
      ch->specials.witnessed = NULL; /* remove witness records. */
      writeCharacter(ch, 4, NOWHERE);
      }

   if(ch->specials.cart)
      {
      ch->specials.cart->owner=NULL;
      AddEvent(EVENT_DECAY, PULSE_HOUR, TRUE, ch->specials.cart, 0);
      ch->specials.cart=NULL;
      writeCart(ch);
      }

   if(IS_PC(ch))
      {
      if(IN_ACHERON(ch))
         {
         act("The corpse of $n melts into the ground, leaving no trace of its "
             "passing.", TRUE, ch, 0, 0, TO_NOTVICT);
         char_from_room(ch);
         SET_POS(ch, GET_POS(ch) + STAT_NORMAL);
         char_to_room(ch, ch->only.pc->acheron_portal, -1);
         act("$n appears suddenly in the room, looking deathly pale.", TRUE, ch,
             0, 0, TO_NOTVICT);
         if(!IN_QUEST_PK(ch))
            {
            send_to_char("\nYour death liberates you from Acheron and returns you to "
                         "the Prime Material plane, no worse for the wear.\n", ch);
            }
         else
            {
            send_to_char("\nYour death sends you spiralling away from the battlefield "
                         "to await the outcome of the conflict.\n", ch);
            ch->only.pc->quest_portal = 0;
            }
         ch->only.pc->acheron_portal = 0;
         GET_MANA (ch) = (GET_MAX_MANA(ch) / 2);
         GET_HIT (ch) = (GET_MAX_HIT(ch) / 2);
         GET_MOVE (ch) = GET_MAX_MOVE(ch);
         if(!IN_QUEST_PK(ch))
            {
            GET_PRESTIGE(ch) -= cc_prestige_fragBonus;//}

            // Remove all know 'burning' spells.
            LOOP_EVENTS(e1, ch->events)
            if((e1->type == EVENT_BURN_CHAR) || (e1->type == EVENT_MULTI_ROUND_SPELL))
               {
               if(e1 && (current_event != e1))
                  {
                  e2 = current_event;
                  current_event = e1;
                  RemoveEvent ();
                  current_event = e2;
                  // Remove the event...
                  }
               }
            }
         REMOVE_CBIT(ch->specials.affects, AFF_MULTI_ROUND_SPELL);
         REMOVE_CBIT(ch->specials.affects, AFF_BURNING);

         CharWait(ch, PULSE_VIOLENCE * 5);
         }
      else
         {
         extract_char(ch);
         }
      }
   else
      {
      extract_char(ch);
      }

   ch = NULL;
}

void die_deathtrap(P_char ch)
{
   /* DTs bad! EVIL! This log message is just so DTs can be spotted and fixed */
   statuslog(ch->player.level, "%s hit a DT in [%d].", GET_NAME(ch), world[ch->in_room].number);
}

int would_die(P_char ch, int damage)
{
   int threshold = -10;

   if(!ch)
      {
      debuglog(51, DS_BADPARAMS, "ACK!  would_die called with a non-existent ch!");
      return FALSE;
      }

   if(IS_AFFECTED(ch, AFF_DEATH_PACT))
      threshold = -100;

   if((GET_HIT(ch) - damage) < threshold)
      return TRUE;

   return FALSE;
}


// The big die routine!! OMG YOUR DEAD! O_O
void die(P_char ch)
{
   int loss;

   ch = ForceReturn(ch);

   if(!ch)
      return;

   if(IS_PC(ch))
      {
      if(!IN_ACHERON(ch) && !IS_ENABLED(CODE_CHAOS))
         {
         if(GET_LEVEL(ch) > 1)
            loss = (EXP_TABLE(ch, 0) - EXP_TABLE(ch, -1)) / 4;
         else
            loss = GET_EXP(ch) / 4;

         if(loss)
            logit(LOG_EXP, "%s lost %d, from dying.", GET_NAME(ch), loss);

         MasterCounter_PcDeath++;
         gain_exp_regardless(ch, -loss);
         }
      }
   else     // If its a mob, store how much exp and money he gave up.
      {
      MasterCounter_MobDeath++;
      MasterCounter_MoneyGained += (int)(GET_PLATINUM(ch) * 1000);
      MasterCounter_MoneyGained += (int)(GET_GOLD(ch) * 100);
      MasterCounter_MoneyGained += (int)(GET_SILVER(ch) * 10);
      MasterCounter_MoneyGained += (int)GET_COPPER(ch);

      //fprintf(stderr, "MobDeath #%d (%s): MoneyGain = %d\n",
      //   MasterCounter_MobDeath, GET_NAME(ch),
      //   MasterCounter_MoneyGained);
      }

   raw_kill(ch);
}

/* award exp for an 'instant_kill' sort of death, call this rather than doing
   massive damage.  Instantkill, the gith sword's beheading proc, power word
   kill, holy/unholy word call this instead of damage() now. JAB */

void SuddenDeath(P_char victim, P_char ch, const char *kill_type)
{
   int XP, hits, i;

   if(!SanityCheck(victim, "SuddenDeath"))
      return;

   if(IS_PC(victim) && IN_ACHERON(ch))
      GET_PRESTIGE(ch) += cc_prestige_fragBonus;

   if(IS_TRUSTED(victim))
      return;

   justice_witness(ch, victim, CRIME_MURDER);

   if(!ch || (ch == victim) || CHAR_IN_ARENA(victim) || IS_TRUSTED(ch))
      {
      if(IS_PC(victim))
         {
         statuslog(victim->player.level, "SuddenDeath (self): %s (via %s) in %d.",
                   GET_NAME(victim), kill_type,
                   ((victim->in_room == NOWHERE) ? -1 : world[victim->in_room].number));
         logit(LOG_DEATH, "SuddenDeath (self): %s (via %s) in %d.",
               GET_NAME(victim), kill_type, ((victim->in_room == NOWHERE) ? -1 : world[victim->in_room].number));
         }

      die(victim);
      return;
      }

   hits = MAX(1, (GET_HIT(victim) + 10));
   XP = GET_LEVEL(victim) * hits * exp_mod(ch, victim);
   XP /= (align_mod(ch, victim) * 400);
   i = (EXP_TABLE(GET_PLYR(ch), 1) - EXP_TABLE(GET_PLYR(ch), 0)) / 4;
   if(IS_PC(ch))
      {
      if(IS_EVIL(victim) && (GET_CLASS(GET_PLYR(ch)) == CLASS_PALADIN))
         XP = (int)(XP * 1.25);
      logit(LOG_EXP, "%s: %d(%d) SuddenDeath (via %s) of %s", GET_NAME(ch), XP,
            BOUNDED(-i, XP, i), kill_type, GET_NAME(victim));
      }
   gain_exp(ch, XP);

   if(GET_GROUP(ch))
      {
      group_gain(ch, victim);
      }
   else
      {
      send_to_char("You receive your share of experience.\n", ch);
      i = (EXP_TABLE(GET_PLYR(ch), 1) - EXP_TABLE(GET_PLYR(ch), 0)) / 4;
      XP = (GET_EXP(victim) * exp_mod(ch, victim)) / (100 * align_mod(ch, victim));
      if(XP && (IS_PC(ch) || IS_MORPH(ch)))
         {
         if(IS_EVIL(victim) && (GET_CLASS(GET_PLYR(ch)) == CLASS_PALADIN))
            XP = (int)(XP * 1.25);
         logit(LOG_EXP, "%s: %d(%d), SuddenDeath (via %s) of %s",
               GET_NAME(GET_PLYR(ch)), XP, BOUNDED(-i, XP, i), kill_type, GET_NAME(victim));
         }

      gain_exp(ch, XP);
      change_alignment(ch, victim);
      if(IS_NPC(victim))
         GET_PRESTIGE(ch) += MAX(0, GET_PRESTIGE_BONUS(victim));

      }

   act("$n &N&+ris dead! R.I.P.&n", TRUE, victim, 0, 0, TO_ROOM);
   if(AWAKE(victim))
      act("&=rlYou feel yourself falling to the ground.", FALSE, victim, 0, 0, TO_CHAR);
   act("&=rlYour soul leaves your body in the cold sleep of death...", FALSE, victim, 0, 0, TO_CHAR);

   if(IS_PC(victim))
      {
      statuslog(victim->player.level, "%s caused SuddenDeath (via %s) to %s in %d.",
                GET_NAME(ch), kill_type, GET_NAME(victim),
                ((victim->in_room == NOWHERE) ? -1 : world[victim->in_room].number));
      logit(LOG_DEATH, "%s caused SuddenDeath (via %s) to %s in %d.",
            GET_NAME(ch), kill_type, GET_NAME(victim),
            ((victim->in_room == NOWHERE) ? -1 : world[victim->in_room].number));
      }
   die(victim);
}

/* experience divisor based on class/align of victim */

int align_mod(P_char k, P_char victim)
{
   int a;

   /* Switch to real PC if shapechanged */
   k = GET_PLYR(k);

   a = victim->specials.alignment;
   if(IS_NPC(k) || (GET_CLASS(k) != CLASS_PALADIN))
      return 1;

   /* paladins */
   if(a <= -350)
      return 1;                   /* full exp for evils */

   if(IS_PC(victim) ||
      IS_CSET(victim->only.npc->npcact, ACT_AGGRESSIVE_GOOD) ||
      IS_CSET(victim->only.npc->npcact, ACT_AGG_RACEGOOD) ||
      (IS_CSET(victim->only.npc->npcact, ACT_AGGRESSIVE)))
      {
      /*
             && !IS_CSET(victim->only.npc->npcact,
                     (ACT_AGGRESSIVE_GOOD | ACT_AGGRESSIVE_NEUTRAL | ACT_AGGRESSIVE_EVIL |
                      ACT_AGG_RACEGOOD | ACT_AGG_RACEEVIL)))) {
      */
      /* victim is agg to paladins, so, no loss, but very little gain */
      if(a > 350)
         {
         return 50;                /* if victim is Good, but agg to paladins, 1/50th exp */
         }
      else
         {
         return 5;                 /* neutral, but agg to paladins, 1/5th exp */
         }
      }
   else
      {
      /* not evil and not agg to paladins, lose exp */
      if(a > 350)
         {
         return -1;                /* if victim is Good, lose full exp. */
         }
      else
         {
         return -3;                /* neutral, lose 1/3rd exp */
         }
      }
   return 1;
}

/* experience multiplier return int % modifier */

int exp_mod(P_char k, P_char victim)
{
   int diff;

   /* Switch to real PC if shapechanged */
   k = GET_PLYR(k);

   diff = GET_LEVEL(k) - GET_LEVEL(victim);
   if(diff > 40)
      return 1;
   else if(diff > 30)           /* 31+ */
      return 3;
   else if(diff > 20)           /* 21-30 */
      return 10;
   else if(diff > 15)           /* 16-20 */
      return 20;
   else if(diff > 10)           /* 11-15 */
      return 30;
   else if(diff > 5)            /* 6-10 */
      return 55;
   else if(diff > 2)            /* 3-5 */
      return 90;
   else if(diff > -3)           /* -2 - 2 */
      return 100;
   else if(diff > -7)           /* -6 - -3 */
      return 107;
   else if(diff > -11)          /* -10 - -7 */
      return 115;

   return 125;                   /* < -10 */
}

int PartySizeMod(int size, int total_levels, int lowest, int highest)
{
   int a, b, c;

   if(highest > 50 || lowest < 1)
      return 10;

   a = 10;

   b = highest - lowest;

   c = total_levels - b * size;

   if(c > highest)
      a += BOUNDED(1, (c - highest) / MAX(1, (size / 2)), 10);

   a += BOUNDED(0, size - 1, 7);

   a -= BOUNDED(1, b / 10, 5);

   /* Result: Returns int in range between 6,27  -> .6* to 2.7* exp
      depending on size of party and level distribution. Practically
      numbers between 1-2 are only used, as 2.7* exp would come
      (as example) from 8 50th levelers partying, and .6 from 50th+1st
      leveler partying. */

   return a;
}

void group_gain(P_char ch, P_char victim)
{
   int g_levels, share, ldiff, XP, amount, lowest, highest;
   bool holyGroup = FALSE;
   bool vileGroup = FALSE;

   P_gmember member;

   highest = lowest = g_levels = 0;
   amount = 0;

   LOOP_THRU_GROUP (member, GET_GROUP(ch))
   if(member->this->in_room == ch->in_room)
      {
      if(GET_LEVEL(member->this) > highest)
         highest = GET_LEVEL(member->this);
      if(GET_LEVEL(member->this) < lowest)
         lowest = GET_LEVEL(member->this);
      g_levels += GET_LEVEL(member->this);
      amount++;
      }
   /* Roleplay Penalty disabled - I'm having doubts this is a good idea
     LOOP_THRU_GROUP (member, GET_GROUP(ch))
     {
       if ((GET_CLASS(member->this) == CLASS_PALADIN) ||
           (GET_CLASS(member->this) == CLASS_RANGER))
             holyGroup = TRUE;
       if ((GET_CLASS(member->this) == CLASS_LICH) ||
           (GET_CLASS(member->this) == CLASS_NECROMANCER) ||
           (GET_CLASS(member->this) == CLASS_ANTIPALADIN))
             vileGroup = TRUE;
     }
   */
   if(GET_EXP(victim) < g_levels)
      share = 1;
   else
      {
      if(!g_levels)
         g_levels = 1;
      share = GET_EXP(victim) * PartySizeMod(amount, g_levels, lowest, highest) / 10 / g_levels;
      }

   LOOP_THRU_GROUP (member, GET_GROUP(ch))
   if(!IS_TRUSTED(member->this) && (member->this->in_room == ch->in_room))
      {
      send_to_char("You receive your share of experience.\n", member->this);
      ldiff = EXP_TABLE(GET_PLYR(member->this), 1) - EXP_TABLE(GET_PLYR(member->this), 0);
      XP = (GET_LEVEL(GET_PLYR(member->this)) * share * exp_mod(GET_PLYR(member->this), victim));
      XP = XP / (align_mod(GET_PLYR(member->this), victim) * 100);
      XP = Trophy_Mod(GET_PLYR(member->this), victim, XP);
      XP = BOUNDED(1, XP, (ldiff / (1 + (GET_LEVEL(GET_PLYR(member->this)) / 3))));
      if(IS_NPC(victim))
         GET_PRESTIGE(member->this) += MAX(0, GET_PRESTIGE_BONUS(victim));
      if(XP && IS_PC(GET_PLYR(member->this)))
         {
         if(IS_EVIL(victim) && (GET_CLASS(member->this) == CLASS_PALADIN))
            XP = (int)(XP * 1.25);
         if(holyGroup)
            if((GET_CLASS(member->this) == CLASS_LICH) ||
               (GET_CLASS(member->this) == CLASS_NECROMANCER) ||
               (GET_CLASS(member->this) == CLASS_ANTIPALADIN))
               GET_PRESTIGE(member->this) -= cc_prestige_roleplayPenalty;
         if(vileGroup)
            if((GET_CLASS(member->this) == CLASS_PALADIN) ||
               (GET_CLASS(member->this) == CLASS_RANGER))
               GET_PRESTIGE(member->this) -= cc_prestige_roleplayPenalty;
         logit(LOG_EXP, "%s: %d(%d) gkill of: %s", GET_NAME(GET_PLYR(member->this)),
               XP, BOUNDED((ldiff / -4), XP, (ldiff / 4)), GET_NAME(victim));
         }

      gain_exp(member->this, XP);
      change_alignment(member->this, victim);
      }
}

/* used exclusively by dam_message */

char *replace_string(const char *str, const char *weapon)
{
   char *cp;
   int i1, i2;
   static char buf[MAX_INPUT_LENGTH];

   cp = buf;

   for(i1 = 0; *(str + i1); i1++)
      {
      if(*(str + i1) == '#')
         {
         switch(*(str + ++i1))
            {
            case 'W':
               for(i2 = 0; *(weapon + i2); *(cp++) = *(weapon + i2++));
               break;
            default:
               *(cp++) = '#';
               break;
            }
         }
      else
         {
         *(cp++) = *(str + i1);
         }

      *cp = 0;
      }                             /* For */

   return(buf);
}

/* moved outside to prevent constant reinitializing. JAB */
static int dam_ref[] = {0, 2, 4, 7, 11, 16, 22, 29, 37, 49, 9999};
/* changed, now shows a relative message, hitting something with 17k hits for
   41, hardly counts as a massacre.  JAB */

void dam_message(int dam, P_char ch, P_char victim, int w_type)
{
   char *buf = 0;
   int percent, loop = 0, race;

   static struct dam_weapon_type
      {
      const char *to_room, *to_char, *to_victim;
      } dam_weapons[] = {
      {                           /*  0 damage  */
         "$n misses $N with $s #W.",
         "You miss $N with your #W.",
         "$n misses you with $s #W."
      }, {                                /*  0%..2% */
         "$n barely #W $N.",
         "You barely #W $N.",
         "$n barely #W you."
      }, {                                /*  3%..4% */
         "$n #W $N.",
         "You #W $N.",
         "$n #W you."
      }, {                                /*  5%..7% */
         "$n #W $N hard.",
         "You #W $N hard.",
         "$n #W you hard."
      }, {                                /*  8%..11% */
         "$n #W $N very hard.",
         "You #W $N very hard.",
         "$n #W you very hard."
      }, {                                /* 12%..16% */
         "$n #W $N extremely hard.",
         "You #W $N extremely hard.",
         "$n #W you extremely hard."
      }, {                                /* 17%..22% */
         "$N staggers from a fearsome #W from $n!",
         "$N staggers from your fearsome #W!",
         "You stagger from a fearsome #W from $n!"
      }, {                                /* 23%..29% */
         "$n lands a mighty #W on $N!",
         "You land a mighty #W on $N!",
         "A mighty #W from $n makes you contemplate a new career!"
      }, {                                /* 30%..37% */
         "$N gasps from $n's awesome #W!",
         "Your awesome #W causes $N to gasp in pain!",
         "You gasp in pain from $n's awesome #W!"
      }, {                                /* 38%..49% */
         "$n enshrouds $N in a mist of blood with $s deadly #W!",
         "Your #W enshrouds $N in a mist of blood!",
         "$n enshrouds you in a mist of blood with $s deadly #W!"
      }, {                                /* 50+% */
         "$N is nearly slain by the force of $n's #W!",
         "$N is nearly slain by the force of your #W!",
         "$n half kills you with $s mighty #W!"
      }
   };

   race = GET_RACE(ch);

   w_type -= TYPE_HIT;           /* Change to base of table with text */

   if(dam <= 0)
      loop = 0;
   else
      {
      percent = BOUNDED(0, (int) ((dam * 100) / MAX(1, (GET_HIT(victim) + dam + 10))), 100);

      /* Find the message reflecting the proper level of mayhem inflicted */
      for(loop = 1; (percent > dam_ref[loop]); loop++);
      if(loop > 11)
         loop = 11;
      }

   /* In the two years that I have worked for Outcast/Outcast I have DEARLY
    * wanted to fix this bug...I have now done so...
    *
    * THE GRAMMAR IS FIXED FOR ATTACK MESSAGES!!!!     -- DDW
    */

   /* ANTI-SPAM */
   if(loop != 0)
      {
      if((loop != 0) && (loop < 6))
         {
         if(!ch->equipment[WIELD])
            {
            buf = replace_string(dam_weapons[loop].to_room, race_hit_text[race].plural);
            }
         else
            {
            buf = replace_string(dam_weapons[loop].to_room, attack_hit_text[w_type].plural);
            }
         }
      else
         {
         if(!ch->equipment[WIELD])
            {
            buf = replace_string(dam_weapons[loop].to_room, race_hit_text[race].singular);
            }
         else
            {
            buf = replace_string(dam_weapons[loop].to_room, attack_hit_text[w_type].singular);
            }
         }
      c_act(buf, FALSE, ch, ch->equipment[WIELD], victim, TO_NOTVICT, MELEE_HITS);
      }

   if(!ch->equipment[WIELD])
      buf = replace_string(dam_weapons[loop].to_char, race_hit_text[race].singular);
   else
      buf = replace_string(dam_weapons[loop].to_char, attack_hit_text[w_type].singular);
   c_act(buf, FALSE, ch, ch->equipment[WIELD], victim, TO_CHAR, MELEE_HITS);

   if((loop != 0) && (loop < 6))
      {
      if(!ch->equipment[WIELD])
         {
         buf = replace_string(dam_weapons[loop].to_victim, race_hit_text[race].plural);
         }
      else
         {
         buf = replace_string(dam_weapons[loop].to_victim, attack_hit_text[w_type].plural);
         }
      }
   else
      {
      if(!ch->equipment[WIELD])
         buf = replace_string(dam_weapons[loop].to_victim, race_hit_text[race].singular);
      else
         buf = replace_string(dam_weapons[loop].to_victim, attack_hit_text[w_type].singular);
      }
   c_act(buf, FALSE, ch, ch->equipment[WIELD], victim, TO_VICT, MELEE_HITS);
}

// For multi dam messages
const char *dam_multi_msg[] =
{
   "zero",
   "one",
   "two",
   "three",
   "four",
   "five",
   "six",
   "seven",
   "eight",
   "nine",
   "ten",
   "eleven",
   "twelve",
   "thirteen",
   "fourteen",
   "fifteen",
   "sixteen",
   "seventeen",
   "eighteen",
   "nineteen",
   "twenty",
   "more than twenty",
   "\n"
};

// For Missile spells in condense - Iyachtu
void fire_missiles(P_char ch, P_char victim, int missiles, char *descript)
{
   char cbuf[MAX_STRING_LENGTH], vbuf[MAX_STRING_LENGTH], rbuf[MAX_STRING_LENGTH];

   missiles = BOUNDED(1, missiles, 15);
   sprintf(cbuf, "You fire %s %s into $N.", dam_multi_msg[missiles], descript);
   sprintf(vbuf, "$n fires %s %s into you.", dam_multi_msg[missiles], descript);
   sprintf(rbuf, "$n fires %s %s into $N.", dam_multi_msg[missiles], descript);

   cm_act(cbuf, FALSE, ch, NULL, victim, TO_CHAR, SPELL_DMG);
   cm_act(vbuf, FALSE, ch, NULL, victim, TO_VICT, SPELL_DMG);
   cm_act(rbuf, FALSE, ch, NULL, victim, TO_NOTVICT, SPELL_DMG);
}

// For dragon breath condense - Iyachtu
void breath_message(P_char ch, int landed, char *descript)
{
   char cbuf[MAX_STRING_LENGTH], rbuf[MAX_STRING_LENGTH];
   P_char victim = NULL;
   char gram1[10];

   landed = BOUNDED(0, landed, 21);

   if(landed >1)
      sprintf(gram1, "targets");
   else
      sprintf(gram1, "target.");

   sprintf(cbuf, "Your %s breath engulfs %s %s", descript, dam_multi_msg[landed], gram1);
   sprintf(rbuf, "$n breathes %s, engulfing %s %s", descript, dam_multi_msg[landed], gram1);

   cm_act(cbuf, FALSE, ch, NULL, victim, TO_CHAR, SPELL_SPEC);
   cm_act(rbuf, FALSE, ch, NULL, victim, TO_ROOM, SPELL_SPEC);
}

// For Area spells in condense - Iyachtu
void area_message(P_char ch, int landed, int avoided, char*descript)
{
   char cbuf[MAX_STRING_LENGTH], rbuf[MAX_STRING_LENGTH];
   P_char victim = NULL;
   char gram1[10], gram2[10];

   landed = BOUNDED(0, landed, 21);
   avoided = BOUNDED(0, avoided, 21);

   if(landed > 1)
      sprintf(gram1, "targets");
   else
      sprintf(gram1, "target");

   if(avoided >1)
      sprintf(gram2, "were");
   else
      sprintf(gram2, "was");

   if(avoided > 0)
      {
      sprintf(cbuf, "Your %s hits %s %s, %s %s outside of the area.",
              descript, dam_multi_msg[landed], gram1, dam_multi_msg[avoided], gram2);
      sprintf(rbuf, "$n's %s hits %s %s, %s %s outside of the area.",
              descript, dam_multi_msg[landed], gram1, dam_multi_msg[avoided], gram2);
      }
   else
      {
      sprintf(cbuf, "Your %s hits %s %s.", descript, dam_multi_msg[landed], gram1);
      sprintf(rbuf, "$n's %s hits %s %s.", descript, dam_multi_msg[landed], gram1);
      }

   cm_act(cbuf, FALSE, ch, NULL, victim, TO_CHAR, SPELL_DMG);
   cm_act(rbuf, FALSE, ch, NULL, victim, TO_ROOM, SPELL_DMG);
}

void dam_message_multi(P_char ch, P_char victim)
{
   char cBuf[256], vBuf[256], rBuf[256];
   byte hitCount = 0;
   int hitCounti = 0;

   // Sanity Check, revisited - Iyachtu :)
   // (Need to stay in the function to display parries, etc)
   if(!ch || !victim)
      return;
   if(!(hitCount = ch->points.hitCount) && avoidCount <1)
      return;

#if 0
   // Sanity Check
   if(!(hitCount = ch->points.hitCount) || !ch || !victim)
      return;
#endif

   // Bound that puppy
   // Bounding to zero now - Iyachtu
   hitCount = BOUNDED(0, hitCount, 15);

   // To shut the compiler the hell up
   hitCounti = hitCount;

   // Skip it if this is a doppleganger
   if(IS_NPC(ch) && (mob_index[ch->nr].virtual == 204))
      return;

   if(hitCount > 0)
      {

      // Assemble the message
      if(hitCount > 1)
         {
         sprintf(cBuf, "You attack $N, landing %s hits.", dam_multi_msg[hitCounti]);
         sprintf(vBuf, "$n attacks you, landing %s hits.", dam_multi_msg[hitCounti]);
         sprintf(rBuf, "$n attacks $N, landing %s hits.", dam_multi_msg[hitCounti]);
         }
      else
         {
         sprintf(cBuf, "You attack $N, landing a single hit.");
         sprintf(vBuf, "$n attacks you, landing a single hit.");
         sprintf(rBuf, "$n attacks $N, landing a single hit.");
         }

      // Send the message
      cm_act(cBuf, FALSE, ch, NULL, victim, TO_CHAR, MELEE_HITS);
      cm_act(vBuf, FALSE, ch, NULL, victim, TO_VICT, MELEE_HITS);
      cm_act(rBuf, FALSE, ch, NULL, victim, TO_NOTVICT, MELEE_HITS);

      // Reset hitCount var
      ch->points.hitCount = 0;
      }

   // Fire/Cold/Unholy Aura shield messages - Iyachtu

   if(shieldHits > 0)
      {
      if(shieldHits >1)
         {
         sprintf(cBuf, "You injured yourself on $N's magical shield %s times.", dam_multi_msg[shieldHits]);
         sprintf(vBuf, "$n is injured on your magical shield %s times.", dam_multi_msg[shieldHits]);
         sprintf(rBuf, "$n is injured on $N's magical shield %s times.", dam_multi_msg[shieldHits]);
         }
      else
         {
         sprintf(cBuf, "You injured yourself on $N's magical shield.");
         sprintf(vBuf, "$n is injured on your magical shield.");
         sprintf(rBuf, "$n is injured on $N's magical shield.");
         }

      // Send shield messages

      cm_act(cBuf, FALSE, ch, NULL, victim, TO_CHAR, MELEE_HITS);
      cm_act(vBuf, FALSE, ch, NULL, victim, TO_VICT, MELEE_HITS);
      cm_act(rBuf, FALSE, ch, NULL, victim, TO_NOTVICT, MELEE_HITS);
      shieldHits = 0;
      }

   // Now for the defensive skills!  - Iyachtu

   if(avoidCount > 0)
      {
      if(avoidCount > 1)
         {
         sprintf(cBuf, "$N defends against %s of your attacks.", dam_multi_msg[avoidCount]);
         sprintf(vBuf, "You defend against %s attacks from $n.", dam_multi_msg[avoidCount]);
         sprintf(rBuf, "$N defends against %s attacks from $n.", dam_multi_msg[avoidCount]);
         }
      else
         {
         sprintf(cBuf, "$N defends against your attack.");
         sprintf(vBuf, "You defend against an attack from $n.");
         sprintf(rBuf, "$N defends against an attack from $n.");
         }

      // Send defense message
      cm_act(cBuf, FALSE, ch, NULL, victim, TO_CHAR, MELEE_DEF);
      cm_act(vBuf, FALSE, ch, NULL, victim, TO_VICT, MELEE_DEF);
      cm_act(rBuf, FALSE, ch, NULL, victim, TO_NOTVICT, MELEE_DEF);
      avoidCount = 0;
      }

   // Finis
   return;
}

void NukeSingleStone(P_char ch)
{
   struct affected_type *af, *af2;

   for(af = ch->affected; af; af = af2)
      {
      af2 = af->next;
      if(af->type == SPELL_STONE_SKIN)
         {
         af->modifier--;
         if(af->modifier <= 0)
            {
            affect_remove(ch, af);
            REMOVE_CBIT(ch->specials.affects, AFF_STONE_SKIN);
            send_to_char(spell_wear_off_msg[SPELL_STONE_SKIN], ch);
            send_to_char("\n", ch);
            }
         return;
         }
      }

   if(!af)
      REMOVE_CBIT(ch->specials.affects, AFF_STONE_SKIN);
}

int DopplegangerAttack(P_char ch, P_char victim, int attackType)
{
   P_char Dopple = NULL, temp = NULL;
   struct follow_type *f, *hold;

   // Bail out and kill the affect if no followers..
   if(!(ch->followers) || !attackType)
      {
      REMOVE_CBIT(ch->specials.affects, AFF_DOPPLEGANGER);
      return FALSE;
      }

   // Loop through followers, find the dopple
   for(f = ch->followers; f; f = hold)
      {
      hold = f->next;
      temp = f->follower;
      if(IS_PC(temp))
         continue;
      if((mob_index[temp->nr].virtual == 204) && ((temp->in_room) == (ch->in_room)))
         {
         Dopple = temp;
         }
      }

   /* Make sure we have an image.. */
   if(!Dopple || Dopple->in_room == -1)
      {
      REMOVE_CBIT(ch->specials.affects, AFF_DOPPLEGANGER);
      return FALSE;
      }

   // We have a doppleganger, make his ass attack!
   Dopple->only.npc->attack_type = attackType;
   CombatMeleeAttack(Dopple, victim, attackType);

   // Now stop fighting, you bad boy.
   if(Dopple && (GET_STAT(Dopple) != STAT_DEAD) && (Dopple->in_room != NOWHERE))
      StopCombat(Dopple);
   else
      REMOVE_CBIT(ch->specials.affects, AFF_DOPPLEGANGER);

   return TRUE;

}

/* Returns 0 if no image exists, is not in room, or the real ch is hit */
/* Returns 1 if mirror image succesfully nuked.                 -- CRM */
int NukeMirrorImage(P_char ch, P_char attacker, int attacktype)
{
   //  P_char temp, image;
   int numImages = 0;

   struct affected_type *af, *af2;

   if(IS_SPELL(attacktype))
      if(IS_SET(skills[pindex2Skill[attacktype]].targets, TAR_IGNORE))
         return FALSE;

   if(attacktype >= SPELL_FIRE_BREATH && attacktype <= SPELL_LIGHTNING_BREATH)
      return FALSE;

   if(attacktype == SPELL_POISON || attacktype == SPELL_VENOM || attacktype == TYPE_SUFFERING)
      return FALSE;

   if(attacktype == TYPE_PSIONIC || IS_PSISKILL(attacktype))
      if(attacktype != SKILL_PROJECT_FORCE)
         return FALSE;

   for(af = ch->affected; af; af = af2)
      {
      af2 = af->next;
      if(af->type == SPELL_MIRROR_IMAGE)
         {
         if(af->modifier <= 0)
            {
            affect_remove(ch, af);
            REMOVE_CBIT(ch->specials.affects, AFF_MIRROR_IMAGE);
            send_to_char(spell_wear_off_msg[SPELL_MIRROR_IMAGE], ch);
            send_to_char("\n", ch);
            return FALSE;
            }
         break;
         }
      }

   /* Make sure we have an image.. */
   if(!af || (af->type != SPELL_MIRROR_IMAGE))
      {
      REMOVE_CBIT(ch->specials.affects, AFF_MIRROR_IMAGE);
      return FALSE;
      }

   numImages = af->modifier;

   /* Chance to hit the real PC instead of the image */
   if(!number(0, numImages))
      return FALSE;

   /* Nuke that suckah */
   act("&+cUpon being struck, a mirror image of &N$n&+c shatters into a million particles of &N&+ylight&N&+c!",
       TRUE, ch, 0, 0, TO_CHAR);
   act("&+cUpon being struck, a mirror image of &N$n&+c shatters into a million particles of &N&+ylight&N&+c!",
       TRUE, ch, 0, 0, TO_ROOM);

   af->modifier -= 1;

   /* Remove the affect if that was the last image we just nuked */
   if(numImages == 1)
      {
      REMOVE_CBIT(ch->specials.affects, AFF_MIRROR_IMAGE);
      affect_remove(ch, af);
      send_to_char(spell_wear_off_msg[SPELL_MIRROR_IMAGE], ch);
      send_to_char("\n", ch);
      }

   return TRUE;
}


void NukePoison(P_char ch)
{
   struct affected_type *af, *af2;

   for(af = ch->affected; af; af = af2)
      {
      af2 = af->next;
      if(af->type == SPELL_POISON)
         {
         affect_remove(ch, af);
         GET_HIT(ch) = 10;
         send_to_char(spell_wear_off_msg[SPELL_POISON], ch);
         }
      }
}

void NukeDisplacement(P_char ch)
{
   struct affected_type *af, *af2;

   for(af = ch->affected; af; af = af2)
      {
      af2 = af->next;
      if(af->type == SPELL_DISPLACEMENT)
         {

         if(af->modifier > 100)
            af->modifier -= 5;
         else if(af->modifier > 50)
            af->modifier -= 7;
         else
            af->modifier -= 10;

         if(af->modifier <= 0)
            {
            affect_remove(ch, af);
            REMOVE_CBIT(ch->specials.affects, AFF_DISPLACEMENT);
            /* send_to_char(spell_wear_off_msg[SPELL_DISPLACEMENT], ch); */
            send_to_char("&+LYour image is no longer displaced.&N", ch);
            send_to_char("\n", ch);
            }
         }
      }

   /*  if (!af) {
      REMOVE_CBIT(ch->specials.affects, AFF_DISPLACEMENT);
      act("You disappear and re-appear a few feet away!", TRUE, ch, 0, 0, TO_CHAR);
      act("$n's displaced form disappears and re-appears a few feet away!",
          TRUE, ch, 0, 0, TO_ROOM);
    } */
}

/* ripped from above for dragonscales spell - CRM */
void NukeSingleScale(P_char ch)
{
   struct affected_type *af, *af2;

   for(af = ch->affected; af; af = af2)
      {
      af2 = af->next;
      if(af->type == SPELL_DRAGONSCALES)
         {
         af->modifier--;
         if(af->modifier <= 0)
            {
            affect_remove(ch, af);
            REMOVE_CBIT(ch->specials.affects, AFF_DRAGONSCALES);
            send_to_char(spell_wear_off_msg[SPELL_DRAGONSCALES], ch);
            send_to_char("\n", ch);
            }
         return;
         }
      }

   if(!af)
      REMOVE_CBIT(ch->specials.affects, AFF_DRAGONSCALES);
}

/* this routine is here to solve some message timing problems, called from several places in damage(),
   checks to see if victim should start fighting ch.  JAB */

void attack_back(P_char ch, P_char victim)
{
   if(!ch || !victim || IS_FIGHTING(victim) || (GET_STAT(ch) == STAT_DEAD))
      return;

   update_pos(victim);


   if((GET_STAT(victim) > STAT_INCAP) &&
      !IS_AFFECTED(victim, AFF_MINOR_PARALYSIS) && !IS_AFFECTED(victim, AFF_MAJOR_PARALYSIS) &&
      !IS_AFFECTED(victim, AFF_SLEEP) && !IS_AFFECTED(victim, AFF_KNOCKED_OUT)
      && !IS_CSET(victim->specials.affects, AFF_GARROTING))
      set_fighting(victim, ch);
}


/* this is called to check for weapons that are poisoned */
/* Call is from damage for melee weapons and is AFTER normal damage */
bool poisonedWeapon(P_char ch, P_char vict, P_obj weapon)
{
   P_obj poison = NULL;
   int weaponType, saveMod;
   bool wire = FALSE;

   /* Sanity Checks */
   SanityCheck(ch,     "poisonedWeapon");
   SanityCheck(vict,   "poisonedWeapon");
   if(!weapon || !weapon->contains || IMMATERIAL(vict) || IS_TRUSTED(vict))
      return FALSE;

   if(isname("garrote", weapon->name))
      wire = TRUE;

   /* be sure we are using a weapon */
   if((weapon->type != ITEM_WEAPON) && (weapon->type != ITEM_FIREWEAPON) && !wire)
      return FALSE;

   /* check for poison */
   for(poison = weapon->contains; poison; poison = poison->next_content)
      if(poison->type == ITEM_POISON)
         break;

   if(!poison)
      return FALSE;

   /* okay there IS poison */
   if(ch != vict)
      send_to_char("&+GThere appears to be poison on your opponent's weapon!\n", vict);

   /* Assassin's are the best at this...everyone has a VERY small chance for success...like 1 in 100 */
   if((GET_CHAR_SKILL(ch, SKILL_APPLY_POISON) > 0) || (!number(0,99)))
      {
      /* now reduce application */
      poison->value[3]--;
      if(poison->value[3] <= 0)
         {
         weaponType = weapon->type;
         weapon->type = ITEM_CONTAINER;
         obj_from_obj(poison);
         weapon->type = weaponType;
         extract_obj(poison);
         if(weaponType == ITEM_WEAPON)
            send_to_char("&+GThe poison on your weapon wears off.\n", ch);
         else if(wire)
            send_to_char("&+GThe poison on your&N&+L wire&+G wears off.\n", ch);
         }

      if(wire)
         {
         saveMod = 5;
         if(isname("razor", weapon->name))
            saveMod = 7;
         }
      else
         {
         saveMod = 2;
         }

      if(NewSaves(vict, SAVING_PARA, saveMod) || IS_UNDEAD(vict) ||
         (GET_RACE(vict) == RACE_TREE) || (GET_RACE(vict) == RACE_GOLEM) ||
         (GET_RACE(vict) == RACE_E_ELEMENTAL) || (GET_RACE(vict) == RACE_F_ELEMENTAL) ||
         (GET_RACE(vict) == RACE_W_ELEMENTAL) || (GET_RACE(vict) == RACE_A_ELEMENTAL) ||
         (GET_RACE(vict) == RACE_DRAGON) || (GET_RACE(vict) == RACE_DEMON) ||
         (GET_RACE(vict) == RACE_DEVIL) || (GET_RACE(vict) == RACE_ANGEL))
         return FALSE;

      /* ok, we get here, means they failed their throw.  Do the nasty... */

      /* apply specific poison type (using spell's saving throws) */
      /* Bullshit! Why should they get two separate throws? I'm rewriting
         this to call a function and just apply the aff from there. --DMB */
      ApplyPoisonEffect(ch, vict, poison->value[0], poison->value[1]);
      }

   /* return TRUE if the vict was killed within this procedure
    * NOTE: It still returns FALSE for the instant death type of poisons
    *       which is what we want since its handled back in damage()      */
   if(GET_STAT(vict) == STAT_DEAD)
      return TRUE;

   return FALSE;

}



/* Have 'ch' pickup one weapon which it dropped.
 * If none can be found in the room, return FALSE; otherwise TRUE.
 *   --TAM
 */

bool PickupDisarmedWeapon(P_char ch)
{
   P_obj obj_i;
   bool retval = FALSE, done = FALSE;
   char arg[MAX_INPUT_LENGTH];

   for(obj_i = world[ch->in_room].contents; obj_i && !done; obj_i = obj_i->next_content)
      {

      if(IS_SET(obj_i->extra_flags, ITEM_WAS_DISARMED) && (obj_i->last_to_hold == ch))
         {
         obj_from_room(obj_i);
         obj_to_char(obj_i, ch);

         if(!IS_FIGHTING(ch))
            {
            act("$n eagerly reaches for $o.", FALSE, ch, obj_i, 0, TO_ROOM);
            }
         else
            {
            act("$n extends $s hands, finally getting a steady hold of $o.", FALSE, ch, obj_i, 0, TO_ROOM);
            }

         REMOVE_BIT(obj_i->extra_flags, ITEM_WAS_DISARMED);

         if(obj_i->name)
            sprintf(arg, "%s", obj_i->name);

         if(strlen(arg) > 64)
            logit(LOG_DEBUG, "pickupdisarm sprintf assigning bogus string obj_i->name");

         if(IS_SET(obj_i->wear_flags, ITEM_WIELD))
            do_wield(ch, arg, -4);

         done = TRUE;
         retval = TRUE;
         }
      }

   return retval;
}

/* Make sure 'ch' is still fighting someone.  If not, disarm penalties don't apply anymore so remove them.
 *  --TAM
 */

bool DisarmCheck(P_char ch)
{
   bool retval;
   struct affected_type *af, *af_next;

   if(ch->specials.fighting == NULL)
      {
      ch->specials.action_delays[ACT_DELAY_FUMBLING_PRIM] = 0;
      ch->specials.action_delays[ACT_DELAY_DROPPED_PRIM] = 0;
      ch->specials.action_delays[ACT_DELAY_FUMBLING_SECOND] = 0;
      ch->specials.action_delays[ACT_DELAY_DROPPED_SECOND] = 0;

      if(IS_AFFECTED(ch, AFF_DROPPED_PRIM) || IS_AFFECTED(ch, AFF_DROPPED_SECOND))
         {
         while(PickupDisarmedWeapon(ch));

         for(af = ch->affected; af; af = af_next)
            {
            af_next = af->next;
            if(af->type == SKILL_DISARM_DROPPED_WEAP)
               {
               affect_remove(ch, af);
               }
            }
         }
      if(IS_AFFECTED(ch, AFF_FUMBLING_PRIM) || IS_AFFECTED(ch, AFF_FUMBLING_SECOND))
         {
         for(af = ch->affected; af; af = af_next)
            {
            af_next = af->next;
            if(af->type == SKILL_DISARM_FUMBLING_WEAP)
               {
               affect_remove(ch, af);
               }
            }
         }
      retval = TRUE;
      }
   else
      {
      retval = FALSE;
      }

   return retval;
}

void HandleFumblingWeapon(P_char ch, int action_delay_type, int affect_type)
{
   bool done = FALSE;
   struct affected_type *af, *af_next;

   --ch->specials.action_delays[(int) action_delay_type];

   if(ch->specials.action_delays[(int) action_delay_type] == 0)
      {
      for(af = ch->affected; !done && af; af = af_next)
         {
         af_next = af->next;
         if(af->type == SKILL_DISARM_FUMBLING_WEAP && IS_CSET(af->sets_affs, affect_type))
            {
            affect_remove(ch, af);
            done = TRUE;
            }
         }

      act("$n finally regains full control of $s weapon.", FALSE, ch, 0, 0, TO_ROOM);
      act("You finally regain full control of your weapon.", FALSE, ch, 0, 0, TO_CHAR);
      }
   else
      {
      act("$n is fumbling for a steady hold of $s weapon.", FALSE, ch, 0, 0, TO_ROOM);
      act("You can't seem to get a firm, steady hold of your weapon.", FALSE, ch, 0, 0, TO_CHAR);
      }
}

void HandleDroppedWeapon(P_char ch, int action_delay_type, int affect_type)
{
   bool get_weap = FALSE, aff_drop_expired = FALSE, done;
   struct affected_type *af, *af_next;

   --ch->specials.action_delays[(int) action_delay_type];

   if(IS_NPC(ch))
      {
      if(ch->specials.action_delays[(int) action_delay_type] == 0)
         {
         get_weap = TRUE;
         }
      }
   else
      {
      if(ch->specials.action_delays[(int) action_delay_type] == 0)
         {
         aff_drop_expired = TRUE;
         }
      }

   /* Here, if get_weap is true, it's time for a _MOB_ to look in room for its dropped weapon.
      When none can be found, another char picked it up, so it stops trying for it and all penalties
      don't apply anymore.  this is why the drop affect expires regardless. */

   if(get_weap)
      {
      if(PickupDisarmedWeapon(ch) == FALSE)
         {
         ch->specials.action_delays[(int) action_delay_type] = 0;
         }
      aff_drop_expired = TRUE;
      }
   else
      {                      /* if time to look for MOB's dislodged item */
      if(IS_NPC(ch))
         {
         act("$n struggles to regain $s weapon.", FALSE, ch, 0, 0, TO_ROOM);
         }
      }

   /* remove either
      a) all the affect for dropped weapon if either all weapons were recovered or the weapons to be
      recovered are no longer in the room or
      b) some of the affect for dropped weapons if all weapons haven't been recovered. */

   if(aff_drop_expired)
      {
      for(af = ch->affected, done = FALSE; !done && af; af = af_next)
         {
         af_next = af->next;
         if(af->type == SKILL_DISARM_DROPPED_WEAP && IS_CSET(af->sets_affs, affect_type))
            {
            affect_remove(ch, af);
            done = TRUE;
            }
         }
      }
}

/* This function serves to allow:

 *  1) MOBs/PCs to have their penalties removed upon recovery from a fumble.
 *     If they're fighting no one (ie. opponent fleed), they recover immediately.
 *  2) MOBs to pick up and re-wield both weapons when their ACT_DELAY_DROPPED_WEAP action_delay[] expires.
 *
 *  PCs don't need automatic weapon recovery, as they can manually do it.
 *
 *  This function is called from perform_violence(), and thus is only called
 *  when the PC/MOB (whose ACT_DELAY_FUMBLING_* or ACT_DELAY_DROPPED_*
 *  action_delays are still valid) is in combat.  Since 'disarm' can't be used
 *  until after combat has been initiated, and the affects it produces are
 *  really only useful while in combat; so when the affected PC/MOB's assailant
 *  has fled the room, it'll be perfectly fine to just clear all AFFECT-flags
 *  and clear all action_delays[] associated with 'disarm'.  This includes when
 *  a weapon is dislodged and laying on the ground--the disarmed MOB will
 *  immediately pick it up, and the disarmed PC may immediately pick his/hers
 *  up.
 *
 *  Note: MOB/PCs can NOT be both FUMBLING_WEAP and DROPPED_WEAP for same weap.
 *
 *  Return TRUE if the PC/MOB can attack.  Otherwise, return FALSE.
 */

bool DisarmRecovery(P_char ch)
{
   bool retval = FALSE;

   /* if not fumbling nor regaining dropped weap, let em strike back */
   if(ch->specials.action_delays[ACT_DELAY_FUMBLING_PRIM] == 0 &&
      ch->specials.action_delays[ACT_DELAY_FUMBLING_SECOND] == 0 &&
      ch->specials.action_delays[ACT_DELAY_DROPPED_PRIM] == 0 &&
      ch->specials.action_delays[ACT_DELAY_DROPPED_SECOND] == 0)
      {
      retval = TRUE;
      }
   /* if PC/MOB isn't fighting anyone anymore (opponent fleed), remove
      all affect-flags and pick up all dropped weapons */

   if(DisarmCheck(ch))
      {
      retval = TRUE;
      }
   /* handle PC/MOB who is fumbling their primary weapon */

   if(ch->specials.action_delays[ACT_DELAY_FUMBLING_PRIM] > 0 && AWAKE(ch))
      {
      HandleFumblingWeapon(ch, ACT_DELAY_FUMBLING_PRIM, AFF_FUMBLING_PRIM);
      retval = TRUE;
      }
   /* handle PCs/MOBs that are fumbling their secondary weapon */

   if(ch->specials.action_delays[ACT_DELAY_FUMBLING_SECOND] > 0 && AWAKE(ch))
      {
      HandleFumblingWeapon(ch, ACT_DELAY_FUMBLING_SECOND, AFF_FUMBLING_SECOND);
      retval = TRUE;
      }
   /* handle PC/MOB who has been disarmed of their primary weapon */

   if(ch->specials.action_delays[ACT_DELAY_DROPPED_PRIM] > 0 && AWAKE(ch))
      {
      HandleDroppedWeapon(ch, ACT_DELAY_DROPPED_PRIM, AFF_DROPPED_PRIM);
      }
   /* handle PC/MOB who has been disarmed of their secondary weapon */

   if(ch->specials.action_delays[ACT_DELAY_DROPPED_SECOND] > 0 && AWAKE(ch))
      {
      HandleDroppedWeapon(ch, ACT_DELAY_DROPPED_SECOND, AFF_DROPPED_SECOND);
      }
   /* this point can only be reached if MOB/PC is affected by disarm in some way */

   return retval;
}

void StopAllAttackers(P_char ch)
{
   P_char t_ch, hold;

   if(!ch)
      return;

   for(t_ch = combat_list; t_ch; t_ch = hold)
      {
      hold = t_ch->specials.next_fighting;
      if(t_ch->specials.fighting == ch)
         stop_fighting(t_ch);
      }
}

void StopMercifulAttackers(P_char ch)
{
   P_char t_ch, hold;

   if(!ch)
      return;

   for(t_ch = combat_list; t_ch; t_ch = hold)
      {
      hold = t_ch->specials.next_fighting;
      if((t_ch->specials.fighting == ch) && !affected_by_spell(t_ch, SKILL_BERSERK) &&
         ((IS_PC(t_ch) && !IS_CSET(t_ch->only.pc->pcact, PLR_VICIOUS)) ||
          (IS_NPC(t_ch) && !is_aggr_to(t_ch, ch))))
         {
         stop_fighting(t_ch);
         if(IS_CSET(ch->specials.affects, AFF_GARROTING))
            REMOVE_CBIT(ch->specials.affects, AFF_GARROTING);
         }
      }
}

/* picks a new random target for mayhem */

void ReTarget(P_char ch)
{
   P_char target;
   char buf[255];

   /* no need to retarget when target _Does_ exist */
   if(ch->specials.fighting)
      return;

   /* retarget blues: */
   if((ch->in_room < 0) || (ch->in_room > 65536))
      return;
   if(!MIN_POS(ch, POS_STANDING + STAT_RESTING))
      return;
   target = PickTarget(ch);
   if(!target)
      return;
   if(IS_NPC(ch))
      MobStartFight(ch, target);
   else
      {
      sprintf(buf, "hit %s", GET_NAME(target));
      command_interpreter(ch, buf);
      }

   debuglog(51, DS_SWITCH, "ReTarget(): [%s] to [%s]", GET_NAME(ch), GET_NAME(target));
}

/* PhasedAttack handles attack skills without using random numbers, used for
   double and dual now. JAB */

bool PhasedAttack(P_char ch, int skill)
{
   int timing, learned = 0, r_val = FALSE;

   if((skill == SKILL_DUAL_WIELD) &&
      (!ch->equipment[PRIMARY_WEAPON] || !ch->equipment[SECONDARY_WEAPON] || ch->equipment[WEAR_SHIELD] ||
       ch->equipment[HOLD]))
      return 0;

   if(IS_PC(ch))
      learned = (byte) (GET_CHAR_SKILL(ch, skill));
   else if(skill == SKILL_DOUBLE_ATTACK)
      {
      if((IS_WARRIOR(ch) && (GET_LEVEL(ch) > 14)) ||
         (IS_THIEF(ch) && GET_LEVEL(ch) > 29))
         learned = GET_LEVEL(ch) << 1;
      }
   else if(skill == SKILL_DUAL_WIELD)
      {
      if((IS_WARRIOR(ch) && (GET_LEVEL(ch) > 14)) ||
         (IS_THIEF(ch) && GET_LEVEL(ch) > 20))
         learned = GET_LEVEL(ch) << 1;
      }

   if(phase % 2)
      {
      if((skill == SKILL_DUAL_WIELD) && (number(1, 200) < learned))
         CharSkillGainChance(ch, SKILL_DUAL_WIELD, -1);
      if((skill == SKILL_DOUBLE_ATTACK) && (number(1, 200) < learned))
         CharSkillGainChance(ch, SKILL_DOUBLE_ATTACK, -1);
      }

   if((skill == SKILL_DUAL_WIELD) && IS_AFFECTED(ch, AFF_HASTE))
      learned = BOUNDED(learned, learned * 2, 100);

   /* phase 0 to 9, timing 0 to 10 */
   timing = BOUNDED(0, (learned / 10) + (((learned % 10) > 4) ? 1 : 0), 10);

   switch(timing)
      {
      case 1:
         if(phase == 9)
            r_val = TRUE;
         break;
      case 2:
         if(!(phase % 5))
            r_val = TRUE;
         break;
      case 3:
         if(!(phase % 4))
            r_val = TRUE;
         break;
      case 4:
         if(!(phase % 3))
            r_val = TRUE;
         break;
      case 5:
         if(phase % 2)
            r_val = TRUE;
         break;
      case 6:
         if(phase % 3)
            r_val = TRUE;
         break;
      case 7:
         if(phase % 4)
            r_val = TRUE;
         break;
      case 8:
         if(phase % 5)
            r_val = TRUE;
         break;
      case 9:
         if(phase != 5)
            r_val = TRUE;
         break;
      case 10:
         r_val = TRUE;
         break;
      }

   return r_val;
}

int WeaponSkill_num(P_char ch, int slot)
{
   P_obj wpn = ch->equipment[slot];

   if(!wpn)
      return 0;
   if((wpn->type != ITEM_WEAPON) && (wpn->type != ITEM_FIREWEAPON))
      return 0;
   switch(wpn->value[3])
      {
      case 4:
      case 5:
      case 6:
      case 7:
         return IS_SET(wpn->extra_flags, ITEM_TWOHANDS) ? SKILL_2H_BLUDGEON : SKILL_1H_BLUDGEON;
      case 0:
      case 1:
      case 2:
      case 8:
      case 9:
      case 10:
         return IS_SET(wpn->extra_flags, ITEM_TWOHANDS) ? SKILL_2H_MISC : SKILL_1H_MISC;
      case 3:
         return IS_SET(wpn->extra_flags, ITEM_TWOHANDS) ? SKILL_2H_SLASHING : SKILL_1H_SLASHING;
      case 11:
         return IS_SET(wpn->extra_flags, ITEM_TWOHANDS) ? SKILL_2H_MISC : SKILL_1H_PIERCING;
      }
   return 0;
}
int WeaponSkill(P_char ch, int slot)
{
   int i = WeaponSkill_num(ch, slot);
   int value = 0;

   // Set weapon skill
   if(!i)
      value =
      (IS_NPC(ch) ? BOUNDED(3, (GET_LEVEL(ch) * 2), 95) : GET_CHAR_SKILL(ch, SKILL_MARTIAL_ARTS));
   else
      value =
      (IS_NPC(ch) ? BOUNDED(3, (GET_LEVEL(ch) * 2), 95) : GET_CHAR_SKILL(ch, i));

   return value;

}

/* victim is attacking ch, and ch will riposte it back to victim on success
   which refers to ch's weapon slot */
int TryRiposte(P_char ch, P_char victim, int which)
{
   int skl, riposte_msg_sent = FALSE;
   P_obj weapon = NULL;
   struct func_attachment *fn = NULL;

   if(IS_NPC(ch))
      skl = IS_WARRIOR(ch) && (GET_LEVEL(ch) > 24) ? GET_LEVEL(ch) * 2 : (GET_LEVEL(ch) - 25) / 2;
   else
      skl = GET_CHAR_SKILL(ch, SKILL_RIPOSTE);

   if(skl < 1)
      return FALSE;

   skl += GET_LEVEL(ch) - GET_LEVEL(victim) + WeaponSkill(ch, which) / 2;

   if(IS_FIGHTING(ch) && (victim != ch->specials.fighting))
      skl /= 2;

   if(number(0, 400) > skl)
      return FALSE;

   /* insert riposte proc check here -Azuth */
   weapon = victim->equipment[which];
   /* please leave this debug stuff here for a little bit, can go away later  -Azuth
      if(weapon)
         debuglog(51, DS_AZUTH, "%s attacked %s, but now %s is riposting it back at %s (weapon is %s)",
               GET_NAME(victim), GET_NAME(ch), GET_NAME(ch), GET_NAME(victim), weapon->short_description);
      else
         debuglog(51, DS_AZUTH, "%s attacked %s, but now %s is riposting it back at %s (but %s has no weapon so no proc)",
               GET_NAME(victim), GET_NAME(ch), GET_NAME(ch), GET_NAME(victim), GET_NAME(victim));
   */
   if(weapon)
      {
      if(obj_index[weapon->R_num].spec_flag & IDX_RIPOSTE)
         {
         fn = obj_index[weapon->R_num].func;
         do
            {
            if(fn->proc_flag & IDX_RIPOSTE)
               {
               c_act("$n deflects $N's blow, and strikes back at $N!", TRUE, ch, 0, victim, TO_NOTVICT, MELEE_DEF);
               c_act("$n deflects your blow, and strikes back at YOU!", TRUE, ch, 0, victim, TO_VICT, MELEE_DEF);
               c_act("You deflect $N's blow, and strike back at $N!", TRUE, ch, 0, victim, TO_CHAR, MELEE_DEF);
               riposte_msg_sent = TRUE;
               /* what does this do??? */
               if((*fn->func.obj) (weapon, victim, PROC_RIPOSTE, (char *) ch) || ch->in_room != victim->in_room)
                  {
                  if(!IS_DEAD(ch))
                     {
                     if(!IS_FIGHTING(ch))
                        set_fighting(ch, victim);

                     if(IS_PC(ch))
                        CharSkillGainChance(ch, SKILL_RIPOSTE, 5);

                     hit(ch, victim, TYPE_UNDEFINED);
                     }
                  return TRUE;
                  }
               }
            fn = fn->next;
            } while(fn);
         }
      }

   if(!riposte_msg_sent)
      {
      c_act("$n deflects $N's blow, and strikes back at $N!", TRUE, ch, 0, victim, TO_NOTVICT, MELEE_DEF);
      c_act("$n deflects your blow, and strikes back at YOU!", TRUE, ch, 0, victim, TO_VICT, MELEE_DEF);
      c_act("You deflect $N's blow, and strike back at $N!", TRUE, ch, 0, victim, TO_CHAR, MELEE_DEF);
      }

   if(!IS_FIGHTING(ch))
      set_fighting(ch, victim);

   if(IS_PC(ch))
      CharSkillGainChance(ch, SKILL_RIPOSTE, 5);

   hit(ch, victim, TYPE_UNDEFINED);
   return TRUE;
}

/*
   ** This function does all the necessary details for implementation
   ** of thief skill "dodging".  Details include print out messages,
   ** and recognization of "victim" being a thief and how learn s/he
   ** with "dodge".  Function return 1 if successful dodge, or 0
   ** otherwise.
 */

int dodgeSucceed(P_char victim, P_char attacker, int which, int mod)
{
   int percent, learned = 0, random_number, npc_skill = 0;
   bool uber_harmony = FALSE, uber_disruption = FALSE;

   if(IS_ENABLED(CODE_NEWDEFENSE))
      {
      return(attemptDodge(victim, attacker, which));
      }

   /* Pulled due to common opinion that after fix it's silly - Iyachtu */
#if 0
   if(IS_PC(victim) && !GET_CHAR_SKILL(victim, SKILL_DODGE))
      return FALSE;
#endif

   if(!MIN_POS(victim, POS_STANDING + STAT_RESTING) || IS_AFFECTED(victim, AFF_BLIND) ||
      IS_AFFECTED(victim, AFF_MAJOR_PARALYSIS) || IS_AFFECTED(victim, AFF_MINOR_PARALYSIS))
      return 0;

   if(IS_NPC(victim))
      {
      if(IS_THIEF(victim))
         npc_skill = 90;
      else if(IS_WARRIOR(victim))
         npc_skill = 65;
      else if(IS_CLERIC(victim))
         npc_skill = 50;
      else if(IS_PSIONICIST(victim))
         npc_skill = 45;
      else if(IS_MAGE(victim))
         npc_skill = 25;
      else
         return 0;
      }
   if(IS_PC(victim))
      learned = GET_CHAR_SKILL(victim, SKILL_DODGE);
   else
      learned = BOUNDED(0,((GET_LEVEL(victim) * npc_skill) / MAXLVL + number(-20, 20)), 90);

   /* main point: if the enemy's lousy shot, you dodge him fairly well. */

   percent = learned - WeaponSkill(attacker, which) / 2; /* range: -80 to 100 */
   percent += BOUNDED(1, STAT_INDEX(GET_C_AGI(victim)), 25);

   /* should be -80 to ~146 */
   percent += GET_LEVEL(victim) / ((GET_CLASS(victim) == CLASS_MONK) ? 2 : 3);
   percent -= GET_LEVEL(attacker) / 2;

   /* should be -110 to ~206 */

   if(percent > 0)
      percent += ((25 - load_modifier(victim)) / 5);

   /* dropped somewhat more */

   /* some chance to dodge _ANY_ attack, if yer skilled: */

   if(percent < (learned / 20))
      percent = learned / 20;

   percent = MIN(percent, learned);

   random_number = number(1, 120);

#ifdef NEW_BARD
   if(affected_by_song(victim, SONG_OF_DEFENSIVE_HARMONY))
      {
      percent += victim->bard_singing->song_singing->modifier;
      if(victim->bard_singing->song_singing->special)
         uber_harmony = TRUE;
      debuglog(51, DS_IYACHTU, "Dodge chance increased by %d to %d", victim->bard_singing->song_singing->modifier, percent);
      }
   else if(affected_by_song(victim, SONG_OF_DEFENSIVE_DISRUPTION))
      {
      percent -= victim->bard_singing->song_singing->modifier;
      if(victim->bard_singing->song_singing->special)
         uber_disruption = TRUE;
      debuglog(51, DS_IYACHTU, "Dodge chance decreased by %d to %d", victim->bard_singing->song_singing->modifier, percent);
      }
#endif

   // Modifier
   for(; mod; mod--)
      if(uber_harmony)
         percent = percent * 2 / 5;
      else if(uber_disruption)
         percent /= 4;
      else
         percent /= 3;

   if(IS_PC(victim))
      debuglog(51, DS_COMBAT_SKILLS, "%s DO = %d,  Roll = %d",GET_NAME(victim), percent, random_number);

   // Temp debug
   debuglog(51, DS_COMBAT, "CombatDodge: dodge chance: %d percent: %d for %s vs %s",
            random_number, percent, GET_NAME(attacker), GET_NAME(victim));

   if(random_number > percent)
      {
      return 0;
      }
   else
      CharSkillGainChance(victim, SKILL_DODGE, 0);

   /* Succeed */

   c_act("You dodge $n's vicious attack.", FALSE, attacker, 0, victim, TO_VICT, MELEE_DEF);
   c_act("$N dodges your futile attack.", FALSE, attacker, 0, victim, TO_CHAR, MELEE_DEF);
   c_act("$N dodges $n's attack.", FALSE, attacker, 0, victim, TO_NOTVICT, MELEE_DEF);
   if(!IS_FIGHTING(victim))
      set_fighting(victim, attacker);
   return 1;
}

/* ripped from dodgeSucceed for Warrior's ShieldBlock skill... - CRM */
int blockSucceed(P_char victim, P_char attacker, int which, int mod)
{
   P_obj shield = NULL;
   struct func_attachment *fn = NULL;
   int learned = 0, random_number, percent;
   bool uber_harmony = FALSE, uber_disruption = FALSE;
   int proc_msg_sent = FALSE;

   if(IS_ENABLED(CODE_NEWDEFENSE))
      {
      return(attemptShieldBlock(victim, attacker, which));
      }

   if(IS_PC(victim) && !GET_CHAR_SKILL(victim, SKILL_SHIELDBLOCK))
      return 0;

   if(IS_NPC(victim))
      {
      if(!IS_WARRIOR(victim))
         return 0;
      }

   /* Duh, need a shield for this. */
   shield = victim->equipment[WEAR_SHIELD];
   if(!shield)
      return 0;

   if(!MIN_POS(victim, POS_STANDING + STAT_RESTING) || IS_AFFECTED(victim, AFF_BLIND) ||
      IS_AFFECTED(victim, AFF_MAJOR_PARALYSIS) || IS_AFFECTED(victim, AFF_MINOR_PARALYSIS))
      return 0;


   if(IS_PC(victim))
      learned = GET_CHAR_SKILL(victim, SKILL_SHIELDBLOCK);
   else
      learned = BOUNDED(0,((GET_LEVEL(victim) * 90) / MAXLVL + number(-20, 20)), 90);

   /* main point: if the enemy's a lousy shot, you block him fairly well. */

   percent = learned - WeaponSkill(attacker, which) / 2; /* range: -80 to 100 */
   percent += STAT_INDEX(GET_C_STR(victim));

   /* should be -80 to ~146 */
   percent += GET_LEVEL(victim) / ((GET_CLASS(victim) == CLASS_MONK) ? 2 : 3);
   percent -= GET_LEVEL(attacker) / 2;

   /* should be -110 to ~206 */
   if(percent > 0)
      percent += ((25 - load_modifier(victim)) / 5);


   /* some chance to block _ANY_ attack, if yer skilled: */

   if(percent < (learned / 20))
      percent = learned / 20;

   percent = MIN(percent, learned);

   debuglog(51, DS_DEFENSE, "%s (&+r%d&N) SHIELDBLOCK vs. %s (&+r%d&N):  Learned (&+L%d&N) | Percent (&+L%d&N) | "
            "VictimStrBonus (&+L%d&N) | VictimLevelBonus(&+L%d&N) | VictimLoadPenalty (&+L%d&N) | "
            "AttackerLevelPenalty (&+L%d&N) | AttackerWeaponPenalty (&+L%d&N)",
            GET_NAME(victim), GET_LEVEL(victim), GET_NAME(attacker), GET_LEVEL(attacker),
            learned, percent, STAT_INDEX(GET_C_STR(victim)), GET_LEVEL(victim) / 3,
            ((25 - load_modifier(victim)) / 5), GET_LEVEL(attacker) / 2,
            (WeaponSkill(attacker, which) / 2));

   random_number = number(1, 120);


#ifdef NEW_BARD
   if(affected_by_song(victim, SONG_OF_DEFENSIVE_HARMONY))
      {
      percent += victim->bard_singing->song_singing->modifier;
      if(victim->bard_singing->song_singing->special)
         uber_harmony = TRUE;
      debuglog(51, DS_IYACHTU, "Shieldblock chance increased by %d to %d", victim->bard_singing->song_singing->modifier, percent);
      }
   else if(affected_by_song(victim, SONG_OF_DEFENSIVE_DISRUPTION))
      {
      percent -= victim->bard_singing->song_singing->modifier;
      if(victim->bard_singing->song_singing->special)
         uber_disruption = TRUE;
      debuglog(51, DS_IYACHTU, "Shieldblock chance decreased by %d to %d", victim->bard_singing->song_singing->modifier, percent);
      }
#endif

   // Modifier
   for(; mod; mod--)
      if(uber_harmony)
         percent = percent * 2 / 5;
      else if(uber_disruption)
         percent /= 4;
      else
         percent /= 3;

   if(IS_PC(victim))
      debuglog(51, DS_COMBAT_SKILLS, "%s SB = %d,  Roll = %d",GET_NAME(victim), percent, random_number);

   if(random_number > percent)
      {
      return 0;
      }
   else
      CharSkillGainChance(victim, SKILL_SHIELDBLOCK, 0);

   /* insert shieldblock proc check here -Azuth */
   if(obj_index[shield->R_num].spec_flag & IDX_SBLOCK)
      {
      fn = obj_index[shield->R_num].func;
      do
         {
         if(fn->proc_flag & IDX_SBLOCK)
            {                                                                      /* what does this do??? */
            c_act("You block $n's attack with your shield!", FALSE, attacker, 0, victim, TO_VICT, MELEE_DEF);
            c_act("$N blocks your futile attempt with $S shield!", FALSE, attacker, 0, victim, TO_CHAR, MELEE_DEF);
            c_act("$N blocks $n's attack with $S shield!", FALSE, attacker, 0, victim, TO_NOTVICT, MELEE_DEF);
            proc_msg_sent = TRUE;

            if((*fn->func.obj) (shield, victim, PROC_SBLOCK, (char *) attacker) || victim->in_room != attacker->in_room)
               {
               if(!IS_DEAD(attacker))
                  {
                  Do_Object_Decay(victim, victim->equipment[WEAR_SHIELD], DECAY_SHIELDBLOCK, victim->in_room);

                  if(!IS_FIGHTING(victim))
                     set_fighting(victim, attacker);
                  }

               return TRUE;
               }
            }
         fn = fn->next;
         } while(fn);
      }

   /* Succeed */
   if(!proc_msg_sent)
      {
      c_act("You block $n's attack with your shield!", FALSE, attacker, 0, victim, TO_VICT, MELEE_DEF);
      c_act("$N blocks your futile attempt with $S shield!", FALSE, attacker, 0, victim, TO_CHAR, MELEE_DEF);
      c_act("$N blocks $n's attack with $S shield!", FALSE, attacker, 0, victim, TO_NOTVICT, MELEE_DEF);
      }

   /* better check how our shield is holding up... */
   Do_Object_Decay(victim, victim->equipment[WEAR_SHIELD], DECAY_SHIELDBLOCK,
                   victim->in_room);

   if(!IS_FIGHTING(victim))
      set_fighting(victim, attacker);
   return 1;
}

int attemptParry(P_char victim, P_char attacker, int which)
{
   int skill, chance, random_number, blockType = 0;

   // Non-Warrior Mobiles cannot parry
   if(IS_NPC(victim) && !IS_WARRIOR(victim))
      return FALSE;

   // Victim status checks
   if(!AWAKE(victim) || IS_AFFECTED(victim, AFF_MAJOR_PARALYSIS) ||
      IS_AFFECTED(victim, AFF_MINOR_PARALYSIS))
      return FALSE;

   // Need something to parry with - no shields though.
   if(!victim->equipment[WIELD] && !victim->equipment[SECONDARY_WEAPON])
      return FALSE;

   // Get or set the skill level for the victim
   if(IS_PC(victim))
      skill = GET_CHAR_SKILL(victim, SKILL_PARRY);
   else
      skill = BOUNDED(0,((GET_LEVEL(victim) * 90) / MAXLVL + number(-20, 20)), IS_WARRIOR(victim) ? 99 : 0);

   // No skill, duh
   if(skill == 0)
      return FALSE;

   // Initial chance is equal to skill
   chance = skill;

   // Apply general defensive skill mods
   chance = DefenseMod(victim, attacker, which, chance, SKILL_PARRY);

   // Roll the dice!
   random_number = number(0, 120);

   debuglog(51, DS_DEFENSE, "Chance: (&+R%d&N) | Random: (&+R%d&N)", chance, random_number);

   // So sorry, you failed.
   if(chance < (random_number))
      {
      return FALSE;
      }


   // Determine partial or full block
   if(((chance * cc_def_saveFactor) / 100) < (random_number))
      blockType = DEFENSE_PARTIAL;
   else
      blockType = DEFENSE_FULL;

   victim->specials.avoid_counter++;

   CharSkillGainChance(victim, SKILL_PARRY, 0);
   CharSkillGainChance(victim, SKILL_DEFENSE, 0);

   return blockType;

   // Fin
}

int attemptShieldBlock(P_char victim, P_char attacker, int which)
{
   int skill, chance, random_number, blockType = 0;

   // Non-Warrior Mobiles cannot shieldblock
   if(IS_NPC(victim) && !IS_WARRIOR(victim))
      return FALSE;

   // Victim status checks
   if(!AWAKE(victim) || IS_AFFECTED(victim, AFF_MAJOR_PARALYSIS) ||
      IS_AFFECTED(victim, AFF_MINOR_PARALYSIS))
      return FALSE;

   // Need a shield to block
   if(!victim->equipment[WEAR_SHIELD])
      return FALSE;

   // Get or set the skill level for the victim
   if(IS_PC(victim))
      skill = GET_CHAR_SKILL(victim, SKILL_SHIELDBLOCK);
   else
      skill = BOUNDED(0,((GET_LEVEL(victim) * 90) / MAXLVL + number(-20, 20)), IS_WARRIOR(victim) ? 99 : 0);

   // No skill, duh
   if(skill == 0)
      return FALSE;

   // Initial chance is equal to skill
   chance = skill;

   // Apply general defensive skill mods
   chance = DefenseMod(victim, attacker, which, chance, SKILL_SHIELDBLOCK);

   // Roll the dice!
   random_number = number(0, 120);

   debuglog(51, DS_DEFENSE, "Chance: (&+R%d&N) | Random: (&+R%d&N)", chance, random_number);

   // So sorry, you failed.
   if(chance < (random_number))
      {
      return FALSE;
      }

   // Determine partial or full block
   if(((chance * cc_def_saveFactor) / 100) < (random_number))
      blockType = DEFENSE_PARTIAL;
   else
      blockType = DEFENSE_FULL;

   victim->specials.avoid_counter++;

   return blockType;

   // Fin
}

int attemptMountBlock(P_char victim, P_char attacker, int which)
{
   int skill, chance, random_number, blockType = 0;

   // Non-Warrior Mobiles cannot mountblock
   if(IS_NPC(victim) && !IS_WARRIOR(victim))
      return FALSE;

   // Victim status checks
   if(!AWAKE(victim) || IS_AFFECTED(victim, AFF_MAJOR_PARALYSIS) ||
      IS_AFFECTED(victim, AFF_MINOR_PARALYSIS))
      return FALSE;

   // Need to be riding a mount
   if(!GET_MOUNT(victim))
      return FALSE;

   // Get or set the skill level for the victim
   if(IS_PC(victim))
      skill = GET_CHAR_SKILL(victim, SKILL_MOUNTED_COMBAT);
   else
      skill = BOUNDED(0,((GET_LEVEL(victim) * 90) / MAXLVL + number(-20, 20)), IS_WARRIOR(victim) ? 99 : 0);

   // No skill, duh
   if(skill == 0)
      return FALSE;

   // Initial chance is equal to skill
   chance = skill;

   // Apply general defensive skill mods
   chance = DefenseMod(victim, attacker, which, chance, SKILL_MOUNTED_COMBAT);

   // Roll the dice!
   random_number = number(0, 120);

   // So sorry, you failed.
   if(chance < (random_number))
      {
      return FALSE;
      }

   // Determine partial or full block
   if(((chance * cc_def_saveFactor) / 100) < (random_number))
      blockType = DEFENSE_PARTIAL;
   else
      blockType = DEFENSE_FULL;

   victim->specials.avoid_counter++;

   return blockType;
}

int attemptDodge(P_char victim, P_char attacker, int which)
{
   int skill, chance, random_number, blockType = 0;
   //  P_obj weapon = NULL;
   //  struct func_attachment *fn = NULL;
   //  bool uber_harmony = FALSE, uber_disruption = FALSE;

   // Victim status checks
   if(!AWAKE(victim) || IS_AFFECTED(victim, AFF_MAJOR_PARALYSIS) ||
      IS_AFFECTED(victim, AFF_MINOR_PARALYSIS))
      return FALSE;

   // Set NPC base skill
   if(IS_NPC(victim))
      {
      if(IS_THIEF(victim))
         skill = 90;
      else if(IS_WARRIOR(victim))
         skill = 65;
      else if(IS_CLERIC(victim))
         skill = 50;
      else if(IS_PSIONICIST(victim))
         skill = 45;
      else if(IS_MAGE(victim))
         skill = 25;
      else
         return 0;
      }

   // Get or set the skill level for the victim
   if(IS_PC(victim))
      skill = GET_CHAR_SKILL(victim, SKILL_DODGE);
   else
      skill = BOUNDED(0,((GET_LEVEL(victim) * skill) / MAXLVL + number(-20, 20)), 90);

   // No skill, duh
   if(skill == 0)
      return FALSE;

   // Initial chance is equal to skill
   chance = skill;

   // Apply general defensive skill mods
   chance = DefenseMod(victim, attacker, which, chance, SKILL_DODGE);

   // Roll the dice!
   random_number = number(0, 120);

   debuglog(51, DS_DEFENSE, "Chance: (&+R%d&N) | Random: (&+R%d&N)", chance, random_number);

   // So sorry, you failed.
   if(chance < (random_number))
      {
      return FALSE;
      }

   // Determine partial or full bloc
   if(((chance * cc_def_saveFactor) / 100) < (random_number))
      blockType = DEFENSE_PARTIAL;
   else
      blockType = DEFENSE_FULL;

   // PC Rogues never half-dodge
   if(IS_PC(victim) && (GET_CLASS(victim) == CLASS_ROGUE))
      blockType = DEFENSE_FULL;

   victim->specials.avoid_counter++;

   return blockType;
}

// DefeneMsg():   This central function displays all of the defensive skill messages.
//                Done this way so we can process all skills before deciding on an
//                outcome and showing the appropriate msg.
void DefenseProcess(P_char victim, P_char attacker, int skill_type, int block_type, int which)
{

   // Sanity Check
   if(!(victim) || !(attacker) || !skill_type || !block_type)
      {
      logit(LOG_EXIT, "dump_core(): bogus parms in DefenseProcess");
      dump_core();
      }

   //debuglog(51, DS_DEFENSE, "Entering DP: %s vs. %s, %d skill %d block",
   //         GET_NAME(victim), GET_NAME(attacker), skill_type, block_type);

   switch(skill_type)
      {

      case SKILL_PARRY:
         if(block_type == DEFENSE_FULL)
            {

            // On successful parry, victim gets chance at riposte
            if(TryRiposte(victim, attacker, which))
               return;

            // Check Procs
            if((DefenseProcCheck(victim, attacker, victim->equipment[WIELD], IDX_PARRY)))
               return;

            // Show Msgs
            c_act("You parry $n's lunge at you.",        FALSE, attacker, 0, victim, TO_VICT, MELEE_DEF);
            c_act("$N parries your futile lunge at $M.", FALSE, attacker, 0, victim, TO_CHAR, MELEE_DEF);
            c_act("$N parries $n's lunge at $M.",        FALSE, attacker, 0, victim, TO_NOTVICT, MELEE_DEF);
            }
         else
            {
            c_act("You partially deflect $n's lunge at you.",        FALSE, attacker, 0, victim, TO_VICT, MELEE_DEF);
            c_act("$N partially deflects your lunge at $M.", FALSE, attacker, 0, victim, TO_CHAR, MELEE_DEF);
            c_act("$N partially deflect $n's lunge at $M.",        FALSE, attacker, 0, victim, TO_NOTVICT, MELEE_DEF);
            }
         break;

      case SKILL_SHIELDBLOCK:
         if(block_type == DEFENSE_FULL)
            {

            // Check Procs
            if((DefenseProcCheck(victim, attacker, victim->equipment[WEAR_SHIELD], IDX_SBLOCK)))
               return;

            // Show Msgs
            c_act("You block $n's attack with your shield!",       FALSE, attacker, 0, victim, TO_VICT, MELEE_DEF);
            c_act("$N blocks your futile attempt with $S shield!", FALSE, attacker, 0, victim, TO_CHAR, MELEE_DEF);
            c_act("$N blocks $n's attack with $S shield!",         FALSE, attacker, 0, victim, TO_NOTVICT, MELEE_DEF);
            }
         else
            {
            c_act("$n's attack glances off your shield, but finds it's mark!", FALSE, attacker, 0, victim, TO_VICT, MELEE_DEF);
            c_act("Your attack glances off $N's shield, but find it's mark!", FALSE, attacker, 0, victim, TO_CHAR, MELEE_DEF);
            c_act("$N partially blocks $n's attack with $S shield!",          FALSE, attacker, 0, victim, TO_NOTVICT, MELEE_DEF);
            }
         break;

      case SKILL_MOUNTED_COMBAT:
         if(block_type == DEFENSE_FULL)
            {

            // Check Procs
            if((DefenseProcCheck(victim, attacker, (GET_MOUNT(victim))->equipment[WEAR_ABOUT], IDX_FBLOCK)))
               return;

            // Show Msgs
            c_act("You maneuver your mount to avoid $n's attack!", FALSE, attacker, 0, victim, TO_VICT, MELEE_DEF);
            c_act("$N maneuvers $S mount to avoid your attack!",   FALSE, attacker, 0, victim, TO_CHAR, MELEE_DEF);
            c_act("$N maneuvers $S mount to avoid $n's attack!",   FALSE, attacker, 0, victim, TO_NOTVICT, MELEE_DEF);
            }
         else
            {
            c_act("$n's attack only grazes you as you maneuver your mount!", FALSE, attacker, 0, victim, TO_VICT, MELEE_DEF);
            c_act("Your attack only grazes $N as $E maneuvers his mount!",   FALSE, attacker, 0, victim, TO_CHAR, MELEE_DEF);
            c_act("$N maneuvers $S mount and is only grazed by $n's attack!",   FALSE, attacker, 0, victim, TO_NOTVICT, MELEE_DEF);
            }
         break;

      case SKILL_DODGE:
         if(block_type == DEFENSE_FULL)
            {
            c_act("You dodge $n's vicious attack.", FALSE, attacker, 0, victim, TO_VICT, MELEE_DEF);
            c_act("$N dodges your futile attack.",  FALSE, attacker, 0, victim, TO_CHAR, MELEE_DEF);
            c_act("$N dodges $n's attack.",         FALSE, attacker, 0, victim, TO_NOTVICT, MELEE_DEF);
            }
         else
            {
            c_act("$n's attack only grazes you as you dodge aside!", FALSE, attacker, 0, victim, TO_VICT, MELEE_DEF);
            c_act("Your attack only grazes $N as $E dodges aside!",  FALSE, attacker, 0, victim, TO_CHAR, MELEE_DEF);
            c_act("$n's attack only grazes $N as $E dodges aside!",  FALSE, attacker, 0, victim, TO_NOTVICT, MELEE_DEF);
            }
         break;

      default:
         logit(LOG_EXIT, "dump_core(): Invalid defensive skill passed to DefenseMessage");
         dump_core();
         break;
      }

   // Skill Gain
   CharSkillGainChance(victim, skill_type, -25);
   CharSkillGainChance(victim, SKILL_DEFENSE, 0);

   // Set fighting if not already
   if(!IS_FIGHTING(victim))
      set_fighting(victim, attacker);

   // Done!
   return;


}
// DefenseMod():  This central function modifies the defensive skill chance and returns
//                the new chance to the caller.

int DefenseMod(P_char victim, P_char attacker, int which, int base_chance, int type)
{
   int chance = 0, att_mod = 0, def_skill = 0, avoid_count = 0, blind_mod = 0;
   bool uber_harmony = FALSE, uber_disruption = FALSE;

   // Sanity Check
   if(!(chance = base_chance))
      return 0;

   // Find the appropriate attribute bonus
   switch(type)
      {

      case SKILL_PARRY:
         att_mod = STAT_INDEX(GET_C_DEX(victim));
         break;

      case SKILL_SHIELDBLOCK:
         att_mod = STAT_INDEX(GET_C_STR(victim));
         break;

      case SKILL_MOUNTED_COMBAT:
         att_mod = STAT_INDEX(GET_C_CON(victim));
         break;

      case SKILL_DODGE:
         att_mod = STAT_INDEX(GET_C_AGI(victim));
         break;

      default:
         dump_core();
         break;
      }

   // Add the attribute bonus
   chance += ((att_mod * cc_def_attFactor) / 100);

   // Find Attacker's weapon skill penalty and subtract it
   chance -= ((WeaponSkill(attacker, which) * cc_def_wpnFactor) / 100);

   // Positional modifiers
   if(GET_POS(victim) == POS_KNEELING)
      chance *= .75;

   if(GET_POS(victim) == POS_PRONE)
      chance /= 2;

   // Blind Mod - modified by blindfighting skill, or mob level
   if(IS_AFFECTED(victim, AFF_BLIND))
      {
      if(IS_PC(victim))
         blind_mod = (50 - (GET_CHAR_SKILL(victim, SKILL_BLINDFIGHTING) / 4));
      else
         blind_mod = (50 - (IS_WARRIOR(victim) ? (CHECK_BLINDFIGHTING(victim) / 3) : (GET_LEVEL(victim) / 4)));

      chance -= blind_mod;
      }

   // Non-magical modifiers finished, bound it to original chance max
   chance = BOUNDED(1, chance, base_chance);

   // Bard Song modifiers

   // Defensive Harmony bonus
   if(affected_by_song(victim, SONG_OF_DEFENSIVE_HARMONY))
      {
      chance += victim->bard_singing->song_singing->modifier;
      if(victim->bard_singing->song_singing->special)
         uber_harmony = TRUE;
      }

   // Defensive Disruption penalty
   if(affected_by_song(victim, SONG_OF_DEFENSIVE_DISRUPTION))
      {
      chance -= victim->bard_singing->song_singing->modifier;
      if(victim->bard_singing->song_singing->special)
         uber_disruption = TRUE;
      }

   // Finally, apply the penalty for successive blocks
   // If you have the defense skill, we use that to find the mod - if not,
   // It's a straight up (-75%) modifier for every avoided hit.

   if(IS_PC(victim))
      def_skill = BOUNDED(25, GET_CHAR_SKILL(victim, SKILL_DEFENSE), 90);
   else
      def_skill = BOUNDED(25, (GET_LEVEL(victim) * (IS_WARRIOR(victim) ? 2 : 1)), 95);

   if(!def_skill)
      def_skill = 25;

   // Get # of hits already blocked this round
   avoid_count = victim->specials.avoid_counter;

   // Apply the penalty
   for(; avoid_count; avoid_count--)
      {
      chance = (chance * def_skill / 100);
      def_skill -= cc_def_penaltySlope;
      }

   // Debugging
   debuglog(51, DS_DEFENSE, "%s (&+r%d&N) &+L%s&N vs. %s (&+r%d&N): Base (&+L%d&N) | Mod (&+L%d&N) | Count (&+L%d&N)"
            " | AttMod (&+L%d&N) | Def-Skill (&+L%d&N) | AttackerWeaponSkill (&+L%d&N) | BlindMod (&+L%d&N)",
            GET_NAME(victim), GET_LEVEL(victim), skills[pindex2Skill[type]].name, GET_NAME(attacker),
            GET_LEVEL(attacker), base_chance, chance, victim->specials.avoid_counter,
            ((att_mod * cc_def_attFactor) / 100), def_skill,
            ((WeaponSkill(attacker, which) * cc_def_wpnFactor) / 100), blind_mod);

   // All done, return the modified chance
   return(chance);
}

bool DefenseProcCheck(P_char ch, P_char attacker, P_obj obj, int idx_type)
{
   struct func_attachment *fn = NULL;
   bool stop = FALSE;
   int proc_type = 0;

   // Sanity Check
   if(!(ch) || !(idx_type))
      {
      logit(LOG_EXIT, "Bogus params for DefenseProcCheck");
      dump_core();
      }

   if(!obj)
      return FALSE;

   switch(idx_type)
      {
      case IDX_PARRY:
         proc_type = PROC_PARRY;
         break;

      case IDX_SBLOCK:
         proc_type = PROC_SBLOCK;
         break;

      case IDX_FBLOCK:
         proc_type = PROC_FBLOCK;
         break;

      default:
         dump_core();
         break;
      }

   if(obj_index[obj->R_num].spec_flag & idx_type)
      {
      fn = obj_index[obj->R_num].func;
      do
         {
         if(fn->proc_flag & idx_type)
            {
            if((*fn->func.obj) (obj, ch, proc_type, (char *) attacker) || ch->in_room != attacker->in_room)
               {
               stop = TRUE;
               break;
               }
            }
         fn = fn->next;
         } while(fn);
      }

   if(stop == TRUE && !IS_DEAD(attacker) && !IS_FIGHTING(ch))
      set_fighting(ch, attacker);

   return(stop);
}


int parrySucceed(P_char victim, P_char attacker, int which, int mod)
{
   P_obj weapon = NULL;
   struct func_attachment *fn = NULL;
   int learned, percent, random_number;
   bool uber_harmony = FALSE, uber_disruption = FALSE;

   if(IS_ENABLED(CODE_NEWDEFENSE))
      {
      return(attemptParry(victim, attacker, which));
      }

   if(IS_NPC(victim) && !IS_WARRIOR(victim))
      return 0;

   if(!AWAKE(victim) || IS_AFFECTED(victim, AFF_MAJOR_PARALYSIS) ||
      IS_AFFECTED(victim, AFF_MINOR_PARALYSIS))
      return 0;

   /* no parry without something to parry with */
   if(!victim->equipment[WEAR_SHIELD] && !victim->equipment[WIELD] && !victim->equipment[SECONDARY_WEAPON])
      return 0;

   if(IS_PC(victim))
      learned = GET_CHAR_SKILL(victim, SKILL_PARRY);
   else
      learned = BOUNDED(0,((GET_LEVEL(victim) * 90) / MAXLVL + number(-20, 20)), IS_WARRIOR(victim) ? 99 : 0);

   if(!MIN_POS(victim, POS_STANDING + STAT_NORMAL))
      learned /= 3;

   if(learned < 1)
      return 0;

   percent = learned - WeaponSkill(attacker, which) / 2;

   percent += dex_app[STAT_INDEX(GET_C_DEX(victim))].reaction * 3;
   percent += DexHitBonus(STAT_INDEX(GET_C_DEX(victim)));
   percent -= (DexHitBonus(STAT_INDEX(GET_C_DEX(attacker))) +
               StrDamBonus(STAT_INDEX(GET_C_STR(attacker))));

   percent = BOUNDED(0, percent, learned);

   debuglog(51, DS_DEFENSE, "%s (&+r%d&N) PARRY vs. %s (&+r%d&N):  Learned (&+L%d&N) | Percent (&+L%d&N) | "
            "DexHitBonus (&+L%d&N) | AttackDexStrBonus (&+L%d&N) | AttackerWeaponMod (&+L%d&N)",
            GET_NAME(victim), GET_LEVEL(victim), GET_NAME(attacker), GET_LEVEL(attacker),
            learned, percent, DexHitBonus(STAT_INDEX(GET_C_DEX(victim))),
            DexHitBonus(STAT_INDEX(GET_C_DEX(attacker))) + StrDamBonus(STAT_INDEX(GET_C_STR(attacker))),
            (WeaponSkill(attacker, which) / 2));
#ifdef NEW_BARD
   if(affected_by_song(victim, SONG_OF_DEFENSIVE_HARMONY))
      {
      percent += victim->bard_singing->song_singing->modifier;
      if(victim->bard_singing->song_singing->special)
         uber_harmony = TRUE;
      debuglog(51, DS_IYACHTU, "Parry chance increased by %d to %d", victim->bard_singing->song_singing->modifier, percent);
      }
   else if(affected_by_song(victim, SONG_OF_DEFENSIVE_DISRUPTION))
      {
      percent -= victim->bard_singing->song_singing->modifier;
      if(victim->bard_singing->song_singing->special)
         uber_disruption = TRUE;
      debuglog(51, DS_IYACHTU, "Parry chance decreased by %d to %d", victim->bard_singing->song_singing->modifier, percent);
      }
#endif

   // Modifier
   for(; mod; mod--)
      if(uber_harmony)
         percent = percent * 2 / 5;
      else if(uber_disruption)
         percent /= 4;
      else
         percent /= 3;

   random_number = number(0,120);

   if(IS_PC(victim))
      debuglog(51, DS_COMBAT_SKILLS, "%s PA = %d,  Roll = %d",GET_NAME(victim), percent, random_number);

   if(percent < (random_number))
      {
      return 0;
      }
   else
      CharSkillGainChance(victim, SKILL_PARRY, 0);

   //  debuglog(51, DS_AZUTH, "Calling TryRiposte(victim(%s), attacker(%s), weaponslot %d)", GET_NAME(victim), GET_NAME(attacker), which);
   if(TryRiposte(victim, attacker, which))
      return TRUE;

   /* insert parry proc check here -Azuth */
   weapon = victim->equipment[which];
   if(weapon)
      {
      if(obj_index[weapon->R_num].spec_flag & IDX_PARRY)
         {
         fn = obj_index[weapon->R_num].func;
         do
            {
            if(fn->proc_flag & IDX_PARRY)
               {                                                                      /* what does this do??? */
               if((*fn->func.obj) (weapon, victim, PROC_PARRY, (char *) attacker) || victim->in_room != attacker->in_room)
                  {
                  if(!IS_DEAD(attacker))
                     {
                     Do_Object_Decay(victim, victim->equipment[which], DECAY_WEAPON_PARRY, victim->in_room);

                     if(!IS_FIGHTING(victim))
                        set_fighting(victim, attacker);
                     }

                  return TRUE;
                  }
               }
            fn = fn->next;
            } while(fn);
         }
      }

   /* succeed */
   c_act("You parry $n's lunge at you.", FALSE, attacker, 0, victim, TO_VICT, MELEE_DEF);
   c_act("$N parries your futile lunge at $M.", FALSE, attacker, 0, victim, TO_CHAR, MELEE_DEF);
   c_act("$N parries $n's lunge at $M.", FALSE, attacker, 0, victim, TO_NOTVICT, MELEE_DEF);

   /* we put object decay down here.  yes, that means a successful riposte
      will not hit a decay check, but it's easier than trying to figure out
      how to riposte and break the weapon at the same time. */
   Do_Object_Decay(victim, victim->equipment[which], DECAY_WEAPON_PARRY, victim->in_room);

   if(!IS_FIGHTING(victim))
      set_fighting(victim, attacker);
   return TRUE;
}

int mountblockSucceed(P_char victim, P_char attacker, int which, int mod)
{
   P_char   mount = NULL;
   P_obj    about = NULL;
   struct func_attachment *fn = NULL;
   int percent, random_number, learned = 0;
   bool uber_harmony = FALSE, uber_disruption = FALSE;

   if(IS_ENABLED(CODE_NEWDEFENSE))
      {
      return(attemptMountBlock(victim, attacker, which));
      }

   if(IS_PC(victim) && !GET_CHAR_SKILL(victim, SKILL_MOUNTED_COMBAT))
      return 0;

   if(GET_CLASS(victim) == CLASS_RANGER)
      return 0;

   if(IS_NPC(victim))
      {
      if(!IS_WARRIOR(victim))
         return 0;
      }

   /* Duh, have to be riding for this. */
   mount = GET_MOUNT(victim);
   if(!mount)
      return 0;

   if(IS_AFFECTED(victim, AFF_BLIND) ||
      IS_AFFECTED(victim, AFF_MAJOR_PARALYSIS) ||
      IS_AFFECTED(victim, AFF_MINOR_PARALYSIS))
      return 0;

   if(IS_PC(victim))
      learned = GET_CHAR_SKILL(victim, SKILL_MOUNTED_COMBAT);
   else
      learned = BOUNDED(0,((GET_LEVEL(victim) * 90) / MAXLVL + number(-20, 20)), 90);

   /* main point: if the enemy's a lousy shot, you block him fairly well. */

   percent = learned - WeaponSkill(attacker, which) / 2; /* range: -80 to 100 */
   percent += STAT_INDEX(GET_C_CON(victim));

   /* should be -80 to ~146 */
   percent += GET_LEVEL(victim) / ((GET_CLASS(victim) == CLASS_MONK) ? 2 : 3);
   percent -= GET_LEVEL(attacker) / 2;

   /* should be -110 to ~206 */
   if(percent > 0)
      percent += ((25 - load_modifier(victim)) / 5);

   /* some chance to block _ANY_ attack, if yer skilled: */

   if(percent < (learned / 20))
      percent = learned / 20;

   percent = MIN(percent, learned);

   random_number = number(1, 120);

#ifdef NEW_BARD
   if(affected_by_song(victim, SONG_OF_DEFENSIVE_HARMONY))
      {
      percent += victim->bard_singing->song_singing->modifier;
      if(victim->bard_singing->song_singing->special)
         uber_disruption = TRUE;
      debuglog(51, DS_IYACHTU, "Mountblock chance increased by %d to %d", victim->bard_singing->song_singing->modifier, percent);
      }
   else if(affected_by_song(victim, SONG_OF_DEFENSIVE_DISRUPTION))
      {
      percent -= victim->bard_singing->song_singing->modifier;
      if(victim->bard_singing->song_singing->special)
         uber_disruption = TRUE;
      debuglog(51, DS_IYACHTU, "Dodge chance decreased by %d to %d", victim->bard_singing->song_singing->modifier, percent);
      }
#endif

   // Modifier
   for(; mod; mod--)
      if(uber_harmony)
         percent = percent * 2 / 5;
      else if(uber_disruption)
         percent /= 4;
      else
         percent /=3;

   if(GET_CLASS(victim) == CLASS_DIRERAIDER)
      percent>>=1;  // halve effectiveness of dire raider at mount block
   if(IS_PC(victim))
      debuglog(51, DS_COMBAT_SKILLS, "%s MB = %d,  Roll = %d",GET_NAME(victim), percent, random_number);

   if(random_number > percent)
      {
      if(GET_CLASS(victim) == CLASS_DIRERAIDER)
         if(random_number > percent <<1)
            CharSkillGainChance(victim, SKILL_MOUNTED_COMBAT, 0);  // so that dires haev same chance to learn as others
      return 0;
      }
   else
      CharSkillGainChance(victim, SKILL_MOUNTED_COMBAT, 0);

   /* Succeed */
   about = mount->equipment[WEAR_ABOUT];
   if(about)
      {
      if(obj_index[about->R_num].spec_flag & IDX_FBLOCK)
         {
         fn = obj_index[about->R_num].func;
         do
            {
            if(fn->proc_flag & IDX_FBLOCK)
               {                                                                      /* what does this do??? */
               if((*fn->func.obj) (about, victim, PROC_FBLOCK, (char *) attacker) || victim->in_room != attacker->in_room)
                  {
                  if(!IS_DEAD(attacker))
                     {
                     if(!IS_FIGHTING(victim))
                        set_fighting(victim, attacker);
                     }

                  return TRUE;
                  }
               }
            fn = fn->next;
            } while(fn);
         }
      }

   c_act("You maneuver your mount to avoid $n's attack!", FALSE, attacker, 0, victim, TO_VICT, MELEE_DEF);
   c_act("$N maneuvers $S mount to avoid your attack!", FALSE, attacker, 0, victim, TO_CHAR, MELEE_DEF);
   c_act("$N maneuvers $S mount to avoid $n's attack!", FALSE, attacker, 0, victim, TO_NOTVICT, MELEE_DEF);

   if(!IS_FIGHTING(victim))
      set_fighting(victim, attacker);

   return 1;
}


void DuelKnockOut(P_char winner, P_char loser)
{
   char Gbuf[MAX_STRING_LENGTH];
   struct affected_type af;
   struct group_element *member = NULL;
   P_group lgroup, wgroup;

   /* Sanity Check */
   if(!(loser && winner))
      {
      logit(LOG_EXIT, "assert: bogus parms in DuelKnockOut");
      dump_core();
      }

   /* Inform the winner and loser */
   send_to_char("&+LWith a final blow, you feel yourself collapsing in agony...&N\n\n", loser);
   sprintf(Gbuf, "&+LYour final blow knocks %s unconscious!\n", GET_NAME(loser));
   send_to_char(Gbuf, winner);

   lgroup = GET_GROUP(loser);
   wgroup = GET_GROUP(winner);

   /* One on One duel */
   if(IS_DUELLING(loser) && (GET_DUEL_TARGET(loser) == winner))
      {

      sprintf(Gbuf, "&+r%s has won the duel!&N\n", GET_NAME(winner));
      send_to_char(Gbuf, loser);

      sprintf(Gbuf, "&+rYou emerge victorious from the duel with %s!&N\n", GET_NAME(loser));
      send_to_char(Gbuf, winner);

      stop_duelling(winner, loser, 1);

      /* Group duel */
      }
   else if(lgroup && wgroup && (GET_DUEL_TARGET_GRP(lgroup) == wgroup))
      {

      /* If this was the group leader, pick a new leader or end the duel if none */
      if(GET_GROUP_LEADER(lgroup) == loser)
         {
         if(lgroup->members->next && lgroup->members->next->next)
            findNewGroupLeader(lgroup);
         else
            {
            send_to_char("&+rYour group has lost the duel!\n", loser);
            send_to_char("&+rYour group has won the duel!\n", winner);

            deleteGroup(loser, lgroup);
            }
         }

      if(lgroup)
         {
         send_to_char ("You are no longer a member of the group.\n", loser);
         sprintf (Gbuf, "%s has left the group.\n", C_NAME(loser));

         LOOP_THRU_GROUP (member, lgroup) {
            if((loser != member->this) && CAN_SEE(member->this, loser))
               send_to_char (Gbuf, member->this);
         }

         removeCharFromGroup(loser);
         }

      /* Huh?  Something funky happened... */
      }
   else
      {
      send_to_char("Duel KO, but not duelling anyone?!?  Report over "
                   "petition to an admin!\n", loser);
      return;
      }

   /* KO! */
   send_to_char("&=rlThe world starts spinning, and blackness overwhelms you...&N\n", loser);
   SET_POS(loser, GET_POS(loser) + STAT_SLEEPING);

   bzero(&af, sizeof(af));
   af.type = 0;
   af.duration = 1;
   af.modifier = 0;
   af.location = 0;
   SET_CBIT(af.sets_affs, AFF_KNOCKED_OUT);
   affect_join(loser, &af, FALSE, FALSE);

   return;
}

void stop_duelling(P_char winner,P_char loser, int flag)
{

   /* Sanity Check */
   if(!(winner && loser))
      {
      logit(LOG_EXIT, "bogus parms in stop_duelling");
      dump_core();
      }

   GET_DUEL_TARGET(winner) = NULL;
   GET_DUEL_TARGET(loser) = NULL;
   REMOVE_CBIT(winner->only.pc->pcact, PLR_DUEL);
   REMOVE_CBIT(loser->only.pc->pcact, PLR_DUEL);

   if(!flag)
      return;

   stop_fighting(loser);
   StopAllAttackers(loser);
   StopCasting(loser);
   stop_riding(loser);

   return;
}

void stop_duelling_group(P_group group, P_group vgroup)
{

   /* Sanity Check */
   if(!(group && vgroup))
      {
      logit(LOG_EXIT, "assert: bogus parms in stop_duelling_group");
      dump_core();
      }

   GET_DUEL_TARGET_GRP(group) = NULL;
   GET_DUEL_TARGET_GRP(vgroup) = NULL;
   group->duelling = FALSE;
   vgroup->duelling = FALSE;

}

void ApplyPoisonEffect(P_char ch, P_char victim, int poison_type, int poison_level)
{
   int dam;
   struct affected_type af;

   /* as explained in poisonedWeapon(), poison used to get two throws.  First,
      there was a check against PARA.  If the victim failed the throw, the
      code would check the poison_type and cast the appropriate spell.  This,
      of course, led to a second throw (in the case of damage spells, it was
      checked in damage(); non-damage spells were checked in the spell_*
      function).  Since that's pretty unfair to the poisoner, I've rewritten
      how poison is handled. Rather than call each spell_* function, we
      just apply the aff/dam here.  --DMB 9/29/99  */

   if(IS_TRUSTED(victim))
      return;

   switch(poison_type)
      {
      case 1:  /* slow */
         if(!affected_by_spell(victim, SPELL_SLOW))
            {
            bzero(&af, sizeof(af));
            af.type = SPELL_SLOW;
            af.duration = modify_by_specialization(ch, current_spell_being_cast,
                                                   (poison_level >> 4) + 1);
            if((GET_RACE(ch) == RACE_OGRE) || (GET_RACE(ch) == RACE_TROLL) ||
               (GET_RACE(ch) == RACE_BARBARIAN) || (GET_RACE(ch) == RACE_MOUNTAIN) ||
               (GET_RACE(ch) == RACE_DUERGAR))
               af.duration = ((af.duration * cc_spells_highConRaceMod) / 100);
            af.modifier = 2;
            SET_CBIT(af.sets_affs, AFF_SLOW);

            affect_to_char(victim, &af);

            act("&+gAs $n's poison takes effect, &N$N &N&+gbegins to sllooowwww "
                "down.", TRUE, ch, 0, victim, TO_ROOM);
            send_to_char("&+gTime starts to slow as a poison takes effect...\n",
                         victim);
            act("&+gAs your poison takes effect, &N$N &N&+gbegins to "
                "sllooowwww down.", TRUE, ch, 0, victim, TO_CHAR);
            }
         break;
      case 2:  /* blindness */
         if(!affected_by_spell(victim, SPELL_BLINDNESS))
            {
            bzero(&af, sizeof(af));
            af.type = SPELL_BLINDNESS;
            SET_CBIT(af.sets_affs, AFF_BLIND);
            af.duration = modify_by_specialization(ch, current_spell_being_cast, 2);
            if((GET_RACE(ch) == RACE_OGRE) || (GET_RACE(ch) == RACE_TROLL) ||
               (GET_RACE(ch) == RACE_BARBARIAN) || (GET_RACE(ch) == RACE_MOUNTAIN) ||
               (GET_RACE(ch) == RACE_DUERGAR))
               af.duration = ((af.duration * cc_spells_highConRaceMod) / 100);
            affect_to_char(victim, &af);

            send_to_char("&+gAs a poison takes affect, your vision begins to "
                         "blur...\n", victim);
            act("&+gAs $n's poison takes effect, &N$N &N&+gappears to go blind!",
                TRUE, ch, 0, victim, TO_ROOM);
            act("&+gAs your poison takes effect, &N$N &N&+gappears to go blind!",
                TRUE, ch, 0, victim, TO_CHAR);
            }
         break;
      case 3:  /* minor paralyzation */
         if(!affected_by_spell(victim, SPELL_MINOR_PARALYSIS))
            {
#if 0
            if(GET_LEVEL(victim) >= 50)
               {
               act("$N&+L turns slightly pale for a moment then shakes off the "
                   "&+Laffect of the poison!&N",
                   TRUE, ch, 0, victim, TO_ROOM);
               act("$N&+L turns slightly pale for a moment then shakes off the "
                   "&+Laffect of the poison!&N",
                   TRUE, ch, 0, victim, TO_CHAR);
               return;
               }
#endif
            bzero(&af, sizeof(af));
            af.type = SPELL_MINOR_PARALYSIS;
            af.duration = modify_by_specialization(ch, current_spell_being_cast, 4);
            if((GET_RACE(ch) == RACE_OGRE) || (GET_RACE(ch) == RACE_TROLL) ||
               (GET_RACE(ch) == RACE_BARBARIAN) || (GET_RACE(ch) == RACE_MOUNTAIN) ||
               (GET_RACE(ch) == RACE_DUERGAR))
               af.duration = ((af.duration * cc_spells_highConRaceMod) / 100);
            SET_CBIT(af.sets_affs, AFF_MINOR_PARALYSIS);

            affect_to_char(victim, &af);

            act("$N &N&+gturns pale as $n's poison takes effect, causing all "
                "motion to halt.", TRUE, ch, 0, victim, TO_ROOM);
            send_to_char("&+gYour joints stiffen as a poison takes effect...\n",
                         victim);
            act("$N &N&+gturns pale as your poison takes effect, causing all "
                "motion to halt.", TRUE, ch, 0, victim, TO_CHAR);

            if(IS_FIGHTING(victim))
               stop_fighting(victim);

            /* stop all non-vicious/agg attackers */
            StopMercifulAttackers(victim);
            }
         break;
      case 4:  /* major paralyzation */
         if(!affected_by_spell(victim, SPELL_MAJOR_PARALYSIS))
            {
            bzero(&af, sizeof(af));
            af.type = SPELL_MAJOR_PARALYSIS;
            af.duration = ((poison_level >> 4) + 1);
            if((GET_RACE(ch) == RACE_OGRE) || (GET_RACE(ch) == RACE_TROLL) ||
               (GET_RACE(ch) == RACE_BARBARIAN) || (GET_RACE(ch) == RACE_MOUNTAIN) ||
               (GET_RACE(ch) == RACE_DUERGAR))
               af.duration = ((af.duration * cc_spells_highConRaceMod) / 100);
            SET_CBIT(af.sets_affs, AFF_MAJOR_PARALYSIS);

            affect_to_char(victim, &af);

            act("&+gAs $n's poison takes effect, &N$N &N&+gceases to move.. still "
                "and lifeless.", FALSE, victim, 0, 0, TO_ROOM);
            send_to_char("&+gYour joints start to ache as a poison takes effect..."
                         "\n", victim);
            act("As your poison takes effect, &N$N &N&+gceases to move.. still "
                "and lifeless.", TRUE, ch, 0, victim, TO_CHAR);

            if(IS_FIGHTING(victim))
               stop_fighting(victim);

            /* stop all non-vicious/agg attackers */
            StopMercifulAttackers(victim);
            }
         break;
      case 5:  /* cause light */
         dam = FindSpellDamage(ch, victim, poison_level, SPELL_CAUSE_LIGHT);

         /* calling these damage spells eventually called damage() which allowed
            the victim a chance to resist.  So, we just do the damage here and
            create our own spanky poison message.  */

         send_to_char("&+gYou feel a little weaker from the poison!\n", victim);
         act("$N &N&+glooks a little weaker as $n's poison takes effect.", TRUE,
             ch, 0, victim, TO_ROOM);
         act("$N &N&+glooks a little weaker as your poison takes effect.", TRUE,
             ch, 0, victim, TO_CHAR);

         GET_HIT(victim) -= dam;
         if(GET_HIT(victim) < 0)
            GET_HIT(victim) = 0;
         break;
      case 6:  /* cause serious */
         dam = FindSpellDamage(ch, victim, poison_level, SPELL_CAUSE_SERIOUS);

         /* calling these damage spells eventually called damage() which allowed
            the victim a chance to resist.  So, we just do the damage here and
            create our own spanky poison message.  */

         send_to_char("&+gYou feel much weaker from the poison!\n", victim);
         act("$N &N&+glooks much weaker as $n's poison takes effect.", TRUE,
             ch, 0, victim, TO_ROOM);
         act("$N &N&+glooks much weaker as your poison takes effect.", TRUE,
             ch, 0, victim, TO_CHAR);

         GET_HIT(victim) -= dam;
         if(GET_HIT(victim) < 0)
            GET_HIT(victim) = 0;
         break;
      case 7:  /* cause critical */
         dam = FindSpellDamage(ch, victim, poison_level, SPELL_CAUSE_CRITICAL);

         /* calling these damage spells eventually called damage() which allowed
            the victim a chance to resist.  So, we just do the damage here and
            create our own spanky poison message.  */

         send_to_char("&+gYou writhe in pain from the poison!\n", victim);
         act("$N &N&+gwrithes in pain from $n's poison.", TRUE,
             ch, 0, victim, TO_ROOM);
         act("$N &N&+gwrithes in pain from your poison.", TRUE, ch, 0, victim,
             TO_CHAR);
         GET_HIT(victim) -= dam;
         if(GET_HIT(victim) < 0)
            GET_HIT(victim) = 0;
         break;
      case 8:  /* harm */
         dam = FindSpellDamage(ch, victim, poison_level, SPELL_HARM);

         /* calling these damage spells eventually called damage() which allowed
            the victim a chance to resist.  So, we just do the damage here and
            create our own spanky poison message.  */

         send_to_char("&+gExtreme pain courses through your body from the "
                      "poison!\n", victim);
         act("$N &N&+glooks to be in extreme pain as $n's poison takes effect.",
             TRUE, ch, 0, victim, TO_ROOM);
         act("$N &N&+glooks to be in extreme pain as your poison takes effect.",
             TRUE, ch, 0, victim, TO_CHAR);

         GET_HIT(victim) -= dam;
         if(GET_HIT(victim) < 0)
            GET_HIT(victim) = 0;
         break;
      case 9:  /* full harm */
         dam = FindSpellDamage(ch, victim, poison_level, SPELL_FULL_HARM);

         /* calling these damage spells eventually called damage() which allowed
            the victim a chance to resist.  So, we just do the damage here and
            create our own spanky poison message.  */

         send_to_char("&+gViolent pain courses through your veins from the "
                      "poison!\n", victim);
         act("$N &N&+glooks to be in agony as $n's poison takes effect.", TRUE,
             ch, 0, victim, TO_ROOM);
         act("$N &N&+glooks to be in agony as your poison takes effect.", TRUE,
             ch, 0, victim, TO_CHAR);

         GET_HIT(victim) -= dam;
         if(GET_HIT(victim) < 0)
            GET_HIT(victim) = 0;
         break;
      case 10: /* instant death */
         if(GET_HIT(victim) > 0)
            {
            send_to_char("&+gYou feel a cold chill run through your veins as a "
                         "powerful poison instantly\n&+gstops your heart!\n", victim);
            send_to_char("&+gThe powerful poison visibly takes effect and "
                         "instantly kills your opponent!\n", ch);
            }
         act("$N convulses once and is very still thereafter.", FALSE, ch, 0,
             victim, TO_NOTVICT);
         SuddenDeath(victim, ch, "instant death poison");
         break;
      default: /* poison spell */
         spell_poison(poison_level, ch, victim, 0);
         break;
      }

   return;
}

int CHECK_BLINDFIGHTING(P_char ch)
{
   int skill = 0;

   if(!ch)
      return FALSE;

   if(IS_PC(ch))
      {
      skill = GET_CHAR_SKILL(ch, SKILL_BLINDFIGHTING);
      if(skill > 0)
         CharSkillGainChance(ch, SKILL_BLINDFIGHTING, 15);
      }
   else
      {
      if(IS_WARRIOR(ch))
         skill = 90;

      skill = BOUNDED(0, ((GET_LEVEL(ch) * skill) / MAXLVL + number(-20, 20)), 100);

      }

   return skill;

}

// For backwards compatibility

void hit(P_char ch, P_char victim, int type)
{
   CombatMeleeAttack(ch, victim, type);
}

// For backwards compatibility
bool damage(P_char ch, P_char victim, int dam, int attacktype)
{
   if(IS_DEAD(victim))
      {
      wizlog(51, "victim(%s) is dead already in call to damage(), returning TRUE", GET_NAME(victim));
      return TRUE;
      }

   CombatDamage(ch, victim, dam, attacktype);

   // If un-vicious, stop whacking on that poor victim
   if(!AWAKE(victim) || (GET_HIT(victim) < -2) ||
      IS_AFFECTED(victim, AFF_MINOR_PARALYSIS) ||
      IS_AFFECTED(victim, AFF_MAJOR_PARALYSIS))
      {
      if(IS_FIGHTING(victim))
         StopCombat(victim);
      StopMercifulAttackers(victim);
      }

   if(!victim || victim->in_room == -1 || GET_STAT(victim) == STAT_DEAD)
      return TRUE;
   else
      return FALSE;

}

// For backwards compatibility
bool Damage(P_char ch, P_char victim, int dam, int attacktype)
{

   if(IS_DEAD(victim))
      {
      wizlog(51, "victim(%s) is dead in call to Damage(), returning TRUE", GET_NAME(victim));
      return TRUE;
      }

   CombatDamage(ch, victim, dam, attacktype);

   if(!victim || victim->in_room == -1 || GET_STAT(victim) == STAT_DEAD)
      return TRUE;
   else
      return FALSE;

}

// For backwards compatibility

void stop_fighting(P_char ch)
{
   StopCombat(ch);
}

// For backwards compatibility
void set_fighting(P_char ch, P_char vict)
{
   StartCombat(ch, vict);
}

// Backwards compatibility
void check_killer(P_char ch, P_char victim)
{
   /* this will be replaced with several law routines, for now, it does nothing.
      JAB */
   return;
}

#endif

