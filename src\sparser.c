/* ***************************************************************************
 *  File: sparser.c                                          Part of Outcast *
 *  Usage: core routines for handling spellcasting                           *
 *  Copyright  1990, 1991 - see 'license.doc' for complete information.      *
 *  Copyright  1994, 1995 - Outcast Systems Ltd.                             *
 *************************************************************************** */

#include <ctype.h>
#include <stdio.h>
#include <string.h>

#include "comm.h"
#include "db.h"
#include "events.h"
#include "interp.h"
#include "prototypes.h"
#include "skillrec.h"
#include "spells.h"
#include "race_class.h"
#include "utils.h"
#ifdef NEWJUSTICE
   #include "newjustice.h"
#endif
#ifdef OLDJUSTICE
   #include "justice.h"
#endif
#include "mm.h"

/* external variables */

extern P_char NPC_list;
extern P_char PC_list;
extern P_desc descriptor_list;
extern P_event current_event;
extern P_event event_type_list[];
extern P_index mob_index;
extern P_index obj_index;
extern P_room world;
extern char *spell_wear_off_msg[];
extern const char *event_names[];
extern const struct stat_data stat_factor[];
extern const struct trait_data racial_traits[];
extern int cheat_flag;
extern int phase;
extern int spl_table[TOTALLVLS][MAX_CIRCLE];
extern int top_of_world;
extern int pulse;                                 // spellcast debug addition.. nuke later on
extern struct zone_data *zone_table;
extern byte saving_throws[TOTALCLASS][5][2];
extern ubyte sets_code_control[CODE_CONTROL_BYTES];
extern P_event schedule[];

// Storage variable for Spell damage. o_O 4/13/01 --MIAX
extern float store_spells_offensiveLevel;
extern float store_spells_diceNum;
extern float store_spells_diceSize;
extern float store_spells_rawDamage;
extern float store_spells_modifiedDamage;
extern float store_spells_specializationDamage;
extern float store_spells_globalDamageFactor;
extern float store_spells_skillDamage;
extern float store_spells_evadeDamage;
extern float store_spells_innateRaceDamage;
extern float store_spells_finalDamage;
extern float store_spells_totalDamage;
extern float store_spells_circleNonAreas1Damage;
extern float store_spells_circleNonAreas2Damage;
extern float store_spells_circleNonAreas3Damage;
extern float store_spells_circleNonAreas4Damage;
extern float store_spells_circleNonAreas5Damage;
extern float store_spells_circleNonAreas6Damage;
extern float store_spells_circleNonAreas7Damage;
extern float store_spells_circleNonAreas8Damage;
extern float store_spells_circleNonAreas9Damage;
extern float store_spells_circleNonAreas10Damage;
extern float store_spells_circleAreas1Damage;
extern float store_spells_circleAreas2Damage;
extern float store_spells_circleAreas3Damage;
extern float store_spells_circleAreas4Damage;
extern float store_spells_circleAreas5Damage;
extern float store_spells_circleAreas6Damage;
extern float store_spells_circleAreas7Damage;
extern float store_spells_circleAreas8Damage;
extern float store_spells_circleAreas9Damage;
extern float store_spells_circleAreas10Damage;

extern float counter_spells_offensiveLevel;
extern float counter_spells_diceNum;
extern float counter_spells_diceSize;
extern float counter_spells_rawDamage;
extern float counter_spells_modifiedDamage;
extern float counter_spells_specializationDamage;
extern float counter_spells_globalDamageFactor;
extern float counter_spells_skillDamage;
extern float counter_spells_evadeDamage;
extern float counter_spells_innateRaceDamage;
extern float counter_spells_finalDamage;
extern float counter_spells_totalDamage;
extern float counter_spells_circleNonAreas1Damage;
extern float counter_spells_circleNonAreas2Damage;
extern float counter_spells_circleNonAreas3Damage;
extern float counter_spells_circleNonAreas4Damage;
extern float counter_spells_circleNonAreas5Damage;
extern float counter_spells_circleNonAreas6Damage;
extern float counter_spells_circleNonAreas7Damage;
extern float counter_spells_circleNonAreas8Damage;
extern float counter_spells_circleNonAreas9Damage;
extern float counter_spells_circleNonAreas10Damage;
extern float counter_spells_circleAreas1Damage;
extern float counter_spells_circleAreas2Damage;
extern float counter_spells_circleAreas3Damage;
extern float counter_spells_circleAreas4Damage;
extern float counter_spells_circleAreas5Damage;
extern float counter_spells_circleAreas6Damage;
extern float counter_spells_circleAreas7Damage;
extern float counter_spells_circleAreas8Damage;
extern float counter_spells_circleAreas9Damage;
extern float counter_spells_circleAreas10Damage;

extern int cc_spells_areasMod;
extern int cc_spells_nonAreasMod;
extern int cc_spells_rawDamageMod;
extern int cc_spells_specializationMod;
extern int cc_spells_globalFactor;
extern int cc_spells_skillDamageMod;
extern int cc_spells_evadeMod;
extern int cc_spells_innateRaceMrMod;
extern int cc_spells_circleNonAreas1Mod;
extern int cc_spells_circleNonAreas2Mod;
extern int cc_spells_circleNonAreas3Mod;
extern int cc_spells_circleNonAreas4Mod;
extern int cc_spells_circleNonAreas5Mod;
extern int cc_spells_circleNonAreas6Mod;
extern int cc_spells_circleNonAreas7Mod;
extern int cc_spells_circleNonAreas8Mod;
extern int cc_spells_circleNonAreas9Mod;
extern int cc_spells_circleNonAreas10Mod;
extern int cc_spells_circleAreas1Mod;
extern int cc_spells_circleAreas2Mod;
extern int cc_spells_circleAreas3Mod;
extern int cc_spells_circleAreas4Mod;
extern int cc_spells_circleAreas5Mod;
extern int cc_spells_circleAreas6Mod;
extern int cc_spells_circleAreas7Mod;
extern int cc_spells_circleAreas8Mod;
extern int cc_spells_circleAreas9Mod;
extern int cc_spells_circleAreas10Mod;
extern int cc_spells_mobSpellBonusMod;
extern int cc_spells_totalDamage;
extern int cc_spells_globalFactor;

#define MANA_MU 1
#define MANA_CL 1

int CONTROL_DEBUG = 0;

/* Skills teach locations */

#define TEACH_ASHREMITE (1<<HOME_ASHREMITE)
#define TEACH_BG (1<<HOME_BALDURS)
#define TEACH_BS (1<<HOME_BLOODSTONE)
#define TEACH_CAL (1<<HOME_CALIMPORT)
#define TEACH_EVERMEET (1<<HOME_LEUTHILSPAR)
#define TEACH_GHORE (1<<HOME_GHORE)
#define TEACH_LUIREN (1<<HOME_LUIREN)
#define TEACH_MHALL (1<<HOME_MITHRIL_HALL)
#define TEACH_WATERDEEP (1<<HOME_WATERDEEP)

/* next define will set result in a macro that has all the bits set up
   to (and including) the one for the last hometown  (neb) */

#define TEACH_ALL ((1 << (LAST_HOME + 1)) - 1)


/* BASE was defined as adding all the TEACH_xxxx macros.  Better then
   doing that, use the TEACH_ALL macro I just made (neb) */

#define BASE TEACH_ALL
#define LEARN SKILL_BASE_SPECIAL        /* foo */

Skill skills[MAX_SKILLS];
int MobSpellIndex[MAX_SKILLS];
const char *spells[MAX_SKILLS];
int pindex2Skill[MAX_SKILLS];

int numSkills = -1;


struct mm_ds *dead_cast_pool = NULL;
struct spellcast_datatype *spellcast_stack = NULL, *sparecast_stack = NULL;

int current_spell_being_cast;   /* set by .. something. :-) */

#ifdef PCS_USE_MANA
bool powercasting = FALSE;
#endif

void SKILL_CREATE(const char *Name, int Index, int Type)
{
   numSkills++;

   skills[numSkills].pindex = (Index);
   skills[numSkills].name = (Name);
   skills[numSkills].type = (Type);
   skills[numSkills].spell_pointer = 0;
   skills[numSkills].harmful = -1;
}

void SONG_CREATE(const char *Name, int Index, int Type)
{
   numSkills++;

   skills[numSkills].pindex = (Index);
   skills[numSkills].name = (Name);
   skills[numSkills].type = (Type);
   skills[numSkills].spell_pointer = 0;
   skills[numSkills].harmful = -1;
   skills[numSkills].song_flag = TRUE;
}


void WARCHANT_CREATE(const char *Name, int Index, int Type)
{
   numSkills++;

   skills[numSkills].pindex = (Index);
   skills[numSkills].name = (Name);
   skills[numSkills].type = (Type);
   skills[numSkills].spell_pointer = 0;
   skills[numSkills].harmful = -1;
   skills[numSkills].song_flag = 2;
}

void SKILL_ADD(int Class, int Level, int MaxLearn, int CostMult, int BaseSkill)
{
   skills[numSkills].class[(int) ((byte) Class - 1)].rlevel = (byte) (Level);
   skills[numSkills].class[(int) ((byte) Class - 1)].maxlearn = (ubyte) (MaxLearn);
   skills[numSkills].class[(int) ((byte) Class - 1)].base_skill = (BaseSkill);
   skills[numSkills].class[(int) ((byte) Class - 1)].costmult = (ubyte) (CostMult);
}

void SPELL_CREATE(const char *Name, int Index, int Type, int Beats, int MinPos, int Targets, int Harmful, int Off_Level,
                  void (*Spell_pointer) (int, P_char, char *, int, P_char, P_obj))
{
   numSkills++;

   skills[numSkills].pindex = (Index);
   skills[numSkills].name = (Name);
   skills[numSkills].spell_pointer = Spell_pointer;
   skills[numSkills].min_pos = (byte) MinPos;
   skills[numSkills].beats = (sh_int) Beats;
   skills[numSkills].harmful = (byte) Harmful;
   skills[numSkills].targets = (sh_int) Targets;
   skills[numSkills].off_level = (sh_int) Off_Level;
   skills[numSkills].type = (ubyte) Type;
}

void SPELL_ADD(int Class, int Level, int BaseSkill)
{
   skills[numSkills].class[(int) ((byte) Class - 1)].rlevel = (byte) (Level);
   skills[numSkills].class[(int) ((byte) Class - 1)].maxlearn = 100;
   skills[numSkills].class[(int) ((byte) Class - 1)].base_skill = (BaseSkill);
}

/** TAM 2/94 -- this func. needed for affects which would upset balance of **/
/**             game if they endured the whole 75 seconds (game hr).  in   **/
/**             other words allow for affects in increments of 20 seconds  **/
/**             (SHORT_AFFECT)                                             **/
/**             Note: a separate list *shouldve* been made for these short **/
/**                   affects, probably in char_data->specials.            **/
// this function is horendous! -Azuth

void short_affect_update(void)
{
   P_char   i = NULL, i_next = NULL;
   int      j, vdam, moveEmOut, campEmOut;
   struct affected_type *af = NULL, *next_af_dude = NULL;
   struct follow_type *f = NULL;

   for(i = PC_list; i; i = i_next)
      {
      i_next = i->next;

      if(i->desc && i->desc->connected)
         continue;  /* not in game */

      /* gonna clear up an ancient carryover, due to items and bugs, it's possible for a player/mob to get to
         STAT_DEAD without dying.  This way they have 20 seconds (maybe) grace and then we kill em off.  JAB */
      if(would_die(i, 0) || (GET_MAX_HIT(i) < -10))
         {
         SuddenDeath(i, i, "discovered dead");
         i = NULL;
         continue;
         }

      /* Venom damage  -- 9/98 Shev */
      if(affected_by_spell(i, SPELL_VENOM))
         {
         for(af = i->affected; af && (af->type != SPELL_VENOM); af = af->next)
            ;

         /* Damage is determined by max hits divided by duration, which is actually */
         /* the LEVEL of the spell, effectively making the LOWER spell level the    */
         /* most powerful.  And you thought regular poison was ugly.                */
         vdam = GET_MAX_HIT(i) / af->duration;

         /* Slow poison halves damage */
         if(IS_AFFECTED(i, AFF_SLOW_POISON))
            vdam /= 2;

         if(damage(i, i, vdam, SPELL_VENOM))
            continue;
         }

      /* poison is now VERY ugly, remaining duration is also 'level' of the poison, which determines damage,
         damage is:  max hits / (62 - level) per short_affect (20 seconds) */
      if(affected_by_spell(i, SPELL_POISON))
         {
         for(af = i->affected; af && (af->type != SPELL_POISON); af = af->next)
            ;

         j = af ? af->duration : 0;

         /* slow poison doubles duration, but reduces intensity */
         if(IS_AFFECTED(i, AFF_SLOW_POISON))
            j >>= 2;

         if(damage(i, i, MAX(1, GET_MAX_HIT(i) / MAX(1, (100 - j))), SPELL_POISON))
            continue;
         }
#if 0
      /* quest pkill exit: modelled after acheron exit which is modelled after camp --Eilistraee*/
      if(IS_AFFECTED(i, AFF_EXITING_QUEST))
         {
         for(af = i->affected; af && (af->type != SKILL_CAMP); af = af->next);
         if(af)
            {
            moveEmOut = TRUE;

            if(!i->desc || IS_FIGHTING(i) || (i->in_room != af->modifier) ||
               (GET_STAT(i) < STAT_SLEEPING))
               {
               affect_from_char(i, SKILL_CAMP);
               send_to_char("So much for that effort to leave the battlefield.\n", i);
               moveEmOut = FALSE;
               }

            if(moveEmOut)
               {
               /* we don't want it wearing off during regular updates, so we add a
                  little extra and close it out before regular update code can
                  kill it. */
               if(--(af->duration) < 2)
                  {
                  /* done the time, now we rent em out. */
                  affect_from_char(i, SKILL_CAMP);
                  act("$n suddenly fades into the ether as $e exits the battlefield.", FALSE, i, 0, 0, TO_ROOM);
                  send_to_char("Your petition is  answered and you are teleported away from the battle.\n", i);

                  //            statuslog(GET_LEVEL(i), "%s has exited Acheron.", GET_NAME(i));
                  char_from_room(i);
                  debuglog(51, DS_EILISTRAEE, "quest_portal: %d", i->only.pc->quest_portal);
                  char_to_room(i, i->only.pc->quest_portal, -1);
                  i->only.pc->quest_portal = 0;
                  }
               }
            else
               {
               logit(LOG_DEBUG, "%s has AFF_EXITING_QUEST, but no affect structure", GET_NAME(i));
               send_to_char("Hmm, something strange has happened to your attempt "
                            " to exit the battle, better petition for an admin.\n", i);
               REMOVE_CBIT(i->specials.affects, AFF_EXITING_QUEST);
               }
            }
         }
#endif
      /* acheron exit: modelled after camp.  --DMB */
      if(IS_AFFECTED(i, AFF_EXITING_ACHERON))
         {
         for(af = i->affected; af && (af->type != SKILL_CAMP); af = af->next);
         if(af)
            {
            moveEmOut = TRUE;

            if(!i->desc || IS_FIGHTING(i) || (i->in_room != af->modifier) ||
               (GET_STAT(i) < STAT_SLEEPING))
               {
               affect_from_char(i, SKILL_CAMP);
               send_to_char("So much for that effort to leave Acheron.\n", i);
               moveEmOut = FALSE;
               }

            if(moveEmOut)
               {
               /* we don't want it wearing off during regular updates, so we add a
                  little extra and close it out before regular update code can
                  kill it. */
               if(--(af->duration) < 2)
                  {
                  /* done the time, now we rent em out. */
                  affect_from_char(i, SKILL_CAMP);
                  act("$n suddenly fades into the ether as $e exits Acheron.", FALSE, i, 0, 0, TO_ROOM);
                  send_to_char("Your prayers are answered and you are teleported out of Acheron.\n", i);
                  statuslog(GET_LEVEL(i), "%s has exited Acheron.", GET_NAME(i));
                  char_from_room(i);
                  /* pets are a little tricky. */
                  if(IS_NPC(i))
                     {
                     if(!i->following)
                        {
                        affect_from_char(i, SKILL_CAMP);
                        send_to_char("So much for that effort to leave Acheron.\n", i);
                        moveEmOut = FALSE;
                        }
                     else if(IN_ACHERON(i->following))
                        char_to_room(i, i->following->only.pc->acheron_portal, -1);
                     else
                        char_to_room(i, i->following->in_room, -1);
                     }
                  else
                     char_to_room(i, i->only.pc->acheron_portal, -1);

                  CharWait(i, PULSE_VIOLENCE * 5);
                  if(i->followers)
                     {
                     for(f = i->followers; f; f = f->next)
                        {
                        if(!IS_PC(f->follower))
                           {
                           char_from_room(f->follower);
                           char_to_room(f->follower, i->only.pc->acheron_portal, -1);
                           }
                        }
                     }

                  i->only.pc->acheron_portal = 0;
                  }
               }
            }
         else
            {
            logit(LOG_DEBUG, "%s has AFF_EXITING_ACHERON, but no affect structure", GET_NAME(i));
            send_to_char("Hmm, something strange has happened to your attempt "
                         " to exit Acheron, better petition for an admin.\n", i);
            REMOVE_CBIT(i->specials.affects, AFF_EXITING_ACHERON);
            }
         }

      /* camp:  for ease of insertion, we just check a few things each short affect update.  If any are true,
         we nuke current attempt, and they must start all over.  If they pass, we decrement the duration
         counter and check if they are ready to go.  If they run the timer down to 0, we rent them out and
         continue with others.  JAB */
      if(IS_AFFECTED(i, AFF_CAMPING))
         {
         for(af = i->affected; af && (af->type != SKILL_CAMP); af = af->next);

         if(af)
            {
            campEmOut = TRUE;

            if(!i->desc || IS_FIGHTING(i) || (i->in_room != i->only.pc->camp_room) || (GET_STAT(i) < STAT_SLEEPING))
               {
               affect_from_char(i, SKILL_CAMP);
               send_to_char("So much for that camping effort.\n", i);
               campEmOut = FALSE;
               }

            /* Check if all the savable pets didnt get in the way of camping   -- Alth Dec 98 */
            if(i->followers && campEmOut)
               {
               f = i->followers;
               while(f)
                  {
                  if(IS_NPC(f->follower) && IS_CSET(f->follower->only.npc->npcact, ACT_SAVE))
                     {       /* is it savable? */
                     if(IS_FIGHTING(f->follower))
                        {
                        send_to_char ("You abort your camping efforts as one of your followers seems to be still engaged in combat.\n", i);
                        campEmOut = FALSE;
                        break;
                        }

                     // Took out the af->modifier since it I couldn't find it assigned in do_camp.
                     if(f->follower->in_room != i->in_room /*|| i->in_room != af->modifier*/)
                        {
                        /* they arent in the same room jim! */
                        send_to_char ("You are unable to setup camp as your pets don't seem to be with you here.\n", i);
                        campEmOut = FALSE;
                        break;
                        }
                     }

                  f = f->next;
                  }
               }

            if(campEmOut)
               {
               /* we don't want it wearing off during regular updates, so we add a little extra, and close it
                  out before regular update code can kill it. */
               if(--(af->duration) < 2)
                  {
                  /* done the time, now we rent em out. */
                  affect_from_char(i, SKILL_CAMP);
                  act("$n rolls $mself up in $s bedroll and tunes out the world.", FALSE, i, 0, 0, TO_ROOM);
                  send_to_char("You hunker down for some serious 'roughing it'.\n", i);
                  if(i->desc)
                     connectlog(GET_LEVEL(i), "%s [%s] has camped in [%d].",
                                GET_NAME(i), full_address(i->desc, 0, 0), world[i->in_room].number);
                  else
                     connectlog(GET_LEVEL(i), "%s [&+BLD - %s&n] has camped in [%d].",
                                GET_NAME(i), GET_LAST_LOGIN(i), world[i->in_room].number);

                  GET_HOME(i) = world[i->in_room].number;
                  writeCharacter(i, 6, i->in_room);

                  /* save all savable pets */
                  f = i->followers;
                  while(f)
                     {
                     if(IS_NPC(f->follower) && IS_CSET(f->follower->only.npc->npcact, ACT_SAVE))
                        {       /* can we save it? */
                        writeMobile(f->follower, SAV_TYPE_RENT, f->follower->in_room);
                        extract_char(f->follower);
                        f = i->followers;                                /* rescan */
                        }
                     else
                        f = f->next;
                     }

                  writeCart(i);

                  if(i->specials.cart)
                     extract_cart(i->specials.cart);

                  extract_char(i);
                  i = NULL;
                  continue;
                  }
               }
            }
         else
            {
            logit(LOG_DEBUG, "%s has AFF_CAMP, but no affect structure", GET_NAME(i));
            send_to_char("hmm, something strange has happened to your camp attempt, better petition for an admin.\n", i);
            REMOVE_CBIT(i->specials.affects, AFF_CAMPING);
            }
         }

      if(!IS_AFFECTED(i, AFF_MAJOR_PARALYSIS))
         continue;

      for(af = i->affected; af; af = next_af_dude)
         {
         next_af_dude = af->next;

         if(!(af->type == SPELL_MAJOR_PARALYSIS))
            continue;

         if(af->duration >= 1)
            af->duration--;
         else if(af->duration == -1)
            /* No action */
            af->duration = -1;      /* GODs only! unlimited */
         else
            {
            if(IS_SPELL(af->type) ||
               ((skills[pindex2Skill[af->type]].type >= SKILL_TYPE_CLAIRSENTIENCE) &&
                (skills[pindex2Skill[af->type]].type <= SKILL_TYPE_METAPSIONICS)))
               {
               if(!af->next || ((af->next) && ((af->next->type != af->type) || (af->next->duration > 0))))
                  {
                  if(*spell_wear_off_msg[af->type] && (spell_wear_off_msg[af->type][0] != '!'))
                     {
                     send_to_char(spell_wear_off_msg[af->type], i);
                     send_to_char("\n", i);
                     }
                  }
               }

            affect_remove(i, af);
            }
         }
      }

   for(i = NPC_list; i; i = i_next)
      {
      i_next = i->next;

      /* gonna clear up an ancient carryover, due to items and bugs, it's possible for a player/mob to get to
         STAT_DEAD without dying.  This way they have 20 seconds (maybe) grace and then we kill em off.  JAB */
      if((GET_HIT(i) < -10) || (GET_MAX_HIT(i) < -10))
         {
         SuddenDeath(i, i, "discovered dead");
         i = NULL;
         continue;
         }

      if(!IS_AFFECTED(i, AFF_CHARM))
         {
         if((716 <= mob_index[i->nr].virtual) && (mob_index[i->nr].virtual <= 747))
            {
            die(i);
            i = NULL;
            continue;
            }

         /* Monster Summoning/Lycans/Familiars/Mounts/hordes */
         if((mob_index[i->nr].virtual >= 300 ) && (mob_index[i->nr].virtual <= 399))
            {
            die(i);
            i = NULL;
            continue;
            }

#if 0
         // Pal/Anti Horsies
         if((mob_index[i->nr].virtual == 1233) || (mob_index[i->nr].virtual == 1232))
            {
            extract_char(i);
            i = NULL;
            continue;
            }
#endif

         /* conjured elementals */
         if(((mob_index[i->nr].virtual == 3050) && (world[i->in_room].sector_type != SECT_FIREPLANE)) ||
            ((mob_index[i->nr].virtual == 203)) || // mirror image
            ((mob_index[i->nr].virtual == 204)) || // doppleganger
            ((mob_index[i->nr].virtual == 902)) || // treant
            ((mob_index[i->nr].virtual == 903)) || // phantom steed
            ((mob_index[i->nr].virtual == 904)) || // phantom lizard
            ((mob_index[i->nr].virtual == 905)) || // shade
            ((mob_index[i->nr].virtual == 33027)) || // dancing dagger
            ((mob_index[i->nr].virtual > 905) && (mob_index[i->nr].virtual < 910)) || // elemental kin
            ((mob_index[i->nr].virtual == 3051) && (world[i->in_room].sector_type != SECT_EARTH_PLANE)) ||
            ((mob_index[i->nr].virtual == 3052) && (world[i->in_room].sector_type != SECT_AIR_PLANE)) ||
            ((mob_index[i->nr].virtual == 3053) && (world[i->in_room].sector_type != SECT_WATER_PLANE)) ||
            ((mob_index[i->nr].virtual == 1250) && (world[i->in_room].sector_type != SECT_FIREPLANE)) ||
            ((mob_index[i->nr].virtual == 1251) && (world[i->in_room].sector_type != SECT_EARTH_PLANE)) ||
            ((mob_index[i->nr].virtual == 1252) && (world[i->in_room].sector_type != SECT_AIR_PLANE)) ||
            ((mob_index[i->nr].virtual == 1253) && (world[i->in_room].sector_type != SECT_WATER_PLANE)))
            {
            die(i);
            i = NULL;
            continue;
            }

         /* animated corpses */
         if((mob_index[i->nr].virtual == 1201) || ((mob_index[i->nr].virtual > 1255) && (mob_index[i->nr].virtual < 1263)))
            {
            act("$n crumbles into dust.", FALSE, i, 0, 0, TO_ROOM);
            die(i);
            i = NULL;
            continue;
            }
         }

      /* poison is now VERY ugly, remaining duration is also 'level' of the poison, which determines damage,
         damage is:  max hits / (62 - level) per short_affect (20 seconds) */
      if(affected_by_spell(i, SPELL_POISON))
         {
         for(af = i->affected; af && (af->type != SPELL_POISON); af = af->next)
            ;

         j = af ? af->duration : 0;

         /* slow poison doubles duration, but reduces intensity */
         if(IS_AFFECTED(i, AFF_SLOW_POISON))
            j >>= 2;

         if(damage(i, i, MAX(1, GET_MAX_HIT(i) / MAX(1, (100 - j))), SPELL_POISON))
            continue;
         }

      if(!IS_AFFECTED(i, AFF_MAJOR_PARALYSIS))
         continue;

      for(af = i->affected; af; af = next_af_dude)
         {
         next_af_dude = af->next;

         if(!(af->type == SPELL_MAJOR_PARALYSIS))
            continue;

         if(af->duration >= 1)
            af->duration--;
         else if(af->duration == -1)
            /* No action */
            af->duration = -1;      /* GODs only! unlimited */
         else
            {
            if(IS_SPELL(af->type) ||
               ((skills[pindex2Skill[af->type]].type >= SKILL_TYPE_CLAIRSENTIENCE) &&
                (skills[pindex2Skill[af->type]].type <= SKILL_TYPE_METAPSIONICS)))
               {
               if(!af->next || ((af->next) && ((af->next->type != af->type) || (af->next->duration > 0))))
                  {
                  if(*spell_wear_off_msg[af->type] && (spell_wear_off_msg[af->type][0] != '!'))
                     {
                     send_to_char(spell_wear_off_msg[af->type], i);
                     send_to_char("\n", i);
                     }
                  }
               }

            if(affected_by_spell(i, SPELL_CHARM_PERSON))
               stop_follower(i);
            else
               affect_remove(i, af);
            }
         }
      }
}                               /* short_affect_update */

void affect_update(void)
{
   struct affected_type *af = NULL, *af2 = NULL, *next_af_dude = NULL;
   P_char i = NULL;

   for(i = PC_list; i != NULL; i = i->next)
      {
      if(i->desc && i->desc->connected)
         continue; // not in game

      if(GET_HIT(i) < GET_MAX_HIT(i))
         StartRegen(i, EVENT_HIT_REGEN);

      if(GET_MOVE(i) < GET_MAX_MOVE(i))
         StartRegen(i, EVENT_MOVE_REGEN);

      for(af = i->affected; af != NULL; af = next_af_dude)
         {
         next_af_dude = af->next;
         if(af->type == SPELL_POISON)
            {
            if(NewSaves(i, SAVING_PARA, af->duration))
               af->duration >>= 1;
            }

         /* ugly, but it works...venom poison is not decremented */
         if(af->type == SPELL_VENOM)
            continue;

         if(af->duration >= 1)
            af->duration--;
         else
            {
            /* hackish, sortof, bonus hits from constitution don't wear off */
            if(af->type == SKILL_CON_BONUS)
               continue;

            /* slow poison doesn't last past current poisoning */
            if(af->type == SPELL_POISON && affected_by_spell(i, SPELL_SLOW_POISON))
               {
               for(af2 = i->affected; af2; af2 = af2->next)
                  {
                  if(af2->type == SPELL_SLOW_POISON)
                     {
                     af2->duration = 0;
                     break;
                     }
                  }
               }

            /* slow poison wears off, duration gets cut in half (round up) */
            if(af->type == SPELL_SLOW_POISON && affected_by_spell(i, SPELL_POISON))
               {
               for(af2 = i->affected; af2; af2 = af2->next)
                  {
                  if(af2->type == SPELL_POISON)
                     {
                     af2->duration = (af2->duration + 1) / 2;
                     break;
                     }
                  }
               }

            if(IS_SPELL(af->type) ||
               ((skills[pindex2Skill[af->type]].type >= SKILL_TYPE_CLAIRSENTIENCE) &&
                (skills[pindex2Skill[af->type]].type <= SKILL_TYPE_METAPSIONICS)))
               {
               if(!af->next || ((af->next) && ((af->next->type != af->type) || (af->next->duration > 0))))
                  {
                  if(*spell_wear_off_msg[af->type] && (spell_wear_off_msg[af->type][0] != '!'))
                     {
                     send_to_char(spell_wear_off_msg[af->type], i);
                     send_to_char("\n", i);
                     }
                  }
               }

            affect_remove(i, af);
            }
         }

      if(CHAR_FALLING(i))
         falling_char(i);
      }

   for(i = NPC_list; i != NULL; i = i->next)
      {
      if((GET_HIT(i) < GET_MAX_HIT(i)) || (GET_HIT(i) > GET_MAX_HIT(i)))
         StartRegen(i, EVENT_HIT_REGEN);

      if(GET_MOVE(i) < GET_MAX_MOVE(i))
         StartRegen(i, EVENT_MOVE_REGEN);

      for(af = i->affected; af != NULL; af = next_af_dude)
         {
         next_af_dude = af->next;
         if(af->type == SPELL_POISON)
            {
            if(NewSaves(i, SAVING_PARA, af->duration))
               af->duration >>= 1;
            }

         /* ugly, but it works...venom poison is not decremented */
         if(af->type == SPELL_VENOM)
            continue;

         if(af->duration >= 1)
            af->duration--;
         else
            {
            /* hackish, sortof, bonus hits from constitution don't wear off */
            if(af->type == SKILL_CON_BONUS)
               continue;

            /* slow poison doesn't last past current poisoning */
            if(af->type == SPELL_POISON && affected_by_spell(i, SPELL_SLOW_POISON))
               {
               for(af2 = i->affected; af2; af2 = af2->next)
                  {
                  if(af2->type == SPELL_SLOW_POISON)
                     {
                     af2->duration = 0;
                     break;
                     }
                  }
               }

            /* slow poison wears off, duration gets cut in half (round up) */
            if(af->type == SPELL_SLOW_POISON && affected_by_spell(i, SPELL_POISON))
               {
               for(af2 = i->affected; af2; af2 = af2->next)
                  {
                  if(af2->type == SPELL_POISON)
                     {
                     af2->duration = (af2->duration + 1) / 2;
                     break;
                     }
                  }
               }

            if(IS_SPELL(af->type) ||
               ((skills[pindex2Skill[af->type]].type >= SKILL_TYPE_CLAIRSENTIENCE) &&
                (skills[pindex2Skill[af->type]].type <= SKILL_TYPE_METAPSIONICS)))
               {
               if(!af->next || ((af->next) && ((af->next->type != af->type) || (af->next->duration > 0))))
                  {
                  if(*spell_wear_off_msg[af->type] && (spell_wear_off_msg[af->type][0] != '!'))
                     {
                     send_to_char(spell_wear_off_msg[af->type], i);
                     send_to_char("\n", i);
                     }
                  }
               }

#if 0   /* This is crashing us... --CRM*/
            if(affected_by_spell(i, SPELL_CHARM_PERSON))
               stop_follower(i);
            else
#endif
               affect_remove(i, af);
            }
         }

      if(CHAR_FALLING(i))
         falling_char(i);
      }
}

/* ch CHAR_FALLING, this routine updates status, moves them, sends appropriate
   messages, kills them if neccessary, and handles event updates.  returns
   TRUE if they are actually falling, FALSE otherwise.

   assumption 15' per room fallen:
   first check: they fall 1 room in 1 second (4 pulses)       31mph
   second check: they fall 1 room in 1/2 second (2 pulses)    43mph
   extra checks: they fall one room per pulse (1/4 second)    +8mph

   speed piles up (to 250 mph ('terminal' velocity)) since this is no longer
   a loop, they can fall 'forever' if someone sets up a 'bottomless' pit.
   This isn't completely realistic but it will suffice, they'll hit 250mph
   in 32 pulses (~8 seconds) after falling 33 rooms (about 500'). If/when
   they hit bottom, they take:  (dice((speed - number(27, 30)), 9) - dex)
   damage so falling just one room will probably not do much damage
   (4d9-dex max), but falling a long ways can be decidedly fatal.

   Note that since this is event driven, if they are summoned out, they are
   still falling that fast, and they'll take that damage.  Since this isn't
   instantaneous they can (theoretically) save themselves by starting to fly
   or levitate (we can now add feather fall that kicks in if they exceed 20
   mph (they'd still be falling though *grin*)
 */

bool falling_char(P_char ch)
{
   P_event e1 = NULL;
   int dam = 0, speed = 0, i, new_room;
   ubyte save_brf;

   if(!ch)
      {
      logit(LOG_EXIT, "falling_char with NULL char.");
      dump_core();
      }

   if(IS_NPC(ch))
      return FALSE;

   for(e1 = ch->events; e1; e1 = e1->next)
      {
      if(e1->type == EVENT_FALLING_CHAR)
         break;
      }

   if(!e1)
      {
      /* if not already falling */
      if(current_event && (current_event->type == EVENT_FALLING_CHAR) && (ch == (P_char) current_event->actor.a_ch))
         {
         /* event call, we have problems */
         logit(LOG_EXIT, "Event call (type %s) to falling_char, no event found", event_names[current_event->type]);
         dump_core();
         }

      /* ch has just stepped into space, initiate the plunge */
      act("$n has just realized $e has no visible means of support!", TRUE, ch, 0, 0, TO_ROOM);
      if(GET_STAT(ch) > STAT_SLEEPING)
         send_to_char("You rediscover the law of gravity...\n...the hard way!\n", ch);
      else if(GET_STAT(ch) > STAT_INCAP)
         send_to_char("You get a sinking feeling.\n", ch);
      else
         send_to_char("Just when it seemed things couldn't get any worse...\n", ch);

      if(world[ch->in_room].dir_option[5] && (world[ch->in_room].dir_option[5]->to_room != NOWHERE))
         {
         /* hehe, they hang there for 1 pulse before they start falling */
         AddEvent(EVENT_FALLING_CHAR, 1, TRUE, ch, 1);
         return TRUE;
         }
      else
         {
         send_to_char("Boy, that ground is pretty far away and is coming up quick!", ch);
         act("$n suddenly remembers that $e can't fly, right before dropping like a stone.", FALSE, ch, 0, 0, TO_ROOM);
         logit(LOG_DEBUG, "Room %d, is NO_GROUND but has no valid 'down' exit", world[ch->in_room].number);
         /* world[ch->in_room].sector_type = SECT_INSIDE; */
         SuddenDeath(ch,ch,"Forgetting they can't fly in a NO_GROUND room with no down exit");
         return TRUE;
         }
      }

   if(!current_event || (current_event->type != EVENT_FALLING_CHAR) || (ch != (P_char) current_event->actor.a_ch))
      {
      /* we have a falling_char event, but this isn't an event call, return */
      return TRUE;
      }

   /* get here, it's a normal event call */
   speed = (int) e1->target.t_num;
   i = ch->in_room;

   if(world[i].dir_option[5])
      {
      new_room = world[i].dir_option[5]->to_room;

      if(speed < 45)
         act("$n drops from sight.", TRUE, ch, 0, 0, TO_ROOM);
      else if(speed < 90)
         act("Someone drops from sight.", TRUE, ch, 0, 0, TO_ROOM);
      else
         act("A large (screaming) object drops from sight!", TRUE, ch, 0, 0, TO_ROOM);

      char_from_room(ch);
      char_to_room(ch, new_room, -2);

      /* change falling speed */
      if(IS_AFFECTED(ch, AFF_LEVITATE) || IS_AFFECTED(ch, AFF_FLY))
         {
         /* 1 gravity decel for lev, 1/2 grav for fly */
         speed -= (IS_AFFECTED(ch, AFF_FLY)) ? 4 : 8;
         if(speed <= 0)
            {
            speed = 0;
            send_to_char("You slow to a stop.  WHEW!\n", ch);
            act("$n floats in from above.", TRUE, ch, 0, 0, TO_ROOM);
            do_look(ch, 0, -2);
            return FALSE;
            }
         else
            send_to_char("You slow down a little.\n", ch);
         }
      else
         {
         if(speed == 1)
            speed = 31;             /* first room */
         else if(speed == 31)
            speed = 43;             /* second room */
         else
            speed += 8;             /* additional rooms */

         if(speed > 250)
            speed = 250;
         }
      }
   else
      new_room = i;  /* this happens when faller is summoned/transed/teleported out */


   if(!world[new_room].dir_option[5] || (world[new_room].dir_option[5]->to_room == NOWHERE))
      {
      /* oh dear, we seem to have run out of falling room!  Muhahaha */
      if(IS_AFFECTED(ch, AFF_CATFALL))
         dam = dice(((speed>>2) - number(27, 30)), 9) - (GET_C_AGI(ch) / 4);
      else
         dam = dice((speed - number(27, 30)), 9) - (GET_C_AGI(ch) / 4);

      if(dam <= 0)
         {
         if(IS_AFFECTED(ch, AFF_CATFALL))
            send_to_char("Your catfall projection pays off.\n", ch);
         send_to_char("You land deftly on your feet, nice jump!\n", ch);
         do_look(ch, 0, -2);
         act("$n drops in from above, landing neatly.", TRUE, ch, 0, 0, TO_ROOM);
         return FALSE;
         }
      else
         {
         if(IS_AFFECTED(ch, AFF_CATFALL))
            send_to_char("Although your catfall ability absorbs the brunt of the impact.\nYou still land with stunning force!\n", ch);
         else
            send_to_char("You land with stunning force!\n", ch);
         act("$n falls in from above, landing in a crumpled heap!", TRUE, ch, 0, 0, TO_ROOM);
         if(damage(ch, ch, dam, DAMAGE_FALLING))
            return TRUE;

         SET_POS(ch, number(0, 2) + GET_STAT(ch));
         Stun(ch, (100 * dam / GET_MAX_HIT(ch)));  /* 1-100 */

         /* also can knock them out for a time. */
         if(number(1, (100 * dam / GET_MAX_HIT(ch))) >
            number(STAT_INDEX(GET_C_CON(ch)) / 2, STAT_INDEX(GET_C_CON(ch)) * 3))
            KnockOut(ch, number(2, MAX(2, (100 - GET_C_CON(ch)) / 4)));     /* 20 - 220 seconds */
         return TRUE;
         }
      }

   /* just passing through */
   if(speed < 45)
      {
      act("$n falls in from above.", TRUE, ch, 0, 0, TO_ROOM);
      do_look(ch, 0, -2);
      }
   else if(speed < 90)
      {
      act("Someone hurtles in from above.", TRUE, ch, 0, 0, TO_ROOM);
      if(IS_PC(ch))
         {
         save_brf = ch->only.pc->pcact[0];
         SET_CBIT(ch->only.pc->pcact, PLR_BRIEF);
         do_look(ch, 0, -2);
         ch->only.pc->pcact[0] = save_brf;
         }
      }
   else
      {
      act("A large (screaming) object plummets in from above", TRUE, ch, 0, 0, TO_ROOM);
      send_to_char("You fall, shapes and sounds shredding past you.\n", ch);
      }

   AddEvent(EVENT_FALLING_CHAR, (speed == 31) ? 4 : (speed == 43) ? 2 : 1, TRUE, ch, speed);
   return TRUE;
}

/* obj OBJ_FALLING, this routine updates speed, moves it, sends appropriate
   messages, destroys/damages objects as neccessary, and handles event updates.
   returns TRUE if object is falling, FALSE otherwise.

   assumption 15' per room fallen:
   first check:  falls 1 room in 1 second (4 pulses)       31mph
   second check: falls 1 room in 1/2 second (2 pulses)    43mph
   extra checks: falls one room per pulse (1/4 second)    +8mph

   speed piles up (to 250 mph ('terminal' velocity)) since this is no longer
   a loop, it can fall 'forever' if someone sets up a 'bottomless' pit.
   This isn't completely realistic but it will suffice, it'll hit 250mph
   in 32 pulses (~8 seconds) after falling 33 rooms (about 500'). If/when
   it hits bottom, it'll take some damage (most things), or be destroyed.

   Note that since this is event driven, if object is grabbed it will xfer
   energy to the grabber, which will be in the form of increased speed (if
   grabber is also falling) and/or damage.  Same holds true if object hits
   a character (I'm not going to include falling objects hitting other objects).
 */

bool falling_obj(P_obj obj)
{
   P_event e1 = NULL;
   int dam = 0, speed = 0, i, new_room;

   if(!obj)
      {
      logit(LOG_EXIT, "falling_obj: NULL obj.");
      dump_core();
      }

   /* PC Corpses and magical floating items will not take the plunge --  DA 9/27/00 */
   if((obj->value[1] == PC_CORPSE)  || (IS_SET(obj->extra_flags, ITEM_MAGIC) && IS_SET(obj->extra_flags, ITEM_FLOAT)))
      return FALSE;

   for(e1 = obj->events; e1; e1 = e1->next)
      {
      if(e1->type == EVENT_FALLING_OBJ)
         break;
      }

   if(!e1)
      {
      /* if not already falling */
      if(!OBJ_ROOM(obj))
         {
         logit(LOG_EXIT, "falling_obj: (%d) %s in NOWHERE.", obj_index[obj->R_num].virtual, obj->name);
         dump_core();
         }

      if(current_event && (current_event->type == EVENT_FALLING_OBJ) && (obj == (P_obj) current_event->actor.a_obj))
         {
         /* event call, we have problems */
         logit(LOG_EXIT, "Event call (type %s) to falling_obj, no event found", event_names[current_event->type]);
         dump_core();
         }

      /* obj has just been dropped/moved into space, initiate the plunge */
      if(!world[obj->loc.room].dir_option[5] || (world[obj->loc.room].dir_option[5]->to_room == NOWHERE))
         {
         if(GET_ITEM_TYPE(obj) != ITEM_CORPSE)
            {
            act("$p plunges from sight, forever lost.", TRUE, 0, obj, 0, TO_ROOM);
            logit(LOG_DEBUG, "Room %d, is NO_GROUND but has no valid 'down' exit", world[obj->loc.room].number);
            /* world[obj->loc.room].sector_type = SECT_INSIDE;*/
            obj_from_room(obj);
            obj_to_room(obj, 0);
            return TRUE;
            }
         }

      debuglog(51, DS_AZUTH, "%s falling in %d", obj ? obj->short_description : "NULL", world[obj->loc.room].number);
      act("$p drops from sight.", TRUE, 0, obj, 0, TO_ROOM);
      new_room = world[obj->loc.room].dir_option[5]->to_room;
      AddEvent(EVENT_FALLING_OBJ, 4, TRUE, obj, 31);  /* 1 second and speed 31 mph after that */
      obj_from_room(obj);
      obj_to_room(obj, new_room);
      return TRUE;
      }

   if(!current_event || (current_event->type != EVENT_FALLING_OBJ) || (obj != (P_obj) current_event->actor.a_obj))
      {
      /* we have a falling_obj event, but this isn't an event call, return */
      return TRUE;
      }

   /* get here, it's a normal event call */
   if(!OBJ_ROOM(obj))
      {
      /* somebody snagged it while it was falling */
      /* may have to do the damage here, but more likely in get() */
      return FALSE;
      }

   speed = (int) e1->target.t_num;
   i = obj->loc.room;

   if(world[i].dir_option[5])
      {
      new_room = world[i].dir_option[5]->to_room;

      if(speed < 45)
         {
         debuglog(51, DS_AZUTH, "%s falling <45 in %d", obj ? obj->short_description : "NULL", world[obj->loc.room].number);
         act("$p drops from sight.", TRUE, 0, obj, 0, TO_ROOM);
         }
      else
         {
         debuglog(51, DS_AZUTH, "%s falling >=45 in %d", obj ? obj->short_description : "NULL", world[obj->loc.room].number);
         act("Something drops from sight.", TRUE, 0, obj, 0, TO_ROOM);
         }

      obj_from_room(obj);
      obj_to_room(obj, new_room);

      /* change falling speed */
      if(speed == 31)
         speed = 43;               /* second room */
      else
         speed += 8;               /* additional rooms */

      if(speed > 250)
         speed = 250;
      }
   else
      new_room = i;  /* this happens when object is moved out without hitting bottom */


   if(!world[new_room].dir_option[5] || (world[new_room].dir_option[5]->to_room == NOWHERE))
      {
      /* we seem to have run out of falling room! */
      dam = dice((speed - number(27, 30)), 9);

      if(dam <= 0)
         {
         debuglog(51, DS_AZUTH, "%s falling dam<0 in %d", obj ? obj->short_description : "NULL", world[obj->loc.room].number);
         act("$p wafts to the ground.", TRUE, 0, obj, 0, TO_ROOM);
         return FALSE;
         }
      else
         {
         debuglog(51, DS_AZUTH, "%s falling dam>0 in %d", obj ? obj->short_description : "NULL", world[obj->loc.room].number);
         act("$p crashes into the ground.", TRUE, 0, obj, 0, TO_ROOM);

         /* add object damage/destroy, also hit people, here */
         return FALSE;
         }
      }

   /* just passing through */
   if(speed < 45)
      {
      debuglog(51, DS_AZUTH, "%s falling #2 <45 in %d", obj ? obj->short_description : "NULL", world[obj->loc.room].number);
      act("$p falls in from above.", TRUE, 0, obj, 0, TO_ROOM);
      }
   else
      {
      debuglog(51, DS_AZUTH, "%s falling #2 >=45 in %d", obj ? obj->short_description : "NULL", world[obj->loc.room].number);
      act("Something plummets in from above.", TRUE, 0, obj, 0, TO_ROOM);
      }

   AddEvent(EVENT_FALLING_OBJ, (speed == 31) ? 4 : (speed == 43) ? 2 : 1, TRUE, obj, speed);
   return TRUE;
}

int kala_spell(int spl)
{
   int i, j = 0;

   for(i = 0; i < LAST_CLASS; i++)
      {
      if((skills[spl].class[i].rlevel > 0) && (skills[spl].class[i].rlevel <= MAXLVLMORTAL))
         switch(i + 1)
            {
            case CLASS_SORCERER:
            case CLASS_NECROMANCER:
            case CLASS_LICH:
            case CLASS_CONJURER:
            case CLASS_INVOKER:
            case CLASS_ENCHANTER:
            case CLASS_ILLUSIONIST:
            case CLASS_ELEMENTALIST:
               j |= 1;
               break;
            case CLASS_CLERIC:
            case CLASS_SHAMAN:
            case CLASS_DRUID:
            case CLASS_PALADIN:
            case CLASS_ANTIPALADIN:
            case CLASS_RANGER:
            case CLASS_DIRERAIDER:
               j |= 2;
               break;
            }
      }

   return j;
}

#define IS_MAGESPELL(spl) ((kala_spell((spl)) & 1))
#define IS_CLERICSPELL(spl) ((kala_spell((spl)) & 2))

void say_spell(P_char ch, int si)
{
   char splwd[MAX_INPUT_LENGTH];
   char Gbuf1[MAX_STRING_LENGTH], Gbuf2[MAX_STRING_LENGTH];
   int j, offs;
   P_char t_ch;

   struct syllable
      {
      char org[10];
      char new[10];
      };

   struct syllable syls[] =
   {
      {" ", " "},
      {"ar", "abra"},
      {"au", "kada"},
      {"bless", "vremm"},
      {"blind", "nore"},
      {"bur", "mosa"},
      {"cu", "judi"},
      {"de", "oculo"},
      {"en", "unso"},
      {"light", "dies"},
      {"lo", "hi"},
      {"mor", "zak"},
      {"move", "sido"},
      {"ness", "lacri"},
      {"ning", "illa"},
      {"per", "duda"},
      {"ra", "gru"},
      {"re", "candus"},
      {"son", "sabru"},
      {"tect", "infra"},
      {"tri", "cula"},
      {"ven", "nofo"},
      {"a", "a"},
      {"b", "b"},
      {"c", "q"},
      {"d", "e"},
      {"e", "z"},
      {"f", "y"},
      {"g", "o"},
      {"h", "p"},
      {"i", "u"},
      {"j", "y"},
      {"k", "t"},
      {"l", "r"},
      {"m", "w"},
      {"n", "i"},
      {"o", "a"},
      {"p", "s"},
      {"q", "d"},
      {"r", "f"},
      {"s", "g"},
      {"t", "h"},
      {"u", "j"},
      {"v", "z"},
      {"w", "x"},
      {"x", "n"},
      {"y", "l"},
      {"z", "k"},
      {"", ""}
   };

   if(IS_NPC(ch) && !ch->desc && ALONE(ch))
      return;

   strcpy(Gbuf1, "");
   strcpy(splwd, skills[si].name);

   offs = 0;

   while(*(splwd + offs))
      {
      for(j = 0; *(syls[j].org); j++)
         {
         if(strn_cmp(syls[j].org, splwd + offs, strlen(syls[j].org)) == 0)
            {
            strcat(Gbuf1, syls[j].new);
            if(strlen(syls[j].org))
               offs += strlen(syls[j].org);
            else
               ++offs;
            }
         }
      }

   sprintf(Gbuf2, "$n utters the words, '%s'", Gbuf1);
   sprintf(Gbuf1, "$n utters the words, '%s'", skills[si].name);

   for(t_ch = world[ch->in_room].people; t_ch; t_ch = t_ch->next_in_room)
      {
      if(t_ch == ch)
         continue;

      if(IS_TRUSTED(t_ch) || IS_NPC(t_ch))
         act(Gbuf1, FALSE, ch, 0, t_ch, TO_VICT);
      else if((IS_MAGESPELL(si) && (number(1, 101) <= GET_CHAR_SKILL(t_ch, SKILL_SPELL_KNOWLEDGE_MAGICAL))) ||
              (IS_CLERICSPELL(si) && (number(1, 101) <= GET_CHAR_SKILL(t_ch, SKILL_SPELL_KNOWLEDGE_CLERICAL))))
         {
         act(Gbuf1, FALSE, ch, 0, t_ch, TO_VICT);
         CharSkillGainChance(t_ch, IS_MAGESPELL(si) ? SKILL_SPELL_KNOWLEDGE_MAGICAL : SKILL_SPELL_KNOWLEDGE_CLERICAL, 0);
         }
      else
         act(Gbuf2, FALSE, ch, 0, t_ch, TO_VICT);
      }
}

/* right now, processing is done other way than this:  the spellcast event is called once / second, to check
   if quick chant / spell knowledge work as they should. (i.e. show how much of spell is left, and stuff) */
int SpellCastTime(P_char ch, int spl)
{
   int dura = skills[spl].beats;
   struct affected_type *af;

   if(mob_index[ch->nr].virtual == 19700)
      {
      if(IS_AFFECTED(ch, AFF_TIME_STOP))
         {
         if(skills[spl].harmful == 1)
            dura >>=1;
         else dura = 0;
         }
      }

   else
      {
      if((skills[spl].harmful == 1) && IS_AFFECTED(ch, AFF_TIME_STOP))
         nuke_time_stop(ch);
      else if(IS_AFFECTED(ch, AFF_TIME_STOP))
         {
         if(fight_in_room(ch))
            nuke_time_stop(ch);
         else
            dura = 0;
         }
      }

   if(IS_AFFECTED(ch, AFF_RES_PENALTY))
      {
      for(af = ch->affected; af; af = af->next)
         {
         if((af->type == SKILL_RES_PENALTY) && (af->duration > 0))
            {
            dura = dura * 100 / (MIN(12, af->duration) * 8);
            break;
            }
         }
      }

   return dura;
}

void SpellCastShow(P_char ch, int spl)
{
   P_char tch;
   int idok, detharm = 0;
   char Gbuf1[MAX_STRING_LENGTH];

   for(tch = world[ch->in_room].people; tch; tch = tch->next_in_room)
      {
      if(tch == ch)
         continue;

      idok = 0;
      if(IS_CLERICSPELL(spl) && (number(1, 101) <= GET_CHAR_SKILL(tch, SKILL_SPELL_KNOWLEDGE_CLERICAL)))
         idok = 2;

      if(IS_MAGESPELL(spl) && (number(1, 101) <= GET_CHAR_SKILL(tch, SKILL_SPELL_KNOWLEDGE_MAGICAL)))
         idok = 1;

      if(idok)
         {
         detharm = 1;
         /* has chance of recognizing the spell fellow starts to cast */
         /* (and 100% chance of recognizing whether it is harmful or not) */
         if(number(1, 100) > 60)
            idok = 0;
         }

      if(!detharm && (GET_C_INT(ch) > number(1, 100)))
         detharm = 1;

      if(GET_CLASS(ch)!=CLASS_PSIONICIST)
         {
         sprintf(Gbuf1, "$n &+cstarts casting %s spell%s%s%s.",
                 (detharm && skills[spl].harmful) ? "an offensive" : "a",
                 idok ? " called '" : "", idok ? skills[spl].name : "", idok ? "'" : "");
         }
      else
         {
         sprintf(Gbuf1, "$n &+cprojects %s spell%s%s%s.",
                 (detharm && skills[spl].harmful) ? "an offensive" : "a",
                 idok ? " called '" : "", idok ? skills[spl].name : "", idok ? "'" : "");
         }

      act(Gbuf1, TRUE, ch, 0, tch, TO_VICT);
      if(idok)
         CharSkillGainChance(tch, (idok == 1) ? SKILL_SPELL_KNOWLEDGE_MAGICAL : SKILL_SPELL_KNOWLEDGE_CLERICAL, 0);
      }
}

/* function returns actual saving throw.  Used by NewSaves and do_stat. JAB */

int find_save(P_char ch, int save_type)
{
   int save = 200, t_save;

   if(!ch || (save_type < SAVING_PARA) || (save_type > SAVING_SPELL))
      {
      logit(LOG_DEBUG, "Invalid arguments to find_save");
      return -1;
      }

   /* saving throw table has changed, now stores only max/min for each class,
      and for each type.  Actual save is somewhere between those two, linear
      change with level, and yes, gods and high level mobs can have better than
      mortal saving throws (why shouldn't they?) */
   if(IS_PC(ch))
      {
      save = (int) (saving_throws[GET_CLASS(ch)][save_type][0] -
                    ((int) (saving_throws[GET_CLASS(ch)][save_type][0] -
                            saving_throws[GET_CLASS(ch)][save_type][1] + 1) *
                     GET_LEVEL(ch) / MAXLVLMORTAL));

      /* specials mods to saves */
      if(GET_CLASS(ch) == CLASS_PALADIN)
         save -= 10;

      if(save_type == SAVING_PARA)
         {
         if(GET_RACE(ch) == RACE_MOUNTAIN)
            save -= 25;

         if(GET_RACE(ch) == RACE_BARBARIAN)
            save -= 10;
         }
      }
   else
      {
      /* mobs (which can have multiple flags set) save at the best rate they can.
         so, this part, checks applicable flags, and chooses lowest save. */
      if(IS_MAGE(ch))
         {
         t_save = (int) (saving_throws[CLASS_ENCHANTER][save_type][0] -
                         ((int) (saving_throws[CLASS_ENCHANTER][save_type][0] -
                                 saving_throws[CLASS_ENCHANTER][save_type][1] + 1) *
                          GET_LEVEL(ch) / MAXLVLMORTAL));

         if(t_save < save)
            save = t_save;
         }
      if(IS_THIEF(ch))
         {
         t_save = (int) (saving_throws[CLASS_THIEF][save_type][0] -
                         ((int) (saving_throws[CLASS_THIEF][save_type][0] -
                                 saving_throws[CLASS_THIEF][save_type][1] + 1) *
                          GET_LEVEL(ch) / MAXLVLMORTAL));

         if(t_save < save)
            save = t_save;
         }
      if(IS_CLERIC(ch))
         {
         t_save = (int) (saving_throws[CLASS_CLERIC][save_type][0] -
                         ((int) (saving_throws[CLASS_CLERIC][save_type][0] -
                                 saving_throws[CLASS_CLERIC][save_type][1] + 1) *
                          GET_LEVEL(ch) / MAXLVLMORTAL));

         if(t_save < save)
            save = t_save;
         }
      if(IS_PSIONICIST(ch))
         {
         t_save = (int) (saving_throws[CLASS_PSIONICIST][save_type][0] -
                         ((int) (saving_throws[CLASS_PSIONICIST][save_type][0] -
                                 saving_throws[CLASS_PSIONICIST][save_type][1] + 1) *
                          GET_LEVEL(ch) / MAXLVLMORTAL));

         if(t_save < save)
            save = t_save;
         }
      /* default save is Warrior, if they don't have a class set */
      if(save >= 100)
         {
         t_save = (int) (saving_throws[CLASS_WARRIOR][save_type][0] -
                         ((int) (saving_throws[CLASS_WARRIOR][save_type][0] -
                                 saving_throws[CLASS_WARRIOR][save_type][1] + 1) *
                          GET_LEVEL(ch) / MAXLVLMORTAL));

         if(t_save < save)
            save = t_save;
         }
      }

   return save;
}

/* The following two functions are for new AreaSave idea  */
/* area_valid_targets returns the number of area targets  */
/* in the room.  This number will then be fed to AreaSave */

int area_valid_targets(P_char ch)
{
   int num_mobs = 0;
   P_char next, tch;

   if(!ch)
      return FALSE;

   /* for now this doesn't effect mob spells, does affect pc pets */
   if(IS_NPC(ch) && (!IS_PET(ch)))
      return 0;

   for(tch = world[ch->in_room].people; tch; tch = next)
      {
      next = tch->next_in_room;
      if(AreaAffectCheck(ch, tch))
         num_mobs ++;
      }

   if(GET_CLASS(ch) == CLASS_INVOKER)
      num_mobs = MAX(0, num_mobs - 2);

   return num_mobs;
}

/* AreaSave gives the mob a chance to not be in the area of   */
/* an incoming area (avoiding all damage), take half damage   */
/* or take full damage.  Heavily influenced by number of mobs */
/* in the room.  Effect caps at 10 mobs in the room - Iyachtu */

int AreaSave(P_char ch, P_char victim, int *damage, int num_mobs)
{
   int area_save, random_number, tmp;

   tmp = *damage;
#if 0
   /* This will reduce area spells by 15% at 10+ mobs - Iyachtu */
   tmp = (tmp * (100 - BOUNDED(0, num_mobs * 3 / 2, 15)) / 100);
#endif

   area_save = BOUNDED(50, 150 - (num_mobs*10), 150);
   random_number = number(1, 151);
   if(random_number < area_save)
      {
      *damage = tmp;
      return FALSE;
      }
   else if(random_number < (area_save + 50))
      {
      tmp >>= 1;
      *damage = tmp;
      return TRUE;
      }
   else if(victim)
      {
      c_act("$N is outside of the area of the blast!", FALSE, ch, NULL, victim, TO_CHAR, SPELL_DMG);
      c_act("$N is outside of the area of the blast!", FALSE, ch, NULL, victim, TO_VICT, SPELL_DMG);
      c_act("$N is outside of the area of the blast!", FALSE, ch, NULL, victim, TO_NOTVICT, SPELL_DMG);
      tmp = 0;
      *damage = tmp;
      return AVOID_AREA;
      }

   return FALSE;
}

/* ok, NewSaves is a more flexible version of saves_spell, to remain backwards
   compatible, mod is a modification to the SAVE, not the roll, thus a negative
   mod makes the save more likely to return TRUE, from the saving character's
   point of view, less is more.  Also, mobs now save as a PC of their level,
   as a Warrior if no ACT_HAS_<class> bits are set, or as the best.  -JAB */

bool NewSaves(P_char ch, int save_type, int mod)
{
   int save, change_type = 0;

   if(save_type == SAVING_SPELL_ENCH)
      {
      change_type = TRUE;
      save_type = SAVING_SPELL;
      }

   if(!ch || (save_type < SAVING_PARA) || (save_type > SAVING_SPELL))
      {
      logit(LOG_DEBUG, "Invalid arguments to NewSaves");
      debuglog(51, DS_BADPARAMS, "Invalid arguments sent to NewSaves");
      return FALSE;
      }

   if(GET_STAT(ch) == STAT_DEAD)
      return FALSE;

   if((save = find_save(ch, save_type)) == -1)
      return FALSE;               /* error in find_save */

   if(change_type && IS_AFFECTED(ch, AFF_BANSHEE_WAIL))
      mod = mod + 5;

   /* save file scale has changed, so need to change meaning of the mods to
      it.  For now, we just multiply the mod by 5. */
   save += (ch->specials.apply_saving_throw[save_type] + mod) * 5;

   /* always 1% chance to fail/save regardless of saving throw */
   return(BOUNDED(1, save, 99) < number(1, 100));
}

/* left in for compatibility. JAB */

bool saves_spell(P_char ch, int save_type)
{
   return(NewSaves(ch, save_type, 0));
}

/*
 * Function to skip over the leading spaces of a string.
 */
void skip_spaces(char **string)
{
   for(; **string && isspace(**string); (*string)++)
      ;
}

int SpellCastChance(P_char ch, int spl)
{
#ifdef SPELL_DEBUG
   char Gbuf1[512], Gbuf2[512];
#endif
   int a;

   if(IS_NPC(ch))
      return(55 + STAT_INDEX(GET_C_INT(ch)) + STAT_INDEX(GET_C_WIS(ch)));

   a = 45;

#ifdef SPELL_DEBUG
   strcpy(Gbuf1, "");
   sprintf(Gbuf2, "Base: %d %%.\n", a);
   strcat(Gbuf1, Gbuf2);
#endif


   if(meming_class(GET_CLASS(ch)))
      {
      a += (GET_CHAR_SKILL(ch, SKILL_SPELL_KNOWLEDGE_MAGICAL) + 9) / 3;
      debuglog(51, DS_IYACHTU, "%d", a);
      }
   else
      {
      a += (GET_CHAR_SKILL(ch, SKILL_SPELL_KNOWLEDGE_CLERICAL) + 9) / 3;
      debuglog(51, DS_IYACHTU, "clerical %d", a);
      }

#if 0
   a += GET_CHAR_SKILL(ch, SKILL_CAST_GENERIC) / 3;
#endif

#ifdef SPELL_DEBUG
   sprintf(Gbuf2, "Generic applied: %d %%.\n", a);
   strcat(Gbuf1, Gbuf2);
#endif

   if((GET_SPELLTYPE(spl) != 0) && GET_CHAR_SKILL(ch, skilltype_of_spell(spl)))
      a += (GET_CHAR_SKILL(ch, skilltype_of_spell(spl)) / 3);
   else
      a += (GET_CHAR_SKILL(ch, SKILL_CAST_GENERIC) / 3);

#ifdef SPELL_DEBUG
   sprintf(Gbuf2, "Type applied: %d %%.\n", a);
   strcat(Gbuf1, Gbuf2);
#endif

   if(IS_MAGESPELL(spl))
      a += STAT_INDEX(GET_C_INT(ch));
   else
      a += STAT_INDEX(GET_C_WIS(ch));

#ifdef SPELL_DEBUG
   sprintf(Gbuf2, "Stats applied: %d %%.\n", a);
   strcat(Gbuf1, Gbuf2);
   send_to_char(Gbuf1, ch);
#endif

   /* chop chop chop - over 97% chance is silly. */
   return MIN(97, a);
}

struct spell_target_data
   {
   int ttype;
   P_char t_char;
   P_obj t_obj;
   char *arg;
   } common_target_data;

void StopCasting(P_char ch)
{
   int circle, num;
   struct spellcast_datatype *arg;

   for(arg = spellcast_stack; (arg && arg->ch != ch); arg = arg->next);

   if(IS_AFFECTED(ch, AFF_CASTING))
      {
      if(meming_class(GET_CLASS(ch)))
         {
         send_to_char("You abort your spell before its done!\n", ch);
         act("$n stops invoking abruptly!", TRUE, ch, 0, 0, TO_ROOM);
         }
      else
         {
         send_to_char("You abort your prayer before its done!\n", ch);
         act("$n stops chanting abruptly!", TRUE, ch, 0, 0, TO_ROOM);
         }

      NukeCharEventType(ch, EVENT_SPELLCAST);
      REMOVE_CBIT(ch->specials.affects, AFF_CASTING);
      }
   // why log it? maybe they not a caster at all
   //  else {
   //    logit(LOG_DEBUG, "CASTING not set on %s in StopCasting()", GET_NAME(ch));
   //  }

   if(arg)
      {
      if(!IS_TRUSTED(ch))
         {
         if(IS_PC(ch))
            {
#ifdef NEW_BARD
            if(GET_CLASS(ch) == CLASS_BARD || GET_CLASS(ch) == CLASS_BATTLECHANTER)
               {
               if(ch->only.pc->available_spells[GetSpellCircle(ch, arg->spell)] > 0)
                  ch->only.pc->available_spells[GetSpellCircle(ch, arg->spell)]--;
               else
                  /* we screwed up, best to know - Iyachtu */
                  dump_core();
               }
            else
#endif
               if(ch->only.pc->skills[skills[arg->spell].pindex].memorized > 0)
               {
               ch->only.pc->skills[skills[arg->spell].pindex].memorized--;
               add_new_memorize_spell(ch, skills[arg->spell].pindex);
               }
            else
               {
               send_to_char("Yer slots memorized are fubared. Fixing it, but if this happens oft, report\nthis to an admin, please.\n", ch);
               ch->only.pc->skills[skills[arg->spell].pindex].memorized = 0;
               }
            }
         else
            {
            /* NPCs handled slightly different still */
            if(GET_STAT(ch) != STAT_DEAD)
               {
               circle = GetLowestSpellCircle(arg->spell);
               ch->only.npc->spells_in_circle[circle]--;
               num = ch->only.npc->spells_in_circle[circle];
               ch->only.npc->spells_in_circle[circle] = BOUNDED(0, num, 127);
               ch->only.npc->spells_in_circle[0]++;
               }
            }
         }

      nuke_spellcast(arg);
      }
   // why log it? maybe they not a caster at all
   //  else {
   //    logit(LOG_DEBUG, "no element in stack for %s in StopCasting()", GET_NAME(ch));
   //  }
}

/* this is simplistic part, which just checks for _most_ obvious stuff
   like char moving around etc. this is called once / second. */

bool cast_common_generic(P_char ch, int spl)
{
   if(is_silent(ch, FALSE))
      {
      send_to_char("You move your lips, but no sound comes forth!\n", ch);
      return FALSE;
      }

   if(CHAR_IN_SAFE_ZONE(ch) && skills[spl].harmful)
      {
      send_to_char("You may not cast harmful magic here!\n", ch);
      return FALSE;
      }

   /* change, all spells were either POSITION_STANDING or POSITION_FIGHTING
      so I just changed min_pos to a bool in_battle flag (like commands).
      In vast majority of cases, these switches will never be used, but it's
      belt and suspenders time.  JAB */
   if(!MIN_POS(ch, POS_STANDING + STAT_NORMAL))
      {
      switch(GET_STAT(ch))
         {
         case STAT_DEAD:
            send_to_char("Corpses make really pitiful spellcasters.\n", ch);
            break;
         case STAT_DYING:
         case STAT_INCAP:
            send_to_char("You are too busy bleeding to death at the moment.\n", ch);
            break;
         case STAT_SLEEPING:
            send_to_char("You dream about great magical powers.\n", ch);
            break;
         case STAT_RESTING:
            send_to_char("You can't concentrate enough while resting.\n", ch);
            break;
         }

      if(GET_CLASS(ch)!=CLASS_PSIONICIST)
         {
         switch(GET_POS(ch))
            {
            case POS_PRONE:
               send_to_char("Standing would be a good first step.\n", ch);
               break;
            case POS_KNEELING:
               send_to_char("Get off your knees!\n", ch);
               break;
            case POS_SITTING:
               send_to_char("You can't do this sitting!\n", ch);
               break;
            }
         }

      return FALSE;
      }
   else if((IS_FIGHTING(ch) && !skills[spl].min_pos) || IS_STUNNED(ch))
      {
      send_to_char("Impossible! You can't concentrate enough!\n", ch);
      return FALSE;
      }
   else if((IS_AFFECTED(ch, AFF_MINOR_PARALYSIS) || IS_AFFECTED(ch, AFF_MAJOR_PARALYSIS)) && (GET_CLASS(ch)!=CLASS_PSIONICIST))
      {
      send_to_char("Too bad, looks like you've been stopped in your tracks!\n", ch);
      return FALSE;
      }

   return TRUE;
}

#define spl common_target_data.ttype
#define tar_obj common_target_data.t_obj
#define tar_char common_target_data.t_char
#define tar_arg common_target_data.arg

bool cast_common(P_char ch, char *argument)
{
   bool target_ok;
   char Gbuf1[MAX_STRING_LENGTH];
   int qend, i;

   skip_spaces(&argument);

   common_target_data.ttype = 0;
   common_target_data.t_obj = 0;
   common_target_data.t_char = 0;
   tar_arg = NULL;

   /* If there is no chars in argument */
   if(!(*argument))
      {
      send_to_char("Cast which what where?\n", ch);
      return FALSE;
      }

   if(*argument != '\'')
      {
      send_to_char("Magic must always be enclosed by the holy symbols: '\n", ch);
      return FALSE;
      }

   /* Locate the last quote && lowercase the magic words (if any) */
   for(qend = 1; *(argument + qend) && (*(argument + qend) != '\''); qend++)
      *(argument + qend) = LOWER(*(argument + qend));

   if(*(argument + qend) != '\'')
      {
      send_to_char("Spells are always to be enclosed by the holy symbols: '\n", ch);
      return FALSE;
      }

   spl = old_search_block(argument, 1, (uint) (MAX(0, (qend - 1))), spells, 0);

   if(IS_NPC(ch))
      {             /* Check for NPC slot availability -  SKB  6 Apr 1995     */
      if(!npc_has_spell_slot(ch, spl))
         {
         send_to_char("Sorry, out of spells in that circle.\n", ch);
         return(FALSE);
         }
      }

   if(spl != -1)
      spl = pindex2Skill[spl];

   if(IS_NPC(ch) && !MobKnowsSpell(ch, spl))
      {
      send_to_char("You have no idea what you are trying to cast.\n", ch);
      return FALSE;
      }

   if(skills[spl].harmful != -1 && (skills[spl].spell_pointer == 0) && (spl > 0))
      {
      send_to_char("Sorry, this magic has not yet been implemented :(\n", ch);
      return FALSE;
      }

   if(!IS_SPELL_S(spl))
      {
      switch(number(1, 5))
         {
         case 1:
            send_to_char("Excuse me?\n", ch);
            break;
         case 2:
            send_to_char("Um . . . you made a typo!\n", ch);
            break;
         case 3:
            send_to_char("You ok??\n", ch);
            break;
         case 4:
            send_to_char("Sorry, typo, try again!\n", ch);
            break;
         default:
            send_to_char("Sorry, you spelled it wrong!\n", ch);
            break;
         }
      return FALSE;
      }

   if(!cast_common_generic(ch, spl))
      return FALSE;

   if(IS_NPC(ch))
      {
      if(!MobKnowsSpell(ch, spl))
         {
         send_to_char("You don't know that spell.\n", ch);
         return FALSE;
         }
      }
   else if((GET_LEVEL(ch) < MINLVLIMMORTAL) && !meming_class(GET_CLASS(ch)))
      {
      if((skills[spl].class[GET_CLASS(ch) - 1].rlevel > (int) GET_LEVEL(ch)) ||
         ((skills[spl].class[GET_CLASS(ch) - 1].base_skill ==
           SKILL_BASE_SPECIAL) && !GET_CHAR_SKILL_S(ch, spl)))
         {
         send_to_char("You don't know that spell.\n", ch);
         return FALSE;
         }
      }

   // hack for not being able to cast song of recovery
   if(skills[spl].name == "song of recovery")
      {
      send_to_char("I don't think that is how you do it.\n", ch);
      return FALSE;
      }

#ifdef NEW_BARD
   if(!IS_TRUSTED(ch) && IS_PC(ch) && (GET_CLASS(ch) == CLASS_BARD || GET_CLASS(ch) == CLASS_BATTLECHANTER))
      {
      if(!ch->only.pc->available_spells[GetSpellCircle(ch, spl)])
         {
         send_to_char("You don't have any more spells available at that circle!\n", ch);
         return FALSE;
         }
      /*
            }
         else if(IS_PC(ch) && IS_SINGER(ch) && IS_CSET(ch->only.pc->pcact, PLR_NO_SING))
            {
            send_to_char("You haven't fully composed yourself after your song.\n", ch);
            return FALSE;
            }
         else
      */
      }
   else
      if(!IS_TRUSTED(ch) && IS_PC(ch))
      {
      if(!ch->only.pc->skills[skills[spl].pindex].memorized)
         {
         send_to_char("You don't have that spell memorized.\n", ch);
         return FALSE;
         }
      }
#else
   if(!IS_TRUSTED(ch) && IS_PC(ch))
      {
      if(!ch->only.pc->skills[skills[spl].pindex].memorized)
         {
         send_to_char("You don't have that spell memorized.\n", ch);
         return FALSE;
         }
      }
#endif

   if((IS_DARK(ch->in_room) || IS_CSET(world[ch->in_room].room_flags, MAGIC_DARK)) && !CAN_SEE(ch, ch) && skills[spl].harmful)
      {
      send_to_char("Hell, you cannot see your own nose in the dark, you dare not do anything \noffensive!\n", ch);
      return FALSE;
      }

   argument += qend + 1;         /* Point to the last ' */
   for(; *argument == ' '; argument++)
      ;

   tar_arg = argument;

   /* **************** Locate targets **************** */
   target_ok = FALSE;
   tar_char = 0;
   tar_obj = 0;

   if(!IS_SET(skills[spl].targets, TAR_IGNORE))
      {
      argument = one_argument(argument, Gbuf1);

      if(*Gbuf1)
         {
         if(!target_ok && IS_SET(skills[spl].targets, TAR_SELF_ONLY))
            {
            if(str_cmp(GET_NAME(ch), Gbuf1) == 0)
               {
               tar_char = ch;
               target_ok = TRUE;
               }
            }

         if(!target_ok && IS_SET(skills[spl].targets, TAR_OBJ_EQUIP))
            {
            for(i = 0; i < MAX_WEAR && !target_ok; i++)
               {
               if(ch->equipment[i] && str_cmp(Gbuf1, ch->equipment[i]->name) == 0)
                  {
                  tar_obj = ch->equipment[i];
                  target_ok = TRUE;
                  }
               }
            }

         if(!target_ok && IS_SET(skills[spl].targets, TAR_OBJ_INV))
            {
            if((tar_obj = get_obj_in_list_vis(ch, Gbuf1, ch->carrying)))
               target_ok = TRUE;
            }

         if(!target_ok && IS_SET(skills[spl].targets, TAR_OBJ_ROOM))
            {
            if((tar_obj = get_obj_in_list_vis(ch, Gbuf1,world[ch->in_room].contents)))
               target_ok = TRUE;
            }

         if(IS_SET(skills[spl].targets, TAR_CHAR_ROOM))
            {
            if((tar_char = get_char_room_vis(ch, Gbuf1)))
               target_ok = TRUE;
            }

         if(!target_ok && IS_SET(skills[spl].targets, TAR_OBJ_WORLD))
            {
            if((tar_obj = get_obj_vis(ch, Gbuf1)))
               target_ok = TRUE;
            }

         if(!target_ok && IS_SET(skills[spl].targets, TAR_CHAR_WORLD))
            {
            if((tar_char = get_char_in_game_vis(ch, Gbuf1, FALSE)))
               target_ok = TRUE;
            }

         }
      else
         {                    /* No argument was typed */
         if(!target_ok && IS_SET(skills[spl].targets, TAR_SELF_ONLY))
            {
            tar_char = ch;
            target_ok = TRUE;
            }

         if(IS_SET(skills[spl].targets, TAR_FIGHT_SELF))
            {
            if(ch->specials.fighting)
               {
               tar_char = ch;
               target_ok = TRUE;
               }
            }

         if(!target_ok && IS_SET(skills[spl].targets, TAR_FIGHT_VICT))
            {
            if(ch->specials.fighting && (GET_STAT(ch->specials.fighting) != STAT_DEAD) &&
               (ch->specials.fighting->in_room == ch->in_room))
               {
               /* WARNING, MAKE INTO POINTER */
               tar_char = ch->specials.fighting;
               target_ok = TRUE;
               }
            }
         }

      }
   else
      target_ok = TRUE;           // No target, is a good target

   if(target_ok)
      {
      if(IS_CSET(world[ch->in_room].room_flags, SINGLE_FILE) &&
         (!tar_char || !AdjacentInRoom(ch, tar_char)) && skills[spl].harmful)
         {
         send_to_char("You daren't spellcast in this cramped room! Spell might hit wrong target.\n", ch);
         return FALSE;
         }
      }

   if(!target_ok)
      {
      if(*Gbuf1)
         {
         if(IS_SET(skills[spl].targets, TAR_CHAR_ROOM))
            send_to_char("Nobody here by that name.\n", ch);
         else if(IS_SET(skills[spl].targets, TAR_CHAR_WORLD))
            send_to_char("Nobody playing by that name.\n", ch);
         else if(IS_SET(skills[spl].targets, TAR_OBJ_INV))
            send_to_char("You are not carrying anything like that.\n", ch);
         else if(IS_SET(skills[spl].targets, TAR_OBJ_ROOM))
            send_to_char("Nothing here by that name.\n", ch);
         else if(IS_SET(skills[spl].targets, TAR_OBJ_WORLD))
            send_to_char("Nothing at all by that name.\n", ch);
         else if(IS_SET(skills[spl].targets, TAR_OBJ_EQUIP))
            send_to_char("You are not wearing anything like that.\n", ch);
         else if(IS_SET(skills[spl].targets, TAR_OBJ_WORLD))
            send_to_char("Nothing at all by that name.\n", ch);
         else if(IS_SET(skills[spl].targets, TAR_SELF_ONLY))
            send_to_char("You can only cast this spell upon yourself.\n", ch);

         }
      else
         {                    /* Nothing was given as argument */
         if(skills[spl].targets < TAR_OBJ_INV)
            send_to_char("Who should the spell be cast upon?\n", ch);
         else
            send_to_char("What should the spell be cast upon?\n", ch);
         }

      return FALSE;
      }
   if((tar_char == ch) && IS_SET(skills[spl].targets, TAR_SELF_NONO))
      {
      send_to_char("You cannot cast this spell upon yourself.\n", ch);
      return FALSE;
      }
   else if((tar_char != ch) && IS_SET(skills[spl].targets, TAR_SELF_ONLY))
      {
      send_to_char("You can only cast this spell upon yourself.\n", ch);
      return FALSE;
      }

   if(IS_AFFECTED(ch, AFF_CHARM))
      {
      if((((IS_SET(skills[spl].targets, TAR_CHAR_WORLD) || IS_SET(skills[spl].targets, TAR_CHAR_ROOM)) &&
           (ch->following == tar_char)) ||
          (IS_SET(skills[spl].targets, TAR_IGNORE) && AreaAffectCheck(ch, ch->following))) && skills[spl].harmful)
         {
         send_to_char("You are afraid that it could harm your master.\n", ch);
         return FALSE;
         }
      }

   /* special case to prevent ppl from finding rare load mobs.. -- Altherog Sep 6 1999 */
   if(target_ok && tar_char && IS_NPC(tar_char) &&
      (skills[spl].pindex == SPELL_CLAIRVOYANCE ||
       skills[spl].pindex == SPELL_RELOCATE ||
       skills[spl].pindex == SPELL_DIMENSION_DOOR ||
       skills[spl].pindex == SPELL_WIZARD_EYE ||
       skills[spl].pindex == SPELL_SUMMON ||
       skills[spl].pindex == SPELL_SCRY_REMAINS ||
       skills[spl].pindex == SPELL_DIMENSIONAL_FOLD ||
       skills[spl].pindex == SPELL_MOONWELL ||
       skills[spl].pindex == SPELL_SPIRIT_WALK ||
       skills[spl].pindex == SPELL_SHADOW_WALK))
      {
      send_to_char("Nobody playing by that name.\n", ch);
      return FALSE;
      }

   return TRUE;
}

void nuke_spellcast(struct spellcast_datatype *foo)
{
   struct spellcast_datatype *tmp = NULL;

   if(foo->args)
      {
      free_string(foo->args);
      foo->args = NULL;
      }

   if(!spellcast_stack)
      return;  // panic

   if(spellcast_stack == foo)
      spellcast_stack = foo->next;
   else
      {
      for(tmp = spellcast_stack; (tmp->next != NULL) && (tmp->next != foo); tmp = tmp->next)
         ;

      if(tmp->next != NULL)
         tmp->next = foo->next;
      }

   mm_release(dead_cast_pool, foo);
}

void NukeRedunantSpellcast(P_char ch)
{
   struct spellcast_datatype *tmp = NULL, *tmp2 = NULL;

   for(tmp = spellcast_stack; tmp; tmp = tmp2)
      {
      tmp2 = tmp->next;

      if(tmp->ch == ch)
         StopCasting(ch);
      else if(tmp->victim && (tmp->victim == ch))
         StopCasting(tmp->ch);
      }
}

#ifdef PCS_USE_MANA
/* powerCast, directly from mana.
   basic advantage is speed, minimal chant time, zero mem time.
   disadvantages:
   mana regen makes snails look like cheetahs
   since it lowers mana, it also lowers number of spells that can be memmed,
   until mana DOES regen.
   higher chance of overmemming, and penalties therefrom.
   JAB
 */

void do_powercast(P_char ch, char *argument, int cmd)
{
   int dura;
   struct spellcast_datatype *tmp_spl;
   char *orig_arg;

   if(IS_AFFECTED(ch, AFF_WRAITHFORM))
      {
      send_to_char("Are you kidding? Wraiths don't cast as a rule.\n", ch);
      return;
      }

   if(overmem_nuke(ch, TRUE, TRUE))
      return;

   powercasting = TRUE;

   if(cheat_flag)
      cheat_flag++;

   if(GET_LEVEL(ch) >= MINLVLIMMORTAL)
      {
      wizlog(GET_LEVEL(ch), "%s powercasts%s [%d]", GET_NAME(ch), argument, world[ch->in_room].number);
      logit(LOG_WIZ, "%s powercasts%s [%d]", GET_NAME(ch), argument, world[ch->in_room].number);
      }

   orig_arg = argument;

   if(!cast_common(ch, argument))
      {
      powercasting = FALSE;
      return;
      }

   send_to_char("You start chanting...\n", ch);
   dura = SpellCastTime(ch, spl);
   if(IS_TRUSTED(ch))
      dura = 1;
   else
      dura /= 3;

   CharWait(ch, dura);
   SpellCastShow(ch, spl);
   if((IS_CSET(world[ch->in_room].room_flags, NO_MAGIC) && !IS_TRUSTED(ch)) || (CHAR_IN_NO_MAGIC_ZONE(ch)))
      {
      send_to_char("The magic gathers, then fades away.\n", ch);
      StopCasting(ch);
      powercasting = FALSE;
      return;
      }
#if 0
   if(!sparecast_stack)
      {
#ifdef MEM_DEBUG
      mem_use[MEM_SPL_CAST] += sizeof(struct spellcast_datatype);
#endif
      CREATE(tmp_spl, struct spellcast_datatype, 1);
      }
   else
      {
      tmp_spl = sparecast_stack;
      sparecast_stack = tmp_spl->next;
      }
#endif
   if(!dead_cast_pool)
      dead_cast_pool =
      mm_create("CASTING", sizeof(struct spellcast_datatype), offsetof(struct spellcast_datatype, next), 2);

   tmp_spl = mm_get(dead_cast_pool);

   tmp_spl->next = spellcast_stack;
   spellcast_stack = tmp_spl;
   tmp_spl->timeleft = dura;
   tmp_spl->spell = common_target_data.ttype;
   tmp_spl->args = str_dup(orig_arg);
   tmp_spl->powercast = TRUE;
   SET_CBIT(ch->specials.affects, AFF_CASTING);
   AddEvent(EVENT_SPELLCAST, BOUNDED(1, dura, 4), TRUE, ch, tmp_spl);

   powercasting = FALSE;
}
#endif

int fight_in_room(P_char ch)
{
   P_char tch = NULL;

   for(tch = world[ch->in_room].people; tch; tch = tch->next_in_room)
      {
      if(IS_FIGHTING(tch))
         return TRUE;
      }

   return FALSE;
}

/* Added an int return of TRUE or FALSE to this function for proc purposes */
/* Returns TRUE if it gets to the end and actually begins to cast the spell */
/* Otherwise returns FALSE  Iyachtu - 8/29/01 */
int do_cast(P_char ch, char *argument, int cmd)
{
   P_char kala, kala2;
   char *orig_arg;
   int dura, fail, sroom;
   struct spellcast_datatype *tmp_spl;
   int npc_quick = 100, roll, npc_made_by = 0, new_dura, npc_tally;

   if(IS_AFFECTED(ch, AFF_WRAITHFORM))
      {
      send_to_char("Are you kidding? Wraiths don't cast as a rule.\n", ch);
      return FALSE;
      }

   if(IS_MORPH(ch))
      {
      send_to_char("while morphed?  I don't think so.\n",ch);
      return FALSE;
      }

#if 0
   /* Taking this out for new cantrip type spellcasting - Iyachtu */
#ifdef NEW_BARD
   if(IS_SINGING(ch))
      {
      send_to_char("You can't cast spells while singing.\n", ch);
      return FALSE;
      }
   else if(IS_PC(ch) && IS_SINGER(ch) && IS_CSET(ch->only.pc->pcact, PLR_NO_SING))
      {
      send_to_char("You haven't composed yourself after your song.\n", ch);
      return FALSE;
      }
#endif
#endif

   if(overmem_nuke(ch, TRUE, TRUE))
      return FALSE;

   if(cheat_flag)
      cheat_flag++;

   orig_arg = argument;

   if(!cast_common(ch, argument))
      return FALSE;

   if(IS_CSET(ch->specials.affects, AFF_GARROTE_VICT))
      {
      send_to_char("It's a little difficult to cast a spell when you're being strangled!\n", ch);
      return FALSE;
      }

   if(IS_TRUSTED(ch))
      {
      wizlog(GET_LEVEL(ch), "%s casts%s [%d]", GET_NAME(ch), argument, world[ch->in_room].number);
      logit(LOG_WIZ, "%s casts%s [%d]", GET_NAME(ch), argument, world[ch->in_room].number);
      }

   /* Block NOKILL mobs from even starting to cast an offensive spell -CRM */
   if(IS_NPC(ch) && IS_CSET(ch->only.npc->npcact, ACT_NOKILL) && skills[spl].harmful == 1)
      {
      send_to_char("You change your mind.\n", ch);
      return FALSE;
      }

   send_to_char("You start chanting...\n", ch);
   dura = SpellCastTime(ch, spl);
   SpellCastShow(ch, spl);
   if((IS_CSET(world[ch->in_room].room_flags, NO_MAGIC) && !IS_TRUSTED(ch)) || CHAR_IN_NO_MAGIC_ZONE(ch))
      {
      send_to_char("The magic gathers, then fades away.\n", ch);
      StopCasting(ch);
      return FALSE;
      }

   if(skills[spl].harmful == 1)
      {
      /* doesnt make sense to cast fire or cold type spells underwater.. */
      if((!IS_CSET(world[ch->in_room].room_flags, ROOM_AIRY_WATER)) &&
         (IS_FIRE_SPELL(skills[spl].pindex) || IS_COLD_SPELL(skills[spl].pindex))
         && ((world[ch->in_room].sector_type == SECT_UNDERWATER) ||
             (world[ch->in_room].sector_type == SECT_UNDERWATER_GR) ||
             (world[ch->in_room].sector_type == SECT_UD_UNDERWATER) ||
             (world[ch->in_room].sector_type == SECT_UD_UNDERWATER_GR)))
         {
         send_to_char("Your spell fizzles out as its extinguished by the surrounding water.\n", ch);
         StopCasting(ch);
         return FALSE;
         }
      }

   if(skills[spl].harmful == 1)
      {
      if(IS_DISGUISE (ch))
         {
         send_to_char ("Your somatic gestures causes your disguise to fall!\r\n", ch);
         IS_DISGUISE_PC (ch) = FALSE;
         IS_DISGUISE_NPC (ch) = FALSE;
         ch->disguise.name = NULL;
         ch->disguise.class = 0;
         ch->disguise.race = 0;
         ch->disguise.level = 0;
         ch->disguise.title = NULL;
         disguiseEvent (ch, TRUE);
         }

      if(IS_SET(skills[spl].targets, TAR_IGNORE))
         {
         /* this is real wicked case, like a fellow casting a 'swarm or somesuch other aggro kick-ass spell.
            All unoccupied mobsters which would get hit by spl do automagical tackle at fellow! -
            they better be occupied, OR... */
         if(IS_AFFECTED(ch, AFF_INVISIBLE))
            appear(ch);

         sroom = ch->in_room;
         for(kala = world[sroom].people; kala; kala = kala2)
            {
            kala2 = kala->next_in_room;
            if(kala == ch)
               continue;

            if(IS_AFFECTED(kala, AFF_MINOR_PARALYSIS) || IS_AFFECTED(kala, AFF_MAJOR_PARALYSIS))
               continue;

            if(IS_PC(kala) && IS_PC(ch))
               continue;

            if(GET_POS(kala) < POS_STANDING)
               continue;

            if(IS_FIGHTING(kala) || !CAN_ACT(kala))
               continue;

            /* if the below conditional is true, then ITS NOT AN AGGRO SPELL! */
            if(((spl == SPELL_HOLY_WORD) && !IS_EVIL(kala)) || ((spl == SPELL_UNHOLY_WORD) && !IS_GOOD(kala)))
               continue;

            if(!AreaAffectCheck(ch, kala))
               continue;

            if(!CAN_SEE(kala, ch))
               continue;

            if(number(1, 150) > (GET_LEVEL(kala) + STAT_INDEX(GET_C_INT(kala))))
               continue;

            /* justice hook:

               okay.. at this point, the victim knows that he's about to
               get hit by an aggro area spell.  If he doesn't know it,
               he'll know when the spell goes off. If he's already
               fighting the caster , there is no reason to call this. The
               only problem with this code, is that someone else in the
               room might (or might not) notice the aggression.  This
               code, unfortunatly, limits everyones knowledge to basically
               the same as the victims. :(  */

            justice_witness(ch, kala, CRIME_ATT_MURDER);

            if(IS_NPC(kala))
               MobStartFight(kala, ch);
            else
               hit(kala, ch, TYPE_UNDEFINED);

            if(!MIN_POS(ch, STAT_NORMAL + POS_STANDING) || (ch->in_room != sroom))
               break;
            }
         }
      else
         {
         if(tar_char && (tar_char != ch))
            {
            if(IS_AFFECTED(ch, AFF_INVISIBLE))
               appear(ch);

            /* It's still "harmful" at this point, so only let it start fight w/ NPC. - DTS 9/1/95 */
            if(should_not_kill(ch, tar_char))
               {
               if(!((IS_FIRE_SPELL(spl)) && ((GET_RACE(tar_char) == RACE_F_ELEMENTAL) || (GET_RACE(tar_char) == RACE_EFFREETI))))
                  {
                  send_to_char("You can't cast this on other players (yet)!\n", ch);
                  return FALSE;
                  }
               }

            /* ok, code to do some quick 'n' dirty hostility checks */
            if(IS_NPC(tar_char) && !IS_FIGHTING(tar_char) && CAN_SEE(tar_char, ch) && (GET_POS(ch) == POS_STANDING) &&
               (GET_STAT(tar_char) > STAT_INCAP))
               {
               /* if a mobster, automagically attacks fellow trying to
                  cast offensive spell at someone in room if able to. */
               if(number(1, 150) <= (GET_LEVEL(tar_char) + STAT_INDEX(GET_C_INT(tar_char))))
                  { /* justice hook: read comments above */
                  justice_witness(ch, tar_char, CRIME_ATT_MURDER);
                  MobStartFight(tar_char, ch);
                  }
               }
            }
         }
      }

   /* Check if any of the preceeding stuff hasnt killed the ch ex: MobStartFight etc */
   if(GET_STAT(ch) == STAT_DEAD)
      return FALSE;

   if(!dead_cast_pool)
      dead_cast_pool =
      mm_create("CASTING", sizeof(struct spellcast_datatype), offsetof(struct spellcast_datatype, next), 2);

   tmp_spl = mm_get(dead_cast_pool);

   if(IS_TRUSTED(ch))
      dura = 1;
   else if(IS_NPC(ch))
      {
      /*  Restored old system on test, so it won't get moved - Iyachtu */
      /*  Pulled the following line, omg it was bad - Iyachtu */
      roll = (number(1, 101));
      switch(STAT_INDEX(GET_C_INT(ch)))
         {
         case 0:
            npc_tally = 0;
            break;
         case 1:
         case 2:
         case 3:
            npc_tally = (GET_LEVEL(ch)) * 15 / 100;
            break;
         case 4:
         case 5:
            npc_tally = (GET_LEVEL(ch)) * 30 / 100;
            break;
         case 6:
         case 7:
         case 8:
            npc_tally = (GET_LEVEL(ch)) * 40 / 100;
            break;
         case 9:
         case 10:
         case 11:
            npc_tally = (GET_LEVEL(ch)) * 50 / 100;
            break;
         case 12:
         case 13:
            npc_tally = (GET_LEVEL(ch)) * 60 / 100;
            break;
         case 14:
         case 15:
         case 16:
            npc_tally = (GET_LEVEL(ch)) * 70 / 100;
            break;
         case 17:
            npc_tally = (GET_LEVEL(ch)) * 80 / 100;
            break;
         case 18:
            npc_tally = (GET_LEVEL(ch)) * 90 / 100;
            break;
         case 19:
            npc_tally = (GET_LEVEL(ch));
            break;
         case 20:
            npc_tally = (GET_LEVEL(ch)) * 110 / 100;
            break;
         case 21:
            npc_tally = (GET_LEVEL(ch)) * 115 / 100;
            break;
         case 22:
            npc_tally = (GET_LEVEL(ch)) * 120 / 100;
            break;
         case 23:
            npc_tally = (GET_LEVEL(ch)) * 125 / 100;
            break;
         case 24:
            npc_tally = (GET_LEVEL(ch)) * 135 / 100;
            break;
         case 25:
            npc_tally = (GET_LEVEL(ch)) * 150 / 100;
            break;
         default:
            npc_tally = (GET_LEVEL(ch)) * 175 / 100;
            break;
         }

      npc_made_by = npc_tally - roll;
      if(npc_made_by < 0)
         npc_quick = 100;
      else if(npc_made_by < 10)
         npc_quick = 90;
      else if(npc_made_by < 20)
         npc_quick = 80;
      else if(npc_made_by < 30)
         npc_quick = 70;
      else if(npc_made_by < 40)
         npc_quick = 60;
      else
         npc_quick = 50;

      new_dura = ((dura * npc_quick) / 100);
      //      debuglog(51, DS_IYACHTU, "lvl: %d int: %d tally: %d made by: %d, quick: %d, dura: %d, new_dura: %d",
      //               GET_LEVEL(ch), GET_C_INT(ch), npc_tally, npc_made_by, npc_quick, dura, new_dura);
      dura = new_dura;
      }

   /*    if((IS_NPC(ch) && (number(1, 101) < (GET_LEVEL(ch)))) || */

   if(affected_by_song(ch, SONG_OF_SORCERY))
      {
      new_dura = dura - ((dura * ch->bard_singing->song_singing->modifier) / 100);
      debuglog(51, DS_IYACHTU, "Sorc: %d/%d", new_dura, dura);
      dura = new_dura;
      }

   if(IS_PC(ch) && (GET_CHAR_SKILL(ch, SKILL_QUICK_CHANT) > number(1, 100)))
      {
      dura >>= 1;
      if(IS_PC(ch))
         debuglog(51, DS_IYACHTU, "&+C%s: QC, %d&N", GET_NAME(ch), GET_CHAR_SKILL(ch, SKILL_QUICK_CHANT));

      CharSkillGainChance(ch, SKILL_QUICK_CHANT, 0);
      }
   else if(IS_PC(ch))
      debuglog(51, DS_IYACHTU, "&+R%s: !QC, %d&N", GET_NAME(ch), GET_CHAR_SKILL(ch, SKILL_QUICK_CHANT));

   if(IS_ENABLED(CODE_CHAOS) && !number(0, 10))
      dura = 1;

   tmp_spl->ch = ch;
   tmp_spl->next = spellcast_stack;
   spellcast_stack = tmp_spl;
   tmp_spl->victim = NULL;
   tmp_spl->object = NULL;
   tmp_spl->timeleft = dura;
   tmp_spl->spell = common_target_data.ttype;
   tmp_spl->args = str_dup(orig_arg);
   tmp_spl->needs_parsing = TRUE;
   tmp_spl->powercast = FALSE;

   /* large change to spell failure, used to check for failure every second so long spells failed ALOT more
      often than they should.  Now, one check when they start casting, for both quick_chant and spell_failure,
      but save how far they get before it fails. JAB */

   if(GetSpellCircle(ch, tmp_spl->spell) == GetMaxCircle_char(ch))
      fail = number(1, 100) - SpellCastChance(ch, tmp_spl->spell);
   else
      fail = -1;

   /* added no-fail to 1st circle spells - KPZ 4/1/2001 */
   if(GetSpellCircle(ch, tmp_spl->spell) == 1)
      fail = -1;

#ifdef NEW_BARD
   if((fail < 0) && affected_by_song(ch, SONG_OF_MISCAST_MAGIC) &&
      (number(1,100) <= (ch->bard_singing->song_singing->modifier * (100 - GetMobMagicResistance(ch)) / 100)))
      {
      fail = ch->bard_singing->song_singing->modifier;
      debuglog(51, DS_IYACHTU, "%s spell failure due to miscase magic song", ch->player.name);
      }
#endif

   /* fail is now 99 to -96, if it's negative, they didn't fail, if it's
      positive they will fail, but we have to determine just when. */

   if(fail < 0)
      tmp_spl->failtime = dura + 4;
   else if(fail == 0)
      tmp_spl->failtime = dura;
   else
      {
      tmp_spl->failtime =
      BOUNDED(1, (dura * fail / MAX(1, (100 - SpellCastChance(ch, tmp_spl->spell)))), dura);
      }

   dura = BOUNDED(1, MIN(tmp_spl->timeleft, tmp_spl->failtime), 4);
   tmp_spl->timeleft -= dura;
   tmp_spl->failtime -= dura;
   SET_CBIT(ch->specials.affects, AFF_CASTING);

   //  if (IS_PC(ch))
   //    debuglog(51, DS_SPELLCAST, "%d do_cast() [%s] dura %d [%s]", pulse, GET_NAME(ch), dura, tmp_spl->args);

   AddEvent(EVENT_SPELLCAST, BOUNDED(1, dura, 4), TRUE, ch, tmp_spl);
   return TRUE;
}

int SpellCastStack(void)
{
   struct spellcast_datatype *t = NULL;
   int i = 0;

   for(t = spellcast_stack; t; t = t->next)
      i++;

   return i;
}

int SpareCastStack(void)
{
   struct spellcast_datatype *t = NULL;
   int i = 0;

   for(t = sparecast_stack; t; t = t->next)
      i++;

   return i;
}

void SpellCastProcess(P_char ch, struct spellcast_datatype *arg)
{
   int skl = 0, i = 0, circle = 0, num = 0, gainchance;
   char buf[MAX_STRING_LENGTH];

   //  if (IS_PC(ch))
   //    debuglog(51, DS_SPELLCAST, "%d SpellCastProcess in [%s]", pulse, GET_NAME(ch));

   if(!IS_AFFECTED(ch, AFF_CASTING))
      return;

   if(IS_CSET(world[ch->in_room].room_flags, ROOM_SILENT))
      {
      send_to_char("The magic silences your chant.", ch);
      StopCasting(ch);
      current_spell_being_cast = 0;
      return;
      }

   if(IS_AFFECTED(ch, AFF_SILENCE_PERSON))
      {
      send_to_char("The magic silences your chant.", ch);
      StopCasting(ch);
      current_spell_being_cast = 0;
      return;
      }

   if(!cast_common_generic(ch, arg->spell))
      {
      StopCasting(ch);
      current_spell_being_cast = 0;
      return;
      }

   /*
    * The boolean needs_parsing field in the struct spellcast_datatype (structs.h)
    * represents a flag by which this function either proceeds with or forgoes
    * parsing of the string in the args field.  If the cast directive came through
    * the command interpreter, then this field requires parsing to extract
    * information about which spell to cast as well as the target of this spell.
    * If the cast directive came from another source, such as the mobact cascade,
    * then the struct already contains the information necessary to proceed with
    * the cast and the args field requires no analysis.  The cast_common()
    * function parses a string and extracts the requisite information, if any, to
    * proceed with a cast. - SKB 23 Mar 1995
    */
   if(arg->needs_parsing)
      {
      if(!cast_common(ch, arg->args))
         {
         StopCasting(ch);
         current_spell_being_cast = 0;
         return;
         }
      }

   if(arg->failtime == 0)
      {
      send_to_char("You lost your concentration!\n", ch);
      gainchance = GetSpellCircle(ch, arg->spell) - GetMaxCircle_char(ch) - 10;
      if(GET_SPELLTYPE(arg->spell) != 0)
         CharSkillGainChance(ch, skilltype_of_spell(arg->spell), gainchance);
      else
         CharSkillGainChance(ch, SKILL_CAST_GENERIC, gainchance);

      current_spell_being_cast = 0;
      StopCasting(ch);
      return;
      }

   /* next section checks if spell has failed yet (failtime), if not, prints
      progress indicator and reschedules next event. JAB */
   if(arg->timeleft > 0)
      {
      if(praying_class(GET_CLASS(ch)))
         skl = SKILL_SPELL_KNOWLEDGE_CLERICAL;
      else
         skl = SKILL_SPELL_KNOWLEDGE_MAGICAL;

      if(number(1, 100) <= GET_CHAR_SKILL(ch, skl))
         {
         sprintf(buf, "Casting: %s ", skills[arg->spell].name);
         for(i = 0; i < (arg->timeleft / 4); i++)
            strcat(buf, "*");

         strcat(buf, "\n");
         send_to_char(buf, ch);
         }
      else if(IS_PC(ch))
         CharSkillGainChance(ch, skl, 0);

      i = MIN(MIN(arg->timeleft, arg->failtime), 4);
      arg->timeleft -= i;
      arg->failtime -= i;
      //    if (IS_PC(ch))
      //      debuglog(51, DS_SPELLCAST, "%d SpellCastProcess AddEvent [%s] i%d tl %d ft %d", pulse,
      //             GET_NAME(ch), i, arg->timeleft, arg->failtime);
      AddEvent(EVENT_SPELLCAST, BOUNDED(1, i, 4), TRUE, ch, arg);
      return;
      }                             /* if (arg->timeleft > 0) */

   if(tar_char && ((GET_STAT(tar_char) == STAT_DEAD) || (tar_char->in_room == NOWHERE)))
      {
      current_spell_being_cast = 0;
      StopCasting(ch);
      return;
      }
   /* ok, we are in the home stretch, this event call has arg->timeleft of <= 0
      so now we *FINALLY*, actually cast the spell.  JAB */

#ifdef SPELL_DEBUG
   send_to_char("String: ", ch);
   send_to_char(arg->args, ch);
   send_to_char("\n", ch);
#endif

#ifdef PCS_USE_MANA
   if(arg->powercast)
      GET_MANA(ch) -= GetSpellCircle(ch, arg->spell) * MANA_PER_CIRCLE;
#endif

   /*
    * The IS_PC() check added for robustness.  !IS_TRUSTED() evalutes to TRUE
    * in the NPC case, and proceeds to wreak havoc by asking for pointers to a
    * struct the NPC does not have. - SKB 23 Mar 1995
    */
   if(!IS_TRUSTED(ch) && IS_PC(ch))
      {
#ifdef NEW_BARD
      if(GET_CLASS(ch) == CLASS_BARD || GET_CLASS(ch) == CLASS_BATTLECHANTER)
         {
         if(ch->only.pc->available_spells[GetSpellCircle(ch, arg->spell)] > 0)
            ch->only.pc->available_spells[GetSpellCircle(ch, arg->spell)]--;
         else
            /* we screwed up, best to know - Iyachtu */
            dump_core();
         }
      else
#endif
         if(ch->only.pc->skills[skills[arg->spell].pindex].memorized > 0)
         {
         ch->only.pc->skills[skills[arg->spell].pindex].memorized--;
         add_new_memorize_spell(ch, skills[arg->spell].pindex);
         gainchance = 1 + GetSpellCircle(ch, arg->spell) - GetMaxCircle_char(ch);

         if(GET_SPELLTYPE(arg->spell) != 0)
            CharSkillGainChance(ch, skilltype_of_spell(arg->spell), gainchance);
         else
            CharSkillGainChance(ch, SKILL_CAST_GENERIC, gainchance);
         }
      else
         {
         send_to_char("Yer slots memorized are fubared. Fixing it, but if this happens oft, report\nthis to an admin, please.\n", ch);
         ch->only.pc->skills[skills[arg->spell].pindex].memorized = 0;
         current_spell_being_cast = 0;
         StopCasting(ch);
         return;
         }
      }                             /* if (!IS_TRUSTED(ch) && IS_PC(ch)) */

   /*
    * If parsing flagged as unnecessary, then the target information already
    * exists in the struct.  This assignment likely possible during the earlier
    * evaluation of the needs_parsing boolean; however, this variable tar_char
    * appears mysteriously and for the moment I've assigned it closest to its
    * explicit usage to avoid any invisible reassignments. - SKB 23 Mar 1995
    */
   if(!(arg->needs_parsing))
      {
      tar_char = arg->victim;
      tar_obj = arg->object;
      }

   /*
    * Make sure target situation appropriate for cast completion.  The nature of
    * the spell.targets sets_affs prompts my quite unconventional usage of the
    * IS_SET() macro.  However, the bitwise "and" operation yields the intended
    * result using the bitwise "or" constructed mask.  Traditionally the macro
    * uses a single bit as a mask, but I see no reason not to employ more complex
    * masks when appropriate, as in the case below IMHO.  N.B., however, that the
    * bitwise masking operations proceed into a boolean not. - SKB 12 Apr 1995
    */

   if(!IS_SET(skills[arg->spell].targets, TAR_IGNORE) && !IS_SET(skills[arg->spell].targets, (TAR_OBJ_INV | TAR_OBJ_ROOM | TAR_OBJ_WORLD | TAR_OBJ_EQUIP)))
      {
      if(!tar_char || (tar_char && !IS_SET(skills[arg->spell].targets, TAR_CHAR_WORLD) && (tar_char->in_room != ch->in_room)))
         {
         current_spell_being_cast = 0;
         StopCasting(ch);
         return;
         }
      }

   if(tar_obj && !IS_SET(skills[arg->spell].targets, (TAR_OBJ_INV | TAR_OBJ_ROOM | TAR_OBJ_WORLD | TAR_OBJ_EQUIP)))
      {
      current_spell_being_cast = 0;
      StopCasting(ch);
      return;
      }

#if 0
   /* Check whether a NON-PK caster is targeting a PC that was recently engaged in PK
    *                                                             -- Altherog Jun 99 */
   if(IS_PC(ch) && IS_CSET(ch->only.pc->pcact, PLR_NO_KILL) && tar_char)
      {
      if(IS_PC(tar_char) && IS_AFFECTED(ch, AFF_ENGAGEDINPK))
         {
         send_to_char("Your spell does not have any effect!. You cannot assist characters that were recently engaged in PK\n", ch);
         wizlog(51, "%s (non-pk char) attempted to cast [%s] at %s (pk char). Whoop his ass!",
                GET_NAME(ch), skills[arg->spell].name, GET_NAME(tar_char));
         StopCasting(ch);
         return;
         }
      }
#endif

   /*
    * If the flow reaches this point, then we have had a successful cast.  In
    * such a case, the (N)PC utters the spell, and invokes its action.  In the
    * NPC case, the appropriate element decremented in
    * mob->only.npc->spells_in_circle[], the array tracking the # of spells the
    * mob may still cast in that circle.  Player spell tracking already existed
    * and handled by the time the mob "mem" case added. - SKB 24 Mar 1995
    */

#ifdef RILDEBUG
   {
      char message[256];

      sprintf(message, "SpellCastProcess():  spell = %s; target = %s; spelltype = %d\n",
              skills[arg->spell].name,
              tar_char ? GET_NAME(tar_char) : "unspecified",
              GET_SPELLTYPE(arg->spell));
      send_to_char(message, ch);
   }
#endif

   //  if (IS_PC(ch))
   //    debuglog(51, DS_SPELLCAST, "%d SpellCastProcess out. EXEC [%s]", pulse, GET_NAME(ch));

   send_to_char("You complete your spell...\n", ch);
   REMOVE_CBIT(ch->specials.affects, AFF_CASTING);
   act("$n completes $s spell...", FALSE, ch, 0, 0, TO_ROOM);

   if(arg->spell != SPELL_VENTRILOQUATE)
      say_spell(ch, arg->spell);

   if((skills[arg->spell].harmful == 1) && tar_char && (ch != tar_char) && IS_AFFECTED(ch, AFF_INVISIBLE))
      appear(ch);

   current_spell_being_cast = skills[arg->spell].pindex;

   /* justice hook */
   /* note the special checking for holyword spells... */

   if(skills[arg->spell].harmful == 1)
      {
      if(IS_SET(skills[arg->spell].targets, TAR_IGNORE))
         {
         P_char t, t_next;
         for(t = world[ch->in_room].people; t; t = t_next)
            {
            t_next = t->next_in_room;
            if(AreaAffectCheck(ch, t))
               {
               if(((arg->spell == SPELL_HOLY_WORD) && !IS_EVIL(t)) || ((arg->spell == SPELL_UNHOLY_WORD) && !IS_GOOD(t)))
                  continue;

               justice_witness(ch, t, CRIME_ATT_MURDER);
               }
            }
         }
      else
         justice_witness(ch, tar_char, CRIME_ATT_MURDER);
      }

   /* Store a copy of tar_arg before calling spell function since it points to arg->args
      which will be freed by nuke_spellcast. This prevents use-after-free errors. */
   char tar_arg_copy[MAX_INPUT_LENGTH];
   if (tar_arg) {
      strncpy(tar_arg_copy, tar_arg, MAX_INPUT_LENGTH - 1);
      tar_arg_copy[MAX_INPUT_LENGTH - 1] = '\0';
   } else {
      tar_arg_copy[0] = '\0';
   }

   ((*skills[arg->spell].spell_pointer) ((int) GET_LEVEL(ch), ch, tar_arg_copy, SPELL_TYPE_SPELL, tar_char, tar_obj));

   if((skills[arg->spell].harmful == 1) && (current_spell_being_cast != SPELL_SLEEP))
      {
      if(tar_char && (ch != tar_char) && (GET_STAT(tar_char) != STAT_DEAD))
         {
         if(affected_by_spell(tar_char, SPELL_SLEEP))
            affect_from_char(tar_char, SPELL_SLEEP);

         if(GET_STAT(tar_char) == STAT_SLEEPING)
            {
            send_to_char("Your rest is violently disturbed!\n", tar_char);
            act("Your spell disturbs $N's beauty sleep!", FALSE, ch, 0, tar_char, TO_CHAR);
            act("$n's spell disturbs $N's beauty sleep!", FALSE, ch, 0, tar_char, TO_NOTVICT);
            SET_POS(tar_char, GET_POS(tar_char) + STAT_NORMAL);
            }
         }                           /* if (tar_char && (ch != tar_char)) */
      }

   /* Modify NPC spelltracking array since spell used.- SKB 24 Mar 1995 */

   if((GET_STAT(ch) != STAT_DEAD) && IS_NPC(ch))
      {
      circle = GetLowestSpellCircle(arg->spell);
      ch->only.npc->spells_in_circle[circle] -= 1;
      num = ch->only.npc->spells_in_circle[circle];
      ch->only.npc->spells_in_circle[circle] = BOUNDED(0, num, 127);
      ch->only.npc->spells_in_circle[0] += 1;
      }

   current_spell_being_cast = 0;

   if((skills[arg->spell].harmful == 1) && tar_char &&
      (tar_char->in_room == ch->in_room) && (tar_char->in_room != -1))
      if(tar_char && !IS_FIGHTING(tar_char) &&
         !IS_AFFECTED(tar_char, AFF_KNOCKED_OUT) &&
         !IS_AFFECTED(tar_char, AFF_MINOR_PARALYSIS) &&
         !IS_AFFECTED(tar_char, AFF_MAJOR_PARALYSIS) &&
         !IS_AFFECTED(tar_char, AFF_SLEEP))
         {
         if(MIN_POS(tar_char, POS_STANDING + STAT_RESTING))
            {
            if(IS_NPC(ch))
               MobStartFight(tar_char, ch);
            else
               attack(tar_char, ch);
            }
         else
            set_fighting(tar_char, ch);
         }                           /* if (tar_char && !IS_FIGHTING(tar_char) && ... */

   nuke_spellcast(arg);

   if(ch && (ch->in_room != NOWHERE) && (GET_STAT(ch) != STAT_DEAD))
      CharWait(ch, PULSE_VIOLENCE / 2);
}                               /* End SpellCastProcess(), modified last by SKB 26 Apr 1994 */

#undef tar_char
#undef tar_obj
#undef spl

void assign_spell_pointers(void)
{
   int i, j;

   /* From spells1.c */

   for(i = 0; i < MAX_SKILLS; i++)
      {
      skills[i].pindex = -1;
      skills[i].name = "Undefined";
      skills[i].min_pos = FALSE;
#if 0
      skills[i].mana = 0;
#endif
      skills[i].beats = 0;
      skills[i].harmful = -1;
      skills[i].targets = 0;
      skills[i].spell_pointer = NULL;
      for(j = 0; j < TOTALCLASS; j++)
         {
         skills[i].class[j].rlevel = 0;
         skills[i].class[j].maxlearn = 0;
#if 0
         skills[i].class[j].maxlearnpprac = 0;
#endif
         skills[i].class[j].base_skill = 0;
         }
      }

   /*
      few generic thoughts about spells:

      - first, those to which 'generic' skill is set as type, cannot
      be specialized, so in long run, they be less powerful, but in
      beginning, succeed more.

      - specialization helps most in damage and long casting time spells.

      - necroes get mostly undead-related spells (those few that I've
      patched up so far) and cold-related spells.

      - sorcerers get most spells.

      - conjurers get some spells sorcerers don't (major phys prot,
      conj elemental, enchant weapon)

      - clerics get best healing
    */

   SPELL_CREATE("magic missile", SPELL_MAGIC_MISSILE, SPELLTYPE_INVOCATION, PULSE_VIOLENCE * 2 / 3, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 1, cast_magic_missile);
   SPELL_ADD(CLASS_SORCERER, 1, BASE);
   SPELL_ADD(CLASS_CONJURER, 1, BASE);
   /*  SPELL_ADD(CLASS_RANGER, 2, BASE); */
   /*  SPELL_ADD(CLASS_SHAMAN, 1, BASE); */
   SPELL_ADD(CLASS_NECROMANCER, 1, BASE);
   SPELL_ADD(CLASS_LICH, 1, BASE);
   SPELL_ADD(CLASS_ENCHANTER, 1, BASE);
   SPELL_ADD(CLASS_INVOKER, 1, BASE);



   SPELL_CREATE("chill touch", SPELL_CHILL_TOUCH, SPELLTYPE_INVOCATION, PULSE_VIOLENCE * 3 / 4, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 3, cast_chill_touch);
   SPELL_ADD(CLASS_NECROMANCER, 2, BASE);
   SPELL_ADD(CLASS_LICH, 2, BASE);
   SPELL_ADD(CLASS_CONJURER, 2, BASE);
   SPELL_ADD(CLASS_SORCERER, 2, BASE);
   /*  SPELL_ADD(CLASS_SHAMAN, 2, BASE); */
   /*  SPELL_ADD(CLASS_RANGER, 4, BASE); */
   SPELL_ADD(CLASS_ENCHANTER, 2, BASE);
   SPELL_ADD(CLASS_INVOKER, 1, BASE);


   SPELL_CREATE("burning hands", SPELL_BURNING_HANDS, SPELLTYPE_INVOCATION, PULSE_VIOLENCE - 1, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 3, cast_burning_hands);
   SPELL_ADD(CLASS_SORCERER, 2, BASE);
   SPELL_ADD(CLASS_CONJURER, 2, BASE);
   /*  SPELL_ADD(CLASS_SHAMAN, 3, BASE); */
   SPELL_ADD(CLASS_ENCHANTER, 2, BASE);
   SPELL_ADD(CLASS_INVOKER, 1, BASE);


   SPELL_CREATE("shocking grasp", SPELL_SHOCKING_GRASP, SPELLTYPE_INVOCATION, PULSE_VIOLENCE, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 5, cast_shocking_grasp);
   SPELL_ADD(CLASS_SORCERER, 3, BASE);
   SPELL_ADD(CLASS_CONJURER, 3, BASE);
   /*  SPELL_ADD(CLASS_SHAMAN, 4, BASE); */
   /*  SPELL_ADD(CLASS_RANGER, 6, BASE); */
   SPELL_ADD(CLASS_INVOKER, 2, BASE);


   SPELL_CREATE("lightning bolt", SPELL_LIGHTNING_BOLT, SPELLTYPE_INVOCATION, PULSE_VIOLENCE + 1, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 7, cast_lightning_bolt);
   SPELL_ADD(CLASS_SORCERER, 4, BASE);
   SPELL_ADD(CLASS_CONJURER, 4, BASE);
   SPELL_ADD(CLASS_ENCHANTER, 4, BASE);
   SPELL_ADD(CLASS_INVOKER, 3, BASE);
   /*  SPELL_ADD(CLASS_SHAMAN, 5, BASE); */
   /*  SPELL_ADD(CLASS_DRUID, 6, BASE); */
   /*  SPELL_ADD(CLASS_RANGER, 9, BASE); */

   SPELL_CREATE("cone of cold", SPELL_CONE_OF_COLD, SPELLTYPE_INVOCATION, PULSE_VIOLENCE * 4 / 3, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 11, cast_cone_of_cold);
   SPELL_ADD(CLASS_SORCERER, 5, BASE);   /* lvl 20 */
#ifdef NEW_NECRO
   SPELL_ADD(CLASS_NECROMANCER, 5, BASE);        /* lvl 21 */
   SPELL_ADD(CLASS_LICH, 5, BASE);
#else
   SPELL_ADD(CLASS_NECROMANCER, 6, BASE);        /* lvl 25 */
   SPELL_ADD(CLASS_LICH, 6, BASE);
#endif
   SPELL_ADD(CLASS_CONJURER, 6, BASE);   /* lvl 25 */
   SPELL_ADD(CLASS_ENCHANTER, 6, BASE);
   SPELL_ADD(CLASS_INVOKER, 5, BASE);
   SPELL_ADD(CLASS_ELEMENTALIST, 6, BASE);


   SPELL_CREATE("energy drain", SPELL_ENERGY_DRAIN, SPELLTYPE_INVOCATION, PULSE_VIOLENCE, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 1, cast_energy_drain);
   SPELL_ADD(CLASS_NECROMANCER, 5, BASE);
   SPELL_ADD(CLASS_LICH, 5, BASE);

   SPELL_CREATE("wither", SPELL_WITHER, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 0, cast_wither);
   SPELL_ADD(CLASS_ANTIPALADIN, 8, BASE);
   SPELL_ADD(CLASS_NECROMANCER, 4, BASE);
   SPELL_ADD(CLASS_LICH, 4, BASE);

   SPELL_CREATE("fireball", SPELL_FIREBALL, SPELLTYPE_INVOCATION, PULSE_VIOLENCE * 4 / 3, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 11, cast_fireball);
   SPELL_ADD(CLASS_SORCERER, 6, BASE);
   SPELL_ADD(CLASS_CONJURER, 6, BASE);
   /*  SPELL_ADD(CLASS_SHAMAN, 7, BASE); */
   SPELL_ADD(CLASS_ENCHANTER, 6, BASE);
   SPELL_ADD(CLASS_INVOKER, 4, BASE);
   SPELL_ADD(CLASS_ELEMENTALIST, 4, BASE);

   SPELL_CREATE("missile shield", SPELL_MISSILE_SHIELD, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE * 4 / 3, TRUE, TAR_CHAR_ROOM, 0, 0, cast_missile_shield);
   SPELL_ADD(CLASS_SORCERER, 2, BASE);
   SPELL_ADD(CLASS_CONJURER, 3, BASE);
   SPELL_ADD(CLASS_SHAMAN, 8, BASE);
   SPELL_ADD(CLASS_ENCHANTER, 3, BASE);


   SPELL_CREATE("meteorswarm", SPELL_METEOR_SWARM, SPELLTYPE_INVOCATION, PULSE_VIOLENCE * 2, TRUE, TAR_IGNORE, 1, 16, cast_meteorswarm);
   SPELL_ADD(CLASS_SORCERER, 10, LEARN);
   SPELL_ADD(CLASS_INVOKER, 9, LEARN);

   SPELL_CREATE("chain lightning", SPELL_CHAIN_LIGHTNING, SPELLTYPE_INVOCATION, PULSE_VIOLENCE * 2, TRUE, TAR_IGNORE, 1, 10, cast_chain_lightning);
   SPELL_ADD(CLASS_SORCERER, 7, BASE);
   /* SPELL_ADD(CLASS_ENCHANTER, 8, BASE); */
   SPELL_ADD(CLASS_INVOKER, 6, BASE);
#ifdef NEW_BARD
   SPELL_ADD(CLASS_BARD, 10, BASE);
#endif

   SPELL_CREATE("bigbys clenched fist", SPELL_BIGBYS_CLENCHED_FIST, SPELLTYPE_INVOCATION, PULSE_VIOLENCE * 3 / 2, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 15, cast_bigbys_clenched_fist);
   SPELL_ADD(CLASS_SORCERER, 7, BASE);
   SPELL_ADD(CLASS_INVOKER, 7, BASE);

   SPELL_CREATE("dimension door", SPELL_DIMENSION_DOOR, SPELLTYPE_TELEPORTATION, PULSE_VIOLENCE * 3 / 2, TRUE, TAR_CHAR_WORLD | TAR_SELF_NONO, 0, 0, cast_dimension_door);
   SPELL_ADD(CLASS_SORCERER, 5, BASE);
   SPELL_ADD(CLASS_CONJURER, 7, BASE);
   SPELL_ADD(CLASS_NECROMANCER, 7, BASE);
   SPELL_ADD(CLASS_LICH, 7, BASE);
   SPELL_ADD(CLASS_ENCHANTER, 5, BASE);
   SPELL_ADD(CLASS_INVOKER, 5, BASE);
   SPELL_ADD(CLASS_ILLUSIONIST, 5, BASE);
   SPELL_ADD(CLASS_ELEMENTALIST, 7, BASE);

   SPELL_CREATE("relocate", SPELL_RELOCATE, SPELLTYPE_TELEPORTATION, PULSE_VIOLENCE * 5, FALSE, TAR_CHAR_WORLD | TAR_SELF_NONO, 0, 0, cast_relocate);
   SPELL_ADD(CLASS_SORCERER, 9, LEARN);
   SPELL_ADD(CLASS_ENCHANTER, 9, LEARN);
   SPELL_ADD(CLASS_INVOKER, 9, LEARN);
   SPELL_ADD(CLASS_ELEMENTALIST, 9, LEARN);

   SPELL_CREATE("wizard eye", SPELL_WIZARD_EYE, SPELLTYPE_DIVINATION, PULSE_VIOLENCE, FALSE, TAR_CHAR_WORLD | TAR_SELF_NONO, 0, 0, cast_wizard_eye);
   SPELL_ADD(CLASS_SORCERER, 4, BASE);
   SPELL_ADD(CLASS_ENCHANTER, 4, BASE);
   SPELL_ADD(CLASS_ENCHANTER, 4, BASE);


   SPELL_CREATE("clairvoyance", SPELL_CLAIRVOYANCE, SPELLTYPE_DIVINATION, PULSE_VIOLENCE * 2, FALSE, TAR_CHAR_WORLD | TAR_SELF_NONO, 0, 0, cast_clairvoyance);
   SPELL_ADD(CLASS_SORCERER, 6, BASE);
   SPELL_ADD(CLASS_ENCHANTER, 6, BASE);
   SPELL_ADD(CLASS_INVOKER, 6, BASE);
   SPELL_ADD(CLASS_ILLUSIONIST, 6, BASE);
   SPELL_ADD(CLASS_ELEMENTALIST, 6, BASE);

   SPELL_CREATE("rejuvenate major", SPELL_REJUVENATE_MAJOR, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE * 3, FALSE, TAR_CHAR_ROOM, 0, 0, cast_rejuvenate_major);
   SPELL_ADD(CLASS_NECROMANCER, 6, BASE);
   SPELL_ADD(CLASS_LICH, 6, BASE);

   SPELL_CREATE("age", SPELL_AGE, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE * 2, FALSE, TAR_CHAR_ROOM, 0, 0, cast_age);
   SPELL_ADD(CLASS_NECROMANCER, 6, BASE);
   SPELL_ADD(CLASS_LICH, 6, BASE);

   SPELL_CREATE("rejuvenate minor", SPELL_REJUVENATE_MINOR, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE * 2, FALSE, TAR_CHAR_ROOM, 0, 0, cast_rejuvenate_minor);
   SPELL_ADD(CLASS_NECROMANCER, 3, BASE);
   SPELL_ADD(CLASS_LICH, 3, BASE);

   SPELL_CREATE("ray of enfeeblement", SPELL_RAY_OF_ENFEEBLEMENT, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 0, cast_ray_of_enfeeblement);
   SPELL_ADD(CLASS_NECROMANCER, 5, BASE);
   SPELL_ADD(CLASS_LICH, 5, BASE);
   SPELL_ADD(CLASS_SORCERER, 4, BASE);
   SPELL_ADD(CLASS_CONJURER, 4, BASE);
   SPELL_ADD(CLASS_SHAMAN, 6, BASE);
   SPELL_ADD(CLASS_ENCHANTER, 3, BASE);
   SPELL_ADD(CLASS_INVOKER, 4, BASE);
   SPELL_ADD(CLASS_ELEMENTALIST, 4, BASE);
#ifdef NEW_BARD
   SPELL_ADD(CLASS_BATTLECHANTER, 6, BASE);
   SPELL_ADD(CLASS_BARD, 6, BASE);
#endif

   SPELL_CREATE("fumble", SPELL_FUMBLE, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 0, cast_fumble);
   SPELL_ADD(CLASS_ENCHANTER, 4, BASE);

   SPELL_CREATE("stumble", SPELL_STUMBLE, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 0, cast_stumble);
   SPELL_ADD(CLASS_ENCHANTER, 5, BASE);

   SPELL_CREATE("enervate", SPELL_ENERVATE, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 0, cast_enervate);
   SPELL_ADD(CLASS_ENCHANTER, 6, BASE);

   SPELL_CREATE("earthquake", SPELL_EARTHQUAKE, SPELLTYPE_NATURE, PULSE_VIOLENCE * 2, TRUE, TAR_IGNORE, 1, 0, cast_earthquake);
   SPELL_ADD(CLASS_SHAMAN, 4, BASE);
   SPELL_ADD(CLASS_CLERIC, 3, BASE);
   SPELL_ADD(CLASS_DRUID, 4, BASE);

   SPELL_CREATE("dispel evil", SPELL_DISPEL_EVIL, SPELLTYPE_GENERIC, PULSE_VIOLENCE * 3 / 2, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 5, cast_dispel_evil);
   SPELL_ADD(CLASS_CLERIC, 3, BASE);
   /*  SPELL_ADD(CLASS_DRUID, 3, BASE); */
   SPELL_ADD(CLASS_PALADIN, 5, BASE);

   SPELL_CREATE("dispel good", SPELL_DISPEL_GOOD, SPELLTYPE_GENERIC, PULSE_VIOLENCE * 3 / 2, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 5, cast_dispel_good);
   SPELL_ADD(CLASS_CLERIC, 3, BASE);
   /*  SPELL_ADD(CLASS_DRUID, 3, BASE); */
   SPELL_ADD(CLASS_ANTIPALADIN, 5, BASE);

   SPELL_CREATE("call lightning", SPELL_CALL_LIGHTNING, SPELLTYPE_NATURE, PULSE_VIOLENCE * 3 / 2, TRUE, TAR_IGNORE, 1, 11, cast_call_lightning);
   SPELL_ADD(CLASS_DRUID, 5, BASE);
   SPELL_ADD(CLASS_RANGER, 9, BASE);

   SPELL_CREATE("harm", SPELL_HARM, SPELLTYPE_HEALING, PULSE_VIOLENCE * 3 / 2, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 7, cast_harm);
   SPELL_ADD(CLASS_CLERIC, 6, BASE);
   SPELL_ADD(CLASS_DRUID, 5, BASE);
   SPELL_ADD(CLASS_ANTIPALADIN, 9, BASE);

   SPELL_CREATE("full harm", SPELL_FULL_HARM, SPELLTYPE_HEALING, PULSE_VIOLENCE * 2, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 13, cast_full_harm);
   SPELL_ADD(CLASS_CLERIC, 8, BASE);

   SPELL_CREATE("firestorm", SPELL_FIRESTORM, SPELLTYPE_INVOCATION, PULSE_VIOLENCE * 2, TRUE, TAR_IGNORE, 1, 4, cast_firestorm);
#ifdef NEW_BARD
   SPELL_ADD(CLASS_BATTLECHANTER, 10, BASE);
#endif
   SPELL_ADD(CLASS_DRUID, 6, BASE);

   SPELL_CREATE("flame strike", SPELL_FLAMESTRIKE, SPELLTYPE_INVOCATION, PULSE_VIOLENCE * 3 / 2, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 5, cast_flamestrike);
   SPELL_ADD(CLASS_CLERIC, 4, BASE);

   SPELL_CREATE("armor", SPELL_ARMOR, SPELLTYPE_PROTECTION, PULSE_VIOLENCE, TRUE, TAR_CHAR_ROOM, 0, 0, cast_armor);
   SPELL_ADD(CLASS_SHAMAN, 1, BASE);
   SPELL_ADD(CLASS_CLERIC, 1, BASE);
   SPELL_ADD(CLASS_PALADIN, 2, BASE);
   SPELL_ADD(CLASS_ANTIPALADIN, 2, BASE);
#ifdef NEW_BARD
   SPELL_ADD(CLASS_BATTLECHANTER, 4, BASE);
#endif

   SPELL_CREATE("teleport", SPELL_TELEPORT, SPELLTYPE_TELEPORTATION, PULSE_VIOLENCE, TRUE, TAR_SELF_ONLY, 0, 0, cast_teleport);
   SPELL_ADD(CLASS_SORCERER, 4, BASE);
   SPELL_ADD(CLASS_NECROMANCER, 5, BASE);
   SPELL_ADD(CLASS_LICH, 5, BASE);
   SPELL_ADD(CLASS_CONJURER, 5, BASE);
   SPELL_ADD(CLASS_ENCHANTER, 4, BASE);
   SPELL_ADD(CLASS_INVOKER, 4, BASE);
   SPELL_ADD(CLASS_ILLUSIONIST, 4, BASE);
   SPELL_ADD(CLASS_ELEMENTALIST, 5, BASE);

   SPELL_CREATE("bless", SPELL_BLESS, SPELLTYPE_PROTECTION, PULSE_VIOLENCE, FALSE, TAR_OBJ_INV | TAR_OBJ_EQUIP | TAR_CHAR_ROOM, 0, 0, cast_bless);
   SPELL_ADD(CLASS_SHAMAN, 2, BASE);
   SPELL_ADD(CLASS_CLERIC, 2, BASE);
   SPELL_ADD(CLASS_DRUID, 2, BASE);
   SPELL_ADD(CLASS_ANTIPALADIN, 1, BASE);
   SPELL_ADD(CLASS_PALADIN, 1, BASE);
   SPELL_ADD(CLASS_RANGER, 3, BASE);
   SPELL_ADD(CLASS_DIRERAIDER, 3, BASE);

   SPELL_CREATE("natures blessing", SPELL_NATURES_BLESSING, SPELLTYPE_NATURE, 0, TRUE, TAR_SELF_ONLY, 0, 0, cast_natures_blessing);
   SPELL_ADD(CLASS_RANGER, 7, BASE);

   SPELL_CREATE("blindness", SPELL_BLINDNESS, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE * 3 / 2, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 0, cast_blindness);
   SPELL_ADD(CLASS_SHAMAN, 3, BASE);
   SPELL_ADD(CLASS_CLERIC, 4, BASE);
   SPELL_ADD(CLASS_ANTIPALADIN, 6, BASE);
#ifdef NEW_BARD
   SPELL_ADD(CLASS_BATTLECHANTER, 6, BASE);
#endif
   SPELL_ADD(CLASS_DIRERAIDER, 6, BASE);

   SPELL_CREATE("minor globe of invulnerability", SPELL_MINOR_GLOBE, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE * 2, TRUE, TAR_CHAR_ROOM, 0, 0, cast_minor_globe);
   SPELL_ADD(CLASS_NECROMANCER, 6, BASE);
   SPELL_ADD(CLASS_LICH, 6, BASE);
   SPELL_ADD(CLASS_SORCERER, 5, BASE);
   SPELL_ADD(CLASS_SHAMAN, 7, BASE);
   SPELL_ADD(CLASS_ENCHANTER, 5, BASE);

   SPELL_CREATE("charm person", SPELL_CHARM_PERSON, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE * 2, TRUE, TAR_CHAR_ROOM | TAR_SELF_NONO, 0, 0, cast_charm_person);
   SPELL_ADD(CLASS_ENCHANTER, 5, BASE);

   SPELL_CREATE("infravision", SPELL_INFRAVISION, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE * 2, FALSE, TAR_CHAR_ROOM, 0, 0, cast_infravision);
   SPELL_ADD(CLASS_SORCERER, 5, BASE);
   SPELL_ADD(CLASS_CONJURER, 5, BASE);
   SPELL_ADD(CLASS_ENCHANTER, 5, BASE);
   SPELL_ADD(CLASS_INVOKER, 5, BASE);
   SPELL_ADD(CLASS_NECROMANCER, 5, BASE);
   SPELL_ADD(CLASS_LICH, 5, BASE);
   SPELL_ADD(CLASS_ELEMENTALIST, 5, BASE);

   SPELL_CREATE("creeping doom", SPELL_CREEPING, SPELLTYPE_NATURE, PULSE_VIOLENCE * 4, TRUE, TAR_IGNORE, 1, 12, cast_creeping);
   SPELL_ADD(CLASS_DRUID, 10, LEARN);

   SPELL_CREATE("control weather", SPELL_CONTROL_WEATHER, SPELLTYPE_NATURE, PULSE_VIOLENCE * 3, FALSE, TAR_IGNORE, 0, 0, cast_control_weather);
   SPELL_ADD(CLASS_DRUID, 5, BASE);
   SPELL_ADD(CLASS_SHAMAN, 9, BASE);
   SPELL_ADD(CLASS_RANGER, 10, BASE);

   SPELL_CREATE("minor creation", SPELL_MINOR_CREATION, SPELLTYPE_GENERIC, PULSE_VIOLENCE, FALSE, TAR_IGNORE, 0, 0, cast_minor_creation);
   SPELL_ADD(CLASS_NECROMANCER, 2, BASE);
   SPELL_ADD(CLASS_LICH, 2, BASE);
   SPELL_ADD(CLASS_SORCERER, 2, BASE);
   SPELL_ADD(CLASS_CONJURER, 1, BASE);
   /*  SPELL_ADD(CLASS_RANGER, 5, BASE); */
   SPELL_ADD(CLASS_ENCHANTER, 2, BASE);
   SPELL_ADD(CLASS_INVOKER, 2, BASE);
   SPELL_ADD(CLASS_ILLUSIONIST, 2, BASE);
   SPELL_ADD(CLASS_ELEMENTALIST, 1, BASE);
#ifdef NEW_BARD
   SPELL_ADD(CLASS_BARD, 2, BASE);
#endif



   SPELL_CREATE("create food", SPELL_CREATE_FOOD, SPELLTYPE_SUMMONING, PULSE_VIOLENCE, FALSE, TAR_IGNORE, 0, 0, cast_create_food);
   SPELL_ADD(CLASS_SHAMAN, 1, BASE);
   SPELL_ADD(CLASS_CLERIC, 1, BASE);
   /*  SPELL_ADD(CLASS_DRUID, 1, BASE); */
   SPELL_ADD(CLASS_PALADIN, 3, BASE);
   /*SPELL_ADD(CLASS_RANGER, 3, BASE); Removed 7/98 */
   SPELL_ADD(CLASS_ANTIPALADIN, 3, BASE);
#ifdef NEW_BARD
   SPELL_ADD(CLASS_BATTLECHANTER, 2, BASE);
#endif
   SPELL_ADD(CLASS_DIRERAIDER, 2, BASE);

   SPELL_CREATE("create water", SPELL_CREATE_WATER, SPELLTYPE_SUMMONING, PULSE_VIOLENCE, FALSE, TAR_OBJ_INV | TAR_OBJ_EQUIP, 0, 0, cast_create_water);
   SPELL_ADD(CLASS_SHAMAN, 1, BASE);
   SPELL_ADD(CLASS_CLERIC, 1, BASE);
   SPELL_ADD(CLASS_DRUID, 1, BASE);
   SPELL_ADD(CLASS_PALADIN, 3, BASE);
   SPELL_ADD(CLASS_ANTIPALADIN, 3, BASE);
   SPELL_ADD(CLASS_RANGER, 2, BASE);
#ifdef NEW_BARD
   SPELL_ADD(CLASS_BATTLECHANTER, 2, BASE);
#endif
   SPELL_ADD(CLASS_DIRERAIDER, 2, BASE);

   SPELL_CREATE("vigorize light", SPELL_VIGORIZE_LIGHT, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE * 2, TRUE, TAR_CHAR_ROOM, 0, 0, cast_vigorize_light);
   SPELL_ADD(CLASS_SHAMAN, 2, BASE);
   SPELL_ADD(CLASS_CLERIC, 2, BASE);
   SPELL_ADD(CLASS_DRUID, 2, BASE);
   SPELL_ADD(CLASS_RANGER, 1, BASE);
   SPELL_ADD(CLASS_DIRERAIDER, 1, BASE);

   SPELL_CREATE("cure blind", SPELL_CURE_BLIND, SPELLTYPE_HEALING, PULSE_VIOLENCE * 2, TRUE, TAR_CHAR_ROOM, 0, 0, cast_cure_blind);
   SPELL_ADD(CLASS_SHAMAN, 4, BASE);
   SPELL_ADD(CLASS_CLERIC, 3, BASE);
   /*  SPELL_ADD(CLASS_DRUID, 3, BASE); */
   SPELL_ADD(CLASS_PALADIN, 6, BASE);

   SPELL_CREATE("vigorize serious", SPELL_VIGORIZE_SERIOUS, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE * 2, TRUE, TAR_CHAR_ROOM, 0, 0, cast_vigorize_serious);
   SPELL_ADD(CLASS_SHAMAN, 3, BASE);
   SPELL_ADD(CLASS_CLERIC, 3, BASE);
   SPELL_ADD(CLASS_DRUID, 3, BASE);
   SPELL_ADD(CLASS_RANGER, 3, BASE);
   SPELL_ADD(CLASS_DIRERAIDER, 3, BASE);

   SPELL_CREATE("vigorize critic", SPELL_VIGORIZE_CRITIC, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE * 2, TRUE, TAR_CHAR_ROOM, 0, 0, cast_vigorize_critic);
   SPELL_ADD(CLASS_SHAMAN, 4, BASE);
   SPELL_ADD(CLASS_CLERIC, 5, BASE);
   SPELL_ADD(CLASS_DRUID, 5, BASE);
   SPELL_ADD(CLASS_RANGER, 5, BASE);
   SPELL_ADD(CLASS_DIRERAIDER, 5, BASE);

   SPELL_CREATE("curse", SPELL_CURSE, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE * 3 / 2, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 0, cast_curse);
   SPELL_ADD(CLASS_CLERIC, 6, BASE);
   SPELL_ADD(CLASS_ANTIPALADIN, 4, BASE);

   SPELL_CREATE("curse item", SPELL_CURSE_OBJ, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE * 3 / 2, TRUE, TAR_OBJ_INV | TAR_SELF_NONO, 1, 0, cast_curse_obj);
   SPELL_ADD(CLASS_CLERIC, 6, BASE);
   SPELL_ADD(CLASS_ANTIPALADIN, 4, BASE);

   SPELL_CREATE("water breathing", SPELL_WATERBREATH, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE * 2, FALSE, TAR_CHAR_ROOM, 0, 0, cast_waterbreath);
   SPELL_ADD(CLASS_CLERIC, 7, BASE);
   SPELL_ADD(CLASS_DRUID, 8, BASE);
   SPELL_ADD(CLASS_SHAMAN, 8, BASE);

   SPELL_CREATE("farsee", SPELL_FARSEE, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE * 5 / 2, FALSE, TAR_IGNORE, 0, 0, cast_farsee);
   SPELL_ADD(CLASS_SORCERER, 4, BASE);
   SPELL_ADD(CLASS_SHAMAN, 6, BASE);
   SPELL_ADD(CLASS_ENCHANTER, 4, BASE);
   SPELL_ADD(CLASS_INVOKER, 4, BASE);
   SPELL_ADD(CLASS_ILLUSIONIST, 4, BASE);
   SPELL_ADD(CLASS_DIRERAIDER, 9, BASE);

   SPELL_CREATE("fear", SPELL_FEAR, SPELLTYPE_GENERIC, PULSE_VIOLENCE, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 0, cast_fear);
   SPELL_ADD(CLASS_SHAMAN, 6, BASE);
   SPELL_ADD(CLASS_CLERIC, 6, BASE);
   SPELL_ADD(CLASS_DRUID, 6, BASE);
   SPELL_ADD(CLASS_ANTIPALADIN, 6, BASE);
   SPELL_ADD(CLASS_DIRERAIDER, 6, BASE);

   SPELL_CREATE("cure light", SPELL_CURE_LIGHT, SPELLTYPE_HEALING, PULSE_VIOLENCE, TRUE, TAR_CHAR_ROOM, 0, 0, cast_cure_light);
   SPELL_ADD(CLASS_SHAMAN, 1, BASE);
   SPELL_ADD(CLASS_CLERIC, 1, BASE);
   SPELL_ADD(CLASS_DRUID, 1, BASE);
   SPELL_ADD(CLASS_RANGER, 2, BASE);
   SPELL_ADD(CLASS_PALADIN, 1, BASE);
   SPELL_ADD(CLASS_DIRERAIDER, 1, BASE);

   SPELL_CREATE("cause light", SPELL_CAUSE_LIGHT, SPELLTYPE_HEALING, PULSE_VIOLENCE, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 1, cast_cause_light);
   SPELL_ADD(CLASS_SHAMAN, 1, BASE);
   SPELL_ADD(CLASS_CLERIC, 1, BASE);
   SPELL_ADD(CLASS_DRUID, 1, BASE);
   SPELL_ADD(CLASS_ANTIPALADIN, 1, BASE);

   SPELL_CREATE("cure serious", SPELL_CURE_SERIOUS, SPELLTYPE_HEALING, PULSE_VIOLENCE * 3 / 2, TRUE, TAR_CHAR_ROOM, 0, 0, cast_cure_serious);
   SPELL_ADD(CLASS_SHAMAN, 2, BASE);
   SPELL_ADD(CLASS_CLERIC, 2, BASE);
   SPELL_ADD(CLASS_DRUID, 2, BASE);
   SPELL_ADD(CLASS_PALADIN, 3, BASE);
   SPELL_ADD(CLASS_RANGER, 4, BASE);
   SPELL_ADD(CLASS_DIRERAIDER, 4, BASE);

   SPELL_CREATE("cause serious", SPELL_CAUSE_SERIOUS, SPELLTYPE_HEALING, PULSE_VIOLENCE * 5 / 4, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 1, cast_cause_serious);
   SPELL_ADD(CLASS_SHAMAN, 2, BASE);
   SPELL_ADD(CLASS_CLERIC, 2, BASE);
   SPELL_ADD(CLASS_DRUID, 2, BASE);
   //  SPELL_ADD(CLASS_ANTIPALADIN, 3, BASE);

   SPELL_CREATE("cure critic", SPELL_CURE_CRITIC, SPELLTYPE_HEALING, PULSE_VIOLENCE * 2, TRUE, TAR_CHAR_ROOM, 0, 0, cast_cure_critic);
   SPELL_ADD(CLASS_SHAMAN, 4, BASE);
   SPELL_ADD(CLASS_CLERIC, 3, BASE);
   SPELL_ADD(CLASS_DRUID, 4, BASE);
   SPELL_ADD(CLASS_PALADIN, 5, BASE);
   SPELL_ADD(CLASS_RANGER, 8, BASE);
   SPELL_ADD(CLASS_DIRERAIDER, 8, BASE);

   SPELL_CREATE("cause critical", SPELL_CAUSE_CRITICAL, SPELLTYPE_HEALING, PULSE_VIOLENCE * 4 / 3, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 3, cast_cause_critical);
   SPELL_ADD(CLASS_SHAMAN, 4, BASE);
   SPELL_ADD(CLASS_CLERIC, 3, BASE);
   SPELL_ADD(CLASS_DRUID, 4, BASE);
   //  SPELL_ADD(CLASS_ANTIPALADIN, 5, BASE);

   SPELL_CREATE("heal", SPELL_HEAL, SPELLTYPE_HEALING, PULSE_VIOLENCE * 3 / 2, TRUE, TAR_CHAR_ROOM, 0, 0,cast_heal);
   SPELL_ADD(CLASS_SHAMAN, 6, BASE);
   SPELL_ADD(CLASS_DRUID, 6, BASE);
   SPELL_ADD(CLASS_CLERIC, 5, BASE);
   SPELL_ADD(CLASS_PALADIN, 9, BASE);

   SPELL_CREATE("full heal", SPELL_FULL_HEAL, SPELLTYPE_HEALING, PULSE_VIOLENCE * 2, TRUE, TAR_CHAR_ROOM, 0, 0, cast_full_heal);
   SPELL_ADD(CLASS_CLERIC, 7, BASE);

   SPELL_CREATE("miracle", SPELL_MIRACLE, SPELLTYPE_HEALING, PULSE_VIOLENCE * 2, TRUE, TAR_CHAR_ROOM, 0, 0, cast_miracle);
   //SPELL_ADD(CLASS_CLERIC, 10, LEARN);

   SPELL_CREATE("vitality", SPELL_VITALITY, SPELLTYPE_HEALING, PULSE_VIOLENCE * 7 / 3, TRUE, TAR_CHAR_ROOM, 0, 0, cast_vitality);
   SPELL_ADD(CLASS_CLERIC, 5, BASE);
   SPELL_ADD(CLASS_DRUID, 6, BASE);
   SPELL_ADD(CLASS_SHAMAN, 7, BASE);

   SPELL_CREATE("divine purification", SPELL_DIVINE_PURIFICATION, SPELLTYPE_HEALING, PULSE_VIOLENCE * 3, TRUE, TAR_CHAR_ROOM, 0, 0, cast_divine_purification);
   SPELL_ADD(CLASS_CLERIC, 9, BASE);

#if 0 // Duplicated
   SPELL_CREATE("mage flame", SPELL_MAGE_FLAME, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE * 3 / 2, FALSE, TAR_CHAR_ROOM, 0, 0, cast_mage_flame);
   SPELL_ADD(CLASS_ENCHANTER, 1, BASE);
   SPELL_ADD(CLASS_INVOKER, 1, BASE);
   SPELL_ADD(CLASS_ILLUSIONIST, 1, BASE);
   SPELL_ADD(CLASS_CONJURER, 1, BASE);
   SPELL_ADD(CLASS_NECROMANCER, 1, BASE);
   SPELL_ADD(CLASS_ELEMENTALIST, 1, BASE);
#endif


   SPELL_CREATE("detect invisibility", SPELL_DETECT_INVISIBLE, SPELLTYPE_DIVINATION, PULSE_VIOLENCE * 3 / 2, FALSE, TAR_CHAR_ROOM, 0, 0, cast_detect_invisibility);
   SPELL_ADD(CLASS_NECROMANCER, 1, BASE);
   SPELL_ADD(CLASS_LICH, 1, BASE);
   SPELL_ADD(CLASS_SORCERER, 1, BASE);
   SPELL_ADD(CLASS_CONJURER, 1, BASE);
   SPELL_ADD(CLASS_SHAMAN, 2, BASE);
   SPELL_ADD(CLASS_RANGER, 3, BASE);
   SPELL_ADD(CLASS_ENCHANTER, 1, BASE);
   SPELL_ADD(CLASS_INVOKER, 1, BASE);
   SPELL_ADD(CLASS_ILLUSIONIST, 1, BASE);
   SPELL_ADD(CLASS_ELEMENTALIST, 1, BASE);
#ifdef NEW_BARD
   SPELL_ADD(CLASS_BATTLECHANTER, 2, BASE);
   SPELL_ADD(CLASS_BARD, 2, BASE);
#endif
   SPELL_ADD(CLASS_DIRERAIDER, 3, BASE);

   SPELL_CREATE("detect evil", SPELL_DETECT_EVIL, SPELLTYPE_DIVINATION, PULSE_VIOLENCE, FALSE, TAR_CHAR_ROOM, 0, 0, cast_detect_evil);
   SPELL_ADD(CLASS_CLERIC, 1, BASE);
   SPELL_ADD(CLASS_DRUID, 2, BASE);
   SPELL_ADD(CLASS_SHAMAN, 2, BASE);
   SPELL_ADD(CLASS_ANTIPALADIN, 2, BASE);
   SPELL_ADD(CLASS_PALADIN, 1, BASE);
   SPELL_ADD(CLASS_RANGER, 2, BASE);

   SPELL_CREATE("detect good", SPELL_DETECT_GOOD, SPELLTYPE_DIVINATION, PULSE_VIOLENCE, FALSE, TAR_CHAR_ROOM, 0, 0, cast_detect_good);
   SPELL_ADD(CLASS_CLERIC, 1, BASE);
   SPELL_ADD(CLASS_DRUID, 2, BASE);
   SPELL_ADD(CLASS_SHAMAN, 2, BASE);
   SPELL_ADD(CLASS_RANGER, 2, BASE);
   SPELL_ADD(CLASS_ANTIPALADIN, 1, BASE);
   SPELL_ADD(CLASS_PALADIN, 2, BASE);

   SPELL_CREATE("detect magic", SPELL_DETECT_MAGIC, SPELLTYPE_DIVINATION, PULSE_VIOLENCE, FALSE, TAR_CHAR_ROOM, 0, 0, cast_detect_magic);
   SPELL_ADD(CLASS_CLERIC, 1, BASE);
   SPELL_ADD(CLASS_SORCERER, 1, BASE);
   SPELL_ADD(CLASS_NECROMANCER, 1, BASE);
   SPELL_ADD(CLASS_LICH, 1, BASE);
   SPELL_ADD(CLASS_DRUID, 1, BASE);
   SPELL_ADD(CLASS_CONJURER, 2, BASE);
   SPELL_ADD(CLASS_SHAMAN, 2, BASE);
   SPELL_ADD(CLASS_RANGER, 1, BASE);
   SPELL_ADD(CLASS_ENCHANTER, 1, BASE);
   SPELL_ADD(CLASS_INVOKER, 1, BASE);
   SPELL_ADD(CLASS_ILLUSIONIST, 1, BASE);
   SPELL_ADD(CLASS_ELEMENTALIST, 2, BASE);
   SPELL_ADD(CLASS_DIRERAIDER, 1, BASE);
#ifdef NEW_BARD
   SPELL_ADD(CLASS_BATTLECHANTER, 2, BASE);
   SPELL_ADD(CLASS_BARD, 2, BASE);
#endif

   SPELL_CREATE("plane shift", SPELL_PLANE_SHIFT, SPELLTYPE_TELEPORTATION, PULSE_VIOLENCE * 2, FALSE, TAR_IGNORE, 0, 0, cast_plane_shift);
   SPELL_ADD(CLASS_CLERIC, 9, LEARN);
   SPELL_ADD(CLASS_DRUID, 9, LEARN);

   SPELL_CREATE("gate", SPELL_GATE, SPELLTYPE_TELEPORTATION, PULSE_VIOLENCE * 3, FALSE, TAR_IGNORE, 0, 0, cast_gate);
   SPELL_ADD(CLASS_SORCERER, 9, LEARN);
   SPELL_ADD(CLASS_ENCHANTER, 9, LEARN);
   SPELL_ADD(CLASS_INVOKER, 9, LEARN);
   SPELL_ADD(CLASS_ILLUSIONIST, 9, LEARN);
   SPELL_ADD(CLASS_ELEMENTALIST, 9, LEARN);

   SPELL_CREATE("resurrect", SPELL_RESURRECT, SPELLTYPE_HEALING, PULSE_VIOLENCE * 10, FALSE, TAR_OBJ_ROOM, 0, 0, cast_resurrect);
   SPELL_ADD(CLASS_CLERIC, 10, LEARN);

   SPELL_CREATE("preserve", SPELL_PRESERVE, SPELLTYPE_GENERIC, PULSE_VIOLENCE * 3, FALSE, TAR_OBJ_ROOM, 0, 0, cast_preserve);
   SPELL_ADD(CLASS_CLERIC, 2, BASE);
   SPELL_ADD(CLASS_NECROMANCER, 1, BASE);
   SPELL_ADD(CLASS_LICH, 1, BASE);
   SPELL_ADD(CLASS_DRUID, 2, BASE);
   SPELL_ADD(CLASS_SHAMAN, 3, BASE);

   SPELL_CREATE("mass charm", SPELL_MASS_CHARM, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE * 3, FALSE, TAR_IGNORE, 1, 0, cast_mass_charm);
#if 0
   SPELL_ADD(CLASS_SORCERER, 6, BASE);
#endif

   SPELL_CREATE("mass invisibility", SPELL_MASS_INVIS, SPELLTYPE_ILLUSION, PULSE_VIOLENCE * 3, FALSE, TAR_IGNORE, 0, 0, cast_mass_invisibility);
   SPELL_ADD(CLASS_SORCERER, 4, BASE);
   SPELL_ADD(CLASS_ENCHANTER, 4, BASE);
   SPELL_ADD(CLASS_ILLUSIONIST, 3, BASE);

   SPELL_CREATE("enchant weapon", SPELL_ENCHANT_WEAPON, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE * 8, FALSE, TAR_OBJ_INV | TAR_OBJ_EQUIP, 0, 0, cast_enchant_weapon);
   SPELL_ADD(CLASS_CONJURER, 4, BASE);
   SPELL_ADD(CLASS_ENCHANTER, 6, BASE);

   SPELL_CREATE("dispel invisible", SPELL_DISPEL_INVISIBLE, SPELLTYPE_DIVINATION, PULSE_VIOLENCE * 2, FALSE, TAR_CHAR_ROOM | TAR_OBJ_INV | TAR_OBJ_ROOM | TAR_OBJ_EQUIP, 0, 0, cast_dispel_invisible);
   /*  SPELL_ADD(CLASS_NECROMANCER, 3, 90, 35, BASE); */
   SPELL_ADD(CLASS_SORCERER, 3, BASE);
   SPELL_ADD(CLASS_ENCHANTER, 3, BASE);
   SPELL_ADD(CLASS_ILLUSIONIST, 2, BASE);

   SPELL_CREATE("invisibility", SPELL_INVISIBLE, SPELLTYPE_ILLUSION, PULSE_VIOLENCE * 4 / 3, FALSE, TAR_CHAR_ROOM | TAR_OBJ_INV | TAR_OBJ_ROOM | TAR_OBJ_EQUIP, 0, 0, cast_invisibility);
   SPELL_ADD(CLASS_NECROMANCER, 2, BASE);
   SPELL_ADD(CLASS_LICH, 2, BASE);
   SPELL_ADD(CLASS_SORCERER, 2, BASE);
   SPELL_ADD(CLASS_CONJURER, 2, BASE);
   SPELL_ADD(CLASS_SHAMAN, 2, BASE);
   SPELL_ADD(CLASS_RANGER, 4, BASE);
   SPELL_ADD(CLASS_ENCHANTER, 2, BASE);
   SPELL_ADD(CLASS_ILLUSIONIST, 2, BASE);
   SPELL_ADD(CLASS_INVOKER, 3, BASE);
   SPELL_ADD(CLASS_ELEMENTALIST, 2, BASE);
#ifdef NEW_BARD
   SPELL_ADD(CLASS_BATTLECHANTER, 4, BASE);
   SPELL_ADD(CLASS_BARD, 4, BASE);
#endif
   SPELL_ADD(CLASS_DIRERAIDER, 4, BASE);

   SPELL_CREATE("locate object", SPELL_LOCATE_OBJECT, SPELLTYPE_DIVINATION, PULSE_VIOLENCE * 3, FALSE, TAR_IGNORE, 0, 0, cast_locate_object);
   SPELL_ADD(CLASS_SORCERER, 3, BASE);
   SPELL_ADD(CLASS_CONJURER, 3, BASE);
   SPELL_ADD(CLASS_INVOKER, 3, BASE);
   SPELL_ADD(CLASS_ENCHANTER, 3, BASE);
   SPELL_ADD(CLASS_ILLUSIONIST, 3, BASE);
   SPELL_ADD(CLASS_ELEMENTALIST, 3, BASE);


   SPELL_CREATE("poison", SPELL_POISON, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE * 2, TRUE, TAR_CHAR_ROOM | TAR_SELF_NONO | TAR_OBJ_INV | TAR_OBJ_EQUIP | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 0, cast_poison);
   /*  SPELL_ADD(CLASS_DRUID, 4, BASE); */
   SPELL_ADD(CLASS_ANTIPALADIN, 5, BASE);
   SPELL_ADD(CLASS_DIRERAIDER, 4, BASE);

   SPELL_CREATE("protection from evil", SPELL_PROTECT_FROM_EVIL, SPELLTYPE_PROTECTION, PULSE_VIOLENCE, TRUE, TAR_CHAR_ROOM, 0, 0, cast_protection_from_evil);
   SPELL_ADD(CLASS_SHAMAN, 5, BASE);
   SPELL_ADD(CLASS_CLERIC, 4, BASE);
   /*  SPELL_ADD(CLASS_DRUID, 4, BASE); */
   SPELL_ADD(CLASS_PALADIN, 4, BASE);

   SPELL_CREATE("protection from good", SPELL_PROTECT_FROM_GOOD, SPELLTYPE_PROTECTION, PULSE_VIOLENCE, TRUE, TAR_CHAR_ROOM, 0, 0, cast_protection_from_good);
   SPELL_ADD(CLASS_SHAMAN, 5, BASE);
   SPELL_ADD(CLASS_CLERIC, 4, BASE);
   /*  SPELL_ADD(CLASS_DRUID, 4, BASE); */
   SPELL_ADD(CLASS_ANTIPALADIN, 4, BASE);

#ifndef NEW_NECRO
   SPELL_CREATE("animate skeleton", SPELL_ANIMATE_SKELETON, SPELLTYPE_NECROMANCY, PULSE_VIOLENCE * 3, FALSE, TAR_OBJ_ROOM, 0, 0, cast_animate_skeleton);
   SPELL_ADD(CLASS_LICH, 3, BASE);
   SPELL_ADD(CLASS_NECROMANCER, 3, BASE);

   SPELL_CREATE("animate zombie", SPELL_ANIMATE_ZOMBIE, SPELLTYPE_NECROMANCY, PULSE_VIOLENCE * 3, FALSE, TAR_OBJ_ROOM, 0, 0, cast_animate_zombie);
   SPELL_ADD(CLASS_LICH, 3, BASE);
   SPELL_ADD(CLASS_NECROMANCER, 3, BASE);

   SPELL_CREATE("animate spectre", SPELL_ANIMATE_SPECTRE, SPELLTYPE_NECROMANCY, PULSE_VIOLENCE * 3, FALSE, TAR_OBJ_ROOM, 0, 0, cast_animate_spectre);
   SPELL_ADD(CLASS_LICH, 4, BASE);
   SPELL_ADD(CLASS_NECROMANCER, 4, BASE);

   SPELL_CREATE("animate wraith", SPELL_ANIMATE_WRAITH, SPELLTYPE_NECROMANCY, PULSE_VIOLENCE * 3, FALSE, TAR_OBJ_ROOM, 0, 0, cast_animate_wraith);
   SPELL_ADD(CLASS_LICH, 6, BASE);
   SPELL_ADD(CLASS_NECROMANCER, 6, BASE);

   SPELL_CREATE("animate ghoul", SPELL_ANIMATE_GHOUL, SPELLTYPE_NECROMANCY, PULSE_VIOLENCE * 3, FALSE, TAR_OBJ_ROOM, 0, 0, cast_animate_ghoul);
   SPELL_ADD(CLASS_LICH, 7, BASE);
   SPELL_ADD(CLASS_NECROMANCER, 7, BASE);
#endif // ndef NEW_NECRO

#ifdef NEW_NECRO

   SPELL_CREATE("animate skeleton", SPELL_ANIMATE_SKELETON, SPELLTYPE_NECROMANCY, PULSE_VIOLENCE * 3, FALSE, TAR_OBJ_ROOM, 0, 0, cast_animate_skeleton);
   SPELL_ADD(CLASS_LICH, 1, BASE);
   SPELL_ADD(CLASS_NECROMANCER, 1, BASE);

   SPELL_CREATE("animate zombie", SPELL_ANIMATE_ZOMBIE, SPELLTYPE_NECROMANCY, PULSE_VIOLENCE * 3, FALSE, TAR_OBJ_ROOM, 0, 0, cast_animate_zombie);
   SPELL_ADD(CLASS_LICH, 2, BASE);
   SPELL_ADD(CLASS_NECROMANCER, 2, BASE);

   SPELL_CREATE("animate spectre", SPELL_ANIMATE_SPECTRE, SPELLTYPE_NECROMANCY, PULSE_VIOLENCE * 3, FALSE, TAR_OBJ_ROOM, 0, 0, cast_animate_spectre);
   SPELL_ADD(CLASS_LICH, 8, BASE);
   SPELL_ADD(CLASS_NECROMANCER, 8, BASE);

   SPELL_CREATE("animate wraith", SPELL_ANIMATE_WRAITH, SPELLTYPE_NECROMANCY, PULSE_VIOLENCE * 3, FALSE, TAR_OBJ_ROOM, 0, 0, cast_animate_wraith);
   SPELL_ADD(CLASS_LICH, 7, BASE);
   SPELL_ADD(CLASS_NECROMANCER, 7, BASE);

   SPELL_CREATE("animate ghoul", SPELL_ANIMATE_GHOUL, SPELLTYPE_NECROMANCY, PULSE_VIOLENCE * 3, FALSE, TAR_OBJ_ROOM, 0, 0, cast_animate_ghoul);
   SPELL_ADD(CLASS_LICH, 3, BASE);
   SPELL_ADD(CLASS_NECROMANCER, 3, BASE);

   SPELL_CREATE("animate ghast", SPELL_ANIMATE_GHAST, SPELLTYPE_NECROMANCY, PULSE_VIOLENCE * 3, FALSE, TAR_OBJ_ROOM, 0, 0, cast_animate_ghast);
   SPELL_ADD(CLASS_LICH, 6, BASE);
   SPELL_ADD(CLASS_NECROMANCER, 6, BASE);

   SPELL_CREATE("animate ghost", SPELL_ANIMATE_GHOST, SPELLTYPE_NECROMANCY, PULSE_VIOLENCE * 3, FALSE, TAR_OBJ_ROOM, 0, 0, cast_animate_ghost);
   SPELL_ADD(CLASS_LICH, 9, BASE);
   SPELL_ADD(CLASS_NECROMANCER, 9, BASE);

   SPELL_CREATE("animate wight", SPELL_ANIMATE_WIGHT, SPELLTYPE_NECROMANCY, PULSE_VIOLENCE * 3, FALSE, TAR_OBJ_ROOM, 0, 0, cast_animate_wight);
   SPELL_ADD(CLASS_LICH, 5, BASE);
   SPELL_ADD(CLASS_NECROMANCER, 5, BASE);

   SPELL_CREATE("animate shadow", SPELL_ANIMATE_SHADOW, SPELLTYPE_NECROMANCY, PULSE_VIOLENCE * 3, FALSE, TAR_OBJ_ROOM, 0, 0, cast_animate_shadow);
   SPELL_ADD(CLASS_LICH, 4, BASE);
   SPELL_ADD(CLASS_NECROMANCER, 4, BASE);

#endif // NEW_NECRO

   SPELL_CREATE("cyclone", SPELL_CYCLONE, SPELLTYPE_NATURE, PULSE_VIOLENCE* 2, TRUE, TAR_IGNORE, 1, 13, cast_cyclone);
   SPELL_ADD(CLASS_DRUID, 7, BASE);

   SPELL_CREATE("remove curse", SPELL_REMOVE_CURSE, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE * 4, FALSE, TAR_CHAR_ROOM | TAR_OBJ_INV | TAR_OBJ_EQUIP | TAR_OBJ_ROOM, 0, 0, cast_remove_curse);
   SPELL_ADD(CLASS_PALADIN, 4, BASE);
   SPELL_ADD(CLASS_CLERIC, 4, BASE);
   /*  SPELL_ADD(CLASS_DRUID, 5, BASE); */
   SPELL_ADD(CLASS_SHAMAN, 5, BASE);

   SPELL_CREATE("stone skin", SPELL_STONE_SKIN, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE * 4 / 3, TRUE, TAR_CHAR_ROOM, 0, 0, cast_stone_skin);
   SPELL_ADD(CLASS_SHAMAN, 6, BASE);
   SPELL_ADD(CLASS_CONJURER, 6, BASE);
#if 0
   SPELL_ADD(CLASS_DRUID, 4, BASE);
#endif
   SPELL_ADD(CLASS_SORCERER, 6, BASE);
   SPELL_ADD(CLASS_ENCHANTER, 5, BASE);
   SPELL_ADD(CLASS_ELEMENTALIST, 6, BASE);
#ifdef NEW_BARD
   SPELL_ADD(CLASS_BATTLECHANTER, 10, BASE);
#endif


   SPELL_CREATE("sleep", SPELL_SLEEP, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE * 2, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT, 0, 0, cast_sleep);
   SPELL_ADD(CLASS_NECROMANCER, 4, BASE);
   SPELL_ADD(CLASS_LICH, 4, BASE);
   SPELL_ADD(CLASS_SORCERER, 4, BASE);
   SPELL_ADD(CLASS_CONJURER, 4, BASE);
   SPELL_ADD(CLASS_RANGER, 6, BASE);
   SPELL_ADD(CLASS_ENCHANTER, 4, BASE);
   SPELL_ADD(CLASS_ELEMENTALIST, 4, BASE);

   SPELL_CREATE("dispel magic", SPELL_DISPEL_MAGIC, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE * 2, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT, 0, 0, cast_dispel_magic);
   SPELL_ADD(CLASS_CONJURER, 3, BASE);
   SPELL_ADD(CLASS_SORCERER, 4, BASE);
   SPELL_ADD(CLASS_NECROMANCER, 4, BASE);
   SPELL_ADD(CLASS_LICH, 4, BASE);
   SPELL_ADD(CLASS_CLERIC, 5, BASE);
   SPELL_ADD(CLASS_DRUID, 5, BASE);
   SPELL_ADD(CLASS_RANGER, 7, BASE);
   SPELL_ADD(CLASS_PALADIN, 7, BASE);
   SPELL_ADD(CLASS_ANTIPALADIN, 7, BASE);
   SPELL_ADD(CLASS_SHAMAN, 6, BASE);
   SPELL_ADD(CLASS_ENCHANTER, 4, BASE);
   SPELL_ADD(CLASS_INVOKER, 4, BASE);
   SPELL_ADD(CLASS_ILLUSIONIST, 4, BASE);
   SPELL_ADD(CLASS_ELEMENTALIST, 3, BASE);
#ifdef NEW_BARD
   SPELL_ADD(CLASS_BATTLECHANTER, 8, BASE);
   SPELL_ADD(CLASS_BARD, 8, BASE);
#endif
   SPELL_ADD(CLASS_DIRERAIDER, 7, BASE);

   SPELL_CREATE("strength", SPELL_STRENGTH, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE * 2, FALSE, TAR_CHAR_ROOM, 0, 0, cast_strength);
   SPELL_ADD(CLASS_NECROMANCER, 4, BASE);
   SPELL_ADD(CLASS_LICH, 4, BASE);
   SPELL_ADD(CLASS_SORCERER, 3, BASE);
   SPELL_ADD(CLASS_CONJURER, 3, BASE);
   SPELL_ADD(CLASS_RANGER, 7, BASE);
   SPELL_ADD(CLASS_ENCHANTER, 3, BASE);
   SPELL_ADD(CLASS_DIRERAIDER, 7, BASE);

   SPELL_CREATE("dexterity", SPELL_DEXTERITY, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE * 2, FALSE, TAR_CHAR_ROOM, 0, 0, cast_dexterity);
   SPELL_ADD(CLASS_SORCERER, 3, BASE);
   SPELL_ADD(CLASS_CONJURER, 3, BASE);
   SPELL_ADD(CLASS_ENCHANTER, 3, BASE);

   /*Moradin 08/17/01 Spell added for glowing_crimson_dagger proc*/
   SPELL_CREATE("agility", SPELL_AGILITY, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE * 2, FALSE, TAR_CHAR_ROOM, 0, 0, cast_agility);

   SPELL_CREATE("summon", SPELL_SUMMON, SPELLTYPE_SUMMONING, PULSE_VIOLENCE * 2, FALSE, TAR_CHAR_WORLD, 0, 0, cast_summon);
   SPELL_ADD(CLASS_SHAMAN, 5, BASE);
   SPELL_ADD(CLASS_CLERIC, 4, BASE);
   SPELL_ADD(CLASS_DRUID, 4, BASE);

   SPELL_CREATE("ventriloquate", SPELL_VENTRILOQUATE, SPELLTYPE_GENERIC, 0, FALSE, TAR_CHAR_ROOM | TAR_OBJ_ROOM | TAR_SELF_NONO, 0, 0, cast_ventriloquate);
   SPELL_ADD(CLASS_NECROMANCER, 1, BASE);
   SPELL_ADD(CLASS_LICH, 1, BASE);
   SPELL_ADD(CLASS_SORCERER, 1, BASE);
   SPELL_ADD(CLASS_CONJURER, 1, BASE);

   SPELL_CREATE("haste", SPELL_HASTE, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE * 3 / 2, TRUE, TAR_CHAR_ROOM, 0, 0, cast_haste);
   SPELL_ADD(CLASS_SORCERER, 5, BASE);
   SPELL_ADD(CLASS_CONJURER, 7, BASE);
   SPELL_ADD(CLASS_NECROMANCER, 7, BASE);
   SPELL_ADD(CLASS_LICH, 7, BASE);
   SPELL_ADD(CLASS_ENCHANTER, 4, BASE);
   SPELL_ADD(CLASS_ELEMENTALIST, 7, BASE);


   SPELL_CREATE("word of recall", SPELL_WORD_OF_RECALL, SPELLTYPE_TELEPORTATION, 0, TRUE, TAR_SELF_ONLY, 0, 0, cast_word_of_recall);
   SPELL_ADD(CLASS_SHAMAN, 3, BASE);
   SPELL_ADD(CLASS_CLERIC, 2, BASE);
   SPELL_ADD(CLASS_DRUID, 2, BASE);

   SPELL_CREATE("remove poison", SPELL_REMOVE_POISON, SPELLTYPE_HEALING, PULSE_VIOLENCE * 2, TRUE, TAR_CHAR_ROOM | TAR_OBJ_INV | TAR_OBJ_ROOM, 0, 0, cast_remove_poison);
   SPELL_ADD(CLASS_CLERIC, 4, BASE);
   /*  SPELL_ADD(CLASS_DRUID, 2, BASE); */
   SPELL_ADD(CLASS_SHAMAN, 3, BASE);
   SPELL_ADD(CLASS_PALADIN, 5, BASE);
   /*  SPELL_ADD(CLASS_RANGER, 3, BASE); */

   SPELL_CREATE("minor paralysis", SPELL_MINOR_PARALYSIS, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT, 1, 0, cast_minor_paralysis);
   SPELL_ADD(CLASS_SHAMAN, 4, BASE);
   SPELL_ADD(CLASS_SORCERER, 4, BASE);
   SPELL_ADD(CLASS_RANGER, 8, BASE);
   SPELL_ADD(CLASS_ENCHANTER, 4, BASE);
   SPELL_ADD(CLASS_INVOKER, 4, BASE);
   SPELL_ADD(CLASS_ANTIPALADIN, 7, BASE);
   SPELL_ADD(CLASS_DIRERAIDER, 8, BASE);

   SPELL_CREATE("major paralysis", SPELL_MAJOR_PARALYSIS, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE * 3, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT, 1, 0, cast_major_paralysis);
   SPELL_ADD(CLASS_SORCERER, 8, BASE);
   SPELL_ADD(CLASS_ENCHANTER, 8, BASE);
   SPELL_ADD(CLASS_INVOKER, 8, BASE);

   SPELL_CREATE("slowness", SPELL_SLOW, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE * 2, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT, 1, 0, cast_slow);
   SPELL_ADD(CLASS_SHAMAN, 5, BASE);
   SPELL_ADD(CLASS_SORCERER, 5, BASE);
   SPELL_ADD(CLASS_ENCHANTER, 5, BASE);
   SPELL_ADD(CLASS_INVOKER, 5, BASE);

   SPELL_CREATE("conjure elemental", SPELL_CONJURE_ELEMENTAL, SPELLTYPE_ELEMENTAL, PULSE_VIOLENCE * 4, FALSE, TAR_IGNORE, 0, 0, cast_conjure_elemental);
   /*  SPELL_ADD(CLASS_DRUID, 6, BASE); */
   SPELL_ADD(CLASS_CONJURER, 5, BASE);
   SPELL_ADD(CLASS_ELEMENTALIST, 5, BASE);
   /*  SPELL_ADD(CLASS_SHAMAN, 6, BASE); */

   SPELL_CREATE("sense life", SPELL_SENSE_LIFE, SPELLTYPE_DIVINATION, PULSE_VIOLENCE * 2, FALSE, TAR_SELF_ONLY, 0, 0, cast_sense_life);
   SPELL_ADD(CLASS_SHAMAN, 3, BASE);
   SPELL_ADD(CLASS_CLERIC, 3, BASE);
   SPELL_ADD(CLASS_DRUID, 4, BASE);
   SPELL_ADD(CLASS_RANGER, 2, BASE);
   SPELL_ADD(CLASS_DIRERAIDER, 2, BASE);

   SPELL_CREATE("continual light", SPELL_CONTINUAL_LIGHT, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE * 3, TRUE, TAR_IGNORE, 0, 0, cast_continual_light);
   SPELL_ADD(CLASS_SHAMAN, 6, BASE);
   SPELL_ADD(CLASS_CLERIC, 6, BASE);
   SPELL_ADD(CLASS_DRUID, 6, BASE);
   SPELL_ADD(CLASS_PALADIN, 8, BASE);

   SPELL_CREATE("darkness", SPELL_DARKNESS, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE * 2, TRUE, TAR_IGNORE, 0, 0, cast_darkness);
   /*  SPELL_ADD(CLASS_DRUID, 4, BASE); */
   SPELL_ADD(CLASS_CLERIC, 6, BASE);
   SPELL_ADD(CLASS_SHAMAN, 7, BASE);

   /* these are 'relatively' minor enchancements. I still cannot fathom
      why their lvl was so high, or their mana cost so steep. -Torm */
   /* Because they HALVE damage from those types of spells!  minor, my ass :P  - CRM */

   SPELL_CREATE("protection from fire", SPELL_PROTECT_FROM_FIRE, SPELLTYPE_PROTECTION, PULSE_VIOLENCE, TRUE, TAR_CHAR_ROOM, 0, 0, cast_protection_from_fire);
   SPELL_ADD(CLASS_CLERIC, 4, BASE);
   SPELL_ADD(CLASS_SHAMAN, 5, BASE);
   SPELL_ADD(CLASS_DRUID, 3, BASE);

   SPELL_CREATE("protection from cold", SPELL_PROTECT_FROM_COLD, SPELLTYPE_PROTECTION, PULSE_VIOLENCE, TRUE, TAR_CHAR_ROOM, 0, 0, cast_protection_from_cold);
   SPELL_ADD(CLASS_CLERIC, 4, BASE);
   SPELL_ADD(CLASS_SHAMAN, 5, BASE);
   SPELL_ADD(CLASS_DRUID, 3, BASE);
   SPELL_ADD(CLASS_NECROMANCER, 2, BASE);
   SPELL_ADD(CLASS_LICH, 2, BASE);

   SPELL_CREATE("protection from gas", SPELL_PROTECT_FROM_GAS, SPELLTYPE_PROTECTION, PULSE_VIOLENCE, TRUE, TAR_CHAR_ROOM, 0, 0, cast_protection_from_gas);
   SPELL_ADD(CLASS_CLERIC, 4, BASE);
   SPELL_ADD(CLASS_SHAMAN, 5, BASE);
   SPELL_ADD(CLASS_DRUID, 3, BASE);

   SPELL_CREATE("protection from acid", SPELL_PROTECT_FROM_ACID, SPELLTYPE_PROTECTION, PULSE_VIOLENCE, TRUE, TAR_CHAR_ROOM, 0, 0, cast_protection_from_acid);
   SPELL_ADD(CLASS_CLERIC, 4, BASE);
   SPELL_ADD(CLASS_SHAMAN, 5, BASE);
   SPELL_ADD(CLASS_DRUID, 3, BASE);

   SPELL_CREATE("protection from lightning", SPELL_PROTECT_FROM_LIGHTNING, SPELLTYPE_PROTECTION, PULSE_VIOLENCE, TRUE, TAR_CHAR_ROOM, 0, 0, cast_protection_from_lightning);
   SPELL_ADD(CLASS_CLERIC, 4, BASE);
   SPELL_ADD(CLASS_SHAMAN, 5, BASE);
   SPELL_ADD(CLASS_DRUID, 3, BASE);

   SPELL_CREATE("levitate", SPELL_LEVITATE, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE, FALSE, TAR_CHAR_ROOM, 0, 0, cast_levitate);
   SPELL_ADD(CLASS_NECROMANCER, 5, BASE);
   SPELL_ADD(CLASS_LICH, 5, BASE);
   SPELL_ADD(CLASS_SORCERER, 4, BASE);
   SPELL_ADD(CLASS_CONJURER, 4, BASE);
   SPELL_ADD(CLASS_ENCHANTER, 4, BASE);
   SPELL_ADD(CLASS_INVOKER, 4, BASE);
   SPELL_ADD(CLASS_ILLUSIONIST, 4, BASE);
   SPELL_ADD(CLASS_ELEMENTALIST, 4, BASE);
#ifdef NEW_BARD
   SPELL_ADD(CLASS_BARD, 6, BASE);
#endif

   SPELL_CREATE("fly", SPELL_FLY, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE * 2, FALSE, TAR_CHAR_ROOM, 0, 0, cast_fly);
   SPELL_ADD(CLASS_SORCERER, 7, BASE);
   SPELL_ADD(CLASS_ENCHANTER, 7, BASE);

   SPELL_CREATE("identify", SPELL_IDENTIFY, SPELLTYPE_DIVINATION, PULSE_VIOLENCE * 5, FALSE, TAR_CHAR_ROOM | TAR_OBJ_INV | TAR_OBJ_EQUIP | TAR_OBJ_ROOM, 0, 0, cast_identify);
   SPELL_ADD(CLASS_CONJURER, 6, BASE);
   SPELL_ADD(CLASS_ENCHANTER, 6, BASE);

   SPELL_CREATE("prismatic spray", SPELL_PRISMATIC_SPRAY, SPELLTYPE_INVOCATION, PULSE_VIOLENCE * 2, TRUE, TAR_IGNORE, 1, 0, cast_prismatic_spray);
   SPELL_ADD(CLASS_CONJURER, 8, BASE);
   SPELL_ADD(CLASS_SORCERER, 7, BASE);
   SPELL_ADD(CLASS_ENCHANTER, 7, BASE);

   SPELL_CREATE("fireshield", SPELL_FIRESHIELD, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE * 3 / 2, TRUE, TAR_IGNORE, 0, 0, cast_fireshield);
   SPELL_ADD(CLASS_CONJURER, 5, BASE);
   SPELL_ADD(CLASS_SORCERER, 5, BASE);
   SPELL_ADD(CLASS_ENCHANTER, 5, BASE);
   SPELL_ADD(CLASS_INVOKER, 5, BASE);
   SPELL_ADD(CLASS_ELEMENTALIST, 5, BASE);

   /* This spell is henceforth retired.  Why, you ask?  Because it's lame.    */
   /* Nay, beyond lame.  Tis utterly 'putzish'.  Good spell for Sesame Street.*/
   /* "Noooo Big Bird!  Don't spray me with your crayons!" sez Snuffiluffigus.*/
   /* - CRM      */
#if 0
   SPELL_CREATE("color spray", SPELL_COLOR_SPRAY, SPELLTYPE_INVOCATION, PULSE_VIOLENCE * 5 / 3, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT, 1, 4, cast_color_spray);
   SPELL_ADD(CLASS_SORCERER, 6, BASE);
   SPELL_ADD(CLASS_CONJURER, 6, BASE);
#endif

   SPELL_CREATE("incendiary cloud", SPELL_INCENDIARY_CLOUD, SPELLTYPE_INVOCATION, PULSE_VIOLENCE * 3, TRUE, TAR_IGNORE, 1, 15, cast_incendiary_cloud);
   SPELL_ADD(CLASS_SORCERER, 8, BASE);
   SPELL_ADD(CLASS_INVOKER, 7, BASE);

   SPELL_CREATE("ice storm", SPELL_ICE_STORM, SPELLTYPE_INVOCATION, PULSE_VIOLENCE * 3 / 2, TRUE, TAR_IGNORE, 1, 7, cast_ice_storm);
   SPELL_ADD(CLASS_LICH, 5, BASE);
   SPELL_ADD(CLASS_NECROMANCER, 5, BASE);
   SPELL_ADD(CLASS_SORCERER, 6, BASE);
   SPELL_ADD(CLASS_CONJURER, 6, BASE);
   SPELL_ADD(CLASS_INVOKER, 5, BASE);

   /* in new system, this one's fairly wicked too - as bad dam as
      bigby, about, yet it disintegrates stuff too - awesome PK weapon,
      and last word in conj spells. */

   /* Fairly wicked, my puckered asshole. Will change it soon.  - CRM */
   /* Gives new meaning to "french kiss".  Those damned, dirty French. - DMB */

   SPELL_CREATE("disintegrate", SPELL_DISINTEGRATE, SPELLTYPE_INVOCATION, PULSE_VIOLENCE * 2, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 15, cast_disintegrate);
   SPELL_ADD(CLASS_SORCERER, 9, BASE);
   SPELL_ADD(CLASS_CONJURER, 10, BASE);
   SPELL_ADD(CLASS_INVOKER, 8, BASE);

   SPELL_CREATE("acid blast", SPELL_ACID_BLAST, SPELLTYPE_INVOCATION, PULSE_VIOLENCE, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 5, cast_acid_blast);
   SPELL_ADD(CLASS_SORCERER, 3, BASE);
   SPELL_ADD(CLASS_INVOKER, 2, BASE);

   SPELL_CREATE("acid bolt", SPELL_ACID_BOLT, SPELLTYPE_INVOCATION, PULSE_VIOLENCE * 3 / 2, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 16, cast_acid_bolt);
   SPELL_ADD(CLASS_INVOKER, 9, LEARN);

   SPELL_CREATE("faerie fire", SPELL_FAERIE_FIRE, SPELLTYPE_ILLUSION, PULSE_VIOLENCE * 3 / 2, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 0, cast_faerie_fire);
   SPELL_ADD(CLASS_SORCERER, 2, BASE);
   SPELL_ADD(CLASS_RANGER, 3, BASE);
   SPELL_ADD(CLASS_SHAMAN, 4, BASE);
   SPELL_ADD(CLASS_DRUID, 2, BASE);
   SPELL_ADD(CLASS_ENCHANTER, 2, BASE);
   SPELL_ADD(CLASS_INVOKER, 2, BASE);
   SPELL_ADD(CLASS_ILLUSIONIST, 2, BASE);
#ifdef NEW_BARD
   SPELL_ADD(CLASS_BATTLECHANTER, 4, BASE);
   SPELL_ADD(CLASS_BARD, 4, BASE);
#endif
   SPELL_ADD(CLASS_DIRERAIDER, 3, BASE);

   SPELL_CREATE("faerie fog", SPELL_FAERIE_FOG, SPELLTYPE_DIVINATION, PULSE_VIOLENCE * 2, FALSE, TAR_IGNORE, 0, 0, cast_faerie_fog);
   SPELL_ADD(CLASS_CONJURER, 4, BASE);
   SPELL_ADD(CLASS_DRUID, 3, BASE);
   SPELL_ADD(CLASS_SHAMAN, 5, BASE);
   SPELL_ADD(CLASS_RANGER, 4, BASE);
   SPELL_ADD(CLASS_ELEMENTALIST, 4, BASE);

   SPELL_CREATE("power word kill", SPELL_PWORD_KILL, SPELLTYPE_GENERIC, 0, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 0, cast_pword_kill);
   SPELL_ADD(CLASS_SORCERER, 8, BASE);
   SPELL_ADD(CLASS_INVOKER, 8, BASE);

   SPELL_CREATE("power word blind", SPELL_PWORD_BLIND, SPELLTYPE_GENERIC, 0, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 0, cast_pword_blind);
   SPELL_ADD(CLASS_SORCERER, 7, BASE);
   SPELL_ADD(CLASS_CONJURER, 8, BASE);
   SPELL_ADD(CLASS_ENCHANTER, 7, BASE);
   SPELL_ADD(CLASS_ELEMENTALIST, 8, BASE);

   SPELL_CREATE("power word stun", SPELL_PWORD_STUN, SPELLTYPE_GENERIC, 0, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 0, cast_pword_stun);
   SPELL_ADD(CLASS_SORCERER, 7, BASE);
   SPELL_ADD(CLASS_CONJURER, 8, BASE);
   SPELL_ADD(CLASS_INVOKER, 7, BASE);
   SPELL_ADD(CLASS_ELEMENTALIST, 8, BASE);

   /* Paladins get their equivalent at a lot lower level, because
      they are relatively less powerful class. */
   /* That almost makes sense.  Too bad it's untrue.  - CRM */

   SPELL_CREATE("unholy word", SPELL_UNHOLY_WORD, SPELLTYPE_INVOCATION, 0, TRUE, TAR_IGNORE, 1, 13, cast_unholy_word);
   SPELL_ADD(CLASS_CLERIC, 7, BASE);
   SPELL_ADD(CLASS_ANTIPALADIN, 10, BASE);

   SPELL_CREATE("holy word", SPELL_HOLY_WORD, SPELLTYPE_INVOCATION, 0, TRUE, TAR_IGNORE, 1, 13, cast_holy_word);
   SPELL_ADD(CLASS_CLERIC, 7, BASE);
   SPELL_ADD(CLASS_PALADIN, 10, BASE);


   SPELL_CREATE("sunray", SPELL_SUNRAY, SPELLTYPE_NATURE, PULSE_VIOLENCE, TRUE, TAR_FIGHT_VICT | TAR_CHAR_ROOM | TAR_SELF_NONO, 1, 3, cast_sunray);
   SPELL_ADD(CLASS_DRUID, 5, BASE);

   SPELL_CREATE("feeblemind", SPELL_FEEBLEMIND, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE * 2, TRUE, TAR_FIGHT_VICT | TAR_CHAR_ROOM | TAR_SELF_NONO, 1, 0, cast_feeblemind);
   SPELL_ADD(CLASS_NECROMANCER, 5, BASE);
   SPELL_ADD(CLASS_LICH, 5, BASE);
   SPELL_ADD(CLASS_SORCERER, 6, BASE);
   SPELL_ADD(CLASS_CONJURER, 6, BASE);
   SPELL_ADD(CLASS_ENCHANTER, 6, BASE);
   SPELL_ADD(CLASS_ELEMENTALIST, 6, BASE);
#ifdef NEW_BARD
   SPELL_ADD(CLASS_BARD, 8, BASE);
#endif

   SPELL_CREATE("silence", SPELL_SILENCE, SPELLTYPE_GENERIC, PULSE_VIOLENCE * 2, TRUE, TAR_IGNORE, 0, 0, cast_silence);
   /*  SPELL_ADD(CLASS_CLERIC, 6, BASE); */
   /*  SPELL_ADD(CLASS_DRUID, 6, BASE); */
   /*  SPELL_ADD(CLASS_SHAMAN, 7, BASE); */

   SPELL_CREATE("turn undead", SPELL_TURN_UNDEAD, SPELLTYPE_GENERIC, PULSE_VIOLENCE, TRUE, TAR_IGNORE, 1, 0, cast_turn_undead);
   SPELL_ADD(CLASS_CLERIC, 1, BASE);
   SPELL_ADD(CLASS_PALADIN, 2, BASE);
   SPELL_ADD(CLASS_SHAMAN, 3, BASE);

   SPELL_CREATE("command undead", SPELL_COMMAND_UNDEAD, SPELLTYPE_GENERIC, PULSE_VIOLENCE * 2, TRUE, TAR_CHAR_ROOM | TAR_SELF_NONO, 0, 0, cast_command_undead);
   SPELL_ADD(CLASS_NECROMANCER, 2, BASE);
   SPELL_ADD(CLASS_LICH, 2, BASE);
   SPELL_ADD(CLASS_ANTIPALADIN, 2, BASE);
   SPELL_ADD(CLASS_CLERIC, 3, BASE);
   SPELL_ADD(CLASS_SHAMAN, 5, BASE);
   SPELL_ADD(CLASS_DIRERAIDER, 3, BASE);

   SPELL_CREATE("group heal", SPELL_GROUP_HEAL, SPELLTYPE_HEALING, PULSE_VIOLENCE * 3 / 2, TRUE, TAR_IGNORE, 0, 0, cast_group_heal);
   SPELL_ADD(CLASS_SHAMAN, 9, LEARN);

   SPELL_CREATE("divine blessing", SPELL_DIVINE_BLESSING, SPELLTYPE_PROTECTION, PULSE_VIOLENCE * 3 / 2, TRUE, TAR_IGNORE, 0, 0, cast_divine_blessing);
   SPELL_ADD(CLASS_CLERIC, 9, BASE);

   SPELL_CREATE("group barkskin", SPELL_GROUP_BARKSKIN, SPELLTYPE_PROTECTION, 0, TRUE, TAR_IGNORE, 0, 0, cast_group_barkskin);

   /* I think not.  Not ever. :P  - CRM */
   SPELL_CREATE("group full heal", SPELL_GROUP_FULL_HEAL, SPELLTYPE_HEALING, 0, TRUE, TAR_IGNORE, 0, 0, cast_group_full_heal);
#if 0
   SPELL_ADD(CLASS_SHAMAN, 10, LEARN);
#endif

   /* hmm: I wonder why someone _always_ disables stuff of mine without
      telling me - grumble. -Torm */
   /* Cause your stuff sucks dude.  Sorry to be the one to break it to ya.  - CRM */

   SPELL_CREATE("comprehend languages", SPELL_COMPREHEND_LANGUAGES, SPELLTYPE_DIVINATION, PULSE_VIOLENCE * 2, FALSE, TAR_CHAR_ROOM, 0, 0, cast_comprehend_languages);
#if 0
   SPELL_ADD(CLASS_SORCERER, 6, LEARN);
#endif

   SPELL_CREATE("slow poison", SPELL_SLOW_POISON, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE, TRUE, TAR_CHAR_ROOM, 0, 0, cast_slow_poison);
   SPELL_ADD(CLASS_CLERIC, 2, BASE);

   SPELL_CREATE("coldshield", SPELL_COLDSHIELD, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE * 3 / 2, TRUE, TAR_IGNORE, 0, 0, cast_coldshield);
   SPELL_ADD(CLASS_CONJURER, 5, BASE);
   SPELL_ADD(CLASS_NECROMANCER, 5, BASE);
   SPELL_ADD(CLASS_LICH, 5, BASE);
   SPELL_ADD(CLASS_ENCHANTER, 5, BASE);
   SPELL_ADD(CLASS_INVOKER, 5, BASE);
   SPELL_ADD(CLASS_ELEMENTALIST, 5, BASE);

   SPELL_CREATE("globe of invulnerability", SPELL_GLOBE, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE * 2, TRUE, TAR_CHAR_ROOM, 0, 0, cast_globe);
   SPELL_ADD(CLASS_NECROMANCER, 9, BASE);
   SPELL_ADD(CLASS_LICH, 9, BASE);
   SPELL_ADD(CLASS_SORCERER, 8, LEARN);
   SPELL_ADD(CLASS_ENCHANTER, 8, BASE);

   SPELL_CREATE("vampiric touch", SPELL_VAMPIRIC_TOUCH, SPELLTYPE_GENERIC, PULSE_VIOLENCE * 2, FALSE, TAR_SELF_ONLY, 0, 0, cast_vampiric_touch);
   SPELL_ADD(CLASS_LICH, 6, BASE);
   SPELL_ADD(CLASS_NECROMANCER, 6, BASE);

   SPELL_CREATE("protect undead", SPELL_PROT_UNDEAD, SPELLTYPE_PROTECTION, PULSE_VIOLENCE * 2, FALSE, TAR_CHAR_ROOM, 0, 0, cast_prot_undead);
   SPELL_ADD(CLASS_LICH, 7, BASE);
   SPELL_ADD(CLASS_NECROMANCER, 7, BASE);

   SPELL_CREATE("protection from undead", SPELL_PROT_FROM_UNDEAD, SPELLTYPE_PROTECTION, PULSE_VIOLENCE * 2, FALSE, TAR_CHAR_ROOM, 0, 0, cast_prot_from_undead);
   SPELL_ADD(CLASS_LICH, 2, BASE);
   SPELL_ADD(CLASS_NECROMANCER, 2, BASE);

   SPELL_CREATE("command horde", SPELL_COMMAND_HORDE, SPELLTYPE_GENERIC, 0, TRUE, TAR_IGNORE, 0, 0, cast_command_horde);
   SPELL_ADD(CLASS_LICH, 4, BASE);
   SPELL_ADD(CLASS_NECROMANCER, 4, BASE);

   SPELL_CREATE("heal undead", SPELL_HEAL_UNDEAD, SPELLTYPE_HEALING, PULSE_VIOLENCE * 2, TRUE, TAR_CHAR_ROOM, 0, 0, cast_heal_undead);
   SPELL_ADD(CLASS_LICH, 5, BASE);
   SPELL_ADD(CLASS_NECROMANCER, 5, BASE);

   SPELL_CREATE("create spring", SPELL_CREATE_SPRING, SPELLTYPE_NATURE, PULSE_VIOLENCE * 5, TRUE, TAR_IGNORE, 0, 0, cast_create_spring);
   SPELL_ADD(CLASS_DRUID, 4, BASE);
   SPELL_ADD(CLASS_RANGER, 5, BASE);

   SPELL_CREATE("barkskin", SPELL_BARKSKIN, SPELLTYPE_NATURE, PULSE_VIOLENCE, TRUE, TAR_CHAR_ROOM, 0, 0, cast_barkskin);
   SPELL_ADD(CLASS_DRUID, 3, BASE);
   SPELL_ADD(CLASS_RANGER, 6, BASE);
   SPELL_ADD(CLASS_DIRERAIDER, 7, BASE);

   SPELL_CREATE("moonwell", SPELL_MOONWELL, SPELLTYPE_TELEPORTATION, PULSE_VIOLENCE * 10, FALSE, TAR_CHAR_WORLD, 0, 0, cast_moonwell);
   SPELL_ADD(CLASS_DRUID, 10, LEARN);

   /*** NEW SPELLS - CRM ***/

   /* Enchantment */

   SPELL_CREATE("mage flame", SPELL_MAGE_FLAME, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE * 3 / 2, FALSE, TAR_CHAR_ROOM, 0, 0, cast_mage_flame);
   SPELL_ADD(CLASS_ENCHANTER, 1, BASE);
   SPELL_ADD(CLASS_INVOKER, 1, BASE);
   SPELL_ADD(CLASS_CONJURER, 1, BASE);
   SPELL_ADD(CLASS_NECROMANCER, 1, BASE);
   SPELL_ADD(CLASS_ILLUSIONIST, 1, BASE);
   SPELL_ADD(CLASS_ELEMENTALIST, 1, BASE);
   SPELL_ADD(CLASS_LICH, 1, BASE);

   SPELL_CREATE("blur", SPELL_BLUR, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE * 3 / 2, TRUE, /* TAR_SELF_ONLY */ TAR_CHAR_ROOM, 0, 0, cast_blur);
   SPELL_ADD(CLASS_ENCHANTER, 6, BASE);

   SPELL_CREATE("constriction", SPELL_CONSTRICTION, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE * 3 / 2, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 15, cast_constriction);
   SPELL_ADD(CLASS_ENCHANTER, 9, BASE);

   SPELL_CREATE("repulsion", SPELL_REPULSION, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE * 3 / 2, TRUE, TAR_SELF_ONLY, 0, 0, cast_repulsion);
   SPELL_ADD(CLASS_ENCHANTER, 7, BASE);

   SPELL_CREATE("airy water", SPELL_AIRY_WATER, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE * 2, TRUE, TAR_IGNORE, 0, 0, cast_airy_water);
   SPELL_ADD(CLASS_ENCHANTER, 8, BASE);

   SPELL_CREATE("blink", SPELL_BLINK, SPELLTYPE_TELEPORTATION, PULSE_VIOLENCE, TRUE, TAR_CHAR_ROOM, 0, 0, cast_blink);
   SPELL_ADD(CLASS_ENCHANTER, 3, BASE);

   SPELL_CREATE("reduce", SPELL_REDUCE, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE * 3 / 2, TRUE, TAR_CHAR_ROOM, 0, 0, cast_reduce);
   SPELL_ADD(CLASS_ENCHANTER, 7, BASE);

   SPELL_CREATE("enlarge", SPELL_ENLARGE, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE * 3 / 2, TRUE, TAR_CHAR_ROOM, 0, 0, cast_enlarge);
   SPELL_ADD(CLASS_ENCHANTER, 7, BASE);

   SPELL_CREATE("mind blank", SPELL_MIND_BLANK, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE * 3, TRUE, TAR_CHAR_ROOM, 0, 0, cast_mind_blank);
   SPELL_ADD(CLASS_ENCHANTER, 9, LEARN);

   SPELL_CREATE("solid fog", SPELL_SOLID_FOG, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE * 2, TRUE, TAR_IGNORE, 0, 0, cast_solid_fog);
   SPELL_ADD(CLASS_ENCHANTER, 7, BASE);

   SPELL_CREATE("dragonscales", SPELL_DRAGONSCALES, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE * 4 / 3, TRUE, TAR_CHAR_ROOM, 0, 0, cast_dragonscales);
   SPELL_ADD(CLASS_ENCHANTER, 10, LEARN);

   SPELL_CREATE("time stop", SPELL_TIME_STOP, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE * 3, TRUE, TAR_SELF_ONLY, 0, 0, cast_time_stop);
   SPELL_ADD(CLASS_ENCHANTER, 10, LEARN);

   SPELL_CREATE("energy shield", SPELL_SHIELD, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE, TRUE, TAR_CHAR_ROOM, 0, 0, cast_shield);
   SPELL_ADD(CLASS_ENCHANTER, 2, BASE);

   SPELL_CREATE("greater thought", SPELL_GREATER_MIND, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE * 2, FALSE, TAR_CHAR_ROOM, 0, 0, cast_greater_mind);

#if 0
   SPELL_CREATE("aura of the griffon", SPELL_GRIFFON_AURA, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE * 5, TRUE, TAR_IGNORE, 0, 0, cast_griffon_aura);
   SPELL_ADD(CLASS_ENCHANTER, 10, BASE);
#endif

   /* Invocation */

   SPELL_CREATE("acidstorm", SPELL_ACIDSTORM, SPELLTYPE_INVOCATION, PULSE_VIOLENCE * 2, TRUE, TAR_IGNORE, 1, 15, cast_acidstorm);
   SPELL_ADD(CLASS_INVOKER, 9, LEARN);

   SPELL_CREATE("sandstorm", SPELL_SANDSTORM, SPELLTYPE_INVOCATION, PULSE_VIOLENCE * 3, TRUE, TAR_IGNORE, 1, 16, cast_sandstorm);
   SPELL_ADD(CLASS_INVOKER, 10, LEARN);

   SPELL_CREATE("inferno", SPELL_INFERNO, SPELLTYPE_INVOCATION, PULSE_VIOLENCE * 3, TRUE, TAR_IGNORE, 1, 18, cast_inferno);
   SPELL_ADD(CLASS_INVOKER, 10, LEARN);

   SPELL_CREATE("sandblast", SPELL_SANDBLAST, SPELLTYPE_INVOCATION,
                PULSE_VIOLENCE * 2, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 18, cast_sandblast);
   SPELL_ADD(CLASS_INVOKER, 10, BASE);

   SPELL_CREATE("blacklight burst", SPELL_BLACKLIGHT_BURST,SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE * 3 / 2, TRUE, TAR_IGNORE, 1, 10, cast_blacklight_burst);
   /* SPELL_ADD(CLASS_INVOKER, 8, BASE); */
   SPELL_ADD(CLASS_ENCHANTER, 8, BASE);

   SPELL_CREATE("thunderblast", SPELL_THUNDERBLAST, SPELLTYPE_INVOCATION, PULSE_VIOLENCE * 3 / 2, TRUE, TAR_IGNORE, 1, 11, cast_thunderblast);
   SPELL_ADD(CLASS_INVOKER, 8, BASE);

   SPELL_CREATE("blazing beam", SPELL_BLAZING_BEAM, SPELLTYPE_INVOCATION, PULSE_VIOLENCE * 4 / 3, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 13, cast_blazing_beam);
   SPELL_ADD(CLASS_INVOKER, 6, BASE);

   SPELL_CREATE("minute meteors", SPELL_MINUTE_METEORS, SPELLTYPE_INVOCATION, PULSE_VIOLENCE * 2 / 3, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 3, cast_minute_meteors);
   SPELL_ADD(CLASS_INVOKER, 3, BASE);

   SPELL_CREATE("mordenkainens sword", SPELL_MORDENKAINS_SWORD, SPELLTYPE_INVOCATION, PULSE_VIOLENCE * 2 / 3, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 11, cast_mordenkains_sword);
   SPELL_ADD(CLASS_INVOKER, 4, BASE);

   SPELL_CREATE("force missiles", SPELL_FORCE_MISSILES, SPELLTYPE_INVOCATION, PULSE_VIOLENCE * 3 / 2, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 13, cast_force_missiles);
   SPELL_ADD(CLASS_INVOKER, 8, BASE);

   SPELL_CREATE("conflagration", SPELL_CONFLAGRATION, SPELLTYPE_INVOCATION, PULSE_VIOLENCE * 3 / 2, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 20, cast_conflagration);
   SPELL_ADD(CLASS_INVOKER, 10, LEARN);

   SPELL_CREATE("fell frost", SPELL_FELL_FROST, SPELLTYPE_INVOCATION, PULSE_VIOLENCE * 3 / 2, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 16, cast_fell_frost);
   SPELL_ADD(CLASS_INVOKER, 9, BASE);

   /* CONJURATION */

   SPELL_CREATE("thunder lance", SPELL_THUNDER_LANCE, SPELLTYPE_ELEMENTAL, PULSE_VIOLENCE * 3 / 2, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 11, cast_thunder_lance);
   SPELL_ADD(CLASS_ELEMENTALIST, 5, BASE);

   SPELL_CREATE("needle swarm", SPELL_NEEDLE_SWARM, SPELLTYPE_SUMMONING, PULSE_VIOLENCE * 3 / 2, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 6, cast_needle_swarm);

   SPELL_CREATE("snapping teeth", SPELL_SNAPPING_TEETH, SPELLTYPE_SUMMONING, PULSE_VIOLENCE * 3 / 2, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 6, cast_snapping_teeth);

   SPELL_CREATE("minor horde", SPELL_MINOR_HORDE, SPELLTYPE_SUMMONING, PULSE_VIOLENCE * 2, TRUE, TAR_IGNORE, 1, 6, cast_minor_horde);

   SPELL_CREATE("grease", SPELL_GREASE, SPELLTYPE_SUMMONING, PULSE_VIOLENCE * 2, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT, 1, 6, cast_spell_stub);

   SPELL_CREATE("evards tentacles", SPELL_EVARDS_TENTACLES, SPELLTYPE_GENERIC, PULSE_VIOLENCE * 2, TRUE, TAR_IGNORE, 1, 6, cast_evards_tentacles);

   SPELL_CREATE("find familiar", SPELL_FIND_FAMILIAR, SPELLTYPE_SUMMONING, PULSE_VIOLENCE * 1, FALSE, TAR_IGNORE, 0, 0, cast_find_familiar);

   SPELL_CREATE("unseen servant", SPELL_UNSEEN_SERVANT, SPELLTYPE_SUMMONING, PULSE_VIOLENCE * 2, FALSE, TAR_IGNORE, 0, 0, cast_unseen_servant);

   SPELL_CREATE("monster summoning", SPELL_MONSTER_SUMMONING, SPELLTYPE_SUMMONING, PULSE_VIOLENCE * 2, FALSE, TAR_IGNORE, 0, 0, cast_monster_summoning);

   SPELL_CREATE("call lycanthrope", SPELL_CALL_LYCANTHROPE, SPELLTYPE_SUMMONING, PULSE_VIOLENCE * 2, FALSE, TAR_IGNORE, 0, 0, cast_call_lycanthrope);

   SPELL_CREATE("call mount", SPELL_SUMMON_MOUNT, SPELLTYPE_SUMMONING, PULSE_VIOLENCE * 2, FALSE, TAR_IGNORE, 0, 0, cast_summon_mount);

   SPELL_CREATE("control fiend", SPELL_CONTROL_FIEND, SPELLTYPE_SUMMONING, PULSE_VIOLENCE * 3, FALSE, TAR_IGNORE, 0, 0, cast_control_fiend);

   SKILL_CREATE("shadow", SKILL_SHADOW, SKILL_TYPE_PHYS_AGI);
   SKILL_ADD(CLASS_ASSASSIN, 20, 90, 16, BASE);
   SKILL_ADD(CLASS_ROGUE, 25, 95, 16, BASE);

   // Elemental
   SPELL_CREATE("elemental ward", SPELL_ELEMENTAL_WARD, SPELLTYPE_ELEMENTAL, PULSE_VIOLENCE * 2, TRUE, TAR_CHAR_ROOM, 0, 0, cast_elemental_ward);
   SPELL_ADD(CLASS_ELEMENTALIST, 10, BASE);

   SPELL_CREATE("lava burst", SPELL_LAVA_BURST, SPELLTYPE_ELEMENTAL, PULSE_VIOLENCE * 2, TRUE, TAR_IGNORE, 1, 14, cast_lava_burst);
   SPELL_ADD(CLASS_ELEMENTALIST, 10, BASE);

   SPELL_CREATE("earth darts", SPELL_EARTH_DARTS, SPELLTYPE_ELEMENTAL, PULSE_VIOLENCE * 2 / 3, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 1, cast_earth_darts);
   SPELL_ADD(CLASS_ELEMENTALIST, 1, BASE);

   SPELL_CREATE("air blast", SPELL_AIR_BLAST, SPELLTYPE_ELEMENTAL, PULSE_VIOLENCE, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 5, cast_air_blast);
   SPELL_ADD(CLASS_ELEMENTALIST, 3, BASE);

   SPELL_CREATE("blizzard sphere", SPELL_BLIZZARD_SPHERE, SPELLTYPE_ELEMENTAL, PULSE_VIOLENCE * 2, TRUE, TAR_IGNORE, 1, 10, cast_blizzard_sphere);
   SPELL_ADD(CLASS_ELEMENTALIST, 6, BASE);

   SPELL_CREATE("ice spear", SPELL_ICE_SPEAR, SPELLTYPE_ELEMENTAL, PULSE_VIOLENCE - 1, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 3, cast_ice_spear);
   SPELL_ADD(CLASS_ELEMENTALIST, 2, BASE);

   SPELL_CREATE("whirlwind", SPELL_WHIRLWIND, SPELLTYPE_ELEMENTAL,  PULSE_VIOLENCE * 2, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 15, cast_whirlwind);
   SPELL_ADD(CLASS_ELEMENTALIST, 8, BASE);

   SPELL_CREATE("ice tongue", SPELL_ICE_TONGUE, SPELLTYPE_ELEMENTAL,  PULSE_VIOLENCE, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 0, cast_ice_tongue);
   SPELL_ADD(CLASS_ELEMENTALIST, 7, BASE);

   SPELL_CREATE("earthblood", SPELL_EARTHBLOOD, SPELLTYPE_ELEMENTAL,  PULSE_VIOLENCE, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 16, cast_earthblood);
   SPELL_ADD(CLASS_ELEMENTALIST, 9, BASE);

   SPELL_CREATE("ice layer", SPELL_SLIPPERY_ICE, SPELLTYPE_ELEMENTAL,  PULSE_VIOLENCE, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 0, cast_slippery_ice);
   SPELL_ADD(CLASS_ELEMENTALIST, 7, BASE);

   SPELL_CREATE("glitterdust", SPELL_GLITTERDUST, SPELLTYPE_ELEMENTAL, PULSE_VIOLENCE * 3 / 2, TRUE, TAR_IGNORE, 1, 0, cast_glitterdust);
   SPELL_ADD(CLASS_ELEMENTALIST, 5, BASE);

   SPELL_CREATE("elemental water embodiment", SPELL_ELEMENTAL_WATER, SPELLTYPE_ELEMENTAL, PULSE_VIOLENCE * 4, TRUE, TAR_CHAR_ROOM, 0, 0, cast_elemental_water);
   SPELL_ADD(CLASS_ELEMENTALIST, 7, BASE);

   SPELL_CREATE("elemental fire embodiment", SPELL_ELEMENTAL_FIRE, SPELLTYPE_ELEMENTAL, PULSE_VIOLENCE * 4, TRUE, TAR_CHAR_ROOM, 0, 0, cast_elemental_fire);
   SPELL_ADD(CLASS_ELEMENTALIST, 10, LEARN);

   SPELL_CREATE("elemental earth embodiment", SPELL_ELEMENTAL_EARTH, SPELLTYPE_ELEMENTAL, PULSE_VIOLENCE * 4, TRUE, TAR_CHAR_ROOM, 0, 0, cast_elemental_earth);
   SPELL_ADD(CLASS_ELEMENTALIST, 9, LEARN);

   SPELL_CREATE("elemental air embodiment", SPELL_ELEMENTAL_AIR, SPELLTYPE_ELEMENTAL, PULSE_VIOLENCE * 4, TRUE, TAR_CHAR_ROOM, 0, 0, cast_elemental_air);
   SPELL_ADD(CLASS_ELEMENTALIST, 8, BASE);

   SPELL_CREATE("elemental embodiment maintain", SPELL_ELEMENTAL_MAINTAIN, SPELLTYPE_ELEMENTAL, PULSE_VIOLENCE * 4, TRUE, TAR_CHAR_ROOM, 0, 0, cast_spell_stub);
   SPELL_CREATE("elemental embodiment maintain", SPELL_CASTER_WATER_EMBODIMENT, SPELLTYPE_ELEMENTAL, PULSE_VIOLENCE * 4, TRUE, TAR_CHAR_ROOM, 0, 0, cast_spell_stub);
   SPELL_CREATE("elemental embodiment maintain", SPELL_CASTER_FIRE_EMBODIMENT, SPELLTYPE_ELEMENTAL, PULSE_VIOLENCE * 4, TRUE, TAR_CHAR_ROOM, 0, 0, cast_spell_stub);
   SPELL_CREATE("elemental embodiment maintain", SPELL_CASTER_EARTH_EMBODIMENT, SPELLTYPE_ELEMENTAL, PULSE_VIOLENCE * 4, TRUE, TAR_CHAR_ROOM, 0, 0, cast_spell_stub);
   SPELL_CREATE("elemental embodiment maintain", SPELL_CASTER_AIR_EMBODIMENT, SPELLTYPE_ELEMENTAL, PULSE_VIOLENCE * 4, TRUE, TAR_CHAR_ROOM, 0, 0, cast_spell_stub);

   SPELL_CREATE("summon elemental kin", SPELL_ELEMENTAL_KIN, SPELLTYPE_SUMMONING, PULSE_VIOLENCE * 2, TRUE, TAR_IGNORE, 0, 0, cast_elemental_kin);
   SPELL_ADD(CLASS_ELEMENTALIST, 3, BASE);

   SPELL_CREATE("firewave", SPELL_FIREWAVE, SPELLTYPE_ELEMENTAL, PULSE_VIOLENCE * 4 / 3, TRUE, TAR_IGNORE, 1, 12, cast_firewave);
   SPELL_ADD(CLASS_ELEMENTALIST, 8, BASE);

   SPELL_CREATE("icewave", SPELL_ICEWAVE, SPELLTYPE_ELEMENTAL, PULSE_VIOLENCE * 4 / 3, TRUE, TAR_IGNORE, 1, 12, cast_icewave);
   SPELL_ADD(CLASS_ELEMENTALIST, 8, BASE);

   SPELL_CREATE("earth fog", SPELL_EARTH_FOG, SPELLTYPE_ELEMENTAL, PULSE_VIOLENCE * 2, TRUE, TAR_IGNORE, 0, 0, cast_earth_fog);
   SPELL_ADD(CLASS_ELEMENTALIST, 8, BASE);

   SPELL_CREATE("fire fog", SPELL_FIRE_FOG, SPELLTYPE_ELEMENTAL, PULSE_VIOLENCE * 2, TRUE, TAR_IGNORE, 0, 0, cast_fire_fog);
   SPELL_ADD(CLASS_ELEMENTALIST, 8, BASE);

   /* Illusion */

   SPELL_CREATE("shadow bolt", SPELL_SHADOW_BOLT, SPELLTYPE_ILLUSION, PULSE_VIOLENCE * 2 / 3, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 1, cast_shadow_bolt);
   SPELL_ADD(CLASS_ILLUSIONIST, 1, BASE);

   SPELL_CREATE("phantasmal blades", SPELL_PHANTASMAL_BLADES, SPELLTYPE_ILLUSION, PULSE_VIOLENCE * 3, TRUE, TAR_IGNORE, 1, 14, cast_phantasmal_blades);
   SPELL_ADD(CLASS_ILLUSIONIST, 7, BASE);

   SPELL_CREATE("shadow burst", SPELL_SHADOW_BURST, SPELLTYPE_ILLUSION, PULSE_VIOLENCE * 3 / 2, TRUE, TAR_IGNORE, 1, 7, cast_shadow_burst);
   SPELL_ADD(CLASS_ILLUSIONIST, 5, BASE);

   SPELL_CREATE("mislead", SPELL_MISLEAD, SPELLTYPE_ILLUSION, PULSE_VIOLENCE * 3 / 2, FALSE, TAR_SELF_ONLY, 0, 1, cast_mislead);
   SPELL_ADD(CLASS_ILLUSIONIST, 8, BASE);

   SPELL_CREATE("phantom armor", SPELL_PHANTOM_ARMOR, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE, TRUE, TAR_CHAR_ROOM, 0, 0, cast_phantom_armor);
   SPELL_ADD(CLASS_ILLUSIONIST, 3, BASE);
#ifdef NEW_BARD
   SPELL_ADD(CLASS_BARD, 4, BASE);
#endif

   SPELL_CREATE("phantasmal killer", SPELL_PHANTASMAL_KILLER, SPELLTYPE_ILLUSION, PULSE_VIOLENCE * 3 / 2, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 16, cast_phantasmal_killer);
   SPELL_ADD(CLASS_ILLUSIONIST, 9, BASE);

   SPELL_CREATE("mirror image", SPELL_MIRROR_IMAGE, SPELLTYPE_ILLUSION, PULSE_VIOLENCE * 4 / 3, FALSE, TAR_SELF_ONLY, 0, 0, cast_mirror_image);
   SPELL_ADD(CLASS_ILLUSIONIST, 8, BASE);

   SPELL_CREATE("nondetection", SPELL_NONDETECTION, SPELLTYPE_ILLUSION, PULSE_VIOLENCE * 3 / 2, FALSE, TAR_CHAR_ROOM, 0, 0, cast_nondetection);
   SPELL_ADD(CLASS_ILLUSIONIST, 6, BASE);

   SPELL_CREATE("sequester", SPELL_SEQUESTER, SPELLTYPE_ILLUSION, PULSE_VIOLENCE * 4 / 3, FALSE, TAR_CHAR_ROOM, 0, 0, cast_sequester);
   SPELL_ADD(CLASS_ILLUSIONIST, 7, BASE);

   SPELL_CREATE("spook", SPELL_SPOOK, SPELLTYPE_ILLUSION, PULSE_VIOLENCE * 2 / 3, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 7, cast_spook);
   SPELL_ADD(CLASS_ILLUSIONIST, 4, BASE);

   SPELL_CREATE("dimension shift", SPELL_DIMENSION_SHIFT, SPELLTYPE_TELEPORTATION, PULSE_VIOLENCE * 3 / 2, TRUE, TAR_IGNORE, 0, 0, cast_dimension_shift);
   SPELL_ADD(CLASS_ILLUSIONIST, 10, LEARN);

   SPELL_CREATE("displacement", SPELL_DISPLACEMENT, SPELLTYPE_ILLUSION, PULSE_VIOLENCE * 4 / 3, TRUE, TAR_CHAR_ROOM, 0, 0, cast_displacement);
   SPELL_ADD(CLASS_ILLUSIONIST, 6, BASE);
#ifdef NEW_BARD
   SPELL_ADD(CLASS_BARD, 10, BASE);
#endif

   SPELL_CREATE("shadow magic", SPELL_SHADOW_MAGIC, SPELLTYPE_ILLUSION, PULSE_VIOLENCE * 4 / 3, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT, 1, 5, cast_shadow_magic);
   SPELL_ADD(CLASS_ILLUSIONIST, 5, BASE);

   SPELL_CREATE("shadow walk", SPELL_SHADOW_WALK, SPELLTYPE_TELEPORTATION, PULSE_VIOLENCE * 4 / 3, FALSE, TAR_CHAR_WORLD | TAR_SELF_NONO, 0, 0, cast_shadow_walk);
   SPELL_ADD(CLASS_ILLUSIONIST, 9, LEARN);

   SPELL_CREATE("phantom steed", SPELL_PHANTOM_STEED, SPELLTYPE_SUMMONING, PULSE_VIOLENCE * 4 / 3, FALSE, TAR_IGNORE, 0, 0, cast_phantom_steed);
   SPELL_ADD(CLASS_ILLUSIONIST, 4, BASE);

   SPELL_CREATE("change self", SPELL_CHANGE_SELF, SPELLTYPE_ILLUSION, PULSE_VIOLENCE * 4 / 3, FALSE, TAR_IGNORE, 0, 0, cast_change_self);
   SPELL_ADD(CLASS_ILLUSIONIST, 3, BASE);

   SPELL_CREATE("simulacrum", SPELL_SIMULACRUM, SPELLTYPE_ILLUSION, PULSE_VIOLENCE * 4 / 3, TRUE, TAR_IGNORE, 0, 0, cast_simulacrum);

   SPELL_CREATE("wraithform", SPELL_WRAITHFORM, SPELLTYPE_ILLUSION, PULSE_VIOLENCE * 5, FALSE, TAR_IGNORE, 0, 0, cast_wraithform);

   SPELL_CREATE("rainbow pattern", SPELL_RAINBOW_PATTERN, SPELLTYPE_ILLUSION, PULSE_VIOLENCE * 4 / 3, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 0, cast_rainbow_pattern);
   SPELL_ADD(CLASS_ILLUSIONIST, 8, BASE);

   SPELL_CREATE("blackthorns", SPELL_BLACKTHORNS, SPELLTYPE_ILLUSION, PULSE_VIOLENCE * 3 / 4, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 3, cast_blackthorns);
   SPELL_ADD(CLASS_ILLUSIONIST, 2, BASE);

   SPELL_CREATE("doppleganger", SPELL_DOPPLEGANGER, SPELLTYPE_ILLUSION, PULSE_VIOLENCE * 4 / 3, FALSE, TAR_CHAR_ROOM , 0, 0, cast_doppleganger);
   SPELL_ADD(CLASS_ILLUSIONIST, 5, BASE);

   SPELL_CREATE("true sight", SPELL_TRUE_SIGHT, SPELLTYPE_DIVINATION, PULSE_VIOLENCE * 4 / 3, TRUE, TAR_CHAR_ROOM, 0, 0, cast_true_sight);
   SPELL_ADD(CLASS_ILLUSIONIST, 5, BASE);

   SPELL_CREATE("feign death", SPELL_FEIGN_DEATH, SPELLTYPE_ILLUSION, PULSE_VIOLENCE * 3 / 2, TRUE, TAR_CHAR_ROOM, 0, 0, cast_feign_death);
   SPELL_ADD(CLASS_ILLUSIONIST, 8, BASE);

   SPELL_CREATE("camouflage", SPELL_CAMOFLAUGE, SPELLTYPE_ILLUSION, PULSE_VIOLENCE * 2 / 3, FALSE, TAR_SELF_ONLY, 0, 0, cast_camoflauge);
   SPELL_ADD(CLASS_ILLUSIONIST, 7, BASE);

   SPELL_CREATE("tranquility", SPELL_TRANQUILITY, SPELLTYPE_ILLUSION, PULSE_VIOLENCE * 2 / 3, TRUE, TAR_IGNORE, 0, 0, cast_tranquility);
   SPELL_ADD(CLASS_ILLUSIONIST, 6, BASE);

   SPELL_CREATE("scarlet outline", SPELL_SCARLET_OUTLINE, SPELLTYPE_ILLUSION, PULSE_VIOLENCE * 2 / 3, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO , 1, 0, cast_scarlet_outline);
   SPELL_ADD(CLASS_ILLUSIONIST, 4, BASE);

   SPELL_CREATE("phantom heal", SPELL_PHANTOM_HEAL, SPELLTYPE_ILLUSION, PULSE_VIOLENCE * 4 / 3, TRUE, TAR_CHAR_ROOM, 0, 0, cast_phantom_heal);
   SPELL_ADD(CLASS_ILLUSIONIST, 8, BASE);

   SPELL_CREATE("nightmare", SPELL_NIGHTMARE, SPELLTYPE_ILLUSION, PULSE_VIOLENCE * 4 / 3, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 18, cast_nightmare);
   SPELL_ADD(CLASS_ILLUSIONIST, 10, LEARN);

   SPELL_CREATE("massmorph", SPELL_MASSMORPH, SPELLTYPE_ILLUSION, PULSE_VIOLENCE * 4 / 3, FALSE, TAR_IGNORE, 0, 0, cast_massmorph);
   SPELL_ADD(CLASS_ILLUSIONIST, 9, BASE);

   SPELL_CREATE("summon shade", SPELL_SHADE, SPELLTYPE_ILLUSION, PULSE_VIOLENCE * 3, TRUE, TAR_IGNORE, 0, 0, cast_shade);
   SPELL_ADD(CLASS_ILLUSIONIST, 6, BASE);

   SPELL_CREATE("shadechill", SPELL_SHADECHILL, SPELLTYPE_ILLUSION, PULSE_VIOLENCE * 3 / 2, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 14, cast_shadechill);
   SPELL_ADD(CLASS_ILLUSIONIST, 7, BASE);

   SPELL_CREATE("dimensional fold", SPELL_DIMENSIONAL_FOLD, SPELLTYPE_ILLUSION, PULSE_VIOLENCE * 3 / 2, TRUE, TAR_CHAR_WORLD | TAR_SELF_NONO, 0, 0, cast_dimensional_fold);
   SPELL_ADD(CLASS_ILLUSIONIST, 8, BASE);

   SPELL_CREATE("shadow flux", SPELL_SHADOW_FLUX, SPELLTYPE_ILLUSION, PULSE_VIOLENCE * 4 / 3, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 0, cast_shadow_flux);
   SPELL_ADD(CLASS_ILLUSIONIST, 7, BASE);

   SPELL_CREATE("beautify", SPELL_BEAUTIFY, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE * 2, FALSE, TAR_CHAR_ROOM, 0, 0, cast_beautify);
   SPELL_ADD(CLASS_ILLUSIONIST, 4, BASE);

   SPELL_CREATE("phantasmal tendrils", SPELL_PHANTASMAL_TENDRIL, SPELLTYPE_ILLUSION, PULSE_VIOLENCE * 5 / 2, TRUE, TAR_IGNORE, 1, 17,  cast_phantasmal_tendril);
   SPELL_ADD(CLASS_ILLUSIONIST, 10, LEARN);

   SPELL_CREATE("corpse glamor", SPELL_CORPSE_GLAMOR, SPELLTYPE_ILLUSION, PULSE_VIOLENCE * 2 / 3, FALSE, TAR_OBJ_ROOM, 0, 0, cast_corpse_glamor);
   SPELL_ADD(CLASS_ILLUSIONIST, 7, BASE);
   SPELL_ADD(CLASS_LICH, 7, BASE);
   SPELL_ADD(CLASS_NECROMANCER, 7, BASE);

   SPELL_CREATE("sun shadow", SPELL_SUN_SHADOW, SPELLTYPE_ILLUSION, PULSE_VIOLENCE * 3, FALSE, TAR_IGNORE, 0, 0, cast_sun_shadow);
   SPELL_ADD(CLASS_ILLUSIONIST, 8, BASE);

   /* Necro Spells */

   SPELL_CREATE("pain touch", SPELL_PAIN_TOUCH, SPELLTYPE_NECROMANCY, PULSE_VIOLENCE * 4 / 3, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 5, cast_pain_touch);
   SPELL_ADD(CLASS_ANTIPALADIN, 3, BASE);
   SPELL_ADD(CLASS_NECROMANCER, 3, BASE);
   SPELL_ADD(CLASS_LICH, 3, BASE);

   SPELL_CREATE("spectral hand", SPELL_SPECTRAL_HAND, SPELLTYPE_NECROMANCY, PULSE_VIOLENCE * 4 / 3, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 7, cast_spectral_hand);
   SPELL_ADD(CLASS_ANTIPALADIN, 5, BASE);
   SPELL_ADD(CLASS_NECROMANCER, 4, BASE);
   SPELL_ADD(CLASS_LICH, 4, BASE);

   SPELL_CREATE("nerve dance", SPELL_NERVE_DANCE, SPELLTYPE_NECROMANCY, PULSE_VIOLENCE * 4 / 3, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 11, cast_nerve_dance);
#ifdef NEW_NECRO
   SPELL_ADD(CLASS_NECROMANCER, 6, BASE);
   SPELL_ADD(CLASS_LICH, 6, BASE);
#else
   SPELL_ADD(CLASS_NECROMANCER, 5, BASE);
   SPELL_ADD(CLASS_LICH, 5, BASE);
#endif

   SPELL_CREATE("rain of blood", SPELL_RAIN_OF_BLOOD, SPELLTYPE_NECROMANCY, PULSE_VIOLENCE * 2, TRUE, TAR_IGNORE, 1, 13, cast_rain_of_blood);
   SPELL_ADD(CLASS_NECROMANCER, 8, BASE);
   SPELL_ADD(CLASS_LICH, 8, BASE);

   /* Changed to 7th circle - Iyachtu */
   SPELL_CREATE("beltyns burning blood", SPELL_BELTYNS_BURNING_BLOOD, SPELLTYPE_NECROMANCY, PULSE_VIOLENCE * 5 / 3, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 15, cast_beltyns_burning_blood);
   SPELL_ADD(CLASS_NECROMANCER, 7, BASE);
   SPELL_ADD(CLASS_LICH, 7, BASE);

   SPELL_CREATE("abi dalzims horrid wilting", SPELL_ABI_DALZIMS_HORRID_WILTING, SPELLTYPE_NECROMANCY, PULSE_VIOLENCE * 2, TRUE, TAR_IGNORE, 1, 14, cast_abi_dalzims_horrid_wilting);
   SPELL_ADD(CLASS_NECROMANCER, 9, BASE);
   SPELL_ADD(CLASS_LICH, 9, BASE);

   SPELL_CREATE("blackmantle", SPELL_BLACKMANTLE, SPELLTYPE_NECROMANCY, PULSE_VIOLENCE * 4/3, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 0, cast_blackmantle);
   SPELL_ADD(CLASS_NECROMANCER, 8, BASE);
   SPELL_ADD(CLASS_LICH, 8, BASE);

   SPELL_CREATE("vampiric curse", SPELL_VAMPIRIC_CURSE, SPELLTYPE_NECROMANCY, PULSE_VIOLENCE * 4/3, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 0, cast_vampiric_curse);
   SPELL_ADD(CLASS_NECROMANCER, 9, LEARN);

#ifdef NEW_NECRO
   SPELL_CREATE("banshee wail", SPELL_BANSHEE_WAIL, SPELLTYPE_NECROMANCY, PULSE_VIOLENCE * 2, TRUE, TAR_IGNORE, 1, 0, cast_banshee_wail);
   SPELL_ADD(CLASS_NECROMANCER, 10, BASE);
   SPELL_ADD(CLASS_LICH, 10, BASE);

   SPELL_CREATE("death pact", SPELL_DEATH_PACT, SPELLTYPE_NECROMANCY, PULSE_VIOLENCE * 2, TRUE, TAR_IGNORE, 0, 0, cast_death_pact);
   SPELL_ADD(CLASS_LICH, 10, BASE);

   SPELL_CREATE("soul bind", SPELL_SOUL_BIND, SPELLTYPE_NECROMANCY, PULSE_VIOLENCE * 4/3, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT, 1, 0, cast_soul_bind);
   SPELL_ADD(CLASS_NECROMANCER, 3, BASE);
   SPELL_ADD(CLASS_LICH, 3, BASE);
#endif

   SPELL_CREATE("totem darts", SPELL_TOTEM_DARTS, SPELLTYPE_SPIRIT, PULSE_VIOLENCE * 2 / 3, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 1, cast_totem_darts);
   SPELL_ADD(CLASS_SHAMAN, 1, BASE);
   SPELL_ADD(CLASS_DIRERAIDER, 2, BASE);

   SPELL_CREATE("spiritknife", SPELL_SPIRIT_KNIFE, SPELLTYPE_SPIRIT, PULSE_VIOLENCE * 2 / 3, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 3, cast_spiritknife);
   SPELL_ADD(CLASS_SHAMAN, 2, BASE);

   SPELL_CREATE("jar the soul", SPELL_JAR_THE_SOUL, SPELLTYPE_SPIRIT,
                PULSE_VIOLENCE * 2 / 3, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT
                | TAR_SELF_NONO, 1, 5, cast_jar_the_soul);
   SPELL_ADD(CLASS_SHAMAN, 3, BASE);
   SPELL_ADD(CLASS_DIRERAIDER, 4, BASE);

   SPELL_CREATE("unleash fetish", SPELL_UNLEASH_FETISH, SPELLTYPE_SPIRIT,
                PULSE_VIOLENCE * 2 / 3, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT
                | TAR_SELF_NONO, 1, 7, cast_unleash_fetish);
   SPELL_ADD(CLASS_SHAMAN, 4, BASE);

   SPELL_CREATE("puppet", SPELL_PUPPET, SPELLTYPE_SPIRIT,
                PULSE_VIOLENCE * 4 / 3, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT
                | TAR_SELF_NONO, 1, 11, cast_puppet);
   SPELL_ADD(CLASS_SHAMAN, 5, BASE);

   SPELL_CREATE("hex", SPELL_HEX, SPELLTYPE_SPIRIT,
                PULSE_VIOLENCE * 3 / 2, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT
                | TAR_SELF_NONO, 1, 0, cast_hex);
   SPELL_ADD(CLASS_SHAMAN, 6, BASE);
#ifdef NEW_BARD
   SPELL_ADD(CLASS_BATTLECHANTER, 8, BASE);
#endif
   SPELL_ADD(CLASS_DIRERAIDER, 9, BASE);

   SPELL_CREATE("soul tempest", SPELL_SOUL_TEMPEST, SPELLTYPE_SPIRIT,
                PULSE_VIOLENCE * 2, TRUE, TAR_IGNORE, 1, 12, cast_soul_tempest);
   SPELL_ADD(CLASS_SHAMAN, 7, BASE);

   SPELL_CREATE("spirit wrack", SPELL_SPIRIT_WRACK, SPELLTYPE_SPIRIT,
                PULSE_VIOLENCE * 3 / 2, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT
                | TAR_SELF_NONO, 1, 14, cast_spirit_wrack);
   SPELL_ADD(CLASS_SHAMAN, 8, BASE);

   SPELL_CREATE("spirit walk", SPELL_SPIRIT_WALK, SPELLTYPE_SPIRIT,
                PULSE_VIOLENCE * 5, FALSE, TAR_CHAR_WORLD, 0, 0, cast_spirit_walk);
   SPELL_ADD(CLASS_SHAMAN, 9, LEARN);

   SPELL_CREATE("scry remains", SPELL_SCRY_REMAINS, SPELLTYPE_SPIRIT,
                PULSE_VIOLENCE * 5, FALSE, TAR_CHAR_WORLD, 0, 0, cast_scry_remains);
   SPELL_ADD(CLASS_SHAMAN, 8, LEARN);

   SPELL_CREATE("ancestral shield", SPELL_ANCESTRAL_SHIELD, SPELLTYPE_SPIRIT,
                PULSE_VIOLENCE * 3 / 2, TRUE, TAR_IGNORE, 0, 0, cast_ancestral_shield);
   SPELL_ADD(CLASS_SHAMAN, 10, LEARN);

   SPELL_CREATE("ancestral fury", SPELL_ANCESTRAL_FURY, SPELLTYPE_SPIRIT,
                PULSE_VIOLENCE * 2, TRUE, TAR_IGNORE, 1, 14, cast_ancestral_fury);
   SPELL_ADD(CLASS_SHAMAN, 10, BASE);

   SPELL_CREATE("goodberry", SPELL_GOODBERRY, SPELLTYPE_NATURE, PULSE_VIOLENCE, FALSE,
                TAR_IGNORE, 0, 0, cast_goodberry);
   SPELL_ADD(CLASS_DRUID, 1, BASE);
   SPELL_ADD(CLASS_RANGER, 2, BASE);

   SPELL_CREATE("shillelagh", SPELL_SHILLELAGH, SPELLTYPE_NATURE,
                PULSE_VIOLENCE * 2 / 3, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT
                | TAR_SELF_NONO, 1, 3, cast_shillelagh);
   SPELL_ADD(CLASS_DRUID, 1, BASE);
   SPELL_ADD(CLASS_RANGER, 1, BASE);

   SPELL_CREATE("protection from animals", SPELL_PROTECTION_FROM_ANIMALS,
                SPELLTYPE_NATURE, PULSE_VIOLENCE * 2, FALSE, TAR_CHAR_ROOM, 0, 0,
                cast_protection_from_animals);
   SPELL_ADD(CLASS_DRUID, 2, BASE);
   SPELL_ADD(CLASS_RANGER, 5, BASE);
   SPELL_ADD(CLASS_DIRERAIDER, 5, BASE);

   SPELL_CREATE("sticks to snakes", SPELL_STICKS_TO_SNAKES, SPELLTYPE_NATURE,
                PULSE_VIOLENCE * 2 / 3, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT
                | TAR_SELF_NONO, 1, 3, cast_sticks_to_snakes);
   SPELL_ADD(CLASS_DRUID, 2, BASE);
   SPELL_ADD(CLASS_RANGER, 3, BASE);

   SPELL_CREATE("summon insects", SPELL_SUMMON_INSECTS, SPELLTYPE_NATURE,
                PULSE_VIOLENCE * 2 / 3, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT
                | TAR_SELF_NONO, 1, 5, cast_summon_insects);
   SPELL_ADD(CLASS_DRUID, 3, BASE);
   SPELL_ADD(CLASS_RANGER, 4, BASE);

   SPELL_CREATE("dust devil", SPELL_DUST_DEVIL, SPELLTYPE_NATURE,
                PULSE_VIOLENCE * 2 / 3, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT
                | TAR_SELF_NONO, 1, 7, cast_dust_devil);
   SPELL_ADD(CLASS_DRUID, 4, BASE);
   SPELL_ADD(CLASS_RANGER, 6, BASE);
   SPELL_ADD(CLASS_DIRERAIDER, 6, BASE);

   SPELL_CREATE("transport via plants", SPELL_TRANSPORT_VIA_PLANTS, SPELLTYPE_NATURE,
                PULSE_VIOLENCE, TRUE, TAR_SELF_ONLY, 0, 0, cast_transport_via_plants);
   SPELL_ADD(CLASS_DRUID, 4, BASE);
   SPELL_ADD(CLASS_RANGER, 9, BASE);

   SPELL_CREATE("suffocate", SPELL_SUFFOCATE, SPELLTYPE_NATURE,
                PULSE_VIOLENCE * 3 / 2, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT
                | TAR_SELF_NONO, 1, 12, cast_suffocate);
   SPELL_ADD(CLASS_DRUID, 6, BASE);

   SPELL_CREATE("insect plague", SPELL_INSECT_PLAGUE, SPELLTYPE_NATURE,
                PULSE_VIOLENCE * 3 / 2, TRUE, TAR_IGNORE, 1, 11, cast_insect_plague);
   SPELL_ADD(CLASS_DRUID, 6, BASE);

   SPELL_CREATE("changestaff", SPELL_CHANGESTAFF, SPELLTYPE_NATURE, PULSE_VIOLENCE * 4,
                FALSE, TAR_IGNORE, 0, 0, cast_changestaff);
   SPELL_ADD(CLASS_DRUID, 6, BASE);

   SPELL_CREATE("pass without trace", SPELL_PASS_WITHOUT_TRACE, SPELLTYPE_NATURE,
                PULSE_VIOLENCE * 2 / 3, FALSE, TAR_IGNORE, 0, 0, cast_pass_without_trace);
   SPELL_ADD(CLASS_DRUID, 7, BASE);
   SPELL_ADD(CLASS_RANGER, 10, BASE);
   SPELL_ADD(CLASS_DIRERAIDER, 10, BASE);

   SPELL_CREATE("flame blade", SPELL_FLAME_BLADE, SPELLTYPE_NATURE,
                PULSE_VIOLENCE, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT
                | TAR_SELF_NONO, 1, 13, cast_flame_blade);
   SPELL_ADD(CLASS_DRUID, 7, BASE);

   SPELL_CREATE("rock to mud", SPELL_ROCK_TO_MUD, SPELLTYPE_NATURE, PULSE_VIOLENCE * 2,
                TRUE, TAR_IGNORE, 0, 8, cast_rock_to_mud);
   SPELL_ADD(CLASS_DRUID, 8, BASE);

   SPELL_CREATE("mud to rock", SPELL_MUD_TO_ROCK, SPELLTYPE_NATURE, PULSE_VIOLENCE * 2,
                TRUE, TAR_IGNORE, 0, 0, cast_mud_to_rock);
   SPELL_ADD(CLASS_DRUID, 8, BASE);

   SPELL_CREATE("fire seeds", SPELL_FIRE_SEEDS, SPELLTYPE_NATURE, PULSE_VIOLENCE * 3 / 2,
                TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 7, cast_fire_seeds);
   SPELL_ADD(CLASS_DRUID, 8, BASE);

   SPELL_CREATE("entangle", SPELL_ENTANGLE, SPELLTYPE_NATURE,
                PULSE_VIOLENCE * 3 / 2, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT
                | TAR_SELF_NONO, 1, 0, cast_entangle);
   SPELL_ADD(CLASS_DRUID, 9, BASE);

   SPELL_CREATE("hailstorm", SPELL_HAILSTORM, SPELLTYPE_NATURE,
                PULSE_VIOLENCE * 2, TRUE, TAR_IGNORE, 1, 14, cast_hailstorm);
   SPELL_ADD(CLASS_DRUID, 9, BASE);

   SPELL_CREATE("dessicate", SPELL_DESSICATE, SPELLTYPE_NATURE,
                PULSE_VIOLENCE * 2, TRUE, TAR_IGNORE, 1, 14, cast_dessicate);
   SPELL_ADD(CLASS_DRUID, 9, BASE);

   SPELL_CREATE("greater realm of protection", SPELL_GREATER_REALM_OF_PROTECTION,
                SPELLTYPE_PROTECTION, PULSE_VIOLENCE, FALSE, TAR_CHAR_ROOM, 0, 0,
                cast_greater_realm_of_protection);
   SPELL_ADD(CLASS_CLERIC, 10, BASE);

   SPELL_CREATE("ward undead", SPELL_WARD_UNDEAD, SPELLTYPE_NECROMANCY, PULSE_VIOLENCE * 2 / 3, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 11, cast_ward_undead);
   SPELL_ADD(CLASS_CLERIC, 4, BASE);
   SPELL_ADD(CLASS_PALADIN, 6, BASE);
   SPELL_ADD(CLASS_NECROMANCER, 6, BASE);
   SPELL_ADD(CLASS_LICH, 6, BASE);

   SPELL_CREATE("destroy undead", SPELL_DESTROY_UNDEAD, SPELLTYPE_NECROMANCY, PULSE_VIOLENCE, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 14, cast_destroy_undead);
   SPELL_ADD(CLASS_CLERIC, 6, BASE);
   SPELL_ADD(CLASS_PALADIN, 8, BASE);
   SPELL_ADD(CLASS_NECROMANCER, 8, BASE);
   SPELL_ADD(CLASS_LICH, 8, BASE);

   SPELL_CREATE("eradicate undead", SPELL_ERADICATE_UNDEAD, SPELLTYPE_NECROMANCY, PULSE_VIOLENCE * 3 / 2, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT | TAR_SELF_NONO, 1, 16, cast_eradicate_undead);
   SPELL_ADD(CLASS_CLERIC, 8, BASE);
   SPELL_ADD(CLASS_NECROMANCER, 9, BASE);
   SPELL_ADD(CLASS_LICH, 9, BASE);

   SPELL_CREATE("annihilate undead", SPELL_ANNIHILATE_UNDEAD, SPELLTYPE_HEALING, PULSE_VIOLENCE * 3 / 2, TRUE, TAR_IGNORE, 1, 6, cast_annihilate_undead);
   SPELL_ADD(CLASS_CLERIC, 9, LEARN);

   SPELL_CREATE("silence person", SPELL_SILENCE_PERSON, SPELLTYPE_GENERIC, PULSE_VIOLENCE * 2,
                TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT |
                TAR_SELF_NONO, 1, 0, cast_silence_person);
   SPELL_ADD(CLASS_CLERIC, 6, BASE);
   /*  SPELL_ADD(CLASS_DRUID, 6, BASE); */
   SPELL_ADD(CLASS_SHAMAN, 7, BASE);

   SPELL_CREATE("revive", SPELL_REVIVE, SPELLTYPE_HEALING, PULSE_VIOLENCE * 10, FALSE, TAR_OBJ_ROOM,
                0, 0, cast_revive);
   SPELL_ADD(CLASS_CLERIC, 9, BASE);

   SPELL_CREATE("poltergeist", SPELL_POLTERGEIST, SPELLTYPE_SPIRIT, PULSE_VIOLENCE * 3, TRUE, TAR_IGNORE, 1, 7, cast_poltergeist);
   SPELL_ADD(CLASS_DIRERAIDER, 10, BASE);
   /*** END NEW SPELLS ***/

#if 0
   /* Myconid Skills */


   SKILL_CREATE("spores of soothing", SPORE_SOOTHE, SKILL_TYPE_MENTAL_INT);
   //  SKILL_ADD(CLASS_MYCONID, 10, 99, 16, BASE);

   SKILL_CREATE("spores of hypnosis", SPORE_HYPNOTIZE, SKILL_TYPE_MENTAL_INT);
   //  SKILL_ADD(CLASS_MYCONID, 10, 99, 16, BASE);

   SKILL_CREATE("spores of pacifying", SPORE_PACIFY, SKILL_TYPE_MENTAL_INT);
   //  SKILL_ADD(CLASS_MYCONID, 10, 99, 16, BASE);

   SKILL_CREATE("spores of distress", SPORE_DISTRESS, SKILL_TYPE_MENTAL_INT);
   //  SKILL_ADD(CLASS_MYCONID, 20, 99, 16, BASE);

   SKILL_CREATE("spores of pain", SPORE_PAIN, SKILL_TYPE_MENTAL_INT);
   //  SKILL_ADD(CLASS_MYCONID, 20, 99, 16, BASE);

   SKILL_CREATE("spores of vigor", SPORE_VIGOR, SKILL_TYPE_MENTAL_INT);
   //  SKILL_ADD(CLASS_MYCONID, 30, 99, 16, BASE);

   SKILL_CREATE("spores of melding", SPORE_MELDING, SKILL_TYPE_MENTAL_INT);
   //  SKILL_ADD(CLASS_MYCONID, 10, 99, 16, BASE);

   /* End Myconid Skills */
#endif

   SPELL_CREATE("venom", SPELL_VENOM, 0, 0, FALSE, TAR_CHAR_ROOM, 1, 0, cast_venom);

   SPELL_CREATE("fire breath", SPELL_FIRE_BREATH, 0, 0, FALSE, TAR_CHAR_ROOM, 1, 0, cast_fire_breath);

   SPELL_CREATE("gas breath", SPELL_GAS_BREATH, 0, 0, FALSE, TAR_CHAR_ROOM, 1, 0, cast_gas_breath);

   SPELL_CREATE("frost breath", SPELL_FROST_BREATH, 0, 0, FALSE, TAR_CHAR_ROOM, 1, 0, cast_frost_breath);

   SPELL_CREATE("acid breath", SPELL_ACID_BREATH, 0, 0, FALSE, TAR_CHAR_ROOM, 1, 0, cast_acid_breath);

   SPELL_CREATE("lightning breath", SPELL_LIGHTNING_BREATH, 0, 0, FALSE, TAR_CHAR_ROOM, 1, 0, cast_lightning_breath);

   SPELL_CREATE("faerie reduce", SPELL_FAERIE_REDUCE, 0, 0, FALSE, TAR_CHAR_ROOM, 1, 0, cast_faerie_reduce);

   /*  Bard Changes, Additions, etc.   05/27/01  -Gargamel  */

#ifndef NEW_BARD
   /* Battlechanter Songs  */
   SONG_CREATE("chant of healing", CHANT_HEALING, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_BATTLECHANTER, 10, 99, 16, BASE);

   SONG_CREATE("chant of protection", CHANT_PROTECTION, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_BATTLECHANTER, 10, 99, 16, BASE);

   SONG_CREATE("chant of charming", CHANT_CHARMING, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_BATTLECHANTER, 20, 99, 16, BASE);

   SONG_CREATE("chant of sleep", CHANT_SLEEP, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_BATTLECHANTER, 10, 99, 16, BASE);

   SONG_CREATE("chant of revelation", CHANT_REVELATION, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_BATTLECHANTER, 20, 99, 16, BASE);

   SONG_CREATE("chant of harming", CHANT_HARMING, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_BATTLECHANTER, 20, 99, 16, BASE);

   SONG_CREATE("chant of flight", CHANT_FLIGHT, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_BATTLECHANTER, 30, 99, 16, BASE);

   SONG_CREATE("chant of heroism", CHANT_HEROISM, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_BATTLECHANTER, 10, 99, 16, BASE);

   SONG_CREATE("chant of cowardice", CHANT_COWARDICE, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_BATTLECHANTER, 25, 99, 16, BASE);

   SONG_CREATE("chant of forgetfulness", CHANT_FORGETFULNESS, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_BATTLECHANTER,  15, 99, 16, BASE);

   SONG_CREATE("chant of luck", CHANT_LUCK, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_BATTLECHANTER, 40, 99, 16, BASE);
#endif

#ifndef NEW_BARD
   /* Bard Songs */
   SONG_CREATE("song of charming", SONG_CHARMING, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_BARD, 20, 99, 16, BASE);

   SONG_CREATE("song of sleep", SONG_SLEEP, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_BARD, 12, 99, 16, BASE);

   SONG_CREATE("song of calming", SONG_CALMING, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_BARD, 12, 99, 16, BASE);

   SONG_CREATE("song of healing", SONG_HEALING, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_BARD, 10, 99, 16, BASE);

   SONG_CREATE("song of revelation", SONG_REVELATION, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_BARD, 20, 99, 16, BASE);

   SONG_CREATE("song of harming", SONG_HARMING, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_BARD, 10, 99, 16, BASE);

   SONG_CREATE("song of flight", SONG_FLIGHT, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_BARD, 30, 99, 16, BASE);

   SONG_CREATE("song of protection", SONG_PROTECTION, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_BARD, 15, 99, 16, BASE);

   SONG_CREATE("song of heroism", SONG_HEROISM, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_BARD, 20, 99, 16, BASE);

   SONG_CREATE("song of cowardice", SONG_COWARDICE, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_BARD, 25, 99, 16, BASE);

   SONG_CREATE("song of forgetfulness", SONG_FORGETFULNESS, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_BARD, 15, 99, 16, BASE);

   SONG_CREATE("song of peace", SONG_PEACE, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_BARD, 12, 99, 16, BASE);

   SONG_CREATE("song of dance", SONG_DANCE, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_BARD, 35, 99, 16, BASE);

   SONG_CREATE("song of enthralling", SONG_ENTHRALLING, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_BARD, 40, 99, 16, BASE);

   SONG_CREATE("song of luck", SONG_LUCK, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_BARD, 45, 99, 16, BASE);

   WARCHANT_CREATE("fortitude", SONG_FORTITUDE, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_BARD, 35, 99, 30, BASE);
   SKILL_ADD(CLASS_BATTLECHANTER, 45, 99, 30, BASE);

   WARCHANT_CREATE("battle hymn", SONG_BATTLEHYMN, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_BARD, 45, 99, 30, BASE);
   SKILL_ADD(CLASS_BATTLECHANTER, 35, 99, 30, BASE);
#endif

   SKILL_CREATE("virtuoso", SKILL_VIRTUOSO, SKILL_TYPE_MENTAL_INT);
#ifndef NEW_BARD
   SKILL_ADD(CLASS_BARD, 40, 99, 16, BASE);
   SKILL_ADD(CLASS_BATTLECHANTER, 40, 99, 16, BASE);
#else
   SKILL_ADD(CLASS_BARD, 25, 99, 16, BASE);
   SKILL_ADD(CLASS_BATTLECHANTER, 25, 99, 16, BASE);
#endif

#ifdef NEW_BARD
   SKILL_CREATE("accompany", SKILL_ACCOMPANY, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_BARD, 20, 90, 16, BASE);
   SKILL_ADD(CLASS_BATTLECHANTER, 20, 90, 16, BASE);
#endif

   SKILL_CREATE("flute", INSTRUMENT_FLUTE, SKILL_TYPE_PHYS_DEX);
   SKILL_ADD(CLASS_BARD, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_BATTLECHANTER, 1, 99, 16, BASE);

   SKILL_CREATE("lyre", INSTRUMENT_LYRE, SKILL_TYPE_PHYS_DEX);
   SKILL_ADD(CLASS_BARD, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_BATTLECHANTER, 1, 99, 16, BASE);

   SKILL_CREATE("mandolin", INSTRUMENT_MANDOLIN, SKILL_TYPE_PHYS_DEX);
   SKILL_ADD(CLASS_BARD, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_BATTLECHANTER, 1, 99, 16, BASE);

   SKILL_CREATE("harp", INSTRUMENT_HARP, SKILL_TYPE_PHYS_DEX);
   SKILL_ADD(CLASS_BARD, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_BATTLECHANTER, 1, 99, 16, BASE);

   SKILL_CREATE("drums", INSTRUMENT_DRUMS, SKILL_TYPE_PHYS_DEX);
   SKILL_ADD(CLASS_BARD, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_BATTLECHANTER, 1, 99, 16, BASE);

   SKILL_CREATE("horn", INSTRUMENT_HORN, SKILL_TYPE_PHYS_DEX);
   SKILL_ADD(CLASS_BARD, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_BATTLECHANTER, 1, 99, 16, BASE);

   SKILL_CREATE("lore", SKILL_LORE, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_BARD, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_BATTLECHANTER, 1, 99, 16, BASE);

   /* New Bard additions - Iyachtu */
#ifdef NEW_BARD
   SKILL_CREATE("healing songs", SKILL_HEALING_SONGS, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_BARD, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_BATTLECHANTER, 1, 99, 16, BASE);

   SKILL_CREATE("melee songs", SKILL_MELEE_SONGS, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_BARD, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_BATTLECHANTER, 1, 99, 16, BASE);

   SKILL_CREATE("magical songs", SKILL_MAGICAL_SONGS, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_BARD, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_BATTLECHANTER, 1, 99, 16, BASE);

   SKILL_CREATE("baneful songs", SKILL_BANEFUL_SONGS, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_BARD, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_BATTLECHANTER, 1, 99, 16, BASE);

   SPELL_CREATE("song of revelation", BARD_REVELATION, SPELLTYPE_GENERIC, PULSE_VIOLENCE * 2, FALSE, TAR_OBJ_ROOM, 0, 0, cast_embalm);
   SPELL_CREATE("song of travel", BARD_TRAVEL, SPELLTYPE_GENERIC, PULSE_VIOLENCE * 2, FALSE, TAR_OBJ_ROOM, 0, 0, cast_embalm);
   SPELL_CREATE("song of protection", BARD_PROTECTION, SPELLTYPE_GENERIC, PULSE_VIOLENCE * 2, FALSE, TAR_OBJ_ROOM, 0, 0, cast_embalm);
#endif

#ifdef NEW_BARD
   SPELL_CREATE("song of recovery", SPELL_RESURRECTION_RECOVERY, SPELLTYPE_HEALING, PULSE_VIOLENCE, TRUE, TAR_IGNORE, 1, 1, cast_resurrection_recovery);
   SPELL_ADD(CLASS_BARD, 10, LEARN);
   SPELL_ADD(CLASS_BATTLECHANTER, 10, LEARN);
#endif

   //SKILL_CREATE("feign death", SKILL_FEIGN_DEATH, SKILL_TYPE_MENTAL_INT);
   //SKILL_ADD(CLASS_MONK, 20, 90, 32, BASE);

   SKILL_CREATE("death grip", SKILL_DEATH_GRIP, SKILL_TYPE_PHYS_DEX);
   SKILL_ADD(CLASS_MONK, 30, 99, 16, BASE);

   SKILL_CREATE("self preservation", SKILL_SELF_PRESERVATION, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_MONK, 1, 90, 14, BASE);

   SKILL_CREATE("safe fall", SKILL_SAFE_FALL, SKILL_TYPE_PHYS_AGI);
   SKILL_ADD(CLASS_MONK, 5, 99, 21, BASE);

   SKILL_CREATE("switch opponents", SKILL_SWITCH_OPPONENTS, SKILL_TYPE_PHYS_DEX);
   SKILL_ADD(CLASS_MONK, 15, 99, 10, BASE);
   SKILL_ADD(CLASS_WARRIOR, 20, 85, 16, BASE);
   SKILL_ADD(CLASS_PALADIN, 20, 85, 16, BASE);
   SKILL_ADD(CLASS_RANGER, 20, 65, 10, BASE);
   SKILL_ADD(CLASS_ANTIPALADIN, 20, 85, 16, BASE);
   SKILL_ADD(CLASS_THIEF, 23, 90, 20, BASE);
   SKILL_ADD(CLASS_ROGUE, 20, 90, 20, BASE);
   SKILL_ADD(CLASS_BARD, 25, 40, 30, BASE);
   SKILL_ADD(CLASS_BATTLECHANTER, 25, 40, 30, BASE);
   SKILL_ADD(CLASS_DIRERAIDER, 20, 65, 10, BASE);

   SKILL_CREATE("springleap", SKILL_SPRINGLEAP, SKILL_TYPE_PHYS_AGI);
   SKILL_ADD(CLASS_MONK, 15, 99, 16, BASE);

   SKILL_CREATE("martial arts", SKILL_MARTIAL_ARTS, SKILL_TYPE_PHYS_DEX);
   SKILL_ADD(CLASS_MONK, 1, 99, 24, BASE);

   SKILL_CREATE("unarmed damage", SKILL_UNARMED_DAMAGE, SKILL_TYPE_PHYS_STR);
   SKILL_ADD(CLASS_MONK, 20, 99, 64, BASE);

   SKILL_CREATE("soul strike", SKILL_SOUL_STRIKE, SKILL_TYPE_MENTAL_WIS);
   SKILL_ADD(CLASS_MONK, 45, 99, 127, BASE);

   SKILL_CREATE("heroism", SKILL_HEROISM, SKILL_TYPE_MENTAL_WIS);
   SKILL_ADD(CLASS_MONK, 25, 99, 32, BASE);

   SKILL_CREATE("chant", SKILL_CHANT, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_MONK, 5, 85, 16, BASE);
   SKILL_ADD(CLASS_BERSERKER, 10, 90, 20, BASE);

   SKILL_CREATE("dragon punch", SKILL_DRAGON_PUNCH, SKILL_TYPE_PHYS_DEX);
   SKILL_ADD(CLASS_MONK, 25, 99, 16, BASE);

   SKILL_CREATE("regeneration", SKILL_REGENERATION, SKILL_TYPE_MENTAL_WIS);
   SKILL_ADD(CLASS_MONK, 30, 85, 16, BASE);
   SKILL_ADD(CLASS_BERSERKER, 15, 90, 20, BASE);

   SKILL_CREATE("calm", SKILL_CALM, SKILL_TYPE_MENTAL_WIS);
   SKILL_ADD(CLASS_MONK, 20, 85, 16, BASE);

   SKILL_CREATE("range specialist", SKILL_RANGE_SPECIALIST, SKILL_TYPE_MENTAL_WIS);
   SKILL_ADD(CLASS_RANGER, 1, 99, 16, BASE);
   //SKILL_ADD(CLASS_THIEF, 10, 99, 16, BASE);
   //SKILL_ADD(CLASS_ROGUE, 10, 90, 16, BASE);
   SKILL_ADD(CLASS_DIRERAIDER, 1, 99, 16, BASE);

   SKILL_CREATE("archery", SKILL_ARCHERY, SKILL_TYPE_PHYS_DEX);
   SKILL_ADD(CLASS_RANGER, 1, 95, 16, BASE);
   SKILL_ADD(CLASS_MERCENARY, 1, 75, 16, BASE);
   SKILL_ADD(CLASS_DIRERAIDER, 1, 95, 16, BASE);

   SKILL_CREATE("range weapons", SKILL_RANGE_WEAPONS, SKILL_TYPE_PHYS_DEX);
   SKILL_ADD(CLASS_THIEF, 1, 95, 16, BASE);
   SKILL_ADD(CLASS_ASSASSIN, 1, 95, 16, BASE);
   SKILL_ADD(CLASS_MERCENARY, 1, 75, 16, BASE);
   //SKILL_ADD(CLASS_ROGUE, 1, 95, 16, BASE);

   SKILL_CREATE("circle", SKILL_CIRCLE, SKILL_TYPE_PHYS_DEX);
   SKILL_ADD(CLASS_THIEF, 10, 99, 20, BASE);
   SKILL_ADD(CLASS_ROGUE, 15, 99, 20, BASE);
   SKILL_ADD(CLASS_BARD, 25, 50, 20, BASE);
   SKILL_ADD(CLASS_BATTLECHANTER, 25, 50, 20, BASE);

   SKILL_CREATE("summon totem", SKILL_SUMMON_TOTEM, SKILL_TYPE_MENTAL_WIS);
   SKILL_ADD(CLASS_SHAMAN, 21, 90, 16, BASE);

   SKILL_CREATE("scribe", SKILL_SCRIBE, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_LICH, 1, 90, 5, BASE);
   SKILL_ADD(CLASS_SORCERER, 1, 90, 5, BASE);
   SKILL_ADD(CLASS_NECROMANCER, 1, 90, 5, BASE);
   SKILL_ADD(CLASS_CONJURER, 1, 90, 5, BASE);
   SKILL_ADD(CLASS_INVOKER, 1, 90, 5, BASE);
   SKILL_ADD(CLASS_ENCHANTER, 1, 90, 5, BASE);
   SKILL_ADD(CLASS_ILLUSIONIST, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_ELEMENTALIST, 1, 90, 5, BASE);


   SKILL_CREATE("quick chant", SKILL_QUICK_CHANT, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_PALADIN, 10, 70, 25, BASE);
   SKILL_ADD(CLASS_ANTIPALADIN, 10, 70, 25, BASE);
   SKILL_ADD(CLASS_SORCERER, 1, 99, 15, BASE);
   SKILL_ADD(CLASS_NECROMANCER, 1, 99, 15, BASE);
   SKILL_ADD(CLASS_LICH, 1, 99, 15, BASE);
   SKILL_ADD(CLASS_CONJURER, 1, 90, 16, BASE);
   SKILL_ADD(CLASS_SHAMAN, 1, 99, 20, BASE);
   SKILL_ADD(CLASS_CLERIC, 1, 99, 20, BASE);
   SKILL_ADD(CLASS_DRUID, 1, 99, 20, BASE);
   SKILL_ADD(CLASS_RANGER, 10, 90, 25, BASE);
   SKILL_ADD(CLASS_INVOKER, 1, 99, 15, BASE);
   SKILL_ADD(CLASS_ENCHANTER, 1, 99, 15, BASE);
   SKILL_ADD(CLASS_ILLUSIONIST, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_ELEMENTALIST, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_BARD, 10, 70, 25, BASE);
   SKILL_ADD(CLASS_BATTLECHANTER, 10, 70, 16, BASE);
   SKILL_ADD(CLASS_DIRERAIDER, 10, 90, 25, BASE);

   SKILL_CREATE("clerical spell knowledge", SKILL_SPELL_KNOWLEDGE_CLERICAL, SKILL_TYPE_MENTAL_WIS);
   SKILL_ADD(CLASS_PALADIN, 10, 85, 16, BASE);
   SKILL_ADD(CLASS_ANTIPALADIN, 10, 85, 16, BASE);
   SKILL_ADD(CLASS_SHAMAN, 5, 90, 16, BASE);
   SKILL_ADD(CLASS_CLERIC, 1, 90, 16, BASE);
   SKILL_ADD(CLASS_DRUID, 1, 90, 16, BASE);
   SKILL_ADD(CLASS_RANGER, 10, 85, 16, BASE);
   SKILL_ADD(CLASS_BATTLECHANTER, 10, 70, 16, BASE);
   SKILL_ADD(CLASS_DIRERAIDER, 10, 85, 16, BASE);

   SKILL_CREATE("sorcerous spell knowledge", SKILL_SPELL_KNOWLEDGE_MAGICAL, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_ANTIPALADIN, 10, 70, 16, BASE);
   SKILL_ADD(CLASS_SORCERER, 1, 90, 16, BASE);
   SKILL_ADD(CLASS_NECROMANCER, 1, 90, 16, BASE);
   SKILL_ADD(CLASS_LICH, 1, 90, 16, BASE);
   SKILL_ADD(CLASS_CONJURER, 1, 90, 16, BASE);
   SKILL_ADD(CLASS_SHAMAN, 1, 90, 16, BASE);
   SKILL_ADD(CLASS_RANGER, 10, 90, 16, BASE);
   SKILL_ADD(CLASS_INVOKER, 1, 90, 16, BASE);
   SKILL_ADD(CLASS_ENCHANTER, 1, 90, 16, BASE);
   SKILL_ADD(CLASS_ILLUSIONIST, 1, 90, 16, BASE);
   SKILL_ADD(CLASS_ELEMENTALIST, 1, 90, 16, BASE);
   SKILL_ADD(CLASS_BARD, 10, 70, 16, BASE);
   SKILL_ADD(CLASS_DIRERAIDER, 10, 90, 16, BASE);

   SKILL_CREATE("offense", SKILL_ATTACK, SKILL_TYPE_PHYS_STR);
   SKILL_ADD(CLASS_PALADIN, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_ANTIPALADIN, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_BERSERKER, 1, 95, 10, BASE);
   SKILL_ADD(CLASS_RANGER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_ASSASSIN, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_THIEF, 1, 80, 16, BASE);
   SKILL_ADD(CLASS_MERCENARY, 1, 70, 16, BASE);
   SKILL_ADD(CLASS_WARRIOR, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_ROGUE, 1, 90, 16, BASE);
   SKILL_ADD(CLASS_BARD, 15, 70, 16, BASE);
   SKILL_ADD(CLASS_CLERIC, 1, 50, 16, BASE);
   SKILL_ADD(CLASS_BATTLECHANTER, 15, 70, 16, BASE);
   SKILL_ADD(CLASS_DIRERAIDER, 1, 99, 16, BASE);

   SKILL_CREATE("summon mount", SKILL_SUMMON_MOUNT, SKILL_TYPE_MENTAL_WIS);
   SKILL_CREATE("summon horde", SKILL_SUMMON_HORDE, SKILL_TYPE_PHYS_STR);

   SKILL_CREATE("spellcast generic", SKILL_CAST_GENERIC, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_PALADIN, 10, 70, 25, BASE);
   SKILL_ADD(CLASS_ANTIPALADIN, 10, 70, 25, BASE);
   SKILL_ADD(CLASS_SORCERER, 1, 99, 20, BASE);
   SKILL_ADD(CLASS_NECROMANCER, 1, 99, 20, BASE);
   SKILL_ADD(CLASS_LICH, 1, 99, 20, BASE);
   SKILL_ADD(CLASS_CONJURER, 1, 99, 20, BASE);
   SKILL_ADD(CLASS_SHAMAN, 1, 99, 22, BASE);
   SKILL_ADD(CLASS_CLERIC, 1, 99, 20, BASE);
   SKILL_ADD(CLASS_DRUID, 1, 99, 20, BASE);
   SKILL_ADD(CLASS_RANGER, 10, 70, 25, BASE);
   SKILL_ADD(CLASS_INVOKER, 1, 99, 20, BASE);
   SKILL_ADD(CLASS_ENCHANTER, 1, 99, 20, BASE);
   SKILL_ADD(CLASS_ILLUSIONIST, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_ELEMENTALIST, 1, 99, 20, BASE);
   SKILL_ADD(CLASS_BARD, 10, 70, 16, BASE);
   SKILL_ADD(CLASS_BATTLECHANTER, 10, 70, 16, BASE);
   SKILL_ADD(CLASS_DIRERAIDER, 10, 70, 25, BASE);

   /* Spellcast FIRE/COLD replaced with INVOCATION - CRM */
#if 0
   SKILL_CREATE("spellcast fire", SKILL_CAST_FIRE, SKILL_TYPE_MENTAL_INT);
   /*  SKILL_ADD(CLASS_PALADIN, 10, 70, 16, BASE); */
   SKILL_ADD(CLASS_ANTIPALADIN, 10, 70, 16, BASE);
   SKILL_ADD(CLASS_SORCERER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_NECROMANCER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_LICH, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_CONJURER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_SHAMAN, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_CLERIC, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_DRUID, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_RANGER, 10, 70, 16, BASE);
   SKILL_ADD(CLASS_ENCHANTER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_INVOKER, 1, 99, 16, BASE);

   SKILL_CREATE("spellcast cold", SKILL_CAST_COLD, SKILL_TYPE_MENTAL_INT);
   /*  SKILL_ADD(CLASS_PALADIN, 10, 70, 16, BASE); */
   SKILL_ADD(CLASS_ANTIPALADIN, 10, 70, 16, BASE);
   SKILL_ADD(CLASS_SORCERER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_NECROMANCER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_LICH, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_CONJURER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_SHAMAN, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_CLERIC, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_DRUID, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_RANGER, 10, 70, 16, BASE);
   SKILL_ADD(CLASS_INVOKER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_ENCHANTER, 1, 99, 16, BASE);
#endif
   /* End of Removed Spellcast FIRE/COLD - CRM */

   SKILL_CREATE("spellcast invocation", SKILL_CAST_INVOCATION, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_ANTIPALADIN, 10, 70, 16, BASE);
   SKILL_ADD(CLASS_PALADIN, 10, 70, 16, BASE);
   SKILL_ADD(CLASS_NECROMANCER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_LICH, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_CONJURER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_SHAMAN, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_CLERIC, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_DRUID, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_RANGER, 10, 70, 16, BASE);
   SKILL_ADD(CLASS_ENCHANTER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_INVOKER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_ILLUSIONIST, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_ELEMENTALIST, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_BARD, 10, 70, 16, BASE);
   SKILL_ADD(CLASS_BATTLECHANTER, 10, 70, 16, BASE);

   SKILL_CREATE("spellcast elemental", SKILL_CAST_ELEMENTAL, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_NECROMANCER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_LICH, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_ENCHANTER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_INVOKER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_ILLUSIONIST, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_ELEMENTALIST, 1, 99, 16, BASE);


   SKILL_CREATE("spellcast spirit", SKILL_CAST_SPIRIT, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_ANTIPALADIN, 10, 70, 16, BASE);
   SKILL_ADD(CLASS_NECROMANCER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_LICH, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_CONJURER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_SHAMAN, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_CLERIC, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_DRUID, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_RANGER, 10, 70, 16, BASE);
   SKILL_ADD(CLASS_ENCHANTER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_INVOKER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_ILLUSIONIST, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_ELEMENTALIST, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_DIRERAIDER, 10, 70, 16, BASE);

   SKILL_CREATE("spellcast nature", SKILL_CAST_NATURE, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_ANTIPALADIN, 10, 70, 16, BASE);
   SKILL_ADD(CLASS_NECROMANCER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_LICH, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_CONJURER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_SHAMAN, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_CLERIC, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_DRUID, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_RANGER, 10, 70, 16, BASE);
   SKILL_ADD(CLASS_ENCHANTER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_INVOKER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_ILLUSIONIST, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_ELEMENTALIST, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_DIRERAIDER, 10, 70, 16, BASE);

   SKILL_CREATE("spellcast illusion", SKILL_CAST_ILLUSION, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_ILLUSIONIST, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_NECROMANCER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_LICH, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_CONJURER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_ENCHANTER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_INVOKER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_SHAMAN, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_DRUID, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_ELEMENTALIST, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_BARD, 10, 70, 16, BASE);
   SKILL_ADD(CLASS_BATTLECHANTER, 10, 70, 16, BASE);
   SKILL_ADD(CLASS_RANGER, 10, 70, 16, BASE);
   SKILL_ADD(CLASS_DIRERAIDER, 10, 70, 16, BASE);

   SKILL_CREATE("spellcast necromancy", SKILL_CAST_NECROMANCY, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_NECROMANCER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_LICH, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_ANTIPALADIN, 10, 70, 16, BASE);
   SKILL_ADD(CLASS_PALADIN, 10, 70, 16, BASE);
   SKILL_ADD(CLASS_CLERIC, 1, 99, 16, BASE);


   SKILL_CREATE("spellcast healing", SKILL_CAST_HEALING, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_PALADIN, 10, 70, 16, BASE);
   SKILL_ADD(CLASS_ANTIPALADIN, 10, 70, 16, BASE);
   SKILL_ADD(CLASS_SORCERER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_NECROMANCER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_LICH, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_CONJURER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_SHAMAN, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_CLERIC, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_DRUID, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_RANGER, 10, 70, 16, BASE);
   SKILL_ADD(CLASS_INVOKER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_ENCHANTER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_ILLUSIONIST, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_ELEMENTALIST, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_BARD, 10, 70, 16, BASE);
   SKILL_ADD(CLASS_BATTLECHANTER, 10, 70, 16, BASE);
   SKILL_ADD(CLASS_DIRERAIDER, 10, 70, 16, BASE);

   SKILL_CREATE("spellcast teleportation", SKILL_CAST_TELEPORTATION, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_ANTIPALADIN, 10, 70, 16, BASE);
   SKILL_ADD(CLASS_SORCERER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_NECROMANCER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_LICH, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_CONJURER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_SHAMAN, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_CLERIC, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_DRUID, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_RANGER, 10, 70, 16, BASE);
   SKILL_ADD(CLASS_INVOKER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_ENCHANTER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_ILLUSIONIST, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_ELEMENTALIST, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_BARD, 10, 70, 16, BASE);
   SKILL_ADD(CLASS_BATTLECHANTER, 10, 70, 16, BASE);


   SKILL_CREATE("spellcast summoning", SKILL_CAST_SUMMONING, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_PALADIN, 10, 70, 16, BASE);
   SKILL_ADD(CLASS_ANTIPALADIN, 10, 70, 16, BASE);
   SKILL_ADD(CLASS_SORCERER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_NECROMANCER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_LICH, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_CONJURER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_SHAMAN, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_CLERIC, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_DRUID, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_RANGER, 10, 70, 16, BASE);
   SKILL_ADD(CLASS_INVOKER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_ENCHANTER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_ILLUSIONIST, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_ELEMENTALIST, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_BARD, 10, 70, 16, BASE);
   SKILL_ADD(CLASS_BATTLECHANTER, 10, 70, 16, BASE);
   SKILL_ADD(CLASS_DIRERAIDER, 10, 70, 16, BASE);

   SKILL_CREATE("spellcast protection", SKILL_CAST_PROTECTION, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_PALADIN, 10, 70, 16, BASE);
   SKILL_ADD(CLASS_ANTIPALADIN, 10, 70, 16, BASE);
   SKILL_ADD(CLASS_SORCERER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_NECROMANCER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_LICH, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_CONJURER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_SHAMAN, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_CLERIC, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_DRUID, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_RANGER, 10, 70, 16, BASE);
   SKILL_ADD(CLASS_INVOKER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_ENCHANTER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_ILLUSIONIST, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_ELEMENTALIST, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_BARD, 10, 70, 16, BASE);
   SKILL_ADD(CLASS_BATTLECHANTER, 10, 70, 16, BASE);
   SKILL_ADD(CLASS_DIRERAIDER, 10, 70, 16, BASE);

   SKILL_CREATE("spellcast divination", SKILL_CAST_DIVINATION, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_PALADIN, 10, 70, 16, BASE);
   SKILL_ADD(CLASS_ANTIPALADIN, 10, 70, 16, BASE);
   SKILL_ADD(CLASS_SORCERER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_NECROMANCER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_LICH, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_CONJURER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_SHAMAN, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_CLERIC, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_DRUID, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_RANGER, 10, 70, 16, BASE);
   SKILL_ADD(CLASS_INVOKER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_ENCHANTER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_ILLUSIONIST, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_ELEMENTALIST, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_BARD, 10, 70, 16, BASE);
   SKILL_ADD(CLASS_BATTLECHANTER, 10, 70, 16, BASE);
   SKILL_ADD(CLASS_DIRERAIDER, 10, 70, 16, BASE);

   SKILL_CREATE("spellcast enchantment", SKILL_CAST_ENCHANT, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_PALADIN, 10, 70, 16, BASE);
   SKILL_ADD(CLASS_RANGER, 10, 70, 16, BASE);
   SKILL_ADD(CLASS_ANTIPALADIN, 10, 70, 16, BASE);
   SKILL_ADD(CLASS_SORCERER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_NECROMANCER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_LICH, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_CONJURER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_SHAMAN, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_CLERIC, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_DRUID, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_INVOKER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_ENCHANTER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_ILLUSIONIST, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_ELEMENTALIST, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_BARD, 10, 70, 16, BASE);
   SKILL_ADD(CLASS_BATTLECHANTER, 10, 70, 16, BASE);
   SKILL_ADD(CLASS_DIRERAIDER, 10, 70, 16, BASE);

   /* Spec FIRE/COLD replaced by spec INVOCATION */
#if 0
   SKILL_CREATE("specialize fire", SKILL_SPEC_FIRE, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_SORCERER, 20, 90, 16, BASE);
   SKILL_ADD(CLASS_CONJURER, 20, 90, 16, BASE);
   SKILL_ADD(CLASS_INVOKER, 20, 90, 16, BASE);
   SKILL_ADD(CLASS_ENCHANTER, 20, 90, 16, BASE);

   SKILL_CREATE("specialize cold", SKILL_SPEC_COLD, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_SORCERER, 20, 90, 16, BASE);
   SKILL_ADD(CLASS_NECROMANCER, 20, 90, 16, BASE);
   SKILL_ADD(CLASS_LICH, 20, 90, 16, BASE);
   SKILL_ADD(CLASS_CONJURER, 20, 90, 16, BASE);
   SKILL_ADD(CLASS_INVOKER, 20, 90, 16, BASE);
   SKILL_ADD(CLASS_ENCHANTER, 20, 90, 16, BASE);
#endif

   SKILL_CREATE("specialize invocation", SKILL_SPEC_INVOCATION, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_INVOKER, 20, 90, 16, BASE);

   SKILL_CREATE("specialize nature", SKILL_SPEC_NATURE, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_DRUID, 20, 90, 16, BASE);

   SKILL_CREATE("specialize spirit", SKILL_SPEC_SPIRIT, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_SHAMAN, 20, 90, 16, BASE);

   SKILL_CREATE("specialize illusion", SKILL_SPEC_ILLUSION, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_ILLUSIONIST, 20, 90, 16, BASE);

   SKILL_CREATE("specialize necromancy", SKILL_SPEC_NECROMANCY, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_NECROMANCER, 20, 90, 16, BASE);
   SKILL_ADD(CLASS_LICH, 20, 90, 16, BASE);

   SKILL_CREATE("specialize healing", SKILL_SPEC_HEALING, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_CLERIC, 20, 90, 16, BASE);

   SKILL_CREATE("specialize teleportation", SKILL_SPEC_TELEPORTATION, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_SORCERER, 20, 90, 16, BASE);
   SKILL_ADD(CLASS_CONJURER, 20, 90, 16, BASE);

   SKILL_CREATE("specialize summoning", SKILL_SPEC_SUMMONING, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_NECROMANCER, 20, 90, 16, BASE);
   SKILL_ADD(CLASS_LICH, 20, 90, 16, BASE);
   SKILL_ADD(CLASS_CONJURER, 20, 90, 16, BASE);
   SKILL_ADD(CLASS_CLERIC, 20, 90, 16, BASE);

   SKILL_CREATE("specialize protection", SKILL_SPEC_PROTECTION, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_SORCERER, 20, 90, 16, BASE);
   SKILL_ADD(CLASS_NECROMANCER, 20, 90, 16, BASE);
   SKILL_ADD(CLASS_LICH, 20, 90, 16, BASE);
   SKILL_ADD(CLASS_CONJURER, 20, 90, 16, BASE);
   SKILL_ADD(CLASS_CLERIC, 20, 90, 16, BASE);

   SKILL_CREATE("specialize elemental", SKILL_SPEC_ELEMENTAL, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_ELEMENTALIST, 20, 90, 16, BASE);


#if 0
   SKILL_CREATE("specialize divination", SKILL_SPEC_DIVINATION, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_SORCERER, 20, 90, 16, BASE);
   SKILL_ADD(CLASS_NECROMANCER, 20, 90, 16, BASE);
   SKILL_ADD(CLASS_LICH, 20, 90, 16, BASE);
   SKILL_ADD(CLASS_CONJURER, 20, 90, 16, BASE);
   SKILL_ADD(CLASS_CLERIC, 20, 90, 16, BASE);
#endif

   SKILL_CREATE("specialize enchantment", SKILL_SPEC_ENCHANT, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_SORCERER, 20, 90, 16, BASE);
   SKILL_ADD(CLASS_NECROMANCER, 20, 90, 16, BASE);
   SKILL_ADD(CLASS_LICH, 20, 90, 16, BASE);
   SKILL_ADD(CLASS_CONJURER, 20, 90, 16, BASE);
   SKILL_ADD(CLASS_CLERIC, 20, 90, 16, BASE);
   SKILL_ADD(CLASS_ENCHANTER, 20, 90, 16, BASE);

   /* Begin assignation of combat skills. */

   SKILL_CREATE("1h bludgeon", SKILL_1H_BLUDGEON, SKILL_TYPE_PHYS_STR);
   SKILL_ADD(CLASS_ANTIPALADIN, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_ASSASSIN, 1, 60, 16, BASE);
   SKILL_ADD(CLASS_BARD, 1, 65, 16, BASE);
   SKILL_ADD(CLASS_BATTLECHANTER, 1, 65, 16, BASE);
   SKILL_ADD(CLASS_BERSERKER, 1, 80, 16, BASE);
   SKILL_ADD(CLASS_CLERIC, 1, 80, 16, BASE);
   SKILL_ADD(CLASS_CONJURER, 1, 50, 16, BASE);
   SKILL_ADD(CLASS_DRUID, 1, 60, 16, BASE);
   SKILL_ADD(CLASS_MERCENARY, 1, 75, 16, BASE);
   SKILL_ADD(CLASS_MONK, 1, 50, 16, BASE);
   SKILL_ADD(CLASS_PALADIN, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_RANGER, 1, 90, 16, BASE);
   SKILL_ADD(CLASS_SORCERER, 1, 50, 16, BASE);
   SKILL_ADD(CLASS_NECROMANCER, 1, 50, 16, BASE);
   SKILL_ADD(CLASS_LICH, 1, 50, 16, BASE);
   SKILL_ADD(CLASS_THIEF, 1, 75, 16, BASE);
   SKILL_ADD(CLASS_WARRIOR, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_SHAMAN, 1, 65, 16, BASE);
   SKILL_ADD(CLASS_PSIONICIST, 1, 65, 16, BASE);
   SKILL_ADD(CLASS_ENCHANTER, 1, 50, 16, BASE);
   SKILL_ADD(CLASS_INVOKER, 1, 50, 16, BASE);
   SKILL_ADD(CLASS_ROGUE, 1, 75, 16, BASE);
   SKILL_ADD(CLASS_ILLUSIONIST, 1, 50, 16, BASE);
   SKILL_ADD(CLASS_ELEMENTALIST, 1, 50, 16, BASE);
   SKILL_ADD(CLASS_DIRERAIDER, 1, 90, 16, BASE);

   SKILL_CREATE("1h slashing", SKILL_1H_SLASHING, SKILL_TYPE_PHYS_STR);
   SKILL_ADD(CLASS_ANTIPALADIN, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_ASSASSIN, 1, 70, 16, BASE);
   SKILL_ADD(CLASS_BARD, 1, 70, 16, BASE);
   SKILL_ADD(CLASS_BATTLECHANTER, 1, 70, 16, BASE);
   SKILL_ADD(CLASS_BERSERKER, 1, 75, 16, BASE);
   SKILL_ADD(CLASS_DRUID, 1, 60, 16, BASE);
   SKILL_ADD(CLASS_MERCENARY, 1, 85, 16, BASE);
   SKILL_ADD(CLASS_MONK, 1, 60, 16, BASE);
   SKILL_ADD(CLASS_PALADIN, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_RANGER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_THIEF, 1, 80, 16, BASE);
   SKILL_ADD(CLASS_WARRIOR, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_PSIONICIST, 1, 55, 16, BASE);
   SKILL_ADD(CLASS_ROGUE, 1, 80, 16, BASE);
   SKILL_ADD(CLASS_DIRERAIDER, 1, 99, 16, BASE);

   SKILL_CREATE("1h piercing", SKILL_1H_PIERCING, SKILL_TYPE_PHYS_STR);
   SKILL_ADD(CLASS_ANTIPALADIN, 1, 95, 16, BASE);
   SKILL_ADD(CLASS_ASSASSIN, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_BARD, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_BATTLECHANTER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_BERSERKER, 1, 70, 16, BASE);
   SKILL_ADD(CLASS_CONJURER, 1, 50, 40, BASE);
   SKILL_ADD(CLASS_MERCENARY, 1, 85, 16, BASE);
   SKILL_ADD(CLASS_MONK, 1, 60, 16, BASE);
   SKILL_ADD(CLASS_NECROMANCER, 1, 50, 40, BASE);
   SKILL_ADD(CLASS_LICH, 1, 50, 40, BASE);
   SKILL_ADD(CLASS_PALADIN, 1, 80, 16, BASE);
   SKILL_ADD(CLASS_RANGER, 1, 85, 16, BASE);
   SKILL_ADD(CLASS_SORCERER, 1, 50, 40, BASE);
   SKILL_ADD(CLASS_THIEF, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_WARRIOR, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_PSIONICIST, 1, 55, 16, BASE);
   SKILL_ADD(CLASS_ENCHANTER, 1, 50, 40, BASE);
   SKILL_ADD(CLASS_INVOKER, 1, 50, 40, BASE);
   SKILL_ADD(CLASS_ROGUE, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_ILLUSIONIST, 1, 50, 40, BASE);
   SKILL_ADD(CLASS_ELEMENTALIST, 1, 50, 40, BASE);
   SKILL_ADD(CLASS_DIRERAIDER, 1, 85, 16, BASE);

   SKILL_CREATE("1h misc", SKILL_1H_MISC, SKILL_TYPE_PHYS_STR);
   SKILL_ADD(CLASS_ANTIPALADIN, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_ASSASSIN, 1, 40, 16, BASE);
   SKILL_ADD(CLASS_BARD, 1, 50, 16, BASE);
   SKILL_ADD(CLASS_BATTLECHANTER, 1, 50, 16, BASE);
   SKILL_ADD(CLASS_BERSERKER, 1, 80, 16, BASE);
   SKILL_ADD(CLASS_MERCENARY, 1, 75, 16, BASE);
   SKILL_ADD(CLASS_MONK, 1, 20, 16, BASE);
   SKILL_ADD(CLASS_PALADIN, 1, 85, 16, BASE);
   SKILL_ADD(CLASS_RANGER, 1, 80, 16, BASE);
   SKILL_ADD(CLASS_THIEF, 1, 50, 16, BASE);
   SKILL_ADD(CLASS_WARRIOR, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_ROGUE, 1, 50, 16, BASE);
   SKILL_ADD(CLASS_DIRERAIDER, 1, 80, 16, BASE);

   SKILL_CREATE("2h bludgeon", SKILL_2H_BLUDGEON, SKILL_TYPE_PHYS_STR);
   SKILL_ADD(CLASS_ANTIPALADIN, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_BERSERKER, 1, 95, 16, BASE);
   SKILL_ADD(CLASS_MERCENARY, 1, 60, 16, BASE);
   SKILL_ADD(CLASS_PALADIN, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_RANGER, 1, 50, 16, BASE);
   SKILL_ADD(CLASS_WARRIOR, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_CLERIC, 1, 80, 16, BASE);
   SKILL_ADD(CLASS_SHAMAN, 1, 50, 16, BASE);
   SKILL_ADD(CLASS_DIRERAIDER, 1, 50, 16, BASE);

   SKILL_CREATE("2h slashing", SKILL_2H_SLASHING, SKILL_TYPE_PHYS_STR);
   SKILL_ADD(CLASS_ANTIPALADIN, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_BERSERKER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_MERCENARY, 1, 60, 16, BASE);
   SKILL_ADD(CLASS_PALADIN, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_RANGER, 1, 50, 16, BASE);
   SKILL_ADD(CLASS_WARRIOR, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_DIRERAIDER, 1, 50, 16, BASE);

   SKILL_CREATE("2h misc", SKILL_2H_MISC, SKILL_TYPE_PHYS_STR);
   SKILL_ADD(CLASS_ANTIPALADIN, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_BERSERKER, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_MERCENARY, 1, 60, 16, BASE);
   SKILL_ADD(CLASS_PALADIN, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_RANGER, 1, 50, 16, BASE);
   SKILL_ADD(CLASS_WARRIOR, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_DIRERAIDER, 1, 50, 16, BASE);

   SKILL_CREATE("blindfighting", SKILL_BLINDFIGHTING, SKILL_TYPE_PHYS_DEX);
   SKILL_ADD(CLASS_WARRIOR, 10, 90, 16, BASE);
   SKILL_ADD(CLASS_PALADIN, 15, 90, 16, BASE);
   SKILL_ADD(CLASS_MONK, 5, 99, 16, BASE);
   SKILL_ADD(CLASS_ANTIPALADIN, 15, 90, 16, BASE);
   SKILL_ADD(CLASS_RANGER, 10, 85, 16, BASE);
   SKILL_ADD(CLASS_DIRERAIDER, 10, 85, 16, BASE);

   SKILL_CREATE("sneak", SKILL_SNEAK, SKILL_TYPE_PHYS_AGI);
   SKILL_ADD(CLASS_THIEF, 1, 90, 16, BASE);
   SKILL_ADD(CLASS_ASSASSIN, 1, 90, 16, BASE);
   SKILL_ADD(CLASS_MONK, 7, 80, 20, BASE);
   SKILL_ADD(CLASS_BARD, 20, 60, 16, BASE);
   SKILL_ADD(CLASS_BATTLECHANTER, 20, 60, 16, BASE);
   SKILL_ADD(CLASS_ROGUE, 1, 95, 16, BASE);

   SKILL_CREATE("hide", SKILL_HIDE, SKILL_TYPE_MENTAL_WIS);
   SKILL_ADD(CLASS_THIEF, 1, 90, 16, BASE);
   SKILL_ADD(CLASS_ASSASSIN, 1, 90, 16, BASE);
   SKILL_ADD(CLASS_MERCENARY, 1, 60, 20, BASE);
   SKILL_ADD(CLASS_ROGUE, 1, 95, 16, BASE);
   SKILL_ADD(CLASS_BARD, 10, 60, 20, BASE);
   SKILL_ADD(CLASS_BATTLECHANTER, 10, 60, 20, BASE);

   SKILL_CREATE("steal", SKILL_STEAL, SKILL_TYPE_PHYS_DEX);
   SKILL_ADD(CLASS_THIEF, 1, 95, 32, BASE);
   SKILL_ADD(CLASS_ROGUE, 1, 95, 32, BASE);

   SKILL_CREATE("backstab", SKILL_BACKSTAB, SKILL_TYPE_PHYS_DEX);
   SKILL_ADD(CLASS_THIEF, 1, 90, 20, BASE);
   SKILL_ADD(CLASS_ASSASSIN, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_MERCENARY, 5, 70, 25, BASE);
   SKILL_ADD(CLASS_ROGUE, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_BARD, 15, 50, 16, BASE);
   SKILL_ADD(CLASS_BATTLECHANTER, 15, 50, 16, BASE);

   SKILL_CREATE("pick lock", SKILL_PICK_LOCK, SKILL_TYPE_PHYS_DEX);
   SKILL_ADD(CLASS_THIEF, 1, 95, 16, BASE);
   /*  SKILL_ADD(CLASS_ASSASSIN, 1, 90, 16, BASE);   */
   SKILL_ADD(CLASS_ROGUE, 1, 95, 16, BASE);
   SKILL_ADD(CLASS_BARD, 30, 60, 30, BASE);
   SKILL_ADD(CLASS_BATTLECHANTER, 30, 60, 30, BASE);

   SKILL_CREATE("disarm", SKILL_DISARM, SKILL_TYPE_PHYS_DEX);
   /*  SKILL_ADD(CLASS_THIEF, 15, 90, 16, BASE);  */
   SKILL_ADD(CLASS_WARRIOR, 22, 90, 16, BASE);
   SKILL_ADD(CLASS_PALADIN, 22, 80, 16, BASE);
   SKILL_ADD(CLASS_ANTIPALADIN, 22, 70, 16, BASE);
   SKILL_ADD(CLASS_ASSASSIN, 12, 90, 16, BASE);
   SKILL_ADD(CLASS_ROGUE, 10, 90, 16, BASE);

   /* By Ilsie - trap stuff - 072998 */
   SKILL_CREATE("detect trap", SKILL_DETECT_TRAPS, SKILL_TYPE_PHYS_DEX);
   SKILL_ADD(CLASS_THIEF, 6, 95, 16, BASE);
   SKILL_ADD(CLASS_ROGUE, 5, 95, 16, BASE);

   SKILL_CREATE("disarm trap", SKILL_REMOVE_TRAPS, SKILL_TYPE_PHYS_DEX);
   SKILL_ADD(CLASS_THIEF, 12, 95, 20, BASE);
   SKILL_ADD(CLASS_ROGUE, 10, 95, 20, BASE);

   SKILL_CREATE("trip", SKILL_TRIP, SKILL_TYPE_PHYS_AGI);
   SKILL_ADD(CLASS_THIEF, 20, 90, 16, BASE);
   SKILL_ADD(CLASS_ROGUE, 25, 90, 16, BASE);

   SKILL_CREATE("escape", SKILL_ESCAPE, SKILL_TYPE_PHYS_AGI);
   SKILL_ADD(CLASS_THIEF, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_ASSASSIN, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_MERCENARY, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_ROGUE, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_BARD, 15, 70, 25, BASE);
   SKILL_ADD(CLASS_BATTLECHANTER, 15, 70, 25, BASE);

   SKILL_CREATE("kick", SKILL_KICK, SKILL_TYPE_PHYS_AGI);
   SKILL_ADD(CLASS_WARRIOR, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_BERSERKER, 1, 85, 16, BASE);
   SKILL_ADD(CLASS_RANGER, 1, 80, 16, BASE);
   SKILL_ADD(CLASS_MERCENARY, 1, 85, 16, BASE);
   SKILL_ADD(CLASS_MONK, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_ANTIPALADIN, 1, 85, 16, BASE);

   SKILL_CREATE("bash", SKILL_BASH, SKILL_TYPE_PHYS_STR);
   SKILL_ADD(CLASS_WARRIOR, 1, 99, 36, BASE);
   SKILL_ADD(CLASS_PALADIN, 1, 99, 36, BASE);
   SKILL_ADD(CLASS_ANTIPALADIN, 1, 99, 36, BASE);
   SKILL_ADD(CLASS_RANGER, 1, 50, 36, BASE);
   SKILL_ADD(CLASS_MERCENARY, 1, 50, 36, BASE);
   SKILL_ADD(CLASS_BERSERKER, 5, 90, 36, BASE);
   SKILL_ADD(CLASS_DIRERAIDER, 1, 50, 36, BASE);

   SKILL_CREATE("rescue", SKILL_RESCUE, SKILL_TYPE_PHYS_AGI);
   SKILL_ADD(CLASS_WARRIOR, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_PALADIN, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_ANTIPALADIN, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_RANGER, 1, 50, 22, BASE);
   SKILL_ADD(CLASS_MERCENARY, 1, 50, 22, BASE);
   SKILL_ADD(CLASS_DIRERAIDER, 1, 50, 22, BASE);

   SKILL_CREATE("trap", SKILL_TRAP, SKILL_TYPE_PHYS_DEX);
   SKILL_ADD(CLASS_THIEF, 5, 90, 16, BASE);
   /* SKILL_ADD(CLASS_ASSASSIN, 5, 90, 16, BASE);  */
   /*  SKILL_ADD(CLASS_MERCENARY, 5, 90, 16, BASE);  */
   SKILL_ADD(CLASS_RANGER, 5, 80, 16, BASE);
   SKILL_ADD(CLASS_DIRERAIDER, 5, 80, 16, BASE);

   SKILL_CREATE("track", SKILL_TRACK, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_RANGER, 20, 99, 16, BASE);
   /*  SKILL_ADD(CLASS_THIEF, 25, 90, 16, BASE); */
   SKILL_ADD(CLASS_ASSASSIN, 25, 90, 16, BASE);
   SKILL_ADD(CLASS_ROGUE, 20, 90, 16, BASE);
   SKILL_ADD(CLASS_DIRERAIDER, 20, 99, 16, BASE);

   SKILL_CREATE("listen", SKILL_LISTEN, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_THIEF, 20, 90, 16, BASE);
   SKILL_ADD(CLASS_ASSASSIN, 10, 90, 16, BASE);
   SKILL_ADD(CLASS_BARD, 15, 90, 16, BASE);
   SKILL_ADD(CLASS_BATTLECHANTER, 15, 90, 16, BASE);

   SKILL_CREATE("double attack", SKILL_DOUBLE_ATTACK, SKILL_TYPE_PHYS_STR);
   SKILL_ADD(CLASS_WARRIOR, 15, 99, 16, BASE);
   SKILL_ADD(CLASS_BERSERKER, 25, 85, 16, BASE);
   SKILL_ADD(CLASS_PALADIN, 20, 99, 16, BASE);
   SKILL_ADD(CLASS_ANTIPALADIN, 20, 99, 16, BASE);
   SKILL_ADD(CLASS_MERCENARY, 20, 75, 16, BASE);
   SKILL_ADD(CLASS_RANGER, 20, 75, 16, BASE);
   SKILL_ADD(CLASS_ASSASSIN, 30, 60, 16, BASE);
   SKILL_ADD(CLASS_ROGUE, 30, 60, 16, BASE);
   SKILL_ADD(CLASS_BARD, 30, 50, 16, BASE);
   SKILL_ADD(CLASS_BATTLECHANTER, 30, 50, 16, BASE);
   SKILL_ADD(CLASS_DIRERAIDER, 20, 75, 16, BASE);

   SKILL_CREATE("dual wield", SKILL_DUAL_WIELD, SKILL_TYPE_PHYS_DEX);
   SKILL_ADD(CLASS_WARRIOR, 20, 50, 16, BASE);
   SKILL_ADD(CLASS_RANGER, 10, 99, 16, BASE);
   SKILL_ADD(CLASS_ASSASSIN, 15, 99, 16, BASE);
   SKILL_ADD(CLASS_THIEF, 22, 99, 16, BASE);
   SKILL_ADD(CLASS_MERCENARY, 24, 50, 16, BASE);
   SKILL_ADD(CLASS_ROGUE, 15, 99, 16, BASE);
   SKILL_ADD(CLASS_BARD, 20, 50, 25, BASE);
   SKILL_ADD(CLASS_BATTLECHANTER, 20, 50, 25, BASE);
   SKILL_ADD(CLASS_DIRERAIDER, 10, 99, 16, BASE);

   SKILL_CREATE("hitall", SKILL_HITALL, SKILL_TYPE_PHYS_DEX);
   SKILL_ADD(CLASS_WARRIOR, 15, 99, 16, BASE);
   SKILL_ADD(CLASS_PALADIN, 15, 50, 16, BASE);
   SKILL_ADD(CLASS_ANTIPALADIN, 15, 70, 16, BASE);
   SKILL_ADD(CLASS_BERSERKER, 10, 85, 16, BASE);

   SKILL_CREATE("berserk", SKILL_BERSERK, SKILL_TYPE_PHYS_STR);
   SKILL_ADD(CLASS_BERSERKER, 7, 98, 16, BASE);

   SKILL_CREATE("defense", SKILL_DEFENSE, SKILL_TYPE_PHYS_STR);
   SKILL_ADD(CLASS_WARRIOR, 1, 90, 16, BASE);
   SKILL_ADD(CLASS_PALADIN, 1, 90, 16, BASE);
   SKILL_ADD(CLASS_ANTIPALADIN, 1, 90, 16, BASE);
   SKILL_ADD(CLASS_RANGER, 1, 80, 16, BASE);
   SKILL_ADD(CLASS_DIRERAIDER, 1, 80, 16, BASE);
   SKILL_ADD(CLASS_ROGUE, 1, 65, 16, BASE);
   SKILL_ADD(CLASS_BARD, 1, 65, 16, BASE);
   SKILL_ADD(CLASS_BATTLECHANTER, 1, 65, 16, BASE);

   SKILL_CREATE("parry", SKILL_PARRY, SKILL_TYPE_PHYS_STR);
   SKILL_ADD(CLASS_BERSERKER, 24, 75, 16, BASE);
   SKILL_ADD(CLASS_WARRIOR, 5, 99, 16, BASE);
   SKILL_ADD(CLASS_PALADIN, 12, 99, 16, BASE);
   SKILL_ADD(CLASS_MERCENARY, 16, 70, 20, BASE);
   SKILL_ADD(CLASS_RANGER, 14, 80, 16, BASE);
   SKILL_ADD(CLASS_ANTIPALADIN, 12, 99, 16, BASE);
   SKILL_ADD(CLASS_DIRERAIDER, 14, 80, 16, BASE);

   SKILL_CREATE("riposte", SKILL_RIPOSTE, SKILL_TYPE_PHYS_DEX);
   SKILL_ADD(CLASS_PALADIN, 25, 99, 16, BASE);
   SKILL_ADD(CLASS_ANTIPALADIN, 25, 99, 16, BASE);
   SKILL_ADD(CLASS_RANGER, 30, 85, 16, BASE);
   SKILL_ADD(CLASS_MERCENARY, 35, 60, 20, BASE);
   SKILL_ADD(CLASS_WARRIOR, 20, 99, 16, BASE);
   SKILL_ADD(CLASS_DIRERAIDER, 30, 85, 16, BASE);

   SKILL_CREATE("surprise", SKILL_SURPRISE, SKILL_TYPE_MENTAL_WIS);
   SKILL_ADD(CLASS_RANGER, 15, 80, 8, BASE);
   SKILL_ADD(CLASS_DIRERAIDER, 15, 80, 8, BASE);

   SKILL_CREATE("headbutt", SKILL_HEADBUTT, SKILL_TYPE_PHYS_STR);
   SKILL_ADD(CLASS_MERCENARY, 20, 85, 16, BASE);
   SKILL_ADD(CLASS_WARRIOR, 40, 99, 16, BASE);

   SKILL_CREATE("meditate", SKILL_MEDITATE, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_PALADIN, 10, 90, 16, BASE);
   SKILL_ADD(CLASS_ANTIPALADIN, 10, 90, 16, BASE);
   SKILL_ADD(CLASS_SORCERER, 1, 90, 16, BASE);
   SKILL_ADD(CLASS_NECROMANCER, 1, 90, 16, BASE);
   SKILL_ADD(CLASS_LICH, 1, 90, 16, BASE);
   SKILL_ADD(CLASS_CONJURER, 1, 90, 16, BASE);
   SKILL_ADD(CLASS_SHAMAN, 1, 90, 16, BASE);
   SKILL_ADD(CLASS_CLERIC, 1, 90, 16, BASE);
   SKILL_ADD(CLASS_DRUID, 1, 90, 16, BASE);
   SKILL_ADD(CLASS_RANGER, 10, 90, 16, BASE);
   SKILL_ADD(CLASS_PSIONICIST, 1, 98, 16, BASE);
   SKILL_ADD(CLASS_INVOKER, 1, 90, 16, BASE);
   SKILL_ADD(CLASS_ENCHANTER, 1, 90, 16, BASE);
   SKILL_ADD(CLASS_ILLUSIONIST, 1, 90, 16, BASE);
   SKILL_ADD(CLASS_ELEMENTALIST, 1, 90, 16, BASE);
   SKILL_ADD(CLASS_DIRERAIDER, 10, 90, 16, BASE);

   SKILL_CREATE("apply poison", SKILL_APPLY_POISON, SKILL_TYPE_PHYS_DEX);
   SKILL_ADD(CLASS_ASSASSIN, 15, 90, 16, BASE);
   SKILL_ADD(CLASS_ROGUE, 20, 95, 16, BASE);
   SKILL_ADD(CLASS_ANTIPALADIN, 30, 60, 16, BASE);

   SKILL_CREATE("shadow", SKILL_SHADOW, SKILL_TYPE_PHYS_AGI);
   SKILL_ADD(CLASS_ASSASSIN, 20, 90, 16, BASE);
   SKILL_ADD(CLASS_ROGUE, 25, 95, 16, BASE);

   SKILL_CREATE("instant kill", SKILL_INSTANT_KILL, SKILL_TYPE_PHYS_DEX);
   /*  SKILL_ADD(CLASS_ASSASSIN, 30, 90, 64, BASE);  */

   SKILL_CREATE("missile snare", SKILL_MISSILE_SNARE, SKILL_TYPE_PHYS_DEX);
   SKILL_ADD(CLASS_RANGER, 30, 30, 16, BASE);
   SKILL_ADD(CLASS_MONK, 10, 90, 16, BASE);
   SKILL_ADD(CLASS_THIEF, 25, 70, 16, BASE);
   SKILL_ADD(CLASS_ROGUE, 15, 70, 16, BASE);
   SKILL_ADD(CLASS_DIRERAIDER, 30, 30, 16, BASE);

   /* New, for warriors only.  kick-type skill.. */
   SKILL_CREATE("shieldpunch", SKILL_SHIELDPUNCH, SKILL_TYPE_PHYS_STR);
   SKILL_ADD(CLASS_WARRIOR, 5, 90, 16, BASE);

   /* New, for warriors only.  Dodge-type skill.. */
   SKILL_CREATE("shieldblock", SKILL_SHIELDBLOCK, SKILL_TYPE_PHYS_STR);
   SKILL_ADD(CLASS_WARRIOR, 5, 99, 16, BASE);

   /* Thief/Assasin skill, still in testing - CRM */
   SKILL_CREATE("disguise", SKILL_DISGUISE, SKILL_TYPE_MENTAL_INT);
   /* SKILL_ADD(CLASS_ASSASSIN, 20, 75, 16, BASE); */
   SKILL_ADD(CLASS_THIEF, 30, 90, 16, BASE);
   SKILL_ADD(CLASS_ROGUE, 30, 95, 16, BASE);

   /* Assassin Skill, not in yet --CRM */
   SKILL_CREATE("vital strike", SKILL_VITAL_STRIKE, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_ASSASSIN, 30, 90, 16, BASE);
   SKILL_ADD(CLASS_ROGUE, 30, 95, 16, BASE);

   /* new assassin skills, not in yet.  --DMB */
   SKILL_CREATE("assassinate", SKILL_ASSASSINATE, SKILL_TYPE_PHYS_DEX);
   SKILL_ADD(CLASS_ASSASSIN, 40, 95, 64, BASE);
   SKILL_ADD(CLASS_ROGUE, 40, 95, 64, BASE);

   SKILL_CREATE("garrote", SKILL_GARROTE, SKILL_TYPE_PHYS_DEX);
   SKILL_ADD(CLASS_ASSASSIN, 35, 95, 32, BASE);
   SKILL_ADD(CLASS_ROGUE, 35, 95, 32, BASE);

   SKILL_CREATE("evasion", SKILL_EVASION, SKILL_TYPE_PHYS_AGI);
   SKILL_ADD(CLASS_ROGUE, 5, 95, 16, BASE);
   SKILL_ADD(CLASS_BARD, 25, 70, 25, BASE);
   SKILL_ADD(CLASS_BATTLECHANTER, 25, 70, 25, BASE);

   /* Thief / Assassin / Bard skill --DMB */
   SKILL_CREATE("unbind", SKILL_UNBIND, SKILL_TYPE_PHYS_DEX);
   SKILL_ADD(CLASS_ANTIPALADIN, 15, 65, 16, BASE);
   SKILL_ADD(CLASS_ASSASSIN, 5, 80, 16, BASE);
   SKILL_ADD(CLASS_BARD, 10, 80, 20, BASE);
   SKILL_ADD(CLASS_BATTLECHANTER, 10, 80, 20, BASE);
   SKILL_ADD(CLASS_BERSERKER, 10, 50, 16, BASE);
   SKILL_ADD(CLASS_CLERIC, 20, 50, 16, BASE);
   SKILL_ADD(CLASS_CONJURER, 25, 25, 16, BASE);
   SKILL_ADD(CLASS_DRUID, 20, 35, 16, BASE);
   SKILL_ADD(CLASS_MERCENARY, 10, 70, 16, BASE);
   SKILL_ADD(CLASS_MONK, 1, 95, 16, BASE);
   SKILL_ADD(CLASS_NECROMANCER, 25, 25, 16, BASE);
   SKILL_ADD(CLASS_LICH, 25, 25, 16, BASE);
   SKILL_ADD(CLASS_PALADIN, 15, 65, 16, BASE);
   SKILL_ADD(CLASS_RANGER, 12, 60, 16, BASE);
   SKILL_ADD(CLASS_SHAMAN, 18, 40, 16, BASE);
   SKILL_ADD(CLASS_SORCERER, 25, 25, 16, BASE);
   SKILL_ADD(CLASS_THIEF, 1, 99, 16, BASE);
   SKILL_ADD(CLASS_WARRIOR, 8, 65, 16, BASE);
   SKILL_ADD(CLASS_PSIONICIST, 10, 45, 16, BASE);
   SKILL_ADD(CLASS_INVOKER, 25, 25, 16, BASE);
   SKILL_ADD(CLASS_ENCHANTER, 25, 25, 16, BASE);
   SKILL_ADD(CLASS_ROGUE, 5, 99, 16, BASE);
   SKILL_ADD(CLASS_ILLUSIONIST, 25, 25, 16, BASE);
   SKILL_ADD(CLASS_ELEMENTALIST, 25, 25, 16, BASE);
   SKILL_ADD(CLASS_DIRERAIDER, 12, 60, 16, BASE);

#if 0
   SKILL_CREATE("mix", SKILL_MIX, SKILL_TYPE_MENTAL_WIS);
   SKILL_ADD(CLASS_SHAMAN, 26, 99, 16, BASE);
#endif

   SKILL_CREATE("dodge", SKILL_DODGE, SKILL_TYPE_PHYS_AGI);
   SKILL_ADD(CLASS_ANTIPALADIN, 15, 65, 16, BASE);
   SKILL_ADD(CLASS_ASSASSIN, 7, 75, 16, BASE);
   SKILL_ADD(CLASS_BARD, 5, 85, 20, BASE);
   SKILL_ADD(CLASS_BATTLECHANTER, 5, 85, 20, BASE);
   SKILL_ADD(CLASS_BERSERKER, 10, 50, 16, BASE);
   SKILL_ADD(CLASS_CLERIC, 20, 50, 16, BASE);
   SKILL_ADD(CLASS_CONJURER, 25, 25, 16, BASE);
   SKILL_ADD(CLASS_DRUID, 20, 35, 16, BASE);
   SKILL_ADD(CLASS_MERCENARY, 10, 70, 16, BASE);
   SKILL_ADD(CLASS_MONK, 1, 95, 16, BASE);
   SKILL_ADD(CLASS_NECROMANCER, 25, 25, 16, BASE);
   SKILL_ADD(CLASS_LICH, 25, 25, 16, BASE);
   SKILL_ADD(CLASS_PALADIN, 15, 65, 16, BASE);
   SKILL_ADD(CLASS_RANGER, 12, 60, 16, BASE);
   SKILL_ADD(CLASS_SHAMAN, 18, 40, 16, BASE);
   SKILL_ADD(CLASS_SORCERER, 25, 25, 16, BASE);
   SKILL_ADD(CLASS_THIEF, 3, 90, 16, BASE);
   SKILL_ADD(CLASS_WARRIOR, 8, 65, 16, BASE);
   SKILL_ADD(CLASS_PSIONICIST, 10, 45, 16, BASE);
   SKILL_ADD(CLASS_INVOKER, 25, 25, 16, BASE);
   SKILL_ADD(CLASS_ENCHANTER, 25, 25, 16, BASE);
   SKILL_ADD(CLASS_ROGUE, 1, 90, 16, BASE);
   SKILL_ADD(CLASS_ILLUSIONIST, 25, 25, 16, BASE);
   SKILL_ADD(CLASS_ELEMENTALIST, 25, 25, 16, BASE);
   SKILL_ADD(CLASS_DIRERAIDER, 12, 60, 16, BASE);

   SKILL_CREATE("mount", SKILL_MOUNT, SKILL_TYPE_PHYS_CON);
   SKILL_ADD(CLASS_MERCENARY, 1, 90, 16, BASE);
   SKILL_ADD(CLASS_RANGER, 1, 90, 16, BASE);
   SKILL_ADD(CLASS_PALADIN, 1, 90, 16, BASE);
   SKILL_ADD(CLASS_ANTIPALADIN, 1, 90, 16, BASE);
   SKILL_ADD(CLASS_WARRIOR, 1, 90, 16, BASE);
   SKILL_ADD(CLASS_BERSERKER, 1, 90, 16, BASE);
   SKILL_ADD(CLASS_SORCERER, 1, 90, 16, BASE);
   SKILL_ADD(CLASS_CLERIC, 1, 90, 16, BASE);
   SKILL_ADD(CLASS_DRUID, 1, 90, 16, BASE);
   SKILL_ADD(CLASS_CONJURER, 1, 90, 16, BASE);
   SKILL_ADD(CLASS_NECROMANCER, 1, 90, 16, BASE);
   SKILL_ADD(CLASS_LICH, 1, 90, 16, BASE);
   SKILL_ADD(CLASS_THIEF, 1, 90, 16, BASE);
   SKILL_ADD(CLASS_SHAMAN, 1, 90, 16, BASE);
   SKILL_ADD(CLASS_ASSASSIN, 1, 90, 16, BASE);
   SKILL_ADD(CLASS_MONK, 1, 90, 16, BASE);
   SKILL_ADD(CLASS_BARD, 1, 90, 16, BASE);
   SKILL_ADD(CLASS_BATTLECHANTER, 1, 90, 16, BASE);
   SKILL_ADD(CLASS_PSIONICIST, 1, 90, 16, BASE);
   SKILL_ADD(CLASS_INVOKER, 1, 90, 16, BASE);
   SKILL_ADD(CLASS_ENCHANTER, 1, 90, 16, BASE);
   SKILL_ADD(CLASS_ROGUE, 1, 90, 16, BASE);
   SKILL_ADD(CLASS_ILLUSIONIST, 1, 90, 16, BASE);
   SKILL_ADD(CLASS_ELEMENTALIST, 1, 90, 16, BASE);
   SKILL_ADD(CLASS_DIRERAIDER, 1, 90, 16, BASE);

   SKILL_CREATE("mounted combat", SKILL_MOUNTED_COMBAT, SKILL_TYPE_PHYS_CON);
   SKILL_ADD(CLASS_PALADIN, 10, 99, 16, BASE);
   SKILL_ADD(CLASS_ANTIPALADIN, 10, 99, 16, BASE);
   SKILL_ADD(CLASS_RANGER, 20, 20, 16, BASE);
   SKILL_ADD(CLASS_DIRERAIDER, 10, 99, 16, BASE);

   SKILL_CREATE("bandage", SKILL_BANDAGE, SKILL_TYPE_MENTAL_WIS);
   SKILL_ADD(CLASS_MERCENARY, 1, 90, 90, BASE);
   SKILL_ADD(CLASS_RANGER, 1, 90, 90, BASE);
   SKILL_ADD(CLASS_PALADIN, 1, 90, 90, BASE);
   SKILL_ADD(CLASS_ANTIPALADIN, 1, 90, 90, BASE);
   SKILL_ADD(CLASS_WARRIOR, 1, 90, 90, BASE);
   SKILL_ADD(CLASS_BERSERKER, 1, 90, 90, BASE);
   SKILL_ADD(CLASS_SORCERER, 1, 90, 90, BASE);
   SKILL_ADD(CLASS_CLERIC, 1, 90, 90, BASE);
   SKILL_ADD(CLASS_DRUID, 1, 90, 90, BASE);
   SKILL_ADD(CLASS_CONJURER, 1, 90, 90, BASE);
   SKILL_ADD(CLASS_NECROMANCER, 1, 90, 90, BASE);
   SKILL_ADD(CLASS_LICH, 1, 90, 90, BASE);
   SKILL_ADD(CLASS_THIEF, 1, 90, 90, BASE);
   SKILL_ADD(CLASS_SHAMAN, 1, 90, 90, BASE);
   SKILL_ADD(CLASS_ASSASSIN, 1, 90, 90, BASE);
   SKILL_ADD(CLASS_MONK, 1, 90, 90, BASE);
   SKILL_ADD(CLASS_BARD, 1, 90, 90, BASE);
   SKILL_ADD(CLASS_BATTLECHANTER, 1, 90, 90, BASE);
   SKILL_ADD(CLASS_PSIONICIST, 1, 90, 90, BASE);
   SKILL_ADD(CLASS_INVOKER, 1, 90, 90, BASE);
   SKILL_ADD(CLASS_ENCHANTER, 1, 90, 90, BASE);
   SKILL_ADD(CLASS_ROGUE, 1, 90, 90, BASE);
   SKILL_ADD(CLASS_ILLUSIONIST, 1, 90, 90, BASE);
   SKILL_ADD(CLASS_ELEMENTALIST, 1, 90, 90, BASE);
   SKILL_ADD(CLASS_DIRERAIDER, 1, 90, 90, BASE);

   SKILL_CREATE("awareness", SKILL_AWARENESS, SKILL_TYPE_MENTAL_INT);
   SKILL_ADD(CLASS_RANGER, 15, 70, 16, BASE);
   SKILL_ADD(CLASS_ASSASSIN, 10, 85, 16, BASE);
   SKILL_ADD(CLASS_ROGUE, 5, 85, 16, BASE);
   SKILL_ADD(CLASS_BARD, 10, 50, 16, BASE);
   SKILL_ADD(CLASS_BATTLECHANTER, 5, 50, 16, BASE);
   SKILL_ADD(CLASS_DIRERAIDER, 15, 70, 16, BASE);

   /* special, was easier to make them skills, but not assigned to any  class, nor is it checked for, but
      this keeps the various arrays, indices, etc, from barfing */

   SKILL_CREATE("constitution hits", SKILL_CON_BONUS, SKILL_TYPE_NONE);
   SKILL_CREATE("establish camp", SKILL_CAMP, SKILL_TYPE_NONE);
   SKILL_CREATE("fumbling weapon", SKILL_DISARM_FUMBLING_WEAP, SKILL_TYPE_NONE);
   SKILL_CREATE("dropped weapon", SKILL_DISARM_DROPPED_WEAP, SKILL_TYPE_NONE);
   SKILL_CREATE("engaged in pkill", SKILL_PKILL_TIMEOUT, SKILL_TYPE_NONE);


   /* CLAIRSENTIENCE */

   SKILL_CREATE("mindblast", SKILL_MINDBLAST, SKILL_TYPE_CLAIRSENTIENCE);
   SKILL_ADD(CLASS_PSIONICIST, 1, 99, 16, BASE);

   SKILL_CREATE("combatmind", SKILL_COMBATMIND, SKILL_TYPE_CLAIRSENTIENCE);
   SKILL_ADD(CLASS_PSIONICIST, 1, 99, 16, BASE);

   SKILL_CREATE("aurasight", SKILL_AURASIGHT, SKILL_TYPE_CLAIRSENTIENCE);
   SKILL_ADD(CLASS_PSIONICIST, 1, 99, 16, BASE);

   SKILL_CREATE("danger sense", SKILL_SENSE_DANGER, SKILL_TYPE_CLAIRSENTIENCE);
   SKILL_ADD(CLASS_PSIONICIST, 4, 99, 16, BASE);

   SKILL_CREATE("vipermind", SKILL_VIPER_MIND, SKILL_TYPE_CLAIRSENTIENCE);
   /* PSYCHOKINESIS */


   SKILL_CREATE("project force", SKILL_PROJECT_FORCE, SKILL_TYPE_PSYCHOKINESIS);
   SKILL_ADD(CLASS_PSIONICIST, 14, 99, 16, BASE);

   SKILL_CREATE("detonate", SKILL_DETONATE, SKILL_TYPE_PSYCHOKINESIS);
   SKILL_ADD(CLASS_PSIONICIST, 9, 99, 16, BASE);

   /* PSYCHOMETABOLISM */

   SKILL_CREATE("adrenalize", SKILL_ADRENALIZE, SKILL_TYPE_PSYCHOMETABOLISM);
   SKILL_ADD(CLASS_PSIONICIST, 12, 99, 16, BASE);

   SKILL_CREATE("body control", SKILL_BODY_CONTROL, SKILL_TYPE_PSYCHOMETABOLISM);
   SKILL_ADD(CLASS_PSIONICIST, 15, 99, 16, BASE);

   SKILL_CREATE("catfall", SKILL_CATFALL, SKILL_TYPE_PSYCHOMETABOLISM);
   SKILL_ADD(CLASS_PSIONICIST, 15, 99, 16, BASE);

   SKILL_CREATE("sustain", SKILL_SUSTAIN, SKILL_TYPE_PSYCHOMETABOLISM);
   SKILL_ADD(CLASS_PSIONICIST, 15, 99, 16, BASE);

   SKILL_CREATE("flesh armor", SKILL_FLESH_ARMOR, SKILL_TYPE_PSYCHOMETABOLISM);
   SKILL_ADD(CLASS_PSIONICIST, 17, 99, 16, BASE);

   SKILL_CREATE("reduction", SKILL_REDUCTION, SKILL_TYPE_PSYCHOMETABOLISM);
   SKILL_ADD(CLASS_PSIONICIST, 17, 99, 16, BASE);

   SKILL_CREATE("expansion", SKILL_EXPANSION, SKILL_TYPE_PSYCHOMETABOLISM);
   SKILL_ADD(CLASS_PSIONICIST, 17, 99, 16, BASE);

   SKILL_CREATE("equalibrium", SKILL_EQUALIBRIUM, SKILL_TYPE_PSYCHOMETABOLISM);
   SKILL_ADD(CLASS_PSIONICIST, 20, 99, 16, BASE);

   SKILL_CREATE("deathfield", SKILL_DEATH_FIELD, SKILL_TYPE_PSYCHOMETABOLISM);
   SKILL_ADD(CLASS_PSIONICIST, 20, 99, 16, BASE);

   SKILL_CREATE("scale skin", SKILL_SCALE_SKIN, SKILL_TYPE_PSYCHOMETABOLISM);

   /* PSYCHOPORTATION */

   SKILL_CREATE("planar rift", SKILL_RIFT, SKILL_TYPE_PSYCHOPORTATION);
   SKILL_ADD(CLASS_PSIONICIST, 30, 99, 16, LEARN);

   SKILL_CREATE("shift", SKILL_SHIFT, SKILL_TYPE_PSYCHOPORTATION);
   SKILL_ADD(CLASS_PSIONICIST, 41, 99, 16, BASE);

   /* TELEPATHY */

   SKILL_CREATE("dominate", SKILL_DOMINATE, SKILL_TYPE_TELEPATHY);
   SKILL_ADD(CLASS_PSIONICIST, 23, 99, 16, BASE);

   SKILL_CREATE("mass domination", SKILL_MASS_DOMINATION, SKILL_TYPE_TELEPATHY);
   SKILL_ADD(CLASS_PSIONICIST, 26, 99, 16, BASE);

   SKILL_CREATE("synaptic static", SKILL_SYNAPTIC_STATIC, SKILL_TYPE_TELEPATHY);
   SKILL_ADD(CLASS_PSIONICIST, 31, 99, 16, BASE);

   SKILL_CREATE("tower of iron will", SKILL_TOWER_OF_IRON_WILL, SKILL_TYPE_TELEPATHY);
   SKILL_ADD(CLASS_PSIONICIST, 37, 99, 16, BASE);


   /* METAPSIONICS */

   SKILL_CREATE("globe of darkness", SKILL_DARKNESS, SKILL_TYPE_METAPSIONICS);
   SKILL_ADD(CLASS_PSIONICIST, 35, 99, 16, LEARN);

   SKILL_CREATE("canibalize", SKILL_CANIBALIZE, SKILL_TYPE_METAPSIONICS);
   SKILL_ADD(CLASS_PSIONICIST, 40, 99, 16, BASE);

   SKILL_CREATE("battle trance", SKILL_BATTLE_TRANCE, SKILL_TYPE_METAPSIONICS);
   SKILL_ADD(CLASS_PSIONICIST, 45, 99, 16, LEARN);

   SKILL_CREATE("stasis field", SKILL_STASIS_FIELD, SKILL_TYPE_METAPSIONICS);
   SKILL_ADD(CLASS_PSIONICIST, 46, 99, 16, BASE);

   SKILL_CREATE("ultrablast", SKILL_ULTRABLAST, SKILL_TYPE_METAPSIONICS);
   SKILL_ADD(CLASS_PSIONICIST, 47, 99, 16, LEARN);

   SKILL_CREATE("globe", SKILL_METAGLOBE, SKILL_TYPE_METAPSIONICS);
   SKILL_ADD(CLASS_PSIONICIST, 47, 99, 16, LEARN);

   SKILL_CREATE("interference shield", SKILL_INTERFERENCE_SHIELD, SKILL_TYPE_METAPSIONICS); /* for our beloved DROW Ilsie 060298 */

   SKILL_CREATE("charge", SKILL_CHARGE, SKILL_TYPE_METAPSIONICS);
   SKILL_ADD(CLASS_PSIONICIST, 9, 99, 16, BASE);

   SKILL_CREATE("swimming", SKILL_SWIMMING, SKILL_TYPE_PHYS_STR);
   SKILL_ADD(CLASS_ANTIPALADIN, 1, 65, 16, BASE);
   SKILL_ADD(CLASS_ASSASSIN, 1, 90, 16, BASE);
   SKILL_ADD(CLASS_BARD, 1, 95, 50, BASE);
   SKILL_ADD(CLASS_BATTLECHANTER, 1, 95, 50, BASE);
   SKILL_ADD(CLASS_BERSERKER, 1, 85, 16, BASE);
   SKILL_ADD(CLASS_CLERIC, 1, 50, 16, BASE);
   SKILL_ADD(CLASS_CONJURER, 1, 50, 16, BASE);
   SKILL_ADD(CLASS_DRUID, 1, 50, 16, BASE);
   SKILL_ADD(CLASS_MERCENARY, 1, 80, 16, BASE);
   SKILL_ADD(CLASS_MONK, 1, 95, 16, BASE);
   SKILL_ADD(CLASS_NECROMANCER, 1, 50, 16, BASE);
   SKILL_ADD(CLASS_LICH, 1, 50, 16, BASE);
   SKILL_ADD(CLASS_PALADIN, 1, 65, 16, BASE);
   SKILL_ADD(CLASS_RANGER, 1, 90, 16, BASE);
   SKILL_ADD(CLASS_SHAMAN, 1, 50, 16, BASE);
   SKILL_ADD(CLASS_SORCERER, 1, 50, 16, BASE);
   SKILL_ADD(CLASS_THIEF, 1, 95, 16, BASE);
   SKILL_ADD(CLASS_WARRIOR, 1, 90, 16, BASE);
   SKILL_ADD(CLASS_PSIONICIST, 1, 50, 16, BASE);
   SKILL_ADD(CLASS_INVOKER, 1, 50, 16, BASE);
   SKILL_ADD(CLASS_ENCHANTER, 1, 50, 16, BASE);
   SKILL_ADD(CLASS_ROGUE, 1, 95, 16, BASE);
   SKILL_ADD(CLASS_ILLUSIONIST, 1, 50, 16, BASE);
   SKILL_ADD(CLASS_ELEMENTALIST, 1, 50, 16, BASE);
   SKILL_ADD(CLASS_DIRERAIDER, 1, 90, 16, BASE);

   SPELL_CREATE("embalm", SPELL_EMBALM, SPELLTYPE_GENERIC, PULSE_VIOLENCE * 2, FALSE, TAR_OBJ_ROOM, 0, 0, cast_embalm);
   SPELL_ADD(CLASS_LICH, 4, BASE);

#ifdef NEW_NECRO
   SPELL_CREATE("rot", SPELL_ROT, SPELLTYPE_NECROMANCY, PULSE_VIOLENCE * 4, TRUE, TAR_IGNORE, 1, 10, cast_rot);
#else
   SPELL_CREATE("rot", SPELL_ROT, SPELLTYPE_NECROMANCY, PULSE_VIOLENCE * 9 / 2, TRUE, TAR_IGNORE, 1, 10, cast_rot);
#endif
   SPELL_ADD(CLASS_LICH, 10, BASE);

   SPELL_CREATE("lich touch", SPELL_LICH_TOUCH, SPELLTYPE_NECROMANCY, PULSE_VIOLENCE * 2, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT, 1, 16, cast_lich_touch);
   SPELL_ADD(CLASS_LICH, 9, BASE);

   SPELL_CREATE("life drain", SPELL_LIFE_DRAIN, SPELLTYPE_NECROMANCY, PULSE_VIOLENCE, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT, 1, 11, cast_life_drain);
   SPELL_ADD(CLASS_LICH, 8, BASE);

   SPELL_CREATE("ice tomb", SPELL_ICE_TOMB, SPELLTYPE_NECROMANCY, PULSE_VIOLENCE * 2, TRUE, TAR_CHAR_ROOM | TAR_FIGHT_VICT, 1, 16, cast_ice_tomb);
   SPELL_ADD(CLASS_LICH, 10, BASE);

   SPELL_CREATE("locate remains", SPELL_LOCATE_REMAINS, SPELLTYPE_DIVINATION, PULSE_VIOLENCE * 2, FALSE, TAR_IGNORE, 0, 0, cast_locate_remains);
   SPELL_ADD(CLASS_LICH, 7, BASE);

   SPELL_CREATE("tazriks frenzied hound", SPELL_TAZRIKS, SPELLTYPE_SUMMONING, PULSE_VIOLENCE * 3, TRUE, TAR_IGNORE, 1, 13,  cast_tazriks_frenzied_hound);
   SPELL_ADD(CLASS_ANTIPALADIN, 8, BASE);

   SPELL_CREATE("dark wrath", SPELL_DARK_WRATH, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE, FALSE, TAR_SELF_ONLY, 0, 0, cast_dark_wrath);
   SPELL_ADD(CLASS_ANTIPALADIN, 9, BASE);

   SPELL_CREATE("unholy aura", SPELL_UNHOLY_AURA, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE, TRUE, TAR_IGNORE, 0, 0, cast_unholy_aura);
   SPELL_ADD(CLASS_ANTIPALADIN, 10, BASE);

   SPELL_CREATE("holy shroud", SPELL_HOLY_SHROUD, SPELLTYPE_ENCHANTMENT, PULSE_VIOLENCE, TRUE, TAR_SELF_ONLY, 0, 0, cast_holy_shroud);
   SPELL_ADD(CLASS_PALADIN, 9, BASE);

#if 0
   /* Currently Unused Spells and Skills --CRM */
   SPELL_CREATE("xxxreconstruction", SPELL_RECONSTRUCTION, SPELLTYPE_DIVINATION, PULSE_VIOLENCE * 2, FALSE, TAR_IGNORE, 0, 0, cast_spell_stub);

   SPELL_CREATE("xxxreanimate flesh", SPELL_REANIMATE_FLESH, SPELLTYPE_DIVINATION, PULSE_VIOLENCE * 2, FALSE, TAR_IGNORE, 0, 0, cast_spell_stub);
#endif
#if 0
   SPELL_CREATE("xxxlich curse", SPELL_LICH_CURSE, SPELLTYPE_DIVINATION, PULSE_VIOLENCE * 2, FALSE, TAR_IGNORE, 0, 0, cast_spell_stub);
#endif
   SPELL_CREATE("xxxrecharger", SPELL_RECHARGER, SPELLTYPE_GENERIC, PULSE_VIOLENCE * 2, TRUE, TAR_CHAR_ROOM, 0, 0, cast_spell_stub);

   SPELL_CREATE("xxxvitalize mana", SPELL_VITALIZE_MANA, SPELLTYPE_GENERIC, PULSE_VIOLENCE * 2, TRUE, TAR_CHAR_ROOM, 0, 0, cast_spell_stub);

   SPELL_CREATE("special proc effect", SPELL_PROC_SPECIAL, SPELLTYPE_GENERIC, PULSE_VIOLENCE, TRUE, TAR_IGNORE, 0, 0, cast_spell_stub);

   /* Snaking these numbers - Iyachtu */
#if 0
   SKILL_CREATE("alter aura", SKILL_ALTER_AURA, SKILL_TYPE_METAPSIONICS);

   SKILL_CREATE("enhance skill", SKILL_ENHANCE_SKILL, SKILL_TYPE_METAPSIONICS);
#endif

   SKILL_CREATE("attraction", SKILL_ATTRACTION, SKILL_TYPE_METAPSIONICS);

   SKILL_CREATE("enhance strength", SKILL_ENHANCE_STR, SKILL_TYPE_METAPSIONICS);

#if 0
   SKILL_CREATE("enhance dexterity", SKILL_ENHANCE_DEX, SKILL_TYPE_METAPSIONICS);

   SKILL_CREATE("enhance constitution", SKILL_ENHANCE_CON, SKILL_TYPE_METAPSIONICS);

   SKILL_CREATE("enhance agility", SKILL_ENHANCE_AGI, SKILL_TYPE_METAPSIONICS);

   SKILL_CREATE("enhance vision", SKILL_ENHANCE_VISION, SKILL_TYPE_METAPSIONICS);

   SKILL_CREATE("enhance stamina", SKILL_ENHANCE_STAMINA, SKILL_TYPE_METAPSIONICS);
#endif

   /* END Unused Spells --CRM */


   numSkills++;                  /* we started at -1 for numSkills in order to
                                    get the right indices using SKILL_CREATE and
                                    SPELL_CREATE but that will leave us 1 short
                                    of the total count of spells/skills so we
                                    bump up the count here so everything is back
                                    to normal */

   for(i = 0; i < MAX_SKILLS; i++)
      spells[i] = "Undefined";

   for(i = 0; i < MAX_SKILLS; i++)
      {
      if(skills[i].pindex != -1)
         spells[skills[i].pindex - 1] = skills[i].name;
      }

   spells[MAX_SKILLS - 1] = "\n";

   for(i = 0; i < MAX_SKILLS; i++)
      {
      for(j = 0; j < MAX_SKILLS; j++)
         {
         if(skills[j].pindex == i)
            break;
         }

      if(j == MAX_SKILLS)
         {
         pindex2Skill[i] = -1;
         /* fprintf(stderr, "Skill #%d not found.\n", i); */
         }
      else
         pindex2Skill[i] = j;
      }

   /* create a lookup table for mob casters.  Since mob caster far outnumber PC casters, any time
      savings is worthwhile.  JAB */

   for(j = 0; j < MAX_SKILLS; j++)
      {
      MobSpellIndex[j] = 11;
      for(i = 0; i < LAST_CLASS; i++)
         {
         if(skills[j].class[i].rlevel > 0)
            {
            if(skills[j].class[i].rlevel < MobSpellIndex[j])
               MobSpellIndex[j] = skills[j].class[i].rlevel;
            }
         }
      }
}

/* This is almost spell-like, basically, ch can't do anything while knocked
   out.  Major diff from SLEEP is that even being hit won't wake the KOed
   ch (mostly).  Also, very few things are immune to being KOed.  It's also
   VERY cumulative.  JAB */


void KnockOut(P_char ch, int duration)
{
   struct affected_type af;

   if(IS_TRUSTED(ch) || STUN_PROOF(ch))
      return;

   bzero(&af, sizeof(af));
   af.type = 0;
   af.duration = duration;
   af.modifier = 0;
   af.location = 0;
   SET_CBIT(af.sets_affs, AFF_KNOCKED_OUT);
   affect_join(ch, &af, FALSE, FALSE);
}

int spectype_of_spell(int spell_id)
{
   int spelltype = 0;

   if(spell_id == -1)
      return -1;

   spelltype = GET_SPELLTYPE(spell_id);

   switch(spelltype)
      {
      case SPELLTYPE_INVOCATION:
         return(SKILL_SPEC_INVOCATION);

      case SPELLTYPE_ELEMENTAL:
         return(SKILL_SPEC_ELEMENTAL);

      case SPELLTYPE_ILLUSION:
         return(SKILL_SPEC_ILLUSION);

      case SPELLTYPE_NECROMANCY:
         return(SKILL_SPEC_NECROMANCY);

      case SPELLTYPE_HEALING:
         return(SKILL_SPEC_HEALING);

      case SPELLTYPE_PROTECTION:
         return(SKILL_SPEC_PROTECTION);

      case SPELLTYPE_TELEPORTATION:
         return(SKILL_SPEC_TELEPORTATION);

      case SPELLTYPE_ENCHANTMENT:
         return(SKILL_SPEC_ENCHANT);

      case SPELLTYPE_SUMMONING:
         return(SKILL_SPEC_SUMMONING);

      case SPELLTYPE_SPIRIT:
         return(SKILL_SPEC_SPIRIT);

      case SPELLTYPE_NATURE:
         return(SKILL_SPEC_NATURE);

      default:
         break;
      }

   return -1;
}

int skilltype_of_spell(int spell_id)
{
   int spelltype = 0;

   if(spell_id == -1)
      return(SKILL_CAST_GENERIC);

   spelltype = GET_SPELLTYPE(spell_id);

   switch(spelltype)
      {
      case SPELLTYPE_HEALING:
         return(SKILL_CAST_HEALING);

      case SPELLTYPE_PROTECTION:
         return(SKILL_CAST_PROTECTION);

      case SPELLTYPE_ENCHANTMENT:
         return(SKILL_CAST_ENCHANT);

      case SPELLTYPE_ELEMENTAL:
         return(SKILL_CAST_ELEMENTAL);

      case SPELLTYPE_DIVINATION:
         return(SKILL_CAST_DIVINATION);

      case SPELLTYPE_TELEPORTATION:
         return(SKILL_CAST_TELEPORTATION);

      case SPELLTYPE_SUMMONING:
         return(SKILL_CAST_SUMMONING);

#if 0
      case SPELLTYPE_FIRE:
         return(SKILL_CAST_FIRE);

      case SPELLTYPE_COLD:
         return(SKILL_CAST_COLD);
#endif

      case SPELLTYPE_INVOCATION:
         return(SKILL_CAST_INVOCATION);

      case SPELLTYPE_ILLUSION:
         return(SKILL_CAST_ILLUSION);

      case SPELLTYPE_NECROMANCY:
         return(SKILL_CAST_NECROMANCY);

      case SPELLTYPE_SPIRIT:
         return(SKILL_CAST_SPIRIT);

      case SPELLTYPE_NATURE:
         return(SKILL_CAST_NATURE);

         // there's a reason why it's called default!
         //      case SPELLTYPE_ELECTRIC:
         //      case SPELLTYPE_ACID:
         //      case SPELLTYPE_GENERIC:
      default:
         break;
      }

   return(SKILL_CAST_GENERIC);
}

#if 0
void do_abort(P_char ch, char *args, int cmd)
{
   if(!IS_AFFECTED(ch, AFF_CASTING))
      {
      send_to_char("But you aren't casting!\n", ch);
      return;
      }

   StopCasting(ch);
}
#endif

void do_abort(P_char ch, char *args, int cmd)
{
   // struct spellcast_datatype *sd;
   bool found = FALSE;
   P_event e1, e2, c_e;

   if(!IS_AFFECTED(ch, AFF_CASTING))
      {
      send_to_char("But you aren't casting!\n", ch);
      return;
      }

   for(e1 = ch->events; e1; e1 = e1->next)
      {
      if(e1->type == EVENT_SPELLCAST)
         {
         found = TRUE;
         break;
         }
      }

   e2 = e1;
   found = FALSE;
   for(c_e = schedule[e1->element]; c_e; c_e = e2)
      {
      e2 = c_e->next_sched;
      if(c_e == e1)
         found = TRUE;
      }

   StopCasting(ch);
}

/* Spell Damage Standardization Code - 11/98 CRM */

/* The damage table struct... */

struct spell_damage_table
   {
   int dice_num;
   int dice_size;
   int pc_pc_dice_num;
   int pc_pc_dice_size;
   };

/* The following table determines the damage rolls of every offensive */
/* spell. DO NOT CHANGE THIS without prior permission from Shevarash .*/
/* Here's how it works: Every spell is assigned an 'offensive level'  */
/* when it is created.  When the spell is cast, it calls the          */
/* FindSpellDam() function, which passes the 'offensive level' of the */
/* spell to this table, and pulls out the appropriate dam dice size   */
/* and number.  Note that the number of dice in this table is only the*/
/* MAXIMUM number of dice, that is, if the caster's level is lower    */
/* than the number here, the level is used as the number of dice.     */
/* More offensive levels may be added by raising the MAX_OFF_LEVEL var*/
/* in config.h and adding the appropriate elements to this table.     */
/*                                                                    */
/* - CRM                                                              */


struct spell_damage_table SpellDamageTable[MAX_OFF_LEVEL + 1] =
{
   /*               PC - PC                                 */
   /*   num   size num  size                comments        */
   /*   ----------------------------------------------------*/
   { 0,   0,   0,   0},      /* Level 0 - Do Not Use    */
   {10,   5,  10,   2},      /* Level 1                 */
   {15,   5,  15,   2},      /* Level 2                 */
   {15,   6,  15,   3},      /* Level 3                 */
   {20,   6,  20,   3},      /* Level 4                 */
   {20,   7,  20,   4},      /* Level 5                 */
   {25,   7,  25,   4},      /* Level 6                 */
   {25,   8,  25,   5},      /* Level 7                 */
   {30,   8,  30,   5},      /* Level 8                 */
   {30,   9,  30,   6},      /* Level 9                 */
   {35,   9,  35,   6},      /* Level 10                */
   {35,  10,  35,   7},      /* Level 11                */
   {40,  10,  40,   7},      /* Level 12                */
   {40,  12,  40,   8},      /* Level 13                */
   {45,  13,  45,   8},      /* Level 14                */
   {45,  15,  45,   9},      /* Level 15                */
   {50,  16,  50,   9},      /* Level 16                */
   {50,  18,  50,  10},      /* Level 17                */
   {50,  20,  50,  10},      /* Level 18                */
   {50,  22,  50,  11},      /* Level 19  INVOKER ONLY  */
   {50,  24,  50,  11}       /* Level 20  INVOKER ONLY  */
};

int FindSpellDamage(P_char ch, P_char vict, int level, int spell)
{
   int off_level = 0, damage = 0, evade;
   int dice_num, dice_size;
   int spell_id = spell;
   float math = 0;

   if(!ch || !spell)
      return 0;

   // Convert PsiDamage - passed in badly.
   if(IS_PSISKILL(spell))
      {
      off_level = level;
      level = GET_LEVEL(ch);
      }

   // Init initial vars
   level = BOUNDED(1, level, MAXLVLMORTAL);  /* Leave this level cap in place! */
   dice_num = level;

   // Get the spell's offensive level (Psis already have this passed in)
   if(!IS_PSISKILL(spell))
      off_level = skills[pindex2Skill[spell]].off_level;

   spell = pindex2Skill[spell];

   if(IS_PC(ch) && IS_PC(vict))
      {
      /* Hrm dunno if its a good idea but lets try anyways.. */
      dice_num  = MIN(dice_num, SpellDamageTable[off_level].pc_pc_dice_num);
      dice_size = SpellDamageTable[off_level].pc_pc_dice_size;
      }
   else
      {
      /* Cap the number of dice */
      dice_num  = MIN(dice_num, SpellDamageTable[off_level].dice_num);
      dice_size = SpellDamageTable[off_level].dice_size;
      }

   /* Roll the dice! */
   damage = ((dice(dice_num, dice_size) * cc_spells_rawDamageMod) / 100);

   // Temp Debug
   if(IS_PC(ch))
      debuglog(51, DS_IYACHTU, "Before Spec: %d", damage);

   /* Modify for Specializations.. */
   damage = ((modify_by_specialization(ch, spell_id, damage) * cc_spells_specializationMod) / 100);

   // Temp debug
   if(IS_PC(ch))
      debuglog(51, DS_IYACHTU, "After Spec:  %d", damage);

   /* Modify by global damage factor */
   damage = ((damage * cc_spells_globalFactor) / 100);

   // PC vs. PC adjustments
   if(IS_PC(ch) && IS_PC(vict) && (ch != vict))
      damage = ((adjustSkillDamage(damage, spell) * cc_spells_skillDamageMod) / 100);

   // Mobile adjustments
   if(IS_NPC(ch) && IS_PC(vict) && off_level >= 6 && IS_SET(skills[spell].targets, TAR_IGNORE))
      damage = ((damage * cc_spells_mobSpellBonusMod) / 100);

   // PC vs. PC debugging
   if(IS_PC(ch) && IS_PC(vict) && (ch != vict))
      {
#ifdef SPELL_DAM_DEBUG
      logit(LOG_SPELL, "SpellDam: %s (%d) from [%s (%d) > %s]: %d damage", skills[spell].name, off_level, GET_NAME(ch),
            level, GET_NAME(vict), damage);
#endif
      debuglog(51, DS_PKILL_DAMAGE_DEBUG, "SpellDam: %s (%d) from [%s (%d) > %s]: %d damage",
               skills[spell].name, off_level, GET_NAME(ch),
               level, GET_NAME(vict), damage);
      }

   /* Ah, Shev, this function truly is a wonderful thing.
      Provides a handy place for Rogue evasion skill check.  --D2 */
   if(IS_SET(skills[spell].targets, TAR_IGNORE) && IS_PC(vict) && (IS_NPC(ch) || IN_ACHERON(ch)) &&
      (evade = ((evadeSucceed(vict, spell) * cc_spells_evadeMod) / 100)))
      {
      if(evade)
         {
         int prev_dam = 0;

         prev_dam = damage;
         damage = (damage * evade / 100);
         CharSkillGainChance(vict, SKILL_EVASION, 8);
         send_to_char("&+RYou partially evade the spell!&N\n", vict);
         //debuglog(51, DS_EVASION, "Char: %s, Spell: %s, Damage Before: %d, Damage After: %d, Damage Evaded: %3.0f", C_NAME(vict), skills[spell].name, prev_dam, hold, (-1) * store_spells_evadeDamage);
         }
      }

   // Innate magic resistance to some races.
   if((GET_RACE(vict) == RACE_GREY) || (GET_RACE(vict) == RACE_DROW))
      {
      damage = ((damage * cc_spells_innateRaceMrMod) / 100);
      //debuglog(51, DS_SPELLREDUCTION, "Racial Spell Reduction: %d for %s", (hold-damage), GET_NAME(vict));
      }

   // Special Areas modifiers go here
   if(IS_SET(skills[spell].targets, TAR_IGNORE))
      {
      // Addition of reduction in area spell damage by Natures Blessing spell. 3/31/02 --MIAX
      if((IS_AFFECTED(vict, AFF_NATURES_BLESSING)) && (GET_LEVEL(vict) > 30))
         {
         if(GET_LEVEL(vict) > 45)
            math = (float)(damage * 0.75);
         else
            math = (float)(damage * (1 - (((GET_LEVEL(vict) - 31) + 10) * .01)));
         /*
               if(math == 0)
                 sprintf(buf, "DEBUG: Natures Blessing Area-Spell Reduction: 0 (%d%% reduction) Perc: %2.2f, Mult: %2.2f for %s.\n",
                   ((GET_LEVEL(vict) - 31) + 10), (float)(((GET_LEVEL(vict) - 31) + 10) / 100),
                   (float)(1 - (((GET_LEVEL(vict) - 31) + 10) *.01)), GET_NAME(vict));
               else
                 sprintf(buf,
                   "DEBUG: Natures Blessing: %%%2.2f (%d%% reduction) B: %d, A: %2.2f, Perc: %2.2f, Mult: %2.2f for %s.\n",
                   (float)((damage / math) * 100), ((GET_LEVEL(vict) - 31) + 10), damage, math,
                   (float)(((GET_LEVEL(vict) - 31) + 10) / 100),
                   (float)(1 - (((GET_LEVEL(vict) - 31) + 10) * .01)), GET_NAME(vict));
               fprintf(stderr, buf);
               send_to_room(buf, ch->in_room);
         */
         damage = math;
         }
      }

   /* All done, return the final damage */
   return(damage);
}

#if 0
struct spell_damage_table SpellDamageTable[MAX_OFF_LEVEL + 1] =
{
   /*               PC - PC                                 */
   /*   num   size num  size                comments        */
   /*   ----------------------------------------------------*/
   { 0,   0,   0,   0},      /* Level 0 - Do Not Use   */
   {10,   5,  10,   2},      /* Level 1                */
   {15,   6,  15,   2},      /* Level 2                */
   {20,   7,  20,   3},      /* Level 3                */
   {25,   8,  25,   3},      /* Level 4                */
   {35,  10,  35,   3},      /* Level 5                */
   {40,  12,  40,   4},      /* Level 6                */
   {45,  15,  45,   4},      /* Level 7                */
   {50,  16,  50,   4},      /* Level 8                */
   {50,  20,  50,   5},      /* Level 9  INVOKER ONLY  */
   {50,  25,  50,   5},      /* Level 10 INVOKER ONLY  */
};

// Modified by Miax on 4/13/01 to add control knob and stat code.. o_o
int FindSpellDamage(P_char ch, P_char vict, int level, int spell)
{
   int off_level = 0, damage = 0, hold = 0, evade;
   int dice_num, dice_size;
   int spell_id = spell;
   float math = 0;

   //  char buf[256];

   if(!ch || !spell)
      return 0;

   Clear_Spell_Stores();

   /* ha, sooooo hackish - psiDamage passes the offensive level, not the
      ch's level, so we need to fix that. */
   if(IS_PSISKILL(spell))
      {
      off_level = level;
      level = GET_LEVEL(ch);
      }

   /* Init these handy vars */
   level = BOUNDED(1, level, MAXLVLMORTAL);  /* Leave this level cap in place! */
   dice_num = level;

   /* Get the spell's Offensive Level */
   if(!IS_PSISKILL(spell))
      off_level = skills[pindex2Skill[spell]].off_level;
   store_spells_offensiveLevel = off_level;

   spell = pindex2Skill[spell];

   if(IS_PC(ch) && IS_PC(vict))
      {
      /* Hrm dunno if its a good idea but lets try anyways.. */
      dice_num = MIN(dice_num, SpellDamageTable[off_level].pc_pc_dice_num);
      dice_size = SpellDamageTable[off_level].pc_pc_dice_size;
      store_spells_diceNum = dice_num;
      store_spells_diceSize = dice_size;
      }
   else
      {
      /* Cap the number of dice */
      dice_num = MIN(dice_num, SpellDamageTable[off_level].dice_num);
      dice_size = SpellDamageTable[off_level].dice_size;
      store_spells_diceNum = dice_num;
      store_spells_diceSize = dice_size;
      }

   /* Roll the dice! */
   damage = ((dice(dice_num, dice_size) * cc_spells_rawDamageMod) / 100);
   store_spells_rawDamage = damage;

   if(IS_PC(ch))
      debuglog(51, DS_IYACHTU, "Before Spec: %d", damage);

   if(CONTROL_DEBUG == YES)
      fprintf(stderr, "Dice: (%d/%d), Roll: %d\n", dice_num, dice_size, damage);

   /* Modify for Specializations.. */
   damage = ((modify_by_specialization(ch, spell_id, damage) * cc_spells_specializationMod) / 100);
   store_spells_specializationDamage = (damage - store_spells_rawDamage);
   hold = damage;

   if(IS_PC(ch))
      debuglog(51, DS_IYACHTU, "After Spec:  %d", damage);

   if(CONTROL_DEBUG == YES)
      fprintf(stderr, "Modified by Specialization: %d (%3.0f)\n", damage, store_spells_specializationDamage);

   /* Modify by global damage factor */
   damage = ((damage * cc_spells_globalFactor) / 100);
   store_spells_globalDamageFactor = (damage - hold);
   hold = damage;

   if(CONTROL_DEBUG == YES)
      fprintf(stderr, "Modified by Global Factor: %d (%3.0f)\n", damage, store_spells_globalDamageFactor);

   if(IS_PC(ch) && IS_PC(vict) && (ch != vict))
      {
      damage = ((adjustSkillDamage(damage, spell) * cc_spells_skillDamageMod) / 100);
      store_spells_skillDamage = (damage - hold);
      hold = damage;
      }

   if(IS_NPC(ch) && IS_PC(vict) && off_level >= 6 && IS_SET(skills[spell].targets, TAR_IGNORE))
      {
      damage = ((damage * cc_spells_mobSpellBonusMod) / 100);
      hold = damage;
      }

   if(CONTROL_DEBUG == YES)
      fprintf(stderr, "Modified by Skills: %d (%3.0f)\n", damage, store_spells_skillDamage);

   if(IS_PC(ch) && IS_PC(vict) && (ch != vict))
      {
#ifdef SPELL_DAM_DEBUG
      logit(LOG_SPELL, "SpellDam: %s (%d) from [%s (%d) > %s]: %d damage", skills[spell].name, off_level, GET_NAME(ch),
            level, GET_NAME(vict), damage);
#endif
      debuglog(51, DS_PKILL_DAMAGE_DEBUG, "SpellDam: %s (%d) from [%s (%d) > %s]: %d damage",
               skills[spell].name, off_level, GET_NAME(ch),
               level, GET_NAME(vict), damage);
      }

   /* Ah, Shev, this function truly is a wonderful thing.
      Provides a handy place for Rogue evasion skill check.  --D2 */
   if(IS_SET(skills[spell].targets, TAR_IGNORE) && IS_PC(vict) && (IS_NPC(ch) || IN_ACHERON(ch)) &&
      (evade = ((evadeSucceed(vict, spell) * cc_spells_evadeMod) / 100)))
      {
      if(evade)
         {
         int prev_dam = 0;

         prev_dam = damage;
         damage = (damage * evade / 100);
         store_spells_evadeDamage = (damage - hold);
         hold = damage;
         CharSkillGainChance(vict, SKILL_EVASION, 8);
         send_to_char("&+RYou partially evade the spell!&N\n", vict);
         //debuglog(51, DS_EVASION, "Char: %s, Spell: %s, Damage Before: %d, Damage After: %d, Damage Evaded: %3.0f", C_NAME(vict), skills[spell].name, prev_dam, hold, (-1) * store_spells_evadeDamage);
         }
      }

   if(CONTROL_DEBUG == YES)
      fprintf(stderr, "Modified by Evade: %d (%3.0f)\n", damage, store_spells_evadeDamage);

   // Innate magic resistance to some races.
   if((GET_RACE(vict) == RACE_GREY) || (GET_RACE(vict) == RACE_DROW))
      {
      damage = ((damage * cc_spells_innateRaceMrMod) / 100);
      debuglog(51, DS_SPELLREDUCTION, "Racial Spell Reduction: %d for %s", (hold-damage), GET_NAME(vict));
      store_spells_innateRaceDamage = (damage - hold);
      hold = damage;
      }

   if(CONTROL_DEBUG == YES)
      fprintf(stderr, "Modified by InnateRaceMR: %d (%3.0f)\n", damage, store_spells_innateRaceDamage);

   /* Reduce damage by 20% for 9th-10th circle AREA damage spells */
   // Naaa, thats old school. Lets put a knob on every damn spell circle, areas and non. o_o
   if(IS_SET(skills[spell].targets, TAR_IGNORE))
      {
      // Addition of reduction in area spell damage by Natures Blessing spell. 3/31/02 --MIAX
      if((IS_AFFECTED(vict, AFF_NATURES_BLESSING)) && (GET_LEVEL(vict) > 30))
         {
         if(GET_LEVEL(vict) > 45)
            math = (float)(damage * 0.75);
         else
            math = (float)(damage * (1 - (((GET_LEVEL(vict) - 31) + 10) * .01)));
         /*
               if(math == 0)
                 sprintf(buf, "DEBUG: Natures Blessing Area-Spell Reduction: 0 (%d%% reduction) Perc: %2.2f, Mult: %2.2f for %s.\n",
                   ((GET_LEVEL(vict) - 31) + 10), (float)(((GET_LEVEL(vict) - 31) + 10) / 100),
                   (float)(1 - (((GET_LEVEL(vict) - 31) + 10) *.01)), GET_NAME(vict));
               else
                 sprintf(buf,
                   "DEBUG: Natures Blessing: %%%2.2f (%d%% reduction) B: %d, A: %2.2f, Perc: %2.2f, Mult: %2.2f for %s.\n",
                   (float)((damage / math) * 100), ((GET_LEVEL(vict) - 31) + 10), damage, math,
                   (float)(((GET_LEVEL(vict) - 31) + 10) / 100),
                   (float)(1 - (((GET_LEVEL(vict) - 31) + 10) * .01)), GET_NAME(vict));
               fprintf(stderr, buf);
               send_to_room(buf, ch->in_room);
         */
         damage = math;
         }

      // Now apply the control knobs.
      switch(GetSpellCircle(ch, spell))
         {
         case 1:
            store_spells_circleAreas1Damage = ((damage * cc_spells_circleAreas1Mod) / 100);
            damage = store_spells_circleAreas1Damage;
            break;
         case 2:
            store_spells_circleAreas2Damage = ((damage * cc_spells_circleAreas2Mod) / 100);
            damage = store_spells_circleAreas2Damage;
            break;
         case 3:
            store_spells_circleAreas3Damage = ((damage * cc_spells_circleAreas3Mod) / 100);
            damage = store_spells_circleAreas3Damage;
            break;
         case 4:
            store_spells_circleAreas4Damage = ((damage * cc_spells_circleAreas4Mod) / 100);
            damage = store_spells_circleAreas4Damage;
            break;
         case 5:
            store_spells_circleAreas5Damage = ((damage * cc_spells_circleAreas5Mod) / 100);
            damage = store_spells_circleAreas5Damage;
            break;
         case 6:
            store_spells_circleAreas6Damage = ((damage * cc_spells_circleAreas6Mod) / 100);
            damage = store_spells_circleAreas6Damage;
            break;
         case 7:
            store_spells_circleAreas7Damage = ((damage * cc_spells_circleAreas7Mod) / 100);
            damage = store_spells_circleAreas7Damage;
            break;
         case 8:
            store_spells_circleAreas8Damage = ((damage * cc_spells_circleAreas8Mod) / 100);
            damage = store_spells_circleAreas8Damage;
            break;
         case 9:
            store_spells_circleAreas9Damage = ((damage * cc_spells_circleAreas9Mod) / 100);
            damage = store_spells_circleAreas9Damage;
            break;
         case 10:
            store_spells_circleAreas10Damage = ((damage * cc_spells_circleAreas10Mod) / 100);
            damage = store_spells_circleAreas10Damage;
            break;
         }
      }
   else
      { // Non-areas spells, a modifier for each! O_O
      switch(GetSpellCircle(ch, spell))
         {
         case 1:
            store_spells_circleNonAreas1Damage = ((damage * cc_spells_circleNonAreas1Mod) / 100);
            damage = store_spells_circleNonAreas1Damage;
            break;
         case 2:
            store_spells_circleNonAreas2Damage = ((damage * cc_spells_circleNonAreas2Mod) / 100);
            damage = store_spells_circleNonAreas2Damage;
            break;
         case 3:
            store_spells_circleNonAreas3Damage = ((damage * cc_spells_circleNonAreas3Mod) / 100);
            damage = store_spells_circleNonAreas3Damage;
            break;
         case 4:
            store_spells_circleNonAreas4Damage = ((damage * cc_spells_circleNonAreas4Mod) / 100);
            damage = store_spells_circleNonAreas4Damage;
            break;
         case 5:
            store_spells_circleNonAreas5Damage = ((damage * cc_spells_circleNonAreas5Mod) / 100);
            damage = store_spells_circleNonAreas5Damage;
            break;
         case 6:
            store_spells_circleNonAreas6Damage = ((damage * cc_spells_circleNonAreas6Mod) / 100);
            damage = store_spells_circleNonAreas6Damage;
            break;
         case 7:
            store_spells_circleNonAreas7Damage = ((damage * cc_spells_circleNonAreas7Mod) / 100);
            damage = store_spells_circleNonAreas7Damage;
            break;
         case 8:
            store_spells_circleNonAreas8Damage = ((damage * cc_spells_circleNonAreas8Mod) / 100);
            damage = store_spells_circleNonAreas8Damage;
            break;
         case 9:
            store_spells_circleNonAreas9Damage = ((damage * cc_spells_circleNonAreas9Mod) / 100);
            damage = store_spells_circleNonAreas9Damage;
            break;
         case 10:
            store_spells_circleNonAreas10Damage = ((damage * cc_spells_circleNonAreas10Mod) / 100);
            damage = store_spells_circleNonAreas10Damage;
            break;
         }
      }

   store_spells_modifiedDamage = (damage - hold);

   if(CONTROL_DEBUG == YES)
      fprintf(stderr, "Modified by Knobs: %d\n", damage);

   store_spells_finalDamage = (float)damage;

   ProcessSpellDamage(ch, vict, level, spell);

   /* All done, return the final damage */
   return(damage);
}

#endif


/* this function handles the new rogue reflexive skill 'evasion'.  Gives
   the rogue a chance to avoid some area spell damage.  It's not applicable
   to targetted spells.  Returns 0 if rogue does not evade, otherwise returns
   modifier to spell damage. */
int evadeSucceed(P_char ch, int spell)
{
   int num, modifier, diff, learned;

   /* ok, despite the fact that this should only be called from FindSpellDamage,
      AND despite the fact that I already performed the area affect check, one
      never knows when some wanker will call this function improperly from
      somewhere else... */
   if(!IS_SET(skills[spell].targets, TAR_IGNORE))
      return 0;

   /* do we even have the skill? */
   learned = GET_CHAR_SKILL(ch, SKILL_EVASION);

   if(!learned)
      return 0;

   /* a few ground rules.  1) this skill shall never, EVER completely
      eliminate damage from any spell.  2) this skill shall never, EVER work
      for a player more than 50% of the time. The way it is currently set up,
      a master evader will evade half of the damage about half of the time. */
   learned *= 2;
   learned /= 3;

   num = number(0, 99);

   /* treat 0 specially */
   if(num == 0)
      {
      modifier = 15;
      return modifier;
      }

   /* since learned cannot be higher than 67, return out if num is. */
   if(num > 67)
      return 0;

   /* otherwise, away we go... */
   if(num > learned)
      return 0; // Rogue failed to evade, takes standard damage.
   else
      {
      /* Rogue evades, modify damage done. */
      diff = learned - num;

      if(diff > 54)
         modifier = 20;
      else if(diff > 49)
         modifier = 25;
      else if(diff > 44)
         modifier = 30;
      else if(diff > 39)
         modifier = 35;
      else if(diff > 34)
         modifier = 40;
      else if(diff > 29)
         modifier = 50;
      else if(diff > 24)
         modifier = 55;
      else if(diff > 19)
         modifier = 60;
      else if(diff > 14)
         modifier = 70;
      else if(diff > 9)
         modifier = 80;
      else if(diff > 4)
         modifier = 90;
      else
         modifier = 95;
      }

   return modifier;
}

/* Below is the prototype for having magic interact with other spells. */
/* In essence, it checks for those grouped with the caster who are casting */
/* at the same time.  If it finds someone casting a spell, it checks to see */
/* if the spells interact.  If they do not, the casting goes on like normal. */
/* If the spells DO interact, they are merged into one spell.  How does this */
/* work you ask?  This function is inserted in the spell being cast.  It begins */
/* by checking for casters in THIS function, then returning that value to the spell */
/* above.  The spell above then handles whatever changes to result.  This function */
/* removes the spell from the "victim" caster and the spell above removes the */
/* original spell from the caster who completes the spell first. - Valk*/

int check_merge_spells(P_char ch)
{
   P_char next, tmp_ch;
   int i, c_spell = 0, v_spell = 0, v_time = 0, merge;

   struct spellcast_datatype *tmp, *tmp2;

   merge = 0;
   i = 0;

   /* Lets see what spell the originating caster is casting */
   c_spell = check_merge_caster(ch, c_spell);
   if(c_spell == 0)
      return merge; // this is likely a spell cast from other than the normal casting of it
                    // like a proc, so we ignore all merging of spells here. Azuth 7/2/2003

   /* Alright, who is the victim caster - 1st person conflicting in the room */
   /* Lets return the spell and the time left */
   for(tmp_ch = world[ch->in_room].people; tmp_ch; tmp_ch = next)
      {
      next = tmp_ch->next_in_room;
      if(IS_PC(tmp_ch) && IS_AFFECTED(tmp_ch, AFF_CASTING) && (ch != tmp_ch))
         {
         for(tmp = spellcast_stack; tmp; tmp = tmp2)
            {
            tmp2 = tmp->next;
            if(tmp->ch == tmp_ch)
               {
               v_spell = (tmp->spell);
               v_time = (tmp->timeleft);
               if(v_spell > 0)
                  break;
               }
            }
         }

      /* Now we have the casters and spells: tmp_ch - v_spell and ch - c_spell */
      /* We still need to see whether they are nearly finishing at the same time */
      /* so lets check and see how close the v_spell cast by tmp_ch is to finishing */
      /* We're using a time of 11 right now for concurrency of spells finishing */
      /* If we want spells to interact more easily and more often, we can increase this number */

      if((v_time < 11) && v_spell)
         i = merge_spells(v_spell, c_spell, tmp_ch, ch, i);

      if(i == 1)
         {
         merge = i;
         return merge;
         }
      else
         {
         v_spell = 0;
         v_time = 0;
         }
      }

   return merge;
}

int check_merge_caster(P_char ch, int c_spell) {

   struct spellcast_datatype *tmp, *tmp2;

   for(tmp = spellcast_stack; tmp; tmp = tmp2)
      {
      tmp2 = tmp->next;
      if(tmp->ch == ch)
         {
         c_spell = (tmp->spell);
         if(c_spell > 0)
            return c_spell;
         }
      }

   // this is likely a spell cast from other than the normal casting of it
   // like a proc, so we ignore all merging of spells here. Azuth 7/2/2003
   //  if (c_spell == 0) {
   //    logit(LOG_EXIT, "assert: bogus params in check_merge_caster!");
   //    dump_core();
   //  }
   return c_spell;
}

void junk_memorized_spell(P_char tmp_ch)
{
   int circle, num;
   struct spellcast_datatype *arg;

   for(arg = spellcast_stack; (arg && arg->ch != tmp_ch); arg = arg->next);

   if(arg)
      {
      if(!IS_TRUSTED(tmp_ch))
         {
         if(IS_PC(tmp_ch))
            {
#ifdef NEW_BARD
            if(GET_CLASS(tmp_ch) == CLASS_BARD || GET_CLASS(tmp_ch) == CLASS_BATTLECHANTER)
               {
               if(tmp_ch->only.pc->available_spells[GetSpellCircle(tmp_ch, arg->spell)] > 0)
                  tmp_ch->only.pc->available_spells[GetSpellCircle(tmp_ch, arg->spell)]--;
               else
                  /* we screwed up, best to know - Iyachtu */
                  dump_core();
               }
            else
#endif
               if(tmp_ch->only.pc->skills[skills[arg->spell].pindex].memorized > 0)
               {
               tmp_ch->only.pc->skills[skills[arg->spell].pindex].memorized--;
               add_new_memorize_spell(tmp_ch, skills[arg->spell].pindex);
               }
            else
               {
               send_to_char("Yer slots memorized are fubared. Fixing it, but if this happens oft, report\nthis to an admin, please.\n", tmp_ch);
               tmp_ch->only.pc->skills[skills[arg->spell].pindex].memorized = 0;
               }
            }
         else
            {
            /* NPCs handled slightly different still */
            if(GET_STAT(tmp_ch) != STAT_DEAD)
               {
               circle = GetLowestSpellCircle(arg->spell);
               tmp_ch->only.npc->spells_in_circle[circle]--;
               num = tmp_ch->only.npc->spells_in_circle[circle];
               tmp_ch->only.npc->spells_in_circle[circle] = BOUNDED(0, num, 127);
               tmp_ch->only.npc->spells_in_circle[0]++;
               }
            }
         }

      nuke_spellcast(arg);
      }
   else
      logit(LOG_DEBUG, "junk_memorized_spell is broken!  Fix it!", GET_NAME(tmp_ch));
}

void merge_spells_messages(P_char tmp_ch, P_char ch)
{
   switch(number(1, 3))
      {
      case 1:
         act("&+WAs $n finishes casting, and the spell mixes with $N's spell, the magic gets out of control!&N", FALSE, ch, 0, tmp_ch, TO_ROOM);
         send_to_char("&+WAs you finish the spell, the magic begins to get out of your control.&N\n", ch);
         send_to_char("&+WAs you near the end of your spell, the magic begins to get out of your control.&N\n", tmp_ch);
         break;
      case 2:
         act("&+WThe air around $n and $N begins to &+CCra&N&+cck and P&+Cop&+W as magic begins to intertwine&N", FALSE, ch, 0, tmp_ch, TO_ROOM);
         send_to_char("&+WThe air around you begins to &+CCra&N&+cck and P&+Cop&+W as the magic begins to intertwine&N\n", ch);
         send_to_char("&+WThe air around you begins to &+CCra&N&+cck and P&+Cop&+W as the magic begins to intertwine&N\n", tmp_ch);
         break;
      case 3:
         act("&+WA strange look comes over $n's face as the magic mixes with $N's spell!&N", FALSE, ch, 0, tmp_ch, TO_ROOM);
         send_to_char("&+WYou make a strange face as you feel control over your magic slipping away...&N\n", ch);
         send_to_char("&+WYou make a strange face as you feel control over your magic slipping away...&N\n", tmp_ch);
         break;
      }

   return;
}

void merge_spells_results(P_char tmp_ch, P_char ch)
{
   NukeCharEventType(tmp_ch, EVENT_SPELLCAST);
   REMOVE_CBIT(tmp_ch->specials.affects, AFF_CASTING);
   NukeCharEventType(ch, EVENT_SPELLCAST);
   REMOVE_CBIT(ch->specials.affects, AFF_CASTING);
   junk_memorized_spell(tmp_ch);
   junk_memorized_spell(ch);
   return;
}

int merge_spells(int v_spell, int c_spell, P_char tmp_ch, P_char ch, int i)
{
   // int lvl = 0;
   // struct affected_type af;

   /* Now lets start going through our spell combinations */
   /* v_spell is the victim caster and c_spell is the first completing caster */
   if(IS_NPC(tmp_ch) || IS_NPC(ch))
      return 0;

   debuglog(51, DS_IYACHTU, "v_spell = %d, c_spell = %d", v_spell, c_spell);

   if((v_spell == pindex2Skill[SPELL_INCENDIARY_CLOUD]) && (c_spell == pindex2Skill[SPELL_INCENDIARY_CLOUD]))
      {
      spell_feedback(tmp_ch, ch);
      return 1;
      }

   if((v_spell == pindex2Skill[SPELL_INFERNO]) && (c_spell == pindex2Skill[SPELL_INFERNO]))
      {
      spell_feedback(tmp_ch, ch);
      return 1;
      }

   if((v_spell == pindex2Skill[SPELL_SANDSTORM]) && (c_spell == pindex2Skill[SPELL_SANDSTORM]))
      {
      spell_feedback(tmp_ch, ch);
      return 1;
      }

   if((v_spell == pindex2Skill[SPELL_ACIDSTORM]) && (c_spell == pindex2Skill[SPELL_ACIDSTORM]))
      {
      spell_feedback(tmp_ch, ch);
      return 1;
      }

   if((v_spell == pindex2Skill[SPELL_METEOR_SWARM]) && (c_spell == pindex2Skill[SPELL_METEOR_SWARM]))
      {
      spell_feedback(tmp_ch, ch);
      return 1;
      }

   if((v_spell == pindex2Skill[SPELL_THUNDERBLAST]) && (c_spell == pindex2Skill[SPELL_THUNDERBLAST]))
      {
      spell_feedback(tmp_ch, ch);
      return 1;
      }

   if((v_spell == pindex2Skill[SPELL_DESSICATE]) && (c_spell == pindex2Skill[SPELL_DESSICATE]))
      {
      spell_feedback(tmp_ch, ch);
      return 1;
      }

   if((v_spell == pindex2Skill[SPELL_HAILSTORM]) && (c_spell == pindex2Skill[SPELL_HAILSTORM]))
      {
      spell_feedback(tmp_ch, ch);
      return 1;
      }

   if((v_spell == pindex2Skill[SPELL_CREEPING]) && (c_spell == pindex2Skill[SPELL_CREEPING]))
      {
      spell_feedback(tmp_ch, ch);
      return 1;
      }

   if((v_spell == pindex2Skill[SPELL_ANCESTRAL_FURY]) && (c_spell == pindex2Skill[SPELL_ANCESTRAL_FURY]))
      {
      spell_feedback(tmp_ch, ch);
      return 1;
      }

   if((v_spell == pindex2Skill[SPELL_ROT]) && (c_spell == pindex2Skill[SPELL_ROT]))
      {
      spell_feedback(tmp_ch, ch);
      return 1;
      }

   if((v_spell == pindex2Skill[SPELL_RAIN_OF_BLOOD]) && (c_spell == pindex2Skill[SPELL_RAIN_OF_BLOOD]))
      {
      spell_feedback(tmp_ch, ch);
      return 1;
      }

   return 0;
}

void spell_feedback(P_char tmp_ch, P_char ch)
{
   act("&+LAs you near the end of your spell, you feel some of the spell turn back on you! &+Y*ZAP*&N", TRUE, ch, 0, 0, TO_CHAR);
   act("&+LAs you finish your spell, you feel some of the spell turn back on you! &+Y*ZAP*&N", TRUE, tmp_ch, 0, 0, TO_CHAR);
   act("&+L$n aborts his spell for fear of his life because of $N's casting!&N", TRUE, ch, 0, tmp_ch, TO_ROOM);
   act("&+LYou cannot finish your spell without killing yourself because of $N's casting!&N", TRUE, ch, 0, tmp_ch, TO_CHAR);

   // Suck it, vokers. --CRM
   if((GET_CLASS(ch) == CLASS_INVOKER) && (GET_CLASS(tmp_ch) == CLASS_INVOKER))
      {
      GET_HIT(ch) -= ((GET_MAX_HIT(ch) * 3) / 4);
      GET_HIT(tmp_ch) -= ((GET_MAX_HIT(tmp_ch) * 3) / 4);
      }
   else
      {
      GET_HIT(ch) -= (GET_MAX_HIT(ch) / 2);
      GET_HIT(tmp_ch) -= (GET_MAX_HIT(tmp_ch) / 2);
      }

   NukeCharEventType(ch, EVENT_SPELLCAST);
   REMOVE_CBIT(ch->specials.affects, AFF_CASTING);
   junk_memorized_spell(ch);
   return;
}

/*
int
use_spell_component(P_char ch, int vnum) {
  int i = 0;
  bool need_component = FALSE;
  bool used_component = FALSE;
  P_obj obj, k;

  if (!ch)
   return 1;

  if (!vnum)
    return 1;

  for (i = 0; sclist[i].spell; i++) {
    debuglog(51, DS_MIELIKKI, "Casting: %d, i: %d, %d", vnum, i, sclist[i].spell);
    if (sclist[i].spell == skills[vnum].pindex) {
      debuglog(51, DS_MIELIKKI, "Spell's match, use component: %d", sclist[i].component);
      need_component = TRUE;
      if (ch->equipment[WEAR_COMPONENT_BAG]) {
        debuglog(51, DS_MIELIKKI, "Got component bag");
        obj = ch->equipment[WEAR_COMPONENT_BAG];
        for (k = obj->contains; k; k = k->next_content) {
          debuglog(51, DS_MIELIKKI, "looping through bag");
          if (k) {
            debuglog(51, DS_MIELIKKI, "bag'd item: %d", obj_index[k->R_num].virtual);
            if (obj_index[k->R_num].virtual == sclist[i].component) {
               debuglog(51, DS_MIELIKKI, "Got the component in the bag");
               act("&+wFor a brief moment &N$p&N&+w attached to your waist glows brightly.&N", FALSE, ch, obj, 0, TO_CHAR);
               extract_obj(k);
               k = NULL;
               used_component = TRUE;
               break;
            }
          }
        }
      }
    }
  }

  if(need_component) {
    if (used_component) {
      return 1;
    } else {
      send_to_char("You lack a spell component for this spell.\n", ch);
      return 0;
    }
  }

  return 1;
}
*/
