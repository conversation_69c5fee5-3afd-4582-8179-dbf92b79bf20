/* ************************************************************************* */
/*                                                                           */
/*      File comm.c - Special Module, part of Outcast III MUD code base.     */
/*   Usage: Main game engine, communication and coordination functions.      */
/*                                                                           */
/*      COPYRIGHT NOTICE: This code for Outcast is Copyright(c) 2001, by     */
/*    <PERSON><PERSON><PERSON> <PERSON>. This code is NOT freeware or shareware,    */
/*   and may NOT be used in any form without expressly written permission    */
/*                             from the author.                              */
/*        K<PERSON> may be reached via <NAME_EMAIL>      */
/*             and <EMAIL> <NAME_EMAIL>                  */
/*                                                                           */
/*************************************************************************** */

#include <ctype.h>
#include <errno.h>
#include <fcntl.h>
#include <netdb.h>
#include <netinet/in.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdarg.h>
#include <sys/socket.h>
#include <sys/stat.h>
#include <sys/time.h>
#include <sys/wait.h>
#include <unistd.h>
#include <signal.h>

#define NEW_SITE_LOOKUPS

#define YES                     1
#define NO                      0
#define BANNER_ONLY_FILE        "lib/information/banner_only"

#include "comm.h"
#include "db.h"
#include "events.h"
#include "interp.h"
#include "prototypes.h"
#include "structs.h"
#include "utils.h"
#include "mm.h"
#include "olc.h"
#include "olcdb.h"
#include "specs.prototypes.h"
#ifdef NEW_SITE_LOOKUPS
#include "lookup.h"
#endif
#include "mccp.h"
#include "bandwidth.h"

//#define SITE_LOOKUPS

/* external variables */

extern P_char PC_list;
extern P_event schedule[];
extern P_index mob_index;
extern P_room world; /* In db.c */
extern P_obj object_list;
extern char debug_mode;
extern int top_of_world; /* In db.c */
extern int top_of_zone_table;
extern struct ban_t *ban_list;
extern struct time_info_data time_info; /* In db.c */
extern struct zone_data *zone;
extern struct zone_data *zone_table;
extern ubyte sets_code_control[CODE_CONTROL_BYTES];
extern int spammy;

/* local globals */

P_char dead_guys = NULL; /* free memory at end of the pulse to avoid bad memory -DCL */
P_desc descriptor_list, next_to_process, next_save = 0;
fd_set input_set, output_set, exc_set; /* for socket handling */
int bounce_null_sites = 0;
int copyover = 0; /* reboot the game after a shutdown */
int ipc_id = 0; /* Queue ID for IPC */
int lawful = 0; /* work like the game regulator */
int max_users_playing = 0; /* used in "who" list */
int no_specials = 0; /* Suppress ass. of special routines */
int override = 1;
int phase = 0; /* needed for combat timing JAB 4/96 */
int pulse = 0; /* needed for certain spells in cmd_interp --TAM 2/94 */
int reboot = 0; /* reboot the game after a shutdown */
int req_passwd = 1;
int shutdownflag = 0; /* clean shutdown */
int slow_death = 0; /* Shut her down, Martha, she's sucking mud */
int tics = 0; /* for extern checkpointing */
int used_descs = 0, avail_descs = 0, max_descs = 0;
int boot_time;
int port; /* port the game is running on */
int scan_player_files = 0;
int sql_scan_objects = 0;
pid_t lookup_host_process; /* PID for the process which will do host lookups */
pid_t lookup_ident_process; /* PID for the process which will do ident lookups */
struct mm_ds *dead_desc_pool = NULL;
int BANNER_ONLY = NO;
int MRTG_VERBOSE = NO;
char *Banner_Only_Message = NULL;

/* ============= Ansi color table and data structure (Ithor) ============== */

struct ansi_color {
  const char *symbol; /* Symbol used in the game. eg &+symbol    */
  const char *fg_code; /* ^[[fg_codem; = foreground color code    */
  const char *bg_code; /* Same except it's the background color   */
} color_table[] = {

  {
    "L", "30", "40"
  }, /* Black            */
  {
    "R", "31", "41"
  }, /* Red              */
  {
    "G", "32", "42"
  }, /* Green            */
  {
    "Y", "33", "43"
  }, /* Yellow           */
  {
    "B", "34", "44"
  }, /* Blue             */
  {
    "M", "35", "45"
  }, /* Magenta          */
  {
    "C", "36", "46"
  }, /* Cyan             */
  {
    "W", "37", "47"
  }, /* White            */
  {
    NULL, NULL, NULL
  } /* End of the table */
};

#define MIN_SOCKET_BUFFER_SIZE 20480    /* from jedi code */

/* *********************************************************************
 *  main game loop and related stuff                                 *
 ********************************************************************* */

int main(int argc, char **argv) {
  int pos = 1;
  const char *dir;
#ifdef _SOLAR2_SOURCE
  char buf[MAX_STRING_LENGTH];
#endif

  port = DFLT_TEST_PORT;
  dir = DFLT_DIR;

  while ((pos < argc) && (*(argv[pos]) == '-')) {
    switch (*(argv[pos] + 1)) {
      case 'b':
        BANNER_ONLY = YES;
        logit(LOG_STATUS, "Banner-only mode, all connections will be rejected.");
        Banner_Only_Message = file_to_string(BANNER_ONLY_FILE);
        break;
      case 'd':
        if (*(argv[pos] + 2))
          dir = argv[pos] + 2;
        else if (++pos < argc)
          dir = argv[pos];
        else {
          logit(LOG_EXIT, "Directory arg expected after option -d.");
          dump_core();
        }
        break;
      case 'l':
        lawful = 1;
        logit(LOG_STATUS, "Lawful mode selected.");
        break;
      case 'm':
        MRTG_VERBOSE = YES;
        logit(LOG_STATUS, "Using Verbose mode for MRTG stat output.");
        fprintf(stderr, "Using Verbose mode for MRTG stat output.\n");
        break;
      case 'n':
        bounce_null_sites = 1;
        logit(LOG_STATUS, "Bouncing null sites");
        break;
      case 'p':
        req_passwd = 0;
        logit(LOG_STATUS, "Allowing changing of password without old one.");
        break;
      case 's':
        no_specials = 1;
        logit(LOG_STATUS, "Suppressing assignment of special routines.");
        break;
#ifdef SQL
      case 'r':
        fprintf(stdout, "***** Will perform OBJECT rescan *****\n");
        sql_scan_objects = 1;
        break;
      case 'R':
        fprintf(stdout, "***** Will perform PFILE rescan *****\n");
        scan_player_files = 1;
        break;
#endif
      default:
        logit(LOG_STATUS, "Unknown option -% in argument string.",
                *(argv[pos] + 1));
        break;
    }
    pos++;
  }

  if (pos < argc) {
    if (!isdigit(*argv[pos])) {
      fprintf(stderr,
              "Usage: %s [-l] [-s] [-p] [-n] [-d pathname] [ port # ]\n",
              argv[0]);
      dump_core();
    } else {
      port = atoi(argv[pos]);
      logit(LOG_STATUS, "Starting on port %d", port);
    }
  }

#ifdef SITE_LOOKUPS
  /* create an IPC msg queue to deal with hostname lookups.  */

  ipc_id = msgget(IPC_PRIVATE, IPC_CREAT | IPC_EXCL | 0600);
  if (ipc_id < 0) {
    fprintf(stderr, "Unable to create message queue!\n");
    dump_core();
    ;
  }


  /* fork() off a new process to deal with hostname lookups. */

  /* fork will return 0 to the newly created process */

  if (!(lookup_host_process = fork()))
    exit(run_lookup_host_process(ipc_id));

  if (!(lookup_ident_process = fork()))
    exit(run_lookup_ident_process(ipc_id));

#endif

  if (chdir(dir) < 0) {
    perror("chdir");
    dump_core();
  }
  logit(LOG_STATUS, "Running game on port %d.", port);

  logit(LOG_STATUS, "Using %s as data directory.", dir);

  setrandom(); /* new random functions - SAM */

  init_cmdlog(); /* init cmd.debug file - DCL */

  run_the_game(port);

#ifdef _SOLAR2_SOURCE
  /* Okay kiddies...this is BAD not having this, so I'm adding it */
  /* After seventy boots or so, my msg queues got full, and the reason...
   * We never delete them...so we are gonna handle this here with a system
   * call to ipcrm, this should take care of all msg queues we created.
   * Note: we also have to deal with this in the event of a core dump, but
   * we currently do not...so if we crash...this should be manually done.
   */
  sprintf(buf, "ipcrm -q %d", ipc_id);
  system(buf);
#endif

  return (0);
}

void game_up_message(int port) {
  FILE *f;
  char Gbuf1[200];

  /* Print startup message to console */
  fprintf(stderr, "\n");
  fprintf(stderr, "========================================\n");
  fprintf(stderr, "   OUTCAST MUD IS NOW RUNNING!\n");
  fprintf(stderr, "   Port: %d\n", port);
  fprintf(stderr, "   Connect: telnet localhost %d\n", port);
  fprintf(stderr, "========================================\n");
  fprintf(stderr, "\n");

  /* Log startup message */
  logit(LOG_STATUS, "MUD is now running on port %d", port);

  /* stealth-wall was a custom wall command used to broadcast MUD startup messages
     to all logged-in users on the original server. This is not needed for local
     development and the binary is not available on most systems. */
  /*
  if ((f = fopen("foo_tmp", "w")) != NULL) {
    sprintf(Gbuf1, "Faerun> The mud is up at port %d in few moments. Run! Panic! *FLEE*\n", port);
    fputs(Gbuf1, f);
    fclose(f);
    system("/usr/local/bin/stealth-wall < foo_tmp");
    unlink("foo_tmp");
  }
  */

#ifdef SITE_LOOKUPS
  signal(SIGCHLD, (void *) reaper);
#endif
}

/* Init sockets, run game, and cleanup sockets */

void run_the_game(int port) {
  int s;

  descriptor_list = NULL;

  logit(LOG_STATUS, "Signal trapping.");
  signal_setup();

  logit(LOG_STATUS, "Opening mother connection.");
  s = init_socket(port);

  logit(LOG_STATUS, "Setting up info for lookup command.");

#if 0
  system("gawk -f lib/lookup.awk areas/world.mob >& lib/misc/lookup.mob &");
  system("gawk -f lib/lookup.awk areas/world.obj >& lib/misc/lookup.obj &");
  system("gawk -f lib/lookup.rawk areas/world.wld >& lib/misc/lookup.wld &");
  system("gawk -f lib/lookup.rawk areas/world.zon >& lib/misc/lookup.zon &");
#endif

  SetSpellCircles(); /* spells circlewise done with pure math */
  SetGuildSpellLvl();
  bootInitializePSPTable(); /* init the psp cost table for psionics  */

  if (IS_ENABLED(CODE_TRADE))
    REMOVE_CBIT(sets_code_control, 1);

  boot_db();
#if 0
  init_whod(port);
#endif
  logit(LOG_STATUS, "Entering game loop.");

  game_up_message(port);

#ifdef AREAS_DEBUG
  dump_core();
#endif

  game_loop(s);

  shutdown_auctions();  /* Clean up auction sets */
  shutdown_ipshare();   /* Clean up IP share tracking */
  free_socials();       /* Free socials memory */
  free_zones();        /* Free zone memory */
  close_sockets(s);
#if 0
  close_whod();
#endif
  if (reboot) {
    logit(LOG_EXIT, "Rebooting.");
    exit(52); /* what's so great about HHGTTG, anyhow? */
  }
  if (copyover) {
    logit(LOG_EXIT, "Copyover reboot.");
    exit(53);
  }
  logit(LOG_STATUS, "Normal termination of game.");
}



/* The new master timestamper, seconds are calculated back starting from January 1, 1970.
   This time will be used as a timestamp on all events, so that duration of events can
   be calculated by converting the difference between two timestamps to days, hours, etc. */
#define SECS_PER_DAY    (24*60*60)

unsigned int GetTimeStamp(void) {
  struct timeval tp;
  struct timezone tzp;
  unsigned long atime;

  gettimeofday(&tp, &tzp);

  atime = (tp.tv_sec + SECS_PER_DAY);

  return (atime);
}

/* Accept new connects, relay commands, and call 'heartbeat-functs' */

void game_loop(int s) {
  P_char t_ch = NULL, t_ch_f = NULL, tank = NULL;
  P_desc point, next_point;
  char buf[MAX_STRING_LENGTH], comm[MAX_STRING_LENGTH], promptbuf[MAX_INPUT_LENGTH];
  char Gbuf1[MAX_STRING_LENGTH], curr_pos[MAX_STRING_LENGTH];
  char *currentTimeStr;
  int percent, t_ch_p = 0, banned_val, mrtg_counter = -1, position;
  sigset_t mask;
  long ct;
  static struct timeval opt_time;
  struct timeval last_time, now, timespent, timeout, null_time;

#ifdef NEW_SITE_LOOKUPS
  int fd, connError, len;
  bool checkForCompletedConnection;
  bool checkForAvailableReplies;
#endif
  P_obj obj = NULL;
  int t_stamp;

  null_time.tv_sec = 0;
  null_time.tv_usec = 0;

  opt_time.tv_usec = OPT_USEC; /* Init time values */
  opt_time.tv_sec = 0;
  gettimeofday(&last_time, (struct timezone *) 0);
  pulse = 0;

#ifdef  DO_SET_DTABLE_SIZE
  (void) setdtablesize(128);
#endif /* #ifdef DO_SET_DTABLE_SIZE */

#ifdef _HPUX_SOURCE
  /* removed getdtablesize(), HPUX doesn't seem to support it */
  avail_descs = 40; /* !! Change if more needed !! */
#else
  avail_descs = getdtablesize() - 18;
#endif

  sprintf(buf, "avail_descs set to: %d", avail_descs);
  logit(LOG_STATUS, buf);

  sigemptyset(&mask);
  sigaddset(&mask, SIGUSR1);
  sigaddset(&mask, SIGUSR2);
  sigaddset(&mask, SIGINT);
  sigaddset(&mask, SIGPIPE);
  sigaddset(&mask, SIGALRM);
  sigaddset(&mask, SIGTERM);
  sigaddset(&mask, SIGURG);
  sigaddset(&mask, SIGXCPU);
  sigaddset(&mask, SIGHUP);
  sigaddset(&mask, SIGSEGV);

  dead_desc_pool =
          mm_create("SOCKET", sizeof (struct descriptor_data), offsetof(struct descriptor_data, next),
          mm_find_best_chunk(sizeof (struct descriptor_data), 80, 150));

  /* Main loop */
  while (!shutdownflag) {
#ifdef NEW_SITE_LOOKUPS
    FD_ZERO(&input_set);
    FD_ZERO(&output_set);
    checkForCompletedConnection = FALSE;
    checkForAvailableReplies = FALSE;
    for (point = descriptor_list; point; point = point->next) {
      if (!point)
        continue;

      // Let's see if this works... -- CRM
      if ((point->ident_lookup && ((point->ident_lookup->identfd < 0) ||
              (point->ident_lookup->identfd >= FD_SETSIZE))) || (point->dns_lookup &&
              ((point->dns_lookup->dnsfd < 0) || (point->dns_lookup->dnsfd >= FD_SETSIZE)))) {
        logit(LOG_COMM, "ERROR! File descriptor out of bounds, disconnecting");
        logit(LOG_EXIT, "ERROR! File descriptor out of bounds, disconnecting");
        close_socket(point);
        continue;
      }

      if (point->ident_lookup && (point->ident_lookup->state == LOOKUP_CONNECTING)) {
        FD_SET(point->ident_lookup->identfd, &output_set); // want to select on all fds with completed connections
        checkForCompletedConnection = TRUE;
      }

      if (point->dns_lookup && (point->dns_lookup->state == LOOKUP_CONNECTING)) {
        FD_SET(point->dns_lookup->dnsfd, &output_set); // want to select on all fds with completed connections
        checkForCompletedConnection = TRUE;
      }

      if (point->ident_lookup && (point->ident_lookup->state == LOOKUP_RESOLVING)) {
        FD_SET(point->ident_lookup->identfd, &input_set); // want to select on all fds that have something available to read
        checkForAvailableReplies = TRUE;
      }

      if (point->dns_lookup && (point->dns_lookup->state == LOOKUP_RESOLVING)) {
        FD_SET(point->dns_lookup->dnsfd, &input_set); // want to select on all fds that have something available to read
        checkForAvailableReplies = TRUE;
      }
    }

    if (select(FD_SETSIZE, &input_set, &output_set, NULL, &null_time) < 0) {
      logit(LOG_LOOKUP, "failed select [%s]", strerror(errno));
      continue;
    }

    if (checkForCompletedConnection) {
      for (point = descriptor_list; point; point = point->next) {
        // Check a pending IDENT connection
        if (point->ident_lookup && (point->ident_lookup->state == LOOKUP_CONNECTING)) {
          fd = point->ident_lookup->identfd;
          connError = 0;
          len = sizeof (connError);
          if (getsockopt(fd, SOL_SOCKET, SO_ERROR, (void*) &connError, &len) < 0) {
            /* Suppress error message - IDENT failures are common and harmless */
            /* perror("error: getsockopt"); */
          }

          if (FD_ISSET(fd, &output_set)) {
            if (connError == ECONNREFUSED || connError != 0) {
              // connection failed
              logit(LOG_LOOKUP, "Lookup Select pool, failed ident connect(), errno %d [%s]", connError, strerror(connError));
              point->ident_lookup->state = LOOKUP_IDENT_ERROR;
              lookupFSM(point, point->ident_lookup);
              continue;
            } else {
              if (connError == 0) {
                // connection succeded
                point->ident_lookup->state = LOOKUP_CONNECTED;
                lookupFSM(point, point->ident_lookup);
              }
            }
          }
        }

        // Check a pending DNS connection
        if (point->dns_lookup && (point->dns_lookup->state == LOOKUP_CONNECTING)) {
          fd = point->dns_lookup->dnsfd;
          getsockopt(fd, SOL_SOCKET, SO_ERROR, (void*) &connError, &len);
          if (FD_ISSET(fd, &output_set)) {
            if (connError == ECONNREFUSED || connError != 0) {
              // connection failed
              logit(LOG_LOOKUP, "Lookup Select pool, failed DNS connect(), errno %d [%s]", connError, strerror(connError));
              point->dns_lookup->state = LOOKUP_DNS_ERROR;
              lookupFSM(point, point->dns_lookup);
              continue;
            } else {
              if (connError == 0) {
                // connection succeded
                point->dns_lookup->state = LOOKUP_CONNECTED;
                lookupFSM(point, point->dns_lookup);
              }
            }
          }
        }
      }
    }

    // Check if we got any replies back
    if (checkForAvailableReplies) {
      for (point = descriptor_list; point; point = point->next) {
        // Check for ident replies
        if (point->ident_lookup && (point->ident_lookup->state == LOOKUP_RESOLVING)) {
          if (FD_ISSET(point->ident_lookup->identfd, &input_set)) {
            lookupFSM(point, point->ident_lookup);
            if (point->ident_lookup->state == LOOKUP_DONE)
              lookupFSM(point, point->ident_lookup);
            else {
              if (point->ident_lookup->state == LOOKUP_IDENT_ERROR)
                lookupFSM(point, point->ident_lookup);
            }
          }
        }

        // Check for DNS replies
        if (point->dns_lookup && (point->dns_lookup->state == LOOKUP_RESOLVING)) {
          if (FD_ISSET(point->dns_lookup->dnsfd, &input_set)) {
            lookupFSM(point, point->dns_lookup);
            if (point->dns_lookup->state == LOOKUP_DONE)
              lookupFSM(point, point->dns_lookup);
            else {
              if (point->dns_lookup->state == LOOKUP_DNS_ERROR)
                lookupFSM(point, point->dns_lookup);
            }
          }
        }
      }
    }
#endif

    /* Check what's happening out there */
    FD_ZERO(&input_set);
    FD_ZERO(&output_set);
    FD_ZERO(&exc_set);

    // Check for MRTG launch time. --MIAX 4/1/01
    mrtg_counter++;
    if ((mrtg_counter > 60) || (mrtg_counter == -1)) {
      mrtg_counter = 1;
      Process_MRTG_Stats();
    }

    FD_SET(s, &input_set);
    for (point = descriptor_list; point; point = next_point) {
      next_point = point->next;

      FD_SET(point->descriptor, &input_set);
      FD_SET(point->descriptor, &exc_set);
      FD_SET(point->descriptor, &output_set);

      /* site ban code */
      if (point->connected != CON_LOOKUP)
        continue;

      banned_val = bannedsite(point, 0);
      if (banned_val == 1) {
        write_to_descriptor(point, SITE_BAN_TEXT);
        close_socket(point);
        continue;
      } else if (banned_val == 2) {
        write_to_descriptor(point, USER_BAN_TEXT);
        close_socket(point);
        continue;
      } else if (banned_val == 3) {
        write_to_descriptor(point, ANON_USER_BAN_TEXT);
        close_socket(point);
        continue;
      } else if (banned_val == 4) {
        write_to_descriptor(point, BAD_SITE_BAN_TEXT);
        close_socket(point);
        continue;
      } else if (banned_val == 0) {
        /* good connection, send them on their way :) */
        SEND_TO_Q("Please enter your term type (<CR> ansi, '?' help): ", point);
        point->connected = CON_TERM;
        point->wait = 1;
      }
    }

    sigprocmask(SIG_BLOCK, &mask, NULL);

    if (select(FD_SETSIZE, &input_set, &output_set, &exc_set, &null_time) < 0) {
      perror("Select poll");
      continue;
    }
    sigprocmask(SIG_UNBLOCK, &mask, NULL);

    /* Respond to whatever might be happening */

    /* New connection? */
    if (FD_ISSET(s, &input_set))
      if (new_descriptor(s) < 0)
        perror("New connection");

    /* kick out the freaky folks */
    for (point = descriptor_list; point; point = next_point) {
      next_point = point->next;
      if (FD_ISSET(point->descriptor, &exc_set)) {
        logit(LOG_COMM, "Closing socket with exception.  FIXME!");
        close_socket(point);
      } else if (FD_ISSET(point->descriptor, &input_set)) {
        if (process_input(point) < 0)
          close_socket(point);
      }
    }

    if (debug_mode)
      loop_debug(); /* save location of players at time of crash -DCL */

    /* process_commands; */
    for (point = descriptor_list; point; point = next_to_process) {
      next_to_process = point->next;
      t_ch = point->character;

      /* new timeout for non-playing sockets */
      if (point->connected) {
        point->wait++;

        switch (point->connected) {
            /* these are either yes/no or <return> 60 second timeout */
          case CON_APROPOS:
          case CON_FLUSH:
          case CON_NEW_CHAR:
          case CON_NMECNF:
          case CON_QSEX:
          case CON_TERM:
          case CON_FRST_QUIT:
          case CON_NEW_QUIT:
          case CON_QUIT:
            if (point->wait > 240) {
              write_to_descriptor(point, "Idle Timeout\n");
              close_socket(point);
              continue;
            }
            break;

            /* slightly more involved, 10 minute timeout */
          case CON_ALIGN:
          case CON_BONUS1:
          case CON_BONUS2:
          case CON_BONUS3:
          case CON_FRST_NME:
          case CON_HOMETOWN:
          case CON_INFO:
          case CON_INFO_WAIT:
          case CON_NME:
          case CON_PWDCNF:
          case CON_PWDDCNF:
          case CON_PWDGET:
          case CON_PWDNCNF:
          case CON_PWDNEW:
          case CON_PWDNGET:
          case CON_PWDNRM:
          case CON_QCLASS:
          case CON_QRACE:
          case CON_QRETURN:
          case CON_REROLL:
            if (point->wait > 2400) {
              write_to_descriptor(point, "Idle Timeout\r\n");
              close_socket(point);
              continue;
            }
            break;

            /* for remaining states, 15 minutes, same as idle timeout in game */
          default:
            if (point->wait > 3600) {
              write_to_descriptor(point, "Idle Timeout\r\n");
              close_socket(point);
              continue;
            }
            break;
        }
      } else if (IS_AFFECTED(t_ch, AFF_SLOW) && !IS_TRUSTED(t_ch) && (pulse % 2))
        continue;

      if ((!t_ch || (t_ch && (t_ch->specials.command_delays != TRUE)) || descriptor_lock(point, NULL, 1)) &&
              get_from_q(&point->input, comm)) {
        if (descriptor_lock(point, NULL, 1) == 1) {
          lock_descriptor(point, comm);
          continue;
        }

        if (t_ch)
          t_ch->specials.timer = 0;

        point->prompt_mode = 1;

        if (point->editor) /* Advanced editor */
          edit_string_add(point->editor, comm);
        else if (point->olc) /* olc */
          direct_olc_return_input(point->olc, comm);
        else if (point->showstr_count) /* pager for text */
          show_string(point, comm);
        else if (t_ch && IS_PC(t_ch) && t_ch->only.pc->writing)
          string_add(t_ch->only.pc->writing, comm);
        else if (point->connected == CON_PLYNG) {
          if (point->showstr_point)
            show_string(point, comm);
          else {
            timer_start();
            command_interpreter(t_ch, comm);
            timer_stop();
            if (timer_elapsed() > ((port != DFLT_MAIN_PORT) ? 0.025 : .250)) // to simulate it on test with no PCs use 10 times less
              debuglog(51, DS_TIMING, "command [%s] issued by %s took %7.3f seconds to execute", comm, GET_NAME(t_ch), timer_elapsed());
          }
        } else {
          point->wait = 0;

          /* WAD Vaprak paging ability during char gen 4/27/98 */
          if (point->showstr_point)
            show_string(point, comm);

          nanny(point, comm);
        }
      }
    }

    //  Modified and cleaned-up for OLC on 9-22-00 --MIAX.
    for (point = descriptor_list; point; point = next_point) {
      next_point = point->next;
      if (FD_ISSET(point->descriptor, &output_set) && point->output.head) {
        if (process_output(point) < 0)
          close_socket(point);

          /* Commented out on 12/16/00 because I don't know if I really need it. --MIAX */
          /* We do.  --D2 2/3/01 */
        else {
          if (point->character && ((IS_PC(point->character) || IS_MORPH(point->character)) &&
                  !IS_CSET(GET_PLYR(point->character)->only.pc->pcact, PLR_SMARTPROMPT))) {
            /* Not only that, but prompt_mode needs to be 1 if we're not in OLC, NOT the
               other way around.  --D2 */
            if (!point->olc)
              point->prompt_mode = 1;
            else
              point->prompt_mode = 0;
          }
        }
      }
    }

    /* prepare a time string, precalc it here instead of inside of the loop */
    ct = time(0);
    {
      struct tm *tm_info = localtime(&ct);
      if (!tm_info) {
        currentTimeStr = "[time error]";
      } else {
        currentTimeStr = asctime(tm_info);
        *(currentTimeStr + strlen(currentTimeStr) - 1) = '\0';
      }
    }

    /* give the people some prompts o_o */
    for (point = descriptor_list; point; point = next_point) {
      next_point = point->next;
      t_ch = point->character;
      if (t_ch)
        t_ch_f = t_ch->specials.fighting;

      if (!point->prompt_mode)
        continue;

      point->prompt_mode = 0; /* reset it */
      if (t_ch && IS_PC(t_ch) && t_ch->only.pc->writing) {
        if (write_to_descriptor(point, "] ") < 0) {
          logit(LOG_COMM, "Closing socket on write error");
          close_socket(point);
        }
        continue;
      }

      /* WAD Vaprak paging ability during char gen 4/27/98 */
      if ((point->connected) && (point->connected != CON_INFO_WAIT))
        continue;

      /* Don't show a prompt if we are in the new editor 9-22-00 --MIAX */
      if (point->editor)
        continue;

      if (point->showstr_point) {
        if (write_to_descriptor(point, "[RETURN for more, q to quit]") < 0) {
          logit(LOG_COMM, "Closing socket on write error");
          close_socket(point);
        }
        continue;
      }

      /* dont show a prompt if awaiting a yes/no answer - SAM 7-94 */
      if (point->confirm_state == CONFIRM_AWAIT)
        continue;

      if (t_ch) {
        if (IS_NPC(t_ch) && t_ch->desc && t_ch->desc->original)
          t_ch_p = t_ch->desc->original->only.pc->prompt;
        else
          t_ch_p = t_ch->only.pc->prompt;
      }

      //t_ch_p =
      //IS_PC(GET_PLYR(t_ch)) ? GET_PLYR(t_ch)->only.pc->prompt : point->original->only.pc->prompt;

      if (IS_ANSI_TERM(point))
        strcpy(promptbuf, "\033[32m<");
      else
        strcpy(promptbuf, "<");

      if ((IS_PC(GET_PLYR(t_ch))) && t_ch_p) {
        if (IS_SET(t_ch_p, PROMPT_HIT)) {
          if (IS_ANSI_TERM(point)) {
            if (GET_MAX_HIT(t_ch) > 0)
              percent = (100 * GET_HIT(t_ch)) / GET_MAX_HIT(t_ch);
            else
              percent = -1;

            if (percent >= 66)
              sprintf(promptbuf + strlen(promptbuf), "\033[0;32m %dh", t_ch->points.hit);
            else if (percent >= 33)
              sprintf(promptbuf + strlen(promptbuf), "\033[33m %dh", t_ch->points.hit);
            else if (percent >= 15)
              sprintf(promptbuf + strlen(promptbuf), "\033[31m %dh", t_ch->points.hit);
            else
              sprintf(promptbuf + strlen(promptbuf), "\033[31;40;1;5m %dh", t_ch->points.hit);
          } else
            sprintf(promptbuf + strlen(promptbuf), " %dh", t_ch->points.hit);
        }

        if (IS_SET(t_ch_p, PROMPT_MAX_HIT)) {
          if (IS_ANSI_TERM(point))
            sprintf(promptbuf + strlen(promptbuf), "\033[0;32m/%dH", GET_MAX_HIT(t_ch));
          else
            sprintf(promptbuf + strlen(promptbuf), "/%dH", GET_MAX_HIT(t_ch));
        }

#ifdef PCS_USE_MANA
        if (IS_SET(t_ch_p, PROMPT_MANA)) {
          if (IS_ANSI_TERM(point)) {
            if (GET_MAX_MANA(t_ch) > 0)
              percent = (100 * GET_MANA(t_ch)) / GET_MAX_MANA(t_ch);
            else
              percent = -1; /* How could MAX_HIT be < 1?? */
            if (percent >= 66)
              sprintf(promptbuf + strlen(promptbuf), "\033[0;32m %dp", GET_MANA(t_ch));
            else if (percent >= 33)
              sprintf(promptbuf + strlen(promptbuf), "\033[0;33m %dp", GET_MANA(t_ch));
            else if (percent >= 0)
              sprintf(promptbuf + strlen(promptbuf), "\033[0;31m %dp", GET_MANA(t_ch));
          } else
            sprintf(promptbuf + strlen(promptbuf), " %dp", GET_MANA(t_ch));
        }

        if (IS_SET(t_ch_p, PROMPT_MAX_MANA)) {
          if (IS_ANSI_TERM(point))
            sprintf(promptbuf + strlen(promptbuf), "\033[0;32m/%dP", GET_MAX_MANA(t_ch));
          else
            sprintf(promptbuf + strlen(promptbuf), "/%dP", GET_MAX_MANA(t_ch));
        }
#endif
        if (IS_SET(t_ch_p, PROMPT_MOVE)) {
          if (IS_ANSI_TERM(point)) {
            if (GET_MAX_MOVE(GET_PLYR(t_ch)) >= 0)
              percent = (100 * GET_MOVE(GET_PLYR(t_ch))) / GET_MAX_MOVE(t_ch);
            else
              percent = -1;

            if (percent >= 66)
              sprintf(promptbuf + strlen(promptbuf), "\033[0;32m %dv", t_ch->points.move);
            else {
              if (percent >= 33)
                sprintf(promptbuf + strlen(promptbuf), "\033[0;33m %dv", t_ch->points.move);
              else
                sprintf(promptbuf + strlen(promptbuf), "\033[0;31m %dv", t_ch->points.move);
            }
          } else
            sprintf(promptbuf + strlen(promptbuf), " %dv", t_ch->points.move);
        }

        if (IS_SET(t_ch_p, PROMPT_MAX_MOVE)) {
          if (IS_ANSI_TERM(point))
            sprintf(promptbuf + strlen(promptbuf), "\033[0;32m/%dV", GET_MAX_MOVE(t_ch));
          else
            sprintf(promptbuf + strlen(promptbuf), "/%dV", GET_MAX_MOVE(t_ch));
        }

        if (IS_SET(t_ch_p, PROMPT_TWOLINE) &&
                (t_ch_p & (PROMPT_HIT | PROMPT_MAX_HIT | PROMPT_MOVE | PROMPT_MAX_MOVE))) {
          if (IS_NPC(GET_PLYR(t_ch)) || IS_CSET(GET_PLYR(t_ch)->only.pc->pcact, PLR_DUMB_TERM)) {
            if (IS_ANSI_TERM(point))
              strcat(promptbuf, "\033[0;32m >\r\n\033[32m<");
            else
              strcat(promptbuf, " >\r\n<");
          } else {
            if (IS_ANSI_TERM(point))
              strcat(promptbuf, "\033[0;32m >\n\033[32m<");
            else
              strcat(promptbuf, " >\n<");
          }
        }

        /* the prompt elements only active while fighting */
        if (t_ch_f && t_ch->in_room == t_ch_f->in_room) {
          /* TANK elements only active if... */
          if ((tank = t_ch_f->specials.fighting) && (t_ch->in_room == tank->in_room)) {
            if (IS_SET(t_ch_p, PROMPT_TANK_NAME)) {
              if (IS_ANSI_TERM(point))
                sprintf(promptbuf + strlen(promptbuf), " \033[0;34;1mT: %s",
                      ((t_ch != tank) && !CAN_SEE(t_ch, tank)) ? "someone" :
                      (IS_PC(tank) ? ((tank)->player.name) : (FirstWord((tank)->player.name))));
              else
                sprintf(promptbuf + strlen(promptbuf), " T: %s",
                      ((t_ch != tank) && !CAN_SEE(t_ch, tank)) ? "someone" :
                      (IS_PC(tank) ? ((tank)->player.name) : (FirstWord((tank)->player.name))));
            }

            if (IS_SET(t_ch_p, PROMPT_TANK_COND)) {
              if (IS_ANSI_TERM(point))
                strcat(promptbuf, "\033[0m TC:");
              else
                strcat(promptbuf, " TC:");

              if (GET_MAX_HIT(tank) > 0)
                percent = (100 * GET_HIT(tank)) / GET_MAX_HIT(tank);
              else
                percent = -1;

              if (percent >= 100) {
                if (IS_ANSI_TERM(point))
                  strcat(promptbuf, "\033[0;32m excellent");
                else
                  strcat(promptbuf, " excellent");
              } else if (percent >= 90) {
                if (IS_ANSI_TERM(point))
                  strcat(promptbuf, "\033[0;33m few scratches");
                else
                  strcat(promptbuf, " few scratches");
              } else if (percent >= 75) {
                if (IS_ANSI_TERM(point))
                  strcat(promptbuf, "\033[0;33;40;1m small wounds");
                else
                  strcat(promptbuf, " small wounds");
              } else if (percent >= 50) {
                if (IS_ANSI_TERM(point))
                  strcat(promptbuf, "\033[0;35;40;1m few wounds");
                else
                  strcat(promptbuf, " few wounds");
              } else if (percent >= 30) {
                if (IS_ANSI_TERM(point))
                  strcat(promptbuf, "\033[0;35m nasty wounds");
                else
                  strcat(promptbuf, " nasty wounds");
              } else if (percent >= 15) {
                if (IS_ANSI_TERM(point))
                  strcat(promptbuf, "\033[0;31;40;1m pretty hurt");
                else
                  strcat(promptbuf, " pretty hurt");
              } else if (percent >= 0) {
                if (IS_ANSI_TERM(point))
                  strcat(promptbuf, "\033[0;31m awful");
                else
                  strcat(promptbuf, " awful");
              } else {
                if (IS_ANSI_TERM(point))
                  strcat(promptbuf, "\033[0;31m bleeding, close to death");
                else
                  strcat(promptbuf, " bleeding, close to death");
              }
            }
          }

          if (IS_SET(t_ch_p, PROMPT_ENEMY)) {
            if (IS_ANSI_TERM(point))
              sprintf(promptbuf + strlen(promptbuf), " \033[0;31mE: %s",
                    !CAN_SEE(t_ch, t_ch_f) ? "someone" :
                    (IS_PC(t_ch_f) ? ((t_ch_f)->player.name) : (FirstWord((t_ch_f)->player.name))));
            else
              sprintf(promptbuf + strlen(promptbuf), " E: %s",
                    !CAN_SEE(t_ch, t_ch_f) ? "someone" :
                    (IS_PC(t_ch_f) ? ((t_ch_f)->player.name) : (FirstWord((t_ch_f)->player.name))));
          }

          if (IS_SET(t_ch_p, PROMPT_ENEMY_COND)) {
            if (GET_MAX_HIT(t_ch_f) > 0)
              percent = (100 * GET_HIT(t_ch_f)) / GET_MAX_HIT(t_ch_f);
            else
              percent = -1;

            if (IS_ANSI_TERM(point))
              strcat(promptbuf, "\033[0;36m EC:");
            else
              strcat(promptbuf, " EC:");

            if (percent >= 100) {
              if (IS_ANSI_TERM(point))
                strcat(promptbuf, "\033[0;32m excellent");
              else
                strcat(promptbuf, " excellent");
            } else if (percent >= 90) {
              if (IS_ANSI_TERM(point))
                strcat(promptbuf, "\033[0;33m few scratches");
              else
                strcat(promptbuf, " few scratches");
            } else if (percent >= 75) {
              if (IS_ANSI_TERM(point))
                strcat(promptbuf, "\033[0;33;40;1m small wounds");
              else
                strcat(promptbuf, " small wounds");
            } else if (percent >= 50) {
              if (IS_ANSI_TERM(point))
                strcat(promptbuf, "\033[0;35;40;1m few wounds");
              else
                strcat(promptbuf, " few wounds");
            } else if (percent >= 30) {
              if (IS_ANSI_TERM(point))
                strcat(promptbuf, "\033[0;35m nasty wounds");
              else
                strcat(promptbuf, " nasty wounds");
            } else if (percent >= 15) {
              if (IS_ANSI_TERM(point))
                strcat(promptbuf, "\033[0;31;40;1m pretty hurt");
              else
                strcat(promptbuf, " pretty hurt");
            } else if (percent >= 0) {
              if (IS_ANSI_TERM(point))
                strcat(promptbuf, "\033[0;31m awful");
              else
                strcat(promptbuf, " awful");
            } else {
              if (IS_ANSI_TERM(point))
                strcat(promptbuf, "\033[0;31m bleeding, close to death");
              else
                strcat(promptbuf, " bleeding, close to death");
            }
          }
        }

        /* Moradin 01 2002 Display player position */
        if (IS_SET(t_ch_p, PROMPT_POSITION)) {
          if (IS_ANSI_TERM(point))
            strcat(promptbuf, "\033[0;32m");

          strcpy(curr_pos, " P:");
          sprintf(promptbuf + strlen(promptbuf), "%s", curr_pos);
          position = (GET_POS(t_ch));
          if (position == 3) {
            if (IS_ANSI_TERM(point))
              strcat(promptbuf, "\033[0;32m");
            strcpy(curr_pos, " std");
          } else if (position == 2) {
            if (IS_ANSI_TERM(point))
              strcat(promptbuf, "\033[0;33m");
            strcpy(curr_pos, " sit");
          } else if (position == 1) {
            if (IS_ANSI_TERM(point))
              strcat(promptbuf, "\033[0;35m");
            strcpy(curr_pos, " knl");
          } else if (position == 0) {
            if (IS_ANSI_TERM(point))
              strcat(promptbuf, "\033[0;31m");
            strcpy(curr_pos, " rcl");
          }

          sprintf(promptbuf + strlen(promptbuf), "%s", curr_pos);
        }

        /* SAM 7-94 Display god visibility level on prompt */
        if (IS_SET(t_ch_p, PROMPT_VIS) && IS_TRUSTED(t_ch)) {
          if (IS_ANSI_TERM(point))
            strcat(promptbuf, "\033[0;35m");
          sprintf(promptbuf + strlen(promptbuf), " Vis: %d", t_ch->only.pc->wiz_invis);
        }

        if (IS_SET(t_ch_p, PROMPT_TIME)) {
          if (IS_ANSI_TERM(point))
            strcat(promptbuf, "\033[0;31m");
          sprintf(promptbuf + strlen(promptbuf), " %s", currentTimeStr);
        }

        if (IS_CSET(t_ch->only.pc->pcact, PLR_AFK)) {
          if (IS_ANSI_TERM(point))
            strcat(promptbuf, "\033[0m (\033[31;40;1mAFK\033[0m)");
          else
            strcat(promptbuf, " (AFK)");
        }
      }

      if (IS_ANSI_TERM(point))
        strcat(promptbuf, " \033[0;32m> ");
      else
        strcat(promptbuf, " > ");

      if (IS_CSET(t_ch->only.pc->pcact, PLR_SMARTPROMPT) && IS_CSET(t_ch->only.pc->pcact, PLR_COMPACT))
        strcat(promptbuf, "\n");

      if (t_ch_p) {
        if (write_to_descriptor(point, promptbuf) < 0) {
          logit(LOG_COMM, "Closing socket on write error");
          close_socket(point);
          continue;
        }

        if (IS_ANSI_TERM(point)) {
          if (write_to_descriptor(point, "\033[0m") < 0) {
            logit(LOG_COMM, "Closing socket on write error");
            close_socket(point);
            continue;
          }
        }

        if (point->olc) {
          SEND_TO_Q("\r\n", point);
          SEND_TO_Q(olc_prompt(Gbuf1, point->olc), point);
          continue; // --MIAX
        }
      }
    }
#if 0
    /* now free all the chars(mobs) that died last pulse */
    /* this avoids any bad pointers because a char death */
    for (curr_dead = dead_guys; curr_dead; curr_dead = next_guy) {
      next_guy = curr_dead->next_dead;
      free_char(curr_dead);
      curr_dead = NULL;
    }

    dead_guys = NULL;
#endif

    /* handle heartbeat stuff */
    /* Note: pulse now changes every 1/4 sec  */
    if (schedule[pulse])
      Events();

#ifdef OVL
    update_ovl();
#endif

    /* SHIP */
    if (!(pulse % PULSE_VEHICLE))
      ship_activity();

    /* Shorter lasting affects (20 sec) */
    if (!(pulse % SHORT_AFFECT))
      short_affect_update();

    if (!(pulse % 240))
      wimps_in_accept_queue();

    if (IS_ENABLED(CODE_EMS)) {
      if (!(pulse % 240))
        EMS_Process_Mail();
    }

    if (!(pulse % PULSE_VIOLENCE)) {
#ifdef NEWCOMBAT
      CombatEngine();
#else
      perform_violence();
#endif
      /* for action_delays[] related to combat --TAM 04/19/94 */
      for (point = descriptor_list; point; point = point->next) {
        if (point->character && point->connected == CON_PLYNG) {
          t_ch = point->character;

          if (t_ch->specials.action_delays[ACT_DELAY_BASH])
            --t_ch->specials.action_delays[ACT_DELAY_BASH];
          if (t_ch->specials.action_delays[ACT_DELAY_BERSERK])
            --t_ch->specials.action_delays[ACT_DELAY_BERSERK];

          /*
          if (t_ch->specials.action_delays[ACT_DELAY_DISARM])
            --t_ch->specials.action_delays[ACT_DELAY_DISARM];
           */

          if (t_ch->specials.action_delays[ACT_DELAY_INSTANTKILL])
            --t_ch->specials.action_delays[ACT_DELAY_INSTANTKILL];

          if ((t_ch->specials.action_delays[ACT_DELAY_FUMBLING_PRIM] > 0) ||
                  (t_ch->specials.action_delays[ACT_DELAY_DROPPED_PRIM] > 0) ||
                  (t_ch->specials.action_delays[ACT_DELAY_FUMBLING_SECOND] > 0) ||
                  (t_ch->specials.action_delays[ACT_DELAY_DROPPED_SECOND] > 0)) {
            (void) DisarmCheck(t_ch);
          }
          if (t_ch->specials.action_delays[ACT_DELAY_HEADBUTT] > 0) {
            --t_ch->specials.action_delays[ACT_DELAY_HEADBUTT];
          }
          if (t_ch->specials.action_delays[ACT_DELAY_HITALL] > 0) {
            --t_ch->specials.action_delays[ACT_DELAY_HITALL];
          }

          if (t_ch->specials.action_delays[ACT_DELAY_CAST] > 0) {
            --t_ch->specials.action_delays[ACT_DELAY_CAST];
          }
          /* ACT_DELAY_DISARM handled in perform_violence() */
        }
      }
    }

    tics++; /* tics since last checkpoint signal */
    if (shutdownflag == 2) {
      for (point = descriptor_list; point; point = point->next) {
        if (point->character) {
          send_to_char("\007\007*********************************************************\n"
                  "******* The Game will be shut down at 7:30 AM !!! *******\n"
                  "*********************************************************\007\007\n",
                  point->character);
        }
      }
      shutdownflag = 0;
    }

    pulse++;
    if (pulse > 239) {
      pulse = 0;
      check_reboot();
      if (lawful)
        night_watchman();
      affect_update();
      point_update();
    }

    /* blargh, ugly, but needed, to tie combat phase to pulses. JAB */
    if (!(pulse % PULSE_VIOLENCE))
      phase++;
    if (phase > 9)
      phase = 0;

    /* check out the time */
    gettimeofday(&now, (struct timezone *) 0);
    timediff(&timespent, &now, &last_time); /* time used this pulse */
    timediff(&timeout, &opt_time, &timespent); /* time to sleep this pulse */

    if (timeout.tv_sec || timeout.tv_usec) {
      /* This keeps game from being a total processor hog by putting it to
         sleep for the part of each 1/4 second that is not used for game
         processing. */

      sigprocmask(SIG_BLOCK, &mask, NULL);

      if (select(0, (fd_set *) 0, (fd_set *) 0, (fd_set *) 0, &timeout) < 0) {
        perror("Select sleep");
        continue;
      }
      sigprocmask(SIG_UNBLOCK, &mask, NULL);
    }
    gettimeofday(&last_time, (struct timezone *) 0); /* end of pulse reset */
  }

  for (point = descriptor_list; point; point = point->next) {
    if (point->character) {
      /* check for CON_PLYNG before extracting char. -DCL */
      if (!point->connected) {
        /* when you extract_char() a morph, it un_morph's first, which
           results in another save.  Unfortunatly, the save_silent(...3)
           has already nuked all the eq...  so.. just un_morph() them
           before the save_silent */
        if (IS_MORPH(point->character)) {
          if (IS_FIGHTING(point->character))
            stop_fighting(point->character);
          un_morph(point->character);
        }

        write_to_descriptor(point, "\r\nGame is shutting down!\r\n\r\nSaving...\r\n");
        do_save_silent(point->character, 3);
        extract_char(point->character);
        point->character = NULL;
      }
    }
  }

  // what the heck, might as well :P
  write_jackpot();

  // write out new corpse data on old corpses
  for (obj = object_list; obj; obj = obj->next) {
    if (obj->type == ITEM_CORPSE && obj->value[1] == PC_CORPSE) {
      if (sscanf(obj->action_description, " %d %s ", &t_stamp, buf) == 2) {
        t_stamp = (time(0) - t_stamp);
        if (t_stamp / 86400 > MAX_CORPSE_LINGER) {
          if (corpseGetDecayTime(obj) == -1) {
            AddEvent(EVENT_DECAY, 1, TRUE, obj, 0);
            corpseSetDecayTime(obj, 7200); // god pressed corpses get a nominal 5 days rot after MAX_CORPSE_LINGER days old
          }

          writeCorpse(obj);
        }
      } else {
        sprintf(buf, "%s has bad or missing create date on it!", obj->short_description);
        logit(LOG_DEBUG, buf);
        continue;
      }
    }
  }
}

/* ******************************************************************
 *  general utility stuff (for local use)                           *
 ****************************************************************** */

int get_from_q(struct txt_q *queue, char *dest) {
  struct txt_block *tmp;

  /* hmm, could it be this simple? JAB */
  if (!queue) {
    logit(LOG_COMM, "call to get_from_q with NULL queue");
    return (0);
  }
  if (!dest) {
    logit(LOG_COMM, "call to get_from_q with bogus string");
    return (0);
  }
  /* Q empty? */
  if (!queue->head)
    return (0);

  tmp = queue->head;
  strcpy(dest, queue->head->text);
  queue->head = queue->head->next;

#ifdef MEM_DEBUG
  mem_use[MEM_QUEUES] -= (strlen(tmp->text) + sizeof (struct txt_block));

#endif
  free(tmp->text);
  free((char *) tmp);

  return (1);
}

/* trying something, if text packets are small, and don't contain any of
   \r \n > TELOPT_ECHO, it combines the packets to cut the overhead involved
   in sending lots of little packets, as opposed to 1 larger one.  JAB */

/* flag:
   0 - input queue, don't do any extra processing
   1 - concat txt onto preceding queue if possible
   2 - as 1, plus remove doubled $$, (trivial I know)
 */

void write_to_q(const char *txt, struct txt_q *queue, int flag) {
  struct txt_block *new;

  /* hmm, could it be this simple? JAB */
  if (!queue) {
    logit(LOG_COMM, "call to write_to_q with NULL queue");
    return;
  }
  if (!txt || (strlen(txt) >= MAX_STRING_LENGTH)) {
    logit(LOG_COMM, "call to write_to_q with bogus string");
    return;
  }
  /* Q empty? */
  if (!queue->head) {
#ifdef MEM_DEBUG
    mem_use[MEM_QUEUES] += (strlen(txt) + 1 + sizeof (struct txt_block));
#endif
    CREATE(new, struct txt_block, 1);
    CREATE(new->text, char, strlen(txt) + 1);

    strcpy(new->text, txt);

    new->next = NULL;
    queue->head = queue->tail = new;
    return;
  }
  /* something already in Q so try to combine if possible */

  if (flag && ((strlen(txt) + strlen(queue->tail->text)) < MAX_INPUT_LENGTH)) {
    /* combine this text with preceding text */
#ifdef MEM_DEBUG
    mem_use[MEM_QUEUES] += strlen(txt) + 1;
#endif
    RECREATE(queue->tail->text, char, (strlen(txt) + strlen(queue->tail->text) + 1));

    strcat(queue->tail->text, txt);
    return;
  }
  /* nope, it needs to be sent, just add a queue entry */
#ifdef MEM_DEBUG
  mem_use[MEM_QUEUES] += (strlen(txt) + 1 + sizeof (struct txt_block));

#endif
  CREATE(new, struct txt_block, 1);
  CREATE(new->text, char, strlen(txt) + 1);

  strcpy(new->text, txt);

  queue->tail->next = new;
  queue->tail = new;
  new->next = NULL;
}

/* if b > a returns 0 secs, 0 usecs */

void timediff(struct timeval *rslt, struct timeval *a, struct timeval *b) {
  rslt->tv_sec = a->tv_sec - b->tv_sec;
  rslt->tv_usec = a->tv_usec - b->tv_usec;

  while (rslt->tv_usec < 0) {
    rslt->tv_usec += 1000000;
    rslt->tv_sec--;
  }

  while (rslt->tv_usec > 1000000) {
    rslt->tv_usec -= 1000000;
    rslt->tv_sec++;
  }

  if (rslt->tv_sec < 0) {
    rslt->tv_usec = 0;
    rslt->tv_sec = 0;
  }
}

/* Empty the queues before closing connection */

void flush_queues(P_desc d) {
  char str[MAX_STRING_LENGTH];

  str[0] = 0;
  while (get_from_q(&d->output, str));
  while (get_from_q(&d->input, str));
}

/* damn, they want wildcards in the ban strings, sigh. JAB */

int ban_str(const char *haystack, const char *needle) {
  char n_c[MAX_INPUT_LENGTH], *ss_p[20], *n_p;
  char const *h_p;
  int i, j;

  /* quickie first */
  if (strstr(needle, "*") == NULL)
    return (strcmp(haystack, needle) ? 0 : 1);

  /* Ok, we have at least one wildcard, using strsep, we
     break up our search string and save the pointers to the
     various bits in 'ss_p', if there are more than 20
     segments we bitch-slap the god doing it and remove the ban.
     Since all the pointers in ss_p will point to various
     terminated substrings in n_c, we don't have to worry
     about strcpy or malloc.  JAB */

#ifndef _SOLAR2_SOURCE
  i = 0;
  strncpy(n_c, needle, MAX_INPUT_LENGTH - 1);
  n_p = n_c;
  do {
    ss_p[i++] = strsep(&n_p, "*");
    if (n_p != NULL) {
      if (*ss_p[i - 1] != '\0') /* no leading '*' */
        ss_p[i++] = (n_p - 1);
    } else {
      ss_p[i] = (n_c + strlen(needle)); /* terminating NULL */
    }
  } while ((i < 18) && (n_p != NULL));

  if (i > 17)
    return -1;

  /* comparing we will go, an ss_p[x] of 0 corresponds to
     an '*' in the ban string, so we run strstr on the
     FOLLOWING element, if we get a hit, we advance the
     index by 2 and start over, if we don't we return 0
     (no match).  */

  h_p = haystack;

  /* this is the balancing bit, if ban_str STARTS with text,
     rather than an '*' we check it and increment 'j',
     then the rest of the string must follow the '*<text>*<text>*...' pattern.  JAB */

  if (*ss_p[0]) {
    if (!strncmp(h_p, ss_p[0], strlen(ss_p[0]))) {
      h_p += strlen(ss_p[0]);
      j = 1;
    } else
      return 0;
  } else
    j = 0;

  do {
    /* *ss_p[j] is ALWAYS '\0', if *ss_p[j + 1] is ALSO '\0'
       then some feeb has '*'s next to each other, increment
       and continue. */
    if (!*ss_p[j + 1]) { /* multiple '*'s, skip the current one. */
      j++;
      continue;
    }
    j++;
    if ((h_p = strstr(h_p, ss_p[j])) == NULL)
      return 0;
    else {
      h_p += strlen(ss_p[j]);
      j++;
    }
  } while (j < i);

  /* only fall through in the event of a trailing '*', or
     if the final bit of text matches.  Final check, if
     final bit of ban string text was not as long as the
     final bit of haystack, we don't have a match. */
  if ((*ss_p[j - 1] != '\0') && (*h_p != '\0'))
    return 0;

#endif
  return 1;
}

int bannedsite(P_desc d, int flag) {
  struct ban_t *tmp;

  if (!d)
    return 1;

  if (IS_SET(d->lookup_status, 2))
    return -1;

  /* this gets much trickier, since we can now have mutliple bans at the same site (mutliple usernames),
     so we return 1 (TRUE), only on an exact match.  If the identd daemon has not yet returned, and there
     is a username ban at the indicated site, we return -1, which means, check us again, after identd
     returns.  If we get an exact match on a username ban, we return 2, so that messages can be tailored.
     Returns 3 if ident returns no-username, and <anon>@<site> is banned.
   */

  for (tmp = ban_list; tmp; tmp = tmp->next) {
    if (tmp->ban_site[0] == '#') {
      if (flag == 0)
        continue;
      if (!ban_str(d->hostname, (tmp->ban_site + 1)) && !ban_str(d->hostIP, (tmp->ban_site + 1)))
        continue;

    } else if (tmp->ban_site[0] == '~') {
      if (flag != 78)
        continue;
      if (!ban_str(d->hostname, (tmp->ban_site + 1)) && !ban_str(d->hostIP, (tmp->ban_site + 1)))
        continue;

    } else if (tmp->ban_site[0] == '!') {
      if (!ban_str(d->hostname, (tmp->ban_site + 1)) && !ban_str(d->hostIP, (tmp->ban_site + 1)))
        continue;
      banlog(51, "bad site ban (%s) [%s].",
              full_address(d, 0, 0), tmp->banner, tmp->ban_site);
      /* this is a special ban that we added for unresolvable sites, so it has it's own return code, that
       * overrides all other checks */
      return 4;
    } else {
      if (!ban_str(d->hostname, tmp->ban_site) && !ban_str(d->hostIP, tmp->ban_site))
        continue;
    }

    /* at least some of the users at this site are banned, but it may not apply to this user. */

    if (!tmp->ban_user || !tmp->ban_user[0]) {
      banlog(51, "%s, %ssite ban (%s) [%s].",
              full_address(d, 0, 0), flag ? "newchar " : "", tmp->banner, tmp->ban_site);
      return 1; /* not a user ban, so the whole site is banned (or new char banned) */
    }

    /* it's a username ban of some sort, so we have to wait for ident to return */

    if (IS_SET(d->lookup_status, 1))
      return -1; /* identd is still looking it up */

    if ((d->username != NULL) && !str_cmp(tmp->ban_user, d->username)) {
      banlog(51, "%s, %suser ban (%s) [%s@%s].",
              full_address(d, 0, 0), flag ? "newchar " : "", tmp->banner, tmp->ban_user, tmp->ban_site);
      return 2; /* username ban */
    }

    if ((d->username == NULL) && !str_cmp(tmp->ban_user, "<anon>")) {
      banlog(51, "%s, %s<anon> user ban (%s) [%s].",
              full_address(d, 0, 0), flag ? "newchar " : "", tmp->banner, tmp->ban_site);
      return 3; /* anonymous username ban */
    }
  }

  /* get here, and they are in the clear */
  return 0;
}

/* ******************************************************************
 *  socket handling                                                    *
 ****************************************************************** */

int init_socket(int port) {
  char hostname[MAX_HOSTNAME + 1];
  int buffsize, buffer = 0, s, opt = 1;
  struct hostent *hp;
  struct sockaddr_in sa;

  bzero(&sa, sizeof (struct sockaddr_in));

  gethostname(hostname, MAX_HOSTNAME);
  logit(LOG_STATUS, "hostname: %s", hostname);
  hp = gethostbyname(hostname);
  if (hp == NULL) {
    herror("gethostbyname");
    exit(1);
  }
  sa.sin_family = hp->h_addrtype;
  sa.sin_port = htons((unsigned short int) port);
  s = socket(PF_INET, SOCK_STREAM, 0);
  if (s < 0) {
    perror("Init-socket");
    exit(1);
  }
  if (setsockopt(s, SOL_SOCKET, SO_REUSEADDR, (char *) &opt, sizeof (opt)) < 0) {
    perror("setsockopt REUSEADDR");
    exit(1);
  }
  buffsize = sizeof (int);

  if (getsockopt(s, SOL_SOCKET, SO_SNDBUF, (char *) &buffer, &buffsize)) {
    perror("getsockopt SNDBUF");
    exit(1);
  }
  if (buffer < MIN_SOCKET_BUFFER_SIZE) {
    buffer = MIN_SOCKET_BUFFER_SIZE;
    if (setsockopt(s, SOL_SOCKET, SO_SNDBUF, (char *) &buffer, sizeof (buffer)) < 0) {
      perror("setsockopt SNDBUF");
      exit(1);
    }
  }
  if (bind(s, (struct sockaddr *) &sa, sizeof (sa)) < 0) {
    perror("bind");
    close(s);
    exit(1);
  }
  listen(s, 40); /* if new connects still lockup, raise the 50.  JAB */
  return (s);
}

int new_connection(int s) {
  int i, t;
  struct sockaddr_in isa;

  i = sizeof (isa);
  getsockname(s, (struct sockaddr *) &isa, &i);

  if ((t = accept(s, (struct sockaddr *) &isa, &i)) < 0) {
    perror("Accept");
    return (-1);
  }
  nonblock(t);

  return (t);
}

void close_sockets(int s) {
  logit(LOG_STATUS, "Closing all sockets.");

  while (descriptor_list)
    close_socket(descriptor_list);
  close(s);
}

void close_socket(struct descriptor_data *d) {
  struct descriptor_data *tmp;

  if (d->descriptor)
    close(d->descriptor);
  flush_queues(d);
  --used_descs;

#ifdef NEW_SITE_LOOKUPS
  if (d->ident_lookup) {
    d->ident_lookup->state = LOOKUP_IDENT_ERROR;
    lookupFSM(d, d->ident_lookup);
  }
  if (d->dns_lookup) {
    d->dns_lookup->state = LOOKUP_DNS_ERROR;
    lookupFSM(d, d->dns_lookup);
  }
#endif

  /* Forget snooping */
  if (d->snoop.snoop_by) {
    send_to_char("Your victim is no longer among us.\n", d->snoop.snoop_by->character);
    d->snoop.snoop_by->snoop.snooping = 0;
    d->snoop.snoop_by = 0;
  }
  if (d->snoop.snooping) {
    /* if !d->character, or they aren't playing, I can't get their
       level.. so I'll assume its better then 58 to be safe */
    if (d->character && (d->connected == CON_PLYNG) &&
            ((GET_LEVEL(d->character) < 55) ||
            (GET_LEVEL(d->character) <= GET_LEVEL(d->snoop.snooping->character))))
      send_to_char("&+CYou are no longer being snooped.\n", d->snoop.snooping->character);
    d->snoop.snooping->snoop.snoop_by = 0;
    d->snoop.snooping = 0;
  }
  if (d->olc) {
    olc_end(d->olc);
  }

  if (d->character) {
    if (d->connected == CON_PLYNG) {
      act("$n has lost $s link.", TRUE, GET_PLYR(d->character), 0, 0, TO_ROOM);
      if ((NumAttackers(d->character) > 0) && !IS_TRUSTED(d->character)) {
        connectlog(51, "Combat DropLink: %s [%s] in [%d].",
                GET_NAME(GET_PLYR(d->character)), full_address(d, 0, 0),
                (d->character->in_room != NOWHERE) ? world[d->character->in_room].number : -1);
      } else {
        connectlog(d->character->player.level, "%s [%s] has lost link in [%d].",
                GET_NAME(GET_PLYR(d->character)), full_address(d, 0, 0),
                (d->character->in_room != NOWHERE) ? world[d->character->in_room].number : -1);
      }
      writeCharacter(d->character, 1, d->character->in_room);
      d->character->desc = 0;
    } else {
      logit(LOG_COMM, "Losing player: %s [%s].", GET_NAME(GET_PLYR(d->character)), full_address(d, 60, 0));
      /* ok, not in game, but link lost, have to make sure PC_list stays nice now. */
      RemoveFromCharList(d->character);
      free_char(d->character);
      d->character = NULL;
    }
  } else
    logit(LOG_COMM, "Losing descriptor without char.");

  if (next_to_process == d)
    next_to_process = next_to_process->next;
  if (d == descriptor_list)
    descriptor_list = descriptor_list->next;
  else {
    /* Locate the previous element */
    for (tmp = descriptor_list; tmp && (tmp->next != d); tmp = tmp->next);
    if (!tmp || (tmp->next != d))
      dump_core();
    tmp->next = d->next;
  }

  mccp_descriptor_shutdown(d);

  if (d->descriptor)
    shutdown(d->descriptor, 2);

  if (d->showstr_head) {
    free_string(d->showstr_head);
    d->showstr_head = NULL;
  }
  if (d->username) {
    free_string(d->username);
    d->username = NULL;
  }
  if (d->hostname) {
    free_string(d->hostname);
    d->hostname = NULL;
  }
  if (d) {
    mm_release(dead_desc_pool, d);
  }
}

void nonblock(int s) {
  int flags;

  flags = fcntl(s, F_GETFL);
  flags |= O_NONBLOCK;
  if (fcntl(s, F_SETFL, flags) < 0) {
    perror("Nonblock");
    exit(1);
  }
}

int new_descriptor(int s) {
  P_desc newd;
  bool found = FALSE;
  char Gbuf1[MAX_STRING_LENGTH];
  int desc, size;
  struct sockaddr_in sock;
#ifdef SITE_LOOKUPS
  int len;
  struct host_request hr_buf;
  struct ident_request buf;
#endif

  if ((desc = new_connection(s)) < 0)
    return (-1);

  used_descs++;

  // Added for banners. --MIAX 03/28/01
  if (BANNER_ONLY == YES) {
    write_to_descriptor_raw(desc, Banner_Only_Message);
    used_descs--;
    shutdown(desc, 2);
    close(desc);
    return (0);
  }

  if (used_descs >= avail_descs) {
    write_to_descriptor_raw(desc, "Sorry, the game is full...\r\n");
    used_descs--;
    shutdown(desc, 2);
    close(desc);
    return (0);
  } else if (used_descs > max_descs)
    max_descs = used_descs;

  /* find info */
  size = sizeof (sock);

  if (getpeername(desc, (struct sockaddr *) &sock, &size) < 0) {
    perror("getpeername");
    strcpy(Gbuf1, "&+RUNTRACEABLE&n");
    found = TRUE;
  } else {

    sprintf(Gbuf1, "%d.%d.%d.%d",
            ((uchar *) &(sock.sin_addr))[0],
            ((uchar *) &(sock.sin_addr))[1],
            ((uchar *) &(sock.sin_addr))[2],
            ((uchar *) &(sock.sin_addr))[3]);

    if (strlen(Gbuf1) < 8) {
      /* address is garbage, bounce em */
      if (bounce_null_sites) {
        write_to_descriptor_raw(desc, "Your site name is unparseable!\r\n");
        banlog(51, LOG_COMM, "Null site name bounced.");
        shutdown(desc, 2);
        used_descs--;
        close(desc);
        return (0);
      } else {
        strcpy(Gbuf1, "&+RUNTRACEABLE&n");
        found = TRUE;
      }
    }
#ifdef SITE_LOOKUPS
    write_to_descriptor_raw(desc, "Looking up your hostname...\r\n");
#endif
  }

  /* grab a new descriptor struct */

  newd = mm_get(dead_desc_pool);
  bzero(newd, sizeof (struct descriptor_data));

  /* prepend to list */

  newd->next = descriptor_list;
  descriptor_list = newd;

  /* init desc data */

  *newd->buf = '\0';
  *newd->last_input = '\0';
  newd->connected = CON_LOOKUP;
  newd->descriptor = desc;
  newd->input.head = NULL;
  newd->olc = NULL;
  newd->editor = NULL;
  newd->lookup_status = 0;
  newd->output.head = NULL;
  newd->wait = 1;
  newd->timestamp = time(0);
  strcpy(newd->hostIP, Gbuf1);
  newd->hostname = str_dup(Gbuf1);

  /* added this to nuke unresolvable sites BEFORE they block up the hostname lookup queue. */

  switch (bannedsite(newd, 0)) {
    case 4: /* bad site */
      write_to_descriptor_raw(desc, BAD_SITE_BAN_TEXT);
      close_socket(newd);
      return (0);
    case 1: /* completely banned site */
      write_to_descriptor_raw(desc, SITE_BAN_TEXT);
      close_socket(newd);
      return (0);
      /* default is to drop through and carry on */
  }

  free_string(newd->hostname);
  newd->hostname = NULL;
  SET_BIT(newd->lookup_status, LOOKUP_IDENT);
  SET_BIT(newd->lookup_status, LOOKUP_DNS);

  mccp_descriptor_init(newd);

#ifdef NEW_SITE_LOOKUPS
  lookup_initConnection(newd);
#endif

  return (0);
}

/* The following code was contributed by Ithor of ??? */

/*
 **  Return index into color_table. (Ithor)
 **  Did not use toupper, because it tends to screw up on some machines. (mine)
 */

int find_color_entry(int c) {
  int i = 0;
  char s;

  s = UPPER(c);

  while ((color_table[i].symbol != NULL) && (*color_table[i].symbol != s))
    i++;

  return i;
}

/*
 **  Combine multiple entries in the output queue to go to the same file
 **  descriptor. (Max of 2 * MAX_STRING_LENGTH (16K))
 **  Also go through the strings adding color codes when appropriate, and
 **  striping the special symbols when needed.
 */

int process_output(P_desc t) {
  bool flg, bold = FALSE, blink = FALSE;
  char buf[MAX_STRING_LENGTH + 1], buffer[MAX_STRING_LENGTH + 1];
  char sbuf[MAX_STRING_LENGTH + 1], Snoop_buf[MAX_INPUT_LENGTH];
  int i, j, k, bg = 0, xlating, boob_tube = FALSE, ibuf = 0;

  if (!t->connected && IS_PC(t->character)) {
    if (t->character->only.pc && t->prompt_mode == IS_CSET(t->character->only.pc->pcact, PLR_SMARTPROMPT)) { // if t->character->only.pc is NULL we'll infer no smart_prompt
      if (write_to_descriptor(t, "\r\n") < 0)
        return (-1);
    }
  }

  /* commented out for new char gen Vaprak WAD
           if ((t->character != NULL) && IS_ANSI_TERM(t) && (GET_LEVEL(t->character) >= 1))
   */
  if (IS_ANSI_TERM(t))
    flg = TRUE;
  else
    flg = FALSE;

  if (t->character != NULL && IS_PC(t->character)) {
    if (t->character->only.pc && IS_CSET(t->character->only.pc->pcact, PLR_DUMB_TERM))
      boob_tube = TRUE; // if t->character->only.pc is NULL infer no boob_tube
  }

  buf[0] = 0;
  /* Cycle thru output queue */
  while (get_from_q(&t->output, buf)) {
    if (t->snoop.snoop_by) {
      xlating = 1;
      sprintf(Snoop_buf, "&+YS[&n%s&+Y]&n ", t->character ? GET_NAME(t->character) : "???");
      strcpy(sbuf, Snoop_buf);

      i = 0;
      j = strlen(sbuf);
      do {
        switch (buf[i]) {
          case '\0':
            sbuf[j] = buf[i];
            xlating = 0;
            break;
          case '\n':
            sbuf[j++] = buf[i++];
            if (buf[i] == '\r')
              i++;
            if (buf[i]) {
              sbuf[j] = 0;
              strcat(sbuf, Snoop_buf);
              j = strlen(sbuf);
            }
            break;
          case '\r':
            if (buf[i + 1] == '\n')
              i++;
            else
              sbuf[j++] = buf[i++];
            break;
          default:
            sbuf[j++] = buf[i++];
            break;
        }
      } while (xlating);

      write_to_q(sbuf, &t->snoop.snoop_by->output, 1);
    }

    ibuf = strlen(buf);

    /* Go through and convert/strip color symbols -Ithor
     * also apply the extra \r's if boob_tube is TRUE.  JAB */
    for (i = 0, j = 0; (i < ibuf) && (j < (sizeof (buffer))); i++) {
      if (buf[i] == '&') {
        i++;
        if (i >= ibuf)
          continue;

        switch (buf[i]) {
          case '&':
            buffer[j++] = '&';
            break;

          case 'N':
          case 'n':
            if (flg) {
              sprintf(&buffer[j], "\033[0m");
              j += 4;
            }
            break;

          case '+':
          case '-':
            bg = (buf[i] == '-');
            i++;
            if (i >= ibuf)
              continue;

            bold = bg ? 0 : (isupper(buf[i])) ? 1 : 0;
            blink = !bg ? 0 : (isupper(buf[i])) ? 1 : 0;
            k = find_color_entry(buf[i]);
            if (color_table[k].symbol != NULL) {
              if (flg) {
                sprintf(&buffer[j], "\033[%s%s%sm", bold ? "1;" : "", blink ? "5;" : "",
                        (bg ? color_table[k].bg_code : color_table[k].fg_code));
                j += (5 + (bold ? 2 : 0) + (blink ? 2 : 0));
              }
            } else {
              sprintf(&buffer[j], "&%c%c", (bg ? '-' : '+'), buf[i]);
              j += 3;
            }
            break;

          case '=':
            i++;
            if (i >= ibuf)
              continue;

            blink = (isupper(buf[i]) ? 1 : 0);
            bg = find_color_entry(buf[i]);
            i++;
            if (i >= ibuf)
              continue;

            bold = (isupper(buf[i]) ? 1 : 0);
            k = find_color_entry(buf[i]);
            if ((color_table[k].symbol != NULL)
                    && (color_table[bg].symbol != NULL)) {
              if (flg) {
                sprintf(&buffer[j], "\033[%s%s%s;%sm", bold ? "1;" : "", blink ? "5;" : "",
                        color_table[bg].bg_code, color_table[k].fg_code);
                j += (8 + (bold ? 2 : 0) + (blink ? 2 : 0));
              }
            } else {
              sprintf(&buffer[j], "&=%c%c", buf[i - 1], buf[i]);
              j += 4;
            }
            break;

          default:
            sprintf(&buffer[j], "&%c", buf[i]);
            j += 2;
            break;
        }
      } else if (buf[i] == '\n') {
        if (flg) {
          /* Want normal color at EoLN */
          sprintf(&buffer[j], "\033[0m");
          j += 4;
        }
        if (boob_tube && (buf[i + 1] != '\r'))
          buffer[j++] = '\r';
        buffer[j++] = '\n';
      } else {
        buffer[j++] = buf[i];
      }
    }

    buffer[j] = '\0';

    if (write_to_descriptor(t, buffer) < 0)
      return (-1);
  }

  if ((t) && (!t->connected) && !(t->olc) && (t->character) &&
          (IS_PC(GET_PLYR(t->character))) &&
          !IS_CSET(GET_PLYR(t->character)->only.pc->pcact, PLR_COMPACT)) {
    if (boob_tube) {
      if (write_to_descriptor(t, "\r\n") < 0)
        return (-1);
    } else {
      if (write_to_descriptor(t, "\n") < 0)
        return (-1);
    }
  }
  return (1);
}

int write_to_descriptor_raw(int desc, const char *txt) {
  int sofar, thisround, total;

  total = strlen(txt);
  sofar = 0;

  do {
    thisround = write(desc, txt + sofar, (unsigned) (total - sofar));
    if (thisround < 0) {
      logit(LOG_COMM, "Write to socket error");
      return (-1);
    }
    sofar += thisround;
  } while (sofar < total);

  bw_trafficOutgoing(sofar, BW_TTYPE_UNCOMPRESSED);

  return (0);
}

int write_to_descriptor(P_desc d, const char *txt) {
  /* See if the compression context is defined, if so then write
   * process through the compression engine first.. it will take
   * care of writing it out to the socket once done - Altherog '2003 */
  if (d->mccp)
    return mccp_write_to_descriptor(d, txt);
  else
    return write_to_descriptor_raw(d->descriptor, txt);
}

/* this routine takes raw input from a socket (t->buf) and breaks it up and
   massages and filters it before writing it to the input queue (t->input)
   for actual parsing by the mud.  ALL input from sockets must pass through
   this routine.  */

int process_input(P_desc t) {
  int sofar = 0, thisround, begin, squelch, i, k, flag = 0;
  char tmp[MAX_INPUT_LENGTH + 3], buffer[MAX_INPUT_LENGTH + 60], sbuf[MAX_STRING_LENGTH];

  begin = strlen(t->buf);

  /* Read in some stuff */
  do {
    if ((thisround = read(t->descriptor, (t->buf + begin + sofar),
            (unsigned) (MAX_QUEUE_LENGTH - begin - sofar - 1))) > 0)
      sofar += thisround;
    else if (thisround < 0)
      if (errno !=
#ifdef _HPUX_SOURCE
              EAGAIN
#else
              EWOULDBLOCK
#endif
              ) {
        logit(LOG_COMM, "process_input() CON_%d %s Read: %d Error: %d",
                t->connected, (t->character) ? GET_NAME(t->character) : "", thisround, errno);
        return (-1);
      } else
        break;
    else {
      logit(LOG_COMM, "EOF encountered on socket read.");
      return (-1);
    }
  } while (!ISNEWL(*(t->buf + begin + sofar - 1)));

  bw_trafficIncoming(sofar, BW_TTYPE_UNCOMPRESSED);

  *(t->buf + begin + sofar) = 0;

  if (!t->mccp) {
    /* scan for a MCCP reply */
    for (i = begin; *(t->buf + i) && !ISNEWL(*(t->buf + i)); i++)
      if (TRUE == mccp_handshake_reply_scan(t, i)) {
        t->buf[0] = 0;
        return 0;
      }
  }

  /* scan input stream for a newline, if one isn't found, command is not yet ready for processing, so do not
     xfer it to input queue, and return 0, so that this socket is skipped. */

  for (i = begin; !ISNEWL(*(t->buf + i)); i++)
    if (!*(t->buf + i))
      return (0);


  /* input contains 1 or more newlines; process the stuff */

  for (i = 0, k = 0; *(t->buf + i);) {

    if (!ISNEWL(*(t->buf + i)) && !(flag = (k >= (MAX_INPUT_LENGTH - 2)))) {
      /* backspace? */
      if (*(t->buf + i) == '\b') {
        /* more than one char ? */
        if (k) {
          if (*(tmp + --k) == '$')
            k--;
          i++;
        } else {
          /* no chars to delete, so just skip the backspace */
          i++;
        }
      } else {
        if (*(t->buf + i) == '$') {
          /* a '$'?  if so, have to double it, so that act() won't choke on it later on. */
          *(tmp + k) = '$';
          k++;
        }
        /* printable character? */
        if (isascii(*(t->buf + i)) && isprint(*(t->buf + i))) {
          *(tmp + k) = *(t->buf + i);
          i++;
          k++;
        } else {
          /* garbage character, skip it */
          i++;
        }
      }

    } else {
      /* newline or input too long, have to actually DO something with it. */
      *(tmp + k) = 0;

      /* bah! this was in wrong spot, wouldn't catch it until after it had
         hosed memory by overwriting some huge ass string to a little dinky
         char array.  JAB */

      if (k > (MAX_INPUT_LENGTH - 1)) {
        k = MAX_INPUT_LENGTH - 1;
        *(tmp + k) = 0;
        sprintf(buffer, "Line too long. Truncated to:\n%s\n", tmp);
        if (write_to_descriptor(t, buffer) < 0)
          return (-1);

        /* skip the rest of the line */
        for (; *(t->buf + i) && !ISNEWL(*(t->buf + i)); i++);
      }
      /* handle '!' to repeat last command */
      if ((*tmp != '!') || !t->last_input || !t->character || t->connected)
        strcpy(t->last_input, tmp);
      else
        strcpy(tmp, t->last_input);

      write_to_q(tmp, &t->input, 0);

      if (t->snoop.snoop_by) {
        sprintf(sbuf, "&+YS:&n%s&+Y:&n %s\n", t->character ? GET_NAME(t->character) : "???", tmp);
        write_to_q(sbuf, &t->snoop.snoop_by->output, 1);
      }
      /* find end of entry */
      for (; ISNEWL(*(t->buf + i)); i++);

      /* squelch the entry from the buffer */
      for (squelch = 0;; squelch++)
        if ((*(t->buf + squelch) = *(t->buf + i + squelch)) == '\0')
          break;
      k = 0;
      i = 0;
    }
  }
  return (1);
}

/* ****************************************************************
 *    Public routines for system-to-player-communication        *
 **************************************************************** */

void send_to_char_f(P_char ch, const char *format, ...) {
  va_list args;
  static char gbuf[MAX_STRING_LENGTH];

  va_start(args, format);
  vsnprintf(gbuf, MAX_STRING_LENGTH, format, args);
  va_end(args);

  send_to_char(gbuf, ch);
}

void send_to_char(const char *messg, P_char ch) {

  if (ch->desc && messg)
    write_to_q(messg, &ch->desc->output, 2);
}

void send_to_all(const char *messg) {
  P_desc i;

  if (messg)
    for (i = descriptor_list; i; i = i->next)
      if (!i->connected)
        write_to_q(messg, &i->output, 2);
}

void send_to_outdoor(const char *messg) {
  P_desc i;

  if (messg)
    for (i = descriptor_list; i; i = i->next)
      if (!i->connected && OUTSIDE(i->character))
        if (IS_TRUSTED(i->character) || !IS_CSET(world[i->character->in_room].room_flags, ROOM_SILENT))
          write_to_q(messg, &i->output, 2);
}

void send_to_zone_func(int z, int mask, const char *msg) {
  P_char tch;

  for (tch = PC_list; tch; tch = tch->next) {
    if (tch->desc && tch->desc->connected)
      continue; /* not in game */

    if ((tch->in_room == NOWHERE) || (world[tch->in_room].zone != z))
      continue;
    if (mask > 0)
      if (!IS_CSET(world[tch->in_room].room_flags, mask))
        continue;
    if (mask < 0)
      if (IS_CSET(world[tch->in_room].room_flags, -mask))
        continue;
    send_to_char(msg, tch);
  }
}

void send_to_zone_outdoor(int z_num, const char *messg) {
  send_to_zone_func(z_num, (int) (-INDOORS), messg);
}

void send_to_zone_indoor(int z_num, const char *messg) {
  send_to_zone_func(z_num, (int) INDOORS, messg);
}

void send_to_zone(int z_num, const char *msg) {
  send_to_zone_func(z_num, 0, msg);
}

void send_to_except(const char *messg, P_char ch) {
  P_desc i;

  if (messg)
    for (i = descriptor_list; i; i = i->next)
      if (ch->desc != i && !i->connected)
        if (IS_TRUSTED(i->character) || !IS_CSET(world[i->character->in_room].room_flags, ROOM_SILENT))
          write_to_q(messg, &i->output, 2);
}

void send_to_room(const char *messg, int room) {
  P_char i;

  if (messg)
    for (i = world[room].people; i; i = i->next_in_room)
      if (i->desc)
        if (IS_TRUSTED(i) || !IS_CSET(world[i->in_room].room_flags, ROOM_SILENT))
          write_to_q(messg, &i->desc->output, 2);

  if (IS_CSET(world[room].room_flags, DIFFUSE_MESSAGES) && !IS_CSET(world[i->in_room].room_flags, ROOM_SILENT))
    replicator((char *) messg, room);

}

void send_to_room_except(const char *messg, int room, P_char ch) {
  P_char i;

  if (messg)
    for (i = world[room].people; i; i = i->next_in_room)
      if ((i != ch) && i->desc) {
        write_to_q(messg, &i->desc->output, 2);
      }
}

void send_to_room_except_two(const char *messg, int room, P_char ch1, P_char ch2) {
  P_char i;

  if (messg)
    for (i = world[room].people; i; i = i->next_in_room) {
      if (!i->desc || (i == ch1) || (i == ch2))
        continue;
      if (IS_TRUSTED(i) || !IS_CSET(world[i->in_room].room_flags, ROOM_SILENT))
        write_to_q(messg, &i->desc->output, 2);
    }
}

void combined_act(const char *str_to_char, const char *str_to_vict, const char *str_to_notvict, int hide_invisible, P_char ch, P_obj obj, void *vict_obj) {

  act(str_to_char, hide_invisible, ch, obj, vict_obj, TO_CHAR);
  act(str_to_vict, hide_invisible, ch, obj, vict_obj, TO_VICT);
  act(str_to_notvict, hide_invisible, ch, obj, vict_obj, TO_NOTVICT);

  return;
}

/* higher-level communication */

void act(const char *str, int hide_invisible, P_char ch, P_obj obj, void *vict_obj, int type) {
  P_char to = NULL;
  bool found;
  char buf[MAX_STRING_LENGTH], tbuf[MAX_STRING_LENGTH];
  int j, tbp, skip;
  register char *point;
  register const char *strp, *i;
  int sent = 0;
  int skip_q = 0;

  if (!str || !*str)
    return;

  if (type == TO_VICT)
    to = (P_char) vict_obj;
  else if (type == TO_CHAR)
    to = ch;
  else {
    if (!ch && obj) {
      if (!OBJ_ROOM(obj)) {
        logit(LOG_DEBUG, "act: no ch, and obj in nowhere");
        dump_stack("act: no ch, and obj in nowhere");
        return;
      }
      to = world[obj->loc.room].people;
    } else {
      if (ch->in_room == NOWHERE) {
        logit(LOG_DEBUG, "act TO_ROOM in NOWHERE %s (%s).", GET_NAME(ch), str);
        return;
      }
      to = world[ch->in_room].people;
    }
  }

  if (!to)
    return; /* if a tree falls in the forest... */

  for (; to; to = to->next_in_room) {
    if (!to->desc) {
      if ((IS_CSET(world[to->in_room].room_flags, DIFFUSE_MESSAGES)) && (!sent) && ((type == TO_ROOM) || (type == TO_NOTVICT)))
        skip_q = 1;
      else
        continue;
    }

    if (!AWAKE(to))
      continue;

    if ((type == TO_CHAR) && (to != ch))
      continue;

    if ((type == TO_ROOM) && (to == ch)) {
      if ((IS_CSET(world[to->in_room].room_flags, DIFFUSE_MESSAGES)) && (!sent))
        skip_q = 1;
      else
        continue;
    }

    if ((type == TO_VICT) && (to != (P_char) vict_obj))
      continue;

    if ((type == TO_NOTVICT) && ((to == (P_char) vict_obj) || (to == ch))) {
      if ((IS_CSET(world[ch->in_room].room_flags, DIFFUSE_MESSAGES)) && (!sent))
        skip_q = 1;
      else
        continue;
    }


    if (hide_invisible && !(ch ? CAN_SEE(to, ch) : CAN_SEE_OBJ(to, obj)))
      continue;

    if (IS_PC(to) && to->only.pc->ignored && (to->only.pc->ignored == ch))
      continue;

    buf[0] = 0;
    tbuf[0] = 0;

    for (strp = str, point = buf;;)
      if (*strp == '$') {
        j = 0;
        i = NULL;
        switch (*(++strp)) {
          case 'n':
            if (ch && to)
              i = PERS(ch, to);
            break;
          case 'N':
            if (vict_obj && to)
              i = PERS((P_char) vict_obj, to);
            break;
          case 'm':
            if (ch)
              i = HMHR(ch);
            break;
          case 'M':
            if (vict_obj)
              i = HMHR((P_char) vict_obj);
            break;
          case 's':
            if (ch)
              i = HSHR(ch);
            break;
          case 'S':
            if (vict_obj)
              i = HSHR((P_char) vict_obj);
            break;
          case 'e':
            if (ch)
              i = HSSH(ch);
            break;
          case 'E':
            if (vict_obj)
              i = HSSH((P_char) vict_obj);
            break;
          case 'o':
            if (obj && to)
              i = OBJN(obj, to);
            break;
          case 'O':
            if (vict_obj && to)
              i = OBJN((P_obj) vict_obj, to);
            break;
          case 'p':
            if (obj && to)
              i = OBJS(obj, to);
            break;
          case 'P':
            if (vict_obj && to)
              i = OBJS((P_obj) vict_obj, to);
            break;
            /* 'q's' are same as p's except it kills 'A |An |The' from the start of the string,
               it's ugly, cause we have to skip leading ansi stuff. JAB */
          case 'q':
          case 'Q':
            *tbuf = '\0';
            tbp = 0;
            skip = 0;
            found = FALSE;
            if (*strp == 'Q') {
              if (vict_obj && to)
                i = OBJS((P_obj) vict_obj, to);
            } else {
              if (obj && to)
                i = OBJS(obj, to);
            }
            if (i == NULL)
              break;

            for (; *i; i++) {
              if (skip) {
                skip--;
              } else {
                /* ANSI skipping */
                if (!found && (*i == '&')) {
                  if ((*(i + 1) == 'N') || (*(i + 1) == 'N'))
                    skip = 1;
                  else if ((*(i + 1) == '-') || (*(i + 1) == '+'))
                    skip = 2;
                  else if (*(i + 1) == '=')
                    skip = 3;
                }
                if (!found && (LOWER(*i) == 'a'))
                  if (*(i + 1)) {
                    if (*(i + 1) == ' ') {
                      found = TRUE;
                      i++;
                      continue;
                    }
                    if ((LOWER(*(i + 1)) == 'n') && *(i + 2) && (*(i + 2) == ' ')) {
                      found = TRUE;
                      i += 2;
                      continue;
                    }
                  }
                if (!found && (LOWER(*i) == 't'))
                  if ((LOWER(*(i + 1)) == 'h') && (LOWER(*(i + 2)) == 'e') && (*(i + 3) == ' ')) {
                    found = TRUE;
                    i += 3;
                    continue;
                  }
              }
              tbuf[tbp++] = *i;
            }
            tbuf[tbp++] = 0;
            i = tbuf;
            break;
          case 'a':
            if (obj)
              i = SANA(obj);
            break;
          case 'A':
            if (vict_obj)
              i = SANA((P_obj) vict_obj);
            break;
          case 'T':
            if (vict_obj)
              i = (char *) vict_obj;
            break;
          case 'F':
            if (vict_obj)
              i = FirstWord((char *) vict_obj);
            break;
          case '$':
            i = "$";
            break;
          default:
            logit(LOG_DEBUG, "Invalid $-code, act(): $%c %s", *strp, str);
            break;
        }
        if (i)
          while (*(i + j))
            *(point++) = *(i + j++);

        ++strp;
      } else if (!(*(point++) = *(strp++)))
        break;

    *(--point) = '\n';
    *(++point) = '\0';

    CAP(buf);

    if ((to->desc) && (!skip_q))
      write_to_q(buf, &to->desc->output, 1);

    if ((type == TO_VICT) || (type == TO_CHAR))
      return;

    else if (!sent) {
      sent = 1;
      if (ch) {
        if (IS_CSET(world[ch->in_room].room_flags, DIFFUSE_MESSAGES))
          replicator(buf, ch->in_room);
      }
      else if (obj) {
        if (IS_CSET(world[obj->loc.room].room_flags, DIFFUSE_MESSAGES))
          replicator(buf, obj->loc.room);
      } else
        return;
    }

  }

}

// For normal combat messages

void c_act(const char *str, int hide_invisible, P_char ch, P_obj obj, void *vict_obj, int type, uint condense) {
  P_char to = NULL;
  bool found;
  char buf[MAX_STRING_LENGTH], tbuf[MAX_STRING_LENGTH];
  int j, tbp, skip;
  register char *point;
  register const char *strp, *i;
  int sent = 0;
  int skip_q = 0;

  if (!str || !*str)
    return;

  if (type == TO_VICT)
    to = (P_char) vict_obj;
  else if (type == TO_CHAR)
    to = ch;
  else {
    if (!ch && obj) {
      if (!OBJ_ROOM(obj)) {
        logit(LOG_DEBUG, "act: no ch, and obj in nowhere");
        dump_stack("c_act: no ch, and obj in nowhere");
        return;
      }
      to = world[obj->loc.room].people;
    } else {
      if (ch->in_room == NOWHERE) {
        logit(LOG_DEBUG, "act TO_ROOM in NOWHERE %s (%s).", GET_NAME(ch), str);
        return;
      }
      to = world[ch->in_room].people;
    }
  }

  if (!to)
    return; /* if a tree falls in the forest... */

  for (; to; to = to->next_in_room) {
    if (!to->desc) {
      if ((IS_CSET(world[to->in_room].room_flags, DIFFUSE_MESSAGES)) && (!sent) && ((type == TO_ROOM) || (type == TO_NOTVICT)))
        skip_q = 1;
      else
        continue;
    }

    // ONLY send this to people with condensed combat togged off
    // Added New condensed flags - Iyachtu

    if ((spammy) || (condense != MELEE_HITS)) {
      if ((type != TO_VICT) && (to != ch)) {
        if (is_condensed(to, condense, OTHER))
          continue;
      } else if (is_condensed(to, condense, SELF))
        continue;
    }

    if (!AWAKE(to))
      continue;

    if ((type == TO_CHAR) && (to != ch))
      continue;

    if ((type == TO_ROOM) && (to == ch)) {
      if ((IS_CSET(world[to->in_room].room_flags, DIFFUSE_MESSAGES)) && (!sent))
        skip_q = 1;
      else
        continue;
    }

    if ((type == TO_VICT) && (to != (P_char) vict_obj))
      continue;

    if ((type == TO_NOTVICT) && ((to == (P_char) vict_obj) || (to == ch))) {
      if ((IS_CSET(world[ch->in_room].room_flags, DIFFUSE_MESSAGES)) && (!sent))
        skip_q = 1;
      else
        continue;
    }


    if (hide_invisible && !(ch ? CAN_SEE(to, ch) : CAN_SEE_OBJ(to, obj)))
      continue;

    if (IS_PC(to) && to->only.pc->ignored && (to->only.pc->ignored == ch))
      continue;

    buf[0] = 0;
    tbuf[0] = 0;

    for (strp = str, point = buf;;)
      if (*strp == '$') {
        j = 0;
        i = NULL;
        switch (*(++strp)) {
          case 'n':
            if (ch && to)
              i = PERS(ch, to);
            break;
          case 'N':
            if (vict_obj && to)
              i = PERS((P_char) vict_obj, to);
            break;
          case 'm':
            if (ch)
              i = HMHR(ch);
            break;
          case 'M':
            if (vict_obj)
              i = HMHR((P_char) vict_obj);
            break;
          case 's':
            if (ch)
              i = HSHR(ch);
            break;
          case 'S':
            if (vict_obj)
              i = HSHR((P_char) vict_obj);
            break;
          case 'e':
            if (ch)
              i = HSSH(ch);
            break;
          case 'E':
            if (vict_obj)
              i = HSSH((P_char) vict_obj);
            break;
          case 'o':
            if (obj && to)
              i = OBJN(obj, to);
            break;
          case 'O':
            if (vict_obj && to)
              i = OBJN((P_obj) vict_obj, to);
            break;
          case 'p':
            if (obj && to)
              i = OBJS(obj, to);
            break;
          case 'P':
            if (vict_obj && to)
              i = OBJS((P_obj) vict_obj, to);
            break;
            /* 'q's' are same as p's except it kills 'A |An |The' from the start of the string,
               it's ugly, cause we have to skip leading ansi stuff. JAB */
          case 'q':
          case 'Q':
            *tbuf = '\0';
            tbp = 0;
            skip = 0;
            found = FALSE;
            if (*strp == 'Q') {
              if (vict_obj && to)
                i = OBJS((P_obj) vict_obj, to);
            } else {
              if (obj && to)
                i = OBJS(obj, to);
            }
            if (i == NULL)
              break;

            for (; *i; i++) {
              if (skip) {
                skip--;
              } else {
                /* ANSI skipping */
                if (!found && (*i == '&')) {
                  if ((*(i + 1) == 'N') || (*(i + 1) == 'N'))
                    skip = 1;
                  else if ((*(i + 1) == '-') || (*(i + 1) == '+'))
                    skip = 2;
                  else if (*(i + 1) == '=')
                    skip = 3;
                }
                if (!found && (LOWER(*i) == 'a'))
                  if (*(i + 1)) {
                    if (*(i + 1) == ' ') {
                      found = TRUE;
                      i++;
                      continue;
                    }
                    if ((LOWER(*(i + 1)) == 'n') && *(i + 2) && (*(i + 2) == ' ')) {
                      found = TRUE;
                      i += 2;
                      continue;
                    }
                  }
                if (!found && (LOWER(*i) == 't'))
                  if ((LOWER(*(i + 1)) == 'h') && (LOWER(*(i + 2)) == 'e') && (*(i + 3) == ' ')) {
                    found = TRUE;
                    i += 3;
                    continue;
                  }
              }
              tbuf[tbp++] = *i;
            }
            tbuf[tbp++] = 0;
            i = tbuf;
            break;
          case 'a':
            if (obj)
              i = SANA(obj);
            break;
          case 'A':
            if (vict_obj)
              i = SANA((P_obj) vict_obj);
            break;
          case 'T':
            if (vict_obj)
              i = (char *) vict_obj;
            break;
          case 'F':
            if (vict_obj)
              i = FirstWord((char *) vict_obj);
            break;
          case '$':
            i = "$";
            break;
          default:
            logit(LOG_DEBUG, "Invalid $-code, act(): $%c %s", *strp, str);
            break;
        }
        if (i)
          while (*(i + j))
            *(point++) = *(i + j++);

        ++strp;
      } else if (!(*(point++) = *(strp++)))
        break;

    *(--point) = '\n';
    *(++point) = '\0';

    CAP(buf);

    if ((to->desc) && (!skip_q))
      write_to_q(buf, &to->desc->output, 1);

    if ((type == TO_VICT) || (type == TO_CHAR))
      return;

    else if (!sent) {
      sent = 1;
      if (ch) {
        if (IS_CSET(world[ch->in_room].room_flags, DIFFUSE_MESSAGES))
          replicator(buf, ch->in_room);
      }
      else if (obj) {
        if (IS_CSET(world[obj->loc.room].room_flags, DIFFUSE_MESSAGES))
          replicator(buf, obj->loc.room);
      } else
        return;
    }

  }

}


// For multiple combat messages
/* Added int condense... function calling cm_act tells it which */

/* Condensed flag to check - Iyachtu */
void cm_act(const char *str, int hide_invisible, P_char ch, P_obj obj, void *vict_obj, int type, uint condense) {
  P_char to = NULL;
  bool found;
  char buf[MAX_STRING_LENGTH], tbuf[MAX_STRING_LENGTH];
  int j, tbp, skip;
  register char *point;
  register const char *strp, *i;
  int sent = 0;
  int skip_q = 0;
  int gag = 0;

  if (!str || !*str)
    return;

  if (type == TO_VICT)
    to = (P_char) vict_obj;
  else if (type == TO_CHAR)
    to = ch;
  else {
    if (!ch && obj) {
      if (!OBJ_ROOM(obj)) {
        logit(LOG_DEBUG, "act: no ch, and obj in nowhere");
        dump_stack("cm_act: no ch, and obj in nowhere");
        return;
      }
      to = world[obj->loc.room].people;
    } else {
      if (ch->in_room == NOWHERE) {
        logit(LOG_DEBUG, "act TO_ROOM in NOWHERE %s (%s).", GET_NAME(ch), str);
        return;
      }
      to = world[ch->in_room].people;
    }
  }

  if (!to)
    return; /* if a tree falls in the forest... */

  for (; to; to = to->next_in_room) {
    if (!to->desc) {
      if ((IS_CSET(world[to->in_room].room_flags, DIFFUSE_MESSAGES)) && (!sent) && ((type == TO_ROOM) || (type == TO_NOTVICT)))
        skip_q = 1;
      else
        continue;
    }

    // New spanky function call - Iyachtu
    if ((type != TO_VICT) && (to != ch))
      gag = is_condensed(to, condense, OTHER);
    else
      gag = is_condensed(to, condense, SELF);

    if (IS_PC(to) && (!gag))
      continue;

    if (!AWAKE(to))
      continue;

    if ((type == TO_CHAR) && (to != ch))
      continue;

    if ((type == TO_ROOM) && (to == ch)) {
      if ((IS_CSET(world[to->in_room].room_flags, DIFFUSE_MESSAGES)) && (!sent))
        skip_q = 1;
      else
        continue;
    }

    if ((type == TO_VICT) && (to != (P_char) vict_obj))
      continue;

    if ((type == TO_NOTVICT) && ((to == (P_char) vict_obj) || (to == ch))) {
      if ((IS_CSET(world[ch->in_room].room_flags, DIFFUSE_MESSAGES)) && (!sent))
        skip_q = 1;
      else
        continue;
    }


    if (hide_invisible && !(ch ? CAN_SEE(to, ch) : CAN_SEE_OBJ(to, obj)))
      continue;

    if (IS_PC(to) && to->only.pc->ignored && (to->only.pc->ignored == ch))
      continue;

    buf[0] = 0;
    tbuf[0] = 0;

    for (strp = str, point = buf;;)
      if (*strp == '$') {
        j = 0;
        i = NULL;
        switch (*(++strp)) {
          case 'n':
            if (ch && to)
              i = PERS(ch, to);
            break;
          case 'N':
            if (vict_obj && to)
              i = PERS((P_char) vict_obj, to);
            break;
          case 'm':
            if (ch)
              i = HMHR(ch);
            break;
          case 'M':
            if (vict_obj)
              i = HMHR((P_char) vict_obj);
            break;
          case 's':
            if (ch)
              i = HSHR(ch);
            break;
          case 'S':
            if (vict_obj)
              i = HSHR((P_char) vict_obj);
            break;
          case 'e':
            if (ch)
              i = HSSH(ch);
            break;
          case 'E':
            if (vict_obj)
              i = HSSH((P_char) vict_obj);
            break;
          case 'o':
            if (obj && to)
              i = OBJN(obj, to);
            break;
          case 'O':
            if (vict_obj && to)
              i = OBJN((P_obj) vict_obj, to);
            break;
          case 'p':
            if (obj && to)
              i = OBJS(obj, to);
            break;
          case 'P':
            if (vict_obj && to)
              i = OBJS((P_obj) vict_obj, to);
            break;
            /* 'q's' are same as p's except it kills 'A |An |The' from the start of the string,
               it's ugly, cause we have to skip leading ansi stuff. JAB */
          case 'q':
          case 'Q':
            *tbuf = '\0';
            tbp = 0;
            skip = 0;
            found = FALSE;
            if (*strp == 'Q') {
              if (vict_obj && to)
                i = OBJS((P_obj) vict_obj, to);
            } else {
              if (obj && to)
                i = OBJS(obj, to);
            }
            if (i == NULL)
              break;

            for (; *i; i++) {
              if (skip) {
                skip--;
              } else {
                /* ANSI skipping */
                if (!found && (*i == '&')) {
                  if ((*(i + 1) == 'N') || (*(i + 1) == 'N'))
                    skip = 1;
                  else if ((*(i + 1) == '-') || (*(i + 1) == '+'))
                    skip = 2;
                  else if (*(i + 1) == '=')
                    skip = 3;
                }
                if (!found && (LOWER(*i) == 'a'))
                  if (*(i + 1)) {
                    if (*(i + 1) == ' ') {
                      found = TRUE;
                      i++;
                      continue;
                    }
                    if ((LOWER(*(i + 1)) == 'n') && *(i + 2) && (*(i + 2) == ' ')) {
                      found = TRUE;
                      i += 2;
                      continue;
                    }
                  }
                if (!found && (LOWER(*i) == 't'))
                  if ((LOWER(*(i + 1)) == 'h') && (LOWER(*(i + 2)) == 'e') && (*(i + 3) == ' ')) {
                    found = TRUE;
                    i += 3;
                    continue;
                  }
              }
              tbuf[tbp++] = *i;
            }
            tbuf[tbp++] = 0;
            i = tbuf;
            break;
          case 'a':
            if (obj)
              i = SANA(obj);
            break;
          case 'A':
            if (vict_obj)
              i = SANA((P_obj) vict_obj);
            break;
          case 'T':
            if (vict_obj)
              i = (char *) vict_obj;
            break;
          case 'F':
            if (vict_obj)
              i = FirstWord((char *) vict_obj);
            break;
          case '$':
            i = "$";
            break;
          default:
            logit(LOG_DEBUG, "Invalid $-code, act(): $%c %s", *strp, str);
            break;
        }
        if (i)
          while (*(i + j))
            *(point++) = *(i + j++);

        ++strp;
      } else if (!(*(point++) = *(strp++)))
        break;

    *(--point) = '\n';
    *(++point) = '\0';

    CAP(buf);

    if ((to->desc) && (!skip_q))
      write_to_q(buf, &to->desc->output, 1);

    if ((type == TO_VICT) || (type == TO_CHAR))
      return;

    else if (!sent) {
      sent = 1;
      if (ch) {
        if (IS_CSET(world[ch->in_room].room_flags, DIFFUSE_MESSAGES))
          replicator(buf, ch->in_room);
      }
      else if (obj) {
        if (IS_CSET(world[obj->loc.room].room_flags, DIFFUSE_MESSAGES))
          replicator(buf, obj->loc.room);
      } else
        return;
    }

  }

}

int descriptor_lock(P_desc d, char *arg, int cmd) {
  return 0;
}

void lock_descriptor(P_desc d, char *arg) {

}

void replicator(char *msg, int room) {
  struct linked_room *i;
  char Buf[MAX_STRING_LENGTH];

  sprintf(Buf, "From: %s\n", world[room].name);

  for (i = world[room].linked; i; i = i->next_room) {
    send_to_room(Buf, i->room);
    send_to_room(msg, i->room);
  }
}

void link_room(int room_linked, int room_target) {
  struct linked_room *new_data, *data;

  CREATE(new_data, struct linked_room, 1);

  new_data->room = room_linked;

  if (!world[room_target].linked) {
    world[room_target].linked = new_data;
    SET_CBIT(world[room_target].room_flags, DIFFUSE_MESSAGES);
  } else {
    data = world[room_target].linked;
    while (data->next_room)
      data = data->next_room;
    data->next_room = new_data;
  }
}

void unlink_room(int room_linked, int room_target) {
  struct linked_room *new_data, *data;

  if (world[room_target].linked->room == room_linked) { /* head of list */

    data = world[room_target].linked;
    world[room_target].linked = data->next_room;
    free(data);
  } else {
    /* locate the previous element */
    for (new_data = world[room_target].linked; new_data && (new_data->next_room->room != room_linked); new_data = new_data->next_room);
    if (!new_data) {
      logit(LOG_EXIT, "called unlink_room, room not in room list");
      dump_core();
    }

    data = new_data->next_room;
    new_data->next_room = data->next_room;
    free(data);
  }

  if (!world[room_target].linked)
    REMOVE_CBIT(world[room_target].room_flags, DIFFUSE_MESSAGES);

}

bool is_linked(int room_linked, int room_target) {
  struct linked_room *data;

  data = world[room_target].linked;

  while (data) {
    if (data->room == room_linked)
      return TRUE;
    data = data->next_room;
  }

  return FALSE;
}

