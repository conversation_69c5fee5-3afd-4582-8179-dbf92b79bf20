# Outcast MUD Trade System - Comprehensive Analysis & Implementation Guide

## Executive Summary

The Outcast MUD trade system implements a sophisticated economic simulation with 46 tradeable commodities plus 1 derived industrial product (47 total) across 9 different town economic types. The system uses production/consumption multipliers stored in trade files (.trd), with dynamic pricing based on supply and demand.

## 1. System Architecture

### Core Components
- **Commodities**: 46 tradeable items + 1 industrial product
- **Trade Files**: 9 .trd files containing 94 integers each
- **Town Types**: 4 primary + 5 hybrid economies
- **Price Formula**: Dynamic 0.7-1.3x multiplier based on supply/demand
- **Population Scaling**: Production/consumption = weight × population / 5000

### File Structure
Each .trd file contains exactly 94 integers:
- **Lines 1-92**: Production/Consumption pairs for commodities 0-45
- **Lines 93-94**: Production/Consumption for industrial product (index 46)

**Mapping Formula**:
```
For commodity i (0-46):
  Production weight = file_value[i*2]
  Consumption weight = file_value[i*2+1]
```

## 2. Complete Commodity List (47 items)

### Precious Metals & Coins (0-3)
| ID | Name | Base Value | Category |
|----|------|------------|----------|
| 0 | Gold | 10,000 | Mineral |
| 1 | Silver | 1,000 | Mineral |
| 2 | Copper | 1,000 | Mineral |
| 3 | Platinum | 100,000 | Mineral |

### Base Metals (4-10)
| ID | Name | Base Value | Category |
|----|------|------------|----------|
| 4 | Iron | 100 | Mineral |
| 5 | Bronze | 1,000 | Mineral |
| 6 | Adamantium | 5,000 | Mineral |
| 7 | Mithril | 15,000 | Mineral |
| 8 | Steel | 200 | Mineral |
| 9 | Lead | 100 | Mineral |
| 10 | Tin | 100 | Mineral |

### Gems (11-13)
| ID | Name | Base Value | Category |
|----|------|------------|----------|
| 11 | Diamond | 100,000 | Luxury |
| 12 | Emerald | 50,000 | Luxury |
| 13 | Ruby | 40,000 | Luxury |

### Food & Beverages (14-28)
| ID | Name | Base Value | Category |
|----|------|------------|----------|
| 14 | Meat | 1,000 | Food |
| 15 | Salt | 2,000 | Food |
| 16 | Spices | 5,000 | Food |
| 17 | Milk | 400 | Food |
| 18 | Ale | 1,000 | Food |
| 19 | Beer | 1,000 | Food |
| 20 | Wine | 2,000 | Food |
| 21 | Zzar | 10,000 | Food |
| 22 | Fruits | 400 | Food |
| 23 | Wheat | 100 | Food |
| 24 | Corn | 100 | Food |
| 25 | Fish | 600 | Food |
| 26 | Oil | 1,200 | Food |
| 27 | Sugar | 2,000 | Food |
| 28 | Honey | 1,600 | Food |

### Building Materials (29-32)
| ID | Name | Base Value | Category |
|----|------|------------|----------|
| 29 | Marble | 20 | Construction |
| 30 | Granite | 25 | Construction |
| 31 | Coal | 20 | Energy |
| 32 | Sulphur | 400 | Chemical |

### Natural Resources (33-37)
| ID | Name | Base Value | Category |
|----|------|------------|----------|
| 33 | Timber | 50 | Construction |
| 34 | Silk | 400 | Textile |
| 35 | Wool | 100 | Textile |
| 36 | Linen | 40 | Textile |
| 37 | Furs | 500 | Textile |

### Manufactured Goods (38-45)
| ID | Name | Base Value | Category |
|----|------|------------|----------|
| 38 | Glass | 500 | Manufactured |
| 39 | Leather | 200 | Manufactured |
| 40 | Parchment | 1,000 | Manufactured |
| 41 | Papyrus | 600 | Manufactured |
| 42 | Dye | 100 | Manufactured |
| 43 | Coffee | 10,000 | Luxury |
| 44 | Tea | 5,000 | Luxury |
| 45 | Tobacco | 20,000 | Luxury |

### Derived Product (46)
| ID | Name | Base Value | Category |
|----|------|------------|----------|
| 46 | Industrial Product | 10,000 | Industrial |

## 3. Economic Formulas

### Production/Consumption Calculation
```c
production = MAX(1, (trade_value[i*2] * population / 5000))
consumption = MAX(1, (trade_value[i*2+1] * population / 5000))

// Special case for industrial product:
industrial_production = trade_value[92] * population * mineral_satisf / 5000
```

### Dynamic Pricing Formula
```c
multiplier = (usage × usage_multi/100 × usage_expectation/100 × 1.5) /
             (in_store + 1 + (production × prod_multi/100 × prod_expectation/100 × 1.5))

// Clamped to range [0.7, 1.3]
// Buy price: 75% of calculated value
// Sell price: Full value with production ratio bonuses
final_price = base_value × multiplier
```

### Population Effects
- **Range**: 1,000 - 50,000 (capped)
- **Growth Formula**: 
  ```c
  if (gain > 0)
      population += (gain / 100000) * food_satisf
  else
      population += (gain / 100000) / food_satisf
  ```

## 4. Complete Trade File Values

### Industrial.trd (Manufacturing Focus)
```
1,2     # 0: gold - produce:1, consume:2
1,2     # 1: silver - produce:1, consume:2
7,5     # 2: copper - produce:7, consume:5
1,2     # 3: platinum - produce:1, consume:2
10,5    # 4: iron - produce:10, consume:5
6,4     # 5: bronze - produce:6, consume:4
1,3     # 6: adamantium - produce:1, consume:3
1,3     # 7: mithril - produce:1, consume:3
9,6     # 8: steel - produce:9, consume:6
4,4     # 9: lead - produce:4, consume:4
6,5     # 10: tin - produce:6, consume:5
1,2     # 11: diamond - produce:1, consume:2
1,2     # 12: emerald - produce:1, consume:2
1,2     # 13: ruby - produce:1, consume:2
3,8     # 14: meat - produce:3, consume:8
2,5     # 15: salt - produce:2, consume:5
1,4     # 16: spices - produce:1, consume:4
3,7     # 17: milk - produce:3, consume:7
2,6     # 18: ale - produce:2, consume:6
2,6     # 19: beer - produce:2, consume:6
2,6     # 20: wine - produce:2, consume:6
2,6     # 21: zzar - produce:2, consume:6
2,6     # 22: fruits - produce:2, consume:6
3,9     # 23: wheat - produce:3, consume:9
3,9     # 24: corn - produce:3, consume:9
2,5     # 25: fish - produce:2, consume:5
2,6     # 26: oil - produce:2, consume:6
2,6     # 27: sugar - produce:2, consume:6
1,4     # 28: honey - produce:1, consume:4
3,3     # 29: marble - produce:3, consume:3
4,3     # 30: granite - produce:4, consume:3
8,4     # 31: coal - produce:8, consume:4
5,3     # 32: sulphur - produce:5, consume:3
4,6     # 33: timber - produce:4, consume:6
4,3     # 34: silk - produce:4, consume:3
3,3     # 35: wool - produce:3, consume:3
5,3     # 36: linen - produce:5, consume:3
2,3     # 37: furs - produce:2, consume:3
7,4     # 38: glass - produce:7, consume:4
6,4     # 39: leather - produce:6, consume:4
5,3     # 40: parchment - produce:5, consume:3
3,3     # 41: papyrus - produce:3, consume:3
5,3     # 42: dye - produce:5, consume:3
1,4     # 43: coffee - produce:1, consume:4
1,4     # 44: tea - produce:1, consume:4
1,4     # 45: tobacco - produce:1, consume:4
12,6    # 46: industrial product - produce:12, consume:6
```

### Agricoltural.trd (Food Production Focus)
```
1,2     # 0: gold - produce:1, consume:2
1,2     # 1: silver - produce:1, consume:2
1,3     # 2: copper - produce:1, consume:3
1,2     # 3: platinum - produce:1, consume:2
1,3     # 4: iron - produce:1, consume:3
1,3     # 5: bronze - produce:1, consume:3
1,2     # 6: adamantium - produce:1, consume:2
1,2     # 7: mithril - produce:1, consume:2
1,3     # 8: steel - produce:1, consume:3
1,3     # 9: lead - produce:1, consume:3
1,3     # 10: tin - produce:1, consume:3
1,1     # 11: diamond - produce:1, consume:1
1,1     # 12: emerald - produce:1, consume:1
1,1     # 13: ruby - produce:1, consume:1
10,5    # 14: meat - produce:10, consume:5
3,3     # 15: salt - produce:3, consume:3
2,3     # 16: spices - produce:2, consume:3
9,5     # 17: milk - produce:9, consume:5
6,4     # 18: ale - produce:6, consume:4
6,4     # 19: beer - produce:6, consume:4
6,4     # 20: wine - produce:6, consume:4
2,3     # 21: zzar - produce:2, consume:3
8,4     # 22: fruits - produce:8, consume:4
10,6    # 23: wheat - produce:10, consume:6
10,6    # 24: corn - produce:10, consume:6
5,4     # 25: fish - produce:5, consume:4
6,4     # 26: oil - produce:6, consume:4
4,3     # 27: sugar - produce:4, consume:3
5,3     # 28: honey - produce:5, consume:3
1,2     # 29: marble - produce:1, consume:2
1,2     # 30: granite - produce:1, consume:2
1,3     # 31: coal - produce:1, consume:3
1,2     # 32: sulphur - produce:1, consume:2
4,4     # 33: timber - produce:4, consume:4
3,3     # 34: silk - produce:3, consume:3
8,4     # 35: wool - produce:8, consume:4
4,4     # 36: linen - produce:4, consume:4
4,3     # 37: furs - produce:4, consume:3
1,3     # 38: glass - produce:1, consume:3
6,4     # 39: leather - produce:6, consume:4
2,3     # 40: parchment - produce:2, consume:3
3,3     # 41: papyrus - produce:3, consume:3
2,3     # 42: dye - produce:2, consume:3
5,3     # 43: coffee - produce:5, consume:3
5,3     # 44: tea - produce:5, consume:3
5,3     # 45: tobacco - produce:5, consume:3
2,5     # 46: industrial product - produce:2, consume:5
```

### Commercial.trd (Balanced Trade Hub)
```
2,3     # 0: gold - produce:2, consume:3
2,3     # 1: silver - produce:2, consume:3
3,4     # 2: copper - produce:3, consume:4
2,3     # 3: platinum - produce:2, consume:3
3,4     # 4: iron - produce:3, consume:4
3,4     # 5: bronze - produce:3, consume:4
1,2     # 6: adamantium - produce:1, consume:2
1,2     # 7: mithril - produce:1, consume:2
3,4     # 8: steel - produce:3, consume:4
2,3     # 9: lead - produce:2, consume:3
2,3     # 10: tin - produce:2, consume:3
2,2     # 11: diamond - produce:2, consume:2
2,2     # 12: emerald - produce:2, consume:2
2,2     # 13: ruby - produce:2, consume:2
4,4     # 14: meat - produce:4, consume:4
3,4     # 15: salt - produce:3, consume:4
3,4     # 16: spices - produce:3, consume:4
4,4     # 17: milk - produce:4, consume:4
4,4     # 18: ale - produce:4, consume:4
4,4     # 19: beer - produce:4, consume:4
4,4     # 20: wine - produce:4, consume:4
3,4     # 21: zzar - produce:3, consume:4
4,4     # 22: fruits - produce:4, consume:4
4,5     # 23: wheat - produce:4, consume:5
4,5     # 24: corn - produce:4, consume:5
3,4     # 25: fish - produce:3, consume:4
3,4     # 26: oil - produce:3, consume:4
3,4     # 27: sugar - produce:3, consume:4
3,4     # 28: honey - produce:3, consume:4
2,3     # 29: marble - produce:2, consume:3
2,3     # 30: granite - produce:2, consume:3
3,4     # 31: coal - produce:3, consume:4
2,3     # 32: sulphur - produce:2, consume:3
3,4     # 33: timber - produce:3, consume:4
3,3     # 34: silk - produce:3, consume:3
3,3     # 35: wool - produce:3, consume:3
3,3     # 36: linen - produce:3, consume:3
3,3     # 37: furs - produce:3, consume:3
3,4     # 38: glass - produce:3, consume:4
3,4     # 39: leather - produce:3, consume:4
3,4     # 40: parchment - produce:3, consume:4
3,4     # 41: papyrus - produce:3, consume:4
3,4     # 42: dye - produce:3, consume:4
3,4     # 43: coffee - produce:3, consume:4
3,4     # 44: tea - produce:3, consume:4
3,4     # 45: tobacco - produce:3, consume:4
4,4     # 46: industrial product - produce:4, consume:4
```

### Mineral.trd (Mining & Raw Materials Focus)
```
6,2     # 0: gold - produce:6, consume:2
5,2     # 1: silver - produce:5, consume:2
8,3     # 2: copper - produce:8, consume:3
3,2     # 3: platinum - produce:3, consume:2
12,4    # 4: iron - produce:12, consume:4
4,3     # 5: bronze - produce:4, consume:3
2,2     # 6: adamantium - produce:2, consume:2
2,2     # 7: mithril - produce:2, consume:2
3,5     # 8: steel - produce:3, consume:5
6,3     # 9: lead - produce:6, consume:3
6,3     # 10: tin - produce:6, consume:3
3,2     # 11: diamond - produce:3, consume:2
3,2     # 12: emerald - produce:3, consume:2
3,2     # 13: ruby - produce:3, consume:2
3,8     # 14: meat - produce:3, consume:8
2,5     # 15: salt - produce:2, consume:5
1,4     # 16: spices - produce:1, consume:4
2,6     # 17: milk - produce:2, consume:6
2,6     # 18: ale - produce:2, consume:6
2,6     # 19: beer - produce:2, consume:6
2,6     # 20: wine - produce:2, consume:6
1,4     # 21: zzar - produce:1, consume:4
2,6     # 22: fruits - produce:2, consume:6
2,8     # 23: wheat - produce:2, consume:8
2,8     # 24: corn - produce:2, consume:8
2,5     # 25: fish - produce:2, consume:5
2,6     # 26: oil - produce:2, consume:6
1,5     # 27: sugar - produce:1, consume:5
1,4     # 28: honey - produce:1, consume:4
7,3     # 29: marble - produce:7, consume:3
8,3     # 30: granite - produce:8, consume:3
12,4    # 31: coal - produce:12, consume:4
9,3     # 32: sulphur - produce:9, consume:3
2,6     # 33: timber - produce:2, consume:6
1,4     # 34: silk - produce:1, consume:4
2,5     # 35: wool - produce:2, consume:5
2,5     # 36: linen - produce:2, consume:5
2,4     # 37: furs - produce:2, consume:4
2,5     # 38: glass - produce:2, consume:5
2,5     # 39: leather - produce:2, consume:5
1,4     # 40: parchment - produce:1, consume:4
1,4     # 41: papyrus - produce:1, consume:4
1,4     # 42: dye - produce:1, consume:4
1,4     # 43: coffee - produce:1, consume:4
1,4     # 44: tea - produce:1, consume:4
1,4     # 45: tobacco - produce:1, consume:4
4,6     # 46: industrial product - produce:4, consume:6
```

### IndComm.trd (Industrial-Commercial Hybrid)
```
1,2     # 0: gold
1,2     # 1: silver
5,5     # 2: copper
1,2     # 3: platinum
7,5     # 4: iron
5,4     # 5: bronze
1,3     # 6: adamantium
1,3     # 7: mithril
6,5     # 8: steel
3,4     # 9: lead
4,4     # 10: tin
1,2     # 11: diamond
1,2     # 12: emerald
1,2     # 13: ruby
3,6     # 14: meat
2,5     # 15: salt
2,4     # 16: spices
3,6     # 17: milk
3,5     # 18: ale
3,5     # 19: beer
3,5     # 20: wine
2,5     # 21: zzar
3,5     # 22: fruits
3,7     # 23: wheat
3,7     # 24: corn
2,5     # 25: fish
2,5     # 26: oil
3,5     # 27: sugar
2,4     # 28: honey
3,3     # 29: marble
3,3     # 30: granite
6,4     # 31: coal
4,3     # 32: sulphur
4,5     # 33: timber
4,3     # 34: silk
3,3     # 35: wool
4,3     # 36: linen
2,3     # 37: furs
5,4     # 38: glass
5,4     # 39: leather
4,3     # 40: parchment
3,3     # 41: papyrus
4,3     # 42: dye
2,4     # 43: coffee
2,4     # 44: tea
2,4     # 45: tobacco
8,5     # 46: industrial product
```

### IndMin.trd (Industrial-Mineral Hybrid)
```
3,2     # 0: gold
3,2     # 1: silver
8,4     # 2: copper
2,2     # 3: platinum
11,5    # 4: iron
5,4     # 5: bronze
2,3     # 6: adamantium
2,3     # 7: mithril
7,6     # 8: steel
5,4     # 9: lead
6,4     # 10: tin
2,2     # 11: diamond
2,2     # 12: emerald
2,2     # 13: ruby
3,8     # 14: meat
2,5     # 15: salt
1,4     # 16: spices
3,7     # 17: milk
2,6     # 18: ale
2,6     # 19: beer
2,6     # 20: wine
1,4     # 21: zzar
2,6     # 22: fruits
3,8     # 23: wheat
3,8     # 24: corn
2,5     # 25: fish
2,6     # 26: oil
2,6     # 27: sugar
1,4     # 28: honey
5,3     # 29: marble
6,3     # 30: granite
10,4    # 31: coal
7,3     # 32: sulphur
3,6     # 33: timber
3,3     # 34: silk
3,3     # 35: wool
4,3     # 36: linen
2,3     # 37: furs
5,5     # 38: glass
5,4     # 39: leather
4,3     # 40: parchment
3,3     # 41: papyrus
4,3     # 42: dye
1,4     # 43: coffee
1,4     # 44: tea
1,4     # 45: tobacco
9,6     # 46: industrial product
```

### MinAgr.trd (Mineral-Agricultural Hybrid)
```
4,2     # 0: gold
3,2     # 1: silver
7,3     # 2: copper
2,2     # 3: platinum
10,4    # 4: iron
4,3     # 5: bronze
2,2     # 6: adamantium
2,2     # 7: mithril
2,5     # 8: steel
4,3     # 9: lead
4,3     # 10: tin
2,2     # 11: diamond
2,2     # 12: emerald
2,2     # 13: ruby
9,5     # 14: meat
3,3     # 15: salt
2,3     # 16: spices
8,5     # 17: milk
6,4     # 18: ale
6,4     # 19: beer
5,4     # 20: wine
2,3     # 21: zzar
7,4     # 22: fruits
9,5     # 23: wheat
9,5     # 24: corn
4,4     # 25: fish
5,4     # 26: oil
3,3     # 27: sugar
4,3     # 28: honey
5,3     # 29: marble
6,3     # 30: granite
9,4     # 31: coal
7,3     # 32: sulphur
3,5     # 33: timber
2,3     # 34: silk
6,4     # 35: wool
4,4     # 36: linen
3,3     # 37: furs
2,4     # 38: glass
3,4     # 39: leather
2,3     # 40: parchment
3,3     # 41: papyrus
2,3     # 42: dye
4,3     # 43: coffee
4,3     # 44: tea
4,3     # 45: tobacco
3,5     # 46: industrial product
```

### MinComm.trd (Mineral-Commercial Hybrid)
```
4,2     # 0: gold
3,2     # 1: silver
6,3     # 2: copper
2,2     # 3: platinum
9,4     # 4: iron
4,3     # 5: bronze
2,2     # 6: adamantium
2,2     # 7: mithril
3,5     # 8: steel
4,3     # 9: lead
4,3     # 10: tin
2,2     # 11: diamond
2,2     # 12: emerald
2,2     # 13: ruby
4,5     # 14: meat
3,4     # 15: salt
3,4     # 16: spices
4,4     # 17: milk
3,4     # 18: ale
3,4     # 19: beer
3,4     # 20: wine
2,4     # 21: zzar
4,4     # 22: fruits
4,5     # 23: wheat
4,5     # 24: corn
3,4     # 25: fish
3,4     # 26: oil
3,4     # 27: sugar
3,4     # 28: honey
4,3     # 29: marble
5,3     # 30: granite
8,4     # 31: coal
6,3     # 32: sulphur
3,4     # 33: timber
2,3     # 34: silk
3,3     # 35: wool
3,3     # 36: linen
3,3     # 37: furs
3,4     # 38: glass
3,4     # 39: leather
3,4     # 40: parchment
3,4     # 41: papyrus
3,4     # 42: dye
2,4     # 43: coffee
2,4     # 44: tea
2,4     # 45: tobacco
5,5     # 46: industrial product
```

### AgrComm.trd (Agricultural-Commercial Hybrid)
```
1,2     # 0: gold
1,2     # 1: silver
2,3     # 2: copper
1,2     # 3: platinum
1,3     # 4: iron
1,3     # 5: bronze
1,2     # 6: adamantium
1,2     # 7: mithril
1,3     # 8: steel
1,3     # 9: lead
1,3     # 10: tin
1,1     # 11: diamond
1,1     # 12: emerald
1,1     # 13: ruby
9,5     # 14: meat
3,3     # 15: salt
3,4     # 16: spices
8,5     # 17: milk
5,4     # 18: ale
5,4     # 19: beer
5,4     # 20: wine
2,3     # 21: zzar
7,4     # 22: fruits
8,5     # 23: wheat
8,5     # 24: corn
4,4     # 25: fish
5,4     # 26: oil
4,3     # 27: sugar
4,3     # 28: honey
1,2     # 29: marble
1,2     # 30: granite
1,3     # 31: coal
1,2     # 32: sulphur
4,4     # 33: timber
3,3     # 34: silk
6,4     # 35: wool
4,4     # 36: linen
3,3     # 37: furs
1,3     # 38: glass
5,4     # 39: leather
2,3     # 40: parchment
3,3     # 41: papyrus
2,3     # 42: dye
4,3     # 43: coffee
4,3     # 44: tea
4,3     # 45: tobacco
3,4     # 46: industrial product
```

## 5. Implementation Guide

### Phase 1: Initial Setup (Week 1)
1. Create .trd files with the provided values
2. Boot server and verify trade initialization
3. Monitor basic price calculations
4. Check for parsing errors in logs

### Phase 2: Testing & Balancing (Weeks 2-3)
1. Monitor price fluctuations (0.7-1.3x range)
2. Track trade logs for supply/demand patterns
3. Verify mineral/food satisfaction calculations
4. Test virtual NPC trading behavior
5. Adjust weights if extreme shortages/surpluses occur

### Phase 3: Economic Tuning (Weeks 4-5)
1. Analyze player trade patterns
2. Balance luxury goods (gems, coffee, tobacco) availability
3. Ensure food production prevents starvation
4. Verify industrial product depends on mineral satisfaction
5. Fine-tune hybrid economies for meaningful trade routes

### Phase 4: Advanced Features (Month 2+)
1. Implement seasonal variations via random events
2. Add trade route disruptions (bandit system)
3. Create economic news generation
4. Develop inter-city trade relationships
5. Add player-owned trade caravans

## 6. Critical Game Balance Notes

### Population Dynamics
- Range: 1,000 - 50,000 (hard-capped)
- Growth depends on food satisfaction
- Mineral satisfaction affects industrial output
- Daily updates trigger production/consumption

### Price Stability
- 0.7x floor prevents price crashes
- 1.3x ceiling prevents runaway inflation
- Buy/sell spread (75% on purchases) ensures merchant profits
- Virtual NPCs provide market liquidity

### Resource Categories
- **Minerals** (0-10): Critical for industrial production
- **Food** (14-28): Essential for population growth
- **Luxury** (gems, coffee, tea, tobacco): High-value, low-volume trade
- **Industrial** (46): Requires mineral inputs via satisfaction system

### Economic Interdependencies
- Industrial towns need food imports
- Agricultural towns need tool/metal imports
- Mineral towns need everything except raw materials
- Commercial hubs balance all needs moderately
- Hybrids create specialized trade relationships

## 7. Validation Checklist

- [ ] All 94 lines present in each .trd file
- [ ] No blank lines or formatting errors
- [ ] Values create meaningful surpluses/deficits
- [ ] Industrial product weights present (lines 93-94)
- [ ] Hybrid economies blend parent types appropriately
- [ ] Base values align with medieval fantasy setting
- [ ] Population scaling produces reasonable quantities
- [ ] Trade logs show active buying/selling
- [ ] Prices fluctuate within 0.7-1.3x range
- [ ] No commodities permanently unavailable

## 8. Troubleshooting Guide

### Common Issues & Solutions

**Problem**: "Problem with trade file directory!" error
- **Solution**: Check file permissions and format (integer,comment per line)

**Problem**: Commodity always at 0 stock
- **Solution**: Increase production weight or decrease consumption weight

**Problem**: Prices stuck at maximum (1.3x)
- **Solution**: Increase production weights for that commodity/town type

**Problem**: Industrial products not being produced
- **Solution**: Check mineral satisfaction - need mineral imports

**Problem**: Population declining
- **Solution**: Verify food satisfaction - increase food availability

## 9. Future Development Opportunities

1. **Dynamic Events**: Weather affects agricultural output
2. **Trade Routes**: Establish preferred paths between complementary economies
3. **Market Speculation**: Allow players to invest in futures
4. **Economic Warfare**: Blockades and embargoes
5. **Crafting Integration**: Link commodity availability to player crafting
6. **Seasonal Variations**: Harvest cycles for agricultural goods
7. **Technology Progression**: Unlock new commodities over time
8. **Regional Specialties**: Unique commodities per geographic area

## Appendix: Quick Reference

### Economy Type Summary
- **Industrial**: High manufactured goods, low food
- **Agricultural**: High food/textiles, low metals
- **Commercial**: Balanced all categories
- **Mineral**: High raw materials, low everything else
- **IndComm**: Industrial with trade focus
- **IndMin**: Industrial with mining support
- **MinAgr**: Mining with local food production
- **MinComm**: Mining with trade capabilities
- **AgrComm**: Farming with market access

### Key Formulas
- Production: `weight × population / 5000`
- Price Multiplier: `demand / (supply + 1)` (clamped 0.7-1.3)
- Food Satisfaction: Affects population growth
- Mineral Satisfaction: Affects industrial output

This comprehensive guide provides everything needed to implement and maintain the Outcast MUD trade system successfully.