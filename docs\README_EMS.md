# Outcast MUD Email Management System (EMS)

This document explains the EMS subsystem, its data files, runtime flow, and common admin operations.

## Overview

EMS manages player registration and communication via email-like message flows. It tracks applications, pending replies, admin accept/decline decisions, and emits status-specific messages. Data is persisted in:
- Master EMS database (EMS_DATABASE)
- Backup EMS database (EMS_BACKUP_DATABASE)
- Per-player files: .db, .headers, .records
- Outgoing email database (OUTGOING_EMAIL)
- Email statistics database (EMAIL_DATABASE)

Core runtime lives in [`src/email_reg.c`](src/email_reg.c), with constants/structs in [`src/email_reg.h`](src/email_reg.h).

## Data Files

The following template files live in lib/ems/ and are loaded at boot. They are concatenated into outbound messages by EMS_Return_Notice and other flows.

Required files:
- lib/ems/ems_header
- lib/ems/ems_footer
- lib/ems/ems_disclaimer
- lib/ems/ems_reply_start
- lib/ems/ems_reply_badname
- lib/ems/ems_reply_badpin
- lib/ems/ems_reply_bademail
- lib/ems/ems_reply_closed
- lib/ems/ems_reply_pending_reply
- lib/ems/ems_reply_pending_accept
- lib/ems/ems_reply_frozen
- lib/ems/ems_reply_thawed
- lib/ems/ems_reply_declined
- lib/ems/ems_reply_broken
- lib/ems/ems_reply_deleted
- lib/ems/ems_reply_oops
- lib/ems/ems_reply_okay
- lib/ems/ems_chargen_info1
- lib/ems/ems_chargen_info2
- lib/ems/ems_banned_msg
- lib/ems/ems_usage

If any file is missing, EMS logs an error and substitutes empty content; it does not crash.

## Databases and File Locations

- EMS master database (text), path defined by EMS_DATABASE in header. First line is a count; each subsequent line is:
  "<name> <pin> <email> <status>"
- EMS backup database: EMS_BACKUP_DATABASE. EMS attempts to repair the primary from backup if needed.
- Outgoing email database: OUTGOING_EMAIL. Lines associate an email with a generated body file path.
- Email statistics database: EMAIL_DATABASE. First line is an index with counters; subsequent lines record email/player stats used by do_ems_status and Email_Database_Lookup.

Per-player files under EMAIL_REG_FILES directory:
- {initial}/{name}.db: per-player event log (status, pin, email entries)
- {initial}/{name}.headers: stores mail header snapshots
- {initial}/{name}.records: admin/player record entries

The code uses temporary files and rename() to safely update databases.

## Runtime Flow

Boot:
1) EMS_Bootup opens the EMS database using EMS_Open_Database, reads the count, and pre-sizes the in-memory struct (EMSD) based on records pending acceptance.
2) Loads EMS text templates from lib/ems/ into memory via EMS_File_To_Struct.

Applications and Replies:
- EMS_Send_Application: Adds entry to OUTGOING_EMAIL and writes a body file including header/disclaimer/footer, then logs an EMS record in the master DB.
- EMS_Process_Mail: Parses incoming transfers (from Sojparse), updates EMSD and DB, and sends automated return notices.
- EMS_Return_Notice: Creates an outgoing message for a given status, using the header, a status-specific body snippet (ems_reply_*), optional admin message, and footer.

Admin Commands (via do_ems):
- list: View accounts (default shows pending accept).
- lookup: Search master DB by name/email/status/pin/domain/all.
- header: Show player mail headers (latest/list/N).
- records: Manage per-player records (list/show/add/delete).
- status: Summarize EMS and email DB statistics.
- accept/decline/freeze/thaw/reset/kill: Update account status and emit notices.

## Hardening and Safety

The module has been hardened against common issues:
- Format string safety: all fprintf(stderr, buf) replaced with fprintf(stderr, "%s", buf).
- Bounded parsing: sscanf calls that store into 8192-byte buffers use "%8191s" specifiers.
- Safe fopen modes: reads use "r", modifications use "r+" with fallback "w".
- DB repair flow:
  - EMS_Open_Database validates count, handles corrupt/missing primary by attempting repair from backup, logging safely without spawning shells.
- Portability:
  - POSIX-only process control isolated and not used in EMS repair paths.
  - The game runs on POSIX at runtime.

## Common Admin Tasks

- View pending accounts:
  ems list
- Approve an account:
  ems accept <name>
- Decline an account:
  ems decline <name> <optional reason...>
- Freeze/thaw:
  ems freeze <name>
  ems thaw <name>
- Reset application to start:
  ems reset <name>
- Kill (delete) account:
  ems kill <name>
- Lookup in DB:
  ems lookup <name|email|status|pin|domain|all>
- Show EMS status summary:
  ems status
- Work with headers:
  ems header <name> [list|N]
- Work with records:
  ems rlist <name>
  ems rshow <name> [N|all]
  ems radd <name> 'Title' <text...>
  ems rdelete <name> <N>

## Troubleshooting

- “Cannot open text file (lib/ems/...)”:
  Ensure all files listed above exist and are readable. Minimal examples are now included by default.
- “EMS database is corrupt / Missing primary”:
  The repair logic will attempt to restore from EMS_BACKUP_DATABASE. Ensure both files are present and in the correct format (first line = record count).
- Outgoing email not created:
  Verify OUTGOING_EMAIL path is writable. The code appends entries and writes a body file at EMAIL_REG_FILES/outgoing/.
- Email DB index/read issues:
  Open_Email_Database recreates the file with an index line if missing; ensure path is writable.

## Development Notes

- Keep buffer sizes and sscanf bounds aligned (8192 ↔ %8191s).
- When editing file paths or DB schemas, update both loader and writer code paths consistently.
- If adding new EMS reply templates, create a corresponding lib/ems/ file and wire it in the switch within EMS_Return_Notice.