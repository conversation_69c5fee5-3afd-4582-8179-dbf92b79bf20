/* ************************************************************************* */
/*                                                                           */
/*    File structs.h - Special Module, part of Outcast III MUD code base.    */
/*    Usage: Most major definitions, structures, and variables for the mud.  */
/*                                                                           */
/*      COPYRIGHT NOTICE: This code for Outcast is Copyright(c) 2000, by     */
/*    Kris<PERSON>her <PERSON>. This code is NOT freeware or shareware,    */
/*   and may NOT be used in any form without expressly written permission    */
/*                             from the author.                              */
/*        K<PERSON> may be reached via <NAME_EMAIL>      */
/*             and <EMAIL> <NAME_EMAIL>                  */
/*                                                                           */
/*************************************************************************** */

/*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*

  WARNING to all!

  Changing ANYTHING in this file can change things in the entire mud.  If you
  change the structure of obj_data or char_data, it will most likely require a
  change to the player save files, and probably changes to many of the other
  source files.  If you change ANYTHING in here, make damn sure those changes
  are documented to ~outcast/Changed.

                                           John Bashaw, Gond of Outcast

 *-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*/

#ifndef _SOJ_STRUCTS_H_
#define _SOJ_STRUCTS_H_

#ifndef _SOJ_CONFIG_H_
#include "config.h"
#endif

#ifndef _SOJ_INTERP_H_
#include "interp.h"
#endif

#ifndef _SOJ_ASSOC_H
#include "assocs.h"
#endif

#ifdef _HPUX_SOURCE
#define srandom srand
#define random rand
#endif

#include "list.h"
#include "mccp_types.h"

#include <sys/types.h>
#include <time.h>

#ifdef _SUN4_SOURCE
typedef unsigned int int uint;
#endif

typedef char bool;
typedef char byte;
typedef signed char sbyte;
typedef signed short int sh_int;
typedef struct AC_Memory Memory;
typedef struct char_data *P_char;
typedef struct descriptor_data *P_desc;
typedef struct event_data *P_event;
typedef struct obj_data *P_obj;
typedef struct room_data *P_room;
typedef struct group_data *P_group;
typedef struct group_element *P_gmember;
typedef struct witness_data wtns_rec;
typedef unsigned char ubyte;
typedef unsigned char uchar;
typedef unsigned short int ush_int;
typedef struct crime_info crime_rec;
typedef struct crime_data crm_rec;
#ifdef KINGDOM
typedef struct house_control_rec *P_house;
typedef struct house_upgrade_rec *P_house_upgrade;
#endif
#ifdef NEW_SITE_LOOKUPS
typedef struct conn_lookup_struct *P_lookup;
#endif
#ifdef NEW_BARD
typedef struct songinfo *P_song;
typedef struct song_list_element *P_song_element;
#endif

typedef struct assoc_data *P_assoc;
typedef struct job_data *P_job;

#define EVENT_SAVING

// Added for olc 12/10/00 --MIAX
#define NO      0
#define YES     1
#define OKAY    0
#define FAILED  1

#define MESS_ATTACKER 1
#define MESS_VICTIM   2
#define MESS_ROOM     3

/* rather than doing these over and over again, define once here and use the
   defines in the other defines.  JAB */

#define BIT_1            1U
#define BIT_2            2U
#define BIT_3            4U
#define BIT_4            8U
#define BIT_5           16U
#define BIT_6           32U
#define BIT_7           64U
#define BIT_8          128U
#define BIT_9          256U
#define BIT_10         512U
#define BIT_11        1024U
#define BIT_12        2048U
#define BIT_13        4096U
#define BIT_14        8192U
#define BIT_15       16384U
#define BIT_16       32768U
#define BIT_17       65536U
#define BIT_18      131072U
#define BIT_19      262144U
#define BIT_20      524288U
#define BIT_21     1048576U
#define BIT_22     2097152U
#define BIT_23     4194304U
#define BIT_24     8388608U
#define BIT_25    16777216U
#define BIT_26    33554432U
#define BIT_27    67108864U
#define BIT_28   134217728U
#define BIT_29   268435456U
#define BIT_30   536870912U
#define BIT_31  1073741824U
#define BIT_32  2147483648U
#define BIT_33  4294967296U

/* for 'generic_find' */

#define FIND_CHAR_ROOM    BIT_1
#define FIND_CHAR_WORLD   BIT_2
#define FIND_OBJ_INV      BIT_3
#define FIND_OBJ_ROOM     BIT_4
#define FIND_OBJ_WORLD    BIT_5
#define FIND_OBJ_EQUIP    BIT_6

/* the char bit array defines (cbits), note that the lookup table currently only supports a max of 96 bits
   if any of the following variables needs more than that, the table will have to be expanded.  They should
   always be a multiple of 8, because it's going to be allocated that way anyway.  JAB */

#define MAX_PACT            72       /* number of PLR_xxx values (0 thru 39) */
#define MAX_AFF_BITS        128       /* number of AFF_xxx values (0 thru 128) */
#define MAX_BIGOT_BITS      48       /* number of BIGOT_xxx values (0 thru 39) */
#define MAX_ITEM_TYPE_BITS  40       /* number of ITEM_xxx values (0 thru 39) */
#define MAX_AGG_BITS        80       /* number of RACE_ defines */
#define MAX_DEBUG_BITS      80       /* number of debug subchannels */
#define MAX_ROOM_FLAG_BITS  72       /* number of room_flag bits  -- Alth */
#define MAX_NPCACT_BITS     40       /* number of npcact bits */
#define MAX_ASC_BITS        40       /* Number of Assoc Bits (stored in P_assoc)*/
#define MAX_ASCM_BITS       40       /* Number of Assoc Member Bits (stored in P_char) */
#define MAX_LAW_FLAG_BITS   152      /* number of law flag bits */
#define MAX_DIS_BITS        64       /* number of disease flags bits */

/* these handy defines are actually MAX_xxx_BITS / 8 (round up), but why do the math over and over?  JAB */

#define PACT_BYTES        9       /* pc_only_data.pcact */
#define AFF_BYTES        16       /* ch->specials.affects, obj->sets_affs, affected_type->sets_affs */
#define SHOP_BIGOT_BYTES  6       /* shop_index[].cheats, shop_index[].hates */
#define ITEM_TYPE_BYTES   5       /* shop_index[].types */
#define AGG_BYTES        10       /* npc->npc.only->aggressive */
#define GRANT_BYTES      15       /* size of the pc.only->grant cbit array  -- Alth */
#define DEBUG_BYTES      10       /* size of the pc.only->debug cbit array  -- Alth */
#define ROOM_FLAG_BYTES   9       /* size of the room->room_flag  -- Alth */
#define NPCACT_BYTES      5       /* size of the npcact array for mobs (KEEP THIS SMALL) -- Alth */
#define ASC_BYTES         5       /* size of assoc->bits */
#define ASCM_BYTES        5       /* size of ch->asc_bits */
#define LAW_FLAG_BYTES   19       /* size of only.pc->law_flag cbit array */
#define DIS_BYTES         8       /* size of struct disease->flags[] array -- Alth */

/* these defines and the structure are used to replace all the horrible kludges used to let players change
 * strings.  When a player starts writing something, one of these structures is created and correct values set.
 * When player is done writing, the structure is referenced, and any existing text string from the writing_info
 * structure replaces the existing text.  Note that the attachment point in the char struct is in pc_only_data,
 * thus NPCs are not allowed to write anything.  Also note that losing link (which wipes out the descriptor), no
 * longer causes problems.  For boards, neither targ_obj nor targ_ch is set, for notes only targ_obj is set, for
 * stringing, either targ_ch or targ_obj MUST be set.  When someone start stringing a character or object, or
 * writing a note, an EVENT_STRINGING event is attached to targ_char or targ_obj, which points to the writing_info
 * struct.  This event has maximum duration, is not saved, and is rescheduled if it DOES wear off, the only thing
 * that removes this event is completion of the writing. */

#define WRT_BOARD              1  /* writing a message on a board, text_start is
                                     the address of the message body */
#define WRT_STRING_OBJ         2  /* stringing a text field on an object */
#define WRT_STRING_CH          3  /* stringing a text field on a character */
#define WRT_EMS                4  /* Writing text for EMS messages. */
#define WRT_OLC                5  /* OLC write functions. */
#define WRT_HOUSE_DESCRIPTION  6  /* writing a house description */
#define WRT_ASSOC_DESC         7
#define WRT_ASSOC_MOTD         8

/* =================== Structure for the olc database ==================== */
struct olc_database {
    int UserID; /* A unique database id */
    char *UserName; /* PC Chars name */

    int UserAccessLevel; /* Access level the user has */
    char *UserManager; /* Name of coordinator managing this user */

    int CurrentArea; /* Name of area being worked on */
    char *AreaFileName; /* File name of area being worked on */
    char *DateLastModified; /* Date when user db enter was last changed */

    int AreaStartVnum; /* Vnum starting point of the zone */
    int AreaEndVnum; /* Vnum ending point of the zone */

    int CurrentRoom; /* Vnum of room being worked on */
    int CurrentMob; /* Vnum of mob being worked on */
    int CurrentObject; /* Vnum of object being worked on */

    int RoomsMade; /* Number of rooms made in this area */
    int RoomsMadeToday; /* Number of rooms made in this area */
    int RoomsChanged; /* Number of rooms made in this area */
    int RoomsChangedToday; /* Number of rooms made in this area */
    int MobsMade; /* Number of mobs made in this area */
    int MobsMadeToday; /* Number of mobs made in this area */
    int MobsChanged; /* Number of mobs made in this area */
    int MobsChangedToday; /* Number of mobs made in this area */
    int ObjectsMade; /* Number of objects made in this area */
    int ObjectsMadeToday; /* Number of objects made in this area */
    int ObjectsChanged; /* Number of objects made in this area */
    int ObjectsChangedToday; /* Number of objects made in this area */
    int QuestsMade; /* Number of quests made in this area */
    int QuestsMadeToday; /* Number of quests made in this area */
    int QuestsChanged; /* Number of quests made in this area */
    int QuestsChangedToday; /* Number of quests made in this area */
    int SocMade; /* Number of socials made in this area */
    int SocMadeToday; /* Number of socials made in this area */
    int SocChanged; /* Number of socials made in this area */
    int SocChangedToday; /* Number of socials made in this area */
    int EchosMade; /* Number of echos made in this area */
    int EchosMadeToday; /* Number of echos made in this area */
    int EchosChanged; /* Number of echos made in this area */
    int EchosChangedToday; /* Number of echos made in this area */

    int Status; /* Account Status */
    char *Note1; /* Account notes */
    char *Note2; /* Account notes */
    char *Note3; /* Account notes */

    struct olc_zone_data *zone_stats[200];
};
typedef struct olc_database *P_olc;

struct writing_info {
    P_char writer; /* the ch doing the writing */
    P_obj targ_obj; /* NULL unless WRT_STRING_OBJ */
    P_char targ_ch; /* NULL unless WRT_STRING_CH */
#ifdef KINGDOM
    P_house targ_house; /* NULL unless WRT_HOUSE_DESCRIPTION */
#endif
    int board_idx; /* 0 unless WRT_BOARD, then index into Boards array. */
    int auction_idx; /* 0 unless WRT_AUCTION, then index into Auctions array. */
    int msg_nr; /* 0 unless WRT_BOARD, then number of the message (with board_idx & auction_idx) */
    char *text; /* this is where text is stored until writing is finished. */
    char **text_start; /* this is where the completed text will wind up. */
    int str_mask; /* the STRUNG_xx mask value this writing will change (only objs and NPCs) */
    int max_length; /* limit on text length, valid is 2 to MAX_STRING_LENGTH - 1 */
    int what; /* flag using the above defines, what exactly are we writing? */
    P_event event; /* the event (if any) that monitors this structure. */
};

/* The following defs are for obj_data  */

/* note:  before reassigning any of these, especially the ones that you have no
   clue about, they are probably there for compatibility with something, if you
   get ambitious, find out which ones aren't used and kill them.  JAB */

/* object 'type' */

#define ITEM_LIGHT       1      /* an illuminating object */
#define ITEM_SCROLL      2      /* spells to be 'recited' */
#define ITEM_WAND        3      /* contains spells to 'use' */
#define ITEM_STAFF       4      /* also contains spells to 'use' (area affect) */
#define ITEM_WEAPON      5      /* for hurting things */
#define ITEM_FIREWEAPON  6      /* weapon used to fire others (bows, slings...) */
#define ITEM_MISSILE     7      /* arrow, bolt, ballista missile... */
#define ITEM_TREASURE    8      /* is intrinsically valuable */
#define ITEM_ARMOR       9      /* a piece of equipment granting physical prot. */
#define ITEM_POTION     10      /* for the quaffing */
#define ITEM_WORN       11      /* non-armor clothing items */
#define ITEM_OTHER      12      /* miscellaneous things */
#define ITEM_TRASH      13      /* OTHER (less useful) miscellaneous things*/
#define ITEM_TRAP       14      /* goes BOOM! */
#define ITEM_CONTAINER  15      /* for containing other items */
#define ITEM_NOTE       16      /* for passing information */
#define ITEM_DRINKCON   17      /* for containing potables */
#define ITEM_KEY        18      /* for unlocking locks */
#define ITEM_FOOD       19      /* for eating */
#define ITEM_MONEY      20      /* a pile of cash */
#define ITEM_PEN        21      /* for scribbling NOTEs */
#define ITEM_BOAT       22      /* row, row, row me boat, gently down the stream */
#define ITEM_BOOK       23      /* well, maybe someday will be useful */
#define ITEM_CORPSE     24      /* internal use only, do NOT assign this type! */
#define ITEM_TELEPORT   25      /* item can be used to teleport */
#define ITEM_POISON     26      /* poison used in apply poison */
#define ITEM_SUMMON     27      /* summon a specified mobile */
#define ITEM_SHIP       28      /* item which represents a sea-going vessel */
#define ITEM_SWITCH     29      /* item is a trigger for a switch proc */
#define ITEM_QUIVER     30      /* container for MISSILEs only */
#define ITEM_PICK       31      /* lockpicks */
#define ITEM_INSTRUMENT 32      /* bard's instruments */
#define ITEM_SPELLBOOK        33 /* Genuine magetype spellbook */
#define ITEM_MISSILE_INFLIGHT 34 /* ITEM_MISSILE while the missile code has hold of it. */
#define ITEM_CART             35 /* cart to be attached to a mountable mob */
#define ITEM_COMMODITY        36 /* commodities for trade */
#define ITEM_PSP_CRYSTAL      37 /* psp storage crystal */
#define ITEM_CRUCIBLE         38 /* used for mixing cures */
#define ITEM_CURE_COMPONENT   39 /* cure component */
#define ITEM_DISTRIBUTION     40 /* distro items */

#define LAST_ITEM_TYPE  40


#define PC_CORPSE       1
#define NPC_CORPSE      2
#define ARENA_CORPSE    3

/* Bitvector For 'wear_flags' */

#define ITEM_TAKE           BIT_1
#define ITEM_WEAR_FINGER    BIT_2
#define ITEM_WEAR_NECK      BIT_3
#define ITEM_WEAR_BODY      BIT_4
#define ITEM_WEAR_HEAD      BIT_5
#define ITEM_WEAR_LEGS      BIT_6
#define ITEM_WEAR_FEET      BIT_7
#define ITEM_WEAR_HANDS     BIT_8
#define ITEM_WEAR_ARMS      BIT_9
#define ITEM_WEAR_SHIELD    BIT_10
#define ITEM_WEAR_ABOUT     BIT_11
#define ITEM_WEAR_WAIST     BIT_12
#define ITEM_WEAR_WRIST     BIT_13
#define ITEM_WIELD          BIT_14
#define ITEM_HOLD           BIT_15
#define ITEM_THROW          BIT_16

#define ITEM_WEAR_EYES      BIT_18
#define ITEM_WEAR_FACE      BIT_19
#define ITEM_WEAR_EARRING   BIT_20
#define ITEM_WEAR_QUIVER    BIT_21
#define ITEM_GUILD_INSIGNIA BIT_22
#define ITEM_WEAR_TAIL      BIT_23

/* Bitvector for 'extra_flags' */

#define ITEM_GLOW          BIT_1
#define ITEM_NOSHOW        BIT_2
#define ITEM_DARK          BIT_3
#define ITEM_NOSELL        BIT_4        /* shopkeepers won't buy it */
#define ITEM_ANTI_GOODRACE BIT_5
#define ITEM_INVISIBLE     BIT_6
#define ITEM_MAGIC         BIT_7
#define ITEM_NODROP        BIT_8
#define ITEM_BLESS         BIT_9
#define ITEM_ANTI_GOOD     BIT_10       /* not usable by good people    */
#define ITEM_ANTI_EVIL     BIT_11       /* not usable by evil people    */
#define ITEM_ANTI_NEUTRAL  BIT_12       /* not usable by neutral people */
#define ITEM_SECRET        BIT_13
#define ITEM_FLOAT         BIT_14
#define ITEM_NOBURN        BIT_15       /* can't be destroyed with dragon breath */
#define ITEM_NOLOCATE      BIT_16       /* Item cannot be located       */
#define ITEM_NOIDENTIFY    BIT_17       /* Item cannot be identified    */
#define ITEM_NOSUMMON      BIT_18       /* if in inventory cannot be summoned */
#define ITEM_LIT           BIT_19       /* Item has a light spell cast on it */
#define ITEM_TRANSIENT     BIT_20       /* Item which dissolves when dropped */
#define ITEM_NOSLEEP       BIT_21       /* If in inventory, cannot be slept */
#define ITEM_NOCHARM       BIT_22       /* If in inventory, cannot be charmed */
#define ITEM_TWOHANDS      BIT_23       /* item requires two hands to hold/wield */
#define ITEM_NORENT        BIT_24       /* item cannot be rented */
#define ITEM_ANTI_EVILRACE BIT_25
#define ITEM_ANTI_WA       BIT_26
#define ITEM_ANTI_CL       BIT_27
#define ITEM_ANTI_TH       BIT_28
#define ITEM_ANTI_MU       BIT_29
#define ITEM_WHOLE_BODY    BIT_30
#define ITEM_WHOLE_HEAD    BIT_31
#define ITEM_WAS_DISARMED  BIT_32       /* if disarmed, re-arm in perform_violence */

/* Bitvector for 'ANTI' flags */

#define ITEM_ANTI_WARRIOR      BIT_1
#define ITEM_ANTI_RANGER       BIT_2
#define ITEM_ANTI_PALADIN      BIT_3
#define ITEM_ANTI_ANTIPALADIN  BIT_4
#define ITEM_ANTI_CLERIC       BIT_5
#define ITEM_ANTI_INVOKER      BIT_6
#define ITEM_ANTI_DRUID        BIT_7
#define ITEM_ANTI_SHAMAN       BIT_8
#define ITEM_ANTI_ENCHANTER    BIT_9
#define ITEM_ANTI_NECROMANCER  BIT_10
#define ITEM_ANTI_CONJURER     BIT_11
#define ITEM_ANTI_PSIONICIST   BIT_12
#define ITEM_ANTI_THIEF        BIT_13
#define ITEM_ANTI_ORC          BIT_14
#define ITEM_ANTI_ILLUSIONIST  BIT_15
#define ITEM_ANTI_BARD         BIT_16
#define ITEM_ANTI_HUMAN        BIT_17
#define ITEM_ANTI_GREYELF      BIT_18
#define ITEM_ANTI_HALFELF      BIT_19
#define ITEM_ANTI_DWARF        BIT_20
#define ITEM_ANTI_HALFLING     BIT_21
#define ITEM_ANTI_GNOME        BIT_22
#define ITEM_ANTI_BARBARIAN    BIT_23
#define ITEM_ANTI_DUERGAR      BIT_24
#define ITEM_ANTI_DROWELF      BIT_25
#define ITEM_ANTI_TROLL        BIT_26
#define ITEM_ANTI_OGRE         BIT_27
#define ITEM_ANTI_ILLITHID     BIT_28
#define ITEM_ANTI_YUANTI       BIT_29
#define ITEM_ANTI_LICH         BIT_30
#define ITEM_ANTI_MALE         BIT_31
#define ITEM_ANTI_FEMALE       BIT_32
// Seriously need to expand this to allow new races and classes in -KPZ

/* Some different kind of liquids */
#define LIQ_WATER       0
#define LIQ_BEER        1
#define LIQ_WINE        2
#define LIQ_ALE         3
#define LIQ_DARKALE     4
#define LIQ_WHISKY      5
#define LIQ_LEMONADE    6
#define LIQ_FIREBRT     7
#define LIQ_LOCALSPC    8
#define LIQ_SLIME       9
#define LIQ_MILK       10
#define LIQ_TEA        11
#define LIQ_COFFE      12
#define LIQ_BLOOD      13
#define LIQ_SALTWATER  14
#define LIQ_COKE       15
#define LIQ_LOTSAWATER 16
#define LIQ_HOLYWATER  17
#define LIQ_MISO       18
#define LIQ_MINESTRONE 19
#define LIQ_DUTCH      20
#define LIQ_SHARK      21
#define LIQ_BIRD       22
#define LIQ_CHAMPAGNE  23
#define LIQ_PEPSI      24
#define LIQ_UNHOLY     25
#define LIQ_SAKE       26
#define LIQ_CURE       27
#define LIQ_EGGNOG     28
#define LIQ_LAST_ONE LIQ_CURE

/* for containers  - value[1] */

#define CONT_CLOSEABLE     BIT_1
#define CONT_HARDPICK      BIT_2 /* not used, for compatibility only */
#define CONT_CLOSED        BIT_3
#define CONT_LOCKED        BIT_4
#define CONT_PICKPROOF     BIT_5

/* for loc_p */
#define LOC_NOWHERE        BIT_1
#define LOC_ROOM           BIT_2
#define LOC_CARRIED        BIT_3
#define LOC_WORN           BIT_4
#define LOC_INSIDE         BIT_5
#define LOC_FLOATING       BIT_6   /* floating implies LOC_ROOM, but object won't fall */

#ifdef BODYPARTS
#define REGION_CRANIAL
#define REGION_TORSIAL
#define REGION_ARMS
#define REGION_LEGS
#define
#endif

/* for 'str_mask' */

#define STRUNG_KEYS   BIT_1     /* M: name         O: name               */
#define STRUNG_DESC1  BIT_2     /* M: long_descr   O: description        */
#define STRUNG_DESC2  BIT_3     /* M: short_descr  O: short_description  */
#define STRUNG_DESC3  BIT_4     /* M: description  O: action_description */
#define STRUNG_EDESC  BIT_5     /* M: (n/a)        O: extra_description  */

#define COIN_COPPER     0
#define COIN_SILVER     1
#define COIN_GOLD       2
#define COIN_PLATINUM   3
#define NUM_COIN_TYPES  4

struct extra_descr_data {
    char *keyword; /* Keyword in look/examine          */
    char *description; /* What to see                      */
    struct extra_descr_data *next; /* Next in list                     */
};

#define OBJ_NOTIMER    -7000000

struct obj_affected_type {
    byte location; /* Which ability to change (APPLY_XXX) */
    sbyte modifier; /* How much it changes by              */
};

/* ======================== Structure for object ========================= */
struct obj_data {
    int R_num; /* Where in data-base               */

#ifdef SERIAL_NUMBERS
    uint OSN; /* object serial number             */
#endif

    byte type; /* Type of item                     */
    byte str_mask; /* for 'strung' char * fields       */
    char *name; /* Title of object :get etc.        */
    char *description; /* When in room                     */
    char *short_description; /* when worn/carry/in cont.         */
    char *action_description; /* What to write when used          */

    struct extra_descr_data
    *ex_description; /* extra descriptions               */

    int value[8]; /* Values of the item (see list)    */
    uint wear_flags; /* Where you can wear it            */
    uint extra_flags; /* If it hums, glows etc            */
    uint anti_flags; /* Specific anti-class and race     */
    int weight; /* Weight, what else                */
    int cost; /* Value when sold (gp.)            */
    int durability; /* how long before the item breaks  */

    sh_int trap_eff; /* trap effect type                 */
    sh_int trap_dam; /* trap damage type                 */
    sh_int trap_charge; /* trap charges                     */
    sh_int trap_level; /* trap level (damage)              */
    sh_int trap_dnum; /* Trap number of dam dice          */
    sh_int trap_dsize; /* Trap size of dam dice            */

    ubyte sets_affs[AFF_BYTES]; /* replaces bitvector and sets_affs */

    struct obj_affected_type
    affected[MAX_OBJ_AFFECT]; /* Which abilities in PC to change  */

    byte loc_p; /* bitfield for loc union           */

    union {
        P_char carrying; /* character carrying object        */
        P_char wearing; /* character wearing object         */
        P_obj inside; /* object's container               */
        int room; /* R_num of room it's in            */
    }
    loc;

    P_char owner; /* @@@@ player who owns a cart           */
    P_char last_to_hold; /* If MOB forcibly looses item      */
    P_event events; /* events attached to this obj      */
    P_obj contains; /* Contains objects                 */
    P_obj next_content; /* For 'contains' lists             */
    P_obj prev, next; /* For the object list              */
};

/* ======================================================================= */

struct spellcast_datatype {
    P_char ch;

    P_char victim; /* Victim, object, & needs_parsing fields added */
    P_obj object; /* to faciliate NewMobAct() NPC casts - SKB     */
    bool needs_parsing; /* to faciliate NewMobAct() NPC casts - SKB       */
    bool powercast;
    int timeleft;
    int failtime;
    ush_int spell;
    char *args;
    struct spellcast_datatype *next;
};

struct scribing_data_type {
    P_char ch;
    P_obj book;

    union {
        P_obj obj;
        P_char teacher;
    }
    source;

    ush_int spell;
    int pagetime; /* in 1/4 seconds, RL / 1 pulses - time to complete one page of the spell */
    int page; /* page number last written - for event-wise way we do these things. */
    int flag; /* 0 = teach, 1 = spellbook, 2 = scroll */
    struct scribing_data_type *next;
};

#define SBOOK_MODE_AT_HAND   1
#define SBOOK_MODE_IN_INV    2
#define SBOOK_MODE_NO_SCROLL 4
#define SBOOK_MODE_NO_BOOK   8

/* for the table of which modes of activation are legal for each spell (valid_spell_devices[] in constant.c) */
struct spell_items_type {
    byte potion;
    byte scroll;
    byte spell;
    byte staff;
    byte wand;
    byte weapon;
};

struct climate {
    char season_pattern;
    char season_wind[MAX_SEASONS];
    char season_wind_dir[MAX_SEASONS];
    char season_wind_variance[MAX_SEASONS];
    char season_precip[MAX_SEASONS];
    char season_temp[MAX_SEASONS];
    char flags;
    signed int energy_add;
};

struct weather_data {
    int free_energy;
    int pressure; /* Kept from previous system */
    int windspeed;
    char ambient_light; /* Interaction between sun, moon, clouds, etc. Local lights ignored */
    char flags;
    char precip_depth; /* Snowpack, flood level */
    char pressure_change, precip_change;
    char wind_dir;
    signed char humidity;
    signed char precip_rate;
    signed char temp; /* In Celsius... So what if I'm a yankee, I still prefer the metric system */
};

#define CMD_STAT_SUCCESS         0
#define CMD_STAT_NO_MOB          1
#define CMD_STAT_SKIP_IF         2
#define CMD_STAT_MOB_FAILED_LOAD 3
#define CMD_STAT_OBJ_FAILED_LOAD 4
#define CMD_STAT_RARE_PCT_FAILED 5
#define CMD_STAT_UNKNOWN_CMD     6
#define CMD_STAT_DISABLED        7
#define CMD_STAT_TIME_FAIL       8
#define CMD_STAT_MAX_LIMIT_HIT   9
#define CMD_STAT_OBJ_TO_OBJ_FAIL 10
#define CMD_STAT_BAD_DOOR        11
#define CMD_STAT_REMOVE_OBJ_FAIL 12
#define CMD_STAT_REMOVE_MOB_FAIL 13
#define CMD_STAT_EQUIP_MOB_FAIL  14
#define CMD_STAT_ERROR           15

/* structure for the reset commands */
struct reset_com {
    char command; /* current command                      */
    bool if_flag; /* if TRUE: exe only if preceding exe'd */
    int arg1; /*                                      */
    int arg2; /* Arguments to the command             */
    int arg3; /*                                      */
    int arg4;

    char old_command; /* for when a cmd gets disabled         */
    int arg1v; /*                                      */
    int arg2v; /* Arguments to the command (virtuals)  */
    int arg3v; /*                                      */
    int arg4v;
    int status; /* status of cmd after a reset          */
    /*
     *  Commands:              *
     *  'M': Read a mobile     *
     *  'O': Read an object    *
     *  'G': Give obj to mob   *
     *  'P': Put obj in obj    *
     *  'G': Obj to char       *
     *  'E': Obj to char equip *
     *  'D': Set state of door *
     *  'F': Follow mobile     *
     */
};

#define MZR_UPTIME_HOURS   0     // this will be in config.h eventually, but here for now
// 40 is a decent ball park starting number as this was std reboot policy

/* zone definition structure. for the 'zone-table'   */
struct zone_data {
    char *name; // name of this zone
    char *filename; // actual filename of zone for OLC
    int active_olc; // Flag: Is this zone being worked on in OLC?
    int lifespan; // current (minutes) in this reset
    int lifespan_min; // minimum age (minutes) before reset  (unused)
    int lifespan_max; // maximum age (minutes) before reset  (unused)
    int age; // current age of this zone (minutes)
    int top; // upper limit for rooms in this zone
    int free; // How many rooms are free in virtual numbers.
    int used; // How many rooms are used in virtual numbers.
    int avail_to_olc; // How many rooms are reserved for creation with OLC.
    uint flags;

    char *maker; // The zone maker - options field in .zon
    char *maker_email; // The zone maker's email - options field in .zon
    char *creation_date; // Date the file was created
    char *last_mod_date; // Last time the file was modified

    /* Added for OLC */
    char *owner; // Area makers name

    int real_top, real_bottom; // these are for rooms, but mob and obj are potentially different!
    int mob_top, mob_bottom;
    int obj_top, obj_bottom;

    int actual_rooms;
    int reset_mode; // Reset mode:
    // 0: Don't reset, and don't update age.
    // 1: Reset if no PC's are located in zone.
    // 2: Just reset.
    // 3: Allow major reset, zone must be empty like mode 1
    // 4: Allow major reset, just reset like mode 2

    int time_last_boot; // this is a time(0) of when last major reset occurred
    int mzr_pct; // major zone reset percent chance (only applies if mode is 3)
    // 100 would mean always, 0 would mean never
    // defaults to 0 if not found in file

    int hrs_since_req; // hours since last mrz must be >= to this before the next mzr is considered
    // deafults to MZR_UPTIME_HOURS if not found in file

    int zone_empty_cnt; // count of resets that zone was empty, reset to zero on a mzr
    int zone_empty_cnt_req; // count of resets that zone was empty must be >= this before next mzr is considered
    // defaults to 0 if not found in file

    struct reset_com *cmd; // command table for reset
    int cmd_count; // number of commands allocated for this zone

    struct climate climate;
    struct weather_data conditions;
    int hometown; // should default to 0, else be a number from 0 to LAST_HOME
};

#define FUNC_NONE 0
#define FUNC_MOB  1
#define FUNC_OBJ  2
#define FUNC_PC   4
#ifdef NEW_GROUP_PROC
#define FUNC_GRP  5
#endif

#ifdef NEW_GROUP_PROC

struct func_attachment {
    byte type; /* 0 - none, 1 - mob, 2 - obj, 5 - grp */
    uint proc_flag; /* which flags this proc sets */
    const char *name; /* name of the function (for do_stat) */

    union {
        int (*ch) (P_char, P_char, int, char *); /* mob function */
        int (*obj) (P_obj, P_char, int, char *); /* obj function */
        int (*grp) (P_group, P_char, int, char *); /* grp function */
    } func;

    struct func_attachment *next;
};
#else /* NEW_GROUP_PROC */

struct func_attachment {
    byte type; /* 0 - none, 1 - mob, 2 - obj */
    uint proc_flag; /* which flags this proc sets */
    const char *name; /* name of the function (for do_stat) */

    union {
        int (*ch) (P_char, P_char, int, char *); /* mob function */
        int (*obj) (P_obj, P_char, int, char *); /* obj function */
    } func;

    struct func_attachment *next;
};
#endif /* NEW_GROUP_PROC */

/* element in monster and object index-tables   */
struct index_data {
    int virtual; /* virtual number of this mob/obj           */
    int number; /* number of existing units of this mob/obj */
    int pos; /* file position of this field              */

    /* these strings are used to share RAM, all mobs/objs desc pointers will
       point here.  Reason being, by placing them here, they never have to be
       freed, simplifies things immensely. -JAB */
    char *keys; /* mob/obj keywords                       */
    char *desc1; /* mob long_descr/obj description         */
    char *desc2; /* mob short_descr/obj short_description  */
    char *desc3; /* mob description/obj action_description */

    int spec_flag; /* flags set by the procs themselves */
    struct func_attachment *func;
};

typedef struct index_data *P_index;

#ifdef ARTIFACT

/* structure containing artifact file position data */
struct artifact_data {
    char *owner; /* owner of the artifact */
    int virtual; /* virtual number of this file position */
    int pos; /* position in the file for this artifact */
    P_char ch; /* the ch which last held this artifact */
};
#endif


#if 0
// Azuth this seems to never be used heh

/* for queueing zones for update   */
struct reset_q_element {
    int zone_to_reset; /* ref to zone_data */
    struct reset_q_element *next;
};

/* structure for the update queue     */
struct reset_q_type {
    struct reset_q_element *head;
    struct reset_q_element *tail;
};
#endif

struct help_index_element {
    char *keyword;
    int pos;
};

struct whelp_index_element {
    char *keyword;
    int pos;
};

struct info_index_element {
    char *keyword;
    int pos;
};

/* The following defs are for room_data  */

/* Bitvector For 'room_flags' */

#define DARK         1  /* Need a light to look around here    */
#define DEATH        2
#define NO_MOB       3  /* Mobiles are not permitted into here */
#define INDOORS      4  /* Room is considered to be 'indoors'  */
#define ROOM_SILENT  5
#define UNDERWATER   6
#define NORECALL     7
#define NO_MAGIC     8  /* Casting magic is not permitted.        */
#define TUNNEL       9
#define PRIVATE      10 /* No more than two ppl can move in here  */
#define ARENA        11
#define SAFE_ZONE    12 /* No steal, attacks permitted in room    */
#define NO_PRECIP    13
#define SINGLE_FILE  14
#define JAIL         15
#define NO_TELEPORT  16
#define RESERVED_OLC 17 /* For null-rooms in olc                  */
#define HEAL         18 /* You regain stats twice as fast here    */
#define NO_HEAL      19 /* Cannot regain hp/mv/ma within room     */
#define HAS_TRAP     20 /* Thief has laid a trap here             */
#define DOCKABLE               21       /* SHIP can dock in this room             */
#define MAGIC_DARK             22
#define MAGIC_LIGHT            23
#define NO_SUMMON              24       /* Cannot summon or be summoned to or from */
#define ON_FIRE                25       /* Fire fire fire fire! */
#define NUKED                  26      /* Fun! --MIAX */
#define BURNING                27
#define DIMENTIONAL_BLOCK      28 /* Prevents dimentional rifts */
#define DIMENTION_OCCUPIED     29 /* Don't Set! Internal use only. */
#define ROOM_AIRY_WATER        30 /* Breathable underwater room */
#define ROOM_SOLID_FOG         31 /* Blocks farsee/targeted missiles/sunblind */
#define MAGIC_OK               32 /* overrides zone flag ZONE_NO_MAGIC */
#define ROOM_HOUSE             33 /* (R) Room is a house  */
#define ROOM_ATRIUM            34 /* (R) The door to a house      */
#define GUILD_ROOM             35 /* for player guild rooms */
#define PSPREGEN               36 /* accelerated PSP regeneration rate */
#define DIFFUSE_MESSAGES       37 /* All the messages displayed in that room are "transmitted" to other rooms as well */
#define ROOM_GLOBE_OF_DARKNESS 38 /* room is under the effect of a globe of darkness, wankers using ultravision wont be blinded */
#define ROOM_HOUSING           39 /* Players are allowed to build houses etc */
#define MAX_TWO_PC             40 /* just like it says */
#define PRIV_ZONE              41 /* just like it says */
#define ROOM_MUD               42 /* Mud to Rock */
#define ROOM_ROCK              43 /* Rock to Mud */
#define ROOM_SPEC              44 // For use with special procedurs
#define ROOM_INN               45 // For the storage command
#define ROOM_SUN_SHADOW        46 // Blocks sunblind
#define ROOM_EARTH_FOG         47 // Earth Fog Spell, blocks sunblind
#define ROOM_FIRE_FOG          48 // Fire Fog Spell, lights up room
#define ROOM_EARTH_FOG_TRAVEL  49
#define ROOM_FIRE_FOG_TRAVEL   50

/* no room for TELEPORT_OK, SUMMON_OK, RECALL_OK! */

/* For 'dir_option' */

#define NORTH          0
#define EAST           1
#define SOUTH          2
#define WEST           3
#define UP             4
#define DOWN           5
#define NUMB_EXITS     6        /* number of exits.. Alth Apr 1st <g> */

#define EX_ISDOOR      BIT_1
#define EX_CLOSED      BIT_2
#define EX_LOCKED      BIT_3
#define EX_RSCLOSED    BIT_4
#define EX_RSLOCKED    BIT_5
#define EX_PICKABLE    BIT_6    /* ignored, for backwards compatibility only */
#define EX_SECRET      BIT_7
#define EX_BLOCKED     BIT_8
#define EX_PICKPROOF   BIT_9
#define EX_TRAPPED     BIT_10

/* For 'Sector types' */

#define SECT_INSIDE            0
#define SECT_CITY              1
#define SECT_FIELD             2
#define SECT_FOREST            3
#define SECT_HILLS             4
#define SECT_MOUNTAIN          5
#define SECT_WATER_SWIM        6
#define SECT_WATER_NOSWIM      7
#define SECT_NO_GROUND         8
#define SECT_UNDERWATER        9
#define SECT_UNDERWATER_GR    10
#define SECT_FIREPLANE        11
#define SECT_OCEAN            12
#define SECT_UNDRWLD_WILD     13
#define SECT_UNDRWLD_CITY     14
#define SECT_UNDRWLD_INSIDE   15
#define SECT_UNDRWLD_WATER    16
#define SECT_UNDRWLD_NOSWIM   17
#define SECT_UNDRWLD_NOGROUND 18
#define SECT_AIR_PLANE        19
#define SECT_WATER_PLANE      20
#define SECT_EARTH_PLANE      21
#define SECT_ETHEREAL         22
#define SECT_ASTRAL           23
#define SECT_ICE_PLANE        24
#define SECT_UD_UNDERWATER    25
#define SECT_UD_UNDERWATER_GR 26
#ifdef KINGDOM
#define SECT_CASTLE_WALL      27
#define SECT_CASTLE_GATE      28
#define SECT_CASTLE           29
#endif
#define SECT_SMOKE_PLANE      30
#define SECT_MAGMA_PLANE      31

// IMPORTANT! - README - IMPORTANT!
// MAKE SURE You increment the NumberOfSectors varible to equal the number
// of actual sector types listed above, if you add/remove some. Also don't
// forget to modify the "Sector_Types" list in constant.c to reflect the
// modifications you made. Fail to do this, and you break the mud. --MIAX o_o

#define NumberOfSectorTypes    31



#define MANA_ALL_ALIGNS      0
#define MANA_GOOD            1
#define MANA_NEUTRAL         2
#define MANA_EVIL            3

/* How much light is in the land ? */

#define SUN_DARK        0
#define SUN_RISE        1
#define SUN_LIGHT       2
#define SUN_SET         3

/* And how is the sky ? */

#define SKY_CLOUDLESS   0
#define SKY_CLOUDY      1
#define SKY_RAINING     2
#define SKY_LIGHTNING   3

struct room_direction_data {
    int to_room; /* Where direction leeds (NOWHERE) */
    int key; /* Key's number (-1 for no key)    */
    char *general_description; /* When look DIR.                  */
    char *keyword; /* for open/close                  */
    sh_int exit_info; /* Exit info                       */

    /* trap stuff... just hope this won't hog too much RAM <Ilsie> */

    bool trap_state; /* 0 disabled, 1 enabled */
    sh_int trap_type;
    sh_int trap_min_damage;
    sh_int trap_max_damage;
    bool trap_effect; /* 0 player effect, 1 area effect */
    sh_int trap_hardness; /* how much hard is to detect and remove the trap */
    sh_int trap_load_percent;
};

/*  TRAP TYPES  */
#define TRAP_NONE                       0
#define TRAP_BLADE              1
#define TRAP_POISON             2
#define TRAP_ROCK                       3
#define TRAP_FIREBALL           4
#define TRAP_LIGHTNING          5
#define TRAP_RANDOM             10
#define TRAP_FALLING            11
#define MAX_TRAP_TYPES       5 /* this should be the number of randomizable traps */

/* more to come... */


struct linked_room {
    int room;
    struct linked_room *next_room;
};

/* ========================= Structure for room ========================== */
struct room_data {
    int number; /* Room number                        */
    ubyte zone; /* Room zone (for resetting)          */
    byte sector_type; /* sector type (move/hide)            */
    char *name; /* Rooms name 'You are ...'           */
    char *description; /* Shown when entered                 */
    struct extra_descr_data *ex_description; /* for examine/look                   */
    struct room_direction_data *dir_option[6]; /* Directions                         */
    ubyte room_flags[ROOM_FLAG_BYTES]; /* DEATH, DARK ... etc                */

    byte light; /* Number of lightsources in room     */
    byte chance_fall;
    byte minlvl, maxlvl;
    byte mana;
    byte mana_alignment;
    uint length;
    uint width;
    uint height;
    byte size;

    /* special procedure */
    int (*funct) (int, P_char, int, char *);

    P_obj contents; /* List of items in room              */
    P_char people; /* List of NPC / PC in room           */
    P_event events; /* events attached to this room       */

    ubyte visited; /* by using a ubyte, we only have to clear
                                        this variable once every 255
                                   tracking efforts, rather than each time.  JAB */

    /* For the skill track, we need a bit (or byte) to indicate that    */
    /* we have traversed this node already (as to avoid looping), the   */
    /* following field is used for exactly this purpose.                */
    bool track_visited;
    /* the idea is to display all the messages that happen in a room to other rooms */
    struct linked_room *linked; /* pointer to an array containing all the vnums of the rooms where the messages are displayed */


#ifdef KINGDOM
    ubyte kingdom_num; /* matches guild or town number       */
    ubyte kingdom_type; /* town, pc, npc                      */
#endif

};

/* ======================================================================== */

/* The following defs and structures are related to char_data   */

/* For 'equipment' */

#define WEAR_LIGHT              0       /* should not be used any longer! */
#define WEAR_FINGER_R           1
#define WEAR_FINGER_L           2
#define WEAR_NECK_1             3
#define WEAR_NECK_2             4
#define WEAR_BODY               5
#define WEAR_HEAD               6
#define WEAR_LEGS               7
#define WEAR_FEET               8
#define WEAR_HANDS              9
#define WEAR_ARMS              10
#define WEAR_SHIELD            11
#define WEAR_ABOUT             12
#define WEAR_WAIST             13
#define WEAR_WRIST_R           14
#define WEAR_WRIST_L           15
#define PRIMARY_WEAPON         16
#define WIELD                  PRIMARY_WEAPON
#define SECONDARY_WEAPON       17
#define HOLD                   18
#define WEAR_EYES              19
#define WEAR_FACE              20
#define WEAR_EARRING_R         21
#define WEAR_EARRING_L         22
#define WEAR_QUIVER            23
#define GUILD_INSIGNIA         24
#define WEAR_TAIL              25

#define CUR_MAX_WEAR           25

/* For 'char_player_data' */

#define SKILL_BASE_SPECIAL BIT_30       /* Taught by quest mobs only (/ spellbooks) */

/* Predifined  conditions */
#define DRUNK        0
#define FULL         1
#define THIRST       2
#define ALL_CONDS    3

/* These new AFF_xxx flags use cbit system, used by obj_data, char_special_data and affected_type
    current valid range 1-88 */
#define AFF_BLIND              1
#define AFF_INVISIBLE          2
#define AFF_FARSEE             3
#define AFF_DETECT_INVISIBLE   4
#define AFF_HASTE              5
#define AFF_SENSE_LIFE         6
#define AFF_MINOR_GLOBE        7
#define AFF_STONE_SKIN         8
#define AFF_CHARGING           9
#define AFF_SHADOW            10
#define AFF_WRAITHFORM        11
#define AFF_WATERBREATH       12
#define AFF_KNOCKED_OUT       13
#define AFF_PROTECT_EVIL      14
#define AFF_BOUND             15
#define AFF_SLOW_POISON       16
#define AFF_PROTECT_GOOD      17
#define AFF_SLEEP             18
#define AFF_SKILL_AWARE       19        /* for awareness skill --TAM 7-9-94 */
#define AFF_SNEAK             20
#define AFF_HIDE              21
#define AFF_FEAR              22
#define AFF_CHARM             23
#define AFF_MEDITATE          24
#define AFF_BARKSKIN          25
#define AFF_INFRAVISION       26
#define AFF_LEVITATE          27
#define AFF_FLY               28
#define AFF_AWARE             29
#define AFF_PROT_FIRE         30
#define AFF_CAMPING           31
#define AFF_SINGING           32        /* bards */
#define AFF_FIRESHIELD        33
#define AFF_ULTRAVISION       34
#define AFF_DETECT_EVIL       35
#define AFF_DETECT_GOOD       36
#define AFF_DETECT_MAGIC      37
#define AFF_MAJOR_PHYSICAL    38
#define AFF_PROT_COLD         39
#define AFF_PROT_LIGHTNING    40
#define AFF_MINOR_PARALYSIS   41
#define AFF_MAJOR_PARALYSIS   42
#define AFF_SLOW              43
#define AFF_GLOBE             44
#define AFF_PROT_GAS          45
#define AFF_PROT_ACID         46
#define AFF_AUTO_FIRING       47        /* Affects for missile code. --MIAX */
#define AFF_HUNTER            48        /* " */
#define AFF_MISSILE_AWARE     49        /* " */
#define AFF_MISSILE_SNARE     50        /* " */
#define AFF_MISSILE_SHIELD    51        /* " */
#define AFF_STUNNED           52
#define AFF_DROPPED_PRIM      53
#define AFF_DROPPED_SECOND    54
#define AFF_FUMBLING_PRIM     55
#define AFF_FUMBLING_SECOND   56
#define AFF_RES_PENALTY       57
#define AFF_MEMORIZING        58
#define AFF_BRAINDRAIN        59
#define AFF_PASS_WITHOUT_TRACE       60
#define AFF_DOCILE            61
#define AFF_CASTING           62
#define AFF_SCRIBING          63
#define AFF_VAMPIRIC_TOUCH    64
#define AFF_CATFALL           65
#define AFF_BODY_CONTROL      66
#define AFF_STASIS_FIELD      67
#define AFF_GLOBE_OF_DARKNESS 68
#define AFF_METAGLOBE         69
#define AFF_ICE_TOMB          70
#define AFF_BLUR              71
#define AFF_BURNING           72   /* For generic char 'burn' events - CRM */
#define AFF_REPULSION         73
#define AFF_MIND_BLANK        74
#define AFF_DRAGONSCALES      75
#define AFF_MIRROR_IMAGE      76
#define AFF_SEQUESTER         77
#define AFF_NONDETECTION      78
#define AFF_DISPLACEMENT      79
#define AFF_GROUP_CACHED      80   /* specified that an obj has been added to a group cache - Alth */
#define AFF_MORPH             81
#define AFF_MAGE_FLAME        82   /* For mage flame spell */
#define AFF_TOWER_OF_IRON_WILL 83
#define AFF_FIRE_FOG          84
#define AFF_EXITING_QUEST     85   /* For exiting quest pkill areas*/
#define AFF_EMITTING          86   /* For myconids.. --CRM */
#define AFF_ENGAGEDINPK       87   /* PC was engaged in PK */
#define AFF_VIRTUOSO          88   /* Bard trying to sing like Sinatra */
#define AFF_GARROTING         89   /* Assassin doing the choke thing.  --D2 */
#define AFF_GARROTE_VICT      90   /* The chokee.  */
#define AFF_TRACKING          91   /* We're on the trail.  --D2 */
#define AFF_EXITING_ACHERON   92   /* like camp, but for acheron */
#define AFF_BLACKMANTLE       93   /* Person can't heal - DA */
#define AFF_SUMMON_HORDE      94
#define AFF_HEX               95
#define AFF_ANCESTRAL_SHIELD  96
#define AFF_PROTECTION_FROM_ANIMALS 97
#define AFF_SILENCE_PERSON    98
#define AFF_EARTH_FOG         99
#define AFF_ENTANGLE          100
#define AFF_TRUE_SIGHT        101
#define AFF_DOPPLEGANGER      102
#define AFF_PLANT_ANCHOR      103
#define AFF_WARCHANTING       104
#define AFF_ELEMENTAL_WATER   105
#define AFF_ELEMENTAL_FIRE    106
#define AFF_ELEMENTAL_EARTH   107
#define AFF_ELEMENTAL_AIR     108
#define AFF_ELEMENTAL_MAINTAIN 109
#define AFF_ELEMENTAL_WARD    110
#define AFF_ORDERED           111
#define AFF_CASTER_STONE_SKIN 112
#define AFF_BANSHEE_WAIL      113
#define AFF_UNDEAD_MELEE_PROC 114
#define AFF_UNDEAD_SPELL_PROC 115
#define AFF_DEATH_PACT        116
#define AFF_SOUL_BIND         117
#define AFF_CASTER_DRAGONSCALES 118
#define AFF_TIME_STOP         119
#define AFF_NATURES_BLESSING  120
#define AFF_PATH              121
#define AFF_REVELATION        122
#define AFF_PROTECTION        123
#define AFF_TRAVEL            124
#define AFF_HOWL              125
//#define AFF_MERCHANT          126 // merchant and raider flag trade system mobs
//#define AFF_RAIDER            127
#define AFF_MULTI_ROUND_SPELL 126   /* For multi-round spells */
#define AFF_CAMO              127  // Camoflage spell

/* ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
 * REMEMBER TO EXPAND the AFF_BYTES AND MAX_AFF_BITS if the number of affects passes
 * a multiple of 8. otherwise massive corruptions will occur out of the blue. -- Alth '01 */

/* modifiers to char's abilities */

#define APPLY_NONE              0
#define APPLY_STR               1
#define APPLY_DEX               2
#define APPLY_INT               3
#define APPLY_WIS               4
#define APPLY_CON               5
#define APPLY_SEX               6
#define APPLY_CLASS             7
#define APPLY_LEVEL             8
#define APPLY_AGE               9
#define APPLY_CHAR_WEIGHT      10
#define APPLY_CHAR_HEIGHT      11
#define APPLY_MANA             12
#define APPLY_HIT              13
#define APPLY_MOVE             14
#define APPLY_GOLD             15
#define APPLY_EXP              16
#define APPLY_AC               17
#define APPLY_ARMOR            17
#define APPLY_HITROLL          18
#define APPLY_DAMROLL          19
#define APPLY_SAVING_PARA      20
#define APPLY_SAVING_ROD       21
#define APPLY_SAVING_PETRI     22
#define APPLY_SAVING_BREATH    23
#define APPLY_SAVING_SPELL     24
#define APPLY_FIRE_PROT        25
#define APPLY_AGI              26  /* these 5 are the 'normal' applies for the new stats */
#define APPLY_POW              27
#define APPLY_CHA              28
#define APPLY_KARMA            29
#define APPLY_LUCK             30
#define APPLY_STR_MAX          31  /* these 10 can raise a stat above 100, I will personally rip the */
#define APPLY_DEX_MAX          32  /* lungs out of anyone using these or the next 10 on easy-to-get items. */
#define APPLY_INT_MAX          33  /* JAB*/
#define APPLY_WIS_MAX          34
#define APPLY_CON_MAX          35
#define APPLY_AGI_MAX          36
#define APPLY_POW_MAX          37
#define APPLY_CHA_MAX          38
#define APPLY_KARMA_MAX        39
#define APPLY_LUCK_MAX         40
#define APPLY_STR_RACE         41  /* these 10 override the racial stat_factor */
#define APPLY_DEX_RACE         42  /* so that setting APPLY_STR_RACE <ogre> will, */
#define APPLY_INT_RACE         43  /* for example, give you gauntlets of ogre strength. */
#define APPLY_WIS_RACE         44  /* before using these, say to yourself, 'Are my lungs safe?' */
#define APPLY_CON_RACE         45
#define APPLY_AGI_RACE         46
#define APPLY_POW_RACE         47
#define APPLY_CHA_RACE         48
#define APPLY_KARMA_RACE       49
#define APPLY_LUCK_RACE        50
#define APPLY_MAGIC_RESIST     51
#define APPLY_CUR_HIT          52  // Current HP, not max.

/* animals fall into three categorizations */
#define ANIMAL_TYPE_BIRD     0
#define ANIMAL_TYPE_REPTILE  1
#define ANIMAL_TYPE_MAMMAL   2

/* sex */
#define SEX_NEUTRAL     0
#define SEX_MALE        1
#define SEX_FEMALE      2

/* these two work together to give us many possible positions. */

/* postures */
#define POS_PRONE       0
#define POS_KNEELING    1
#define POS_SITTING     2
#define POS_STANDING    3

/* status */
#define STAT_DEAD       BIT_3
#define STAT_DYING      BIT_4
#define STAT_INCAP      BIT_5
#define STAT_SLEEPING   BIT_6
#define STAT_RESTING    BIT_7
#define STAT_NORMAL     BIT_8

#define STAT_MASK       (BIT_9 - 4)

/* for mobile actions: only.npc->npcact */
#define ACT_SPEC               1        /* mob has a special routine           */
#define ACT_SENTINEL           2        /* this mobile not to be moved         */
#define ACT_SCAVENGER          3        /* pick up stuff lying around          */
#define ACT_ISNPC          4    /* needs to be rem'd from areas b4 use */
#define ACT_NICE_THIEF         5        /* Set if a thief should NOT be killed */
#define ACT_AGGRESSIVE         6        /* Set if automatic attack on PC's     */
#define ACT_STAY_ZONE          7        /* MOB Must stay inside its own zone   */
#define ACT_WIMPY              8        /* MOB Will flee when injured          */
#define ACT_AGGRESSIVE_EVIL    9
#define ACT_AGGRESSIVE_GOOD    10
#define ACT_AGGRESSIVE_NEUTRAL 11
#define ACT_MEMORY             12
#define ACT_STAY_SECTOR        13
#define ACT_SAVE               14
#define ACT_HELPER             15   /* Mob will freely cast on fellow NPC's*/
#define ACT_DELAY_HUNTER       16   /* Mob will only start hunting once its been damaged */
#define ACT_ARCHER             17
#define ACT_NOKILL             18   /* Mob will not fight, or be hurt -CRM */
#define ACT_HAS_PS             19
#define ACT_HAS_CL             20
#define ACT_HAS_MU             21
#define ACT_HAS_TH             22
#define ACT_HAS_WA             23
#define ACT_SPEC_DIE           24
#define ACT_WITNESS            25
#define ACT_BREAK_CHARM        26
#define ACT_PROTECTOR          27
#define ACT_MOUNT              28       /* MOB can be mounted by player -DCL */
#define ACT_AGG_RACEEVIL       29       /* Aggressive towards evil races.    */
#define ACT_AGG_RACEGOOD       30       /* Aggressive to good races. --MIAX  */
#define ACT_HUNTER             31       /* Hunt mode, disregards sentinel    */
#define ACT_AGG_OUTCAST        32       /* Mob aggroes on outcasts           */

/* For players : only.pc->pcact current range 0-39, change MAX_PACT and PACT_BYTES for more. */

#define PLR_BRIEF        1      /* Do not show long desc on a do_look    */
#define PLR_NOSHOUT      2      /* Like auction, etc, but with a !       */
#define PLR_COMPACT      3      /* Do not put in extra carriage return   */
#define PLR_DUMB_TERM    4      /* do we have an stupid terminal that needs \r's in addition to \n's? */
#define PLR_CONNMSG      5      /* If on, player hears connection messages */
#define PLR_PETITION     6      /* If on, player can buzz god            */
#define PLR_OOC          7      /* If on, player hears ooc channel       */
#define PLR_WIZLOG       8      /* For wizards. To recieve system msgs   */
#define PLR_STATUS       9      /* deaths, logon, logoff */
#define PLR_NOFOLLOW     10
#define PLR_VICIOUS      11
#define PLR_ECHO         12
#define PLR_SNOTIFY      13
#define PLR_NOTELL       14
#define PLR_BANS         15 /* reject connect messages */
#define PLR_ANONYMOUS    16
#define PLR_AGGIMMUNE    17
#define PLR_WIZMUFFED    18
#define PLR_NOEMOTE      19
#define PLR_PAGING_ON    20
#define PLR_VNUM         21
#define PLR_WITNESS      22 /* player has witnessed a crime */
#define PLR_AFK          23
#define PLR_SMARTPROMPT  24
/* next 3 moved from pflags (which is now law_flags). JAB */
#define PLR_SILENCE      25 /* Player has been silenced.               */
#define PLR_FROZEN       26 /* Player has been frozen. (no cmd input)  */
#define PLR_NO_KILL      27 /* player will not attack other players    */
/* god classes (sic), will shoot anyone setting these on mortals.  JAB */
#define PLR_G_ADMIN      28
#define PLR_G_AREAS      29
#define PLR_G_CODER      30
#define PLR_G_WWW        31
#define PLR_ROLEPLAY     32
#define PLR_MORPH        33 /* For Shapechanged chars */
#define PLR_DEBUGLOG     34
#define PLR_OUTCAST      35
#define PLR_PETITION2    36 /* duplicated pure petition channel (god only), dumb, but admins want their additional spam on PLR_PETITION */
#define PLR_EMSLOG       37
#define PLR_ASSOC_CHAN   38 /* association channel, something like ooc or gsay but for member of a specific assoc  -- Alth */
#define PLR_NOGRPTITLE   39 /* player is not allowed to title groups when set */
#define PLR_VISDOWN      40 /* automaticaly vis down to the last vis level on game reentry */
#define PLR_NCC          41 /* Player is tuned into the NCC channel */
#define PLR_HELPER       42 /* Player is a newbie helper */
#define PLR_CHALLENGE    43 /* Duel Challenge */
#define PLR_DUEL         44 /* PLayer is duelling */
#define PLR_ASSOCLOG     45 /* tuned into the assoc log channel, staff only */
#define PLR_WHODISPROOM  46 /* who will display room vnums of targ PCs    -- alth Sep 17 1999 */
#define PLR_SHOWGIVES    47 /* will show obj transfers ie, bob gives a sword to PC or NPC    -- alth Sep 17 1999 */
#define PLR_SHOWTITS     48 /* look will show player titles in brief mode   -- alth Sep 17 1999 */
#define PLR_CONDENSED    49
#define PLR_REPLY_HOLD   50 /* For do_reply command -- CRM */
#define PLR_NO_SING      51 /* stops bard from singing */
#define PLR_ACHERON_CHANNEL 52 /* acheron comm channel */
#define PLR_G_QUEST      53 /* Quest God type */
#define PLR_QWIZ         54 /* Quest WIZ Channel */
#define PLR_WIZCMD       55 /* wiz command log spam control - alth '01 */
#define PLR_ROOMSIZE     56 /* Toggle for room sizes. 12/28/01 */
#define PLR_GROUPALLOW   57 // Allows good/evil race grouping
#define PLR_NAMEBAN      58
#define PLR_LFG          59
#define PLR_B_RP         60  // RP Indicator flags - Bad/Poor/Good/Excellent, in that order --CRM
#define PLR_P_RP         61
#define PLR_G_RP         62
#define PLR_E_RP         63
#define PLR_AUCTION      64
#define PLR_IMPTITLES    65  /* Implementor titles flag */
#define PLR_WIZMOVES     66  /* Wizard moves flag */

#define ACT_DELAY_BASH            0
#define ACT_DELAY_BERSERK         1
#define ACT_DELAY_INSTANTKILL     2
#define ACT_DELAY_DISARM          3     /* pulse violence */
#define ACT_DELAY_FUMBLING_PRIM   4     /* pulse violence */
#define ACT_DELAY_DROPPED_PRIM    5     /* pulse violence */
#define ACT_DELAY_FUMBLING_SECOND 6     /* pulse violence */
#define ACT_DELAY_DROPPED_SECOND  7     /* pulse violence */
#define ACT_DELAY_HEADBUTT        8     /* pulse violence */
#define ACT_DELAY_SHIELDPUNCH     9
#define ACT_DELAY_ARCHER         10
#define ACT_DELAY_HITALL         11
#define ACT_DELAY_DRAGON         12
#define ACT_DELAY_CAST           13

/* For players : Prompt flags (16 bits max) */
#define PROMPT_NONE        BIT_1
#define PROMPT_HIT         BIT_2
#define PROMPT_MAX_HIT     BIT_3
#define PROMPT_MANA        BIT_4
#define PROMPT_MAX_MANA    BIT_5
#define PROMPT_MOVE        BIT_6
#define PROMPT_MAX_MOVE    BIT_7
#define PROMPT_TANK_COND   BIT_8
#define PROMPT_TANK_NAME   BIT_9
#define PROMPT_ENEMY       BIT_10
#define PROMPT_ENEMY_COND  BIT_11
#define PROMPT_VIS         BIT_12
#define PROMPT_TWOLINE     BIT_13
#define PROMPT_TIME        BIT_14
#define PROMPT_POSITION    BIT_15

/* Skill usage limit --TAM 04/16/94 */
#define TIMED_USAGE_DETECT_TRAPS             0
#define TIMED_USAGE_REMOVE_TRAPS             1
#define TIMED_USAGE_SCHANGE_MAMMAL           2
#define TIMED_USAGE_LAYHAND                  3
#define TIMED_USAGE_LANGUAGES                4
#define TIMED_USAGE_PICK                     5
#define TIMED_USAGE_INNATE1                  6
#define TIMED_USAGE_INNATE2                  7
#define TIMED_USAGE_INNATE3                  8
#define TIMED_USAGE_PHYSICAL_LEARN           9
#define TIMED_USAGE_MENTAL_LEARN             10
#define TIMED_USAGE_SUMMON_MOUNT             11
#define TIMED_USAGE_LORE                     12
#define TIMED_USAGE_QUAFFING                 13
#define TIMED_USAGE_SHAMAN_SUMMON_TOTEM      14
#define TIMED_USAGE_CANIBALIZE               15
#define TIMED_USAGE_DROW_INTERFERENCE_SHIELD 16
#define TIMED_USAGE_DROW_DANGER_SENSE        17
#define TIMED_USAGE_FORAGE                   18
#define TIMED_USAGE_SCHANGE_REPTILE          19
#define TIMED_USAGE_SCHANGE_BIRD             20
#define TIMED_USAGE_SCHANGE_FISH             21
#define TIMED_USAGE_ASSASSINATE                    22
#define TIMED_USAGE_LIFETAP                            23
#define TIMED_USAGE_SUMMON_HORDE             24

#define TIMED_USAGE_MAX                      24  /* make sure this reflects the highest USED usage */

// Backwards compatibility - These have been removed.
#define TIMED_USAGE_FEIGN_DEATH              12
#define TIMED_USAGE_MONK_CHANT               8

/* for mob's memory system. */

struct AC_Memory {
    int m_max_size; /* Max size */
    int m_size; /* Current size */
    char **m_names; /* Names */
};

/* New struct for holding all shop data. MIAX */

/* heavily revised for new shop format. JAB */

struct shop_data {
    int keeper; /* The NPC who owns the shop (real)        */
    int in_room; /* Where is the shop? (real)               */

    byte hours[4]; /* Shop open/close times (cbit)            */
    byte open; /* Flag for open/close messages            */
    int *production; /* Which items shop produces (alloced array) (real) */
    sh_int greed; /* how greedy is keeper?                   */
    sh_int profit; /* What's the spread between buy/sell?     */

    byte types[ITEM_TYPE_BYTES]; /* Which item types shop will buy.         */
    byte cheats[SHOP_BIGOT_BYTES]; /* races/classes shop will cheat (cbit)    */
    byte hates[SHOP_BIGOT_BYTES]; /* races/classes shop will not deal with (cbit) */

    char *SM_shop_open; /* Path from home to work in morning       */
    char *SM_shop_close; /* Path from work to home in evening       */

    char *SM_sell_to; /* Message when player buys item           */
    char *SM_buy_from; /* Message when player sells item          */

    char *SM_shop_broke; /* Message if keeper hasn't got cash       */
    char *SM_buyer_broke; /* Message if player hasn't got cash       */
    char *SM_shop_not_have; /* Message if keeper hasn't got an item    */
    char *SM_buyer_not_have; /* Message if player hasn't got an item    */

    char *SM_not_buy; /* If keeper dosn't buy such things.       */
    char *SM_hates; /* Message given to hated races.           */

    byte magic_allowed; /* Flag->Can magic be cast in the shop     */
    byte shop_killable; /* Does shopkeeper allow attacks?          */
    byte shop_is_roaming; /* Flag->Does shopkeeper roam the world?   */
    byte deadbeat; /* How does keeper react if no money       */
    byte offense; /* How does keeper react when attacked     */
    sh_int production_count; /* number of items this shop produces      */
};

/* group data bitvectors */
#define GAFF_MASSMORPH                     BIT_1
#define GAFF_INVISIBLE                   BIT_2
#define GAFF_DETECT_INVISIBLE            BIT_3
#define GAFF_HASTE                       BIT_4
#define GAFF_MINOR_GLOBE                 BIT_5
#define GAFF_PROTECT_EVIL                BIT_6
#define GAFF_PROTECT_GOOD                BIT_7
#define GAFF_PROTECT_UNDEAD              BIT_8
#define GAFF_PROT_FIRE                   BIT_9
#define GAFF_PROT_COLD                   BIT_10
#define GAFF_PROT_LIGHTNING              BIT_11
#define GAFF_PROT_GAS                    BIT_12
#define GAFF_PROT_ACID                   BIT_13
#define GAFF_DETECT_EVIL                 BIT_14
#define GAFF_DETECT_GOOD                 BIT_15
#define GAFF_DETECT_MAGIC                BIT_16
#define GAFF_LEVITATE                    BIT_17
#define GAFF_FLY                         BIT_18

/* Group element bitvectors */
#define GRE_STATUS                       BIT_1 /* Toggle group status msgs */
#define GRE_NO_SPLIT                     BIT_2
#define GRE_NO_CACHE                     BIT_3
#define GRE_CHARMED                      BIT_4
#define GRE_DEFENDER                     BIT_5

/* feel free to add more here... */

struct cache_list {
    P_obj this; /* Pointer to object   */
    struct cache_list *next; /* Next object in list */
};

struct group_element {
    P_char this; /* Current Group Memeber            */
    uint flags; /* Personal group element flags */
    uint g_flags; /* flags dealing with group affects */
    struct affected_type *g_affs; /* affects the whole group           */
    int group_cash[4]; /* number of group's coins this has */
    struct cache_list *g_objs; /* list of group's objects this has */
    P_gmember next; /* next group member                */
};

// Combat Stats struct

struct combat_data {
    int meleeAttacks;
    int meleeHits;
    int meleeDamage;
    int spellAttacks;
    int spellDamage;
};

/* =======================  Structure for Groups  ====================== */
struct group_data {
    int number; /* real number of the group */

    byte str_mask; /* for 'strung' char * fields */
    char *long_descr; /* Leader can set the group's name
                                    and this is what is displayed when
                                    the group enters a room           */
    char *description; /* the description of the group */

    uint flags;
    int cash[4]; /* Total money owned by the group    */
    int exp; /* experience value of the group */
    bool duelling;

    P_char leader; /* Pointer to the leader             */
    P_gmember members; /* List containing all group members */
    P_group duel; /* Group this one is duelling with   */
    P_group next; /* group list                        */
    struct combat_data combat_log;
};

/* ===================================================================== */



/* This structure is purely intended to be an easy way to transfer */

/* and return information about time (real or mudwise).            */
struct time_info_data {
    sh_int second;
    sh_int minute;
    sh_int hour;
    sh_int day;
    sh_int month;
    sh_int year;
};

/* These data contain information about a players time data */
struct time_data {
    time_t birth; /* This represents the characters age    */
    time_t logon; /* Time of the last logon                */
    time_t saved; /* Time of the last save                 */
    sh_int perm_aging; /* permanent 'unnatural' aging */
    sh_int age_mod; /* temporary 'unnatural' aging */

    uint played; /* accumulated time played in secs       */
};

struct combat_stats {
    // Thac0 Stats.
    int classMod;
    int levelMod;
    int calcWeaponSkillNum;
    int calcWeaponSkill;
    int weaponSkillMod;
    int dexMod;
    int hitrollMod;
    int paladinBonus;
    int vampTouchBonus;
    int blindBonus;
    int blindMalus;
    int loadMod;
    int protEvilMalus;
    int protGoodMalus;
    int protUndead;
    int blurrMalus;
    int ressurectMalus;
    int victAgility;
    int victAgilityMod;
    int victimACMod;
    int victimAC;
    int mobHitrollMod;
    int mobSkillMod;
    int baseHitChance;
    int hitChance;
    int diceRoll;
    int lastHitType;
    int hitTypeFumble;
    int hitTypeCrit;
    int hitTypeHit;
    int hitTypeMiss;
    int hits;
    int attacks;

    // Damage Stats
    int damWeapon;
    int damBarehand;
    int damrollBonus;
    int strDamBonus;
    int backstabBonus;
    int circleBonus;
    int postureBonus;
    int offenseBonus;
    int mountedBonus;
    int critHitBonus;
    int acAbsorptionBonus;
    int mobDam10Bonus;
    int mobDam20Bonus;
    int mobDam30Bonus;
    int mobDam40Bonus;
    int mobDam50Bonus;
    int mobDam60Bonus;
    int boundedDamage;
    int highestDamage;
    int lowestDamage;
    unsigned int store_dam_totalDamage;
    unsigned int counter_dam_totalDamage;

    float store_spells_diceNum;
    float store_spells_diceSize;
    float store_spells_rawDamage;
    float store_spells_offensiveLevel;
    float store_spells_modifiedDamage;
    float store_spells_specializationDamage;
    float store_spells_globalDamageFactor;
    float store_spells_skillDamage;
    float store_spells_evadeDamage;
    float store_spells_innateRaceDamage;
    float store_spells_highConRaceDamage;
    float store_spells_finalDamage;
    float store_spells_circleNonAreas1Damage;
    float store_spells_circleNonAreas2Damage;
    float store_spells_circleNonAreas3Damage;
    float store_spells_circleNonAreas4Damage;
    float store_spells_circleNonAreas5Damage;
    float store_spells_circleNonAreas6Damage;
    float store_spells_circleNonAreas7Damage;
    float store_spells_circleNonAreas8Damage;
    float store_spells_circleNonAreas9Damage;
    float store_spells_circleNonAreas10Damage;
    float store_spells_circleAreas1Damage;
    float store_spells_circleAreas2Damage;
    float store_spells_circleAreas3Damage;
    float store_spells_circleAreas4Damage;
    float store_spells_circleAreas5Damage;
    float store_spells_circleAreas6Damage;
    float store_spells_circleAreas7Damage;
    float store_spells_circleAreas8Damage;
    float store_spells_circleAreas9Damage;
    float store_spells_circleAreas10Damage;
    float store_spells_totalDamage;
    float store_spells_highestDamage;
    float store_spells_lowestDamage;
    float store_spells_spellsCast;

    float counter_spells_diceNum;
    float counter_spells_diceSize;
    float counter_spells_rawDamage;
    float counter_spells_offensiveLevel;
    float counter_spells_modifiedDamage;
    float counter_spells_specializationDamage;
    float counter_spells_globalDamageFactor;
    float counter_spells_skillDamage;
    float counter_spells_evadeDamage;
    float counter_spells_innateRaceDamage;
    float counter_spells_highConRaceDamage;
    float counter_spells_finalDamage;
    float counter_spells_circleNonAreas1Damage;
    float counter_spells_circleNonAreas2Damage;
    float counter_spells_circleNonAreas3Damage;
    float counter_spells_circleNonAreas4Damage;
    float counter_spells_circleNonAreas5Damage;
    float counter_spells_circleNonAreas6Damage;
    float counter_spells_circleNonAreas7Damage;
    float counter_spells_circleNonAreas8Damage;
    float counter_spells_circleNonAreas9Damage;
    float counter_spells_circleNonAreas10Damage;
    float counter_spells_circleAreas1Damage;
    float counter_spells_circleAreas2Damage;
    float counter_spells_circleAreas3Damage;
    float counter_spells_circleAreas4Damage;
    float counter_spells_circleAreas5Damage;
    float counter_spells_circleAreas6Damage;
    float counter_spells_circleAreas7Damage;
    float counter_spells_circleAreas8Damage;
    float counter_spells_circleAreas9Damage;
    float counter_spells_circleAreas10Damage;
    float counter_spells_totalDamage;
    float counter_spells_highestDamage;
    float counter_spells_lowestDamage;
    float counter_spells_spellsCast;

    // Counters for total combat damage;
    float store_dam_scaleProtectSize; // Modifier: Dam size stopped by scale
    float store_dam_stoneProtectSize; // Modifier: Sam size stopped by stone
    float store_dam_scaleMaxHits; // Modifier: Hits absorbed by scale
    float store_dam_stoneMaxHits; // Modifier: Hits absorbed by stone
    float store_dam_lowestDamage; // Lowest amoutn of damage done;
    float store_dam_highestDamage; // Highest amoutn of damage done;
    float counter_dam_scaleProtectSize; // Modifier: Dam size stopped by scale
    float counter_dam_stoneProtectSize; // Modifier: Sam size stopped by stone
    float counter_dam_scaleMaxHits; // Modifier: Hits absorbed by scale
    float counter_dam_stoneMaxHits; // Modifier: Hits absorbed by stone

    unsigned int store_dam_totalAdjustedDamage; // Total damage output;
    unsigned int counter_dam_totalAdjustedDamage; // Total damage output;

    int goodStats;
};

struct char_player_data {
    char *name; /* PC / NPC s name (kill ...  )         */
    char *short_descr; /* for 'actions'                        */
    char *long_descr; /* for 'look'.. Only here for testing   */
    char *description; /* Extra descriptions                   */
    ubyte sex; /* sex                                  */
    ubyte class; /* class (not used by NPCs (yet))       */
    ush_int class_choice; /* class choice (shamans only ATM) JTC  */
    ubyte race; /* race                                 */
    ubyte level; /* level                                */
    int hometown; /* PCs Hometown (last saved room)       */
    int birthplace; /* birth room                           */
    struct time_data time; /* Age                          */
    struct combat_stats statistics; /* Combat Stats                 */
};

struct stat_data {
    sh_int Str; /* 4 physical stats */
    sh_int Dex;
    sh_int Agi;
    sh_int Con;

    sh_int Pow; /* 4 mental stats */
    sh_int Int;
    sh_int Wis;
    sh_int Cha;

    sh_int Karma; /* 2 'special' stats */
    sh_int Luck;
};

struct player_disguise_data {
    bool active_pc; /* is he disguise                       */
    bool active_npc;
    char *name; /* PC name                              */
    int class; /* class                                */
    ubyte race; /* race                                 */
    ubyte level; /* level                                */
    char *title; /* title                                */
};

struct char_point_data {
    // hitChance, for CombatEngine
    byte hitCount;

    /* base is unmodified, this is the value that is saved for players */
    sh_int base_hit;
    sh_int base_mana;
    sh_int base_move;

    /* these 3 are the current values */
    sh_int hit;
    sh_int mana;
    sh_int move;

    /* these 3 are base + modifiers, and are used as a limit */
    sh_int max_hit;
    sh_int max_mana;
    sh_int max_move;

    sh_int base_armor; /* Mainly for mobs, PC is always 100  */
    sh_int curr_armor; /* current armor class  */
    int cash[4]; /* Money carried  */
    int max_exp; /* The highest experience player has ever had  */
    int curr_exp; /* The current experience of the player       */
    byte damnodice; /* The number of damage dice               */
    byte damsizedice; /* The size of the damage dice             */
    byte base_hitroll; /* base hit roll, 0 for PCs    */
    byte base_damroll; /* base damage roll, 0 for PCs */
    byte hitroll; /* Any bonus or penalty to the hit roll    */
    byte damroll; /* Any bonus or penalty to the damage roll */
    uint height; /* height                               */
    uint base_height; /* base height                          */
    uint base_weight;
    uint weight;
    uint base_magic_resistance;
    uint magic_resistance; /*  mob MR                              */
};

struct shadow_data {
    P_char shadowing;
    bool shadow_move;
    bool valid_last_move;
    int room_last_in;
    struct shadower_data *who;
};

struct shadower_data {
    P_char shadower;
    byte dir_last_move;
    byte num_of_moves;
    int noticed;
    struct shadower_data *next;
};

struct memorize_data {
    bool flag;
    ush_int spell;
    int ticks;
    struct memorize_data *next; /* two ways linked list so it's _WAY_ faster to mem stuff with event-based system */
};

struct char_skill_data {
    byte learned; /* % chance for success 0 = not learned   */
    byte memorized; /* # of spells memorized */
};

/* Skill usage limit --TAM 04/16/94 */

struct timed_usages_struct {
    time_t time_of_first_use;
    byte times_used;
};

struct usage_limits_struct {
    byte max_can_use;
    time_t time_period_per_use;
};

struct affected_type {
    ush_int type; /* The type of spell that caused this      */
    sh_int duration; /* For how long its effects will last      */
    sh_int modifier; /* This is added to apropriate ability     */
    ubyte location; /* Tells which ability to change(APPLY_XXX)*/
    ubyte sets_affs[AFF_BYTES]; /* replaces bitvector and bitvector2       */
    P_char target; /* Holds info for linked affect            */
    char *instruction; /* Initially for order follower            */

    struct affected_type *next;
};

struct follow_type {
    P_char follower;
    struct follow_type *next;
};

/* Mailbox structs   - Altherog '01 */
typedef struct mmesg_s {
    unsigned long state;
    unsigned long size; /* size of message text */
    unsigned long date;
    char *from; /* message is from */
    char *subject; /* message subject */
    char *text; /* message body */
    struct mmesg_s *next; /* next message in mailbox */
} mmesg;

typedef struct mbox_s {
    struct list_head mbox_link; /* next mailbox on global mbox double linked list */
    int state; /* mailbox state */
    int size; /* mailbox message count */
    char *mbox_fname; /* filename saving mailbox */
    char *owner; /* name of mailbox owner */
    mmesg *messages; /* mailbox message link list */
} mbox;

struct pc_only_data { /* values only used by PCs        */
    char *poofIn;
    char *poofOut;
    char *title;
    char *last_login;

    P_char switched;
    P_char ignored;
    P_char duel;

    ubyte pcact[PACT_BYTES]; /* flags for PC variables */

    int prestige; /* Prestige/Honor */
    byte wiz_invis; /* Used by wizard command "vis" */
    ubyte screen_length; /* adjust paging to fit terminal */
    ubyte echo_toggle;
    ush_int prompt;
    char pwd[12]; /* 'crypt'ed password    */
    int balance[4]; /* money in the bank   */
    ubyte grant[GRANT_BYTES]; /* grant command bits as a cbit array  -- Altherog Dec 98 */
    ubyte debug[DEBUG_BYTES]; /* debug subchannels a PC is tunned to  -- Alth Jan 99 */

    uint law_flags; /* KNOWN, WANTED, OUTCAST in hometowns */
    ubyte law_flag[LAW_FLAG_BYTES]; /* KNOWN, WANTED, OUTCAST in hometowns */
#ifdef OVL
    sh_int ovl_count;
    sh_int ovl_timer;
#endif
    short aggressive; /* If not -1, PC will attack aggs */
    short wimpy; /* If wimpy set, max hp before autoflee */
    sh_int time_judge; /* number of times a character's been judged */
#if 0
    short guild; /* For barlows guild stuff... */
#endif

    struct email_registration_data *ems;

    struct writing_info *writing;
#ifdef KINGDOM
    char **str; /* for the modify-str system  */
    int max_str; /* -                          */
#endif

    struct timed_usages_struct timed_usages[MAX_TIMED_USAGES];

    struct char_skill_data skills[MAX_SKILLS]; /* Skills                */
    struct memorize_data *memorize_list;

    byte spells_memmed[MAX_CIRCLE + 1];
    ubyte talks[MAX_TONGUE]; /* PC's Tongue's 0 for NPC         */
    ush_int song; /* for bards, removed from talks by evident incongruity - Kelly*/
#ifdef EXP_TROPHY
    struct Trophy_data *Trophies;
#endif
#ifdef PCPROCS
    ush_int spec_flag; /* flags set by the procs themselves */
    struct func_attachment *func;
#endif

    struct disease *diseases; /* linked list of disease structs */

    ush_int asc_num; /* which guild?                            */
    ubyte asc_bits[ASCM_BYTES]; /* how you enter, etc.               */
    int asc_rank; /* rank */
    time_t time_left_guild; /* time you left guild                     */
    ubyte nb_left_guild; /* number of time you left a guild         */

    int acheron_portal; /* number of acheron portal we entered */
    int quest_portal; /* number of quest portal we entered */
    char genName[8][25]; /* stores ran gen names during char creation */

    mbox *mbox; /* players mailbox */

    byte condensed_flags; /* stores settings for condensed options */
    P_char reply;
    int camp_room;
    ubyte available_spells[11];
    int manaburn;
};

#ifdef PET_STABLE

struct stable_struct {
    int id;
    int room;
    int time;
    sh_int hit;
    sh_int mana;
    sh_int move;
    struct stable_struct *next;
};
#endif

struct npc_only_data { /* values only used by NPCs  */
    P_char rider; /* pointer to rider on this char -DCL     */
    byte last_direction; /* The last direction the monster went    */
    int spec[4]; /* for use by various special procs      */
    ubyte attack_type; /* barehand attack                        */
    ubyte default_pos; /* Default position                       */
    ubyte str_mask; /* flag field for 'strung' char* fields   */
    ush_int home; /* Gamble: no more than 65536 real rooms */
    void *memory; /* Used for memory system                 */
    byte spells_in_circle[MAX_CIRCLE + 1]; /* Array tracking # of spells of a  */
    /* given circle still available for */
    /* casting - SKB */
    ubyte npcact[NPCACT_BYTES]; /* flags for NPC behavior                  */
    ubyte aggressive[AGG_BYTES]; /* determines aggressivness to specified races
                                 * REMEMBER to bump up AGG_BYTES after adding
                                 * new races if needed..
                                                                 -- Diirinka */
    char *lastowner; /* pointer to the name of last owner of the mobile..
                                 * used to determine who the pet should
                                 * follow after the PC takes a wipe and returns
                                 * to claim it back (used only in pet save) */


    P_char orig_char; /* Used to determine the orig owner for morphs */
    P_char owner_char; /* Used to store the 'owner' PC of various pets */

    int prestige_bonus; /* bonus prestige for 'Boss' mobs */

#ifdef PET_STABLE
    struct stable_struct *stable;
#endif
};

struct char_special_data {
    ubyte affects[AFF_BYTES]; /* replaces affected_by and affected_by2 */
    ubyte affects2[AFF_BYTES]; /* new cbits for expanded affs..        */

    ubyte position; /* posture and status                      */

    int carry_weight; /* Carried weight                          */
    ush_int carry_items; /* Number of items carried                 */
    int was_in_room; /* previous room char was in               */
    byte apply_saving_throw[5]; /* Saving throw (Bonuses)                  */
    byte conditions[3]; /* Drunk full etc.                         */

    sh_int alignment; /* +-1000 for alignments                   */

    bool command_delays; /* input not processed while TRUE */
    P_char consent;
    P_char fighting; /* Opponent                                */

    P_char next_fighting; /* For fighting list             */

    P_char riding; /* pointer to mount this char is riding -DCL */
    P_obj cart; /* @@@@ pointer to the cart the player owns */
    sh_int timer;
    wtns_rec *witnessed; /* linked list of witness records */
    P_char arrest_by; /* pointer to arrest */
#ifdef CONFIG_JAIL
    sh_int jail_time;
    P_char arrest_by;
    P_char arrest_link;
    P_char witnessing;
    P_char witness_vict;
    int witness_cmd;
#endif
    ubyte action_delays[MAX_ACTION_DELAYS]; /* to support multiple # cmds requiring them --TAM */

    struct shadow_data shadow; /* data pertaining to shadow skill --TAM 7-6-94 */

#ifdef EVENT_SAVING
    /* Queue of restored events that have to be scheduled once
     * the char is put back into the game.. */
    struct restored_event_data *eventRestoreQ;
#endif
    int tracking; /* room we're tracking to.  --DMB 10/24/99 */
    ush_int avoid_counter;

};

/* ================== Structure for player/non-player ===================== */

/* only the most basic values go into this struct, plus pointers to anything
   else.  Most values should be organized in substructures, mainly just to
   establish a logical tree of values.  JAB */

struct char_data {
    sh_int nr; /* monster nr (position in mob_index)  */
    int in_room; /* Location                  */
    byte light; /* amount of light emitted by char */
    int job_id;

#ifdef SERIAL_NUMBERS
    uint OSN; /* object serial number             */
#endif

    uint psn; /* pet serial number, used while saving/renting pets */

    /* new, removed the pc/npc specific items to their own structs -JAB */
    union {
        struct pc_only_data *pc;
        struct npc_only_data *npc;
    } only;

    P_desc desc;

    P_char next_in_room; /* For room->people - list       */
    P_char next; /* For either mobile | p-list    */

    P_event events; /* events attached to this char      */

    struct char_player_data player; /* Normal data               */

    struct player_disguise_data disguise; /* Disguise data */

    struct stat_data base_stats;
    struct stat_data curr_stats;

    struct char_point_data points;
    struct char_special_data specials; /* Special playing constants   */

    struct affected_type *affected; /* affected by what spells    */

    P_obj equipment[MAX_WEAR]; /* Equipment array            */
    P_obj carrying; /* Head of list               */

    P_char following; /* Who is char following?      */
    struct follow_type *followers; /* List of chars followers     */
    struct group_data *group; /* Group which character belongs to */
    int anchor_point; /* Transport Via Plants Anchor */
#if 0
    byte cond_settings; /* To be used for series of condensed toggs */
#endif
#ifdef NEW_BARD
    P_char bard_singing;
    P_song song_singing;
    P_char accompany;
#endif
};

/* ======================================================================== */

struct ban_t {
    char *banner;
    int banner_lvl;
    char *ban_user;
    char *ban_site;
    char *ban_str;
    struct ban_t *next;
};

/* ***********************************************************
 *  The following structures are related to descriptor_data   *
 *********************************************************** */

struct txt_block {
    char *text;
    struct txt_block *next;
};

struct txt_q {
    struct txt_block *head;
    struct txt_block *tail;
};

#define LOOKUP_IDENT    1
#define LOOKUP_DNS      2

struct conn_lookup_struct {
    int type;
    int state;
    int desc; // fd of the initial connection
    int identfd; // fd of the ident connection
    int remotePort; // what port did the remote client open the connection on
    char *remoteIP; // IP of the remote client
    char *resolvedHostname;
    char username[16];
    int dnsfd; // fd of the hostname lookup connection
    P_lookup ignored;
};

/* modes of connectedness */

#define CON_PLYNG     0
#define CON_NME       1
#define CON_NMECNF    2
#define CON_PWDNRM    3
#define CON_PWDGET    4
#define CON_PWDCNF    5
#define CON_QSEX      6
#define CON_RMOTD     7
#define CON_SLCT      8
#define CON_PUNTCNF   9
#define CON_QCLASS   10
#define CON_LDEAD    11
#define CON_PWDNEW   12
#define CON_PWDNCNF  13
#define CON_FLUSH    14
#define CON_PWDNGET  15
#define CON_PWDDCNF  16
#define CON_QRACE    17
#define CON_TERM     18
#define CON_EXDSCR   19
#define CON_QRETURN  20
#define CON_EMSINFO1 21
#define CON_EMSINFO2 22
#define CON_EMS_MAIL 23
#define CON_EMS_PIN  24
#define CON_EMS_CHK  25
#define CON_EMS_CNFM 26
#define CON_DISCLMR  27
#define CON_INFO     28
#define CON_UNUSED   29
#define CON_RACEWAR  30
#define CON_LINKVR   31
#define CON_LINKSET  32
#define CON_APROPOS  33
#define CON_LOOKUP   34      /* still missing either username or hostname */
#define CON_REROLL   35
#define CON_BONUS1   36
#define CON_BONUS2   37
#define CON_BONUS3   38
#define CON_KEEPCHAR 39
#define CON_ALIGN    40
#define CON_HOMETOWN 41
#define CON_ACCEPTWAIT 42
#define CON_WELCOME  43
#define CON_NEW_NAME 44
#define CON_NEW_CHAR 45
#define CON_FRST_NME 46
#define CON_FRST_QUIT 47
#define CON_QUIT     48
#define CON_INFO_WAIT 49
#define CON_NEW_QUIT 50
#define CON_REREG_CHK 51
#define CON_REREG_PIN 52

/* modes of confirmation- SAM 7-94 */
#define CONFIRM_NONE    0
#define CONFIRM_AWAIT   1
#define CONFIRM_DONE    2

struct snoop_data {
    P_desc snooping; /* Who is this char snooping */
    P_desc snoop_by; /* And who is snooping on this char */
};

struct descriptor_data {
    P_char character; /* linked to char             */
    P_char original; /* original char              */
    P_desc next; /* link to next descriptor    */
    byte connected; /* mode of 'connectedness'    */
    int wait; /* wait for how many loops    */
    char **showstr_vector; /*       -                    */
    int showstr_count; /* number of pages to page through      */
    int showstr_page; /* which page are we currently showing? */
    char **str; /* for the modify-str system  */
    char *backstr; /* abort buffers              */
    char *storage; /* file editor holding        */
    int max_str; /* -                          */
    char *name; /* name for mail system       */
    byte prompt_mode; /* control of prompt-printing */
    byte rtype; /* character restore status   */
    byte term_type; /* terminal type, normal or ansi */
    char *showstr_head; /* for paging through texts   */
    char *showstr_point; /*       -                    */
    char buf[MAX_QUEUE_LENGTH]; /* buffer for raw input       */
    char confirm_state; /* SAM 7-94, used to allow confirming commands */
    char last_command[MAX_INPUT_LENGTH];
    char last_input[MAX_INPUT_LENGTH + 1]; /* the last input         */
    /* SAM 7-94, used to allow confirming commands */
    char old_pwd[12]; /* old password held here when changing SAM 7-94 */
    char tmp_val; /* temporary field used in char creation only */
    sh_int descriptor; /* file descriptor for socket */

    char *hostname; /* hostname                   */
    char *username; /* userid from host           */
    char hostIP[17]; /* IP number of host          */
    char lookup_status; /* flag for host/user lookups */
    char ems_buffer[256]; /* ems buffer for outbound.   */
    time_t timestamp; /* for ident lookup           */

    struct olc_data *olc; /*. OLC info - defined in olc.h   .*/
    struct edit_data *editor; /* for new editor code */

    struct snoop_data snoop; /* to snoop people.           */
    struct txt_q input; /* q of unprocessed input     */
    struct txt_q output; /* q of strings to send       */
#ifdef NEW_SITE_LOOKUPS
    struct conn_lookup_struct *dns_lookup; /* hostname lookup structure */
    struct conn_lookup_struct *ident_lookup; /* client ident lookup structure */
#endif
    mmcp_desc_ctx_t *mccp; /* MCCP compression protocol context */
};

struct msg_type {
    char *attacker_msg; /* message to attacker */
    char *victim_msg; /* message to victim   */
    char *room_msg; /* message to room     */
};

struct message_type {
    struct msg_type die_msg; /* messages when death            */
    struct msg_type miss_msg; /* messages when miss             */
    struct msg_type hit_msg; /* messages when hit              */
    struct msg_type sanctuary_msg; /* messages when hit on sanctuary */
    struct msg_type god_msg; /* messages when hit on god       */
    struct message_type *next; /* to next messages of this kind.*/
};

struct message_list {
    int a_type; /* Attack type  */
    int number_of_attacks; /* How many attack messages to chose from. */
    struct message_type *msg; /* List of messages. */
};

struct str_app_type {
    byte tohit; /* To Hit (THAC0) Bonus/Penalty        */
    byte todam; /* Damage Bonus/Penalty                */
};

struct dex_app_type {
    byte reaction;
    byte miss_att;
    byte p_pocket;
    byte p_locks;
    byte traps;
};

struct agi_app_type {
    byte defensive;
    byte sneak;
    byte hide;
};

struct con_app_type {
    byte hitp;
    byte shock;
};

struct int_app_type {
    byte learn; /* how many % a player learns a spell/skill */
};

struct wis_app_type {
    byte bonus; /* how many bonus skills a player can */
    /* practice pr. level                 */
};

struct pow_app_type {
    byte bonus; /* how many bonus psps per level */
};

struct cha_app_type {
    byte modifier;
};

/* For spec_procs */

#define SPEC_TYPE_UNDEF  0
#define SPEC_TYPE_DAMAGE 1
#define SPEC_TYPE_DEATH  2

/* Attacktypes with grammar */

struct attack_missile_type {
    const char *singular;
    const char *plural;
};

struct attack_hit_type {
    const char *singular;
    const char *plural;
};

/** Support minor creation --TAM 2/94 **/
struct minor_create_struct {
    const char *keyword;
    sh_int obj_number;
};

/* Support woodcarving 4/2001 -KPZ */
struct woodcarving_struct {
    const char *keyword;
    sh_int obj_number;
};

/* ShapeChange: Valid mobs to become-type */
struct shapechange_struct {
    const char *name; /* mob name */
    sh_int mob_number;
    int animal_type;
};

/* Disguise: Valid generic mob list for disguising into.. */
struct disguise_list_data_struct {
    int race;
    const char *name[3];
};

/* Gate Spell struct */
struct planes_data {
    const char *keyword;
    int zone_start;
    int zone_end;
    int gate_start;
    int gate_end;
};

/* Anti-escape struct */
struct anti_escape_data {
    const char *keyword;
    int zone_start;
    int zone_end;
};

/* Money 'container' struct, has nothing to do with how money is stored
 * in objects or chars */
struct moneyStruct {
    int platinum;
    int gold;
    int silver;
    int copper;
    unsigned long total;
};

/* Types */
struct ClassSkillInfo {
    byte rlevel; /* level required, for spells, spell circle #*/
    byte maxlearn; /* max % that can be gained */
    int base_skill; /* base skill for this class? */
    byte costmult;
};

struct s_skill {
    int pindex; /* index for skill lookup in player struct */
    const char *name; /* name of skill                           */
    ush_int type; /* Skills: mental/phys Spells: category    */
    byte min_pos; /* min position required                   */
    sh_int beats; /* cast time                               */
    byte harmful; /* spell harmful to others?                */
    sh_int targets; /* target types allowed                    */
    sh_int off_level; /* Offensive level (Spells only, for now)  */
    sh_int song_flag; // Is this a song?

    void (*spell_pointer) (int, P_char, char *, int, P_char, P_obj);
    struct ClassSkillInfo class[TOTALCLASS]; /* info for each class */
};

typedef struct s_skill Skill;

struct command_info {
    void (*command_pointer) (P_char, char *, int);
    byte minimum_position;
    bool in_battle;
    byte minimum_level;
    byte req_confirm;
    byte grantable;
};

/* hunt types that use a P_char as a target should be below 127.  Ones
   that use a room as a target should be above 127 */

#define HUNT_HUNTER             1 /* ACT_HUNTER mobs */
#define HUNT_JUSTICE_ARREST     2 /* going to arrest someone */
#define HUNT_JUSTICE_OUTCAST    3 /* going to kill an outcast */
#define HUNT_JUSTICE_INVADER    4 /* going to kill an invader */
#define HUNT_JUSTICE_SPECVICT   5 /* special justice purpose */
/******************************************************************
  All types above use 'victim' as targ */

#define HUNT_LAST_VICTIM_TARGET 127

/* All types below use 'room' as targ
 ******************************************************************/
#define HUNT_JUSTICE_SPECROOM   128 /* special justice purpose */
#define HUNT_ROOM               129 /* just run to that room */
#define HUNT_JUSTICE_HELP       130 /* call for help from that room */
#define HUNT_JUSTICE_REPORT     131 /* go report witnessed crimes */

struct hunt_data {
    ubyte hunt_type; /* see defines above.. */
    ubyte retry; /* counter for retrying moves that
                                   don't work */
    ubyte retry_dir; /* last failed direction I tried moving */

    union {
        P_char victim; /* who am I hunting? */
        int room; /* what room am I hunting? */
    }
    targ;
};

struct room_exec_data {
    P_char ch;
    void (*re_func) (P_char, P_room);
};

#ifdef EVENT_SAVING

struct func_registration_data {
    char *name; /* name of the function */
    void *ptr; /* function pointer */
    ush_int repeatTime; /* repeat time for IDX_PERIODIC procs */
};

struct savable_char_exec_data {
    void (*t_func) (P_char, char *, int); /* fn to call once the event hits */
    char *t_arg; /* user data space unique to this event */
    ush_int t_arg_size; /* size of the allocated data space, has to be freed once the event is processed */
};

/* this struct is used to queue restored events untill the char is actually put
 * in the game..                                                                   */
struct restored_event_data {
    ubyte type; /* type of event triggered:  EVENT_*        */
    bool one_shot; /* if TRUE, event is deleted once triggered */
    int time_left; /* time left until the event hits           */

    union { /* union to hold all kinds of data types for different event types */
        struct savable_char_exec_data *sced;
    } data;

    struct restored_event_data *next; /* pointer to next element in queue */
};

#endif

struct event_data {
    ubyte type; /* type of event triggered:  EVENT_*        */
    ubyte element; /* element of events[] this is a member of  */
    bool one_shot; /* if TRUE, event is deleted once triggered */
    ush_int timer; /* number of cycles (minutes) before event, if we want to schedule an event longer
                                   than 45 (real) days in advance we'll have to change this to u_int which will
                                   let us schedule up to 4000 (real) years in advance. */

    union {
        P_char a_ch; /* one of these will point to the initiator */
        P_obj a_obj; /* (actor) of this event.  type determines  */
        P_room a_room; /* which is valid.                          */
        void (*a_func) (void);
        void (*a_func_param) (void *);
    } actor;

    union {
        P_char t_ch; /* one of these will point at the target of */
        P_obj t_obj; /* this event (or none, it's optional in    */
        P_room t_room; /* some cases).  Or if this is a delayed    */
        struct zone_data *t_zone; /* command of some sort, t_arg will get     */
        char *t_arg; /* sent to command_interpreter.             */
        int t_num;
        struct writing_info *t_writing;
        struct scribing_data_type *t_scribe;
        struct spellcast_datatype *t_spell;
        void (*t_func) (P_char);
        void (*t_func_o) (P_obj);
        void (*t_func_proc) (P_char, P_char, int, char *);
        struct hunt_data *t_hunt;
        struct room_exec_data *t_r_e_d;
#ifdef EVENT_SAVING
        struct savable_char_exec_data *t_sced;
#endif
        void *t_param;
    } target;

    P_event prev_sched; /* pointer to prev event in schedule[]       */
    P_event prev_type; /* pointer to prev event in event_sub_list[] */
    P_event prev_event; /* pointer to prev event in event_list or avail_events */
    P_event next_sched; /* pointer to next event in schedule[]       */
    P_event next_type; /* pointer to next event in event_sub_list[] */
    P_event next_event; /* pointer to next event in event_list or avail_events */
    P_event next; /* pointer to next event on obj or char      */
};

/* data structure for justice witness record as held in memory.  This
   struct ends up being a linked list.. */

struct witness_data {
    time_t time; /* When did it happen? */
    char *attacker; /* who did it? */
    char *victim; /* who did they do it to? */
    ubyte crime; /* what did they do? */
    int room; /* Where did they do it?  (VIRTUAL!) */
    wtns_rec *next; /* next record (or NULL if none) */
};

struct crime_data {
    time_t time;
    char *attacker;
    char *victim;
    ubyte crime;
    int room;
    int money;
    ubyte status;
    crm_rec *next;
};

/* for trophy exp. modifier system */
struct Trophy_data {
    int Vnum;
    uint XP;
    struct Trophy_data *next;
};


/****************************************************************************/
/****************************************************************************/
/*  Stuff for mob behaviour definitions ...         Altherog                */
/****************************************************************************/
/****************************************************************************/


#define    CMD_BYTES           (MAX_CMD_LIST / 8 + 1)
#define    SOCIALS_INDEX_SIZE  20000
#define    SOCIALS_FILE        "areas/world.soc"

#define    NON_COMBAT_ONLY     BIT_1
#define    COMBAT_ONLY         BIT_2
#define    USE_INITIATOR       BIT_3
#define    USE_SELF            BIT_4
#define    USE_TARGET          BIT_5
#define    DIRECTED            BIT_6
#define    BLOCK_OTHER         BIT_7
#define    FROM_MOB            BIT_8
#define    FROM_PC             BIT_9
#define    LAST_SOC_FLAG       BIT_10

#define    SOC_REPLY           BIT_1
#define    SOC_PERIODIC        BIT_2
#define    SOC_TIMED           BIT_3
#define    SOC_PATH            BIT_4
#define    SOC_LIST            BIT_5

#define    SOC_ACTION          1
#define    SOC_FLAG            2
#define    SOC_CHANCE          3
#define    SOC_DELAY           4
#define    SOC_TRIGGER         5
#define    SOC_TYPE            6
#define    SOC_ROOMS           7
#define    SOC_DIRS            8
#define    SOC_ID              9
#define    SOC_DONE            10
#define    SOC_DONELIST        11
#define    SOC_HOUR            12

/* Periodic Commands */
struct socData_periodic {
    ush_int flag; /* Misc Flags   */
    ush_int chance; /* Chance of getting a target getting a response */
    ush_int delay; /* Delay used in the AddEvent for a response command or action */
    ush_int action; /* Command to be performed periodicaly */
    char *arg; /* String containing the optional arguments */

    struct socData_periodic *chainNext; /* Ptr to next command to be performed right after this one is finished */
    struct socData_periodic *next; /* Ptr to another periodic command for this mobile */
};

/* A sort of a header for the periodic lists */

struct socData_list {
    ush_int numActions; /* number of actions in the list */
    struct socList *listArray;
    struct socData_list *nextList; /* Completely different List.. */
    /* I dont know _Why_ you'd want to have
     * multiple lists of periodic commands
     * but i'm including it for the sake of
     * completeness */
};

/* Keeps the data for each periodic action within the periodic list  */
struct socList {
    ush_int flag; /* Misc Flags   */
    ush_int chance; /* Chance of getting a target getting a response */
    ush_int delay; /* Delay used in the AddEvent for a response command or action */
    ush_int action; /* Command to be performed periodicaly */
    char *arg; /* String containing the optional arguments */

    struct socList *next;
};

/* Holds the triggers/responses */
struct socData_trigger {
    ush_int flag; /* Misc Flags   */
    ush_int chance; /* Chance of getting a target getting a response */
    ush_int delay; /* Delay used in the AddEvent for a response command or action */
    ush_int trigger_action; /* Command that triggers the response */
    ush_int response_action; /* Command performed as a reply to the trigger */
    char *arg; /* String containing the optional arguments */

    struct socData_trigger *chainNext; /* Ptr to next command to be performed right after this one is finished */
    struct socData_trigger *next; /* Ptr to a totaly separate command for this mobile */
};

/* Holds the timed responses */
struct socData_timed {
    ush_int flag; /* Misc Flags   */
    ush_int chance; /* Chance of getting a target getting a response */
    ush_int delay; /* Delay used in the AddEvent for a response command or action */
    ubyte hour; /* Time that triggers the response */
    ush_int response_action; /* Command performed as a reply to the trigger */
    char *arg; /* String containing the optional arguments */

    struct socData_timed *chainNext; /* Ptr to next command to be performed right after this one is finished */
    struct socData_timed *next; /* Ptr to a totaly separate command for this mobile */
};

struct socData_path {
    ush_int flag; /* Misc Flags   */
};

struct socials_head {
    struct socData_periodic *periodic;
    struct socData_list *list;
    struct socData_trigger *trigger;
    struct socData_path *path;
    struct socData_timed *timed;
    char *error;
    ubyte cmd_list[CMD_BYTES];
};

/****************************************************************************/
/****************************************************************************/

/* defines for pet rent           -  Altherog */

struct npc_file_header {
    unsigned int offset;
    unsigned int size;
    unsigned int psn;
    unsigned int saved_in;
};

#define SAV_TYPE_INGAME           1
#define SAV_TYPE_RENT             2
#define SAV_TYPE_CLAIM            3

/****************************************************************************/
/****************************************************************************/

/* Disease Structures and Defines */
#define MAX_CURE_COMPONENTS               255  // maximum number of components required to make one cure

// List of all known diseases
#define DISEASE_BLACK_PLAGUE                1
#define DISEASE_TIBERIAN_FLU                2

// List of all known cures
#define CURE_BLACK_PLAGUE                   1
#define CURE_TIBERIAN_FLU                   2

// Disease flags
#define DIS_INFECT_MORE_THEN_ONCE           1   // AddDisease will let you add a disease to th$
#define DIS_REMOVE_ON_DEATH                 2   // Remove the disease whenever a ch dies


// *name contains the disease name
// *desc is the generic disease message as seen by whoever diagnoses it (shaman or cleric?)
// if the disease struct is added to a p_char, your code can overwrite *name or *desc and
//    replace it with something else more approperiate if the disease advances to a later stage
//    note: freeing *name or *desc will free the disease definition pointers itself
//          the once on the p_char->disease are simply copied, so you can pretty much overwrite em
//
// stage can be used as a counter to indicate how advanced a disease is on a particular P_char.
//    cures could or should use it as an indicator on how effective they will be..
//    ie your cure sees that P_char->disease->stage is set to say 10, it can cure the disease
//    down by say 5 stages instead of simply removing it.. ala aspirin and fever..
//
// onInfectFunc and onCureFunc function pointers can be defined or left null, if defined
//    corresponding fn will be called when a p_char is infected via AddDisease or cured..

struct disease {
    int type;
    int cureFactor; // between 1 and 10 , determines how hard it is to cure it ..
    char *name;
    char *desc;

    int stage;
    ubyte sets_disease[DIS_BYTES];

    int (*onInfectFunc) (P_char, P_char, int, char *);
    int (*onCureFunc) (P_obj, P_char, int, char *);
    int (*onRemoveFunc) (P_char, P_char, int, char *);
    int (*onStatFunc) (P_char, void *);

    struct disease *next;
};

struct curesDisease {
    int type;
    int stageChange;
    struct curesDisease *next;
};

struct cureComponent {
    int vnum;
    int quantity;
    bool notifyIfMissing; // if set, do_mix will notify ch that the component is missing
    struct cureComponent *next;
};

struct cure {
    int id;
    char *name;
    char *desc;
    int applications; // create once, use 'applications' times..

    struct curesDisease *cures; // list of diseases cure is good for
    struct cureComponent *components; // components needed to create it

    struct cure *next;
};

/* struct for the new editor code */

struct edit_data {
    char **lines;
    int max_lines;
    int cur_line;
    int is_god;
    P_desc desc;
    void (*callback)(P_desc desc, int callback_data, char *text);
    int callback_data;
};

/* all our log files */

/* general status type logs, not problems */
#define LOG_STATUS "lib/logs/log/status"
#define LOG_COMM "lib/logs/log/comm"
#define LOG_EXP "lib/logs/log/exp"
#define LOG_EXIT "lib/logs/log/exit"
#define LOG_DAEMON "lib/logs/log/daemon"
#define LOG_SPELL "lib/logs/log/spell"
#define LOG_TELLS "lib/misc/.t/tells"
#define LOG_EMAILREG "lib/misc/log/emailreg"
#define LOG_TRADE "lib/logs/log/trade"
#define LOG_GSAYS "lib/misc/.t/gsays"
#define LOG_PETITIONS "lib/misc/.t/petition"
#define LOG_OLC "lib/logs/log/olc"
#define LOG_OLCDB "lib/logs/log/olcdb"
#define LOG_OLCPC "lib/logs/olc_logs"

/* problem reporting */
#define LOG_BOARD "lib/logs/log/board"
#define LOG_AUCTION "lib/logs/log/auction"
#define LOG_BOOT "lib/logs/log/boot"
#define LOG_DEBUG "lib/logs/log/debug"
#define LOG_DEBUGLOG "lib/logs/log/debuglog"
#define LOG_EVENT "lib/logs/log/events"
#define LOG_FILE "lib/logs/log/file"
#define LOG_MOB "lib/logs/log/mob"
#define LOG_OBJ "lib/logs/log/obj"
#define LOG_SYS "lib/logs/log/sys"
#define LOG_VEHICLE "lib/logs/log/vehicle"
#define LOG_HOUSE "lib/logs/log/house"
#define LOG_LOOKUP "lib/logs/log/lookup"
#define LOG_IPSHARE "lib/logs/log/ipshare"
#define LOG_CASINO "lib/logs/log/casino"
#define LOG_GIVE "lib/logs/log/give"

/* use this for quick things, don't use it for anything that we might want to log for the long term.  JAB */
#define LOG_TMP "lib/logs/log/tmp"

/* these logs have data of lasting importance, so they are kept longer */
#define LOG_CORPSE "lib/logs/player-log/corpse"
#define LOG_CORPSE_PRES "lib/logs/player-log/corpse_pres"
#define LOG_NEW "lib/logs/player-log/new"
#define LOG_DEATH "lib/logs/player-log/death"
#define LOG_GENNAME "lib/logs/player-log/genname"
#define LOG_LEVEL "lib/logs/player-log/level"
#define LOG_PLAYER "lib/logs/player-log/player"
#define LOG_NEWCHAR "lib/logs/player-log/newchar"
#define LOG_LIFE "lib/logs/player-log/life"
#define LOG_SNOOP "lib/logs/player-log/special_snoop"
#define LOG_ASSOC "lib/logs/player-log/assoc"
#define LOG_STORAGE "lib/logs/player-log/storage"

#define WEB_STATS "/home/<USER>/html/statistics/mud_info.txt"

/* what are those damn immorts up to now? */
#define LOG_WIZ "lib/logs/player-log/wizcmds"
#define LOG_FLAG LOG_WIZ
#define LOG_FORCE LOG_WIZ
#define LOG_WIZLOAD LOG_WIZ

/* Defines for Code Control stuff.. Uses CBIT arrays..
 * Mostly ment to facilitate runtime disabling/enabling of fresh code in
 * case it starts acting up.. Lets see what becomes of it later on <g>
 *                                                      -- Altherog  Dec 98  */

struct code_control_tags {
    char *name;
    int type;
};


/* Code Control stuffs */
#define CODE_PET_SAVE          1          /* allow renting pets */
#define CODE_TRADE             2          /* trade and economy system */
#define CODE_MOBPSI            3          /* Psionic mob routines */
#define CODE_DEMON             4          /* Special demon gating procs */
#define CODE_EMS               5          /* Email registration */
#define CODE_PLAGUE            6          /* black plague */
#define CODE_PKILL             7          /* pkill */
#define CODE_ASSOC             8          /* duris association code */
#define CODE_TELL_LOG          9          /* tell log code */
#define CODE_SHAPECHANGE      10          /* druid shapechange */
#define CODE_KINGDOMS         11          /* kingdom and house code */
#define CODE_NEWBIE           12          /* Newbie channel/helpers */
#define CODE_NEWJUSTICE       13          /* justice code rewrite */
#define CODE_EVENT_BALANCING  14
#define CODE_CAST_RESCHEDULE  15
#define CODE_DUEL             16          /* player duelling */
#define CODE_TIBERIAN_FLU     17          /* tiberian flu disease */
#define CODE_MMAIL            18          /* mmail system */
#define CODE_MISSILE          19          /* missile weapon engine */
#define CODE_CHAOS            20          /* Name says it all, baby. */
#define CODE_NEWDEFENSE       21          /* Ditto.                  */
#define CODE_OOC              22          // Spammy bastards
#define CODE_NEWTROPHY        23          /* New trophy system    */
#define CODE_NEWAUCTION       24          /* New auction system */
#define CODE_EAT_DRINK        25          /* Eat/Drink */

/* Debug Subchannels - add approperiate name to debug_subchannels in constants.c as well */
#define DS_PKILL_DAMAGE_DEBUG  1          /* debug dumps for pkill damage */
#define DS_GROUP               2          /* group */
#define DS_GROUP_CACHE_DEBUG   3          /* group object cache debug */
#define DS_EMS                 4          /* EMS debugging */
#define DS_HOUSE               5          /* house debug channel */
#define DS_MISSILE             6          /* missile combat */
#define DS_NEWJUSTICE          7          /* justice rewrite debug */
#define DS_MMAIL               8          /* mud mail */
#define DS_TROPHY              9          /* trophy */
#define DS_COMBAT             10          /* combat spam */
#define DS_SWITCH             11          /* retarget in combat */
#define DS_DIGUISE            12          /* diguise */
#define DS_SPELLREDUCTION     13          /* spell reduction/resistance */
#define DS_ELEMENTALIST       14          /* elementalist */
#define DS_BADPARAMS          15          /* general bad parameters */
#define DS_BARD_SONG          16          /* bard code */
#define DS_HUNT               17          /* hunt code */
#define DS_SPL_SPEC           18          /* spell specialization */
#define DS_SPELLCAST          19          /* spell casting */
#define DS_COMBAT_SKILLS      20          /* dogde, parry, the like */
#define DS_STONESKIN          21          /* stoneskin exp */
#define DS_SPLIT              22          /* new do_split code */
#define DS_PSIONICS           23          /* psionics */
#define DS_TIMING             24          /* timing of functions */
#define DS_DEFENSE            25          // Defsenvie skill debug
#define DS_EILISTRAEE         26          /* Eilistraee's debug */
#define DS_MIELIKKI           27          /* Mielikkig's debug */
#define DS_MERRSHAULK         28          /* Merrshaulk's debug */
#define DS_AZUTH              29          /* Azuth's debug # */
#define DS_SHEVARASH          30          /* Shevarash's debug # */
#define DS_MIAX               31          /* Miax's debug # */
#define DS_D2                 32          /* D2's debug # */
#define DS_KIARANSALEE        33          /* Kia's debug # */
#define DS_ALTHEROG           34          /* Altherog's debug # */
#define DS_GARGAUTH           35          /* Gargauth's debug # */
#define DS_IYACHTU            36          /* Iyachtu's debug # */
#define DS_UTHGAR             37          /* Uthgar's debug # */
#define DS_MORADIN            38          /* Moradin's debug # */
#define DS_URDLEN             39          /* Urdlen's debug # */
#define DS_NOTHUNT            40          /* Reasons a mob stops hunting (or never starts) */
#define DS_DUGMAREN           41          /* Dugmaren's debug # */
#define DS_AUCTION            42          /* auction debug channel */
#define DS_VELSH              43          /* Velsharoon's debug # */
#define DS_PC                 44          /* debug for PC activity */
#define DS_NPC                45          /* debug for NPC activity */
#define DS_OBJ                46          /* debug for obj activity */
#define DS_ZONERESET          47          /* debug for zone resets */

/* debuglog uses CBITs and they are 0 based, so with 5 bytes 39 is max DS_*
   therefore I moved DS_AZUTH up to 29 so it would get sticky :)  -Azuth */

/* Struct defines for associations -- CRM */


struct assoc_member_data {
    char *name;
    char *alt_name;
    ubyte bits[ASCM_BYTES];
    int rank;
    int pfine;
    int gfine;
    int sfine;
    int cfine;
};

struct assoc_data {
    ush_int number;
    int status;
    int type;
    int num_members;
    ubyte bits[ASC_BYTES];
    int pcoins;
    int gcoins;
    int scoins;
    int ccoins;
    char *description;
    char *message;
    char name[100];
    char rank_names[4][100];
    struct assoc_member_data members[ASC_MAX_MEMBERS];
    P_assoc next;
};

#ifdef CONTRACTS

struct job_data {
    int id; /* ID Number of the job       */
    char *operative; /* Player assigned to the job */
    int type; /* Job Type: Delivery, etc.   */
    int level; /* Difficulty level of job    */
    int status; /* Active, Complete, Failed   */
    int trigger_type; /* How is this job triggered? */
    int time_target; /* Deadline (real time)       */
    int faction; /* Which faction is this for? */
    int contact; /* Vnum of the employer       */
    int payment; /* Payment, in plat           */
    int npc[3]; /* Mob vnums - various uses   */
    int npc_ingame[3]; /* Toggle for npc array       */
    int location[3]; /* Room vnum #1, various uses */
    int object[3]; /* Obj vnum #1, various uses  */
    int group[3]; /* ID of pre-set npc groups   */
    int enemy_faction[3]; /* Opposing faction           */
    int zone[3]; /* Activation zone(s)         */
    char *description; /* Job Desc..                 */
    P_job next;
};
#endif

#ifdef NEW_BARD

/* New structure for bard songs (used in newbard.c) - Yak  */

struct song_list_element {
    P_char this_target;
    P_song_element next;
    P_song_element previous;
};

struct songinfo {
    ubyte song_type; /* What song?                         */
    ubyte difficulty; /* 80-120, lower is easier (80%-120%) */
    ush_int verse; /* How long singing?  Cap at 250      */
    bool sing_type; /* 0 = song, 1 = chant                */
    bool harmful; /* 1 = aggressive to room or target   */
    bool target; /* 0 = room/group, 1 = single target  */
    int modifier; /* Modifier as of last verse check    */
    ubyte virtuoso; /* # of times virtuoso check made     */
    P_song_element affecting; /* linked list of those affected      */
    P_char accompany; /* bard who is accompanying singer    */
    int proc_info; /* used to store info on active procs */
    bool special; /* was special quality threshold rchd */
};

#endif

#endif /* _SOJ_STRUCTS_H_ */
